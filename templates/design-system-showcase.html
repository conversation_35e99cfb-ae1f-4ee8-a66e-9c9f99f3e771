<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>設計系統展示 - Han AttendanceOS v2005.6.12</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
    <style>
        .showcase-section {
            margin-bottom: var(--spacing-3xl);
            padding: var(--spacing-xl);
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
        }
        
        .showcase-title {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--color-primary-500);
            color: var(--color-primary-700);
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .showcase-item {
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            text-align: center;
        }
        
        .color-swatch {
            width: 100%;
            height: 4rem;
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            border: 1px solid var(--border-light);
        }
        
        .typography-sample {
            margin-bottom: var(--spacing-sm);
        }
        
        .animation-demo {
            width: 3rem;
            height: 3rem;
            background: var(--color-primary-500);
            border-radius: var(--radius-md);
            margin: 0 auto var(--spacing-sm);
        }
    </style>
</head>

<body>
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: var(--spacing-xl);">
        <!-- 頁面標題 -->
        <header class="text-center" style="margin-bottom: var(--spacing-3xl);">
            <h1 class="heading-1" style="color: var(--color-primary-700); margin-bottom: var(--spacing-md);">
                Han AttendanceOS v2005.6.12
            </h1>
            <h2 class="heading-3" style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
                UI設計系統展示
            </h2>
            <p class="paragraph" style="color: var(--text-tertiary);">
                遠漢科技考勤系統統一設計規範 - 展示所有可用的組件和樣式
            </p>
        </header>

        <!-- 色彩系統 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">🎨 色彩系統</h2>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">主色調</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-primary-500);"></div>
                    <div class="text-sm font-medium">Primary 500</div>
                    <div class="text-xs text-tertiary">#3b82f6</div>
                </div>
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-primary-600);"></div>
                    <div class="text-sm font-medium">Primary 600</div>
                    <div class="text-xs text-tertiary">#2563eb</div>
                </div>
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-primary-700);"></div>
                    <div class="text-sm font-medium">Primary 700</div>
                    <div class="text-xs text-tertiary">#1d4ed8</div>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">功能色彩</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-success-500);"></div>
                    <div class="text-sm font-medium">Success</div>
                    <div class="text-xs text-tertiary">成功/出勤</div>
                </div>
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-warning-500);"></div>
                    <div class="text-sm font-medium">Warning</div>
                    <div class="text-xs text-tertiary">警告/遲到</div>
                </div>
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-error-500);"></div>
                    <div class="text-sm font-medium">Error</div>
                    <div class="text-xs text-tertiary">錯誤/缺勤</div>
                </div>
                <div class="showcase-item">
                    <div class="color-swatch" style="background: var(--color-info-500);"></div>
                    <div class="text-sm font-medium">Info</div>
                    <div class="text-xs text-tertiary">資訊</div>
                </div>
            </div>
        </section>

        <!-- 字體系統 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">✍️ 字體系統</h2>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">標題層級</h3>
            <div style="margin-bottom: var(--spacing-lg);">
                <div class="typography-sample">
                    <h1 class="heading-1">主標題 Heading 1 (48px)</h1>
                </div>
                <div class="typography-sample">
                    <h2 class="heading-2">次標題 Heading 2 (36px)</h2>
                </div>
                <div class="typography-sample">
                    <h3 class="heading-3">三級標題 Heading 3 (30px)</h3>
                </div>
                <div class="typography-sample">
                    <h4 class="heading-4">四級標題 Heading 4 (24px)</h4>
                </div>
                <div class="typography-sample">
                    <h5 class="heading-5">五級標題 Heading 5 (20px)</h5>
                </div>
                <div class="typography-sample">
                    <h6 class="heading-6">六級標題 Heading 6 (18px)</h6>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">段落樣式</h3>
            <div>
                <p class="paragraph-large">大段落文字 - 適用於重要內容和引言 (18px)</p>
                <p class="paragraph">標準段落文字 - 適用於一般內容和說明 (16px)</p>
                <p class="paragraph-small">小段落文字 - 適用於次要資訊 (14px)</p>
                <p class="caption">說明文字 - 適用於標籤和提示 (12px)</p>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">字重展示</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="font-light text-lg">細體 Light</div>
                </div>
                <div class="showcase-item">
                    <div class="font-normal text-lg">標準 Normal</div>
                </div>
                <div class="showcase-item">
                    <div class="font-medium text-lg">中等 Medium</div>
                </div>
                <div class="showcase-item">
                    <div class="font-semibold text-lg">半粗體 Semibold</div>
                </div>
                <div class="showcase-item">
                    <div class="font-bold text-lg">粗體 Bold</div>
                </div>
            </div>
        </section>

        <!-- 按鈕系統 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">🔘 按鈕系統</h2>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">基礎按鈕</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <button class="btn btn-primary">主要按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-secondary">次要按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-success">成功按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-warning">警告按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-danger">危險按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-info">資訊按鈕</button>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">按鈕尺寸</h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <button class="btn btn-primary btn-xs">超小</button>
                <button class="btn btn-primary btn-sm">小</button>
                <button class="btn btn-primary">標準</button>
                <button class="btn btn-primary btn-lg">大</button>
                <button class="btn btn-primary btn-xl">超大</button>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">輪廓按鈕</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <button class="btn btn-outline-primary">主要輪廓</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-outline-secondary">次要輪廓</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-outline-success">成功輪廓</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-ghost">幽靈按鈕</button>
                </div>
                <div class="showcase-item">
                    <button class="btn btn-link">鏈接按鈕</button>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">特殊按鈕</h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <button class="btn btn-primary btn-circle">+</button>
                <button class="btn btn-primary btn-loading">載入中...</button>
                <button class="btn btn-gradient">漸變按鈕</button>
                <button class="btn btn-glass">毛玻璃</button>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">按鈕組</h3>
            <div class="btn-group">
                <button class="btn btn-outline-primary">左</button>
                <button class="btn btn-outline-primary">中</button>
                <button class="btn btn-outline-primary">右</button>
            </div>
        </section>

        <!-- 表單系統 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">📝 表單系統</h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl);">
                <div>
                    <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">基礎表單</h3>
                    <form class="form">
                        <div class="form-group">
                            <label class="form-label required">員工姓名</label>
                            <input type="text" class="form-input" placeholder="請輸入員工姓名">
                            <div class="form-help">請輸入完整的中文姓名</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">部門</label>
                            <select class="form-select">
                                <option>請選擇部門</option>
                                <option>技術部</option>
                                <option>業務部</option>
                                <option>人事部</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">備註</label>
                            <textarea class="form-textarea" placeholder="請輸入備註"></textarea>
                        </div>
                    </form>
                </div>

                <div>
                    <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">表單控件</h3>

                    <div class="form-group">
                        <label class="form-label">輸入框尺寸</label>
                        <input type="text" class="form-input form-input-sm" placeholder="小輸入框" style="margin-bottom: var(--spacing-xs);">
                        <input type="text" class="form-input" placeholder="標準輸入框" style="margin-bottom: var(--spacing-xs);">
                        <input type="text" class="form-input form-input-lg" placeholder="大輸入框">
                    </div>

                    <div class="form-group">
                        <label class="form-label">複選框</label>
                        <div class="form-checkbox">
                            <input type="checkbox" id="option1" checked>
                            <label for="option1">選項一</label>
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" id="option2">
                            <label for="option2">選項二</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">單選框</label>
                        <div class="form-radio">
                            <input type="radio" id="radio1" name="radio" value="1" checked>
                            <label for="radio1">選項一</label>
                        </div>
                        <div class="form-radio">
                            <input type="radio" id="radio2" name="radio" value="2">
                            <label for="radio2">選項二</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">開關按鈕</label>
                        <div class="form-switch">
                            <input type="checkbox" id="switch1" checked>
                            <label for="switch1">接收通知</label>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">表單狀態</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                <div class="form-group">
                    <label class="form-label">成功狀態</label>
                    <input type="text" class="form-input is-valid" value="正確的輸入">
                    <div class="form-success">輸入正確</div>
                </div>
                <div class="form-group">
                    <label class="form-label">錯誤狀態</label>
                    <input type="text" class="form-input is-invalid" value="錯誤的輸入">
                    <div class="form-error">請輸入有效的資料</div>
                </div>
            </div>
        </section>

        <!-- 動畫系統 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">🎬 動畫系統</h2>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">基礎動畫</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="animation-demo animate-fade-in"></div>
                    <div class="text-sm">淡入</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-scale-in"></div>
                    <div class="text-sm">縮放進入</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-slide-in-up"></div>
                    <div class="text-sm">滑入</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-bounce-in"></div>
                    <div class="text-sm">彈跳進入</div>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">循環動畫</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="animation-demo animate-spin"></div>
                    <div class="text-sm">旋轉</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-pulse"></div>
                    <div class="text-sm">脈衝</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-bounce"></div>
                    <div class="text-sm">彈跳</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo animate-heartbeat"></div>
                    <div class="text-sm">心跳</div>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">懸停效果</h3>
            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="animation-demo hover-lift"></div>
                    <div class="text-sm">懸停上升</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo hover-grow"></div>
                    <div class="text-sm">懸停放大</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo hover-glow"></div>
                    <div class="text-sm">懸停發光</div>
                </div>
                <div class="showcase-item">
                    <div class="animation-demo hover-rotate"></div>
                    <div class="text-sm">懸停旋轉</div>
                </div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">載入狀態</h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-xl); justify-content: center;">
                <div style="text-align: center;">
                    <div class="loading-spinner" style="margin: 0 auto var(--spacing-sm);"></div>
                    <div class="text-sm">旋轉載入器</div>
                </div>
                <div style="text-align: center;">
                    <div class="loading-dots" style="margin: 0 auto var(--spacing-sm);"></div>
                    <div class="text-sm">點狀載入器</div>
                </div>
                <div style="text-align: center;">
                    <div class="loading-bars" style="margin: 0 auto var(--spacing-sm);"></div>
                    <div class="text-sm">條狀載入器</div>
                </div>
            </div>
        </section>

        <!-- 工具類別 -->
        <section class="showcase-section">
            <h2 class="showcase-title heading-2">🛠️ 工具類別</h2>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">間距系統</h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="background: var(--color-primary-100); padding: var(--spacing-xs); border-radius: var(--radius-sm);">xs (4px)</div>
                <div style="background: var(--color-primary-100); padding: var(--spacing-sm); border-radius: var(--radius-sm);">sm (8px)</div>
                <div style="background: var(--color-primary-100); padding: var(--spacing-md); border-radius: var(--radius-sm);">md (16px)</div>
                <div style="background: var(--color-primary-100); padding: var(--spacing-lg); border-radius: var(--radius-sm);">lg (24px)</div>
                <div style="background: var(--color-primary-100); padding: var(--spacing-xl); border-radius: var(--radius-sm);">xl (32px)</div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">圓角系統</h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-sm);">sm</div>
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-md);">md</div>
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-lg);">lg</div>
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-xl);">xl</div>
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-2xl);">2xl</div>
                <div style="background: var(--color-secondary-100); padding: var(--spacing-md); border-radius: var(--radius-full);">full</div>
            </div>

            <h3 class="heading-4" style="margin-bottom: var(--spacing-md);">陰影系統</h3>
            <div class="showcase-grid">
                <div style="background: var(--bg-primary); padding: var(--spacing-md); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); text-align: center;">
                    <div class="text-sm">Small</div>
                </div>
                <div style="background: var(--bg-primary); padding: var(--spacing-md); border-radius: var(--radius-lg); box-shadow: var(--shadow-md); text-align: center;">
                    <div class="text-sm">Medium</div>
                </div>
                <div style="background: var(--bg-primary); padding: var(--spacing-md); border-radius: var(--radius-lg); box-shadow: var(--shadow-lg); text-align: center;">
                    <div class="text-sm">Large</div>
                </div>
                <div style="background: var(--bg-primary); padding: var(--spacing-md); border-radius: var(--radius-lg); box-shadow: var(--shadow-xl); text-align: center;">
                    <div class="text-sm">Extra Large</div>
                </div>
            </div>
        </section>

        <!-- 頁尾 -->
        <footer class="text-center" style="margin-top: var(--spacing-3xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-light);">
            <p class="paragraph-small" style="color: var(--text-tertiary);">
                Han AttendanceOS v2005.6.12 - 遠漢科技考勤系統<br> © 2024 遠漢科技有限公司. 保留所有權利.
            </p>
        </footer>
    </div>

    <!-- 浮動操作按鈕 -->
    <button class="btn-fab" onclick="window.scrollTo({top: 0, behavior: 'smooth'})">
        ↑
    </button>

    <script>
        // 重新觸發動畫
        function restartAnimations() {
            const animatedElements = document.querySelectorAll('[class*="animate-"]');
            animatedElements.forEach(el => {
                el.style.animation = 'none';
                el.offsetHeight; // 觸發重排
                el.style.animation = null;
            });
        }

        // 每5秒重新觸發動畫
        setInterval(restartAnimations, 5000);

        // 添加互動效果
        document.addEventListener('DOMContentLoaded', function() {
            // 為所有按鈕添加點擊效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 創建漣漪效果
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.5);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加漣漪動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>

</html>