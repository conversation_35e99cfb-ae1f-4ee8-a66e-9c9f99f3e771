#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本資料API測試腳本

功能說明：
- 測試所有基本資料API的GET請求
- 驗證API返回的資料格式和內容
- 生成測試報告

使用方法：
python test_masterdata_apis.py
"""

import requests
import json
import sys
from datetime import datetime

def test_masterdata_api(table_name, description):
    """
    測試基本資料API
    
    參數：
    table_name (str): 資料表名稱
    description (str): 資料表描述
    
    返回：
    dict: 測試結果
    """
    url = f"http://localhost:7072/api/masterdata/{table_name}"
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            return {
                "table_name": table_name,
                "description": description,
                "status": "成功",
                "status_code": response.status_code,
                "total_records": data.get("total", 0),
                "sample_record": data.get("records", [{}])[0] if data.get("records") else None,
                "error": None
            }
        else:
            return {
                "table_name": table_name,
                "description": description,
                "status": "失敗",
                "status_code": response.status_code,
                "total_records": 0,
                "sample_record": None,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        return {
            "table_name": table_name,
            "description": description,
            "status": "錯誤",
            "status_code": None,
            "total_records": 0,
            "sample_record": None,
            "error": str(e)
        }

def main():
    """
    主要測試函數
    """
    print("🔍 基本資料API測試開始")
    print("=" * 60)
    
    # 定義要測試的API
    test_apis = [
        ("education_levels", "學歷等級"),
        ("positions", "職位"),
        ("salary_grades", "薪資等級"),
        ("work_locations", "工作地點"),
        ("skills", "技能"),
        ("clock_status_types", "打卡狀態類型"),
        ("leave_types", "假別類型"),
        ("departments", "部門"),
        ("shifts", "班別")
    ]
    
    results = []
    success_count = 0
    
    for table_name, description in test_apis:
        print(f"\n📋 測試 {description} ({table_name})...")
        result = test_masterdata_api(table_name, description)
        results.append(result)
        
        if result["status"] == "成功":
            success_count += 1
            print(f"   ✅ 成功 - 共 {result['total_records']} 筆記錄")
            if result["sample_record"]:
                sample = result["sample_record"]
                # 顯示第一筆記錄的關鍵欄位
                if "name" in sample:
                    print(f"   📝 範例: {sample['name']}")
                elif "status_name" in sample:
                    print(f"   📝 範例: {sample['status_name']}")
        else:
            print(f"   ❌ {result['status']} - {result['error']}")
    
    # 生成測試報告
    print("\n" + "=" * 60)
    print("📊 測試結果摘要")
    print("=" * 60)
    print(f"總測試項目: {len(test_apis)}")
    print(f"成功項目: {success_count}")
    print(f"失敗項目: {len(test_apis) - success_count}")
    print(f"成功率: {(success_count / len(test_apis) * 100):.1f}%")
    
    # 詳細結果
    print("\n📋 詳細測試結果:")
    for result in results:
        status_icon = "✅" if result["status"] == "成功" else "❌"
        print(f"{status_icon} {result['description']} ({result['table_name']}): {result['status']}")
        if result["status"] == "成功":
            print(f"   記錄數: {result['total_records']}")
        else:
            print(f"   錯誤: {result['error']}")
    
    # 保存詳細報告
    report = {
        "test_time": datetime.now().isoformat(),
        "summary": {
            "total_tests": len(test_apis),
            "successful_tests": success_count,
            "failed_tests": len(test_apis) - success_count,
            "success_rate": round(success_count / len(test_apis) * 100, 1)
        },
        "detailed_results": results
    }
    
    with open("masterdata_api_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 詳細測試報告已保存至: masterdata_api_test_report.json")
    
    # 返回適當的退出碼
    if success_count == len(test_apis):
        print("\n🎉 所有基本資料API測試通過！")
        return 0
    else:
        print(f"\n⚠️  有 {len(test_apis) - success_count} 個API測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 