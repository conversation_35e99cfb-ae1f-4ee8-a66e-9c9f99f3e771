<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>假別類型測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>假別類型動態載入測試</h1>

        <div class="section">
            <h2>1. 測試假別類型 API</h2>
            <button onclick="testLeaveTypesAPI()">載入假別類型</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="section">
            <h2>2. 測試假別類型轉換</h2>
            <button onclick="testLeaveTypeConversion()">測試轉換功能</button>
            <div id="conversionResult" class="result"></div>
        </div>

        <div class="section">
            <h2>3. 測試審批資料</h2>
            <button onclick="testApprovalData()">載入審批資料</button>
            <div id="approvalResult" class="result"></div>
        </div>
    </div>

    <script>
        let leaveTypeMap = {};

        // 測試假別類型 API
        async function testLeaveTypesAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '載入中...';

            try {
                const response = await fetch('/api/masterdata/leave_types');
                const data = await response.json();

                if (response.ok && data.items) {
                    // 建立假別類型對應表
                    leaveTypeMap = {};
                    data.items.forEach(item => {
                        leaveTypeMap[item.code] = item.name;
                    });

                    resultDiv.innerHTML = `
                        <h3>載入成功！共 ${data.items.length} 種假別類型：</h3>
                        <ul>
                            ${data.items.map(item => `<li><strong>${item.code}</strong>: ${item.name}</li>`).join('')}
                        </ul>
                        <p><strong>假別類型對應表：</strong></p>
                        <pre>${JSON.stringify(leaveTypeMap, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">載入失敗：${data.error || '未知錯誤'}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">載入錯誤：${error.message}</p>`;
            }
        }

        // 測試假別類型轉換
        function testLeaveTypeConversion() {
            const resultDiv = document.getElementById('conversionResult');
            
            if (Object.keys(leaveTypeMap).length === 0) {
                resultDiv.innerHTML = '<p style="color: orange;">請先載入假別類型！</p>';
                return;
            }
            
            const testCodes = ['marriage', 'annual', 'sick', 'personal', 'compensatory', 'unknown'];
            
            function getLeaveTypeText(type) {
                return leaveTypeMap[type] || type;
            }
            
            const results = testCodes.map(code => {
                const text = getLeaveTypeText(code);
                return `<li><strong>${code}</strong> → ${text}</li>`;
            });
            
            resultDiv.innerHTML = `
                <h3>假別類型轉換測試：</h3>
                <ul>${results.join('')}</ul>
            `;
        }

        // 測試審批資料
        async function testApprovalData() {
            const resultDiv = document.getElementById('approvalResult');
            resultDiv.innerHTML = '載入中...';
            
            try {
                const response = await fetch('/api/approval/leaves');
                const data = await response.json();
                
                if (response.ok && data.leaves) {
                    function getLeaveTypeText(type) {
                        return leaveTypeMap[type] || type;
                    }
                    
                    const results = data.leaves.slice(0, 5).map(leave => {
                        const originalType = leave.leave_type;
                        const convertedType = getLeaveTypeText(originalType);
                        return `
                            <li>
                                <strong>${leave.employee_name}</strong> - 
                                原始類型: <code>${originalType}</code> → 
                                轉換後: <strong>${convertedType}</strong>
                            </li>
                        `;
                    });
                    
                    resultDiv.innerHTML = `
                        <h3>審批資料測試（前5筆）：</h3>
                        <ul>${results.join('')}</ul>
                    `;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">載入失敗：${data.error || '未知錯誤'}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">載入錯誤：${error.message}</p>`;
            }
        }

        // 頁面載入時自動測試
        document.addEventListener('DOMContentLoaded', function() {
            testLeaveTypesAPI();
        });
    </script>
</body>

</html>