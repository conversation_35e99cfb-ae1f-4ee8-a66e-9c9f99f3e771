[flake8]
max-line-length = 88
max-complexity = 10
select = E,W,F,C
ignore = 
    # Line too long (handled by black)
    E501,
    # Line break before binary operator (conflicts with black)
    W503,
    # Line break after binary operator (conflicts with black)
    W504,
    # Whitespace before ':' (conflicts with black)
    E203

exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .tox,
    .pytest_cache,
    htmlcov,
    .coverage,
    migrations

per-file-ignores =
    # Allow unused imports in __init__.py files
    __init__.py:F401
    # Allow import * in conftest.py
    tests/conftest.py:F403,F401
    # Allow long lines in data migration files
    migrations/*.py:E501

# Error codes reference:
# E: pycodestyle errors
# W: pycodestyle warnings  
# F: pyflakes errors
# C: complexity errors

# Specific codes we care about:
# E722: do not use bare except
# F401: imported but unused
# F403: unable to detect undefined names
# F811: redefinition of unused name
# F841: local variable assigned but never used
# C901: function is too complex