-- 單表多租戶架構：所有公司共用相同表格，用company_id區分

-- 1. 公司主表
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    company_code VARCHAR(50) UNIQUE NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    config JSONB DEFAULT '{}',  -- 存放公司特殊配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 員工表 (添加company_id)
CREATE TABLE employees (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id),
    employee_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    department_id INTEGER,
    position VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    custom_fields JSONB DEFAULT '{}',  -- 存放公司特殊欄位
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 複合唯一索引：同一公司內員工編號不重複
    UNIQUE(company_id, employee_id)
);

-- 3. 部門表 (添加company_id)
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50),
    manager_id INTEGER,
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(company_id, code)
);

-- 4. 考勤記錄表 (添加company_id)
CREATE TABLE attendance_records (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id),
    employee_id INTEGER NOT NULL REFERENCES employees(id),
    date DATE NOT NULL,
    shift_id INTEGER,
    check_in_time TIME,
    check_out_time TIME,
    status VARCHAR(50),
    custom_fields JSONB DEFAULT '{}',  -- ABC公司的project_code等
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(company_id, employee_id, date)
);

-- 5. 請假記錄表 (添加company_id)
CREATE TABLE leave_requests (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id),
    employee_id INTEGER NOT NULL REFERENCES employees(id),
    leave_type_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 班表設定 (添加company_id)
CREATE TABLE shifts (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id),
    name VARCHAR(100) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(company_id, name)
);

-- 重要索引：確保查詢效能
CREATE INDEX idx_employees_company_id ON employees(company_id);
CREATE INDEX idx_attendance_company_id ON attendance_records(company_id);
CREATE INDEX idx_attendance_company_date ON attendance_records(company_id, date);
CREATE INDEX idx_leave_requests_company_id ON leave_requests(company_id);
CREATE INDEX idx_departments_company_id ON departments(company_id);
CREATE INDEX idx_shifts_company_id ON shifts(company_id);

-- 行級安全策略 (Row Level Security)
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE leave_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE shifts ENABLE ROW LEVEL SECURITY;

-- 創建安全策略：用戶只能看到自己公司的數據
CREATE POLICY company_isolation_employees ON employees
    FOR ALL TO PUBLIC
    USING (company_id = current_setting('app.current_company_id')::INTEGER);

CREATE POLICY company_isolation_attendance ON attendance_records
    FOR ALL TO PUBLIC
    USING (company_id = current_setting('app.current_company_id')::INTEGER);

CREATE POLICY company_isolation_leaves ON leave_requests
    FOR ALL TO PUBLIC
    USING (company_id = current_setting('app.current_company_id')::INTEGER);

-- 插入範例公司數據
INSERT INTO companies (company_code, company_name, config) VALUES
('default', '預設公司', '{"api_version": "v1", "features": ["basic"]}'),
('abc_corp', 'ABC公司', '{"api_version": "v2", "features": ["project_tracking", "cost_center"]}'),
('xyz_ltd', 'XYZ有限公司', '{"api_version": "v2", "features": ["security_tracking"]}');

-- 插入範例員工數據 (每家公司都有相同的員工結構)
INSERT INTO employees (company_id, employee_id, name, email, custom_fields) VALUES
-- 預設公司
(1, 'E001', '黎麗玲', '<EMAIL>', '{}'),
(1, 'E002', '蔡秀娟', '<EMAIL>', '{}'),

-- ABC公司 (有專案代碼和成本中心)
(2, 'E001', '王大明', '<EMAIL>', '{"project_code": "PRJ001", "cost_center": "CC100"}'),
(2, 'E002', '李小華', '<EMAIL>', '{"project_code": "PRJ002", "cost_center": "CC200"}'),

-- XYZ公司 (有安全等級)
(3, 'E001', '張三豐', '<EMAIL>', '{"security_clearance": "SECRET", "department_level": 3}'),
(3, 'E002', '趙雲龍', '<EMAIL>', '{"security_clearance": "TOP_SECRET", "department_level": 5}'); 