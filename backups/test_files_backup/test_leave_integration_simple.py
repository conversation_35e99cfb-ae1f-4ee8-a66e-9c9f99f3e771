#!/usr/bin/env python3
"""
簡化的請假整合功能測試

測試改進後的跨多天請假處理功能
"""

import sqlite3
import requests
import json
from datetime import datetime, timedelta
from database import create_connection

# API設定
API_BASE_URL = "http://localhost:7072"

def setup_test_data():
    """
    設置測試資料
    """
    print("=== 設置測試資料 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 清理現有測試資料
        cursor.execute("DELETE FROM leave_requests WHERE employee_id IN (1, 2)")
        cursor.execute("DELETE FROM attendance_records WHERE employee_id IN (1, 2)")
        # 注意：punch_records表已刪除，不需要清理
        
        # 確保有測試員工
        cursor.execute("SELECT COUNT(*) FROM employees WHERE id IN (1, 2)")
        if cursor.fetchone()[0] < 2:
            print("警告：測試員工不足，請確保資料庫中有ID為1、2的員工")
            return False
        
        # 創建測試請假記錄
        today = datetime.now().date()
        test_date_1 = today + timedelta(days=1)  # 明天
        test_date_2 = today + timedelta(days=2)  # 後天
        
        # 員工1：請假兩天（跨多天請假）
        cursor.execute("""
            INSERT INTO leave_requests (
                employee_id, leave_type, start_date, end_date, 
                leave_hours, reason, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            1, '年假', 
            test_date_1.strftime('%Y-%m-%d'), 
            test_date_2.strftime('%Y-%m-%d'),
            16.0, '家庭事務', 'approved'
        ))
        
        # 員工2：請假一天（全天請假）
        cursor.execute("""
            INSERT INTO leave_requests (
                employee_id, leave_type, start_date, end_date, 
                leave_hours, reason, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
        """, (
            2, '病假', 
            test_date_1.strftime('%Y-%m-%d'), 
            test_date_1.strftime('%Y-%m-%d'),
            8.0, '身體不適', 'approved'
        ))
        
        conn.commit()
        print("✓ 測試資料設置完成")
        print(f"  - 員工1：{test_date_1} 至 {test_date_2} 請假兩天（16小時）")
        print(f"  - 員工2：{test_date_1} 請假一天（8小時）")
        
        return True
        
    except Exception as e:
        print(f"✗ 設置測試資料失敗: {e}")
        return False
    finally:
        conn.close()

def test_attendance_generation():
    """
    測試考勤生成功能
    """
    print("\n=== 測試考勤生成功能 ===")
    
    today = datetime.now().date()
    test_dates = [
        (today + timedelta(days=1)).strftime('%Y-%m-%d'),  # 明天
        (today + timedelta(days=2)).strftime('%Y-%m-%d'),  # 後天
    ]
    
    for test_date in test_dates:
        print(f"\n--- 測試日期：{test_date} ---")
        
        try:
            # 調用考勤生成API
            response = requests.post(f'{API_BASE_URL}/api/attendance/management/generate-complete', 
                                   json={
                                       'target_date': test_date,
                                       'force_regenerate': True
                                   },
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print("✓ 考勤生成成功")
                print(f"  - 處理員工數：{result.get('processing_summary', {}).get('total_active_employees', 0)}")
                print(f"  - 生成記錄數：{result.get('generation_results', {}).get('generated_count', 0)}")
                print(f"  - 更新記錄數：{result.get('generation_results', {}).get('updated_count', 0)}")
            else:
                print(f"✗ 考勤生成失敗: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"✗ 調用API失敗: {e}")

def verify_attendance_records():
    """
    驗證生成的考勤記錄
    """
    print("\n=== 驗證考勤記錄 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        today = datetime.now().date()
        test_dates = [
            (today + timedelta(days=1)).strftime('%Y-%m-%d'),  # 明天
            (today + timedelta(days=2)).strftime('%Y-%m-%d'),  # 後天
        ]
        
        # 先檢查請假記錄
        print("--- 檢查請假記錄 ---")
        cursor.execute("""
            SELECT employee_id, leave_type, start_date, end_date, leave_hours, status
            FROM leave_requests 
            WHERE employee_id IN (1, 2)
            ORDER BY created_at DESC
        """)
        
        leave_records = cursor.fetchall()
        if leave_records:
            print(f"找到 {len(leave_records)} 筆請假記錄：")
            for record in leave_records:
                emp_id, leave_type, start_date, end_date, leave_hours, status = record
                print(f"  員工{emp_id} - {leave_type}: {start_date} 至 {end_date}, {leave_hours}小時, 狀態: {status}")
        else:
            print("沒有找到請假記錄")
        
        for test_date in test_dates:
            print(f"\n--- 驗證日期：{test_date} ---")
            
            # 查詢該日期的考勤記錄
            cursor.execute("""
                SELECT ar.employee_id, e.name, ar.status, ar.leave_hours, ar.note,
                       ar.clock_in_time, ar.clock_out_time, ar.work_date, ar.created_at
                FROM attendance_records ar
                JOIN employees e ON ar.employee_id = e.id
                WHERE ar.employee_id IN (1, 2)
                AND ar.work_date = ?
                ORDER BY ar.employee_id
            """, (test_date,))
            
            records = cursor.fetchall()
            
            if not records:
                print("  ⚠️  沒有找到考勤記錄")
                continue
            
            for record in records:
                emp_id, emp_name, status, leave_hours, note, clock_in, clock_out, work_date, created_at = record
                print(f"  員工{emp_id}（{emp_name}）:")
                print(f"    - 工作日期: {work_date}")
                print(f"    - 狀態: {status}")
                print(f"    - 請假時數: {leave_hours or 0}")
                print(f"    - 上班時間: {clock_in or '無'}")
                print(f"    - 下班時間: {clock_out or '無'}")
                print(f"    - 備註: {note or '無'}")
                print(f"    - 創建時間: {created_at}")
                
                # 驗證邏輯
                if test_date == (today + timedelta(days=1)).strftime('%Y-%m-%d'):
                    # 明天的驗證
                    if emp_id == 1:
                        expected_hours = 8.0  # 跨多天請假，每天8小時
                        if abs((leave_hours or 0) - expected_hours) < 0.1:
                            print("    ✓ 員工1請假時數正確（跨多天請假第一天）")
                        else:
                            print(f"    ✗ 員工1請假時數錯誤，期望{expected_hours}，實際{leave_hours}")
                    
                    elif emp_id == 2:
                        expected_hours = 8.0  # 全天請假
                        if abs((leave_hours or 0) - expected_hours) < 0.1:
                            print("    ✓ 員工2全天請假正確")
                        else:
                            print(f"    ✗ 員工2請假處理錯誤，期望8小時，實際{leave_hours}小時")
                
                elif test_date == (today + timedelta(days=2)).strftime('%Y-%m-%d'):
                    # 後天的驗證（只有員工1有請假）
                    if emp_id == 1:
                        expected_hours = 8.0  # 跨多天請假，每天8小時
                        if abs((leave_hours or 0) - expected_hours) < 0.1:
                            print("    ✓ 員工1請假時數正確（跨多天請假第二天）")
                        else:
                            print(f"    ✗ 員工1請假時數錯誤，期望{expected_hours}，實際{leave_hours}")
                    elif emp_id == 2:
                        # 員工2在後天應該沒有請假
                        if (leave_hours or 0) == 0:
                            print("    ✓ 員工2在後天沒有請假，正確")
                        else:
                            print(f"    ✗ 員工2在後天不應該有請假，但實際有{leave_hours}小時")
                
                print()
        
        # 額外檢查：驗證work_date欄位是否正確設置
        print("\n--- 檢查work_date欄位設置 ---")
        cursor.execute("""
            SELECT employee_id, work_date, created_at
            FROM attendance_records 
            WHERE employee_id IN (1, 2)
            AND work_date IS NOT NULL
            ORDER BY work_date, employee_id
        """)
        
        work_date_records = cursor.fetchall()
        if work_date_records:
            print(f"找到 {len(work_date_records)} 筆有work_date的記錄：")
            for record in work_date_records:
                emp_id, work_date, created_at = record
                print(f"  員工{emp_id} - 工作日期: {work_date}, 創建時間: {created_at}")
        else:
            print("沒有找到有work_date的記錄")
        
    except Exception as e:
        print(f"✗ 驗證考勤記錄失敗: {e}")
    finally:
        conn.close()

def test_api_connectivity():
    """
    測試API連接性
    """
    print("\n=== 測試API連接性 ===")
    
    try:
        # 測試基本API連接
        response = requests.get(f'{API_BASE_URL}/api/attendance/management/last-process-date', timeout=5)
        
        if response.status_code == 200:
            print("✓ API連接正常")
            result = response.json()
            print(f"  - 上次處理日期: {result.get('last_process_date', '無')}")
        else:
            print(f"✗ API連接失敗: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 測試API連接失敗: {e}")

def cleanup_test_data():
    """
    清理測試資料
    """
    print("\n=== 清理測試資料 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("DELETE FROM leave_requests WHERE employee_id IN (1, 2)")
        cursor.execute("DELETE FROM attendance_records WHERE employee_id IN (1, 2)")
        # 注意：punch_records表已刪除，不需要清理
        
        conn.commit()
        print("✓ 測試資料清理完成")
        
    except Exception as e:
        print(f"✗ 清理測試資料失敗: {e}")
    finally:
        conn.close()

def main():
    """
    主測試流程
    """
    print("🚀 開始測試改進後的請假整合功能")
    print("=" * 50)
    
    # 測試API連接性
    test_api_connectivity()
    
    # 設置測試資料
    if not setup_test_data():
        print("❌ 測試資料設置失敗，終止測試")
        return
    
    # 測試考勤生成
    test_attendance_generation()
    
    # 驗證考勤記錄
    verify_attendance_records()
    
    # 額外檢查：直接查詢資料庫
    print("\n=== 直接查詢資料庫檢查 ===")
    conn = create_connection()
    cursor = conn.cursor()
    
    # 檢查所有考勤記錄
    cursor.execute("""
        SELECT employee_id, work_date, status, leave_hours, note, created_at
        FROM attendance_records 
        WHERE employee_id IN (1, 2)
        ORDER BY created_at DESC
    """)
    
    all_records = cursor.fetchall()
    if all_records:
        print(f"找到 {len(all_records)} 筆員工1和2的考勤記錄：")
        for record in all_records:
            emp_id, work_date, status, leave_hours, note, created_at = record
            print(f"  員工{emp_id} - 工作日期: {work_date}, 狀態: {status}, 請假時數: {leave_hours}, 創建時間: {created_at}")
            if note:
                print(f"    備註: {note}")
    else:
        print("沒有找到員工1和2的考勤記錄")
    
    conn.close()
    
    # 詢問是否清理測試資料
    print("\n" + "=" * 50)
    print("測試完成！保留測試資料以供檢查。")
    print("如需清理測試資料，請手動執行 cleanup_test_data() 函數")
    
    print("\n🎉 測試完成！")

if __name__ == "__main__":
    main() 