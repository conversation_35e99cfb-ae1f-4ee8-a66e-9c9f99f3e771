#!/usr/bin/env python3\n\"\"\"\n員工狀態類型 API\n\n此模組提供員工狀態類型的 API 端點\n\"\"\"\n\nfrom flask import jsonify\nfrom database import create_connection\nimport logging\n\ndef get_employee_status_types():\n    \"\"\"\n    獲取員工狀態類型設定\n    \n    返回：\n    - items: 員工狀態列表\n    - total: 總數量\n    \"\"\"\n    conn = create_connection()\n    try:\n        cursor = conn.cursor()\n        \n        # 從系統設定中獲取員工狀態\n        cursor.execute(\"\"\"\n            SELECT setting_key as code, setting_value as name, description\n            FROM system_settings \n            WHERE category = 'employee_status'\n            ORDER BY \n                CASE setting_key \n                    WHEN 'active' THEN 1\n                    WHEN 'trial' THEN 2\n                    WHEN 'inactive' THEN 3\n                    WHEN 'leave' THEN 4\n                    ELSE 5\n                END\n        \"\"\")\n        \n        columns = [col[0] for col in cursor.description]\n        items = [dict(zip(columns, row)) for row in cursor.fetchall()]\n        \n        # 如果沒有設定，返回預設狀態\n        if not items:\n            items = [\n                {'code': 'active', 'name': '在職', 'description': '正常在職狀態'},\n                {'code': 'trial', 'name': '試用期', 'description': '試用期員工'},\n                {'code': 'inactive', 'name': '停職', 'description': '暫時停職狀態'},\n                {'code': 'leave', 'name': '離職', 'description': '已離職員工'}\n            ]\n        \n        return jsonify({\n            'items': items,\n            'total': len(items),\n            'table_name': 'employee_status',\n            'display_name': '員工狀態設定'\n        })\n        \n    except Exception as e:\n        logging.error(f\"獲取員工狀態類型錯誤: {str(e)}\")\n        return jsonify({\"error\": str(e)}), 500\n    finally:\n        conn.close() 