"""
通知服務模組。

此模組負責：
- 發送通知（電子郵件、應用內通知等）
- 管理通知記錄
- 處理通知規則
"""

import json
import logging
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from database import create_connection
from config import Config


class NotificationService:
    """通知服務類，處理系統中的各類通知"""

    def __init__(self):
        """初始化通知服務"""
        self.conn = create_connection()
        self.logger = logging.getLogger("notification_service")
    
    def close(self):
        """關閉資料庫連接"""
        if self.conn:
            self.conn.close()
    
    def get_notification_rules(self, event_type):
        """
        取得特定事件類型的通知規則。
        
        參數：
        event_type (str): 事件類型，如 'late', 'absent', 'leave_request' 等
        
        返回：
        list: 通知規則列表
        """
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT * FROM notification_rules
                WHERE event_type = ?
            """, (event_type,))
            
            columns = [col[0] for col in cursor.description]
            rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return rules
        except Exception as e:
            self.logger.error(f"取得通知規則錯誤: {str(e)}")
            return []
    
    def create_notification(self, type, recipients, message, status="pending"):
        """
        建立新的通知記錄。
        
        參數：
        type (str): 通知類型，如 'email', 'app', 'sms' 等
        recipients (list): 接收者列表，可以是員工ID或角色名稱
        message (str): 通知內容
        status (str): 通知狀態，預設為 'pending'
        
        返回：
        int: 新建通知的ID，失敗則返回-1
        """
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO notifications (type, recipients, message, status, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (type, json.dumps(recipients), message, status, datetime.now()))
            
            self.conn.commit()
            return cursor.lastrowid
        except Exception as e:
            self.logger.error(f"建立通知錯誤: {str(e)}")
            return -1
    
    def update_notification_status(self, notification_id, status, sent_at=None):
        """
        更新通知狀態。
        
        參數：
        notification_id (int): 通知ID
        status (str): 新的狀態，如 'sent', 'failed' 等
        sent_at (datetime): 發送時間，預設為當前時間
        
        返回：
        bool: 更新是否成功
        """
        try:
            cursor = self.conn.cursor()
            if sent_at is None and status == "sent":
                sent_at = datetime.now()
                
            if sent_at:
                cursor.execute("""
                    UPDATE notifications
                    SET status = ?, sent_at = ?
                    WHERE id = ?
                """, (status, sent_at, notification_id))
            else:
                cursor.execute("""
                    UPDATE notifications
                    SET status = ?
                    WHERE id = ?
                """, (status, notification_id))
            
            self.conn.commit()
            return True
        except Exception as e:
            self.logger.error(f"更新通知狀態錯誤: {str(e)}")
            return False
    
    def send_email_notification(self, to_emails, subject, body):
        """
        發送電子郵件通知。
        
        參數：
        to_emails (list): 收件人電子郵件列表
        subject (str): 郵件主題
        body (str): 郵件內容
        
        返回：
        bool: 發送是否成功
        """
        try:
            # 這裡為了演示，只是記錄訊息，實際應用中需實現SMTP發送
            self.logger.info(f"發送電子郵件: 收件人={to_emails}, 主題={subject}")
            
            # 如果有配置SMTP伺服器，可以啟用以下代碼
            if hasattr(Config, 'SMTP_SERVER') and Config.SMTP_SERVER:
                msg = MIMEMultipart()
                msg['From'] = Config.SMTP_SENDER
                msg['To'] = ", ".join(to_emails)
                msg['Subject'] = subject
                msg.attach(MIMEText(body, 'html'))
                
                server = smtplib.SMTP(Config.SMTP_SERVER, Config.SMTP_PORT)
                server.ehlo()
                if Config.SMTP_USE_TLS:
                    server.starttls()
                
                if Config.SMTP_USERNAME and Config.SMTP_PASSWORD:
                    server.login(Config.SMTP_USERNAME, Config.SMTP_PASSWORD)
                
                server.sendmail(Config.SMTP_SENDER, to_emails, msg.as_string())
                server.close()
            
            return True
        except Exception as e:
            self.logger.error(f"發送電子郵件錯誤: {str(e)}")
            return False
    
    def send_app_notification(self, user_ids, title, message):
        """
        發送應用內通知。
        
        參數：
        user_ids (list): 用戶ID列表
        title (str): 通知標題
        message (str): 通知內容
        
        返回：
        bool: 發送是否成功
        """
        try:
            for user_id in user_ids:
                self.create_notification(
                    type="app",
                    recipients=[user_id],
                    message=json.dumps({"title": title, "message": message}),
                    status="sent"
                )
            return True
        except Exception as e:
            self.logger.error(f"發送應用內通知錯誤: {str(e)}")
            return False
    
    def process_leave_request_notification(self, leave_id, employee_name, dept_name, start_date, end_date):
        """
        處理請假申請通知。
        
        參數：
        leave_id (int): 請假記錄ID
        employee_name (str): 員工姓名
        dept_name (str): 部門名稱
        start_date (str): 開始日期
        end_date (str): 結束日期
        
        返回：
        bool: 處理是否成功
        """
        try:
            # 取得通知規則
            rules = self.get_notification_rules("leave_request")
            if not rules:
                self.logger.warning("未找到請假申請的通知規則")
                return False
            
            # 取得部門主管
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT e.id, e.name, e.email 
                FROM employees e 
                JOIN departments d ON e.id = d.manager_id
                WHERE d.name = ?
            """, (dept_name,))
            
            manager = cursor.fetchone()
            if not manager:
                self.logger.warning(f"未找到部門 {dept_name} 的主管")
                return False
            
            # 發送通知
            for rule in rules:
                recipients = json.loads(rule["recipients"])
                if "department_manager" in recipients and manager[2]:  # 確認主管有郵件
                    subject = f"請假申請通知: {employee_name} ({start_date} - {end_date})"
                    body = f"""
                        <p>尊敬的 {manager[1]}：</p>
                        <p>您有一個新的請假申請需要審批：</p>
                        <ul>
                            <li>員工: {employee_name}</li>
                            <li>部門: {dept_name}</li>
                            <li>請假期間: {start_date} 至 {end_date}</li>
                        </ul>
                        <p>請登入系統進行審批。</p>
                    """
                    self.send_email_notification([manager[2]], subject, body)
                
                if rule["notification_type"] == "app":
                    self.send_app_notification(
                        [manager[0]], 
                        "新請假申請",
                        f"{employee_name} ({start_date} - {end_date}) 申請請假，請審批"
                    )
            
            return True
        except Exception as e:
            self.logger.error(f"處理請假申請通知錯誤: {str(e)}")
            return False
    
    def process_attendance_alert(self, employee_id, event_type, timestamp=None):
        """
        處理考勤警報通知。
        
        參數：
        employee_id (int): 員工ID
        event_type (str): 事件類型，如 'late', 'absent', 'early_leave' 等
        timestamp (datetime): 時間戳，預設為當前時間
        
        返回：
        bool: 處理是否成功
        """
        try:
            if timestamp is None:
                timestamp = datetime.now()
                
            # 取得通知規則
            rules = self.get_notification_rules(event_type)
            if not rules:
                self.logger.warning(f"未找到 {event_type} 事件的通知規則")
                return False
            
            # 取得員工和部門資訊
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT e.name, e.email, d.name as dept_name, m.id as manager_id, m.name as manager_name, m.email as manager_email
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                LEFT JOIN employees m ON d.manager_id = m.id
                WHERE e.id = ?
            """, (employee_id,))
            
            employee_info = cursor.fetchone()
            if not employee_info:
                self.logger.warning(f"未找到員工 ID {employee_id} 的資訊")
                return False
            
            event_type_display = {
                "late": "遲到",
                "absent": "缺勤",
                "early_leave": "早退"
            }.get(event_type, event_type)
            
            # 發送通知
            for rule in rules:
                recipients = json.loads(rule["recipients"])
                
                # 員工通知
                if "employee" in recipients and employee_info[1]:  # 確認員工有郵件
                    subject = f"考勤異常通知: {event_type_display}"
                    body = f"""
                        <p>{employee_info[0]} 您好：</p>
                        <p>系統檢測到您在 {timestamp.strftime('%Y-%m-%d %H:%M:%S')} 有考勤異常記錄：{event_type_display}</p>
                        <p>請注意考勤時間，如有特殊情況請向主管說明。</p>
                    """
                    self.send_email_notification([employee_info[1]], subject, body)
                
                # 部門主管通知
                if "department_manager" in recipients and employee_info[5]:  # 確認主管有郵件
                    subject = f"員工考勤異常通知: {employee_info[0]} - {event_type_display}"
                    body = f"""
                        <p>{employee_info[4]} 您好：</p>
                        <p>您部門的員工 {employee_info[0]} 在 {timestamp.strftime('%Y-%m-%d %H:%M:%S')} 有考勤異常記錄：{event_type_display}</p>
                        <p>請登入系統查看詳情。</p>
                    """
                    self.send_email_notification([employee_info[5]], subject, body)
                    
                # 應用內通知
                if rule["notification_type"] == "app":
                    if "employee" in recipients:
                        self.send_app_notification(
                            [employee_id], 
                            f"考勤異常: {event_type_display}",
                            f"您在 {timestamp.strftime('%Y-%m-%d')} 有考勤異常記錄: {event_type_display}"
                        )
                    
                    if "department_manager" in recipients and employee_info[3]:
                        self.send_app_notification(
                            [employee_info[3]], 
                            f"員工考勤異常",
                            f"{employee_info[0]} 在 {timestamp.strftime('%Y-%m-%d')} 有考勤異常: {event_type_display}"
                        )
            
            return True
        except Exception as e:
            self.logger.error(f"處理考勤警報通知錯誤: {str(e)}")
            return False


notification_service = NotificationService()


def get_notification_service():
    """
    獲取通知服務實例。
    
    返回：
    NotificationService: 通知服務實例
    """
    return notification_service 