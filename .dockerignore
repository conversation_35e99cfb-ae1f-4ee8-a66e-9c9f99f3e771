# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
.venv

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor
.vscode
.idea
*.swp
*.swo

# Logs
*.log
logs

# Database (exclude in production)
*.db
*.sqlite
*.sqlite3

# Uploads (handle separately in production)
uploads/*

# Development
.env.local
.env.development
.env.test
node_modules

# Documentation
docs
README.md
CONTRIBUTING.md
.pre-commit-config.yaml
Makefile

# Development dependencies
requirements-dev.txt