"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
    Database,
    Search,
    Plus,
    Edit,
    Trash2,
    ArrowLeft,
    GraduationCap,
    Briefcase,
    Calendar,
    DollarSign,
    MapPin,
    Award,
    Clock,
    Building,
    RefreshCw,
    CheckCircle,
    XCircle,
    Info,
    Users,
    Settings
} from 'lucide-react'
import Link from 'next/link'

// 基本資料類別配置
const MASTERDATA_CATEGORIES = [
    {
        id: 'education_levels',
        name: '學歷',
        icon: GraduationCap,
        color: 'from-blue-500 to-indigo-500',
        description: '管理學歷等級設定',
        fields: [
            { name: 'name', label: '學歷名稱', type: 'text', required: true, icon: GraduationCap },
            { name: 'level_order', label: '等級順序', type: 'number', required: true, icon: Award },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'positions',
        name: '職位',
        icon: Briefcase,
        color: 'from-green-500 to-emerald-500',
        description: '管理職位等級設定',
        fields: [
            { name: 'name', label: '職位名稱', type: 'text', required: true, icon: Briefcase },
            { name: 'level_order', label: '職位等級', type: 'number', required: true, icon: Award },
            { name: 'salary_range_min', label: '最低薪資', type: 'number', required: false, icon: DollarSign },
            { name: 'salary_range_max', label: '最高薪資', type: 'number', required: false, icon: DollarSign },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'leave_types',
        name: '假別',
        icon: Calendar,
        color: 'from-purple-500 to-pink-500',
        description: '管理假別類型設定',
        fields: [
            { name: 'name', label: '假別名稱', type: 'text', required: true, icon: Calendar },
            { name: 'code', label: '假別代碼', type: 'text', required: true, icon: Info },
            { name: 'annual_limit', label: '年度上限(天)', type: 'number', required: false, icon: Award },
            { name: 'is_paid', label: '是否有薪', type: 'checkbox', required: false, icon: DollarSign },
            { name: 'requires_approval', label: '需要審核', type: 'checkbox', required: false, icon: CheckCircle },
            { name: 'advance_notice_days', label: '提前申請天數', type: 'number', required: false, icon: Clock },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'salary_grades',
        name: '薪資等級',
        icon: DollarSign,
        color: 'from-yellow-500 to-orange-500',
        description: '管理薪資等級設定',
        fields: [
            { name: 'name', label: '等級名稱', type: 'text', required: true, icon: Award },
            { name: 'grade_code', label: '等級代碼', type: 'text', required: true, icon: Info },
            { name: 'level_order', label: '排序', type: 'number', required: true, icon: Award },
            { name: 'min_salary', label: '最低薪資', type: 'number', required: true, icon: DollarSign },
            { name: 'max_salary', label: '最高薪資', type: 'number', required: true, icon: DollarSign },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'work_locations',
        name: '工作地點',
        icon: MapPin,
        color: 'from-red-500 to-pink-500',
        description: '管理工作地點設定',
        fields: [
            { name: 'name', label: '地點名稱', type: 'text', required: true, icon: MapPin },
            { name: 'address', label: '地址', type: 'text', required: false, icon: MapPin },
            { name: 'city', label: '城市', type: 'text', required: false, icon: Building },
            { name: 'country', label: '國家', type: 'text', required: false, icon: Building },
            { name: 'is_remote', label: '遠端工作', type: 'checkbox', required: false, icon: Settings },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'skills',
        name: '專業技能',
        icon: Award,
        color: 'from-indigo-500 to-purple-500',
        description: '管理專業技能設定',
        fields: [
            { name: 'name', label: '技能名稱', type: 'text', required: true, icon: Award },
            { name: 'category', label: '技能分類', type: 'text', required: false, icon: Building },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'clock_status_types',
        name: '打卡狀態設定',
        icon: Clock,
        color: 'from-cyan-500 to-blue-500',
        description: '管理打卡狀態設定',
        fields: [
            { name: 'status_code', label: '狀態代碼', type: 'text', required: true, icon: Info },
            { name: 'status_name', label: '狀態名稱', type: 'text', required: true, icon: Clock },
            { name: 'color_code', label: '顏色代碼', type: 'text', required: false, icon: Settings },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info }
        ]
    },
    {
        id: 'departments',
        name: '部門',
        icon: Building,
        color: 'from-teal-500 to-cyan-500',
        description: '管理部門設定',
        fields: [
            { name: 'name', label: '部門名稱', type: 'text', required: true, icon: Building },
            { name: 'manager_id', label: '主管ID', type: 'number', required: false, icon: Users },
            { name: 'description', label: '說明', type: 'textarea', required: false, icon: Info },
            { name: 'permission_id', label: '權限ID', type: 'number', required: false, icon: Settings }
        ]
    }
]

export default function MasterdataPage() {
    const router = useRouter()
    const [selectedCategory, setSelectedCategory] = useState<string>('')
    const [records, setRecords] = useState<any[]>([])
    const [loading, setLoading] = useState(false)
    const [showModal, setShowModal] = useState(false)
    const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
    const [selectedRecord, setSelectedRecord] = useState<any>(null)
    const [formData, setFormData] = useState<any>({})

    // 獲取當前選中類別的配置
    const getCurrentCategory = () => {
        return MASTERDATA_CATEGORIES.find(cat => cat.id === selectedCategory)
    }

    // 載入記錄
    const loadRecords = async (categoryId: string) => {
        if (!categoryId) return

        setLoading(true)
        try {
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/masterdata/${categoryId}`)
            const data = await response.json()

            if (data.records) {
                setRecords(data.records || [])
            } else if (data.success) {
                setRecords(data.data || [])
            } else {
                console.error('載入記錄失敗:', data.error)
                setRecords([])
            }
        } catch (error) {
            console.error('載入記錄錯誤:', error)
            setRecords([])
        } finally {
            setLoading(false)
        }
    }

    // 選擇類別
    const handleCategorySelect = (categoryId: string) => {
        setSelectedCategory(categoryId)
        loadRecords(categoryId)
    }

    // 新增記錄
    const handleAdd = () => {
        const category = getCurrentCategory()
        if (!category) return

        const initialData: any = {}
        category.fields.forEach(field => {
            if (field.type === 'checkbox') {
                initialData[field.name] = false
            } else if (field.type === 'number') {
                initialData[field.name] = ''
            } else {
                initialData[field.name] = ''
            }
        })

        setFormData(initialData)
        setSelectedRecord(null)
        setModalMode('add')
        setShowModal(true)
    }

    // 編輯記錄
    const handleEdit = (record: any) => {
        setFormData({ ...record })
        setSelectedRecord(record)
        setModalMode('edit')
        setShowModal(true)
    }

    // 刪除記錄
    const handleDelete = async (record: any) => {
        if (!confirm('確定要刪除這筆記錄嗎？')) return

        try {
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/masterdata/${selectedCategory}/${record.id}`, {
                method: 'DELETE'
            })
            const data = await response.json()

            if (data.message || response.ok) {
                loadRecords(selectedCategory)
            } else {
                alert('刪除失敗: ' + (data.error || '未知錯誤'))
            }
        } catch (error) {
            console.error('刪除錯誤:', error)
            alert('刪除失敗')
        }
    }

    // 保存記錄
    const handleSave = async (e: React.FormEvent) => {
        e.preventDefault()

        const category = getCurrentCategory()
        if (!category) return

        // 驗證必填欄位
        for (const field of category.fields) {
            if (field.required && !formData[field.name]) {
                alert(`請填寫 ${field.label}`)
                return
            }
        }

        try {
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const url = modalMode === 'add'
                ? `${apiBaseUrl}/api/masterdata/${selectedCategory}`
                : `${apiBaseUrl}/api/masterdata/${selectedCategory}/${selectedRecord.id}`

            const method = modalMode === 'add' ? 'POST' : 'PUT'

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })

            const data = await response.json()

            if (data.message || data.id || response.ok) {
                setShowModal(false)
                loadRecords(selectedCategory)
            } else {
                alert('保存失敗: ' + (data.error || '未知錯誤'))
            }
        } catch (error) {
            console.error('保存錯誤:', error)
            alert('保存失敗')
        }
    }

    // 格式化顯示值
    const formatValue = (value: any, field: any) => {
        if (field.type === 'checkbox') {
            return value ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    是
                </span>
            ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <XCircle className="w-3 h-3 mr-1" />
                    否
                </span>
            )
        }

        if ((field.name.includes('salary') || field.name.includes('range')) && value) {
            return `NT$ ${Number(value).toLocaleString()}`
        }

        return value || '-'
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            {/* 📦 主容器 - 內容區域容器 */}
            <div className="p-6">
                {/* 🎨 頁面標題 - 統一的標題設計 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        {/* 📍 左側標題區 */}
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">基本資料管理</h1>
                            <div className="flex items-center space-x-2">
                                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                    <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                    <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                                </Link>
                                <p className="text-indigo-100 text-base font-medium">管理學歷、職位、假別、薪資等級 基本資料設定</p>
                            </div>
                        </div>

                        {/* 📍 右側資訊區 */}
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">基本資料管理</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <Database className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* 🏷️ 類別選擇區 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 mb-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">選擇資料類別</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {MASTERDATA_CATEGORIES.map((category) => {
                            const IconComponent = category.icon
                            return (
                                <button
                                    key={category.id}
                                    onClick={() => handleCategorySelect(category.id)}
                                    className={`p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${selectedCategory === category.id
                                        ? 'border-indigo-500 bg-indigo-50 shadow-lg'
                                        : 'border-gray-200 bg-white hover:border-indigo-300 hover:shadow-md'
                                        }`}
                                >
                                    <div className={`w-10 h-10 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center mb-3 mx-auto`}>
                                        <IconComponent className="w-5 h-5 text-white" />
                                    </div>
                                    <h3 className="font-semibold text-gray-900 text-sm mb-1">{category.name}</h3>
                                    <p className="text-xs text-gray-600">{category.description}</p>
                                </button>
                            )
                        })}
                    </div>
                </div>

                {/* 📊 資料列表區 */}
                {selectedCategory && (
                    <>
                        {/* 🔧 工具欄 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                        {getCurrentCategory()?.name} 資料列表
                                    </h2>
                                    <span className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                                        {records.length} 筆記錄
                                    </span>
                                </div>

                                <div className="flex items-center space-x-3">
                                    <Button
                                        onClick={() => loadRecords(selectedCategory)}
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center space-x-2"
                                    >
                                        <RefreshCw className="w-4 h-4" />
                                        <span>重新整理</span>
                                    </Button>

                                    <Button
                                        onClick={handleAdd}
                                        className="bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 flex items-center space-x-2"
                                    >
                                        <Plus className="w-4 h-4" />
                                        <span>新增</span>
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* 📋 資料表格 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                            {loading ? (
                                <div className="flex items-center justify-center py-16">
                                    <div className="text-center">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                                            <div className="relative">
                                                <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                                            </div>
                                        </div>
                                        <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                                    </div>
                                </div>
                            ) : records.length === 0 ? (
                                <div className="text-center py-16">
                                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
                                        <Database className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">暫無資料</h3>
                                    <p className="text-gray-500">點擊「新增」按鈕開始建立資料</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                                            <tr>
                                                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">ID</th>
                                                {getCurrentCategory()?.fields.map((field) => (
                                                    <th key={field.name} className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                                                        <div className="flex items-center space-x-2">
                                                            <field.icon className="w-4 h-4 text-gray-500" />
                                                            <span>{field.label}</span>
                                                            {field.required && <span className="text-red-500">*</span>}
                                                        </div>
                                                    </th>
                                                ))}
                                                <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-100">
                                            {records.map((record, index) => (
                                                <tr key={record.id} className="hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-200">
                                                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{record.id}</td>
                                                    {getCurrentCategory()?.fields.map((field) => (
                                                        <td key={field.name} className="px-6 py-4 text-sm text-gray-700">
                                                            {formatValue(record[field.name], field)}
                                                        </td>
                                                    ))}
                                                    <td className="px-6 py-4 text-center">
                                                        <div className="flex items-center justify-center space-x-2">
                                                            <button
                                                                onClick={() => handleEdit(record)}
                                                                className="inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors duration-200"
                                                            >
                                                                <Edit className="w-3 h-3 mr-1" />
                                                                編輯
                                                            </button>
                                                            <button
                                                                onClick={() => handleDelete(record)}
                                                                className="inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200 transition-colors duration-200"
                                                            >
                                                                <Trash2 className="w-3 h-3 mr-1" />
                                                                刪除
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </>
                )}
            </div>

            {/* 📝 新增/編輯模態框 */}
            {showModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        {/* 🏷️ 模態框標題 */}
                        <div className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white p-6 rounded-t-2xl">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                                        {getCurrentCategory() && React.createElement(getCurrentCategory()!.icon, { className: "w-5 h-5" })}
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold">
                                            {modalMode === 'add' ? '新增' : '編輯'} {getCurrentCategory()?.name}
                                        </h3>
                                        <p className="text-indigo-100 text-sm">
                                            {modalMode === 'add' ? '建立新的' : '修改現有的'}{getCurrentCategory()?.name}資料
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={() => setShowModal(false)}
                                    className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center hover:bg-white/30 transition-colors"
                                >
                                    <XCircle className="w-5 h-5" />
                                </button>
                            </div>
                        </div>

                        {/* 📝 表單內容 */}
                        <form onSubmit={handleSave} className="p-6 space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {getCurrentCategory()?.fields.map((field) => (
                                    <div key={field.name} className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
                                        <div className="flex items-center space-x-2 mb-2">
                                            <field.icon className="w-4 h-4 text-indigo-600" />
                                            <label className="block text-sm font-semibold text-gray-700">
                                                {field.label}
                                                {field.required && <span className="text-red-500 ml-1">*</span>}
                                            </label>
                                        </div>

                                        {field.type === 'textarea' ? (
                                            <textarea
                                                value={formData[field.name] || ''}
                                                onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                                className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
                                                rows={3}
                                                placeholder={`請輸入${field.label}`}
                                            />
                                        ) : field.type === 'checkbox' ? (
                                            <div className="flex items-center space-x-3">
                                                <input
                                                    type="checkbox"
                                                    checked={formData[field.name] || false}
                                                    onChange={(e) => setFormData({ ...formData, [field.name]: e.target.checked })}
                                                    className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                                />
                                                <span className="text-sm text-gray-600">啟用此選項</span>
                                            </div>
                                        ) : (
                                            <input
                                                type={field.type}
                                                value={formData[field.name] || ''}
                                                onChange={(e) => setFormData({ ...formData, [field.name]: e.target.value })}
                                                className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
                                                placeholder={`請輸入${field.label}`}
                                                required={field.required}
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>

                            {/* 🎛️ 底部按鈕 */}
                            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-100">
                                <Button
                                    type="button"
                                    onClick={() => setShowModal(false)}
                                    variant="outline"
                                    className="px-6 py-2"
                                >
                                    取消
                                </Button>
                                <Button
                                    type="submit"
                                    className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600 px-6 py-2"
                                >
                                    {modalMode === 'add' ? '新增' : '更新'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    )
} 