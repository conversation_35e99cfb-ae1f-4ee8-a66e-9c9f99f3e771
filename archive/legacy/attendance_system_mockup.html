<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #007AFF;
            --secondary-color: #5856D6;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #F2F2F7;
        }

        /* 側邊欄樣式 */
        #sidebar {
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            width: var(--sidebar-width);
            background: white;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        #sidebar.collapsed {
            transform: translateX(-100%);
        }

        #sidebar .logo {
            height: var(--header-height);
            display: flex;
            align-items: center;
            padding: 0 20px;
            background: var(--primary-color);
            color: white;
        }

        #sidebar .menu-item {
            padding: 15px 20px;
            display: flex;
            align-items: center;
            color: #1C1C1E;
            text-decoration: none;
            border-bottom: 1px solid #F2F2F7;
            transition: background-color 0.2s;
        }

        #sidebar .menu-item:hover {
            background-color: #F2F2F7;
        }

        #sidebar .menu-item.active {
            background-color: #E5E5EA;
            color: var(--primary-color);
        }

        #sidebar .menu-item i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* 主要內容區域 */
        #main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        #main-content.expanded {
            margin-left: 0;
        }

        /* 頂部導航欄 */
        #top-nav {
            height: var(--header-height);
            background: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            padding: 0 20px;
            margin-bottom: 20px;
        }

        #menu-toggle {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: var(--primary-color);
        }

        /* 卡片樣式 */
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #E5E5EA;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        /* 表格樣式 */
        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            color: #8E8E93;
            font-weight: 600;
        }

        /* 按鈕樣式 */
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
        }

        .btn-secondary {
            background: var(--secondary-color);
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
        }

        /* 表單樣式 */
        .form-control {
            border-radius: 8px;
            border: 1px solid #E5E5EA;
            padding: 8px 12px;
        }

        /* 圖表容器 */
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            #sidebar {
                transform: translateX(-100%);
            }

            #sidebar.active {
                transform: translateX(0);
            }

            #main-content {
                margin-left: 0;
            }
        }

        /* 各個功能頁面的容器 */
        .page-container {
            display: none;
        }

        .page-container.active {
            display: block;
        }

        /* 通知樣式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
            z-index: 1000;
        }

        .notification.success {
            background: #34C759;
            color: white;
        }

        .notification.error {
            background: #FF3B30;
            color: white;
        }
    </style>
</head>
<body>
    <!-- 側邊選單 -->
    <div id="sidebar">
        <div class="logo">
            <h4>考勤系統</h4>
        </div>
        <a href="#" class="menu-item active" onclick="showPage('dashboard')">
            <i class="bi bi-speedometer2"></i>
            儀表板
        </a>
        <a href="#" class="menu-item" onclick="showPage('attendance')">
            <i class="bi bi-calendar-check"></i>
            考勤記錄
        </a>
        <a href="#" class="menu-item" onclick="showPage('schedule')">
            <i class="bi bi-calendar3"></i>
            排班管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('leave')">
            <i class="bi bi-calendar-x"></i>
            請假管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('overtime')">
            <i class="bi bi-clock"></i>
            加班管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('reports')">
            <i class="bi bi-graph-up"></i>
            統計報表
        </a>
        <a href="#" class="menu-item" onclick="showPage('employees')">
            <i class="bi bi-people"></i>
            員工管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('departments')">
            <i class="bi bi-diagram-3"></i>
            部門管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('permissions')">
            <i class="bi bi-shield-lock"></i>
            權限管理
        </a>
        <a href="#" class="menu-item" onclick="showPage('settings')">
            <i class="bi bi-gear"></i>
            系統設定
        </a>
    </div>

    <!-- 主要內容區域 -->
    <div id="main-content">
        <div id="top-nav">
            <button id="menu-toggle">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <!-- 儀表板 -->
        <div id="dashboard" class="page-container active">
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5>今日出勤</h5>
                            <h2>85%</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5>請假人數</h5>
                            <h2>5</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5>遲到人數</h5>
                            <h2>2</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5>加班時數</h5>
                            <h2>12h</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    出勤趨勢
                </div>
                <div class="card-body">
                    <canvas id="attendanceChart" class="chart-container"></canvas>
                </div>
            </div>
        </div>

        <!-- 考勤記錄 -->
        <div id="attendance" class="page-container">
            <div class="card">
                <div class="card-header">
                    考勤記錄
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <input type="date" class="form-control" id="attendance-date">
                        </div>
                        <div class="col">
                            <select class="form-control" id="attendance-department">
                                <option value="">選擇部門</option>
                                <option value="1">管理部</option>
                                <option value="2">技術部</option>
                                <option value="3">業務部</option>
                            </select>
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">查詢</button>
                            <button class="btn btn-secondary">匯出</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>員工編號</th>
                                <th>姓名</th>
                                <th>部門</th>
                                <th>簽到時間</th>
                                <th>簽退時間</th>
                                <th>狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>EMP001</td>
                                <td>張三</td>
                                <td>技術部</td>
                                <td>09:00</td>
                                <td>18:00</td>
                                <td>正常</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">編輯</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 排班管理 -->
        <div id="schedule" class="page-container">
            <div class="card">
                <div class="card-header">
                    排班管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <select class="form-control" id="schedule-department">
                                <option value="">選擇部門</option>
                                <option value="1">管理部</option>
                                <option value="2">技術部</option>
                                <option value="3">業務部</option>
                            </select>
                        </div>
                        <div class="col">
                            <input type="month" class="form-control" id="schedule-month">
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">新增排班</button>
                            <button class="btn btn-secondary">匯入排班</button>
                        </div>
                    </div>
                    <div id="schedule-calendar"></div>
                </div>
            </div>
        </div>

        <!-- 請假管理 -->
        <div id="leave" class="page-container">
            <div class="card">
                <div class="card-header">
                    請假管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <select class="form-control" id="leave-status">
                                <option value="">請假狀態</option>
                                <option value="pending">待審核</option>
                                <option value="approved">已核准</option>
                                <option value="rejected">已拒絕</option>
                            </select>
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">申請請假</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>申請日期</th>
                                <th>員工姓名</th>
                                <th>請假類型</th>
                                <th>開始日期</th>
                                <th>結束日期</th>
                                <th>狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-05-12</td>
                                <td>張三</td>
                                <td>事假</td>
                                <td>2024-05-15</td>
                                <td>2024-05-15</td>
                                <td>待審核</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">審核</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 加班管理 -->
        <div id="overtime" class="page-container">
            <div class="card">
                <div class="card-header">
                    加班管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <select class="form-control" id="overtime-status">
                                <option value="">加班狀態</option>
                                <option value="pending">待審核</option>
                                <option value="approved">已核准</option>
                                <option value="rejected">已拒絕</option>
                            </select>
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">申請加班</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>申請日期</th>
                                <th>員工姓名</th>
                                <th>加班日期</th>
                                <th>加班時數</th>
                                <th>狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-05-12</td>
                                <td>張三</td>
                                <td>2024-05-12</td>
                                <td>2</td>
                                <td>待審核</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">審核</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 統計報表 -->
        <div id="reports" class="page-container">
            <div class="card">
                <div class="card-header">
                    統計報表
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <select class="form-control" id="report-type">
                                <option value="">報表類型</option>
                                <option value="attendance">出勤統計</option>
                                <option value="leave">請假統計</option>
                                <option value="overtime">加班統計</option>
                            </select>
                        </div>
                        <div class="col">
                            <input type="month" class="form-control" id="report-month">
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">產生報表</button>
                            <button class="btn btn-secondary">匯出 Excel</button>
                        </div>
                    </div>
                    <canvas id="reportChart" class="chart-container"></canvas>
                </div>
            </div>
        </div>

        <!-- 員工管理 -->
        <div id="employees" class="page-container">
            <div class="card">
                <div class="card-header">
                    員工管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <input type="text" class="form-control" placeholder="搜尋員工">
                        </div>
                        <div class="col">
                            <button class="btn btn-primary">新增員工</button>
                            <button class="btn btn-secondary">匯入員工</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>員工編號</th>
                                <th>姓名</th>
                                <th>部門</th>
                                <th>職位</th>
                                <th>電話</th>
                                <th>電子郵件</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>EMP001</td>
                                <td>張三</td>
                                <td>技術部</td>
                                <td>工程師</td>
                                <td>0912345678</td>
                                <td><EMAIL></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">編輯</button>
                                    <button class="btn btn-sm btn-danger">刪除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 部門管理 -->
        <div id="departments" class="page-container">
            <div class="card">
                <div class="card-header">
                    部門管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <button class="btn btn-primary">新增部門</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>部門編號</th>
                                <th>部門名稱</th>
                                <th>主管</th>
                                <th>人數</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>DEPT001</td>
                                <td>技術部</td>
                                <td>李四</td>
                                <td>10</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">編輯</button>
                                    <button class="btn btn-sm btn-danger">刪除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 權限管理 -->
        <div id="permissions" class="page-container">
            <div class="card">
                <div class="card-header">
                    權限管理
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col">
                            <button class="btn btn-primary">新增角色</button>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>角色名稱</th>
                                <th>權限說明</th>
                                <th>使用人數</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>系統管理員</td>
                                <td>擁有所有權限</td>
                                <td>2</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">編輯</button>
                                    <button class="btn btn-sm btn-danger">刪除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 系統設定 -->
        <div id="settings" class="page-container">
            <div class="card">
                <div class="card-header">
                    系統設定
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label class="form-label">公司名稱</label>
                            <input type="text" class="form-control" value="範例公司">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">上班時間</label>
                            <input type="time" class="form-control" value="09:00">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">下班時間</label>
                            <input type="time" class="form-control" value="18:00">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">寬限時間（分鐘）</label>
                            <input type="number" class="form-control" value="15">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">通知設定</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">遲到通知</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">請假審核通知</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">加班審核通知</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">儲存設定</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 側邊欄切換
        document.getElementById('menu-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
            document.getElementById('main-content').classList.toggle('expanded');
        });

        // 頁面切換
        function showPage(pageId) {
            // 隱藏所有頁面
            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });
            
            // 顯示選中的頁面
            document.getElementById(pageId).classList.add('active');
            
            // 更新選單項目的激活狀態
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
        }

        // 初始化圖表
        const ctx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['週一', '週二', '週三', '週四', '週五'],
                datasets: [{
                    label: '出勤率',
                    data: [95, 88, 92, 85, 90],
                    borderColor: '#007AFF',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // 初始化報表圖表
        const reportCtx = document.getElementById('reportChart').getContext('2d');
        new Chart(reportCtx, {
            type: 'bar',
            data: {
                labels: ['一月', '二月', '三月', '四月', '五月'],
                datasets: [{
                    label: '出勤率',
                    data: [95, 88, 92, 85, 90],
                    backgroundColor: '#007AFF'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>