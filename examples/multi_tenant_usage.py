from flask import Flask, g, request, jsonify
from database.multi_tenant_queries import multi_tenant_db

app = Flask(__name__)

@app.before_request
def set_company_context():
    """
    設定公司上下文
    """
    # 從Header、Subdomain或路徑獲取公司ID
    company_id = request.headers.get('X-Company-ID', 'default')
    g.company_id = company_id

# ==================== API範例 ====================

@app.route('/api/employees')
def get_employees():
    """
    獲取員工列表（自動過濾公司）
    
    返回：
    list: 當前公司的員工列表
    """
    try:
        # 自動只查詢當前公司的員工
        employees = multi_tenant_db.query_employees()
        
        # 根據公司配置決定返回欄位
        company_code = g.company_id
        
        if company_code == 'abc_corp':
            # ABC公司：添加專案代碼和成本中心
            for emp in employees:
                custom = emp.get('custom_fields', {})
                emp['project_code'] = custom.get('project_code', 'N/A')
                emp['cost_center'] = custom.get('cost_center', 'N/A')
        
        elif company_code == 'xyz_ltd':
            # XYZ公司：添加安全等級
            for emp in employees:
                custom = emp.get('custom_fields', {})
                emp['security_clearance'] = custom.get('security_clearance', 'PUBLIC')
                emp['department_level'] = custom.get('department_level', 1)
        
        return jsonify(employees)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/attendance')
def get_attendance():
    """
    獲取考勤記錄（自動過濾公司）
    
    返回：
    list: 當前公司的考勤記錄
    """
    try:
        # 獲取查詢參數
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_id = request.args.get('employee_id')
        
        # 設定日期範圍
        date_range = (start_date, end_date) if start_date and end_date else None
        
        # 自動只查詢當前公司的考勤記錄
        records = multi_tenant_db.query_attendance(
            date_range=date_range,
            employee_id=employee_id
        )
        
        # 根據公司配置添加自定義欄位
        company_code = g.company_id
        
        if company_code == 'abc_corp':
            # ABC公司：添加專案工時資訊
            for record in records:
                custom = record.get('custom_fields', {})
                record['project_code'] = custom.get('project_code', 'N/A')
                record['billable_hours'] = custom.get('billable_hours', 8.0)
        
        elif company_code == 'xyz_ltd':
            # XYZ公司：添加安全區域資訊
            for record in records:
                custom = record.get('custom_fields', {})
                record['security_zone'] = custom.get('security_zone', 'PUBLIC')
                record['access_level'] = custom.get('access_level', 'NORMAL')
        
        return jsonify(records)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/employees', methods=['POST'])
def create_employee():
    """
    新增員工（自動添加到當前公司）
    
    返回：
    dict: 新員工資訊
    """
    try:
        data = request.get_json()
        
        # 根據公司配置處理自定義欄位
        company_code = g.company_id
        custom_fields = {}
        
        if company_code == 'abc_corp':
            # ABC公司：處理專案代碼和成本中心
            custom_fields = {
                'project_code': data.get('project_code', ''),
                'cost_center': data.get('cost_center', '')
            }
        
        elif company_code == 'xyz_ltd':
            # XYZ公司：處理安全等級
            custom_fields = {
                'security_clearance': data.get('security_clearance', 'PUBLIC'),
                'department_level': data.get('department_level', 1)
            }
        
        # 準備員工資料
        employee_data = {
            'employee_id': data['employee_id'],
            'name': data['name'],
            'email': data.get('email'),
            'department_id': data.get('department_id'),
            'position': data.get('position'),
            'custom_fields': custom_fields
        }
        
        # 新增員工（自動添加company_id）
        new_id = multi_tenant_db.insert_employee(employee_data)
        
        return jsonify({
            'id': new_id,
            'message': '員工新增成功',
            'company': company_code
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_company_stats():
    """
    獲取公司統計資料
    
    返回：
    dict: 統計資料
    """
    try:
        stats = multi_tenant_db.get_company_stats()
        stats['company_code'] = g.company_id
        return jsonify(stats)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ==================== 測試範例 ====================

def test_multi_tenant():
    """
    測試多租戶功能
    """
    with app.test_client() as client:
        
        print("=== 測試預設公司 ===")
        response = client.get('/api/employees', headers={'X-Company-ID': 'default'})
        print(f"預設公司員工: {response.get_json()}")
        
        print("\n=== 測試ABC公司 ===")
        response = client.get('/api/employees', headers={'X-Company-ID': 'abc_corp'})
        print(f"ABC公司員工: {response.get_json()}")
        
        print("\n=== 測試XYZ公司 ===")
        response = client.get('/api/employees', headers={'X-Company-ID': 'xyz_ltd'})
        print(f"XYZ公司員工: {response.get_json()}")
        
        print("\n=== 測試統計資料 ===")
        response = client.get('/api/stats', headers={'X-Company-ID': 'abc_corp'})
        print(f"ABC公司統計: {response.get_json()}")

if __name__ == '__main__':
    # 執行測試
    test_multi_tenant()
    
    # 啟動應用
    app.run(debug=True, port=5000) 