version: '3.8'

services:
  # 共享PostgreSQL資料庫
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: attendance_multi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: attendance_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-schemas.sql:/docker-entrypoint-initdb.d/init-schemas.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 共享Redis快取
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # 考勤系統應用 (支援多租戶)
  attendance_app:
    build: .
    environment:
      # 資料庫配置
      DATABASE_URL: *******************************************************/attendance_multi
      REDIS_URL: redis://redis:6379/0
      
      # 應用配置
      FLASK_ENV: production
      SECRET_KEY: your-secret-key-here
      MULTI_TENANT: "true"
      
      # 日誌配置
      LOG_LEVEL: INFO
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 3  # 3個實例做負載平衡
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.2'

  # Nginx負載平衡器和反向代理
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx-multi-tenant.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - attendance_app
    restart: unless-stopped

  # 監控服務 (可選)
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    ports:
      - "9090:9090"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    restart: unless-stopped

volumes:
  postgres_data:
  grafana_data:
  app_logs:
  app_uploads:

networks:
  default:
    driver: bridge 