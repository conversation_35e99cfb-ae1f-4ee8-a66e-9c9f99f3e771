<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班審核管理 - AttendanceOS Elite</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 設計系統CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- 頂部導航 -->
        <header class="bg-white shadow-sm border-b">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="/elite" class="text-blue-600 hover:text-blue-800">
                            <i data-lucide="arrow-left" class="w-5 h-5"></i>
                        </a>
                        <h1 class="text-2xl font-bold text-gray-900">加班審核管理</h1>
                    </div>
                    <button onclick="showAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新增申請</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主內容 -->
        <main class="p-6">
            <!-- 統計卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i data-lucide="clock" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">待審核</p>
                            <p class="text-2xl font-bold" id="pendingCount">-</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i data-lucide="check" class="w-6 h-6 text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">已核准</p>
                            <p class="text-2xl font-bold" id="approvedCount">-</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <i data-lucide="x" class="w-6 h-6 text-red-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">已拒絕</p>
                            <p class="text-2xl font-bold" id="rejectedCount">-</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i data-lucide="clock-4" class="w-6 h-6 text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">本月總時數</p>
                            <p class="text-2xl font-bold" id="totalHours">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申請列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">加班申請列表</h3>
                        <button onclick="loadOvertimeRequests()" class="text-gray-600 hover:text-gray-800">
                            <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">員工</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">時間</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">時數</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">類型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">原因</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">狀態</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                            </tr>
                        </thead>
                        <tbody id="overtimeTableBody" class="divide-y divide-gray-200">
                            <tr>
                                <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                    <i data-lucide="loader" class="w-6 h-6 mx-auto mb-2 animate-spin"></i>
                                    <p>載入中...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 滑動通知框 -->
    <div id="slideNotification" class="fixed top-4 right-4 transform translate-x-full transition-transform duration-500 ease-in-out z-50">
        <div id="notificationCard" class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-xl p-4 min-w-80 max-w-96 border border-green-400">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i id="notificationIcon" data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-3">
                    <p id="notificationTitle" class="text-sm font-semibold text-white">操作成功</p>
                    <p id="notificationMessage" class="text-sm text-green-100 mt-1">加班申請已核准</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增申請模態框 -->
    <div id="addModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">新增加班申請</h3>
                <form id="addForm">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">員工</label>
                            <select id="employeeSelect" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">選擇員工</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">加班日期</label>
                            <input type="date" id="overtimeDate" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">開始時間</label>
                                <input type="time" id="startTime" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">結束時間</label>
                                <input type="time" id="endTime" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">加班類型</label>
                            <select id="overtimeType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">選擇類型</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">加班原因</label>
                            <textarea id="reason" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="請說明加班原因..."></textarea>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">提交申請</button>
                        <button type="button" onclick="hideAddModal()" class="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', async() => {
            try {
                // 初始化工具函數庫
                const utilsLoaded = await UtilsLoader.initPageUtils('elite-overtime');
                if (!utilsLoaded) {
                    console.warn('工具函數庫載入失敗，使用降級模式');
                }

                lucide.createIcons();
                loadOvertimeRequests();
                loadStatistics();
            } catch (error) {
                console.error('頁面初始化錯誤:', error);
                // 降級處理：即使工具函數庫失敗也要正常運行
                lucide.createIcons();
                loadOvertimeRequests();
                loadStatistics();
            }
        });

        // 顯示通知的統一函數
        function showNotification(type, title, message) {
            // 優先使用工具函數庫的通知系統
            if (window.NotificationSystem && typeof window.NotificationSystem.show === 'function') {
                window.NotificationSystem.show(type, title, message);
            } else {
                // 降級到本地通知系統
                showSlideNotification(type, title, message);
            }
        }

        // 載入統計資料
        async function loadStatistics() {
            try {
                const response = await fetch('/api/overtime/statistics');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('pendingCount').textContent = data.pending_count || 0;
                    document.getElementById('approvedCount').textContent = data.approved_count || 0;
                    document.getElementById('rejectedCount').textContent = data.rejected_count || 0;
                    document.getElementById('totalHours').textContent = (data.total_hours || 0) + 'h';
                }
            } catch (error) {
                console.error('載入統計失敗:', error);
            }
        }

        // 載入加班申請列表
        async function loadOvertimeRequests() {
            try {
                const response = await fetch('/api/overtime/requests?limit=50');
                const data = await response.json();

                const tbody = document.getElementById('overtimeTableBody');
                if (data.success && data.records.length > 0) {
                    tbody.innerHTML = data.records.map(record => `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">${record.employee_name || '未知員工'}</div>
                                <div class="text-sm text-gray-500">${record.employee_code || ''}</div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">${record.overtime_date}</td>
                            <td class="px-6 py-4 text-sm text-gray-900">${record.start_time} - ${record.end_time}</td>
                            <td class="px-6 py-4 text-sm text-gray-900">${record.overtime_hours}h</td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    ${record.overtime_type_name || record.overtime_type}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="max-w-xs">
                                    <p class="text-sm text-gray-900 truncate" title="${record.reason || '無'}">${record.reason || '無'}</p>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(record.status)}">
                                    ${getStatusText(record.status)}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                ${record.status === 'pending' ? `
                                    <div class="flex space-x-2">
                                        <button onclick="approveRequest(${record.id})" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1.5 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md flex items-center space-x-1 text-xs font-medium">
                                            <i data-lucide="check" class="w-3 h-3"></i>
                                            <span>核准</span>
                                        </button>
                                        <button onclick="rejectRequest(${record.id})" class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1.5 rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-sm hover:shadow-md flex items-center space-x-1 text-xs font-medium">
                                            <i data-lucide="x" class="w-3 h-3"></i>
                                            <span>拒絕</span>
                                        </button>
                                    </div>
                                ` : `
                                    <span class="text-gray-500">已處理</span>
                                `}
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-2 text-gray-300"></i>
                                <p>暫無加班申請資料</p>
                            </td>
                        </tr>
                    `;
                }
                lucide.createIcons();
            } catch (error) {
                console.error('載入失敗:', error);
                document.getElementById('overtimeTableBody').innerHTML = `
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center text-red-500">
                            <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-2"></i>
                            <p>載入失敗，請重試</p>
                        </td>
                    </tr>
                `;
                lucide.createIcons();
            }
        }

        // 載入表單資料
        async function loadFormData() {
            try {
                const [employeesRes, typesRes] = await Promise.all([
                    fetch('/api/employees'),
                    fetch('/api/overtime/types')
                ]);
                
                const employees = await employeesRes.json();
                const types = await typesRes.json();
                
                const employeeSelect = document.getElementById('employeeSelect');
                if (employees.success) {
                    employeeSelect.innerHTML = '<option value="">選擇員工</option>' + 
                        employees.records.map(emp => `<option value="${emp.id}">${emp.name} (${emp.employee_id})</option>`).join('');
                }
                
                const typeSelect = document.getElementById('overtimeType');
                if (types.success) {
                    typeSelect.innerHTML = '<option value="">選擇類型</option>' + 
                        types.records.map(type => `<option value="${type.code}">${type.name} (${type.rate}倍)</option>`).join('');
                }
            } catch (error) {
                console.error('載入表單資料失敗:', error);
            }
        }

        // 狀態樣式和文字
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'approved': return 'bg-green-100 text-green-800';
                case 'rejected': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待審核';
                case 'approved': return '已核准';
                case 'rejected': return '已拒絕';
                default: return '未知';
            }
        }

        // 模態框控制
        function showAddModal() {
            document.getElementById('addModal').classList.remove('hidden');
            loadFormData();
        }

        function hideAddModal() {
            document.getElementById('addModal').classList.add('hidden');
            document.getElementById('addForm').reset();
        }

        // 顯示滑動通知（降級版本）
        function showSlideNotification(type, title, message) {
            const notification = document.getElementById('slideNotification');
            const icon = document.getElementById('notificationIcon');
            const titleEl = document.getElementById('notificationTitle');
            const messageEl = document.getElementById('notificationMessage');
            const container = notification.querySelector('div');
            
            // 設置內容
            titleEl.textContent = title;
            messageEl.textContent = message;
            
            // 設置樣式
            if (type === 'success') {
                container.className = 'bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-xl p-4 min-w-80 max-w-96 border border-green-400';
                icon.className = 'w-6 h-6 text-white';
                titleEl.className = 'text-sm font-semibold text-white';
                messageEl.className = 'text-sm text-green-100 mt-1';
                icon.setAttribute('data-lucide', 'check-circle');
            } else if (type === 'error') {
                container.className = 'bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-xl p-4 min-w-80 max-w-96 border border-red-400';
                icon.className = 'w-6 h-6 text-white';
                titleEl.className = 'text-sm font-semibold text-white';
                messageEl.className = 'text-sm text-red-100 mt-1';
                icon.setAttribute('data-lucide', 'x-circle');
            } else if (type === 'warning') {
                container.className = 'bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg shadow-xl p-4 min-w-80 max-w-96 border border-yellow-400';
                icon.className = 'w-6 h-6 text-white';
                titleEl.className = 'text-sm font-semibold text-white';
                messageEl.className = 'text-sm text-yellow-100 mt-1';
                icon.setAttribute('data-lucide', 'alert-triangle');
            }
            
            // 重新創建圖標
            lucide.createIcons();
            
            // 滑出動畫
            notification.classList.remove('translate-x-full');
            notification.classList.add('translate-x-0');
            
            // 2秒後滑回
            setTimeout(() => {
                notification.classList.remove('translate-x-0');
                notification.classList.add('translate-x-full');
            }, 2000);
        }

        // 通知函數統一接口
        function showNotification(type, title, message) {
            // 優先使用工具函數庫的通知系統
            if (window.NotificationSystem && window.NotificationSystem.show) {
                window.NotificationSystem.show(type, title, message);
            } else {
                // 降級使用本地通知系統
                showSlideNotification(type, title, message);
            }
        }

        // 審核操作
        async function approveRequest(id) {
            try {
                const response = await fetch(`/api/overtime/requests/${id}/approve`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        approver_id: 1, 
                        action: 'approve',
                        comments: '管理員核准' 
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    showNotification('success', '核准成功', '加班申請已成功核准');
                    // 使用頁面重新載入確保數據更新
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('error', '核准失敗', result.error || '操作失敗，請重試');
                }
            } catch (error) {
                console.error('核准失敗:', error);
                showNotification('error', '核准失敗', '網路錯誤，請重試');
            }
        }

        async function rejectRequest(id) {
            const reason = prompt('請輸入拒絕原因：');
            if (!reason) return;
            
            try {
                const response = await fetch(`/api/overtime/requests/${id}/approve`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        approver_id: 1, 
                        action: 'reject',
                        rejection_reason: reason 
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    showNotification('success', '拒絕成功', '加班申請已拒絕');
                    // 使用頁面重新載入確保數據更新
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('error', '拒絕失敗', result.error || '操作失敗，請重試');
                }
            } catch (error) {
                console.error('拒絕失敗:', error);
                showNotification('error', '拒絕失敗', '網路錯誤，請重試');
            }
        }

        // 提交新申請
        document.getElementById('addForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                employee_id: parseInt(document.getElementById('employeeSelect').value),
                overtime_date: document.getElementById('overtimeDate').value,
                start_time: document.getElementById('startTime').value,
                end_time: document.getElementById('endTime').value,
                overtime_type: document.getElementById('overtimeType').value,
                reason: document.getElementById('reason').value
            };
            
            // 基本驗證
            if (!formData.employee_id || !formData.overtime_date || !formData.start_time || !formData.end_time || !formData.overtime_type) {
                showNotification('warning', '表單不完整', '請填寫所有必填欄位');
                return;
            }
            
            try {
                const response = await fetch('/api/overtime/requests', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                if (response.ok) {
                    showNotification('success', '提交成功', '加班申請已成功提交');
                    hideAddModal();
                    // 使用頁面重新載入確保數據更新
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('error', '提交失敗', result.error || '操作失敗，請重試');
                }
            } catch (error) {
                console.error('提交失敗:', error);
                showNotification('error', '提交失敗', '網路錯誤，請重試');
            }
        });
    </script>
</body>

</html>