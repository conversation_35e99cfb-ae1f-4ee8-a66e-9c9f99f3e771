-- 更新班表的當天起算時間為上班前兩小時
-- 這樣可以更好地處理員工提早上班或前一天加班的情況

-- 更新所有班表的day_start_time為上班前兩小時
UPDATE shifts 
SET day_start_time = 
    CASE 
        -- 處理上班時間，計算前兩小時
        WHEN CAST(SUBSTR(start_time, 1, 2) AS INTEGER) >= 2 THEN
            printf('%02d:%s', 
                CAST(SUBSTR(start_time, 1, 2) AS INTEGER) - 2, 
                SUBSTR(start_time, 4)
            )
        ELSE
            -- 如果上班時間小於02:00，則設定為前一天的時間
            printf('%02d:%s', 
                CAST(SUBSTR(start_time, 1, 2) AS INTEGER) + 22, 
                SUBSTR(start_time, 4)
            )
    END;

-- 檢查更新結果
SELECT id, name, start_time, day_start_time, 
       '上班前' || 
       CASE 
           WHEN CAST(SUBSTR(start_time, 1, 2) AS INTEGER) >= CAST(SUBSTR(day_start_time, 1, 2) AS INTEGER) THEN
               CAST(CAST(SUBSTR(start_time, 1, 2) AS INTEGER) - CAST(SUBSTR(day_start_time, 1, 2) AS INTEGER) AS TEXT)
           ELSE
               CAST(24 + CAST(SUBSTR(start_time, 1, 2) AS INTEGER) - CAST(SUBSTR(day_start_time, 1, 2) AS INTEGER) AS TEXT)
       END || '小時' as time_difference
FROM shifts 
ORDER BY id; 