"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import {
    Clock,
    MapPin,
    LogOut,
    CheckCircle,
    XCircle,
    Calendar,
    BarChart3,
    Plus,
    Settings,
    History,
    Eye,
    Smartphone,
    User,
    Building,
    Shield,
    ChevronRight,
    Activity,
    Timer,
    Coffee,
    Briefcase,
    Camera,
    Upload,
    FileText,
    MessageSquare,
    Trash2
} from 'lucide-react'

interface AttendanceRecord {
    id: number
    date: string
    check_in: string | null
    check_out: string | null
    status: string
    location?: string
}

interface OverTimeRequest {
    id: number
    overtime_date: string
    start_time: string
    end_time: string
    reason: string
    status: string
}

interface LeaveRequest {
    id: number
    start_date: string
    end_date: string
    type: string
    reason: string
    status: string
}

export default function MobilePage() {
    const { user, logout, loading: authLoading } = useAuth()
    const router = useRouter()
    const [loading, setLoading] = useState(true)
    const [currentTime, setCurrentTime] = useState(new Date())
    const [location, setLocation] = useState<string>('獲取位置中...')
    const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null)
    const [recentRecords, setRecentRecords] = useState<AttendanceRecord[]>([])
    const [overtimeRequests, setOvertimeRequests] = useState<OverTimeRequest[]>([])
    const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
    const [showWorkReportModal, setShowWorkReportModal] = useState(false)
    const [workReportForm, setWorkReportForm] = useState({
        date: '',
        time: '',
        category: '',
        content: '',
        photos: [] as File[]
    })
    const [userAvatar, setUserAvatar] = useState<string | null>(null)

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date())
        }, 1000)
        return () => clearInterval(timer)
    }, [])

    // 🖼️ 載入用戶頭像（從後台API）
    useEffect(() => {
        const loadUserAvatar = async () => {
            if (!user?.employee_id) return

            try {
                // 從後台API獲取用戶資料
                const apiUrl = `${getApiBaseUrl()}/api/profile/${user.employee_id}`
                console.log('🔍 開始載入頭像，API URL:', apiUrl)

                const response = await fetch(apiUrl)
                console.log('📡 API回應狀態:', response.status, response.statusText)

                if (response.ok) {
                    const result = await response.json()
                    console.log('📦 API回應內容:', result)

                    if (result.success && result.profile.avatar && result.profile.avatar.trim()) {
                        const avatarUrl = `${getApiBaseUrl()}${result.profile.avatar}`
                        console.log('🖼️ 構建的頭像URL:', avatarUrl)

                        setUserAvatar(avatarUrl)
                        // 同時更新localStorage作為快取
                        localStorage.setItem('userAvatar', avatarUrl)
                        console.log('✅ 頭像設定成功，狀態已更新')

                        // 測試圖片是否可以載入
                        const testImg = new Image()
                        testImg.onload = () => {
                            console.log('✅ 頭像圖片載入測試成功')
                        }
                        testImg.onerror = (error) => {
                            console.error('❌ 頭像圖片載入測試失敗:', error)
                        }
                        testImg.src = avatarUrl
                    } else {
                        console.log('📷 用戶尚未設定頭像，API回應:', result)
                        // 清除localStorage中的舊頭像
                        localStorage.removeItem('userAvatar')
                        setUserAvatar(null)
                    }
                } else {
                    console.warn('⚠️ 無法載入用戶資料，狀態碼:', response.status)
                    // 嘗試從localStorage載入快取
                    const cachedAvatar = localStorage.getItem('userAvatar')
                    if (cachedAvatar) {
                        setUserAvatar(cachedAvatar)
                        console.log('📦 使用快取頭像:', cachedAvatar)
                    }
                }
            } catch (error) {
                console.error('❌ 載入頭像失敗:', error)
                // 嘗試從localStorage載入快取
                const cachedAvatar = localStorage.getItem('userAvatar')
                if (cachedAvatar) {
                    setUserAvatar(cachedAvatar)
                    console.log('📦 載入失敗，使用快取頭像:', cachedAvatar)
                }
            }
        }

        loadUserAvatar()

        // 監聽storage事件，當其他頁面更新頭像時同步更新
        const handleStorageChange = (e: StorageEvent) => {
            if (e.key === 'userAvatar') {
                setUserAvatar(e.newValue)
                console.log('🔄 頭像已同步更新')
            }
        }

        window.addEventListener('storage', handleStorageChange)
        return () => window.removeEventListener('storage', handleStorageChange)
    }, [user?.employee_id])

    useEffect(() => {
        // 🔧 改進的 GPS 定位功能
        const initializeLocation = async () => {
            // 檢查是否為 HTTPS 或 localhost
            const isSecureContext = window.location.protocol === 'https:' ||
                window.location.hostname === 'localhost' ||
                window.location.hostname === '127.0.0.1'

            if (!isSecureContext) {
                setLocation('台北辦公室 (需要HTTPS定位)')
                return
            }

            if (!navigator.geolocation) {
                setLocation('台北辦公室 (瀏覽器不支援定位)')
                return
            }

            try {
                // 先檢查權限狀態
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' })
                    if (permission.state === 'denied') {
                        setLocation('台北辦公室 (定位權限被拒絕)')
                        return
                    }
                }

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude.toFixed(4)
                        const lng = position.coords.longitude.toFixed(4)
                        setLocation(`${lat}, ${lng}`)
                        console.log('✅ GPS定位成功:', { lat, lng })
                    },
                    (error) => {
                        console.warn('GPS定位失敗，使用預設位置:', error.message)
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                setLocation('台北辦公室 (用戶拒絕定位權限)')
                                break
                            case error.POSITION_UNAVAILABLE:
                                setLocation('台北辦公室 (位置資訊無法取得)')
                                break
                            case error.TIMEOUT:
                                setLocation('台北辦公室 (定位請求超時)')
                                break
                            default:
                                setLocation('台北辦公室 (定位服務不可用)')
                                break
                        }
                    },
                    {
                        enableHighAccuracy: false, // 降低精度要求，提高成功率
                        timeout: 5000,            // 縮短超時時間
                        maximumAge: 300000        // 5分鐘內的快取位置可接受
                    }
                )
            } catch (error) {
                console.warn('GPS初始化失敗:', error)
                setLocation('台北辦公室 (定位初始化失敗)')
            }
        }

        initializeLocation()
    }, [])

    // 🌐 獲取API基礎URL
    const getApiBaseUrl = () => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname
            console.log('🌐 員工主頁檢測到的主機:', hostname)

            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:7072'
            } else {
                const apiUrl = `http://${hostname}:7072`
                console.log('📱 員工主頁使用API URL:', apiUrl)
                return apiUrl
            }
        }
        return 'http://localhost:7072'
    }

    useEffect(() => {
        const loadData = async () => {
            console.log('🔍 手機版主頁 loadData 檢查:', { authLoading, user: !!user, userDetails: user })

            if (authLoading) {
                console.log('⏳ AuthContext 還在載入中，等待...')
                return
            }

            if (!user) {
                console.log('❌ 沒有用戶資料，重定向到登入頁面')
                router.push('/m/login')
                return
            }

            console.log('✅ 用戶已認證，開始載入數據:', user)

            // 🔝 滾動到頁面頂部
            window.scrollTo({ top: 0, behavior: 'smooth' })

            const apiBaseUrl = getApiBaseUrl()

            try {
                // 載入今日考勤記錄
                try {
                    const today = new Date().toISOString().split('T')[0]
                    const url = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`
                    console.log('📅 載入今日考勤 URL:', url)

                    const todayResponse = await fetch(url)
                    if (todayResponse.ok) {
                        const todayData = await todayResponse.json()
                        console.log('載入今日考勤記錄:', todayData)
                        setTodayAttendance(todayData.data || null)
                    } else {
                        console.error('載入今日考勤失敗，API返回錯誤')
                        setTodayAttendance(null)
                    }
                } catch (error) {
                    console.error('載入今日考勤失敗:', error)
                    setTodayAttendance(null)
                }

                // 載入最近考勤記錄
                try {
                    const endDate = new Date().toISOString().split('T')[0]
                    const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                    const url = `${apiBaseUrl}/api/attendance/records?employee_id=${user.employee_id}&start_date=${startDate}&end_date=${endDate}&limit=5`
                    console.log('📋 載入最近考勤記錄 URL:', url)

                    const recordsResponse = await fetch(url)
                    if (recordsResponse.ok) {
                        const recordsData = await recordsResponse.json()
                        console.log('載入最近考勤記錄:', recordsData)
                        setRecentRecords(recordsData.records || [])
                    } else {
                        console.error('載入最近考勤記錄失敗，API返回錯誤')
                        setRecentRecords([])
                    }
                } catch (error) {
                    console.error('載入最近考勤記錄失敗:', error)
                    setRecentRecords([])
                }

                // 載入真實的加班申請記錄
                try {
                    const url = `${apiBaseUrl}/api/overtime/requests?employee_id=${user.id}`
                    console.log('⏰ 載入加班申請記錄 URL:', url)

                    const overtimeResponse = await fetch(url)
                    if (overtimeResponse.ok) {
                        const overtimeData = await overtimeResponse.json()
                        console.log('載入加班申請記錄:', overtimeData)
                        setOvertimeRequests(overtimeData.records || [])
                    } else {
                        console.error('載入加班申請記錄失敗，API返回錯誤')
                        setOvertimeRequests([])
                    }
                } catch (error) {
                    console.error('載入加班申請失敗:', error)
                    setOvertimeRequests([])
                }

                // 載入請假記錄
                try {
                    const url = `${apiBaseUrl}/api/leave-requests?employee_id=${user.id}&limit=5`
                    console.log('🏖️ 載入請假記錄 URL:', url)

                    const leaveResponse = await fetch(url)
                    if (leaveResponse.ok) {
                        const leaveData = await leaveResponse.json()
                        console.log('載入請假記錄:', leaveData)
                        setLeaveRequests(leaveData.records || [])
                    } else {
                        console.error('載入請假記錄失敗，API返回錯誤')
                        setLeaveRequests([])
                    }
                } catch (error) {
                    console.error('載入請假記錄失敗:', error)
                    setLeaveRequests([])
                }

            } catch (error) {
                console.error('載入數據失敗:', error)
            } finally {
                setLoading(false)
            }
        }

        loadData()
    }, [user, authLoading, router])

    const handleClockIn = async () => {
        if (!user) return

        try {
            console.log('執行上班打卡')
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/attendance/clock-in`
            console.log('⏰ 上班打卡 URL:', url)

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_id: user.employee_id,
                    location: location
                })
            })

            const result = await response.json()
            console.log('上班打卡結果:', result)

            if (result.success) {
                alert('上班打卡成功！')
                // 重新載入今日考勤記錄
                const todayUrl = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`
                const todayResponse = await fetch(todayUrl)
                if (todayResponse.ok) {
                    const todayData = await todayResponse.json()
                    setTodayAttendance(todayData.data || null)
                }
            } else {
                alert('打卡失敗：' + (result.error || result.message || '未知錯誤'))
            }
        } catch (error) {
            console.error('打卡失敗:', error)
            alert('打卡失敗，請檢查網路連線')
        }
    }

    const handleClockOut = async () => {
        if (!user) return

        try {
            console.log('執行下班打卡')
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/attendance/clock-out`
            console.log('⏰ 下班打卡 URL:', url)

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_id: user.employee_id,
                    location: location
                })
            })

            const result = await response.json()
            console.log('下班打卡結果:', result)

            if (result.success) {
                alert('下班打卡成功！')
                // 重新載入今日考勤記錄
                const todayUrl = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`
                const todayResponse = await fetch(todayUrl)
                if (todayResponse.ok) {
                    const todayData = await todayResponse.json()
                    setTodayAttendance(todayData.data || null)
                }
            } else {
                alert('打卡失敗：' + (result.error || result.message || '未知錯誤'))
            }
        } catch (error) {
            console.error('打卡失敗:', error)
            alert('打卡失敗，請檢查網路連線')
        }
    }

    // 🗑️ 取消請假申請函數
    const handleCancelLeaveRequest = async (requestId: number) => {
        try {
            if (!user) {
                alert('用戶資訊不存在，請重新登入')
                return
            }

            const apiBaseUrl = getApiBaseUrl()

            // 顯示確認對話框
            const confirmed = window.confirm('確定要取消這個請假申請嗎？')
            if (!confirmed) return

            const response = await fetch(`${apiBaseUrl}/api/leave-requests/${requestId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            const result = await response.json()
            console.log('取消請假申請結果:', result)

            if (result.success) {
                alert('請假申請已成功取消！')
                // 重新載入請假記錄
                const leaveUrl = `${apiBaseUrl}/api/leave-requests/employee/${user.employee_id}`
                const leaveResponse = await fetch(leaveUrl)
                if (leaveResponse.ok) {
                    const leaveData = await leaveResponse.json()
                    setLeaveRequests(leaveData.data || [])
                }
            } else {
                alert('取消失敗：' + (result.error || result.message || '未知錯誤'))
            }
        } catch (error) {
            console.error('取消請假申請失敗:', error)
            alert('取消失敗，請檢查網路連線')
        }
    }

    // 🗑️ 取消加班申請函數
    const handleCancelOvertimeRequest = async (requestId: number) => {
        try {
            if (!user) {
                alert('用戶資訊不存在，請重新登入')
                return
            }

            const apiBaseUrl = getApiBaseUrl()

            // 顯示確認對話框
            const confirmed = window.confirm('確定要取消這個加班申請嗎？')
            if (!confirmed) return

            const response = await fetch(`${apiBaseUrl}/api/overtime-requests/${requestId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            const result = await response.json()
            console.log('取消加班申請結果:', result)

            if (result.success) {
                alert('加班申請已成功取消！')
                // 重新載入加班記錄
                const overtimeUrl = `${apiBaseUrl}/api/overtime-requests/employee/${user.employee_id}`
                const overtimeResponse = await fetch(overtimeUrl)
                if (overtimeResponse.ok) {
                    const overtimeData = await overtimeResponse.json()
                    setOvertimeRequests(overtimeData.data || [])
                }
            } else {
                alert('取消失敗：' + (result.error || result.message || '未知錯誤'))
            }
        } catch (error) {
            console.error('取消加班申請失敗:', error)
            alert('取消失敗，請檢查網路連線')
        }
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'normal': return 'bg-emerald-50 text-emerald-700 border-emerald-200'
            case 'late': return 'bg-amber-50 text-amber-700 border-amber-200'
            case 'early_leave': return 'bg-orange-50 text-orange-700 border-orange-200'
            case 'absent': return 'bg-red-50 text-red-700 border-red-200'
            case 'working': return 'bg-blue-50 text-blue-700 border-blue-200'
            default: return 'bg-gray-50 text-gray-700 border-gray-200'
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case 'normal': return '正常'
            case 'late': return '遲到'
            case 'early_leave': return '早退'
            case 'absent': return '缺勤'
            case 'working': return '工作中'
            default: return '未知'
        }
    }

    if (authLoading || loading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                <div className="text-center">
                    <div className="relative">
                        <div className="w-16 h-16 border-4 border-blue-100 border-t-blue-600 rounded-full animate-spin mx-auto mb-6"></div>
                        <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-300 rounded-full animate-pulse mx-auto"></div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">載入中</h3>
                    <p className="text-gray-600">正在初始化系統...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
            {/* 🚪 登出按鈕 */}
            <button
                onClick={logout}
                className="fixed top-4 right-4 z-50 w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 border-white/20"
                title="登出"
            >
                <LogOut className="w-5 h-5 text-white" />
            </button>

            {/* 📱 主要內容區域 - 大幅縮小間距 */}
            <div className="px-2 py-2 space-y-2">
                {/* 🕐 時間和打卡區域 - 縮小間距 */}
                <div className="bg-gradient-to-br from-indigo-600 via-blue-600 to-purple-700 rounded-2xl p-3 text-white shadow-xl">
                    {/* 時間顯示 - 縮小字體 */}
                    <div className="text-center mb-3">
                        <div className="text-2xl font-bold mb-1">
                            {currentTime.toLocaleTimeString('zh-TW', {
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            })}
                        </div>
                        <div className="text-sm text-white/80">
                            {currentTime.toLocaleDateString('zh-TW', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                weekday: 'long'
                            })}
                        </div>
                    </div>

                    {/* 用戶資訊 - 縮小間距 */}
                    <div className="flex items-center justify-center space-x-3 mb-3">
                        <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center overflow-hidden">
                            {userAvatar ? (
                                <img
                                    src={userAvatar}
                                    alt="用戶頭像"
                                    className="w-full h-full object-cover"
                                    onLoad={() => {
                                        console.log('✅ 頭像圖片成功顯示:', userAvatar)
                                    }}
                                    onError={(e) => {
                                        console.error('❌ 頭像圖片載入失敗:', userAvatar)
                                        console.error('錯誤詳情:', e)
                                        // 載入失敗時顯示預設圖標
                                        setUserAvatar(null)
                                    }}
                                />
                            ) : (
                                <User className="w-10 h-10 text-white" />
                            )}
                        </div>
                        <div className="text-left">
                            <p className="text-white font-bold text-xl">{user.name}</p>
                            <p className="text-white/80 text-base">{user.employee_id}</p>
                            {/* 🔧 調試信息 - 顯示頭像狀態 */}
                            <p className="text-white/60 text-sm">
                                頭像: {userAvatar ? '已載入' : '未設定'}
                            </p>
                        </div>
                    </div>



                    {/* 圓形打卡按鈕 - 縮小尺寸和間距 */}
                    <div className="flex justify-center items-center space-x-6 px-4">
                        <div className="text-center">
                            <Button
                                onClick={handleClockIn}
                                disabled={!!todayAttendance?.check_in}
                                className={`relative w-20 h-20 rounded-full transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 ${todayAttendance?.check_in
                                    ? 'bg-gradient-to-br from-gray-500 to-gray-700 border-gray-400 cursor-not-allowed opacity-50'
                                    : 'bg-gradient-to-br from-green-500 via-emerald-600 to-teal-700 hover:from-green-600 hover:via-emerald-700 hover:to-teal-800 border-green-400 animate-pulse'
                                    }`}
                                style={!todayAttendance?.check_in ? {
                                    boxShadow: '0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.1)'
                                } : {}}
                            >
                                <div className="relative z-10 text-center">
                                    <span className="text-lg font-bold text-white block mb-1">上班</span>
                                    <div className="text-base text-white/90 font-medium">
                                        {todayAttendance?.check_in ? '已打卡' : '尚未打卡'}
                                    </div>
                                </div>
                            </Button>
                        </div>

                        <div className="text-center">
                            <Button
                                onClick={handleClockOut}
                                disabled={!todayAttendance?.check_in || !!todayAttendance?.check_out}
                                className={`relative w-20 h-20 rounded-full transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 ${!todayAttendance?.check_in || todayAttendance?.check_out
                                    ? 'bg-gradient-to-br from-gray-500 to-gray-700 border-gray-400 cursor-not-allowed opacity-50'
                                    : 'bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-800 border-blue-400 animate-pulse'
                                    }`}
                                style={todayAttendance?.check_in && !todayAttendance?.check_out ? {
                                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.1)'
                                } : {}}
                            >
                                <div className="relative z-10 text-center">
                                    <span className="text-lg font-bold text-white block mb-1">下班</span>
                                    <div className="text-base text-white/90 font-medium">
                                        {todayAttendance?.check_out ? '已打卡' : '尚未打卡'}
                                    </div>
                                </div>
                            </Button>
                        </div>
                    </div>
                </div>

                {/* 🚀 功能區域 - 縮小間距 */}
                <div className="bg-white rounded-2xl p-3 border border-gray-100 shadow-xl">
                    <div className="grid grid-cols-2 gap-3">
                        {/* 上排：請假申請 */}
                        <button
                            onClick={() => router.push('/m/leave-apply')}
                            className="group relative p-3 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden"
                        >
                            {/* 背景裝飾 */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent"></div>
                            <div className="absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full"></div>
                            <div className="absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full"></div>

                            <div className="relative z-10 text-center">
                                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow">
                                    <Calendar className="w-5 h-5 text-white" />
                                </div>
                                <p className="text-lg font-bold text-white mb-1">請假申請</p>
                                <p className="text-base text-blue-100 font-medium">提交請假申請</p>
                            </div>
                        </button>

                        {/* 上排：加班申請 */}
                        <button
                            onClick={() => router.push('/m/overtime-apply')}
                            className="group relative p-3 bg-gradient-to-br from-orange-500 via-orange-600 to-red-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden"
                        >
                            {/* 背景裝飾 */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent"></div>
                            <div className="absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full"></div>
                            <div className="absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full"></div>

                            <div className="relative z-10 text-center">
                                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow">
                                    <Plus className="w-5 h-5 text-white" />
                                </div>
                                <p className="text-lg font-bold text-white mb-1">加班申請</p>
                                <p className="text-base text-orange-100 font-medium">申請加班時數</p>
                            </div>
                        </button>

                        {/* 下排：工作回報 */}
                        <button
                            onClick={() => router.push('/m/work-reports')}
                            className="group relative p-3 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden"
                        >
                            {/* 背景裝飾 */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent"></div>
                            <div className="absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full"></div>
                            <div className="absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full"></div>

                            <div className="relative z-10 text-center">
                                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow">
                                    <FileText className="w-5 h-5 text-white" />
                                </div>
                                <p className="text-lg font-bold text-white mb-1">工作回報</p>
                                <p className="text-base text-indigo-100 font-medium">記錄工作進度</p>
                            </div>
                        </button>

                        {/* 下排：員工設定 */}
                        <button
                            onClick={() => router.push('/m/settings')}
                            className="group relative p-3 bg-gradient-to-br from-green-500 via-emerald-600 to-teal-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden"
                        >
                            {/* 背景裝飾 */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent"></div>
                            <div className="absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full"></div>
                            <div className="absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full"></div>

                            <div className="relative z-10 text-center">
                                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow">
                                    <Settings className="w-5 h-5 text-white" />
                                </div>
                                <p className="text-lg font-bold text-white mb-1">員工設定</p>
                                <p className="text-base text-green-100 font-medium">個人資料設定</p>
                            </div>
                        </button>
                    </div>
                </div>

                {/* 📈 最近記錄 - 縮小間距並統一文字大小 */}
                <div className="bg-white rounded-2xl p-3 border border-gray-100 shadow-xl">
                    <div className="flex items-center space-x-2 mb-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                            <History className="w-3 h-3 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">最近三天記錄</h3>
                    </div>

                    <div className="space-y-2">
                        {recentRecords.slice(0, 3).map((record, index) => (
                            <div key={record.id} className="group p-3 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-200">
                                {/* �� 只顯示日期 - 統一文字大小 */}
                                <div className="mb-2">
                                    <p className="font-bold text-gray-900 text-xl">{record.date}</p>
                                </div>

                                {/* 🕐 打卡時間詳細顯示 - 改為上下排列，統一文字大小 */}
                                <div className="space-y-2">
                                    {/* 上班打卡 */}
                                    <div className="bg-green-50 rounded-lg p-2 border border-green-100">
                                        <div className="flex items-center space-x-2 mb-1">
                                            <div className="w-4 h-4 bg-green-500 rounded-md flex items-center justify-center">
                                                <Clock className="w-2 h-2 text-white" />
                                            </div>
                                            <span className="text-lg font-semibold text-green-700">上班打卡</span>
                                        </div>
                                        <p className="text-xl font-bold text-green-800 ml-6">
                                            {record.check_in || '未打卡'}
                                        </p>
                                    </div>

                                    {/* 下班打卡 */}
                                    <div className="bg-blue-50 rounded-lg p-2 border border-blue-100">
                                        <div className="flex items-center space-x-2 mb-1">
                                            <div className="w-4 h-4 bg-blue-500 rounded-md flex items-center justify-center">
                                                <LogOut className="w-2 h-2 text-white" />
                                            </div>
                                            <span className="text-lg font-semibold text-blue-700">下班打卡</span>
                                        </div>
                                        <p className="text-xl font-bold text-blue-800 ml-6">
                                            {record.check_out || '未打卡'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* 📝 最近請假記錄 - 縮小間距 */}
                <div className="bg-white rounded-2xl p-3 border border-gray-100 shadow-xl">
                    <div className="flex items-center space-x-2 mb-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <Calendar className="w-3 h-3 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">最近請假記錄</h3>
                    </div>

                    <div className="space-y-2">
                        {leaveRequests.slice(0, 5).length > 0 ? (
                            leaveRequests.slice(0, 5).map((request, index) => (
                                <div key={request.id} className="group p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 hover:border-blue-200 hover:shadow-md transition-all duration-200">
                                    {/* 📅 請假標題和狀態 */}
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center space-x-2">
                                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                <Calendar className="w-4 h-4 text-white" />
                                            </div>
                                            <div>
                                                <p className="font-bold text-blue-900 text-xl">{request.type}</p>
                                                <p className="text-lg text-blue-600">請假申請</p>
                                            </div>
                                        </div>
                                        {request.status === 'pending' ? (
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={() => handleCancelLeaveRequest(request.id)}
                                                    className="p-1 rounded-full bg-red-100 hover:bg-red-200 transition-colors duration-200 group"
                                                    title="取消申請"
                                                >
                                                    <Trash2 className="w-4 h-4 text-red-600 group-hover:text-red-700" />
                                                </button>
                                                <span className="px-2 py-1 rounded-full text-lg font-bold bg-amber-100 text-amber-800 border border-amber-300 animate-pulse">
                                                    🕐 待審核
                                                </span>
                                            </div>
                                        ) : request.status === 'approved' ? (
                                            <span className="px-2 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                                ✅ 已核准
                                            </span>
                                        ) : (
                                            <span className="px-2 py-1 rounded-full text-lg font-bold bg-red-100 text-red-800 border border-red-300">
                                                ❌ 已拒絕
                                            </span>
                                        )}
                                    </div>

                                    {/* 📋 請假詳細資訊 */}
                                    <div className="space-y-2">
                                        {/* 請假日期 */}
                                        <div className="bg-white/60 rounded-lg p-2 border border-blue-200">
                                            <div className="flex items-center space-x-1 mb-1">
                                                <Calendar className="w-3 h-3 text-blue-600" />
                                                <span className="text-lg font-semibold text-blue-700">請假期間</span>
                                            </div>
                                            <p className="text-lg font-bold text-blue-900">
                                                {request.start_date} 至 {request.end_date}
                                            </p>
                                            <p className="text-lg text-blue-600 mt-1">
                                                {(() => {
                                                    const start = new Date(request.start_date)
                                                    const end = new Date(request.end_date)
                                                    const diffTime = Math.abs(end.getTime() - start.getTime())
                                                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
                                                    return `共 ${diffDays} 天`
                                                })()}
                                            </p>
                                        </div>

                                        {/* 請假原因 */}
                                        {request.reason && (
                                            <div className="bg-white/60 rounded-lg p-2 border border-blue-200">
                                                <div className="flex items-center space-x-1 mb-1">
                                                    <FileText className="w-3 h-3 text-blue-600" />
                                                    <span className="text-lg font-semibold text-blue-700">請假原因</span>
                                                </div>
                                                <p className="text-lg text-blue-800 leading-relaxed">
                                                    {request.reason}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-4">
                                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <Calendar className="w-5 h-5 text-blue-600" />
                                </div>
                                <p className="text-gray-600 font-medium text-base">尚無請假記錄</p>
                                <p className="text-sm text-gray-500">您還沒有提交過請假申請</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* 🕐 最近加班申請 - 縮小間距 */}
                <div className="bg-white rounded-2xl p-3 border border-gray-100 shadow-xl">
                    <div className="flex items-center space-x-2 mb-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                            <Plus className="w-3 h-3 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">最近加班申請</h3>
                    </div>

                    <div className="space-y-2">
                        {overtimeRequests.slice(0, 5).length > 0 ? (
                            overtimeRequests.slice(0, 5).map((request, index) => (
                                <div key={request.id} className="group p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-100 hover:border-orange-200 hover:shadow-md transition-all duration-200">
                                    {/* 🕐 加班標題和狀態 */}
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center space-x-2">
                                            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                                                <Clock className="w-4 h-4 text-white" />
                                            </div>
                                            <div>
                                                <p className="font-bold text-orange-900 text-xl">{request.overtime_date}</p>
                                                <p className="text-lg text-orange-600">加班申請</p>
                                            </div>
                                        </div>
                                        {request.status === 'pending' ? (
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={() => handleCancelOvertimeRequest(request.id)}
                                                    className="p-1 rounded-full bg-red-100 hover:bg-red-200 transition-colors duration-200 group"
                                                    title="取消申請"
                                                >
                                                    <Trash2 className="w-4 h-4 text-red-600 group-hover:text-red-700" />
                                                </button>
                                                <span className="px-2 py-1 rounded-full text-lg font-bold bg-amber-100 text-amber-800 border border-amber-300 animate-pulse">
                                                    🕐 待審核
                                                </span>
                                            </div>
                                        ) : request.status === 'approved' ? (
                                            <span className="px-2 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                                ✅ 已核准
                                            </span>
                                        ) : (
                                            <span className="px-2 py-1 rounded-full text-lg font-bold bg-red-100 text-red-800 border border-red-300">
                                                ❌ 已拒絕
                                            </span>
                                        )}
                                    </div>

                                    {/* 📋 加班詳細資訊 */}
                                    <div className="space-y-2">
                                        {/* 加班時間 */}
                                        <div className="bg-white/60 rounded-lg p-2 border border-orange-200">
                                            <div className="flex items-center space-x-1 mb-1">
                                                <Timer className="w-3 h-3 text-orange-600" />
                                                <span className="text-lg font-semibold text-orange-700">加班時間</span>
                                            </div>
                                            <p className="text-lg font-bold text-orange-900">
                                                {request.start_time} 至 {request.end_time}
                                            </p>
                                            <p className="text-lg text-orange-600 mt-1">
                                                {(() => {
                                                    const start = new Date(`2000-01-01 ${request.start_time}`)
                                                    const end = new Date(`2000-01-01 ${request.end_time}`)
                                                    const diffHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60)
                                                    return `共 ${diffHours.toFixed(1)} 小時`
                                                })()}
                                            </p>
                                        </div>

                                        {/* 加班原因 */}
                                        {request.reason && (
                                            <div className="bg-white/60 rounded-lg p-2 border border-orange-200">
                                                <div className="flex items-center space-x-1 mb-1">
                                                    <FileText className="w-3 h-3 text-orange-600" />
                                                    <span className="text-lg font-semibold text-orange-700">加班原因</span>
                                                </div>
                                                <p className="text-lg text-orange-800 leading-relaxed">
                                                    {request.reason}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-4">
                                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <Plus className="w-5 h-5 text-orange-600" />
                                </div>
                                <p className="text-gray-600 font-medium text-base">尚無加班申請</p>
                                <p className="text-sm text-gray-500">您還沒有提交過加班申請</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
} 