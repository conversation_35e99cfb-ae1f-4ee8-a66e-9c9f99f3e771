{"summary": {"test_type": "加班計算專項測試", "total_tests": 52, "total_overtime_scenarios": 35, "passed_tests": 40, "passed_scenarios": 25, "failed_tests": 12, "failed_scenarios": 10, "scenario_success_rate": 71.43, "overall_success_rate": 76.92}, "shift_types_tested": ["標準班（8:30-17:30）", "早班（6:30-14:30）", "晚班（14:00-22:00）", "夜班（22:00-06:00，跨日）", "彈性班（9:30-18:30，僅下班後加班）", "無加班班（8:00-17:00，不計算加班）"], "test_coverage": {"normal_scenarios": "準時上下班", "pre_overtime": "上班前加班（各種門檻測試）", "post_overtime": "下班後加班（各種門檻測試）", "combined_overtime": "早到晚走組合", "extreme_cases": "極端加班情況", "edge_cases": "邊界情況處理", "no_overtime": "無加班情況"}, "detailed_results": [{"test_name": "創建班別: 標準班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.166296", "details": "ID: 88, 門檻: 前30分/後60分", "error": null}, {"test_name": "創建班別: 早班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.170881", "details": "ID: 89, 門檻: 前45分/後90分", "error": null}, {"test_name": "創建班別: 晚班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.176327", "details": "ID: 90, 門檻: 前60分/後60分", "error": null}, {"test_name": "創建班別: 夜班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.180246", "details": "ID: 91, 門檻: 前60分/後60分", "error": null}, {"test_name": "創建班別: 彈性班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.184287", "details": "ID: 92, 門檻: 前0分/後30分", "error": null}, {"test_name": "創建班別: 無加班班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.187741", "details": "ID: 93, 門檻: 前0分/後0分", "error": null}, {"test_name": "標準班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.191304", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:30-17:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 早到30分（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.194219", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:00-17:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 早到31分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.196941", "details": "實際: 總0.02h(前0.02h/後0h) | 預期: 總0.5h(前0.5h/後0h) | 時間: 07:59-17:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 早到1小時", "status": "PASS", "timestamp": "2025-06-02T12:56:52.203517", "details": "實際: 總0.5h(前0.5h/後0h) | 預期: 總0.5h(前0.5h/後0h) | 時間: 07:30-17:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 早到1.5小時", "status": "PASS", "timestamp": "2025-06-02T12:56:52.270393", "details": "實際: 總1.0h(前1.0h/後0h) | 預期: 總1.0h(前1.0h/後0h) | 時間: 07:00-17:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 晚走1小時（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.274428", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:30-18:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 晚走1小時1分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.279095", "details": "實際: 總0.02h(前0h/後0.02h) | 預期: 總0.5h(前0h/後0.5h) | 時間: 08:30-18:31", "error": null}, {"test_name": "標準班-OT-0602125652 - 晚走2小時", "status": "PASS", "timestamp": "2025-06-02T12:56:52.295551", "details": "實際: 總1.0h(前0h/後1.0h) | 預期: 總1.0h(前0h/後1.0h) | 時間: 08:30-19:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 早到1小時+晚走2小時", "status": "PASS", "timestamp": "2025-06-02T12:56:52.299245", "details": "實際: 總1.5h(前0.5h/後1.0h) | 預期: 總1.5h(前0.5h/後1.0h) | 時間: 07:30-19:30", "error": null}, {"test_name": "標準班-OT-0602125652 - 極端加班情況", "status": "PASS", "timestamp": "2025-06-02T12:56:52.302808", "details": "實際: 總3.5h(前1.5h/後2.0h) | 預期: 總3.5h(前1.5h/後2.0h) | 時間: 06:30-20:30", "error": null}, {"test_name": "早班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.305348", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 06:30-14:30", "error": null}, {"test_name": "早班-OT-0602125652 - 早到30分（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.307920", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 06:00-14:30", "error": null}, {"test_name": "早班-OT-0602125652 - 早到46分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.311369", "details": "實際: 總0.02h(前0.02h/後0h) | 預期: 總0.75h(前0.75h/後0h) | 時間: 05:44-14:30", "error": null}, {"test_name": "早班-OT-0602125652 - 晚走1小時（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.314516", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 06:30-15:30", "error": null}, {"test_name": "早班-OT-0602125652 - 晚走1.5小時（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.317221", "details": "實際: 總0.02h(前0h/後0.02h) | 預期: 總0.5h(前0h/後0.5h) | 時間: 06:30-16:01", "error": null}, {"test_name": "早班-OT-0602125652 - 早到1小時+晚走2小時", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.324195", "details": "實際: 總0.75h(前0.25h/後0.5h) | 預期: 總1.75h(前0.75h/後1.0h) | 時間: 05:30-16:30", "error": null}, {"test_name": "晚班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.327849", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 14:00-22:00", "error": null}, {"test_name": "晚班-OT-0602125652 - 早到30分（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.331332", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 13:30-22:00", "error": null}, {"test_name": "晚班-OT-0602125652 - 早到1小時1分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.335268", "details": "實際: 總0.02h(前0.02h/後0h) | 預期: 總1.0h(前1.0h/後0h) | 時間: 12:59-22:00", "error": null}, {"test_name": "晚班-OT-0602125652 - 晚走1小時（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.338533", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 14:00-23:00", "error": null}, {"test_name": "晚班-OT-0602125652 - 晚走1小時1分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.341842", "details": "實際: 總0.02h(前0h/後0.02h) | 預期: 總1.0h(前0h/後1.0h) | 時間: 14:00-23:01", "error": null}, {"test_name": "夜班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.345233", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 22:00-06:00", "error": null}, {"test_name": "夜班-OT-0602125652 - 早到30分（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.348773", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 21:30-06:00", "error": null}, {"test_name": "夜班-OT-0602125652 - 早到1小時1分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.351966", "details": "實際: 總0.02h(前0.02h/後0h) | 預期: 總1.0h(前1.0h/後0h) | 時間: 20:59-06:00", "error": null}, {"test_name": "夜班-OT-0602125652 - 晚走1小時（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.354929", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 22:00-07:00", "error": null}, {"test_name": "夜班-OT-0602125652 - 晚走1小時1分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.358035", "details": "實際: 總0.02h(前0h/後0.02h) | 預期: 總1.0h(前0h/後1.0h) | 時間: 22:00-07:01", "error": null}, {"test_name": "彈性班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.360991", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 09:30-18:30", "error": null}, {"test_name": "彈性班-OT-0602125652 - 早到1.5小時（不計算）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.363947", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:00-18:30", "error": null}, {"test_name": "彈性班-OT-0602125652 - 晚走30分（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.366681", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 09:30-19:00", "error": null}, {"test_name": "彈性班-OT-0602125652 - 晚走31分（達門檻）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.369672", "details": "實際: 總0.02h(前0h/後0.02h) | 預期: 總0.5h(前0h/後0.5h) | 時間: 09:30-19:01", "error": null}, {"test_name": "彈性班-OT-0602125652 - 早到+晚走（僅計算晚走）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.372455", "details": "實際: 總1.5h(前0h/後1.5h) | 預期: 總1.5h(前0h/後1.5h) | 時間: 07:00-20:30", "error": null}, {"test_name": "無加班班-OT-0602125652 - 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:56:52.375402", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:00-17:00", "error": null}, {"test_name": "無加班班-OT-0602125652 - 早到2小時（不計算）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.378315", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 06:00-17:00", "error": null}, {"test_name": "無加班班-OT-0602125652 - 晚走3小時（不計算）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.383199", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 08:00-20:00", "error": null}, {"test_name": "無加班班-OT-0602125652 - 早到晚走（完全不計算）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.386154", "details": "實際: 總0h(前0h/後0h) | 預期: 總0h(前0h/後0h) | 時間: 06:00-20:00", "error": null}, {"test_name": "邊界測試: 相同時間", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.391594", "details": null, "error": "期望錯誤但獲得: 200"}, {"test_name": "邊界測試: 結束時間早於開始時間（非跨日）", "status": "FAIL", "timestamp": "2025-06-02T12:56:52.395563", "details": null, "error": "期望錯誤但獲得: 200"}, {"test_name": "邊界測試: 極端早到（凌晨2點）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.398846", "details": "計算結果: 6.0h", "error": null}, {"test_name": "邊界測試: 極端晚走（次日凌晨）", "status": "PASS", "timestamp": "2025-06-02T12:56:52.402255", "details": "計算結果: 0h", "error": null}, {"test_name": "清理: 標準班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.405862", "details": null, "error": null}, {"test_name": "清理: 早班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.409643", "details": null, "error": null}, {"test_name": "清理: 晚班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.413085", "details": null, "error": null}, {"test_name": "清理: 夜班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.416787", "details": null, "error": null}, {"test_name": "清理: 彈性班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.420622", "details": null, "error": null}, {"test_name": "清理: 無加班班-OT-0602125652", "status": "PASS", "timestamp": "2025-06-02T12:56:52.423826", "details": null, "error": null}, {"test_name": "清理總結", "status": "PASS", "timestamp": "2025-06-02T12:56:52.423836", "details": "成功清理 6/6 個測試班別", "error": null}], "generated_at": "2025-06-02T12:56:52.423899"}