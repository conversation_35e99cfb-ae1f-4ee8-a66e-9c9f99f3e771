#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
import sqlite3
import logging

app = Flask(__name__)

def create_connection():
    try:
        conn = sqlite3.connect('attendance.db')
        return conn
    except Exception as e:
        print(f"資料庫連接錯誤: {e}")
        return None

@app.route("/test-shift", methods=["POST"])
def test_shift():
    try:
        data = request.get_json()
        shift_id = data.get('shift_id', 1)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 測試查詢
        cursor.execute("""
            SELECT id, name, start_time, end_time, break_start_time, break_duration_minutes
            FROM shifts
            WHERE id = ?
        """, (shift_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return jsonify({
                "success": True,
                "shift": {
                    "id": result[0],
                    "name": result[1],
                    "start_time": result[2],
                    "end_time": result[3],
                    "break_start_time": result[4],
                    "break_duration_minutes": result[5]
                }
            })
        else:
            return jsonify({"success": False, "error": "班表不存在"})
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=7073, debug=True) 