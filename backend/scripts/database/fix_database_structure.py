#!/usr/bin/env python3
"""
資料庫結構修復腳本

此腳本負責：
1. 檢查當前資料庫結構
2. 備份重要資料
3. 更新表結構以匹配代碼定義
4. 恢復資料
5. 驗證修復結果
"""

import sqlite3
import json
import os
from datetime import datetime
from database import create_connection

def backup_data():
    """備份重要資料"""
    print("🔄 開始備份資料...")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    backup_dict = {}
    
    try:
        # 備份schedules表資料
        cursor.execute("SELECT * FROM schedules")
        schedules_columns = [description[0] for description in cursor.description]
        schedules_data = cursor.fetchall()
        backup_dict['schedules'] = {
            'columns': schedules_columns,
            'data': schedules_data
        }
        print(f"   ✅ 備份schedules表: {len(schedules_data)}筆資料")
        
        # 備份shifts表資料
        cursor.execute("SELECT * FROM shifts")
        shifts_columns = [description[0] for description in cursor.description]
        shifts_data = cursor.fetchall()
        backup_dict['shifts'] = {
            'columns': shifts_columns,
            'data': shifts_data
        }
        print(f"   ✅ 備份shifts表: {len(shifts_data)}筆資料")
        
        # 備份其他重要表
        important_tables = ['employees', 'departments', 'attendance', 'leaves']
        for table in important_tables:
            try:
                cursor.execute(f"SELECT * FROM {table}")
                columns = [description[0] for description in cursor.description]
                data = cursor.fetchall()
                backup_dict[table] = {
                    'columns': columns,
                    'data': data
                }
                print(f"   ✅ 備份{table}表: {len(data)}筆資料")
            except sqlite3.Error as e:
                print(f"   ⚠️  備份{table}表失敗: {e}")
        
        # 保存備份檔案
        backup_filename = f"database_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(backup_dict, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"   💾 備份檔案已保存: {backup_filename}")
        conn.close()
        return backup_filename, backup_dict
        
    except Exception as e:
        print(f"❌ 備份失敗: {e}")
        conn.close()
        return None, None

def check_table_structure():
    """檢查當前表結構"""
    print("🔍 檢查當前表結構...")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 檢查schedules表結構
        cursor.execute("PRAGMA table_info(schedules)")
        schedules_structure = cursor.fetchall()
        print("   📋 schedules表當前結構:")
        for col in schedules_structure:
            print(f"      - {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
        
        # 檢查shifts表結構
        cursor.execute("PRAGMA table_info(shifts)")
        shifts_structure = cursor.fetchall()
        print("   📋 shifts表當前結構:")
        for col in shifts_structure:
            print(f"      - {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查表結構失敗: {e}")
        conn.close()
        return False

def fix_schedules_table():
    """修復schedules表結構"""
    print("🔧 修復schedules表結構...")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 檢查是否存在shift_id欄位
        cursor.execute("PRAGMA table_info(schedules)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'shift_id' not in columns:
            print("   🆕 schedules表缺少shift_id欄位，開始修復...")
            
            # 重命名舊表
            cursor.execute("ALTER TABLE schedules RENAME TO schedules_old")
            
            # 創建新的schedules表
            cursor.execute("""
                CREATE TABLE schedules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    shift_date DATE NOT NULL,
                    shift_id INTEGER NOT NULL,  -- 關聯到班別表
                    status TEXT DEFAULT 'scheduled',  -- scheduled, completed, cancelled
                    actual_start_time TIME,  -- 實際上班時間
                    actual_end_time TIME,    -- 實際下班時間
                    overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 加班時數
                    pre_overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 上班前加班時數
                    post_overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 下班後加班時數
                    break_time_minutes INTEGER DEFAULT 0,  -- 實際休息時間（分鐘）
                    note TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (shift_id) REFERENCES shifts (id)
                )
            """)
            
            # 從舊表遷移資料，將shift_type映射到shift_id
            # 首先獲取預設班別ID
            cursor.execute("SELECT id FROM shifts WHERE code = 'STANDARD_DAY' LIMIT 1")
            default_shift_result = cursor.fetchone()
            default_shift_id = default_shift_result[0] if default_shift_result else 1
            
            # 遷移資料
            cursor.execute(f"""
                INSERT INTO schedules (
                    employee_id, shift_date, shift_id, status, 
                    actual_start_time, actual_end_time, note, created_at
                )
                SELECT 
                    employee_id, 
                    shift_date, 
                    {default_shift_id}, -- 使用預設班別
                    'scheduled',
                    start_time,
                    end_time,
                    '從舊系統遷移',
                    created_at
                FROM schedules_old
            """)
            
            migrated_count = cursor.rowcount
            print(f"   ✅ 成功遷移 {migrated_count} 筆排班資料")
            
            # 刪除舊表
            cursor.execute("DROP TABLE schedules_old")
            print("   ✅ 清理舊表完成")
            
        else:
            print("   ✅ schedules表結構正確，無需修復")
        
        # 重新創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_employee_date ON schedules(employee_id, shift_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_shift ON schedules(shift_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_schedules_date ON schedules(shift_date)")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修復schedules表失敗: {e}")
        conn.rollback()
        conn.close()
        return False

def verify_structure():
    """驗證修復後的表結構"""
    print("✅ 驗證修復結果...")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 驗證schedules表
        cursor.execute("PRAGMA table_info(schedules)")
        schedules_columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['id', 'employee_id', 'shift_date', 'shift_id', 'status']
        missing_columns = [col for col in required_columns if col not in schedules_columns]
        
        if not missing_columns:
            print("   ✅ schedules表結構驗證通過")
            
            # 測試基本查詢
            cursor.execute("SELECT COUNT(*) FROM schedules")
            count = cursor.fetchone()[0]
            print(f"   📊 schedules表資料數量: {count}")
            
            # 測試關聯查詢
            cursor.execute("""
                SELECT COUNT(*) FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                LIMIT 1
            """)
            print("   ✅ schedules與shifts表關聯查詢正常")
            
        else:
            print(f"   ❌ schedules表缺少欄位: {missing_columns}")
            return False
        
        # 驗證shifts表
        cursor.execute("SELECT COUNT(*) FROM shifts WHERE is_active = 1")
        active_shifts = cursor.fetchone()[0]
        print(f"   📊 可用班別數量: {active_shifts}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        conn.close()
        return False

def update_attendance_processor():
    """修復attendance_processor.py中的SQL查詢"""
    print("🔧 修復attendance_processor.py中的SQL查詢...")
    
    try:
        # 讀取檔案
        with open('services/attendance_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到有問題的SQL查詢並修復
        old_sql = """            cursor.execute(\"\"\"
                SELECT s.start_time, s.end_time, s.break_duration_minutes,
                       s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes,
                       s.enable_pre_overtime, s.enable_post_overtime
                FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                WHERE sc.employee_id = ? AND sc.shift_date = ?
            \"\"\", (employee_id, work_date))"""
        
        new_sql = """            cursor.execute(\"\"\"
                SELECT s.start_time, s.end_time, s.break_duration_minutes,
                       s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes,
                       s.enable_pre_overtime, s.enable_post_overtime
                FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                WHERE sc.employee_id = ? AND sc.shift_date = ?
            \"\"\", (employee_id, work_date))"""
        
        # 如果找不到舊的註釋掉的代碼，直接替換新代碼
        if "# 暫時註釋掉原本的查詢" in content:
            # 移除註釋，恢復正常查詢
            content = content.replace(
                "            # 暫時註釋掉原本的查詢，因為schedules表結構不支援",
                "            # 獲取員工的班別資訊（如果有排班）"
            )
            content = content.replace(
                "            # cursor.execute(\"\"\"",
                "            cursor.execute(\"\"\""
            )
            content = content.replace(
                "            # \"\"\", (employee_id, work_date))",
                "            \"\"\", (employee_id, work_date))"
            )
            content = content.replace(
                "            # shift_info = cursor.fetchone()",
                "            shift_info = cursor.fetchone()"
            )
            
            # 移除警告和跳過邏輯
            lines = content.split('\n')
            new_lines = []
            skip_warning_block = False
            
            for line in lines:
                if "# 記錄警告" in line:
                    skip_warning_block = True
                    continue
                elif skip_warning_block and line.strip() and not line.startswith(' '):
                    skip_warning_block = False
                
                if not skip_warning_block:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
            
        # 寫回檔案
        with open('services/attendance_processor.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ attendance_processor.py修復完成")
        return True
        
    except Exception as e:
        print(f"❌ 修復attendance_processor.py失敗: {e}")
        return False

def main():
    """主函數"""
    print("🏢 AttendanceOS 資料庫結構修復工具")
    print("=" * 60)
    
    # 步驟1: 檢查當前結構
    if not check_table_structure():
        print("❌ 無法檢查資料庫結構，中止修復")
        return False
    
    # 步驟2: 備份資料
    backup_file, backup_dict = backup_data()
    if not backup_file:
        print("❌ 資料備份失敗，中止修復")
        return False
    
    # 步驟3: 修復schedules表結構
    if not fix_schedules_table():
        print("❌ 修復schedules表失敗")
        return False
    
    # 步驟4: 修復程式碼中的SQL查詢
    if not update_attendance_processor():
        print("❌ 修復程式碼失敗")
        return False
    
    # 步驟5: 驗證修復結果
    if not verify_structure():
        print("❌ 驗證修復結果失敗")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 資料庫結構修復完成!")
    print(f"💾 備份檔案: {backup_file}")
    print("✅ schedules表已更新為正確結構")
    print("✅ 程式碼中的SQL查詢已修復")
    print("✅ 表結構驗證通過")
    print("\n現在可以安全地測試排班功能了！")
    
    return True

if __name__ == "__main__":
    main() 