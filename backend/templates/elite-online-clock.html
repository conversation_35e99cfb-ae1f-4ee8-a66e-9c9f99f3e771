<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>線上打卡 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .clock-animation {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%,
            100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        .success-ripple {
            animation: ripple 0.6s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-sans min-h-screen">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/user" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回申請系統</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-lg">
            <!-- 頁面標題 -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-4">線上打卡</h1>
                <p class="text-gray-600">點擊按鈕進行上下班打卡</p>
            </div>

            <!-- 時間顯示 -->
            <div class="text-center mb-8">
                <div class="text-5xl font-bold text-gray-900 mb-2" id="currentTime">09:00:00</div>
                <div class="text-lg text-gray-500" id="currentDate">2024年5月28日 星期二</div>
            </div>

            <!-- 打卡區域 -->
            <div class="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl shadow-indigo-100/50 border border-white/20 mb-6">
                <div class="text-center">
                    <!-- 打卡按鈕 -->
                    <div class="flex justify-center space-x-12 mb-8">
                        <!-- 上班打卡按鈕 -->
                        <div class="flex flex-col items-center">
                            <button id="clockInButton" class="group relative w-44 h-44 bg-gradient-to-br from-emerald-400 via-green-500 to-emerald-600 rounded-full flex flex-col items-center justify-center text-white shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 clock-animation">
                                <!-- 動態光暈效果 -->
                                <div class="absolute inset-0 bg-gradient-to-br from-emerald-300/30 to-green-600/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="relative z-10 flex flex-col items-center">
                                    <i data-lucide="log-in" class="w-12 h-12 mb-4 drop-shadow-lg"></i>
                                    <span class="text-xl font-semibold tracking-wide">上班打卡</span>
                                    <span class="text-sm opacity-90 mt-1">Clock In</span>
                                </div>
                                <!-- 內圈光暈 -->
                                <div class="absolute inset-4 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </button>
                            <!-- 上班時間顯示 -->
                            <div id="firstPunchDisplay" class="mt-4 text-center">
                                <div class="text-sm text-gray-500 mb-1">上班時間</div>
                                <div id="firstPunchTime" class="text-lg font-semibold text-emerald-600">--:--</div>
                            </div>
                        </div>

                        <!-- 下班打卡按鈕 -->
                        <div class="flex flex-col items-center">
                            <button id="clockOutButton" class="group relative w-44 h-44 bg-gradient-to-br from-orange-400 via-red-500 to-pink-600 rounded-full flex flex-col items-center justify-center text-white shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 clock-animation">
                                <!-- 動態光暈效果 -->
                                <div class="absolute inset-0 bg-gradient-to-br from-orange-300/30 to-pink-600/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="relative z-10 flex flex-col items-center">
                                    <i data-lucide="log-out" class="w-12 h-12 mb-4 drop-shadow-lg"></i>
                                    <span class="text-xl font-semibold tracking-wide">下班打卡</span>
                                    <span class="text-sm opacity-90 mt-1">Clock Out</span>
                                </div>
                                <!-- 內圈光暈 -->
                                <div class="absolute inset-4 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </button>
                            <!-- 下班時間顯示 -->
                            <div id="lastPunchDisplay" class="mt-4 text-center">
                                <div class="text-sm text-gray-500 mb-1">下班時間</div>
                                <div id="lastPunchTime" class="text-lg font-semibold text-orange-600">--:--</div>
                            </div>
                        </div>
                    </div>

                    <!-- 狀態顯示 -->
                    <div id="statusDisplay" class="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl shadow-indigo-100/50 border border-white/20 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-3 shadow-lg">
                                    <i data-lucide="activity" class="w-5 h-5 text-white"></i>
                                </div>
                                準備打卡
                            </h2>
                            <div class="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                                即時狀態
                            </div>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-6">點擊上方按鈕進行打卡操作</p>
                        <div class="flex items-center justify-center space-x-6">
                            <div class="flex items-center space-x-3 bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 rounded-2xl border border-green-100">
                                <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse shadow-lg"></div>
                                <span class="text-sm font-medium text-gray-700">系統運行中</span>
                            </div>
                            <div class="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-2xl border border-blue-100">
                                <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full shadow-lg"></div>
                                <span class="text-sm font-medium text-gray-700">GPS 定位正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- 通知訊息 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 max-w-sm transform transition-all duration-300 translate-y-0">
            <div class="flex items-start space-x-4">
                <div id="notificationIcon" class="w-8 h-8 flex-shrink-0"></div>
                <div class="flex-1">
                    <p id="notificationTitle" class="font-semibold text-gray-900 text-lg"></p>
                    <p id="notificationMessage" class="text-sm text-gray-600 mt-1 leading-relaxed"></p>
                </div>
            </div>
            <!-- 進度條 -->
            <div class="mt-4 h-1 bg-gray-100 rounded-full overflow-hidden">
                <div class="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-3000 w-full"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全域變數
        const API_BASE = '/api';
        const DEFAULT_EMPLOYEE_ID = 1; // 使用預設員工ID
        let todayAttendance = null;
        let todayPunchRecords = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('頁面初始化開始');

            // 初始化圖標
            lucide.createIcons();

            // 添加事件監聽器
            document.getElementById('clockInButton').addEventListener('click', function() {
                console.log('上班打卡按鈕被點擊');
                performClock('clock_in');
            });

            document.getElementById('clockOutButton').addEventListener('click', function() {
                console.log('下班打卡按鈕被點擊');
                performClock('clock_out');
            });

            // 開始時間更新
            updateTime();
            setInterval(updateTime, 1000);

            // 載入今日狀態
            loadTodayAttendance();
            loadTodayPunchRecords();

            console.log('頁面初始化完成');
        });

        // 更新時間顯示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-TW', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }

        // 載入今日考勤狀態
        async function loadTodayAttendance() {
            try {
                const response = await fetch(`${API_BASE}/attendance/today/${DEFAULT_EMPLOYEE_ID}`);
                const data = await response.json();

                if (data.success) {
                    todayAttendance = data.attendance;
                } else {
                    todayAttendance = null;
                }
                updateStatusDisplay();
            } catch (error) {
                console.error('載入今日考勤失敗:', error);
                todayAttendance = null;
                updateStatusDisplay();
            }
        }

        // 載入今日打卡記錄
        async function loadTodayPunchRecords() {
            try {
                console.log('開始載入今日打卡記錄...');
                const response = await fetch(`${API_BASE}/attendance/today-punches/${DEFAULT_EMPLOYEE_ID}`);
                const data = await response.json();
                console.log('API返回數據:', data);

                if (data.success) {
                    todayPunchRecords = data;
                    console.log('設置todayPunchRecords:', todayPunchRecords);
                    updatePunchTimeDisplay();
                } else {
                    console.log('API返回失敗');
                    todayPunchRecords = null;
                    updatePunchTimeDisplay();
                }
            } catch (error) {
                console.error('載入今日打卡記錄失敗:', error);
                todayPunchRecords = null;
                updatePunchTimeDisplay();
            }
        }

        // 更新打卡時間顯示
        function updatePunchTimeDisplay() {
            console.log('開始更新打卡時間顯示...');
            const firstPunchTime = document.getElementById('firstPunchTime');
            const lastPunchTime = document.getElementById('lastPunchTime');

            console.log('DOM元素:', {
                firstPunchTime,
                lastPunchTime
            });
            console.log('todayPunchRecords:', todayPunchRecords);

            if (todayPunchRecords && todayPunchRecords.summary) {
                const {
                    first_punch,
                    last_punch,
                    total_punches
                } = todayPunchRecords.summary;

                console.log('解析數據:', {
                    first_punch,
                    last_punch,
                    total_punches
                });

                // 顯示第一筆打卡時間（上班時間）
                if (first_punch) {
                    const firstTime = formatPunchTime(first_punch);
                    console.log('第一筆打卡時間:', firstTime);
                    firstPunchTime.textContent = firstTime;
                    firstPunchTime.classList.remove('text-gray-400');
                    firstPunchTime.classList.add('text-emerald-600');
                } else {
                    console.log('沒有第一筆打卡時間');
                    firstPunchTime.textContent = '--:--';
                    firstPunchTime.classList.remove('text-emerald-600');
                    firstPunchTime.classList.add('text-gray-400');
                }

                // 顯示最後一筆打卡時間（下班時間）
                if (last_punch && total_punches > 1) {
                    const lastTime = formatPunchTime(last_punch);
                    console.log('最後一筆打卡時間:', lastTime);
                    lastPunchTime.textContent = lastTime;
                    lastPunchTime.classList.remove('text-gray-400');
                    lastPunchTime.classList.add('text-orange-600');
                } else {
                    console.log('沒有最後一筆打卡時間，條件:', {
                        last_punch,
                        total_punches
                    });
                    lastPunchTime.textContent = '--:--';
                    lastPunchTime.classList.remove('text-orange-600');
                    lastPunchTime.classList.add('text-gray-400');
                }
            } else {
                console.log('沒有打卡記錄數據');
                // 沒有打卡記錄
                firstPunchTime.textContent = '--:--';
                lastPunchTime.textContent = '--:--';
                firstPunchTime.classList.remove('text-emerald-600');
                lastPunchTime.classList.remove('text-orange-600');
                firstPunchTime.classList.add('text-gray-400');
                lastPunchTime.classList.add('text-gray-400');
            }
        }

        // 更新狀態顯示
        function updateStatusDisplay() {
            const statusDisplay = document.getElementById('statusDisplay');

            if (!todayAttendance) {
                statusDisplay.innerHTML = `
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-3 shadow-lg">
                                <i data-lucide="activity" class="w-5 h-5 text-white"></i>
                            </div>
                            尚未打卡
                        </h2>
                        <div class="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                            即時狀態
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed mb-6">準備開始新的一天</p>
                    <div class="flex items-center justify-center space-x-6">
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 rounded-2xl border border-green-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">系統運行中</span>
                        </div>
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-2xl border border-blue-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">GPS 定位正常</span>
                        </div>
                    </div>
                `;
            } else if (todayAttendance.check_in && !todayAttendance.check_out) {
                statusDisplay.innerHTML = `
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-3 shadow-lg">
                                <i data-lucide="play-circle" class="w-5 h-5 text-white"></i>
                            </div>
                            工作中
                        </h2>
                        <div class="text-sm text-gray-500 bg-emerald-50 px-3 py-1 rounded-full border border-emerald-200">
                            進行中
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed mb-6">上班時間：${formatTime(todayAttendance.check_in)}</p>
                    <div class="flex items-center justify-center space-x-6">
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-emerald-50 to-green-50 px-4 py-3 rounded-2xl border border-emerald-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">已上班打卡</span>
                        </div>
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-yellow-50 to-orange-50 px-4 py-3 rounded-2xl border border-yellow-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">等待下班打卡</span>
                        </div>
                    </div>
                `;
            } else if (todayAttendance.check_in && todayAttendance.check_out) {
                statusDisplay.innerHTML = `
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-3 shadow-lg">
                                <i data-lucide="check-circle-2" class="w-5 h-5 text-white"></i>
                            </div>
                            今日完成
                        </h2>
                        <div class="text-sm text-gray-500 bg-blue-50 px-3 py-1 rounded-full border border-blue-200">
                            已完成
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed mb-6">工作時間：${formatTime(todayAttendance.check_in)} - ${formatTime(todayAttendance.check_out)}</p>
                    <div class="flex items-center justify-center space-x-6">
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-2xl border border-blue-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">打卡完成</span>
                        </div>
                        <div class="flex items-center space-x-3 bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 rounded-2xl border border-green-100">
                            <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full shadow-lg"></div>
                            <span class="text-sm font-medium text-gray-700">感謝辛勞</span>
                        </div>
                    </div>
                `;
            }

            // 重新初始化圖標
            lucide.createIcons();
        }

        // 執行打卡
        async function performClock(type) {
            console.log(`開始執行${type === 'clock_in' ? '上班' : '下班'}打卡`);

            try {
                const endpoint = type === 'clock_in' ? 'clock-in' : 'clock-out';
                const response = await fetch(`${API_BASE}/attendance/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        employee_id: DEFAULT_EMPLOYEE_ID
                    })
                });

                const data = await response.json();
                console.log('API回應:', data);

                if (data.success) {
                    const actionText = type === 'clock_in' ? '上班打卡' : '下班打卡';
                    showNotification('成功', `${actionText}成功！`, 'success');

                    // 重新載入今日考勤狀態和打卡記錄
                    await loadTodayAttendance();
                    await loadTodayPunchRecords();
                } else {
                    showNotification('錯誤', data.error || '打卡失敗', 'error');
                }
            } catch (error) {
                console.error('打卡失敗:', error);
                showNotification('錯誤', '打卡失敗，請稍後再試', 'error');
            }
        }



        // 顯示通知
        function showNotification(title, message, type = 'info') {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notificationIcon');
            const titleEl = document.getElementById('notificationTitle');
            const messageEl = document.getElementById('notificationMessage');

            // 設定圖標和顏色
            let iconName, colorClass;
            switch (type) {
                case 'success':
                    iconName = 'check-circle';
                    colorClass = 'text-success-500';
                    break;
                case 'error':
                    iconName = 'x-circle';
                    colorClass = 'text-error-500';
                    break;
                default:
                    iconName = 'info';
                    colorClass = 'text-brand-500';
            }

            icon.innerHTML = `<i data-lucide="${iconName}" class="w-6 h-6 ${colorClass}"></i>`;
            titleEl.textContent = title;
            messageEl.textContent = message;

            // 顯示通知
            notification.classList.remove('hidden');
            lucide.createIcons();

            // 3秒後隱藏
            setTimeout(() => {
                notification.classList.add('hidden');
            }, 3000);
        }

        // 工具函數
        function formatTime(timestamp) {
            if (!timestamp) return '-';
            return new Date(timestamp).toLocaleTimeString('zh-TW', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        /**
         * 格式化打卡時間
         * 
         * 參數：
         * timestamp (string): 時間戳字符串
         * 
         * 返回：
         * string: 格式化後的時間字符串 (HH:MM)
         */
        function formatPunchTime(timestamp) {
            if (!timestamp) return '--:--';
            try {
                const date = new Date(timestamp);
                return date.toLocaleTimeString('zh-TW', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                console.error('時間格式化錯誤:', error);
                return '--:--';
            }
        }
    </script>
</body>

</html>