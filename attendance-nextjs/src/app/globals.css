@tailwind base;
@tailwind components;
@tailwind utilities;

/* Han AttendanceOS 全域樣式 */

/* 字體載入 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* 動畫定義 */
@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-soft {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 動畫工具類別 */
.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

.animate-bounce-soft {
  animation: bounce-soft 0.6s ease-out;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}

/* 無障礙支援 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 選取樣式 */
::selection {
  background-color: rgba(59, 130, 246, 0.2);
  color: rgb(59, 130, 246);
}

/* 焦點樣式 */
:focus-visible {
  outline: 2px solid rgb(59, 130, 246);
  outline-offset: 2px;
}

/* 輸入框樣式優化 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
select,
textarea {
  @apply appearance-none;
}

/* 按鈕禁用樣式 */
button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed;
}

/* 毛玻璃效果工具類別 */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

/* 載入動畫 */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* 頁面轉場效果 */
.page-transition {
  @apply animate-fade-in;
}

/* 卡片懸停效果 */
.card-hover {
  @apply transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
}

/* 響應式文字大小 */
.text-responsive-sm {
  @apply text-sm md:text-base;
}

.text-responsive-lg {
  @apply text-lg md:text-xl;
}

.text-responsive-xl {
  @apply text-xl md:text-2xl;
}

.text-responsive-2xl {
  @apply text-2xl md:text-3xl;
}

/* 響應式間距 */
.p-responsive {
  @apply p-4 md:p-6 lg:p-8;
}

.px-responsive {
  @apply px-4 md:px-6 lg:px-8;
}

.py-responsive {
  @apply py-4 md:py-6 lg:py-8;
}

/* 狀態指示器 */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot.success {
  @apply bg-success-500;
}

.status-dot.warning {
  @apply bg-warning-500;
}

.status-dot.error {
  @apply bg-error-500;
}

.status-dot.info {
  @apply bg-info-500;
}

/* 徽章樣式 */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge.success {
  @apply bg-success-100 text-success-800;
}

.badge.warning {
  @apply bg-warning-100 text-warning-800;
}

.badge.error {
  @apply bg-error-100 text-error-800;
}

.badge.info {
  @apply bg-info-100 text-info-800;
}

.badge.neutral {
  @apply bg-neutral-100 text-neutral-800;
}
