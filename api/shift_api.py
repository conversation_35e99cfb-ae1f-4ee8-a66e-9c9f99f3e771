"""
班表管理API模組

包含所有班表和排班管理相關的API端點：
- 班表CRUD操作
- 排班管理
- 加班計算
- 班表統計
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import sqlite3
import logging
from database import create_connection

# 創建藍圖
shift_bp = Blueprint('shift', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

def validate_time_format(time_str):
    """
    驗證時間格式是否為 HH:MM
    
    參數：
    time_str (str): 時間字串
    
    返回：
    bool: 格式是否正確
    """
    try:
        datetime.strptime(time_str, "%H:%M")
        return True
    except ValueError:
        return False

def calculate_day_start_time(start_time):
    """
    根據上班時間計算當天起算時間（上班前兩小時）
    
    參數：
    start_time (str): 上班時間，格式為 HH:MM
    
    返回：
    str: 當天起算時間，格式為 HH:MM
    """
    try:
        # 解析上班時間
        start_dt = datetime.strptime(start_time, "%H:%M")
        
        # 計算上班前兩小時
        day_start_dt = start_dt - timedelta(hours=2)
        
        # 格式化為 HH:MM
        return day_start_dt.strftime("%H:%M")
    except ValueError:
        # 如果解析失敗，返回預設值
        return "06:00"

@shift_bp.route("/api/shifts", methods=["GET"])
def get_shifts():
    """
    獲取班別列表
    
    返回：
    - 班別列表，包含所有班表資訊
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, name, code, start_time, end_time, break_start_time, 
                   break_duration_minutes, pre_overtime_threshold_minutes, 
                   post_overtime_threshold_minutes, enable_pre_overtime, 
                   enable_post_overtime, auto_calculate_overtime, color_code, 
                   description, is_active, created_at, updated_at, day_start_time, is_default
            FROM shifts 
            WHERE is_active = 1 
            ORDER BY is_default DESC, name
        """
        )
        
        shifts = []
        for row in cursor.fetchall():
            shift = {
                "id": row[0],
                "name": row[1],
                "code": row[2],
                "start_time": row[3],
                "end_time": row[4],
                "break_start_time": row[5],
                "break_duration_minutes": row[6],
                "pre_overtime_threshold_minutes": row[7],
                "post_overtime_threshold_minutes": row[8],
                "enable_pre_overtime": bool(row[9]),
                "enable_post_overtime": bool(row[10]),
                "auto_calculate_overtime": bool(row[11]),
                "color_code": row[12],
                "description": row[13],
                "is_active": bool(row[14]),
                "created_at": row[15],
                "updated_at": row[16],
                "day_start_time": row[17],
                "is_default": bool(row[18])
            }
            shifts.append(shift)
        
        return jsonify({"shifts": shifts})
        
    except Exception as e:
        logger.error(f"獲取班別列表失敗: {e}")
        return jsonify({"error": "獲取班別列表失敗"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts", methods=["POST"])
def create_shift():
    """
    新增班別
    
    請求體：
    - name: 班別名稱（必填）
    - code: 班別代碼（必填）
    - start_time: 上班時間（必填，格式：HH:MM）
    - end_time: 下班時間（必填，格式：HH:MM）
    - break_start_time: 休息開始時間（格式：HH:MM）
    - break_duration_minutes: 休息時長（分鐘）
    - pre_overtime_threshold_minutes: 提前加班門檻（分鐘）
    - post_overtime_threshold_minutes: 延後加班門檻（分鐘）
    - enable_pre_overtime: 啟用提前加班
    - enable_post_overtime: 啟用延後加班
    - auto_calculate_overtime: 自動計算加班
    - color_code: 顏色代碼
    - description: 描述
    
    返回：
    - 新增的班別ID
    """
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["name", "code", "start_time", "end_time"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    if not validate_time_format(data["start_time"]):
        return jsonify({"error": "上班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if not validate_time_format(data["end_time"]):
        return jsonify({"error": "下班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if data.get("break_start_time") and not validate_time_format(data["break_start_time"]):
        return jsonify({"error": "休息開始時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別代碼是否已存在
        cursor.execute("SELECT id FROM shifts WHERE code = ?", (data["code"],))
        if cursor.fetchone():
            return jsonify({"error": "班別代碼已存在"}), 400
        
        # 如果設為預設班表，先將所有其他班表的預設狀態取消
        if data.get("is_default", False):
            logger.info("設定新的預設班表，取消所有其他班表的預設狀態")
            cursor.execute("UPDATE shifts SET is_default = 0 WHERE is_active = 1")
            affected_rows = cursor.rowcount
            logger.info(f"已取消 {affected_rows} 個班表的預設狀態")
        
        # 插入新班別
        cursor.execute(
            """
            INSERT INTO shifts (
                name, code, start_time, end_time, break_start_time, 
                break_duration_minutes, pre_overtime_threshold_minutes, 
                post_overtime_threshold_minutes, enable_pre_overtime, 
                enable_post_overtime, auto_calculate_overtime, color_code, 
                description, day_start_time, is_default
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                data["name"],
                data["code"],
                data["start_time"],
                data["end_time"],
                data.get("break_start_time"),
                data.get("break_duration_minutes", 60),
                data.get("pre_overtime_threshold_minutes", 0),
                data.get("post_overtime_threshold_minutes", 0),
                data.get("enable_pre_overtime", False),
                data.get("enable_post_overtime", False),
                data.get("auto_calculate_overtime", True),
                data.get("color_code", "#3B82F6"),
                data.get("description", ""),
                data.get("day_start_time") or calculate_day_start_time(data.get("start_time", "08:00")),
                data.get("is_default", False)
            ),
        )
        
        shift_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"班別新增成功: {data['name']} (ID: {shift_id})")
        return jsonify({"message": "班別新增成功", "id": shift_id}), 201
        
    except Exception as e:
        logger.error(f"新增班別失敗: {e}")
        return jsonify({"error": "新增班別失敗"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts/<int:shift_id>", methods=["GET"])
def get_shift(shift_id):
    """
    獲取單一班別詳情
    
    參數：
    shift_id (int): 班別ID
    
    返回：
    - 班別詳細資訊
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, name, code, start_time, end_time, break_start_time, 
                   break_duration_minutes, pre_overtime_threshold_minutes, 
                   post_overtime_threshold_minutes, enable_pre_overtime, 
                   enable_post_overtime, auto_calculate_overtime, color_code, 
                   description, is_active, created_at, updated_at, day_start_time, is_default
            FROM shifts 
            WHERE id = ?
        """,
            (shift_id,),
        )
        
        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "班別不存在"}), 404
        
        shift = {
            "id": row[0],
            "name": row[1],
            "code": row[2],
            "start_time": row[3],
            "end_time": row[4],
            "break_start_time": row[5],
            "break_duration_minutes": row[6],
            "pre_overtime_threshold_minutes": row[7],
            "post_overtime_threshold_minutes": row[8],
            "enable_pre_overtime": bool(row[9]),
            "enable_post_overtime": bool(row[10]),
            "auto_calculate_overtime": bool(row[11]),
            "color_code": row[12],
            "description": row[13],
            "is_active": bool(row[14]),
            "created_at": row[15],
            "updated_at": row[16],
            "day_start_time": row[17],
            "is_default": bool(row[18])
        }
        
        return jsonify(shift)
        
    except Exception as e:
        logger.error(f"獲取班別詳情失敗: {e}")
        return jsonify({"error": "獲取班別詳情失敗"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts/<int:shift_id>", methods=["PUT"])
def update_shift(shift_id):
    """
    更新班別資料
    
    功能說明：
    - 更新指定班別的所有資訊
    - 包含時間驗證和衝突檢查
    
    參數：
    shift_id (int): 班別ID
    
    請求體：
    - name: 班別名稱（必填）
    - code: 班別代碼（必填）
    - start_time: 上班時間（必填，格式：HH:MM）
    - end_time: 下班時間（必填，格式：HH:MM）
    - break_start_time: 休息開始時間
    - break_duration_minutes: 休息時長（分鐘）
    - pre_overtime_threshold_minutes: 上班前加班門檻
    - post_overtime_threshold_minutes: 下班後加班門檻
    - enable_pre_overtime: 是否啟用上班前加班
    - enable_post_overtime: 是否啟用下班後加班
    - auto_calculate_overtime: 是否自動計算加班
    - color_code: 顏色代碼
    - description: 描述
    
    返回：
    - 更新結果
    """
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["name", "code", "start_time", "end_time"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    if not validate_time_format(data["start_time"]):
        return jsonify({"error": "上班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if not validate_time_format(data["end_time"]):
        return jsonify({"error": "下班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if data.get("break_start_time") and not validate_time_format(data["break_start_time"]):
        return jsonify({"error": "休息開始時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別是否存在
        cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
        if not cursor.fetchone():
            return jsonify({"error": "班別不存在"}), 404
        
        # 檢查班別代碼是否與其他班別衝突
        cursor.execute(
            "SELECT id FROM shifts WHERE code = ? AND id != ?", 
            (data["code"], shift_id)
        )
        if cursor.fetchone():
            return jsonify({"error": "班別代碼已存在"}), 400
        
        # 如果設為預設班表，先將所有其他班表的預設狀態取消
        if data.get("is_default", False):
            logger.info(f"將班表 {shift_id} 設為預設班表，取消所有其他班表的預設狀態")
            cursor.execute("UPDATE shifts SET is_default = 0 WHERE id != ? AND is_active = 1", (shift_id,))
            affected_rows = cursor.rowcount
            logger.info(f"已取消 {affected_rows} 個其他班表的預設狀態")
        # 如果取消預設班表，確保該班表的 is_default 設為 0
        elif data.get("is_default", True) == False:
            logger.info(f"取消班表 {shift_id} 的預設狀態")
        
        # 更新班別
        cursor.execute(
            """
            UPDATE shifts SET 
                name = ?, code = ?, start_time = ?, end_time = ?, 
                break_start_time = ?, break_duration_minutes = ?, 
                pre_overtime_threshold_minutes = ?, post_overtime_threshold_minutes = ?, 
                enable_pre_overtime = ?, enable_post_overtime = ?, 
                auto_calculate_overtime = ?, color_code = ?, description = ?,
                day_start_time = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """,
            (
                data["name"],
                data["code"],
                data["start_time"],
                data["end_time"],
                data.get("break_start_time"),
                data.get("break_duration_minutes", 60),
                data.get("pre_overtime_threshold_minutes", 0),
                data.get("post_overtime_threshold_minutes", 0),
                data.get("enable_pre_overtime", False),
                data.get("enable_post_overtime", False),
                data.get("auto_calculate_overtime", True),
                data.get("color_code", "#3B82F6"),
                data.get("description", ""),
                data.get("day_start_time") or calculate_day_start_time(data.get("start_time", "08:00")),
                data.get("is_default", False),
                shift_id
            ),
        )
        
        conn.commit()
        logger.info(f"班別更新成功: ID {shift_id}")
        return jsonify({"message": "班別更新成功"})
        
    except Exception as e:
        logger.error(f"更新班別失敗: {e}")
        return jsonify({"error": "更新班別失敗"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts/<int:shift_id>", methods=["DELETE"])
def delete_shift(shift_id):
    """
    刪除班別（軟刪除）
    
    功能說明：
    - 軟刪除指定班別，不實際從資料庫中移除
    - 將is_active設為0，保留歷史記錄
    
    參數：
    shift_id (int): 班別ID
    
    返回：
    - 刪除結果
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別是否存在且為活躍狀態
        cursor.execute("SELECT id, is_active FROM shifts WHERE id = ?", (shift_id,))
        shift_record = cursor.fetchone()
        if not shift_record:
            return jsonify({"error": "班別不存在"}), 404
        
        if not shift_record[1]:  # is_active = 0
            return jsonify({"error": "班別已被刪除"}), 400
        
        # 軟刪除班別
        cursor.execute(
            """
            UPDATE shifts SET 
                is_active = 0, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """,
            (shift_id,),
        )
        
        conn.commit()
        
        logger.info(f"班別 {shift_id} 已成功刪除（軟刪除）")
        return jsonify({"message": "班別刪除成功"})
        
    except Exception as e:
        logger.error(f"刪除班別失敗: {e}")
        return jsonify({"error": f"刪除班別失敗: {str(e)}"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts/calculate-overtime", methods=["POST"])
def calculate_overtime():
    """
    計算加班時數
    
    功能說明：
    - 根據班別設定和實際工作時間計算加班時數
    - 支援上班前加班和下班後加班
    - 考慮加班門檻設定
    
    請求體：
    - shift_id: 班別ID（必填）
    - actual_start_time: 實際上班時間（必填，格式：HH:MM）
    - actual_end_time: 實際下班時間（必填，格式：HH:MM）
    
    返回：
    - 加班時數計算結果和詳細資訊
    """
    data = request.json
    
    required_fields = ["shift_id", "actual_start_time", "actual_end_time"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    if not validate_time_format(data["actual_start_time"]):
        return jsonify({"error": "實際上班時間格式錯誤"}), 400
    
    if not validate_time_format(data["actual_end_time"]):
        return jsonify({"error": "實際下班時間格式錯誤"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取班別資訊
        cursor.execute(
            """
            SELECT start_time, end_time, break_duration_minutes,
                   pre_overtime_threshold_minutes, post_overtime_threshold_minutes,
                   enable_pre_overtime, enable_post_overtime, auto_calculate_overtime
            FROM shifts WHERE id = ? AND is_active = 1
        """,
            (data["shift_id"],),
        )
        
        shift = cursor.fetchone()
        if not shift:
            return jsonify({"error": "班別不存在或已停用"}), 404
        
        if not shift[7]:  # auto_calculate_overtime
            return jsonify({
                "pre_overtime_hours": 0,
                "post_overtime_hours": 0,
                "total_overtime_hours": 0,
                "message": "此班別未啟用自動計算加班"
            })
        
        # 計算加班時數的邏輯
        # 解析時間
        shift_start = datetime.strptime(shift[0], "%H:%M").time()
        shift_end = datetime.strptime(shift[1], "%H:%M").time()
        actual_start = datetime.strptime(data["actual_start_time"], "%H:%M").time()
        actual_end = datetime.strptime(data["actual_end_time"], "%H:%M").time()
        
        pre_overtime_hours = 0
        post_overtime_hours = 0
        
        # 計算上班前加班
        if shift[5] and shift[3] > 0:  # enable_pre_overtime and threshold > 0
            # 計算門檻時間：上班時間 - 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_start) - 
                            timedelta(minutes=shift[3])).time()
            
            # 如果實際到達時間早於門檻時間，計算加班
            if actual_start < threshold_time:
                # 加班時間 = 門檻時間 - 實際到達時間
                pre_overtime_minutes = (datetime.combine(datetime.today(), threshold_time) - 
                                      datetime.combine(datetime.today(), actual_start)).total_seconds() / 60
                pre_overtime_hours = pre_overtime_minutes / 60
        
        # 計算下班後加班
        if shift[6] and shift[4] > 0:  # enable_post_overtime and threshold > 0
            # 計算門檻時間：下班時間 + 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_end) + 
                            timedelta(minutes=shift[4])).time()
            
            # 如果實際離開時間晚於門檻時間，計算加班
            if actual_end > threshold_time:
                # 加班時間 = 實際離開時間 - 門檻時間
                post_overtime_minutes = (datetime.combine(datetime.today(), actual_end) - 
                                       datetime.combine(datetime.today(), threshold_time)).total_seconds() / 60
                post_overtime_hours = post_overtime_minutes / 60
        
        total_overtime_hours = pre_overtime_hours + post_overtime_hours
        
        return jsonify({
            "pre_overtime_hours": round(pre_overtime_hours, 2),
            "post_overtime_hours": round(post_overtime_hours, 2),
            "total_overtime_hours": round(total_overtime_hours, 2),
            "break_duration_minutes": shift[2],
            "calculation_details": {
                "shift_start": shift[0],
                "shift_end": shift[1],
                "actual_start": data["actual_start_time"],
                "actual_end": data["actual_end_time"],
                "pre_threshold_minutes": shift[3],
                "post_threshold_minutes": shift[4],
                "enable_pre_overtime": bool(shift[5]),
                "enable_post_overtime": bool(shift[6])
            }
        })
        
    except Exception as e:
        logger.error(f"計算加班時數失敗: {e}")
        return jsonify({"error": f"計算加班時數失敗: {str(e)}"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/shifts/calculate-overtime-advanced", methods=["POST"])
def calculate_overtime_advanced():
    """
    進階加班時數計算
    
    功能說明：
    - 支援更複雜的加班計算規則
    - 包含加班費率計算
    - 支援不同的計算單位和捨入規則
    
    請求體：
    - shift_id: 班別ID（必填）
    - actual_start_time: 實際上班時間（必填）
    - actual_end_time: 實際下班時間（必填）
    - calculation_unit: 計算單位（minutes/hours，預設：minutes）
    - rounding_rule: 捨入規則（up/down/round，預設：down）
    
    返回：
    - 詳細的加班計算結果
    """
    data = request.json
    
    required_fields = ["shift_id", "actual_start_time", "actual_end_time"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    if not validate_time_format(data["actual_start_time"]):
        return jsonify({"error": "實際上班時間格式錯誤"}), 400
    
    if not validate_time_format(data["actual_end_time"]):
        return jsonify({"error": "實際下班時間格式錯誤"}), 400
    
    calculation_unit = data.get("calculation_unit", "minutes")
    rounding_rule = data.get("rounding_rule", "down")
    
    # 驗證參數
    if calculation_unit not in ["minutes", "hours"]:
        return jsonify({"error": "計算單位必須是 minutes 或 hours"}), 400
    
    if rounding_rule not in ["up", "down", "round"]:
        return jsonify({"error": "捨入規則必須是 up、down 或 round"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取班別資訊
        cursor.execute(
            """
            SELECT name, start_time, end_time, break_duration_minutes,
                   pre_overtime_threshold_minutes, post_overtime_threshold_minutes,
                   enable_pre_overtime, enable_post_overtime, auto_calculate_overtime
            FROM shifts WHERE id = ? AND is_active = 1
        """,
            (data["shift_id"],),
        )
        
        shift = cursor.fetchone()
        if not shift:
            return jsonify({"error": "班別不存在或已停用"}), 404
        
        # 解析時間
        shift_start = datetime.strptime(shift[1], "%H:%M").time()
        shift_end = datetime.strptime(shift[2], "%H:%M").time()
        actual_start = datetime.strptime(data["actual_start_time"], "%H:%M").time()
        actual_end = datetime.strptime(data["actual_end_time"], "%H:%M").time()
        
        # 計算標準工作時間
        standard_work_minutes = (datetime.combine(datetime.today(), shift_end) - 
                               datetime.combine(datetime.today(), shift_start)).total_seconds() / 60
        standard_work_minutes -= shift[3]  # 扣除休息時間
        
        # 計算實際工作時間
        actual_work_minutes = (datetime.combine(datetime.today(), actual_end) - 
                             datetime.combine(datetime.today(), actual_start)).total_seconds() / 60
        actual_work_minutes -= shift[3]  # 扣除休息時間
        
        # 計算加班時間
        overtime_minutes = max(0, actual_work_minutes - standard_work_minutes)
        
        # 應用加班門檻
        if shift[6] and shift[5] > 0:  # enable_post_overtime and threshold > 0
            overtime_minutes = max(0, overtime_minutes - shift[5])
        
        # 應用捨入規則
        def apply_rounding(minutes, unit, rule):
            if unit == "hours":
                hours = minutes / 60
                if rule == "up":
                    import math
                    return math.ceil(hours)
                elif rule == "down":
                    import math
                    return math.floor(hours)
                else:  # round
                    return round(hours)
            else:  # minutes
                if rule == "up":
                    import math
                    return math.ceil(minutes)
                elif rule == "down":
                    import math
                    return math.floor(minutes)
                else:  # round
                    return round(minutes)
        
        overtime_result = apply_rounding(overtime_minutes, calculation_unit, rounding_rule)
        
        # 計算加班費率（假設平日1.33倍，假日1.66倍）
        overtime_rate = 1.33  # 可以根據實際需求調整
        
        return jsonify({
            "shift_name": shift[0],
            "standard_work_minutes": standard_work_minutes,
            "actual_work_minutes": actual_work_minutes,
            "overtime_minutes": overtime_minutes,
            "overtime_result": overtime_result,
            "overtime_unit": calculation_unit,
            "rounding_rule": rounding_rule,
            "overtime_rate": overtime_rate,
            "calculation_details": {
                "shift_start": shift[1],
                "shift_end": shift[2],
                "actual_start": data["actual_start_time"],
                "actual_end": data["actual_end_time"],
                "break_duration": shift[3],
                "threshold_minutes": shift[5] if shift[6] else 0
            }
        })
        
    except Exception as e:
        logger.error(f"進階加班計算失敗: {e}")
        return jsonify({"error": f"進階加班計算失敗: {str(e)}"}), 500
    finally:
        conn.close()


@shift_bp.route("/api/schedules/batch", methods=["POST"])
def batch_schedule():
    """
    批次設定班表
    
    功能說明：
    - 支援批次新增多個員工的排班記錄
    - 自動驗證員工和班別的有效性
    
    請求體：
    - 班表記錄陣列，每個記錄包含：
      - employee_id: 員工ID
      - shift_date: 班表日期
      - shift_type: 班別類型
      - start_time: 開始時間
      - end_time: 結束時間
    
    返回：
    - 批次設定結果
    """
    data = request.json
    if not isinstance(data, list):
        return jsonify({"error": "資料格式錯誤，需要陣列格式"}), 400

    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        success_count = 0
        error_records = []
        
        for i, schedule in enumerate(data):
            try:
                # 驗證必要欄位
                required_fields = ["employee_id", "shift_date", "shift_type", "start_time", "end_time"]
                for field in required_fields:
                    if field not in schedule:
                        error_records.append({
                            "index": i,
                            "error": f"缺少必要欄位: {field}",
                            "data": schedule
                        })
                        continue
                
                # 驗證員工是否存在
                cursor.execute("SELECT id FROM employees WHERE id = ?", (schedule["employee_id"],))
                if not cursor.fetchone():
                    error_records.append({
                        "index": i,
                        "error": f"員工ID {schedule['employee_id']} 不存在",
                        "data": schedule
                    })
                    continue
                
                cursor.execute(
                    """
                    INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
                    VALUES (?, ?, ?, ?, ?)
                """,
                    (
                        schedule["employee_id"],
                        schedule["shift_date"],
                        schedule["shift_type"],
                        schedule["start_time"],
                        schedule["end_time"],
                    ),
                )
                success_count += 1
                
            except sqlite3.Error as e:
                error_records.append({
                    "index": i,
                    "error": str(e),
                    "data": schedule
                })
        
        conn.commit()
        logger.info(f"成功批次設定 {success_count} 筆班表資料")
        
        result = {
            "message": f"批次設定完成，成功 {success_count} 筆",
            "success_count": success_count,
            "total_count": len(data),
            "error_count": len(error_records)
        }
        
        if error_records:
            result["errors"] = error_records
        
        return jsonify(result)
        
    except sqlite3.Error as e:
        logger.error(f"批次設定班表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@shift_bp.route("/api/schedules/rules", methods=["GET", "POST"])
def manage_schedule_rules():
    """
    管理排班規則
    
    功能說明：
    - 獲取和設定排班相關的規則
    - 包含工作時間限制、休息規則等
    
    GET: 獲取所有排班規則
    POST: 新增排班規則
    
    返回：
    - 排班規則列表或新增結果
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("SELECT * FROM schedule_rules ORDER BY rule_type")
            columns = [col[0] for col in cursor.description]
            rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify({
                "rules": rules,
                "total": len(rules)
            })
        else:
            data = request.json
            
            # 驗證必要欄位
            required_fields = ["rule_type", "rule_value", "description"]
            for field in required_fields:
                if field not in data:
                    return jsonify({"error": f"缺少必要欄位: {field}"}), 400
            
            cursor.execute(
                """
                INSERT INTO schedule_rules (rule_type, rule_value, description)
                VALUES (?, ?, ?)
            """,
                (data["rule_type"], data["rule_value"], data["description"]),
            )
            
            conn.commit()
            logger.info(f"成功設定排班規則 {data['rule_type']}")
            return jsonify({"message": "規則設定成功"})
            
    except sqlite3.Error as e:
        logger.error(f"管理排班規則錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@shift_bp.route("/api/schedules/calendar", methods=["GET"])
def get_calendar_schedules():
    """
    獲取日曆排班數據
    
    查詢參數：
    - year: 年份 (必填)
    - month: 月份 (必填)
    - employee_id: 員工ID (可選)
    - department_id: 部門ID (可選)
    
    返回：
    - 指定月份的排班數據
    """
    try:
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        employee_id_param = request.args.get('employee_id')  # 可能是字符串編號或數字ID
        department_id = request.args.get('department_id', type=int)
        
        if not year or not month:
            return jsonify({"error": "請提供年份和月份"}), 400
        
        # 計算月份的開始和結束日期
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = ["s.shift_date >= ? AND s.shift_date < ?", "s.status != 'cancelled'"]
        query_params = [start_date, end_date]
        employee_id = None
        
        if employee_id_param:
            # 如果是字符串編號（如E015），需要轉換為數字ID
            if isinstance(employee_id_param, str) and employee_id_param.startswith('E'):
                cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
                result = cursor.fetchone()
                if result:
                    employee_id = result[0]
            else:
                # 如果是數字ID，直接使用
                try:
                    employee_id = int(employee_id_param)
                except ValueError:
                    pass
        
        if employee_id:
            where_conditions.append("s.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        # 執行查詢
        query = f"""
            SELECT s.*, e.name as employee_name, e.employee_id as employee_code,
                   sh.name as shift_name, sh.start_time, sh.end_time, sh.code as shift_code,
                   d.name as department_name
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE {' AND '.join(where_conditions)}
            ORDER BY s.shift_date, s.employee_id
        """
        
        cursor.execute(query, query_params)
        columns = [description[0] for description in cursor.description]
        schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        
        logger.info(f"獲取日曆排班成功: {year}年{month}月，共{len(schedules)}筆記錄")
        return jsonify({
            "year": year,
            "month": month,
            "schedules": schedules,
            "total": len(schedules)
        })
        
    except Exception as e:
        logger.error(f"獲取日曆排班失敗: {e}")
        return jsonify({"error": f"獲取日曆排班失敗: {str(e)}"}), 500


@shift_bp.route("/api/schedules/statistics", methods=["GET"])
def get_schedule_statistics():
    """
    獲取排班統計資訊
    
    查詢參數：
    - year: 年份 (必填)
    - month: 月份 (必填)
    
    返回：
    - 排班統計數據
    """
    try:
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        
        if not year or not month:
            return jsonify({"error": "請提供年份和月份"}), 400
        
        # 計算月份範圍
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 統計排班數量
        cursor.execute("""
            SELECT COUNT(*) as total_schedules,
                   COUNT(DISTINCT employee_id) as total_employees
            FROM schedules 
            WHERE shift_date >= ? AND shift_date < ? AND status != 'cancelled'
        """, (start_date, end_date))
        
        basic_stats = dict(zip([d[0] for d in cursor.description], cursor.fetchone()))
        
        # 統計各班別數量
        cursor.execute("""
            SELECT sh.name, COUNT(*) as count
            FROM schedules s
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            WHERE s.shift_date >= ? AND s.shift_date < ? AND s.status != 'cancelled'
            GROUP BY s.shift_id, sh.name
            ORDER BY count DESC
        """, (start_date, end_date))
        
        shift_stats = [dict(zip([d[0] for d in cursor.description], row)) for row in cursor.fetchall()]
        
        # 統計各部門數量
        cursor.execute("""
            SELECT d.name, COUNT(*) as count
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE s.shift_date >= ? AND s.shift_date < ? AND s.status != 'cancelled'
            GROUP BY e.department_id, d.name
            ORDER BY count DESC
        """, (start_date, end_date))
        
        department_stats = [dict(zip([d[0] for d in cursor.description], row)) for row in cursor.fetchall()]
        
        conn.close()
        
        logger.info(f"獲取排班統計成功: {year}年{month}月")
        return jsonify({
            "period": f"{year}年{month}月",
            "basic_statistics": basic_stats,
            "shift_statistics": shift_stats,
            "department_statistics": department_stats
        })
        
    except Exception as e:
        logger.error(f"獲取排班統計失敗: {e}")
        return jsonify({"error": f"獲取排班統計失敗: {str(e)}"}), 500


@shift_bp.route("/api/schedules/update", methods=["PUT"])
def update_employee_schedule():
    """
    修改員工班表並重新計算考勤記錄
    
    請求體：
    {
        "employee_id": 員工ID,
        "date": "日期 (YYYY-MM-DD)",
        "shift_id": 新的班別ID
    }
    
    返回：
    - 更新結果和重新計算的考勤記錄
    """
    try:
        data = request.get_json()
        employee_id = data.get('employee_id')
        date = data.get('date')
        shift_id = data.get('shift_id')
        
        if not all([employee_id, date, shift_id]):
            return jsonify({"error": "請提供員工ID、日期和班別ID"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查員工是否存在
        cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
        employee = cursor.fetchone()
        if not employee:
            return jsonify({"error": "員工不存在"}), 404
        
        # 檢查班別是否存在
        cursor.execute("SELECT name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
        shift = cursor.fetchone()
        if not shift:
            return jsonify({"error": "班別不存在"}), 404
        
        # 檢查是否已有該日期的排班記錄
        cursor.execute("""
            SELECT id FROM schedules 
            WHERE employee_id = ? AND shift_date = ?
        """, (employee_id, date))
        existing_schedule = cursor.fetchone()
        
        if existing_schedule:
            # 更新現有排班記錄
            cursor.execute("""
                UPDATE schedules 
                SET shift_id = ?, updated_at = CURRENT_TIMESTAMP
                WHERE employee_id = ? AND shift_date = ?
            """, (shift_id, employee_id, date))
            logging.info(f"更新員工 {employee['name']} 在 {date} 的班表為 {shift['name']}")
        else:
            # 新增排班記錄
            cursor.execute("""
                INSERT INTO schedules (employee_id, shift_date, shift_id, status)
                VALUES (?, ?, ?, 'scheduled')
            """, (employee_id, date, shift_id))
            logging.info(f"新增員工 {employee['name']} 在 {date} 的班表為 {shift['name']}")
        
        # 重新計算該日期的考勤記錄
        recalculated_attendance = recalculate_attendance_for_date(employee_id, date, shift_id, cursor)
        
        conn.commit()
        conn.close()
        
        return jsonify({
            "success": True,
            "message": f"成功更新 {employee['name']} 在 {date} 的班表為 {shift['name']}",
            "employee_name": employee['name'],
            "date": date,
            "shift_name": shift['name'],
            "recalculated_attendance": recalculated_attendance
        })
        
    except Exception as e:
        logging.error(f"更新班表失敗: {str(e)}")
        return jsonify({"error": f"更新班表失敗: {str(e)}"}), 500


def recalculate_attendance_for_date(employee_id, date, shift_id, cursor):
    """
    重新計算指定日期的考勤記錄
    
    參數：
    - employee_id: 員工ID
    - date: 日期
    - shift_id: 班別ID
    - cursor: 資料庫游標
    
    返回：
    - 重新計算後的考勤記錄
    """
    try:
        # 獲取班別資訊
        cursor.execute("""
            SELECT name, start_time, end_time, work_hours 
            FROM shifts WHERE id = ?
        """, (shift_id,))
        shift = cursor.fetchone()
        
        if not shift:
            return None
        
        # 獲取該日期的考勤記錄
        cursor.execute("""
            SELECT id, check_in, check_out, clock_in_time, clock_out_time
            FROM attendance
            WHERE employee_id = ? AND work_date = ?
        """, (employee_id, date))
        attendance = cursor.fetchone()
        
        if not attendance:
            logging.info(f"員工 {employee_id} 在 {date} 沒有考勤記錄")
            return None
        
        # 解析班別時間
        shift_start = datetime.strptime(f"{date} {shift['start_time']}", "%Y-%m-%d %H:%M").time()
        shift_end = datetime.strptime(f"{date} {shift['end_time']}", "%Y-%m-%d %H:%M").time()
        
        # 解析實際打卡時間
        actual_check_in = None
        actual_check_out = None
        
        if attendance['clock_in_time']:
            actual_check_in = datetime.fromisoformat(attendance['clock_in_time'].replace('Z', '+00:00')).time()
        elif attendance['check_in']:
            actual_check_in = datetime.fromisoformat(attendance['check_in'].replace('Z', '+00:00')).time()
            
        if attendance['clock_out_time']:
            actual_check_out = datetime.fromisoformat(attendance['clock_out_time'].replace('Z', '+00:00')).time()
        elif attendance['check_out']:
            actual_check_out = datetime.fromisoformat(attendance['check_out'].replace('Z', '+00:00')).time()
        
        # 計算遲到、早退、加班時間
        late_minutes = 0
        early_leave_minutes = 0
        overtime_minutes = 0
        work_hours = 0
        status = "normal"
        
        if actual_check_in and actual_check_out:
            # 計算遲到時間
            if actual_check_in > shift_start:
                late_delta = datetime.combine(datetime.today(), actual_check_in) - datetime.combine(datetime.today(), shift_start)
                late_minutes = int(late_delta.total_seconds() / 60)
                status = "late"
            
            # 計算早退時間
            if actual_check_out < shift_end:
                early_delta = datetime.combine(datetime.today(), shift_end) - datetime.combine(datetime.today(), actual_check_out)
                early_leave_minutes = int(early_delta.total_seconds() / 60)
                if status == "normal":
                    status = "early_leave"
            
            # 計算工作時數
            work_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), actual_check_in)
            work_hours = work_delta.total_seconds() / 3600
            
            # 計算加班時間（下班後）
            if actual_check_out > shift_end:
                overtime_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), shift_end)
                overtime_minutes = int(overtime_delta.total_seconds() / 60)
        
        elif not actual_check_in and not actual_check_out:
            status = "absent"
        
        # 更新考勤記錄
        cursor.execute("""
            UPDATE attendance 
            SET shift_id = ?, 
                status = ?, 
                late_minutes = ?, 
                early_leave_minutes = ?, 
                overtime_minutes = ?, 
                work_hours = ?,
                overtime_hours = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (shift_id, status, late_minutes, early_leave_minutes, 
              overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
        
        logging.info(f"重新計算考勤記錄 - 員工ID: {employee_id}, 日期: {date}, 狀態: {status}, 遲到: {late_minutes}分鐘, 早退: {early_leave_minutes}分鐘, 加班: {overtime_minutes}分鐘")
        
        return {
            "attendance_id": attendance['id'],
            "shift_name": shift['name'],
            "status": status,
            "late_minutes": late_minutes,
            "early_leave_minutes": early_leave_minutes,
            "overtime_minutes": overtime_minutes,
            "work_hours": round(work_hours, 2),
            "check_in_time": attendance['clock_in_time'] or attendance['check_in'],
            "check_out_time": attendance['clock_out_time'] or attendance['check_out']
        }
        
    except Exception as e:
        logging.error(f"重新計算考勤記錄失敗: {str(e)}")
        return None


@shift_bp.route("/api/schedules/recalculate", methods=["POST"])
def recalculate_attendance():
    """
    重新計算指定日期範圍的考勤記錄
    
    請求體：
    {
        "employee_id": 員工ID (可選),
        "start_date": "開始日期 (YYYY-MM-DD)",
        "end_date": "結束日期 (YYYY-MM-DD)"
    }
    
    返回：
    - 重新計算的結果統計
    """
    try:
        data = request.get_json()
        employee_id = data.get('employee_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if not start_date or not end_date:
            return jsonify({"error": "請提供開始日期和結束日期"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = ["s.shift_date BETWEEN ? AND ?"]
        params = [start_date, end_date]
        
        if employee_id:
            where_conditions.append("s.employee_id = ?")
            params.append(employee_id)
        
        # 獲取需要重新計算的排班記錄
        cursor.execute(f"""
            SELECT s.employee_id, s.shift_date, s.shift_id, e.name as employee_name
            FROM schedules s
            JOIN employees e ON s.employee_id = e.id
            WHERE {' AND '.join(where_conditions)}
            ORDER BY s.employee_id, s.shift_date
        """, params)
        
        schedules = cursor.fetchall()
        
        recalculated_count = 0
        results = []
        
        for schedule in schedules:
            result = recalculate_attendance_for_date(
                schedule['employee_id'], 
                schedule['shift_date'], 
                schedule['shift_id'], 
                cursor
            )
            
            if result:
                recalculated_count += 1
                results.append({
                    "employee_name": schedule['employee_name'],
                    "date": schedule['shift_date'],
                    "result": result
                })
        
        conn.commit()
        conn.close()
        
        return jsonify({
            "success": True,
            "message": f"成功重新計算 {recalculated_count} 筆考勤記錄",
            "recalculated_count": recalculated_count,
            "total_schedules": len(schedules),
            "results": results
        })
        
    except Exception as e:
        logging.error(f"重新計算考勤記錄失敗: {str(e)}")
        return jsonify({"error": f"重新計算考勤記錄失敗: {str(e)}"}), 500


# 這裡之後會添加其他班表相關的API
# 例如：班表模板管理、排班規則設定等 