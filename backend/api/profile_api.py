#!/usr/bin/env python3
"""
個人資料管理API
處理員工個人資料更新、照片上傳等功能
"""

from flask import Blueprint, request, jsonify, session
import sqlite3
import os
import uuid
from werkzeug.utils import secure_filename
from datetime import datetime
import logging
import hashlib

# 創建Blueprint
profile_bp = Blueprint('profile', __name__)

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
UPLOAD_FOLDER = 'static/uploads/avatars'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def allowed_file(filename):
    """檢查文件是否允許上傳"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_db_connection():
    """獲取資料庫連接"""
    conn = sqlite3.connect('../attendance.db')
    conn.row_factory = sqlite3.Row
    return conn

@profile_bp.route('/api/profile/<employee_id>', methods=['GET'])
def get_profile(employee_id):
    """
    獲取員工個人資料
    
    參數：
    employee_id (str): 員工編號
    
    返回：
    dict: 包含員工個人資料的字典
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查詢員工基本資料
        cursor.execute("""
            SELECT e.employee_id, e.name, e.email, e.phone, 
                   d.name as department, e.position,
                   e.photo_url
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE e.employee_id = ?
        """, (employee_id,))
        
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({
                'success': False,
                'message': '找不到該員工'
            }), 404
        
        profile_data = {
            'employee_id': employee['employee_id'],
            'name': employee['name'],
            'email': employee['email'] or '',
            'phone': employee['phone'] or '',
            'department': employee['department'] or '',
            'position': employee['position'] or '',
            'avatar': employee['photo_url'] or ''
        }
        
        return jsonify({
            'success': True,
            'profile': profile_data
        })
        
    except Exception as e:
        logger.error(f"獲取個人資料失敗: {str(e)}")
        return jsonify({
            'success': False,
            'message': '獲取個人資料失敗'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()

@profile_bp.route('/api/profile/<employee_id>', methods=['PUT'])
def update_profile(employee_id):
    """
    更新員工個人資料
    
    參數：
    employee_id (str): 員工編號
    
    請求體：
    {
        "name": "姓名",
        "email": "電子郵件",
        "phone": "電話"
    }
    
    返回：
    dict: 更新結果
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '請提供更新資料'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 檢查員工是否存在
        cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({
                'success': False,
                'message': '找不到該員工'
            }), 404
        
        # 更新員工資料
        update_fields = []
        update_values = []
        
        if 'name' in data:
            update_fields.append('name = ?')
            update_values.append(data['name'])
        
        if 'email' in data:
            update_fields.append('email = ?')
            update_values.append(data['email'])
        
        if 'phone' in data:
            update_fields.append('phone = ?')
            update_values.append(data['phone'])
        
        if update_fields:
            update_values.append(employee_id)
            sql = f"UPDATE employees SET {', '.join(update_fields)} WHERE employee_id = ?"
            cursor.execute(sql, update_values)
            conn.commit()
        
        return jsonify({
            'success': True,
            'message': '個人資料更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新個人資料失敗: {str(e)}")
        return jsonify({
            'success': False,
            'message': '更新個人資料失敗'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()

@profile_bp.route('/api/auth/change-password', methods=['POST'])
def change_password():
    """
    修改密碼
    
    請求體：
    {
        "employee_id": "員工編號",
        "current_password": "目前密碼",
        "new_password": "新密碼"
    }
    
    返回：
    dict: 修改結果
    """
    try:
        data = request.get_json()
        
        if not data or not all(k in data for k in ['employee_id', 'current_password', 'new_password']):
            return jsonify({
                'success': False,
                'message': '請提供完整的密碼資料'
            }), 400
        
        employee_id = data['employee_id']
        current_password = data['current_password']
        new_password = data['new_password']
        
        # 密碼長度檢查
        if len(new_password) < 6:
            return jsonify({
                'success': False,
                'message': '新密碼長度至少需要6個字元'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 驗證目前密碼
        cursor.execute("SELECT password FROM employees WHERE employee_id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({
                'success': False,
                'message': '找不到該員工'
            }), 404
        
        # 檢查目前密碼是否正確
        current_password_hash = hashlib.md5(current_password.encode()).hexdigest()
        if employee['password'] != current_password_hash:
            return jsonify({
                'success': False,
                'message': '目前密碼不正確'
            }), 400
        
        # 更新密碼
        new_password_hash = hashlib.md5(new_password.encode()).hexdigest()
        cursor.execute(
            "UPDATE employees SET password = ? WHERE employee_id = ?",
            (new_password_hash, employee_id)
        )
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '密碼修改成功'
        })
        
    except Exception as e:
        logger.error(f"修改密碼失敗: {str(e)}")
        return jsonify({
            'success': False,
            'message': '修改密碼失敗'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()

@profile_bp.route('/api/profile/avatar', methods=['POST'])
def upload_avatar():
    """
    上傳頭像
    
    表單資料：
    - avatar: 頭像檔案
    - employee_id: 員工編號
    
    返回：
    dict: 上傳結果
    """
    try:
        # 檢查是否有檔案
        if 'avatar' not in request.files:
            return jsonify({
                'success': False,
                'message': '請選擇頭像檔案'
            }), 400
        
        file = request.files['avatar']
        employee_id = request.form.get('employee_id')
        
        if not employee_id:
            return jsonify({
                'success': False,
                'message': '請提供員工編號'
            }), 400
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '請選擇頭像檔案'
            }), 400
        
        # 檢查檔案大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'message': '檔案大小不能超過5MB'
            }), 400
        
        # 檢查檔案類型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': '不支援的檔案格式，請使用 PNG、JPG、JPEG 或 GIF'
            }), 400
        
        # 確保上傳目錄存在
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        
        # 生成唯一檔名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{employee_id}_{uuid.uuid4().hex[:8]}.{file_extension}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
        
        # 儲存檔案
        file.save(file_path)
        
        # 更新資料庫
        avatar_url = f"/static/uploads/avatars/{unique_filename}"
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 檢查員工是否存在
        cursor.execute("SELECT id, photo_url FROM employees WHERE employee_id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            # 刪除已上傳的檔案
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({
                'success': False,
                'message': '找不到該員工'
            }), 404
        
        # 刪除舊頭像檔案
        if employee['photo_url']:
            old_file_path = employee['photo_url'].replace('/static/', 'static/')
            if os.path.exists(old_file_path):
                try:
                    os.remove(old_file_path)
                except:
                    pass  # 忽略刪除舊檔案的錯誤
        
        # 更新頭像URL
        cursor.execute(
            "UPDATE employees SET photo_url = ? WHERE employee_id = ?",
            (avatar_url, employee_id)
        )
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '頭像上傳成功',
            'avatar_url': avatar_url
        })
        
    except Exception as e:
        logger.error(f"上傳頭像失敗: {str(e)}")
        return jsonify({
            'success': False,
            'message': '上傳頭像失敗'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()

@profile_bp.route('/api/profile/avatar/<employee_id>', methods=['DELETE'])
def delete_avatar(employee_id):
    """
    刪除頭像
    
    參數：
    employee_id (str): 員工編號
    
    返回：
    dict: 刪除結果
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 獲取目前頭像URL
        cursor.execute("SELECT photo_url FROM employees WHERE employee_id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({
                'success': False,
                'message': '找不到該員工'
            }), 404
        
        # 刪除檔案
        if employee['photo_url']:
            file_path = employee['photo_url'].replace('/static/', 'static/')
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass  # 忽略刪除檔案的錯誤
        
        # 更新資料庫
        cursor.execute(
            "UPDATE employees SET photo_url = NULL WHERE employee_id = ?",
            (employee_id,)
        )
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '頭像刪除成功'
        })
        
    except Exception as e:
        logger.error(f"刪除頭像失敗: {str(e)}")
        return jsonify({
            'success': False,
            'message': '刪除頭像失敗'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close() 