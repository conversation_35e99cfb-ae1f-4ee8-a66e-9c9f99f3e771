#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新員工班表分配腳本
讓員工使用多樣化的班別，而不是全部使用正常班
"""

import sqlite3
import random
from datetime import datetime

def create_connection():
    """創建資料庫連接"""
    return sqlite3.connect('attendance.db')

def update_employee_shifts():
    """
    更新員工班表分配
    根據不同的業務需求分配班表：
    - 標準日班：60% (大部分辦公室員工)
    - 早班：15% (早班服務人員)
    - 晚班：10% (晚班服務人員)
    - 夜班：5% (夜班保全、維護人員)
    - 彈性班：8% (研發、管理人員)
    - 半日班：2% (兼職人員)
    """
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 獲取所有員工
        cursor.execute("SELECT id, name, position FROM employees ORDER BY id")
        employees = cursor.fetchall()
        
        print(f"開始更新 {len(employees)} 位員工的班表分配...")
        
        # 定義班表分配權重
        shift_weights = [
            (1, "標準日班", 60),  # 60%
            (2, "早班", 15),      # 15%
            (3, "晚班", 10),      # 10%
            (4, "夜班", 5),       # 5%
            (5, "彈性班", 8),     # 8%
            (6, "半日班", 2)      # 2%
        ]
        
        # 創建加權選擇列表
        weighted_shifts = []
        for shift_id, shift_name, weight in shift_weights:
            weighted_shifts.extend([shift_id] * weight)
        
        # 特殊職位的班表分配規則
        special_positions = {
            "主管": [1, 5],      # 主管通常是標準日班或彈性班
            "經理": [1, 5],      # 經理通常是標準日班或彈性班
            "總監": [1, 5],      # 總監通常是標準日班或彈性班
            "保全": [4],         # 保全通常是夜班
            "清潔": [2, 4],      # 清潔人員早班或夜班
            "維護": [2, 3, 4],   # 維護人員各班次
            "客服": [1, 2, 3],   # 客服人員日班、早班、晚班
        }
        
        update_count = 0
        shift_distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
        
        for emp_id, name, position in employees:
            # 根據職位決定班表
            shift_id = None
            
            # 檢查是否有特殊職位規則
            for pos_keyword, allowed_shifts in special_positions.items():
                if pos_keyword in position:
                    shift_id = random.choice(allowed_shifts)
                    break
            
            # 如果沒有特殊規則，使用加權隨機選擇
            if shift_id is None:
                shift_id = random.choice(weighted_shifts)
            
            # 更新員工班表
            cursor.execute(
                "UPDATE employees SET shift_type = ? WHERE id = ?",
                (shift_id, emp_id)
            )
            
            shift_distribution[shift_id] += 1
            update_count += 1
            
            # 獲取班表名稱用於顯示
            shift_name = next(name for sid, name, _ in shift_weights if sid == shift_id)
            print(f"  {name} ({position}) -> {shift_name}")
        
        conn.commit()
        
        print(f"\n✅ 成功更新 {update_count} 位員工的班表分配")
        print("\n📊 班表分配統計：")
        
        # 顯示分配統計
        cursor.execute("""
            SELECT s.name, COUNT(e.id) as count, 
                   ROUND(COUNT(e.id) * 100.0 / (SELECT COUNT(*) FROM employees), 1) as percentage
            FROM shifts s
            LEFT JOIN employees e ON s.id = e.shift_type
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            ORDER BY count DESC
        """)
        
        for shift_name, count, percentage in cursor.fetchall():
            print(f"  {shift_name}: {count} 人 ({percentage}%)")
        
        print(f"\n🕐 更新時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 更新失敗: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔄 員工班表分配更新工具")
    print("=" * 50)
    update_employee_shifts()
    print("=" * 50)
    print("✨ 更新完成！")