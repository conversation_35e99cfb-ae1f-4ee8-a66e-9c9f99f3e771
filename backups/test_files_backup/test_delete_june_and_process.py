#!/usr/bin/env python3
"""
刪除6月考勤記錄並執行整理動作測試腳本。

此腳本將：
1. 檢查現有6月考勤記錄
2. 刪除6月考勤記錄
3. 執行增量考勤整理
4. 驗證結果
"""

import sqlite3
import sys
from datetime import datetime, date
from services.enhanced_attendance_processor import EnhancedAttendanceProcessor

def create_connection():
    """
    創建與 SQLite 資料庫的連接。
    
    返回：
    sqlite3.Connection: 資料庫連接物件
    """
    try:
        conn = sqlite3.connect("attendance.db")
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
    except Exception as e:
        print(f"資料庫連接錯誤: {e}")
        return None

def check_june_records():
    """
    檢查6月考勤記錄。
    
    返回：
    dict: 6月記錄統計
    """
    conn = create_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        
        # 檢查6月記錄
        cursor.execute("""
            SELECT COUNT(*) as total_records, 
                   MIN(work_date) as earliest_date, 
                   MAX(work_date) as latest_date
            FROM attendance 
            WHERE work_date LIKE '2024-06%'
        """)
        
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            return {
                'total_records': result[0],
                'earliest_date': result[1],
                'latest_date': result[2]
            }
        else:
            return {'total_records': 0}
            
    except Exception as e:
        print(f"檢查6月記錄錯誤: {e}")
        return None
    finally:
        conn.close()

def delete_june_records():
    """
    刪除6月考勤記錄。
    
    返回：
    int: 刪除的記錄數量
    """
    conn = create_connection()
    if not conn:
        return 0
    
    try:
        cursor = conn.cursor()
        
        # 刪除6月記錄
        cursor.execute("DELETE FROM attendance WHERE work_date LIKE '2024-06%'")
        deleted_count = cursor.rowcount
        
        conn.commit()
        return deleted_count
        
    except Exception as e:
        print(f"刪除6月記錄錯誤: {e}")
        return 0
    finally:
        conn.close()

def check_current_status():
    """
    檢查當前考勤處理狀態。
    
    返回：
    dict: 當前狀態資訊
    """
    processor = EnhancedAttendanceProcessor()
    
    # 獲取上次處理日期
    last_processed = processor.get_last_processed_date()
    
    # 獲取需要處理的日期
    dates_to_process = processor.get_dates_to_process()
    
    return {
        'last_processed_date': last_processed,
        'dates_to_process': dates_to_process,
        'total_days_to_process': len(dates_to_process)
    }

def execute_batch_processing():
    """
    執行批量考勤處理。
    
    返回：
    dict: 處理結果
    """
    processor = EnhancedAttendanceProcessor()
    
    try:
        result = processor.process_attendance_batch()
        return result
    except Exception as e:
        print(f"批量處理錯誤: {e}")
        return None

def main():
    """
    主函數：執行完整的測試流程。
    """
    print("=" * 60)
    print("🧪 刪除6月考勤記錄並執行整理動作測試")
    print("=" * 60)
    
    # 步驟1：檢查現有6月記錄
    print("\n📊 步驟1：檢查現有6月考勤記錄")
    print("-" * 40)
    
    june_records = check_june_records()
    if june_records is None:
        print("❌ 無法檢查6月記錄")
        return
    
    if june_records['total_records'] == 0:
        print("ℹ️  目前沒有6月考勤記錄")
    else:
        print(f"📋 找到 {june_records['total_records']} 筆6月考勤記錄")
        print(f"📅 日期範圍：{june_records['earliest_date']} ~ {june_records['latest_date']}")
    
    # 步驟2：刪除6月記錄（如果存在）
    if june_records['total_records'] > 0:
        print("\n🗑️  步驟2：刪除6月考勤記錄")
        print("-" * 40)
        
        deleted_count = delete_june_records()
        if deleted_count > 0:
            print(f"✅ 成功刪除 {deleted_count} 筆6月考勤記錄")
        else:
            print("❌ 刪除6月記錄失敗")
            return
    else:
        print("\n⏭️  步驟2：跳過刪除（沒有6月記錄）")
    
    # 步驟3：檢查處理前狀態
    print("\n🔍 步驟3：檢查處理前狀態")
    print("-" * 40)
    
    before_status = check_current_status()
    print(f"📅 上次處理日期：{before_status['last_processed_date'] or '無記錄'}")
    print(f"📊 需要處理天數：{before_status['total_days_to_process']} 天")
    
    if before_status['total_days_to_process'] > 0:
        print(f"📋 需要處理的日期範圍：")
        print(f"   從：{before_status['dates_to_process'][0]}")
        print(f"   到：{before_status['dates_to_process'][-1]}")
    
    # 步驟4：執行批量處理
    print("\n⚙️  步驟4：執行批量考勤處理")
    print("-" * 40)
    
    if before_status['total_days_to_process'] == 0:
        print("ℹ️  沒有需要處理的日期，跳過批量處理")
    else:
        print("🚀 開始執行批量處理...")
        
        processing_result = execute_batch_processing()
        
        if processing_result:
            print(f"✅ 批量處理完成！")
            print(f"📊 處理統計：")
            print(f"   成功處理天數：{processing_result.get('successful_days', 0)}")
            print(f"   失敗天數：{processing_result.get('failed_days', 0)}")
            print(f"   總處理員工數：{processing_result.get('total_employees_processed', 0)}")
            print(f"   總處理記錄數：{processing_result.get('total_records_processed', 0)}")
            
            # 顯示每日處理詳情
            daily_results = processing_result.get('daily_results', [])
            if daily_results:
                print(f"\n📋 每日處理詳情：")
                for i, daily in enumerate(daily_results[:5], 1):  # 只顯示前5天
                    status = "✅" if daily.get('success', False) else "❌"
                    print(f"   {i}. {daily.get('date')} {status} - "
                          f"員工數：{daily.get('employees_processed', 0)}, "
                          f"記錄數：{daily.get('records_processed', 0)}")
                
                if len(daily_results) > 5:
                    print(f"   ... 還有 {len(daily_results) - 5} 天的處理結果")
        else:
            print("❌ 批量處理失敗")
    
    # 步驟5：檢查處理後狀態
    print("\n🔍 步驟5：檢查處理後狀態")
    print("-" * 40)
    
    after_status = check_current_status()
    print(f"📅 處理後上次處理日期：{after_status['last_processed_date'] or '無記錄'}")
    print(f"📊 處理後需要處理天數：{after_status['total_days_to_process']} 天")
    
    # 步驟6：驗證結果
    print("\n✅ 步驟6：驗證測試結果")
    print("-" * 40)
    
    if before_status['total_days_to_process'] > 0:
        if after_status['total_days_to_process'] == 0:
            print("🎉 測試成功！增量處理功能正常工作")
            print("✅ 系統能夠正確檢測需要處理的日期範圍")
            print("✅ 系統能夠按順序處理考勤記錄")
            print("✅ 處理完成後狀態更新正確")
        else:
            print("⚠️  測試部分成功，但仍有未處理的日期")
            print(f"   剩餘未處理天數：{after_status['total_days_to_process']}")
    else:
        print("ℹ️  測試完成，但沒有需要處理的日期範圍")
    
    print("\n" + "=" * 60)
    print("🏁 測試完成")
    print("=" * 60)

if __name__ == "__main__":
    main() 