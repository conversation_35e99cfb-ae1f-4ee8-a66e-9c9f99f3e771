#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
考勤管理系統 - 重構版主應用程式

這是重構後的主應用程式文件，採用模組化架構：
- API按功能分類到不同模組
- 主應用程式只負責基本配置和路由註冊
- 提高代碼可維護性和可讀性
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, jsonify
from flask_cors import CORS

# 導入配置和資料庫
from config import Config
from database import init_db

# 導入API模組
from api import register_api_blueprints

# ====== 應用程式初始化 ======
app = Flask(__name__)
app.config.from_object(Config)

# 啟用CORS
CORS(app)

# ====== 日誌配置 ======
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ====== 註冊API藍圖 ======
register_api_blueprints(app)

# ====== 頁面路由 ======
@app.route("/")
def index():
    return render_template("index.html")

@app.route("/elite")
def elite_dashboard():
    return render_template("elite-dashboard.html")

@app.route("/elite/attendance")
def elite_attendance():
    return render_template("elite-attendance.html")

@app.route("/elite/attendance-management")
def elite_attendance_management():
    return render_template("elite-attendance-management.html")

@app.route("/elite/shifts")
def elite_shifts():
    return render_template("elite-shifts.html")

@app.route("/elite/employees")
def elite_employees():
    return render_template("elite-employees.html")

@app.route("/elite/leaves")
def elite_leaves():
    return render_template("elite-leaves.html")

@app.route("/elite/analytics")
def elite_analytics():
    return render_template("elite-analytics.html")

@app.route("/elite/settings")
def elite_settings():
    return render_template("elite-settings.html")

# ====== 健康檢查 ======
@app.route("/health")
def health_check():
    """
    系統健康檢查
    """
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0-refactored"
    })

# ====== 錯誤處理 ======
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "頁面不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "內部伺服器錯誤"}), 500

# ====== 應用程式啟動 ======
if __name__ == "__main__":
    try:
        # 初始化資料庫
        init_db()
        logger.info("資料庫初始化成功")
        
        # 啟動應用程式
        logger.info("應用程式啟動於 http://0.0.0.0:7072")
        app.run(host="0.0.0.0", port=7072, debug=False)
        
    except Exception as e:
        logger.error(f"應用程式啟動失敗: {e}")
        raise 