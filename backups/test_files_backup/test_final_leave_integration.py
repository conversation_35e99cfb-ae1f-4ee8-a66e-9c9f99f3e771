#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終請假整合測試腳本

功能：
1. 清理重複記錄
2. 重新執行考勤整理
3. 驗證請假記錄整合
4. 生成最終測試報告
"""

import requests
import sqlite3
import subprocess
import sys
from datetime import datetime
from database import create_connection

# API基礎URL
BASE_URL = "http://127.0.0.1:7072"

def cleanup_duplicates():
    """
    清理重複記錄
    """
    print("🧹 步驟1：清理重複記錄")
    print("-" * 50)
    
    try:
        result = subprocess.run([sys.executable, "cleanup_duplicate_records.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 重複記錄清理成功")
            return True
        else:
            print("❌ 重複記錄清理失敗")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 清理重複記錄時發生錯誤: {str(e)}")
        return False

def force_regenerate_attendance():
    """
    強制重新生成考勤記錄
    """
    print("\n🔄 步驟2：強制重新生成考勤記錄")
    print("-" * 50)
    
    try:
        # 強制重新生成六月1-5日的考勤記錄
        for date in ["2025-06-01", "2025-06-02", "2025-06-03", "2025-06-04", "2025-06-05"]:
            response = requests.post(
                f"{BASE_URL}/api/attendance/management/generate-complete",
                json={
                    "target_date": date,
                    "department_ids": [],
                    "force_regenerate": True
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {date} - 新增: {data['generation_results']['generated_count']}, 更新: {data['generation_results']['updated_count']}")
            else:
                print(f"❌ {date} - 生成失敗: {response.text}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 強制重新生成考勤記錄時發生錯誤: {str(e)}")
        return False

def verify_leave_integration():
    """
    驗證請假記錄整合
    """
    print("\n🔍 步驟3：驗證請假記錄整合")
    print("-" * 50)
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 查詢有請假記錄的考勤資料
        cursor.execute("""
            SELECT 
                ar.id, ar.employee_id, ar.work_date, ar.status, 
                ar.leave_hours, ar.note,
                lr.hours as leave_request_hours, lr.reason as leave_reason,
                lt.name as leave_type_name,
                e.name as employee_name, e.employee_id as employee_code
            FROM attendance_records ar
            LEFT JOIN leave_requests lr ON ar.employee_id = lr.employee_id 
                AND ar.work_date = lr.start_date
            LEFT JOIN leave_types lt ON lr.leave_type = lt.id
            LEFT JOIN employees e ON ar.employee_id = e.id
            WHERE ar.work_date BETWEEN '2025-06-01' AND '2025-06-05'
            AND lr.id IS NOT NULL
            ORDER BY ar.work_date, ar.employee_id
        """)
        
        records = cursor.fetchall()
        conn.close()
        
        if not records:
            print("❌ 沒有找到有請假記錄的考勤資料")
            return False
        
        print(f"✅ 找到 {len(records)} 筆有請假記錄的考勤資料")
        
        correct_leave_hours = 0
        correct_status = 0
        total_records = len(records)
        
        for record in records:
            ar_id, emp_id, work_date, status, leave_hours, note, req_hours, reason, leave_type, emp_name, emp_code = record
            
            print(f"\n📋 考勤記錄 {ar_id} - {emp_name} ({emp_code}) - {work_date}")
            print(f"   考勤狀態: {status}")
            print(f"   考勤請假時數: {leave_hours}")
            print(f"   請假申請時數: {req_hours}")
            print(f"   請假類型: {leave_type}")
            print(f"   備註: {note}")
            
            # 檢查請假時數是否正確
            if abs(float(leave_hours or 0) - float(req_hours or 0)) < 0.1:
                print("   ✅ 請假時數正確")
                correct_leave_hours += 1
            else:
                print(f"   ❌ 請假時數不正確 (應為 {req_hours})")
            
            # 檢查考勤狀態是否正確
            expected_status = None
            if req_hours >= 8:
                expected_status = "leave_full_day"
            elif req_hours > 0:
                expected_status = "leave_partial_absent"
            
            if status == expected_status:
                print("   ✅ 考勤狀態正確")
                correct_status += 1
            else:
                print(f"   ❌ 考勤狀態不正確 (應為 {expected_status})")
        
        print(f"\n📊 整合檢查結果：")
        print(f"   請假時數正確: {correct_leave_hours}/{total_records} ({correct_leave_hours/total_records*100:.1f}%)")
        print(f"   考勤狀態正確: {correct_status}/{total_records} ({correct_status/total_records*100:.1f}%)")
        
        return {
            "total_records": total_records,
            "correct_leave_hours": correct_leave_hours,
            "correct_status": correct_status,
            "leave_hours_rate": correct_leave_hours/total_records*100 if total_records > 0 else 0,
            "status_rate": correct_status/total_records*100 if total_records > 0 else 0
        }
        
    except Exception as e:
        print(f"❌ 驗證請假記錄整合時發生錯誤: {str(e)}")
        return False

def check_duplicate_records():
    """
    檢查是否還有重複記錄
    """
    print("\n🔍 步驟4：檢查重複記錄")
    print("-" * 50)
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT employee_id, work_date, COUNT(*) as count
            FROM attendance_records
            WHERE work_date BETWEEN '2025-06-01' AND '2025-06-05'
            GROUP BY employee_id, work_date
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        conn.close()
        
        if duplicates:
            print(f"❌ 發現 {len(duplicates)} 組重複記錄")
            for emp_id, work_date, count in duplicates[:5]:  # 只顯示前5組
                print(f"   員工 {emp_id} - {work_date}: {count} 筆記錄")
            return False
        else:
            print("✅ 沒有發現重複記錄")
            return True
            
    except Exception as e:
        print(f"❌ 檢查重複記錄時發生錯誤: {str(e)}")
        return False

def generate_final_report(verification_result):
    """
    生成最終測試報告
    """
    print("\n📊 最終測試報告")
    print("=" * 60)
    
    if verification_result:
        print(f"✅ 請假記錄整合測試完成")
        print(f"📈 測試結果統計：")
        print(f"   總考勤記錄: {verification_result['total_records']}")
        print(f"   請假時數正確率: {verification_result['leave_hours_rate']:.1f}%")
        print(f"   考勤狀態正確率: {verification_result['status_rate']:.1f}%")
        
        if verification_result['leave_hours_rate'] >= 90 and verification_result['status_rate'] >= 90:
            print("\n🎉 測試結果：優秀 (≥90%)")
            return "優秀"
        elif verification_result['leave_hours_rate'] >= 70 and verification_result['status_rate'] >= 70:
            print("\n👍 測試結果：良好 (≥70%)")
            return "良好"
        else:
            print("\n⚠️ 測試結果：需要改進 (<70%)")
            return "需要改進"
    else:
        print("❌ 請假記錄整合測試失敗")
        return "失敗"

def main():
    """
    主函數
    """
    print("🚀 最終請假整合測試")
    print("=" * 60)
    
    # 步驟1：清理重複記錄
    if not cleanup_duplicates():
        print("\n❌ 測試失敗：無法清理重複記錄")
        return
    
    # 步驟2：強制重新生成考勤記錄
    if not force_regenerate_attendance():
        print("\n❌ 測試失敗：無法重新生成考勤記錄")
        return
    
    # 步驟3：驗證請假記錄整合
    verification_result = verify_leave_integration()
    if not verification_result:
        print("\n❌ 測試失敗：請假記錄整合驗證失敗")
        return
    
    # 步驟4：檢查重複記錄
    if not check_duplicate_records():
        print("\n⚠️ 警告：仍有重複記錄存在")
    
    # 生成最終報告
    final_result = generate_final_report(verification_result)
    
    print(f"\n🏁 測試完成 - 結果: {final_result}")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 