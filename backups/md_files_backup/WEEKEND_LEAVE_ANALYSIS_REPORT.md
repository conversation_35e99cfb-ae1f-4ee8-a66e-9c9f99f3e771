# 週末請假顯示問題分析報告

## 問題描述
用戶發現PDF報表中，某些假日（週末）記錄顯示有請假類型（如婚假、事假），但請假小時數為0，詢問是否因為請假日期本身就是假日。

## 問題分析

### 1. 劉志偉（E003）6月份請假記錄分析

**請假記錄詳情**：
```
喪假: 2025-06-02 (週一) - 8小時 - 已核准
婚假: 2025-06-08 (週日) 到 2025-06-10 (週二) - 24小時 - 已核准  
事假: 2025-06-08 (週日) 到 2025-06-09 (週一) - 16小時 - 已核准
年假: 2025-06-09 (週一) 到 2025-06-11 (週三) - 24小時 - 已拒絕
事假: 2025-06-15 (週日) 到 2025-06-17 (週二) - 24小時 - 已核准
```

### 2. 週末日期分析

**問題日期**：
- **6月8日（週日）**：同時有婚假和事假記錄
- **6月15日（週日）**：有事假記錄

**星期對照**（SQLite strftime '%w' 格式）：
- 0 = 週日
- 1 = 週一  
- 2 = 週二
- 3 = 週三
- 4 = 週四
- 5 = 週五
- 6 = 週六

### 3. 考勤記錄對照

**6月8日（週日）考勤記錄**：
```
work_date: 2025-06-08
weekday: 7 (週日)
date_type: weekend
check_in: NULL
check_out: NULL  
leave_hours: 0.0
status: weekend
```

**6月15日（週日）考勤記錄**：
```
work_date: 2025-06-15
weekday: 7 (週日)
date_type: weekend
check_in: NULL
check_out: NULL
leave_hours: 0.0  
status: weekend
```

## 根本原因

### 1. 跨日請假包含週末
員工申請的請假期間包含了週末日期：

- **婚假（6/8-6/10）**：包含週日6/8
- **事假（6/8-6/9）**：包含週日6/8  
- **事假（6/15-6/17）**：包含週日6/15

### 2. API查詢邏輯
在 `attendance_api.py` 中，查詢使用了 LEFT JOIN：

```sql
LEFT JOIN leaves l ON l.employee_id = a.employee_id 
    AND a.work_date BETWEEN l.start_date AND l.end_date
    AND l.status = 'approved'
```

這導致：
- 只要考勤日期在請假期間內，就會顯示請假類型
- 即使是週末日期，也會顯示請假類型
- 但週末的 leave_hours 為 0，因為週末本來就不計工時

### 3. PDF顯示邏輯
PDF中的顯示邏輯：
- **上班/下班時間**：週末顯示"假日"
- **請假類型**：顯示資料庫中匹配的請假類型
- **請假小時**：顯示考勤記錄中的 leave_hours（週末為0）

## 業務邏輯問題

### 1. 請假申請邏輯缺陷
- 系統允許員工申請包含週末的請假
- 沒有自動排除週末日期的機制
- 週末日期不應計入請假時數

### 2. 請假時數計算問題
- 跨日請假應該只計算工作日的時數
- 週末日期應該被排除在請假計算之外

## 解決方案建議

### 1. 短期解決方案：修改PDF顯示邏輯
在PDF生成時，對週末日期不顯示請假類型：

```python
# 在PDF生成邏輯中添加判斷
if record['date_type'] == 'weekend':
    leave_type_display = '-'  # 週末不顯示請假類型
else:
    leave_type_display = record.get('leave_type', '-')
```

### 2. 中期解決方案：修改API查詢邏輯
修改查詢條件，排除週末的請假顯示：

```sql
LEFT JOIN leaves l ON l.employee_id = a.employee_id 
    AND a.work_date BETWEEN l.start_date AND l.end_date
    AND l.status = 'approved'
    AND a.date_type = 'workday'  -- 只在工作日顯示請假
```

### 3. 長期解決方案：改善請假申請系統
1. **請假申請時自動排除週末**
2. **請假時數計算只計工作日**
3. **跨日請假自動分解為工作日**

## 測試驗證

### 驗證步驟
1. 檢查其他員工是否有類似問題
2. 確認所有跨週末請假記錄
3. 驗證請假時數計算邏輯

### 預期結果
- 週末日期不應顯示請假類型
- 請假時數應該只計算工作日
- PDF報表顯示邏輯一致

## 結論

**用戶的觀察是正確的**：
- 確實是因為請假日期包含了假日（週末）
- 系統邏輯上存在缺陷，允許週末計入請假期間
- PDF顯示了請假類型但請假小時為0，造成混淆

**建議優先處理**：
1. 修改PDF顯示邏輯，週末不顯示請假類型
2. 檢討請假申請和計算邏輯
3. 確保業務邏輯的一致性

---
**分析日期**: 2025-06-07  
**分析人員**: AI Assistant  
**問題狀態**: 已確認，待修復  
**優先級**: 中等 