[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smart-attendance-system"
version = "1.0.0"
description = "智慧考勤系統 - 現代化的企業考勤管理解決方案"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
maintainers = [
    {name = "Your Name", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: Flask",
    "Topic :: Office/Business",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
keywords = [
    "attendance",
    "hr",
    "management",
    "flask",
    "sqlite",
    "workforce"
]
dependencies = [
    "Flask>=3.1.0",
    "Flask-CORS>=5.0.1",
    "python-dotenv>=1.0.0",
    "blinker>=1.9.0",
    "itsdangerous>=2.2.0",
    "Jinja2>=3.1.6",
    "MarkupSafe>=3.0.2",
    "Werkzeug>=3.1.3",
    "click>=8.1.8",
]
requires-python = ">=3.8"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-flask>=1.2.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "bandit>=1.7.0",
]
production = [
    "gunicorn>=21.0.0",
    "psycopg2-binary>=2.9.0",  # 如果使用 PostgreSQL
]
docs = [
    "mkdocs>=1.4.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.20.0",
]

[project.urls]
Homepage = "https://github.com/your-username/smart-attendance-system"
Documentation = "https://your-username.github.io/smart-attendance-system"
Repository = "https://github.com/your-username/smart-attendance-system.git"
"Bug Tracker" = "https://github.com/your-username/smart-attendance-system/issues"
Changelog = "https://github.com/your-username/smart-attendance-system/blob/main/CHANGELOG.md"

[project.scripts]
attendance-system = "app:main"

[tool.setuptools.packages.find]
exclude = ["tests*"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "config", "database", "models", "services"]
known_third_party = ["flask", "sqlite3", "datetime"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# mypy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "flask_cors.*",
    "sqlite3.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    ".venv/*",
    "venv/*",
    "setup.py",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", ".venv", "venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_possible

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See .flake8 file for configuration