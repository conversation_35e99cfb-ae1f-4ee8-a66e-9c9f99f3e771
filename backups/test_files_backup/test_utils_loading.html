<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具函數庫載入測試</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">工具函數庫載入測試</h1>

        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">載入狀態</h2>
            <div id="loadingStatus" class="space-y-2">
                <div>檢查中...</div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">通知測試</h2>
            <div class="space-x-2">
                <button id="testSuccess" class="bg-green-500 text-white px-4 py-2 rounded">成功通知</button>
                <button id="testError" class="bg-red-500 text-white px-4 py-2 rounded">錯誤通知</button>
                <button id="testWarning" class="bg-yellow-500 text-white px-4 py-2 rounded">警告通知</button>
                <button id="testInfo" class="bg-blue-500 text-white px-4 py-2 rounded">資訊通知</button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">API測試</h2>
            <button id="testAPI" class="bg-purple-500 text-white px-4 py-2 rounded">測試請假審核API</button>
            <div id="apiResult" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <pre id="apiData"></pre>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            const statusDiv = document.getElementById('loadingStatus');

            // 檢查工具函數庫載入狀態
            function updateStatus() {
                const status = [];

                status.push(`UtilsLoader: ${window.UtilsLoader ? '✅' : '❌'}`);
                status.push(`NotificationSystem: ${window.NotificationSystem ? '✅' : '❌'}`);
                status.push(`LeaveCalculator: ${window.LeaveCalculator ? '✅' : '❌'}`);
                status.push(`FormValidator: ${window.FormValidator ? '✅' : '❌'}`);
                status.push(`AttendanceHelper: ${window.AttendanceHelper ? '✅' : '❌'}`);

                if (window.UtilsLoader) {
                    const loaded = UtilsLoader.getLoadedUtils();
                    status.push(`已載入工具: ${loaded.join(', ') || '無'}`);
                }

                statusDiv.innerHTML = status.map(s => `<div>${s}</div>`).join('');
            }

            // 初始檢查
            updateStatus();

            // 等待工具函數庫載入
            if (window.UtilsLoader) {
                try {
                    await UtilsLoader.initPageUtils('elite-approval');
                    console.log('工具函數庫初始化完成');
                } catch (error) {
                    console.error('工具函數庫初始化失敗:', error);
                }
                updateStatus();
            }

            // 綁定測試按鈕
            document.getElementById('testSuccess').addEventListener('click', function() {
                if (window.NotificationSystem) {
                    NotificationSystem.success('這是成功通知測試');
                } else {
                    alert('NotificationSystem 未載入');
                }
            });

            document.getElementById('testError').addEventListener('click', function() {
                if (window.NotificationSystem) {
                    NotificationSystem.error('這是錯誤通知測試');
                } else {
                    alert('NotificationSystem 未載入');
                }
            });

            document.getElementById('testWarning').addEventListener('click', function() {
                if (window.NotificationSystem) {
                    NotificationSystem.warning('這是警告通知測試');
                } else {
                    alert('NotificationSystem 未載入');
                }
            });

            document.getElementById('testInfo').addEventListener('click', function() {
                if (window.NotificationSystem) {
                    NotificationSystem.info('這是資訊通知測試');
                } else {
                    alert('NotificationSystem 未載入');
                }
            });

            document.getElementById('testAPI').addEventListener('click', async function() {
                try {
                    const response = await fetch('/api/approval/leaves');
                    const data = await response.json();

                    document.getElementById('apiResult').classList.remove('hidden');
                    document.getElementById('apiData').textContent = JSON.stringify(data, null, 2);

                    if (window.NotificationSystem) {
                        NotificationSystem.success(`API測試成功，獲取到 ${data.records?.length || 0} 筆記錄`);
                    }
                } catch (error) {
                    console.error('API測試失敗:', error);
                    if (window.NotificationSystem) {
                        NotificationSystem.error('API測試失敗: ' + error.message);
                    }
                }
            });

            // 創建圖標
            if (window.lucide) {
                lucide.createIcons();
            }
        });
    </script>
</body>

</html>