#!/usr/bin/env python3
"""
順序處理測試腳本。

驗證批量考勤處理確實按照日期順序逐天處理：
1. 測試日期列表的順序性
2. 驗證處理順序的正確性
3. 模擬從6月1號到6月10號的批量處理
"""

import sys
import logging
from datetime import datetime, timedelta
from services.enhanced_attendance_processor import enhanced_attendance_processor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_date_sequence_generation():
    """測試日期序列生成的順序性"""
    
    print("🧪 測試日期序列生成")
    print("-" * 50)
    
    try:
        # 模擬從特定日期開始的處理
        # 假設上次處理到 2025-06-01，現在要處理到 2025-06-10
        
        # 手動設置測試範圍
        start_date = datetime(2025, 6, 2)  # 從6月2號開始
        end_date = datetime(2025, 6, 10)   # 到6月10號結束
        
        # 生成日期列表
        dates_list = []
        current_date = start_date
        
        while current_date <= end_date:
            dates_list.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        print(f"📅 模擬處理日期範圍: {dates_list[0]} 到 {dates_list[-1]}")
        print(f"📊 總共需要處理: {len(dates_list)} 天")
        print(f"📋 日期順序:")
        
        for i, date in enumerate(dates_list, 1):
            print(f"   {i:2d}. {date}")
        
        # 驗證順序性
        is_sequential = True
        for i in range(1, len(dates_list)):
            prev_date = datetime.strptime(dates_list[i-1], '%Y-%m-%d')
            curr_date = datetime.strptime(dates_list[i], '%Y-%m-%d')
            
            if (curr_date - prev_date).days != 1:
                is_sequential = False
                print(f"❌ 日期順序錯誤: {dates_list[i-1]} -> {dates_list[i]}")
                break
        
        if is_sequential:
            print("✅ 日期順序正確，每天依序遞增")
        
        return dates_list, is_sequential
        
    except Exception as e:
        print(f"❌ 日期序列生成測試失敗: {e}")
        return [], False


def test_actual_processing_sequence():
    """測試實際處理順序"""
    
    print("\n🧪 測試實際處理順序")
    print("-" * 50)
    
    try:
        # 使用系統的日期生成功能
        dates_to_process = enhanced_attendance_processor.get_dates_to_process()
        
        if not dates_to_process:
            print("📭 目前沒有需要處理的日期")
            return True
        
        print(f"📅 系統檢測到需要處理的日期:")
        for i, date in enumerate(dates_to_process, 1):
            print(f"   {i:2d}. {date}")
        
        # 驗證日期順序
        is_sequential = True
        if len(dates_to_process) > 1:
            for i in range(1, len(dates_to_process)):
                prev_date = datetime.strptime(dates_to_process[i-1], '%Y-%m-%d')
                curr_date = datetime.strptime(dates_to_process[i], '%Y-%m-%d')
                
                if (curr_date - prev_date).days != 1:
                    is_sequential = False
                    print(f"❌ 日期順序錯誤: {dates_to_process[i-1]} -> {dates_to_process[i]}")
                    break
        
        if is_sequential:
            print("✅ 系統生成的日期順序正確")
        
        return is_sequential
        
    except Exception as e:
        print(f"❌ 實際處理順序測試失敗: {e}")
        return False


def test_sequential_batch_processing():
    """測試順序批量處理"""
    
    print("\n🧪 測試順序批量處理")
    print("-" * 50)
    
    try:
        # 模擬處理指定日期範圍
        test_dates = [
            '2025-06-01',
            '2025-06-02', 
            '2025-06-03'
        ]
        
        print(f"📅 模擬處理日期範圍: {test_dates[0]} 到 {test_dates[-1]}")
        print(f"🔄 開始順序處理...")
        
        processing_order = []
        total_processed = 0
        
        for i, date_str in enumerate(test_dates, 1):
            print(f"\n📅 第 {i} 步: 處理 {date_str}")
            
            try:
                # 記錄處理順序
                processing_order.append(date_str)
                
                # 執行單日處理
                result = enhanced_attendance_processor.process_daily_attendance(date_str)
                
                if result['success']:
                    processed_count = result.get('processed_count', 0)
                    created_count = result.get('created_count', 0)
                    updated_count = result.get('updated_count', 0)
                    
                    print(f"   ✅ 處理完成: 處理 {processed_count} 位員工, 新增 {created_count}, 更新 {updated_count}")
                    total_processed += processed_count
                else:
                    print(f"   ❌ 處理失敗: {result.get('error')}")
                
            except Exception as e:
                print(f"   ❌ 處理 {date_str} 時發生錯誤: {e}")
        
        print(f"\n📊 處理順序驗證:")
        print(f"   預期順序: {' -> '.join(test_dates)}")
        print(f"   實際順序: {' -> '.join(processing_order)}")
        print(f"   總處理員工: {total_processed}")
        
        # 驗證順序是否正確
        sequence_correct = processing_order == test_dates
        
        if sequence_correct:
            print("✅ 處理順序完全正確")
        else:
            print("❌ 處理順序有誤")
        
        return sequence_correct
        
    except Exception as e:
        print(f"❌ 順序批量處理測試失敗: {e}")
        return False


def test_batch_processing_with_logging():
    """測試批量處理並觀察日誌順序"""
    
    print("\n🧪 測試批量處理日誌順序")
    print("-" * 50)
    
    try:
        # 獲取需要處理的日期
        dates_to_process = enhanced_attendance_processor.get_dates_to_process()
        
        if not dates_to_process:
            print("📭 目前沒有需要處理的日期，創建測試範圍")
            # 創建測試日期範圍
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            dates_to_process = [yesterday]
        
        print(f"📅 將要處理的日期: {dates_to_process}")
        print(f"🔄 執行批量處理，觀察處理順序...")
        
        # 執行批量處理
        result = enhanced_attendance_processor.process_attendance_batch()
        
        if result['success']:
            print(f"\n✅ 批量處理完成")
            print(f"📊 處理統計:")
            print(f"   總天數: {result['total_days']}")
            print(f"   成功天數: {result['processed_days']}")
            print(f"   失敗天數: {result['failed_days']}")
            
            # 檢查每日處理結果的順序
            if result.get('daily_results'):
                print(f"\n📋 每日處理順序:")
                for i, daily in enumerate(result['daily_results'], 1):
                    status = "✅" if daily['success'] else "❌"
                    print(f"   {i:2d}. {status} {daily['date']}: 處理 {daily['processed_count']} 位員工")
                
                # 驗證處理順序
                processed_dates = [daily['date'] for daily in result['daily_results']]
                expected_dates = sorted(processed_dates)
                
                if processed_dates == expected_dates:
                    print("✅ 批量處理按照正確的日期順序執行")
                else:
                    print("❌ 批量處理順序有誤")
                    print(f"   預期: {expected_dates}")
                    print(f"   實際: {processed_dates}")
        else:
            print(f"❌ 批量處理失敗: {result.get('error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 批量處理日誌測試失敗: {e}")
        return False


def demonstrate_sequential_logic():
    """演示順序處理邏輯"""
    
    print("\n💡 順序處理邏輯演示")
    print("-" * 50)
    
    print("📝 批量處理的執行邏輯:")
    print("   1. 檢測上次整理日期（例如：2025-06-01）")
    print("   2. 生成需要處理的日期列表（例如：2025-06-02 到 2025-06-10）")
    print("   3. 按照日期順序逐天處理：")
    print("      - 第1天：處理 2025-06-02 的所有員工考勤")
    print("      - 第2天：處理 2025-06-03 的所有員工考勤")
    print("      - 第3天：處理 2025-06-04 的所有員工考勤")
    print("      - ...")
    print("      - 第9天：處理 2025-06-10 的所有員工考勤")
    print("   4. 統計處理結果並返回")
    
    print(f"\n🔧 關鍵程式碼片段:")
    print(f"```python")
    print(f"# 獲取按順序排列的日期列表")
    print(f"dates_to_process = self.get_dates_to_process(end_date)")
    print(f"")
    print(f"# 按順序逐天處理")
    print(f"for date_str in dates_to_process:")
    print(f"    self.logger.info(f'📅 處理日期: {{date_str}}')")
    print(f"    result = self.process_daily_attendance(date_str)")
    print(f"```")
    
    print(f"\n✅ 這確保了：")
    print(f"   - 資料處理的時間順序性")
    print(f"   - 每天的考勤記錄都完整建立")
    print(f"   - work_date 欄位按照正確的日期順序填入")
    print(f"   - 後續查詢上次整理日期時能得到正確結果")


def main():
    """主測試函數"""
    
    print("🔧 順序處理邏輯驗證測試")
    print("=" * 60)
    print("驗證批量考勤處理確實按照日期順序逐天執行")
    print()
    
    test_results = []
    
    # 1. 測試日期序列生成
    dates_list, sequence_ok = test_date_sequence_generation()
    test_results.append(sequence_ok)
    
    # 2. 測試實際處理順序
    actual_sequence_ok = test_actual_processing_sequence()
    test_results.append(actual_sequence_ok)
    
    # 3. 測試順序批量處理
    batch_sequence_ok = test_sequential_batch_processing()
    test_results.append(batch_sequence_ok)
    
    # 4. 測試批量處理日誌
    log_sequence_ok = test_batch_processing_with_logging()
    test_results.append(log_sequence_ok)
    
    # 5. 演示順序處理邏輯
    demonstrate_sequential_logic()
    
    # 統計結果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"🎯 測試結果統計")
    print(f"   通過測試: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！順序處理邏輯正確")
        print("✅ 批量處理確實按照日期順序逐天執行")
        print("✅ 從6月1號到6月10號會依序處理每一天")
        print("✅ 資料完整性得到保證")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查順序處理邏輯")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 