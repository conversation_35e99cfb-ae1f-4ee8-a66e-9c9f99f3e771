'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Clock, Calendar, User, FileText, Plus, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'

interface OvertimeType {
    id: number
    name: string
    code: string
}

export default function OvertimeApplyPage() {
    const router = useRouter()
    const { user, isAuthenticated, loading: authLoading } = useAuth()
    const [loading, setLoading] = useState(false)
    const [overtimeTypes, setOvertimeTypes] = useState<OvertimeType[]>([])
    const [formData, setFormData] = useState({
        overtime_date: new Date().toISOString().split('T')[0],
        start_time: '18:00',
        end_time: '20:00',
        overtime_type: '',
        reason: '',
        employee_id: 0
    })

    useEffect(() => {
        if (authLoading) return

        if (!isAuthenticated || !user) {
            router.push('/m/login')
            return
        }

        setFormData(prev => ({
            ...prev,
            employee_id: user.id || 0
        }))

        loadOvertimeTypes()
    }, [user, isAuthenticated, authLoading, router])

    // 🌐 獲取API基礎URL
    const getApiBaseUrl = () => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname
            console.log('🌐 加班申請頁面檢測到的主機:', hostname)

            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:7072'
            } else {
                const apiUrl = `http://${hostname}:7072`
                console.log('📱 加班申請頁面使用API URL:', apiUrl)
                return apiUrl
            }
        }
        return 'http://localhost:7072'
    }

    const loadOvertimeTypes = async () => {
        try {
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/overtime/types`
            console.log('⏰ 載入加班類型 URL:', url)

            const response = await fetch(url)
            if (response.ok) {
                const data = await response.json()
                console.log('加班類型數據:', data)
                setOvertimeTypes(data.records || [])
            } else {
                console.error('載入加班類型失敗，API返回錯誤')
                setOvertimeTypes([])
            }
        } catch (error) {
            console.error('載入加班類型失敗:', error)
            setOvertimeTypes([])
        }
    }

    const calculateHours = () => {
        if (!formData.start_time || !formData.end_time) return 0

        const start = new Date(`2000-01-01 ${formData.start_time}`)
        const end = new Date(`2000-01-01 ${formData.end_time}`)

        if (end <= start) {
            end.setDate(end.getDate() + 1) // 跨日處理
        }

        const diffMs = end.getTime() - start.getTime()
        return Math.round(diffMs / (1000 * 60 * 60) * 10) / 10 // 保留一位小數
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!formData.overtime_type || !formData.reason.trim()) {
            alert('請填寫所有必填欄位')
            return
        }

        setLoading(true)
        try {
            console.log('提交加班申請數據:', formData)

            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/overtime/requests`
            console.log('📤 提交加班申請 URL:', url)

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })

            console.log('API響應狀態:', response.status)

            if (response.ok) {
                const result = await response.json()
                console.log('API響應數據:', result)
                alert('加班申請提交成功！')
                router.push('/m')
            } else {
                const errorData = await response.text()
                console.error('API錯誤響應:', errorData)
                throw new Error(`提交失敗: ${response.status}`)
            }
        } catch (error) {
            console.error('提交加班申請失敗:', error)
            const errorMessage = error instanceof Error ? error.message : '未知錯誤'
            alert(`提交失敗：${errorMessage}`)
        } finally {
            setLoading(false)
        }
    }

    if (authLoading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-white">
                <div className="text-center">
                    <div className="w-16 h-16 border-4 border-orange-100 border-t-orange-600 rounded-full animate-spin mx-auto mb-6"></div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">載入中</h3>
                    <p className="text-gray-600">正在初始化系統...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-white">
            {/* 🔙 返回按鈕 */}
            <div className="fixed top-4 left-4 z-50">
                <Button
                    onClick={() => router.push('/m')}
                    className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl rounded-full px-4 py-2"
                >
                    <ArrowLeft className="w-4 h-4 text-gray-700" />
                    <span className="text-gray-700 font-medium">返回</span>
                </Button>
            </div>

            <div className="px-6 pt-20 pb-20">
                {/* 🎯 標題區域 */}
                <div className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 rounded-3xl p-8 shadow-2xl mb-6">
                    <div className="text-center">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <Plus className="w-8 h-8 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold text-white mb-2">加班申請</h1>
                        <p className="text-orange-100">申請加班時數，記錄額外工作時間</p>
                    </div>
                </div>

                {/* 📝 申請表單 */}
                <div className="bg-white rounded-3xl p-6 shadow-xl border border-gray-100">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* 員工資訊 */}
                        <div className="bg-orange-50 rounded-2xl p-4 border border-orange-200">
                            <div className="flex items-center space-x-3 mb-3">
                                <User className="w-5 h-5 text-orange-600" />
                                <h3 className="font-bold text-orange-900">申請人資訊</h3>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-orange-700 font-medium">姓名：</span>
                                    <span className="text-orange-900">{user.name}</span>
                                </div>
                                <div>
                                    <span className="text-orange-700 font-medium">員工編號：</span>
                                    <span className="text-orange-900">{user.employee_id}</span>
                                </div>
                            </div>
                        </div>

                        {/* 加班日期 */}
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <Calendar className="w-4 h-4 text-orange-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    加班日期 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <input
                                type="date"
                                value={formData.overtime_date}
                                onChange={(e) => setFormData(prev => ({ ...prev, overtime_date: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                                required
                            />
                        </div>

                        {/* 時間範圍 */}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <Clock className="w-4 h-4 text-orange-600" />
                                    <label className="block text-sm font-semibold text-gray-700">
                                        開始時間 <span className="text-red-500">*</span>
                                    </label>
                                </div>
                                <input
                                    type="time"
                                    value={formData.start_time}
                                    onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                                    className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <Clock className="w-4 h-4 text-orange-600" />
                                    <label className="block text-sm font-semibold text-gray-700">
                                        結束時間 <span className="text-red-500">*</span>
                                    </label>
                                </div>
                                <input
                                    type="time"
                                    value={formData.end_time}
                                    onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                                    className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                                    required
                                />
                            </div>
                        </div>

                        {/* 加班時數顯示 */}
                        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-4 border border-orange-200">
                            <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                    <Clock className="w-4 h-4 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm text-orange-700 font-medium">預計加班時數</p>
                                    <p className="text-2xl font-bold text-orange-900">{calculateHours()} 小時</p>
                                </div>
                            </div>
                        </div>

                        {/* 加班類型 */}
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <FileText className="w-4 h-4 text-orange-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    加班類型 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <select
                                value={formData.overtime_type}
                                onChange={(e) => setFormData(prev => ({ ...prev, overtime_type: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                                required
                            >
                                <option value="">請選擇加班類型</option>
                                {overtimeTypes.map(type => (
                                    <option key={type.id} value={type.code}>
                                        {type.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* 加班原因 */}
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <FileText className="w-4 h-4 text-orange-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    加班原因 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <textarea
                                value={formData.reason}
                                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                                placeholder="請詳細說明加班原因..."
                                rows={4}
                                className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 resize-none"
                                required
                            />
                        </div>

                        {/* 提交按鈕 */}
                        <div className="pt-4">
                            <Button
                                type="submit"
                                disabled={loading}
                                className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center space-x-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        <span>提交中...</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center space-x-2">
                                        <CheckCircle className="w-5 h-5" />
                                        <span>提交加班申請</span>
                                    </div>
                                )}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    )
} 