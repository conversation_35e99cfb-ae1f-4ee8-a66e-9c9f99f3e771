#!/usr/bin/env python3
"""
測試考勤記錄生成功能
"""

import sqlite3
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_connection():
    """創建資料庫連接"""
    return sqlite3.connect('attendance.db')

def test_attendance_generation():
    """測試考勤記錄生成"""
    target_date = '2025-06-02'
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 從punch_records表查詢打卡記錄
        cursor.execute("""
            SELECT e.id as employee_id, p.punch_datetime, p.status_code, p.device_id, p.raw_data,
                   e.name as employee_name, e.employee_id as employee_code
            FROM punch_records p
            JOIN employees e ON p.employee_id = e.employee_id
            WHERE p.punch_date = ?
            ORDER BY e.id, p.punch_datetime
            LIMIT 10
        """, (target_date,))
        
        punch_records = cursor.fetchall()
        logger.info(f"找到 {len(punch_records)} 筆打卡記錄")
        
        # 按員工分組處理打卡記錄
        employee_punches = {}
        for record in punch_records:
            emp_id = record[0]
            logger.info(f"處理記錄: employee_id={emp_id}, punch_datetime={record[1]}, status_code={record[2]}")
            
            if emp_id not in employee_punches:
                employee_punches[emp_id] = {"punches": []}
            employee_punches[emp_id]["punches"].append({"datetime": record[1], "status_code": record[2]})
        
        logger.info(f"分組後有 {len(employee_punches)} 個員工")
        
        # 為每個員工生成考勤記錄
        for emp_id, data in employee_punches.items():
            punches = data["punches"]
            logger.info(f"處理員工 {emp_id}, 有 {len(punches)} 筆打卡")
            
            # 確保employee_id不是None
            if emp_id is None:
                logger.error(f"employee_id為None，跳過此記錄")
                continue
            
            # 找出上班和下班打卡
            check_in_time = None
            check_out_time = None
            
            for punch in punches:
                if punch["status_code"] == "0":  # 上班
                    if not check_in_time or punch["datetime"] < check_in_time:
                        check_in_time = punch["datetime"]
                elif punch["status_code"] == "1":  # 下班
                    if not check_out_time or punch["datetime"] > check_out_time:
                        check_out_time = punch["datetime"]
            
            # 判斷考勤狀態
            status = "normal"
            if check_in_time and not check_out_time:
                status = "incomplete"
            elif not check_in_time and not check_out_time:
                status = "absent"
            
            note = f"從打卡記錄生成 - 共{len(punches)}筆打卡"
            
            logger.info(f"員工 {emp_id}: 上班={check_in_time}, 下班={check_out_time}, 狀態={status}")
            
            # 檢查是否已有考勤記錄
            cursor.execute("SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out, created_at)) = ?", (emp_id, target_date))
            existing_record = cursor.fetchone()
            
            if existing_record:
                logger.info(f"更新現有記錄 ID: {existing_record[0]}")
                cursor.execute("UPDATE attendance SET check_in = ?, check_out = ?, status = ?, note = ? WHERE id = ?", 
                             (check_in_time, check_out_time, status, note, existing_record[0]))
            else:
                logger.info(f"創建新記錄")
                cursor.execute("INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at) VALUES (?, ?, ?, ?, ?, ?)", 
                             (emp_id, check_in_time, check_out_time, status, note, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        logger.info("測試成功完成")
        
    except Exception as e:
        logger.error(f"測試失敗: {e}", exc_info=True)
    finally:
        conn.close()

if __name__ == "__main__":
    test_attendance_generation()