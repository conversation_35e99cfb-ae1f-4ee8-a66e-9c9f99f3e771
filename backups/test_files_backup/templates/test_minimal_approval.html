<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化審核頁面測試</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50">
    <div class="max-w-7xl mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">最小化審核頁面測試</h1>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">測試狀態</h2>
            <div id="testStatus" class="space-y-2">
                <p>初始化中...</p>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">API測試結果</h2>
            <div id="apiResults" class="space-y-2">
                <p>等待測試...</p>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h2 class="text-lg font-semibold mb-4">審核列表</h2>
            <div id="approvalList" class="space-y-2">
                <p>載入中...</p>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 最小化測試頁面開始載入...');

        // 測試狀態更新函數
        function updateTestStatus(message) {
            const statusDiv = document.getElementById('testStatus');
            const p = document.createElement('p');
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            statusDiv.appendChild(p);
            console.log(message);
        }

        // 測試API結果更新函數
        function updateApiResults(message) {
            const resultsDiv = document.getElementById('apiResults');
            const p = document.createElement('p');
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(p);
            console.log(message);
        }

        // 簡化的顯示列表函數
        function displaySimpleList(leaves) {
            const container = document.getElementById('approvalList');

            if (leaves.length === 0) {
                container.innerHTML = '<p class="text-gray-500">沒有待審核記錄</p>';
                return;
            }

            const html = leaves.map(leave => {
                return `
                    <div class="border rounded p-3 mb-2">
                        <p><strong>申請人:</strong> ${leave.employee_name || '未知'}</p>
                        <p><strong>假別:</strong> ${leave.leave_type_name || leave.leave_type || '未知'}</p>
                        <p><strong>日期:</strong> ${leave.start_date} ~ ${leave.end_date}</p>
                        <p><strong>原因:</strong> ${leave.reason || '無'}</p>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        // 測試API調用
        async function testApiCall() {
            try {
                updateApiResults('開始測試API調用...');

                const response = await fetch('/api/approval/leaves');
                updateApiResults(`API回應狀態: ${response.status}`);

                const data = await response.json();
                updateApiResults(`API回應數據: ${JSON.stringify(data).substring(0, 100)}...`);

                const leaves = data.records || [];
                updateApiResults(`解析到 ${leaves.length} 筆記錄`);

                displaySimpleList(leaves);
                updateApiResults('✅ API測試完成');

            } catch (error) {
                updateApiResults(`❌ API測試失敗: ${error.message}`);
                console.error('API測試錯誤:', error);
            }
        }

        // 頁面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                updateTestStatus('🚀 頁面初始化開始');

                updateTestStatus('🎨 初始化圖標...');
                lucide.createIcons();

                updateTestStatus('🔍 檢查工具函數庫...');
                if (window.UtilsLoader) {
                    updateTestStatus('✅ 工具函數庫存在');
                    await UtilsLoader.initPageUtils('test-minimal-approval');
                    updateTestStatus('✅ 工具函數庫初始化完成');
                } else {
                    updateTestStatus('⚠️ 工具函數庫不存在');
                }

                updateTestStatus('📡 開始API測試...');
                await testApiCall();

                updateTestStatus('✅ 頁面初始化完成');

            } catch (error) {
                updateTestStatus(`❌ 初始化失敗: ${error.message}`);
                console.error('初始化錯誤:', error);
            }
        });
    </script>
</body>

</html>