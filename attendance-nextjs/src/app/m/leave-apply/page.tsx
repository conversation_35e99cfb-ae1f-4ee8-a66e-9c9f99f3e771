"use client"

import { withAuth, useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { apiGet, apiPost } from '@/lib/api-client'
import type { LeaveRequestForm, Employee } from '@/types'
import {
    Calendar,
    User,
    Clock,
    MessageSquare,
    ArrowLeft,
    AlertCircle,
    CheckCircle,
    Users
} from 'lucide-react'

interface LeaveType {
    id: number
    name: string
    code: string
    max_days_per_year: number
    requires_approval: boolean
    is_paid: boolean
    description?: string
}

function LeaveApplyPage() {
    const { user } = useAuth()
    const [formData, setFormData] = useState<LeaveRequestForm>({
        leave_type: '',
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0],
        start_time: '09:00',
        end_time: '18:00',
        is_full_day: true,
        reason: '',
        substitute_id: undefined,
        emergency_contact: ''
    })
    const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
    const [employees, setEmployees] = useState<Employee[]>([])
    const [loading, setLoading] = useState(false)
    const [submitting, setSubmitting] = useState(false)
    const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

    // 載入請假類型和員工列表
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true)
            try {
                // 載入請假類型
                const leaveTypesResponse = await apiGet<{ leave_types: LeaveType[] }>('/api/leave-types')
                if (leaveTypesResponse.success && leaveTypesResponse.data) {
                    setLeaveTypes(leaveTypesResponse.data.leave_types)
                }

                // 載入員工列表作為代理人選項
                const employeesResponse = await apiGet<{ employees: Employee[] }>('/api/employees')
                if (employeesResponse.success && employeesResponse.data) {
                    const filteredEmployees = employeesResponse.data.employees.filter(emp => emp.id !== user?.id && emp.status === 'active')
                    console.log('載入的員工數據:', filteredEmployees)
                    setEmployees(filteredEmployees)
                }
            } catch (error) {
                console.error('載入數據失敗:', error)
            } finally {
                setLoading(false)
            }
        }

        fetchData()
    }, [user])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setSubmitting(true)
        setMessage(null)

        try {
            // 找到選中的請假類型ID
            const selectedLeaveType = leaveTypes.find(type => type.code === formData.leave_type)
            if (!selectedLeaveType) {
                setMessage({ type: 'error', text: '請選擇有效的請假類型' })
                return
            }

            // 準備API數據
            const apiData = {
                employee_id: user?.id,
                leave_type: formData.leave_type,
                start_date: formData.start_date,
                end_date: formData.end_date,
                reason: formData.reason,
                substitute_id: formData.substitute_id,
                emergency_contact: formData.emergency_contact,
                // 如果是部分工時，添加時間信息
                ...(formData.is_full_day ? {} : {
                    start_time: formData.start_time,
                    end_time: formData.end_time
                })
            }

            const response = await apiPost('/api/leave-requests', apiData)

            if (response.success) {
                setMessage({ type: 'success', text: '請假申請提交成功！' })
                // 重置表單
                setFormData({
                    leave_type: '',
                    start_date: new Date().toISOString().split('T')[0],
                    end_date: new Date().toISOString().split('T')[0],
                    start_time: '09:00',
                    end_time: '18:00',
                    is_full_day: true,
                    reason: '',
                    substitute_id: undefined,
                    emergency_contact: ''
                })

                // 3秒後自動返回
                setTimeout(() => {
                    window.location.href = '/m'
                }, 3000)
            } else {
                setMessage({ type: 'error', text: response.error || '提交失敗' })
            }
        } catch (error) {
            setMessage({ type: 'error', text: '系統錯誤，請稍後再試' })
        } finally {
            setSubmitting(false)
        }
    }

    const handleFullDayChange = (isFullDay: boolean) => {
        setFormData({
            ...formData,
            is_full_day: isFullDay,
            start_time: isFullDay ? '09:00' : formData.start_time,
            end_time: isFullDay ? '18:00' : formData.end_time
        })
    }

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-white">
                <div className="text-center">
                    <div className="w-16 h-16 border-4 border-green-100 border-t-green-600 rounded-full animate-spin mx-auto mb-6"></div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">載入中</h3>
                    <p className="text-gray-600">正在初始化系統...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-white">
            {/* 🔙 返回按鈕 */}
            <div className="fixed top-4 left-4 z-50">
                <Button
                    onClick={() => window.location.href = '/m'}
                    className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl rounded-full px-4 py-2"
                >
                    <ArrowLeft className="w-4 h-4 text-gray-700" />
                    <span className="text-gray-700 font-medium">返回</span>
                </Button>
            </div>

            <div className="px-6 pt-20 pb-20">
                {/* 🎯 標題區域 */}
                <div className="bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 rounded-3xl p-8 shadow-2xl mb-6">
                    <div className="text-center">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <Calendar className="w-8 h-8 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold text-white mb-2">請假申請</h1>
                        <p className="text-green-100">申請休假時間，安排工作代理</p>
                    </div>
                </div>

                {/* 📝 申請表單 */}
                <div className="bg-white rounded-3xl p-6 shadow-xl border border-gray-100">

                    {/* 訊息提示 */}
                    {message && (
                        <div className={`flex items-center space-x-2 p-3 rounded-lg mb-4 ${message.type === 'success'
                            ? 'bg-green-50 border border-green-200'
                            : 'bg-red-50 border border-red-200'
                            }`}>
                            {message.type === 'success' ? (
                                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                            ) : (
                                <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                            )}
                            <span className={`text-sm ${message.type === 'success' ? 'text-green-700' : 'text-red-700'
                                }`}>
                                {message.text}
                            </span>
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* 員工資訊 */}
                        <div className="bg-green-50 rounded-2xl p-4 border border-green-200">
                            <div className="flex items-center space-x-3 mb-3">
                                <User className="w-5 h-5 text-green-600" />
                                <h3 className="font-bold text-green-900">申請人資訊</h3>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-green-700 font-medium">姓名：</span>
                                    <span className="text-green-900">{user?.name}</span>
                                </div>
                                <div>
                                    <span className="text-green-700 font-medium">員工編號：</span>
                                    <span className="text-green-900">{user?.employee_id}</span>
                                </div>
                            </div>
                        </div>

                        {/* 請假類型 */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <Calendar className="w-4 h-4 text-green-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    請假類型 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <select
                                value={formData.leave_type}
                                onChange={(e) => setFormData({ ...formData, leave_type: e.target.value })}
                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                required
                            >
                                <option value="">請選擇請假類型</option>
                                {leaveTypes.map(type => (
                                    <option key={type.code} value={type.code}>
                                        {type.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* 請假時間 */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <Clock className="w-4 h-4 text-green-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    請假時間 <span className="text-red-500">*</span>
                                </label>
                            </div>

                            {/* 全天/半天選擇 */}
                            <div className="flex space-x-6 mb-4">
                                <label className="flex items-center cursor-pointer">
                                    <input
                                        type="radio"
                                        checked={formData.is_full_day}
                                        onChange={() => handleFullDayChange(true)}
                                        className="mr-3 w-4 h-4 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm font-medium text-gray-700">全天請假</span>
                                </label>
                                <label className="flex items-center cursor-pointer">
                                    <input
                                        type="radio"
                                        checked={!formData.is_full_day}
                                        onChange={() => handleFullDayChange(false)}
                                        className="mr-3 w-4 h-4 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm font-medium text-gray-700">部分工時</span>
                                </label>
                            </div>

                            {/* 日期選擇 */}
                            <div className="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-xs font-medium text-gray-600 mb-2">開始日期</label>
                                    <input
                                        type="date"
                                        value={formData.start_date}
                                        onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                                        className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-gray-600 mb-2">結束日期</label>
                                    <input
                                        type="date"
                                        value={formData.end_date}
                                        onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                                        className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                        required
                                    />
                                </div>
                            </div>

                            {/* 時間選擇 (僅部分工時顯示) */}
                            {!formData.is_full_day && (
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-xs font-medium text-gray-600 mb-2">開始時間</label>
                                        <input
                                            type="time"
                                            value={formData.start_time}
                                            onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                                            className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-gray-600 mb-2">結束時間</label>
                                        <input
                                            type="time"
                                            value={formData.end_time}
                                            onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                                            className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* 代理人 */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <Users className="w-4 h-4 text-green-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    代理人 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <select
                                value={formData.substitute_id || ''}
                                onChange={(e) => setFormData({ ...formData, substitute_id: e.target.value ? Number(e.target.value) : undefined })}
                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                required
                            >
                                <option value="">請選擇代理人</option>
                                {employees.map(emp => (
                                    <option key={emp.id} value={emp.id}>
                                        {emp.name} ({emp.employee_id}) - {emp.department_name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* 請假原因 */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <MessageSquare className="w-4 h-4 text-green-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    請假原因 <span className="text-red-500">*</span>
                                </label>
                            </div>
                            <textarea
                                value={formData.reason}
                                onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm resize-none"
                                rows={3}
                                placeholder="請詳細說明請假原因"
                                required
                            />
                        </div>

                        {/* 緊急聯絡方式 */}
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <User className="w-4 h-4 text-green-600" />
                                <label className="block text-sm font-semibold text-gray-700">
                                    緊急聯絡方式
                                </label>
                            </div>
                            <input
                                type="text"
                                value={formData.emergency_contact}
                                onChange={(e) => setFormData({ ...formData, emergency_contact: e.target.value })}
                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm bg-white shadow-sm"
                                placeholder="請假期間的聯絡電話或其他方式"
                            />
                        </div>

                        {/* 提交按鈕 */}
                        <div className="flex space-x-4 pt-6">
                            <Button
                                type="button"
                                onClick={() => window.location.href = '/m'}
                                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded-xl py-3 px-6 font-medium transition-all duration-300 shadow-sm hover:shadow-md"
                            >
                                取消
                            </Button>
                            <Button
                                type="submit"
                                disabled={submitting}
                                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-xl py-3 px-6 font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                            >
                                {submitting ? (
                                    <div className="flex items-center space-x-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        <span>提交中...</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-2">
                                        <CheckCircle className="w-4 h-4" />
                                        <span>提交申請</span>
                                    </div>
                                )}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    )
}

export default withAuth(LeaveApplyPage) 