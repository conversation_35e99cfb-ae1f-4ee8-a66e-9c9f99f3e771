<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班表選擇功能測試</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        /* 班表選擇模態框樣式 */
        
        .shift-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }
        
        .shift-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .shift-modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .shift-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 8px;
        }
        
        .shift-option:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .shift-option.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 2000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .notification.success {
            background: #10b981;
        }
        
        .notification.error {
            background: #ef4444;
        }
        
        .notification.warning {
            background: #f59e0b;
        }
        
        .notification.info {
            background: #3b82f6;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>班表選擇功能測試</h1>
        <p>這個頁面用來測試班表選擇模態框的功能是否正常。</p>

        <div>
            <h3>測試按鈕：</h3>
            <button class="test-button" onclick="testShowModal()">
                測試顯示班表選擇模態框
            </button>
            <button class="test-button" onclick="testAPI()">
                測試換班API
            </button>
        </div>

        <div id="testResults" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
            <h4>測試結果：</h4>
            <div id="resultContent">等待測試...</div>
        </div>
    </div>

    <!-- 班表選擇模態框 -->
    <div id="shiftModal" class="shift-modal">
        <div class="shift-modal-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                <h3 style="margin: 0; font-size: 20px; font-weight: bold;">選擇班表</h3>
                <button type="button" id="closeShiftModalBtn" style="background: none; border: none; cursor: pointer; color: #6b7280;">
                    <i data-lucide="x" style="width: 24px; height: 24px;"></i>
                </button>
            </div>

            <div style="margin-bottom: 16px;">
                <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
                    員工：<span id="modalEmployeeName" style="font-weight: 500; color: #111827;">測試員工</span>
                </p>
                <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
                    日期：<span id="modalWorkDate" style="font-weight: 500; color: #111827;">2025-06-04</span>
                </p>
            </div>

            <div id="shiftOptions" style="margin-bottom: 24px;">
                <!-- 動態載入班表選項 -->
            </div>

            <div style="display: flex; justify-content: flex-end; gap: 12px;">
                <button type="button" id="cancelShiftBtn" class="test-button" style="background: #6b7280;">
                    取消
                </button>
                <button type="button" id="confirmShiftBtn" class="test-button">
                    確認修改
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentAttendanceId = 812; // 測試用的考勤記錄ID
        let selectedShiftId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            bindEvents();
            logResult('頁面初始化完成');
        });

        // 綁定事件
        function bindEvents() {
            document.getElementById('closeShiftModalBtn').addEventListener('click', hideShiftModal);
            document.getElementById('cancelShiftBtn').addEventListener('click', hideShiftModal);
            document.getElementById('confirmShiftBtn').addEventListener('click', confirmShiftChange);
            logResult('事件綁定完成');
        }

        // 測試顯示模態框
        async function testShowModal() {
            logResult('開始測試顯示模態框...');
            await showShiftModal(812, '洪志鴻', '2025-06-27', 3);
        }

        // 測試API
        async function testAPI() {
            logResult('開始測試換班API...');
            try {
                const response = await fetch('/api/attendance/management/update-shift', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        attendance_id: 812,
                        shift_id: 1
                    })
                });

                const data = await response.json();
                logResult(`API測試結果: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                logResult(`API測試失敗: ${error.message}`);
            }
        }

        // 顯示班表選擇模態框
        async function showShiftModal(attendanceId, employeeName, workDate, currentShiftId) {
            currentAttendanceId = attendanceId;
            selectedShiftId = currentShiftId;

            // 更新模態框資訊
            document.getElementById('modalEmployeeName').textContent = employeeName;
            document.getElementById('modalWorkDate').textContent = workDate;

            // 載入班表選項
            await loadShiftOptions(currentShiftId);

            // 顯示模態框
            document.getElementById('shiftModal').classList.add('active');
            logResult('模態框顯示成功');
        }

        // 隱藏班表選擇模態框
        function hideShiftModal() {
            document.getElementById('shiftModal').classList.remove('active');
            currentAttendanceId = null;
            selectedShiftId = null;
            logResult('模態框已隱藏');
        }

        // 載入班表選項
        async function loadShiftOptions(currentShiftId) {
            try {
                logResult('開始載入班表選項...');
                const response = await fetch('/api/shifts');
                const data = await response.json();
                const shifts = data.shifts || data;

                const container = document.getElementById('shiftOptions');
                container.innerHTML = '';

                shifts.forEach(shift => {
                    const isSelected = shift.id == currentShiftId;
                    const option = document.createElement('div');
                    option.className = `shift-option ${isSelected ? 'selected' : ''}`;
                    option.dataset.shiftId = shift.id;

                    option.innerHTML = `
                        <div>
                            <div style="font-weight: 500; color: #111827;">${shift.name}</div>
                            <div style="font-size: 14px; color: #6b7280;">${shift.start_time} - ${shift.end_time}</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-size: 14px; font-weight: 500; color: #3b82f6;">${calculateWorkDuration(shift.start_time, shift.end_time)}</div>
                            <div style="font-size: 12px; color: #9ca3af;">${shift.code || ''}</div>
                        </div>
                    `;

                    option.addEventListener('click', function() {
                        // 移除其他選項的選中狀態
                        container.querySelectorAll('.shift-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });

                        // 選中當前選項
                        this.classList.add('selected');
                        selectedShiftId = shift.id;
                        logResult(`選擇班表: ${shift.name} (ID: ${shift.id})`);
                    });

                    container.appendChild(option);
                });

                logResult(`載入了 ${shifts.length} 個班表選項`);

            } catch (error) {
                logResult(`載入班表選項失敗: ${error.message}`);
                showNotification('載入班表選項失敗', 'error');
            }
        }

        // 確認班表修改
        async function confirmShiftChange() {
            if (!currentAttendanceId || !selectedShiftId) {
                showNotification('請選擇班表', 'warning');
                logResult('確認修改失敗: 未選擇班表');
                return;
            }

            try {
                logResult(`開始確認修改: 考勤ID=${currentAttendanceId}, 班表ID=${selectedShiftId}`);
                showNotification('正在修改班表...', 'info');

                const response = await fetch('/api/attendance/management/update-shift', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        attendance_id: currentAttendanceId,
                        shift_id: selectedShiftId
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showNotification('班表修改成功', 'success');
                    hideShiftModal();
                    logResult(`班表修改成功: ${JSON.stringify(data, null, 2)}`);
                } else {
                    showNotification(data.error || '班表修改失敗', 'error');
                    logResult(`班表修改失敗: ${data.error}`);
                }

            } catch (error) {
                logResult(`班表修改失敗: ${error.message}`);
                showNotification('班表修改失敗', 'error');
            }
        }

        // 計算工作時長
        function calculateWorkDuration(startTime, endTime) {
            try {
                const start = new Date(`2000-01-01 ${startTime}`);
                const end = new Date(`2000-01-01 ${endTime}`);
                const diff = (end - start) / (1000 * 60 * 60);
                return `${diff}小時`;
            } catch (error) {
                return '-';
            }
        }

        // 顯示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // 記錄測試結果
        function logResult(message) {
            const resultContent = document.getElementById('resultContent');
            const timestamp = new Date().toLocaleTimeString();
            resultContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            resultContent.scrollTop = resultContent.scrollHeight;
        }
    </script>
</body>

</html>