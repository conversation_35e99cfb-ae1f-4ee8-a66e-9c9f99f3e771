"use client"

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import {
  Search,
  RefreshCw,
  Download,
  FileText,
  Calculator,
  ArrowLeft,
  CheckCircle,
  Clock,
  ClockAlert,
  PlusCircle,
  CalendarX,
  XCircle,
  ClipboardList,
  X,
  ChevronLeft,
  ChevronRight,
  Eye,
  Edit,
  AlertTriangle,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import {
  getAttendanceManagementRecords,
  getAttendanceManagementRecordDetail,
  updateAttendanceShift,
  getShifts,
  exportAttendanceExcel,
  exportAttendancePDF,
  getEmployees,
  getDepartments,
  getAttendanceEditData,
  saveAttendanceEdit,
  type AttendanceManagementRecord,
  type AttendanceRecordsResponse,
  getAttendanceStatusOptions
} from '@/lib/api-client'

// 類型定義
interface Shift {
  id: number
  name: string
  start_time: string
  end_time: string
  color: string
}

interface AttendanceStats {
  normal: number
  late: number
  early_leave: number
  overtime: number
  leave: number
  absent: number
}

export default function AttendanceManagementPage() {
  const { user } = useAuth()
  const router = useRouter()

  // 狀態管理
  const [records, setRecords] = useState<AttendanceManagementRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<AttendanceStats>({
    normal: 0,
    late: 0,
    early_leave: 0,
    overtime: 0,
    leave: 0,
    absent: 0
  })

  // 模態框狀態
  const [showShiftModal, setShowShiftModal] = useState(false)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<AttendanceManagementRecord | null>(null)
  const [shifts, setShifts] = useState<Shift[]>([])
  const [leaveTypes, setLeaveTypes] = useState<{ code: string, name: string }[]>([])
  const [selectedShift, setSelectedShift] = useState<number | null>(null)

  // 編輯表單狀態
  const [editForm, setEditForm] = useState({
    check_in: '',
    check_out: '',
    shift_id: '',
    leave_type: '',
    leave_hours: 0,
    leave_reason: '',
    note: ''
  })

  // 查詢條件
  const [searchParams, setSearchParams] = useState({
    employee_id: '',
    department_id: '',
    start_date: '',
    end_date: '',
    status: ''
  })

  // 下拉選項
  const [employees, setEmployees] = useState<{ id: number, name: string, employee_id: string }[]>([])
  const [departments, setDepartments] = useState<{ id: number, name: string }[]>([])
  const [statusOptions, setStatusOptions] = useState<{ code: string, name: string, color: string, bgColor: string, icon: string }[]>([])

  // 分頁
  const [currentPage, setCurrentPage] = useState(1)
  const [totalRecords, setTotalRecords] = useState(0)
  const pageSize = 50

  // 計算總頁數
  const totalPages = Math.ceil(totalRecords / pageSize)

  // 初始化
  useEffect(() => {
    loadMasterData()
    searchRecords()
  }, [])

  // 設定預設日期（今天）
  const setDefaultDates = () => {
    const today = new Date().toISOString().split('T')[0]
    setSearchParams(prev => ({
      ...prev,
      start_date: today,
      end_date: today
    }))
  }

  // 載入員工列表
  const loadEmployees = async () => {
    try {
      const response = await getEmployees()
      if (response.success && response.data) {
        setEmployees(response.data)
      }
    } catch (error) {
      console.error('載入員工列表失敗:', error)
    }
  }

  // 載入部門列表
  const loadDepartments = async () => {
    try {
      const response = await getDepartments()
      if (response.success && response.data) {
        setDepartments(response.data)
      }
    } catch (error) {
      console.error('載入部門列表失敗:', error)
    }
  }

  // 載入班表列表
  const loadShifts = async () => {
    try {
      const response = await getShifts()
      if (response.success && response.data?.shifts) {
        setShifts(response.data.shifts)
      }
    } catch (error) {
      console.error('載入班表列表失敗:', error)
    }
  }

  // 載入主數據
  const loadMasterData = async () => {
    try {
      // 載入員工列表
      const employeesResponse = await getEmployees()
      if (employeesResponse.success && employeesResponse.data) {
        setEmployees(employeesResponse.data)
      }

      // 載入部門列表
      const departmentsResponse = await getDepartments()
      if (departmentsResponse.success && departmentsResponse.data) {
        setDepartments(departmentsResponse.data)
      }

      // 🆕 載入狀態選項
      const statusOpts = getAttendanceStatusOptions()
      setStatusOptions(statusOpts)
      console.log('載入狀態選項:', statusOpts)

    } catch (error) {
      console.error('載入主數據失敗:', error)
    }
  }

  // 查詢考勤記錄
  const searchRecords = async () => {
    setLoading(true)
    try {
      const response = await getAttendanceManagementRecords({
        page: currentPage,
        per_page: pageSize,
        ...Object.fromEntries(
          Object.entries(searchParams).filter(([_, value]) => value !== '')
        )
      })

      if (response.success && response.data) {
        setRecords(response.data.records || [])
        setTotalRecords(response.data.pagination?.total || 0)
      } else {
        console.error('查詢失敗:', response.error)
      }
    } catch (error) {
      console.error('查詢考勤記錄失敗:', error)
    } finally {
      setLoading(false)
    }
  }

  // 快速日期選擇
  const setQuickDate = (days: number) => {
    const today = new Date()
    let startDate = new Date(today)
    let endDate = new Date(today)

    switch (days) {
      case 0: // 今天
        break
      case 1: // 昨天
        startDate.setDate(today.getDate() - 1)
        endDate.setDate(today.getDate() - 1)
        break
      case 7: // 本週
        const dayOfWeek = today.getDay()
        const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
        startDate.setDate(diff)
        break
      case 14: // 上週
        const lastWeekStart = today.getDate() - today.getDay() - 6
        startDate.setDate(lastWeekStart)
        endDate.setDate(lastWeekStart + 6)
        break
      case 30: // 本月
        startDate.setDate(1)
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        // 確保時區正確，避免日期偏移
        endDate.setHours(12, 0, 0, 0)
        break
    }

    setSearchParams(prev => ({
      ...prev,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    }))

    // 自動查詢
    setTimeout(searchRecords, 100)
  }

  // 分頁控制函數
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  // 當頁面改變時重新查詢
  useEffect(() => {
    if (currentPage > 0) {
      searchRecords()
    }
  }, [currentPage])

  // 顯示班表切換模態框
  const handleShiftChange = (record: AttendanceManagementRecord) => {
    setSelectedRecord(record)
    setSelectedShift(record.shift_id)
    setShowShiftModal(true)
  }

  // 保存班表切換
  const handleSaveShiftChange = async () => {
    if (!selectedRecord || !selectedShift) return

    try {
      const response = await updateAttendanceShift(selectedRecord.id, selectedShift)
      if (response.success) {
        setShowShiftModal(false)
        setSelectedRecord(null)
        setSelectedShift(null)
        searchRecords() // 重新載入數據
        alert('班表更新成功')
      } else {
        alert('班表更新失敗: ' + response.error)
      }
    } catch (error) {
      console.error('班表更新失敗:', error)
      alert('班表更新失敗')
    }
  }

  // 顯示詳情模態框
  const handleShowDetail = async (record: AttendanceManagementRecord) => {
    try {
      const response = await getAttendanceManagementRecordDetail(record.id)
      if (response.success && response.data?.record) {
        setSelectedRecord(response.data.record)
        setShowDetailModal(true)
      } else {
        alert('載入詳情失敗: ' + response.error)
      }
    } catch (error) {
      console.error('載入詳情失敗:', error)
      alert('載入詳情失敗')
    }
  }

  // 匯出Excel
  const handleExportExcel = async () => {
    try {
      const result = await exportAttendanceExcel({
        ...Object.fromEntries(
          Object.entries(searchParams).filter(([_, value]) => value !== '')
        )
      })
      if (result.success) {
        alert('Excel匯出成功')
      } else {
        alert('Excel匯出失敗: ' + result.error)
      }
    } catch (error) {
      console.error('Excel匯出失敗:', error)
      alert('Excel匯出失敗')
    }
  }

  // 匯出PDF
  const handleExportPDF = async () => {
    try {
      const result = await exportAttendancePDF({
        ...Object.fromEntries(
          Object.entries(searchParams).filter(([_, value]) => value !== '')
        )
      })
      if (result.success) {
        alert('PDF匯出成功')
      } else {
        alert('PDF匯出失敗: ' + result.error)
      }
    } catch (error) {
      console.error('PDF匯出失敗:', error)
      alert('PDF匯出失敗')
    }
  }

  // 顯示編輯模態框
  const handleEditRecord = async (record: AttendanceManagementRecord) => {
    try {
      console.log('開始載入編輯資料，記錄ID:', record.id)
      const response = await getAttendanceEditData(record.id)
      console.log('API回應:', response)

      if (response.success && response.data) {
        setSelectedRecord(response.data.record)
        setShifts(response.data.available_shifts || [])
        setLeaveTypes(response.data.leave_types || [])

        // 設置編輯表單初始值
        console.log('原始打卡時間:', response.data.record.check_in, response.data.record.check_out)

        // 處理打卡時間格式 - 確保正確轉換為 datetime-local 格式
        const formatTimeForInput = (timeStr: string | null) => {
          if (!timeStr) return ''
          try {
            // 如果時間格式是 "YYYY-MM-DD HH:MM:SS"，需要轉換為 "YYYY-MM-DDTHH:MM"
            if (timeStr.includes(' ')) {
              const [date, time] = timeStr.split(' ')
              const [hours, minutes] = time.split(':')
              return `${date}T${hours}:${minutes}`
            }
            // 如果已經是 ISO 格式，直接處理
            return new Date(timeStr).toISOString().slice(0, 16)
          } catch (error) {
            console.error('時間格式轉換錯誤:', error, timeStr)
            return ''
          }
        }

        const checkInTime = formatTimeForInput(response.data.record.check_in)
        const checkOutTime = formatTimeForInput(response.data.record.check_out)

        console.log('轉換後的時間:', checkInTime, checkOutTime)

        // 處理請假資訊 - 從 leave_records 中獲取
        const leaveRecord = response.data.leave_records && response.data.leave_records.length > 0
          ? response.data.leave_records[0]
          : null

        setEditForm({
          check_in: checkInTime,
          check_out: checkOutTime,
          shift_id: response.data.record.shift_id ? response.data.record.shift_id.toString() : '',
          leave_type: leaveRecord?.leave_type || response.data.record.leave_type || '',
          leave_hours: leaveRecord?.leave_hours || response.data.record.leave_hours || 0,
          leave_reason: leaveRecord?.reason || response.data.record.leave_reason || '',
          note: response.data.record.note || ''
        })

        console.log('設置的編輯表單:', {
          check_in: checkInTime,
          check_out: checkOutTime,
          shift_id: response.data.record.shift_id,
          leave_type: leaveRecord?.leave_type || response.data.record.leave_type,
          leave_hours: leaveRecord?.leave_hours || response.data.record.leave_hours,
          leave_reason: leaveRecord?.reason || response.data.record.leave_reason,
          note: response.data.record.note
        })

        setShowEditModal(true)
      } else {
        console.error('API回應錯誤:', response.error)
        alert('載入編輯資料失敗: ' + response.error)
      }
    } catch (error) {
      console.error('載入編輯資料失敗:', error)
      alert('載入編輯資料失敗: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 保存編輯
  const handleSaveEdit = async () => {
    if (!selectedRecord) return

    try {
      const editData = {
        check_in: editForm.check_in ? editForm.check_in.replace('T', ' ') + ':00' : null,
        check_out: editForm.check_out ? editForm.check_out.replace('T', ' ') + ':00' : null,
        shift_id: editForm.shift_id && editForm.shift_id !== '' ? parseInt(editForm.shift_id) : null,
        leave_type: editForm.leave_type && editForm.leave_type !== '' ? editForm.leave_type : null,
        leave_hours: editForm.leave_hours || 0,
        leave_reason: editForm.leave_reason || '',
        note: editForm.note || ''
      }

      console.log('保存編輯資料:', editData)
      const response = await saveAttendanceEdit(selectedRecord.id, editData)
      if (response.success) {
        setShowEditModal(false)
        setSelectedRecord(null)
        searchRecords() // 重新載入數據
        alert('考勤記錄更新成功！系統已重新計算遲到、早退、加班和請假時數。')
      } else {
        alert('更新失敗: ' + response.error)
      }
    } catch (error) {
      console.error('更新失敗:', error)
      alert('更新失敗: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 更新編輯表單
  const updateEditForm = (field: string, value: any) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 轉換星期幾為中文
  const getWeekdayText = (weekday: string) => {
    const weekdayMap: { [key: string]: string } = {
      '1': '星期一',
      '2': '星期二',
      '3': '星期三',
      '4': '星期四',
      '5': '星期五',
      '6': '星期六',
      '7': '星期日',
      '0': '星期日', // 有些系統用0表示星期日
      'Monday': '星期一',
      'Tuesday': '星期二',
      'Wednesday': '星期三',
      'Thursday': '星期四',
      'Friday': '星期五',
      'Saturday': '星期六',
      'Sunday': '星期日',
      '星期一': '星期一',
      '星期二': '星期二',
      '星期三': '星期三',
      '星期四': '星期四',
      '星期五': '星期五',
      '星期六': '星期六',
      '星期日': '星期日'
    }
    return weekdayMap[weekday] || weekday
  }

  // 判斷是否為週末
  const isWeekend = (record: AttendanceManagementRecord) => {
    return record.date_type === 'weekend' ||
      record.weekday === '6' || record.weekday === '7' ||
      record.weekday === '0' || record.weekday === 'Saturday' ||
      record.weekday === 'Sunday' || record.weekday === '星期六' ||
      record.weekday === '星期日'
  }

  if (!user || user.role_id !== 999) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">權限不足</h2>
          <p className="text-gray-600 mb-4">您需要管理員權限才能訪問此頁面</p>
          <Link href="/m" className="text-blue-500 hover:text-blue-700">
            返回儀表板
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 📦 主容器 - 內容區域容器 */}
      <div className="p-6">
        {/* 🎨 頁面標題 - 統一的標題設計 */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
          <div className="flex items-center justify-between">
            {/* 📍 左側標題區 */}
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2 text-white">考勤作業管理</h1>
              <div className="flex items-center space-x-2">
                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                  <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                  <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                </Link>
                <p className="text-indigo-100 text-base font-medium">管理員工每日考勤狀況，包含上下班時間、加班、遲到早退、請假等統計</p>
              </div>
            </div>

            {/* 📍 右側資訊區 */}
            <div className="flex items-center space-x-3 text-right">
              <div>
                <p className="text-sm font-medium text-white">管理員模式</p>
                <p className="text-xs text-indigo-100">考勤管理</p>
              </div>
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                <Calendar className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* 查詢條件 */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">員工</label>
              <select
                value={searchParams.employee_id}
                onChange={(e) => setSearchParams(prev => ({ ...prev, employee_id: e.target.value }))}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              >
                <option value="">所有員工</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>
                    {emp.name} ({emp.employee_id})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">部門</label>
              <select
                value={searchParams.department_id}
                onChange={(e) => setSearchParams(prev => ({ ...prev, department_id: e.target.value }))}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              >
                <option value="">所有部門</option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">開始日期</label>
              <input
                type="date"
                value={searchParams.start_date}
                onChange={(e) => setSearchParams(prev => ({ ...prev, start_date: e.target.value }))}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
              <input
                type="date"
                value={searchParams.end_date}
                onChange={(e) => setSearchParams(prev => ({ ...prev, end_date: e.target.value }))}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">考勤狀態</label>
              <select
                value={searchParams.status}
                onChange={(e) => setSearchParams(prev => ({ ...prev, status: e.target.value }))}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              >
                <option value="">所有狀態</option>
                {statusOptions.map((option) => (
                  <option key={option.code} value={option.code}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={searchRecords}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Search className="w-4 h-4" />
                <span>查詢</span>
              </button>
            </div>
          </div>

          {/* 快速選擇 */}
          <div className="mt-4 flex flex-wrap gap-2">
            <button
              onClick={() => setQuickDate(0)}
              className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
            >
              今天
            </button>
            <button
              onClick={() => setQuickDate(7)}
              className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
            >
              本週
            </button>
            <button
              onClick={() => setQuickDate(30)}
              className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
            >
              本月
            </button>
            <button
              onClick={() => setQuickDate(1)}
              className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
            >
              昨天
            </button>
            <button
              onClick={() => setQuickDate(14)}
              className="text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
            >
              上週
            </button>
          </div>
        </div>

        {/* 工具欄 */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">考勤作業列表</h2>
              <span className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                {totalRecords} 筆記錄
              </span>
              {/* 分頁資訊 */}
              {totalPages > 1 && (
                <span className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                  第 {currentPage} 頁，共 {totalPages} 頁
                </span>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={searchRecords}
                className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-sm hover:shadow-md transform hover:scale-105"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重新整理</span>
              </button>
              <button className="bg-gradient-to-r from-emerald-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                <Calculator className="w-4 h-4" />
                <span>重新計算</span>
              </button>
              <button
                onClick={handleExportExcel}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Download className="w-4 h-4" />
                <span>匯出Excel</span>
              </button>
              <button
                onClick={handleExportPDF}
                className="bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-pink-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <FileText className="w-4 h-4" />
                <span>產生PDF檔</span>
              </button>
            </div>
          </div>
        </div>

        {/* 考勤記錄列表 */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/30 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-500"></div>
                <span className="text-gray-600">載入中...</span>
              </div>
            </div>
          ) : records.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <ClipboardList className="w-12 h-12 mx-auto mb-3" />
              </div>
              <p className="text-gray-600">暫無考勤記錄</p>
              <p className="text-sm text-gray-400 mt-1">調整查詢條件或點擊「重新計算」生成考勤資料</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white sticky top-0 z-10">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">日期</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">員工</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">班別</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">實際到班</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">實際下班</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">遲到</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">早退</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">加班</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">請假</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">請假小時</th>
                      <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">狀態</th>
                      <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {records.map((record, index) => (
                      <tr
                        key={record.id}
                        className="hover:bg-gradient-to-r hover:from-slate-50 hover:to-blue-50 transition-all duration-200"
                      >
                        <td className="px-4 py-3 text-sm">
                          <div>
                            <div className={`font-medium ${isWeekend(record) ? 'text-red-600' : 'text-gray-900'}`}>
                              {record.work_date}
                            </div>
                            <div className={`text-xs ${isWeekend(record) ? 'text-red-500' : 'text-gray-500'}`}>
                              {getWeekdayText(record.weekday)}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <div>
                            <div className="font-medium text-gray-900">{record.employee_name}</div>
                            <div className="text-gray-500 text-xs">{record.employee_code}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <div>
                            <button
                              onClick={() => handleShiftChange(record)}
                              className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:from-purple-600 hover:to-indigo-600 transition-all duration-200"
                            >
                              {record.shift_name}
                            </button>
                            <div className="text-gray-500 text-xs mt-1">
                              {record.shift_start_time && record.shift_end_time
                                ? `${record.shift_start_time.substring(0, 5)} - ${record.shift_end_time.substring(0, 5)}`
                                : '時間未設定'
                              }
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.check_in ? new Date(record.check_in).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' }) : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.check_out ? new Date(record.check_out).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' }) : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.late_minutes > 0 ? `${record.late_minutes}分` : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.early_leave_minutes > 0 ? `${record.early_leave_minutes}分` : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.overtime_minutes > 0 ? `${Math.round(record.overtime_minutes / 60 * 100) / 100}時` : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.leave_type || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {record.leave_hours > 0 ? `${record.leave_hours}時` : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            // 週末或假日顯示休假
                            isWeekend(record) || record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday' ? 'bg-blue-100 text-blue-800' :
                              // 有請假記錄顯示請假
                              record.leave_type && record.leave_hours > 0 ? 'bg-purple-100 text-purple-800' :
                                // 有早退顯示早退
                                record.early_leave_minutes > 0 ? 'bg-orange-100 text-orange-800' :
                                  // 有遲到顯示遲到
                                  record.late_minutes > 0 ? 'bg-yellow-100 text-yellow-800' :
                                    // 其他情況顯示正常
                                    'bg-green-100 text-green-800'
                            }`}>
                            {
                              // 週末或假日顯示休假
                              isWeekend(record) || record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday' ? '休假' :
                                // 有請假記錄顯示請假
                                record.leave_type && record.leave_hours > 0 ? '請假' :
                                  // 有早退顯示早退
                                  record.early_leave_minutes > 0 ? '早退' :
                                    // 有遲到顯示遲到
                                    record.late_minutes > 0 ? '遲到' :
                                      // 其他情況顯示正常
                                      '正常'
                            }
                          </span>
                        </td>
                        <td className="px-4 py-3 text-right text-sm">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleShowDetail(record)}
                              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105"
                            >
                              <Eye className="w-3 h-3 mr-1" />
                              詳情
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleEditRecord(record)
                              }}
                              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-green-500 to-green-600 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105"
                            >
                              <Edit className="w-3 h-3 mr-1" />
                              編輯
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 分頁控制 */}
              {totalPages > 1 && (
                <div className="bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    {/* 左側：記錄資訊 */}
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-700">
                        顯示第 <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> 到{' '}
                        <span className="font-medium">{Math.min(currentPage * pageSize, totalRecords)}</span> 筆，
                        共 <span className="font-medium">{totalRecords}</span> 筆記錄
                      </span>
                    </div>

                    {/* 右側：分頁控制 */}
                    <div className="flex items-center space-x-2">
                      {/* 上一頁按鈕 */}
                      <button
                        onClick={handlePreviousPage}
                        disabled={currentPage <= 1}
                        className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage <= 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 shadow-sm hover:shadow-md'
                          }`}
                      >
                        <ChevronLeft className="w-4 h-4 mr-1" />
                        上一頁
                      </button>

                      {/* 頁碼按鈕 */}
                      <div className="flex items-center space-x-1">
                        {/* 第一頁 */}
                        {currentPage > 3 && (
                          <>
                            <button
                              onClick={() => handlePageChange(1)}
                              className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
                            >
                              1
                            </button>
                            {currentPage > 4 && (
                              <span className="px-2 py-2 text-gray-500">...</span>
                            )}
                          </>
                        )}

                        {/* 當前頁面附近的頁碼 */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                          if (pageNum > totalPages) return null

                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${pageNum === currentPage
                                ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'
                                : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                              {pageNum}
                            </button>
                          )
                        })}

                        {/* 最後一頁 */}
                        {currentPage < totalPages - 2 && (
                          <>
                            {currentPage < totalPages - 3 && (
                              <span className="px-2 py-2 text-gray-500">...</span>
                            )}
                            <button
                              onClick={() => handlePageChange(totalPages)}
                              className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200"
                            >
                              {totalPages}
                            </button>
                          </>
                        )}
                      </div>

                      {/* 下一頁按鈕 */}
                      <button
                        onClick={handleNextPage}
                        disabled={currentPage >= totalPages}
                        className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage >= totalPages
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 shadow-sm hover:shadow-md'
                          }`}
                      >
                        下一頁
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 班表切換模態框 */}
      {showShiftModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-2xl shadow-2xl w-[90vw] max-w-2xl max-h-[80vh] overflow-hidden">
            {/* 模態框頭部 */}
            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 px-6 py-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-bold">更換班表</h3>
                  <p className="text-purple-100 text-sm">
                    {selectedRecord.employee_name} - {selectedRecord.work_date}
                  </p>
                </div>
                <button
                  onClick={() => setShowShiftModal(false)}
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-lg transition-all duration-200"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* 模態框內容 */}
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              <div className="space-y-3">
                {shifts.map((shift) => (
                  <div
                    key={shift.id}
                    onClick={() => setSelectedShift(shift.id)}
                    className={`flex items-center justify-between p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${selectedShift === shift.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-purple-300 hover:bg-gray-50'
                      }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: shift.color || '#6366f1' }}
                      ></div>
                      <div>
                        <div className="font-medium text-gray-900">{shift.name}</div>
                        <div className="text-sm text-gray-500">
                          {shift.start_time} - {shift.end_time}
                        </div>
                      </div>
                    </div>
                    {selectedShift === shift.id && (
                      <CheckCircle className="w-5 h-5 text-purple-500" />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 模態框底部按鈕 */}
            <div className="bg-gray-50 px-6 py-4 border-t">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowShiftModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSaveShiftChange}
                  disabled={!selectedShift}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  確認更換
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 考勤詳情模態框 */}
      {showDetailModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-2xl shadow-2xl w-[90vw] max-w-4xl max-h-[90vh] overflow-hidden">
            {/* 模態框頭部 */}
            <div className="bg-gradient-to-r from-blue-500 to-indigo-500 px-6 py-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-bold">考勤詳情</h3>
                  <p className="text-blue-100 text-sm">
                    {selectedRecord.employee_name} - {selectedRecord.work_date}
                  </p>
                </div>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-lg transition-all duration-200"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* 模態框內容 */}
            <div className="p-6 max-h-[70vh] overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 基本資訊 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">基本資訊</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">員工姓名:</span>
                      <span className="font-medium">{selectedRecord.employee_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">員工編號:</span>
                      <span className="font-medium">{selectedRecord.employee_code}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">部門:</span>
                      <span className="font-medium">{selectedRecord.department_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">工作日期:</span>
                      <span className="font-medium">{selectedRecord.work_date}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">星期:</span>
                      <span className="font-medium">{getWeekdayText(selectedRecord.weekday)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">日期類型:</span>
                      <span className="font-medium">
                        {selectedRecord.date_type === 'workday' ? '工作日' : '週末'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 考勤資訊 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">考勤資訊</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">班表:</span>
                      <span className="font-medium">{selectedRecord.shift_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">班表時間:</span>
                      <span className="font-medium">
                        {selectedRecord.shift_start_time} - {selectedRecord.shift_end_time}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">實際到班:</span>
                      <span className="font-medium">
                        {selectedRecord.check_in
                          ? new Date(selectedRecord.check_in).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' })
                          : '-'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">實際下班:</span>
                      <span className="font-medium">
                        {selectedRecord.check_out
                          ? new Date(selectedRecord.check_out).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' })
                          : '-'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">工作時數:</span>
                      <span className="font-medium">{selectedRecord.work_hours}小時</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">狀態:</span>
                      {selectedRecord.date_type === 'weekend' || selectedRecord.status === 'weekend' || selectedRecord.status === 'holiday' ? (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">
                          週末
                        </span>
                      ) : (
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${selectedRecord.status === 'normal' ? 'bg-green-100 text-green-800' :
                          selectedRecord.status === 'late' ? 'bg-yellow-100 text-yellow-800' :
                            selectedRecord.status === 'early_leave' ? 'bg-orange-100 text-orange-800' :
                              selectedRecord.status === 'overtime' ? 'bg-blue-100 text-blue-800' :
                                selectedRecord.status === 'leave' ? 'bg-purple-100 text-purple-800' :
                                  selectedRecord.status === 'absent' ? 'bg-red-100 text-red-800' :
                                    'bg-green-100 text-green-800'
                          }`}>
                          {selectedRecord.status === 'normal' ? '正常' :
                            selectedRecord.status === 'late' ? '遲到' :
                              selectedRecord.status === 'early_leave' ? '早退' :
                                selectedRecord.status === 'overtime' ? '加班' :
                                  selectedRecord.status === 'leave' ? '請假' :
                                    selectedRecord.status === 'absent' ? '曠職' : '正常'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* 異常統計 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">異常統計</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">遲到:</span>
                      <span className={`font-medium ${selectedRecord.late_minutes > 0 ? 'text-yellow-600' : ''}`}>
                        {selectedRecord.late_minutes > 0 ? `${selectedRecord.late_minutes}分鐘` : '無'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">早退:</span>
                      <span className={`font-medium ${selectedRecord.early_leave_minutes > 0 ? 'text-orange-600' : ''}`}>
                        {selectedRecord.early_leave_minutes > 0 ? `${selectedRecord.early_leave_minutes}分鐘` : '無'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">加班:</span>
                      <span className={`font-medium ${selectedRecord.overtime_minutes > 0 ? 'text-blue-600' : ''}`}>
                        {selectedRecord.overtime_minutes > 0 ? `${Math.round(selectedRecord.overtime_minutes / 60 * 100) / 100}小時` : '無'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">請假:</span>
                      <span className={`font-medium ${selectedRecord.leave_hours > 0 ? 'text-purple-600' : ''}`}>
                        {selectedRecord.leave_hours > 0 ? `${selectedRecord.leave_hours}小時` : '無'}
                      </span>
                    </div>
                    {selectedRecord.leave_type && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">請假類型:</span>
                        <span className="font-medium text-purple-600">{selectedRecord.leave_type}</span>
                      </div>
                    )}
                    {selectedRecord.leave_reason && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">請假原因:</span>
                        <span className="font-medium text-purple-600">{selectedRecord.leave_reason}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 備註 */}
                {selectedRecord.note && (
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">備註</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-700">{selectedRecord.note}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 模態框底部按鈕 */}
            <div className="bg-gray-50 px-6 py-4 border-t">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  關閉
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 考勤編輯模態框 */}
      {showEditModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-2xl shadow-2xl w-[90vw] max-w-5xl max-h-[90vh] overflow-hidden">
            {/* 模態框頭部 */}
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 px-6 py-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">編輯考勤記錄</h3>
                  <p className="text-yellow-100 text-sm">
                    {selectedRecord.employee_name} ({selectedRecord.employee_code}) - {selectedRecord.work_date}
                  </p>
                </div>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-lg transition-all duration-200"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* 模態框內容 */}
            <div className="p-6 max-h-[75vh] overflow-y-auto">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 左側：員工基本資訊 */}
                <div className="lg:col-span-1">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
                    <h4 className="text-lg font-semibold text-gray-900 border-b border-blue-200 pb-2 mb-4">員工資訊</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">姓名:</span>
                        <span className="font-medium">{selectedRecord.employee_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">員工編號:</span>
                        <span className="font-medium">{selectedRecord.employee_code}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">部門:</span>
                        <span className="font-medium">{selectedRecord.department_name || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">工作日期:</span>
                        <span className="font-medium">{selectedRecord.work_date}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">目前班表:</span>
                        <span className="font-medium text-blue-600">
                          {selectedRecord.shift_name || '未設定'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 當前統計 */}
                  <div className="mt-4 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl p-4 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 mb-4">當前統計</h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{selectedRecord.work_hours || 0}</div>
                        <div className="text-xs text-gray-600">工時</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600">{selectedRecord.late_minutes || 0}</div>
                        <div className="text-xs text-gray-600">遲到(分)</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">{selectedRecord.early_leave_minutes || 0}</div>
                        <div className="text-xs text-gray-600">早退(分)</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{Math.round((selectedRecord.overtime_minutes || 0) / 60 * 100) / 100}</div>
                        <div className="text-xs text-gray-600">加班(時)</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 右側：考勤編輯表單 */}
                <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 基本資訊 */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">基本資訊</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">員工姓名</label>
                        <input
                          type="text"
                          value={selectedRecord.employee_name}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">工作日期</label>
                        <input
                          type="text"
                          value={selectedRecord.work_date}
                          disabled
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">班表</label>
                        <select
                          value={editForm.shift_id}
                          onChange={(e) => updateEditForm('shift_id', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        >
                          <option value="">請選擇班表</option>
                          {shifts.map((shift) => (
                            <option key={shift.id} value={shift.id}>
                              {shift.name} ({shift.start_time} - {shift.end_time})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* 打卡時間 */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">打卡時間</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">上班打卡</label>
                        <input
                          type="datetime-local"
                          value={editForm.check_in}
                          onChange={(e) => updateEditForm('check_in', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">下班打卡</label>
                        <input
                          type="datetime-local"
                          value={editForm.check_out}
                          onChange={(e) => updateEditForm('check_out', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* 請假資訊 */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">請假資訊</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">請假類型</label>
                        <select
                          value={editForm.leave_type}
                          onChange={(e) => updateEditForm('leave_type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        >
                          <option value="">無請假</option>
                          {leaveTypes.map((type) => (
                            <option key={type.code} value={type.code}>
                              {type.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">請假時數</label>
                        <input
                          type="number"
                          step="0.5"
                          min="0"
                          max="24"
                          value={editForm.leave_hours}
                          onChange={(e) => updateEditForm('leave_hours', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">請假原因</label>
                        <textarea
                          rows={2}
                          value={editForm.leave_reason}
                          onChange={(e) => updateEditForm('leave_reason', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                          placeholder="請輸入請假原因..."
                        />
                      </div>
                    </div>
                  </div>

                  {/* 備註 */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 border-b pb-2">備註</h4>
                    <div>
                      <textarea
                        rows={3}
                        value={editForm.note}
                        onChange={(e) => updateEditForm('note', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                        placeholder="請輸入備註..."
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* 重新計算提示 */}
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Calculator className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h5 className="text-sm font-medium text-blue-900">自動重新計算</h5>
                    <p className="text-sm text-blue-700 mt-1">
                      儲存後系統將根據新的打卡時間、班表和請假資訊，自動重新計算：
                    </p>
                    <ul className="text-sm text-blue-600 mt-2 space-y-1">
                      <li>• 工作時數</li>
                      <li>• 遲到和早退時間</li>
                      <li>• 加班時數</li>
                      <li>• 請假時數統計</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 模態框底部按鈕 */}
            <div className="bg-gray-50 px-6 py-4 border-t">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200"
                >
                  儲存變更
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}