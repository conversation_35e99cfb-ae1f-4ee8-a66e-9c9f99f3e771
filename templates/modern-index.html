<!DOCTYPE html>
<html lang="zh-TW" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧考勤系統 - 現代化企業管理平台</title>

    <!-- 預載關鍵資源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- 現代化字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- 圖標庫 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.css">

    <!-- 現代化CSS框架 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace']
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    }
                }
            },
            darkMode: 'class'
        }
    </script>

    <!-- 自定義樣式 -->
    <style>
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        @media (max-width: 768px) {
            .sidebar-hidden {
                transform: translateX(-100%);
            }
        }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 font-sans antialiased">
    <!-- 載入畫面 -->
    <div id="loading-screen" class="fixed inset-0 z-50 flex items-center justify-center gradient-bg">
        <div class="text-center text-white">
            <div class="w-16 h-16 mx-auto mb-4 animate-pulse-slow">
                <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                </svg>
            </div>
            <h1 class="text-2xl font-bold mb-2">智慧考勤系統</h1>
            <p class="text-blue-100">正在載入中...</p>
        </div>
    </div>

    <!-- 主應用容器 -->
    <div id="app" class="min-h-screen flex" style="display: none;">
        <!-- 側邊欄 -->
        <aside id="sidebar" class="w-64 bg-white dark:bg-gray-800 shadow-lg sidebar-transition">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900 dark:text-white">智慧考勤</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">管理系統</p>
                    </div>
                </div>
            </div>

            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="nav-item active flex items-center space-x-3 px-4 py-3 rounded-lg bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300">
                            <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                            <span>儀表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="#attendance" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="clock" class="w-5 h-5"></i>
                            <span>考勤打卡</span>
                        </a>
                    </li>
                    <li>
                        <a href="#schedules" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="calendar" class="w-5 h-5"></i>
                            <span>排班管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#leaves" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="calendar-x" class="w-5 h-5"></i>
                            <span>請假管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#employees" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="users" class="w-5 h-5"></i>
                            <span>員工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#reports" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                            <span>統計報表</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主內容區 -->
        <main class="flex-1 flex flex-col">
            <!-- 頂部導航 -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <button id="sidebar-toggle" class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="menu" class="w-5 h-5"></i>
                        </button>
                        <h2 id="page-title" class="text-xl font-semibold text-gray-900 dark:text-white">儀表板</h2>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- 搜尋框 -->
                        <div class="relative hidden md:block">
                            <input type="text" placeholder="搜尋..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <i data-lucide="search" class="absolute left-3 top-2.5 w-4 h-4 text-gray-400"></i>
                        </div>

                        <!-- 通知 -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="bell" class="w-5 h-5 text-gray-600 dark:text-gray-400"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </button>

                        <!-- 主題切換 -->
                        <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i data-lucide="sun" class="w-5 h-5 text-gray-600 dark:text-gray-400 dark:hidden"></i>
                            <i data-lucide="moon" class="w-5 h-5 text-gray-600 dark:text-gray-400 hidden dark:block"></i>
                        </button>

                        <!-- 用戶選單 -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">管</span>
                                </div>
                                <span class="hidden md:block text-gray-700 dark:text-gray-300">系統管理員</span>
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 頁面內容 -->
            <div id="page-content" class="flex-1 p-6 overflow-auto">
                <!-- 儀表板內容 -->
                <div id="dashboard-content" class="space-y-6">
                    <!-- 統計卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">今日出勤</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
                                    <p class="text-sm text-green-600">+12% 較昨日</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                                    <i data-lucide="users" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">遲到人數</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">8</p>
                                    <p class="text-sm text-red-600">+2 較昨日</p>
                                </div>
                                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                                    <i data-lucide="clock" class="w-6 h-6 text-red-600 dark:text-red-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">請假申請</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">12</p>
                                    <p class="text-sm text-yellow-600">3 待審批</p>
                                </div>
                                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                                    <i data-lucide="calendar-x" class="w-6 h-6 text-yellow-600 dark:text-yellow-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">部門數量</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">8</p>
                                    <p class="text-sm text-blue-600">4 個活躍</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                    <i data-lucide="building" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">快速操作</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button class="flex flex-col items-center p-4 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">
                                <i data-lucide="clock-in" class="w-8 h-8 text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">上班打卡</span>
                            </button>
                            <button class="flex flex-col items-center p-4 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">
                                <i data-lucide="clock-out" class="w-8 h-8 text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">下班打卡</span>
                            </button>
                            <button class="flex flex-col items-center p-4 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">
                                <i data-lucide="calendar-plus" class="w-8 h-8 text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">申請請假</span>
                            </button>
                            <button class="flex flex-col items-center p-4 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">
                                <i data-lucide="download" class="w-8 h-8 text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">匯出報表</span>
                            </button>
                        </div>
                    </div>

                    <!-- 最近活動 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近考勤記錄</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">張小明</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">09:00 上班打卡</p>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">剛剛</span>
                                </div>
                                <!-- 更多記錄... -->
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">待處理事項</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                                            <i data-lucide="clock" class="w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">李小華的請假申請</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">病假 2024/05/29-30</p>
                                        </div>
                                    </div>
                                    <button class="text-xs bg-primary-500 text-white px-3 py-1 rounded-full hover:bg-primary-600">審批</button>
                                </div>
                                <!-- 更多待處理事項... -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        // 初始化圖標
        lucide.createIcons();

        // 載入完成
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('app').style.display = 'flex';
                document.getElementById('app').classList.add('animate-fade-in');
            }, 1000);
        });

        // 側邊欄切換
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('sidebar-hidden');
        });

        // 主題切換
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');

            if (isDark) {
                html.classList.remove('dark');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // 載入保存的主題
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.documentElement.setAttribute('data-theme', 'dark');
        }

        // 導航功能
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有活躍狀態
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active', 'bg-primary-50', 'text-primary-700', 'dark:bg-primary-900', 'dark:text-primary-300');
                    nav.classList.add('text-gray-700', 'dark:text-gray-300');
                });

                // 添加活躍狀態
                this.classList.add('active', 'bg-primary-50', 'text-primary-700', 'dark:bg-primary-900', 'dark:text-primary-300');
                this.classList.remove('text-gray-700', 'dark:text-gray-300');

                // 更新頁面標題
                const pageTitle = this.querySelector('span').textContent;
                document.getElementById('page-title').textContent = pageTitle;
            });
        });
    </script>
</body>

</html>