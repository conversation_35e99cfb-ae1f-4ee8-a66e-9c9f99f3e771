# AttendanceOS 排班管理規則文件

## 📋 排班系統概述

AttendanceOS 排班系統提供靈活的班別管理和智能加班計算功能，支援多種工作模式和自訂排班需求。系統具備完整的跨日考勤處理能力，能夠智能判斷打卡歸屬日期。

## 🌅 跨日考勤邏輯

### 換日時間設定
系統支援自訂換日時間，預設為早上 **06:00**。此設定決定了打卡記錄的歸屬日期。

**基本原則：**
- 在換日時間**之前**的打卡，歸屬於**前一個工作日**
- 在換日時間**之後**的打卡，歸屬於**當前工作日**

### 跨日考勤實例

**情境1：夜班員工**
```
班別：夜班（22:00-06:00）
換日時間：06:00
工作日：2024-06-01

打卡記錄：
- 2024-06-01 21:50 → 歸屬於 2024-06-01（上班前加班）
- 2024-06-01 22:00 → 歸屬於 2024-06-01（正常上班）
- 2024-06-02 02:00 → 歸屬於 2024-06-01（休息時間）
- 2024-06-02 05:30 → 歸屬於 2024-06-01（下班前）
- 2024-06-02 06:30 → 歸屬於 2024-06-01（下班後加班）

結果：所有打卡都歸屬於 2024-06-01 工作日
```

**情境2：跨日加班**
```
班別：標準日班（08:30-17:30）
換日時間：06:00
工作日：2024-06-01

打卡記錄：
- 2024-06-01 08:00 → 歸屬於 2024-06-01（上班前加班）
- 2024-06-01 08:30 → 歸屬於 2024-06-01（正常上班）
- 2024-06-02 02:00 → 歸屬於 2024-06-01（深夜加班）

結果：深夜加班正確歸屬於前一工作日
```

**情境3：早班員工**
```
班別：早班（06:00-14:00）
換日時間：06:00
工作日：2024-06-01

打卡記錄：
- 2024-06-01 05:30 → 歸屬於 2024-05-31（前一日的加班）
- 2024-06-01 06:00 → 歸屬於 2024-06-01（正常上班）
- 2024-06-01 14:00 → 歸屬於 2024-06-01（正常下班）

注意：05:30 的打卡會歸屬於前一日
```

### 智能打卡判斷

系統採用「第一筆上班，最後一筆下班」的邏輯：

1. **當日第一筆打卡** → 自動判定為上班
2. **當日最後一筆打卡** → 自動判定為下班
3. **中間打卡** → 根據前一筆記錄狀態判斷

**打卡流程範例：**
```
工作日：2024-06-01
換日時間：06:00

時間軸：
2024-06-01 07:30 → 第1筆：上班 ✓
2024-06-01 12:00 → 第2筆：外出（可選）
2024-06-01 13:00 → 第3筆：回來（可選）
2024-06-01 18:30 → 第4筆：下班 ✓
2024-06-02 01:00 → 第5筆：加班結束 ✓（歸屬2024-06-01）

系統判斷：
- 07:30 = 上班時間
- 18:30 = 正常下班時間  
- 01:00 = 最終下班時間（更新下班記錄）
```

## 🕐 班別定義規則

### 班別基本參數
每個班別包含以下可設定參數：

1. **基本資訊**
   - 班別名稱：可自訂任意名稱（如：早班、晚班、夜班、彈性班等）
   - 班別代碼：系統內部識別碼
   - 顏色代碼：用於排班表視覺區分

2. **工作時間設定**
   - 上班時間：班別開始時間
   - 下班時間：班別結束時間
   - 休息開始時間：中間休息時段開始時間
   - 休息時長：休息時間長度（分鐘）

3. **加班設定**
   - 上班前加班門檻：提前多少分鐘開始計算上班前加班
   - 下班後加班門檻：延後多少分鐘開始計算下班後加班
   - 啟用上班前加班：是否計算上班前加班
   - 啟用下班後加班：是否計算下班後加班
   - 自動計算加班：是否自動計算加班時數

## ⏰ 加班計算規則

### 1. 上班前加班計算

**規則說明：**
- 只有在班別設定中啟用「上班前加班」時才會計算
- 員工實際到達時間早於「上班時間 - 上班前加班門檻」時開始計算

**計算範例：**
```
班別設定：
- 上班時間：08:00
- 上班前加班門檻：60分鐘
- 啟用上班前加班：是

計算邏輯：
- 如果員工 07:00 到達 → 上班前加班 1小時
- 如果員工 07:30 到達 → 上班前加班 0.5小時
- 如果員工 08:00 到達 → 無上班前加班
- 如果員工 08:30 到達 → 無上班前加班（遲到）

特殊情況：
- 如果「啟用上班前加班」設為否，即使 06:00 到達也不計算加班
```

### 2. 下班後加班計算

**規則說明：**
- 只有在班別設定中啟用「下班後加班」時才會計算
- 員工實際離開時間晚於「下班時間 + 下班後加班門檻」時開始計算

**計算範例：**
```
班別設定：
- 下班時間：17:00
- 下班後加班門檻：30分鐘
- 啟用下班後加班：是

計算邏輯：
- 如果員工 17:00 離開 → 無下班後加班
- 如果員工 17:20 離開 → 無下班後加班（在門檻內）
- 如果員工 17:30 離開 → 無下班後加班（剛好門檻）
- 如果員工 18:00 離開 → 下班後加班 0.5小時
- 如果員工 20:00 離開 → 下班後加班 2.5小時

特殊情況：
- 如果「啟用下班後加班」設為否，即使工作到深夜也不計算加班
```

### 3. 休息時間扣除規則

**基本原則：**
- 休息時間不計入工作時數
- 加班時數計算需扣除休息時間

**計算範例：**
```
班別設定：
- 上班時間：08:00
- 下班時間：20:00（總計12小時）
- 休息時間：12:00-13:00（1小時）
- 下班後加班門檻：30分鐘

實際工作：
- 到達時間：08:00
- 離開時間：20:00
- 實際工作時數：12小時 - 1小時休息 = 11小時
- 正常工作時數：8小時（17:00-08:00，扣除1小時休息）
- 下班後加班：20:00 - 17:30 = 2.5小時
```

### 4. 複雜情況處理

**跨日班別（如夜班）：**
```
班別設定：
- 上班時間：22:00
- 下班時間：06:00（次日）
- 休息時間：02:00-02:30（30分鐘）

計算邏輯：
- 工作時數：8小時 - 0.5小時休息 = 7.5小時
- 跨日處理：系統自動識別跨日情況
```

**彈性班別：**
```
班別設定：
- 彈性班別
- 啟用上班前加班：否
- 啟用下班後加班：否

特點：
- 不計算任何加班
- 適用於彈性工作制員工
- 只記錄實際工作時間
```

## 🔧 系統設定參數

### 全域規則設定
```
min_rest_hours: 8              # 兩班次間最少休息時間
max_weekly_hours: 40           # 每週最大工作時數
max_monthly_overtime: 46       # 每月最大加班時數
overtime_rate_normal: 1.34     # 平日加班費率
overtime_rate_holiday: 2.0     # 假日加班費率
late_tolerance_minutes: 10     # 遲到容忍時間
early_leave_tolerance_minutes: 10  # 早退容忍時間
auto_clock_out_hours: 12       # 自動下班打卡時間
break_time_deduction: 1        # 是否扣除休息時間
weekend_overtime_auto: 1       # 週末是否自動計算加班
```

## 📊 排班模板系統

### 模板類型
1. **標準週班表**：固定週一到週五的排班
2. **輪班模式**：多班次輪替排班
3. **彈性排班**：根據需求靈活安排
4. **專案排班**：特定專案的臨時排班

### 模板應用規則
- 模板可套用至個人或部門
- 支援批量排班操作
- 可設定例外日期處理
- 自動處理國定假日調整

## 🎯 加班計算實例

### 實例1：標準日班加班
```
班別：標準日班
設定：08:30-17:30，休息12:00-13:00，上班前加班門檻60分鐘，下班後加班門檻30分鐘

員工實際：07:30到達，19:00離開

計算過程：
1. 上班前加班：08:30 - 07:30 = 1小時（超過門檻）
2. 正常工作：08:30-17:30 = 9小時 - 1小時休息 = 8小時
3. 下班後加班：19:00 - 18:00 = 1小時（18:00 = 17:30 + 30分鐘門檻）
4. 總加班時數：1 + 1 = 2小時
```

### 實例2：夜班跨日加班
```
班別：夜班
設定：22:00-06:00，休息02:00-02:30，啟用前後加班

員工實際：21:00到達，07:30離開

計算過程：
1. 上班前加班：22:00 - 21:00 = 1小時
2. 正常工作：22:00-06:00 = 8小時 - 0.5小時休息 = 7.5小時
3. 下班後加班：07:30 - 06:00 = 1.5小時
4. 總加班時數：1 + 1.5 = 2.5小時
```

### 實例3：彈性班別
```
班別：彈性班
設定：09:00-18:00，不啟用加班計算

員工實際：07:00到達，21:00離開

計算過程：
1. 上班前加班：0小時（未啟用）
2. 正常工作：記錄實際工作時間
3. 下班後加班：0小時（未啟用）
4. 總加班時數：0小時
```

## 🚨 特殊情況處理

### 1. 忘記打卡
- 系統提供手動補卡功能
- 需要主管審核確認
- 記錄補卡原因和時間

### 2. 外出公務
- 可設定外出時間段
- 外出時間計入正常工作時數
- 需要事前申請或事後補登

### 3. 國定假日
- 國定假日工作自動計算為加班
- 加班費率使用假日費率
- 可設定補休或加班費選擇

### 4. 請假期間
- 請假時間不計入工作時數
- 部分請假日的排班自動調整
- 支援小時級請假計算

## 📈 報表統計

### 加班統計報表
- 個人加班時數統計
- 部門加班時數分析
- 月度/年度加班趨勢
- 加班費用計算報表

### 排班效率分析
- 班別使用率統計
- 人力配置分析
- 排班衝突檢測
- 工時分佈報告

## 🔄 系統整合

### 與考勤系統整合
- 自動讀取打卡記錄
- 智能計算工作時數
- 異常情況自動標記
- 實時更新排班狀態

### 與薪資系統整合
- 自動計算加班費
- 生成薪資計算基礎資料
- 支援多種薪資結構
- 提供詳細計算明細

---

**📝 注意事項：**
1. 所有時間計算精確到分鐘
2. 加班時數四捨五入至小數點後一位
3. 系統自動處理夏令時間調整
4. 支援多時區排班管理
5. 所有規則變更需要管理員權限
6. 歷史資料不受規則變更影響 