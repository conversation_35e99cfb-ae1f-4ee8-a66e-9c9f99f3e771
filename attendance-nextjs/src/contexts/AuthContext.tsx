"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import type { User } from '@/types'
import { verifySession } from '@/lib/api-client'

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (user: User) => void
  logout: () => void
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)
  const router = useRouter()

  // 確保組件已掛載，避免 hydration 錯誤
  useEffect(() => {
    setMounted(true)
  }, [])

  // 檢查本地存儲中的用戶資訊
  useEffect(() => {
    if (!mounted) return // 只有在組件掛載後才執行

    const initAuth = async () => {
      try {
        console.log('開始認證初始化')
        // 首先檢查 localStorage
        const storedUser = localStorage.getItem('user')
        console.log('本地存儲的用戶資料:', storedUser)

        if (storedUser) {
          const userData = JSON.parse(storedUser)
          console.log('解析的用戶資料:', userData)
          setUser(userData)

          // 簡化驗證 - 暫時跳過會話驗證，專注解決跳轉問題
          console.log('用戶已設定，跳過會話驗證')
        } else {
          console.log('沒有本地用戶資料')
        }
      } catch (error) {
        console.error('認證初始化失敗:', error)
        if (typeof window !== 'undefined') {
          localStorage.removeItem('user')
        }
        setUser(null)
      } finally {
        console.log('認證初始化完成，設定 loading = false')
        setLoading(false)
      }
    }

    initAuth()
  }, [mounted])

  const login = (userData: User) => {
    console.log('AuthContext login 被調用:', userData)
    setUser(userData)
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(userData))
    }
    console.log('AuthContext 用戶狀態已更新')
  }

  const logout = () => {
    setUser(null)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user')
    }
    router.push('/login')
  }

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 保護路由的 HOC
export function withAuth<T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function AuthenticatedComponent(props: T) {
    const { isAuthenticated, loading } = useAuth()
    const router = useRouter()

    console.log('withAuth 檢查:', { loading, isAuthenticated })

    useEffect(() => {
      if (!loading && !isAuthenticated) {
        console.log('withAuth: 未認證，導向登入頁面')
        router.push('/login')
      }
    }, [isAuthenticated, loading, router])

    if (loading) {
      console.log('withAuth: 載入中')
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      )
    }

    if (!isAuthenticated) {
      console.log('withAuth: 未認證，返回 null')
      return null
    }

    console.log('withAuth: 已認證，渲染組件')
    return <Component {...props} />
  }
}