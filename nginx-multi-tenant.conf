events {
    worker_connections 1024;
}

http {
    upstream attendance_backend {
        least_conn;
        server attendance_app:5000 max_fails=3 fail_timeout=30s;
    }

    # 日誌格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'company_id="$company_id"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本設定
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 多租戶主配置
    server {
        listen 80;
        server_name *.attendance.local attendance.local;

        # 設定公司ID變數
        set $company_id "default";
        
        # 從subdomain提取公司ID
        if ($host ~* "^([^.]+)\.attendance\.local$") {
            set $company_id $1;
        }

        # 從路徑提取公司ID
        if ($uri ~* "^/company/([^/]+)") {
            set $company_id $1;
        }

        # 靜態文件快取
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Company-ID $company_id;
        }

        # API請求
        location /api/ {
            proxy_pass http://attendance_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Company-ID $company_id;
            
            # 超時設定
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 主要應用
        location / {
            proxy_pass http://attendance_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Company-ID $company_id;
            
            # WebSocket支援
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 健康檢查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }

    # 管理介面 (可選)
    server {
        listen 80;
        server_name admin.attendance.local;

        location / {
            proxy_pass http://attendance_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Company-ID "admin";
            
            # 基本認證 (可選)
            # auth_basic "Admin Area";
            # auth_basic_user_file /etc/nginx/.htpasswd;
        }
    }
} 