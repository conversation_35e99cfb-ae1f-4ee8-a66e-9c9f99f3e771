-- 為班表新增當天起算時間欄位
-- 此欄位用於判斷跨日打卡的歸屬日期

ALTER TABLE shifts ADD COLUMN day_start_time TEXT DEFAULT '06:00';

-- 更新現有班表的預設值
UPDATE shifts SET day_start_time = '06:00' WHERE day_start_time IS NULL;

-- 為不同班別設定合適的起算時間
UPDATE shifts SET day_start_time = '05:00' WHERE name LIKE '%早班%';
UPDATE shifts SET day_start_time = '14:00' WHERE name LIKE '%晚班%';
UPDATE shifts SET day_start_time = '22:00' WHERE name LIKE '%夜班%';

-- 新增註釋說明
PRAGMA table_info(shifts); 