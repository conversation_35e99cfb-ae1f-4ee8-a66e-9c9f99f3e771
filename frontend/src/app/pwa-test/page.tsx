"use client"

import { useState, useEffect } from 'react'
import {
    Smartphone,
    Download,
    Wifi,
    WifiOff,
    CheckCircle,
    XCircle,
    Monitor,
    Globe,
    Settings,
    RefreshCw
} from 'lucide-react'

export default function PWATestPage() {
    const [isOnline, setIsOnline] = useState(true)
    const [isStandalone, setIsStandalone] = useState(false)
    const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null)
    const [installPrompt, setInstallPrompt] = useState<any>(null)
    const [cacheStatus, setCacheStatus] = useState<string>('檢查中...')

    useEffect(() => {
        // 檢測網路狀態
        setIsOnline(navigator.onLine)

        const handleOnline = () => setIsOnline(true)
        const handleOffline = () => setIsOnline(false)

        window.addEventListener('online', handleOnline)
        window.addEventListener('offline', handleOffline)

        // 檢測是否為獨立模式
        const standalone = window.matchMedia('(display-mode: standalone)').matches
        setIsStandalone(standalone)

        // 檢查Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration().then((registration) => {
                setSwRegistration(registration || null)
            })
        }

        // 監聽安裝提示
        const handleBeforeInstallPrompt = (e: any) => {
            e.preventDefault()
            setInstallPrompt(e)
        }

        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

        // 檢查緩存狀態
        checkCacheStatus()

        return () => {
            window.removeEventListener('online', handleOnline)
            window.removeEventListener('offline', handleOffline)
            window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
        }
    }, [])

    const checkCacheStatus = async () => {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys()
                setCacheStatus(`已緩存 ${cacheNames.length} 個緩存組`)
            } else {
                setCacheStatus('不支援緩存API')
            }
        } catch (error) {
            setCacheStatus('緩存檢查失敗')
        }
    }

    const handleInstall = async () => {
        if (installPrompt) {
            try {
                await installPrompt.prompt()
                const { outcome } = await installPrompt.userChoice
                console.log('安裝結果:', outcome)
                setInstallPrompt(null)
            } catch (error) {
                console.error('安裝失敗:', error)
            }
        }
    }

    const testOfflineMode = () => {
        // 模擬離線測試
        alert('請關閉網路連線後重新載入頁面來測試離線功能')
    }

    const clearCache = async () => {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys()
                await Promise.all(cacheNames.map(name => caches.delete(name)))
                setCacheStatus('緩存已清除')
                alert('緩存已清除，請重新載入頁面')
            }
        } catch (error) {
            alert('清除緩存失敗')
        }
    }

    const StatusCard = ({
        title,
        status,
        icon: Icon,
        description,
        isGood
    }: {
        title: string
        status: string
        icon: any
        description: string
        isGood: boolean
    }) => (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
            <div className="flex items-center space-x-3 mb-3">
                <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${isGood ? 'bg-green-100' : 'bg-red-100'
                    }`}>
                    <Icon className={`w-5 h-5 ${isGood ? 'text-green-600' : 'text-red-600'}`} />
                </div>
                <div>
                    <h3 className="font-bold text-gray-900">{title}</h3>
                    <p className={`text-sm ${isGood ? 'text-green-600' : 'text-red-600'}`}>
                        {status}
                    </p>
                </div>
            </div>
            <p className="text-gray-600 text-sm">{description}</p>
        </div>
    )

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
            {/* 🔙 返回按鈕 */}
            <div className="fixed top-4 left-4 z-50">
                <button
                    onClick={() => window.history.back()}
                    className="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                    <span>←</span>
                    <span>返回</span>
                </button>
            </div>

            <div className="pt-16 max-w-4xl mx-auto">
                {/* 🎨 頁面標題 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold mb-2">PWA 功能測試</h1>
                            <p className="text-indigo-100">檢測漸進式網頁應用程式功能狀態</p>
                        </div>
                        <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                            <Settings className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>

                {/* PWA 狀態檢測 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <StatusCard
                        title="網路連線"
                        status={isOnline ? '已連線' : '離線模式'}
                        icon={isOnline ? Wifi : WifiOff}
                        description={isOnline ? '網路連線正常，可以同步數據' : '離線模式，使用緩存數據'}
                        isGood={isOnline}
                    />

                    <StatusCard
                        title="安裝狀態"
                        status={isStandalone ? '已安裝' : '瀏覽器模式'}
                        icon={isStandalone ? CheckCircle : Monitor}
                        description={isStandalone ? '應用已安裝到設備' : '在瀏覽器中運行'}
                        isGood={isStandalone}
                    />

                    <StatusCard
                        title="Service Worker"
                        status={swRegistration ? '已註冊' : '未註冊'}
                        icon={swRegistration ? CheckCircle : XCircle}
                        description={swRegistration ? '離線功能可用' : 'Service Worker 未啟用'}
                        isGood={!!swRegistration}
                    />

                    <StatusCard
                        title="緩存狀態"
                        status={cacheStatus}
                        icon={Globe}
                        description="應用資源緩存狀態"
                        isGood={cacheStatus.includes('已緩存')}
                    />
                </div>

                {/* 功能測試按鈕 */}
                <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 mb-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">功能測試</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* 安裝測試 */}
                        {installPrompt && !isStandalone && (
                            <button
                                onClick={handleInstall}
                                className="flex items-center space-x-3 bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg"
                            >
                                <Download className="w-5 h-5" />
                                <div className="text-left">
                                    <div className="font-medium">安裝應用</div>
                                    <div className="text-sm text-green-100">將應用安裝到設備</div>
                                </div>
                            </button>
                        )}

                        {/* 離線測試 */}
                        <button
                            onClick={testOfflineMode}
                            className="flex items-center space-x-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg"
                        >
                            <WifiOff className="w-5 h-5" />
                            <div className="text-left">
                                <div className="font-medium">測試離線模式</div>
                                <div className="text-sm text-blue-100">檢查離線功能</div>
                            </div>
                        </button>

                        {/* 清除緩存 */}
                        <button
                            onClick={clearCache}
                            className="flex items-center space-x-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg"
                        >
                            <RefreshCw className="w-5 h-5" />
                            <div className="text-left">
                                <div className="font-medium">清除緩存</div>
                                <div className="text-sm text-orange-100">重置應用緩存</div>
                            </div>
                        </button>

                        {/* 重新載入 */}
                        <button
                            onClick={() => window.location.reload()}
                            className="flex items-center space-x-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
                        >
                            <RefreshCw className="w-5 h-5" />
                            <div className="text-left">
                                <div className="font-medium">重新載入</div>
                                <div className="text-sm text-purple-100">刷新應用狀態</div>
                            </div>
                        </button>
                    </div>
                </div>

                {/* PWA 功能說明 */}
                <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">PWA 功能說明</h2>

                    <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <Smartphone className="w-3 h-3 text-blue-600" />
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900">應用安裝</h3>
                                <p className="text-gray-600 text-sm">可以將考勤系統安裝到手機或電腦桌面，像原生應用一樣使用</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <WifiOff className="w-3 h-3 text-green-600" />
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900">離線功能</h3>
                                <p className="text-gray-600 text-sm">即使沒有網路連線，也可以查看已緩存的考勤記錄和基本功能</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <Globe className="w-3 h-3 text-purple-600" />
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900">快速載入</h3>
                                <p className="text-gray-600 text-sm">應用資源會被緩存，第二次開啟時載入速度更快</p>
                            </div>
                        </div>

                        <div className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <CheckCircle className="w-3 h-3 text-orange-600" />
                            </div>
                            <div>
                                <h3 className="font-medium text-gray-900">自動更新</h3>
                                <p className="text-gray-600 text-sm">應用會自動檢查並下載更新，確保使用最新版本</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 