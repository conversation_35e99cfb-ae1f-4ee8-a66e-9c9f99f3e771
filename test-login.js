// Han AttendanceOS 登入功能測試腳本
// 使用 Node.js 和 fetch API 來模擬前端登入流程

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:7072';
const FRONTEND_URL = 'http://localhost:7075';

// 測試配置
const TEST_ACCOUNTS = {
    admin: {
        employee_id: 'admin',
        password: 'admin123',
        expected_role: 999,
        description: '系統管理員'
    },
    employee: {
        employee_id: 'E001',
        password: 'password123',
        expected_role: 2,
        description: '一般員工'
    }
};

// 顏色輸出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 測試前端頁面可訪問性
async function testFrontendPages() {
    log('\n🌐 測試前端頁面可訪問性', 'cyan');
    
    const pages = [
        { url: `${FRONTEND_URL}/admin`, name: '管理後台' },
        { url: `${FRONTEND_URL}/m`, name: '員工介面' },
        { url: `${FRONTEND_URL}/admin/login`, name: '管理員登入頁' },
        { url: `${FRONTEND_URL}/m/login`, name: '員工登入頁' }
    ];
    
    for (const page of pages) {
        try {
            const response = await fetch(page.url);
            if (response.ok) {
                log(`✅ ${page.name}: ${response.status}`, 'green');
            } else {
                log(`❌ ${page.name}: ${response.status}`, 'red');
            }
        } catch (error) {
            log(`❌ ${page.name}: 連接失敗 - ${error.message}`, 'red');
        }
    }
}

// 測試 API 健康檢查
async function testAPIHealth() {
    log('\n🔍 測試 API 健康檢查', 'cyan');
    
    try {
        const response = await fetch(`${BASE_URL}/api/health`);
        const data = await response.json();
        
        if (response.ok && data.status === 'healthy') {
            log('✅ API 健康檢查通過', 'green');
            log(`   版本: ${data.version}`, 'blue');
            log(`   資料庫: ${data.checks.database.status}`, 'blue');
            log(`   記憶體使用: ${data.checks.memory.usage_percent}%`, 'blue');
            log(`   磁碟空間: ${data.checks.disk.free_space_gb}GB`, 'blue');
        } else {
            log('❌ API 健康檢查失敗', 'red');
        }
    } catch (error) {
        log(`❌ API 健康檢查錯誤: ${error.message}`, 'red');
    }
}

// 測試登入功能
async function testLogin(accountType) {
    const account = TEST_ACCOUNTS[accountType];
    log(`\n🔐 測試${account.description}登入`, 'cyan');
    
    try {
        const response = await fetch(`${BASE_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                employee_id: account.employee_id,
                password: account.password
            })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            const user = data.data.user;
            log(`✅ 登入成功`, 'green');
            log(`   用戶: ${user.employee_name} (${user.employee_code})`, 'blue');
            log(`   部門: ${user.department_name}`, 'blue');
            log(`   角色ID: ${user.role_id}`, 'blue');
            log(`   權限: ${user.permissions.join(', ')}`, 'blue');
            
            // 驗證角色
            if (user.role_id === account.expected_role) {
                log(`✅ 角色驗證通過`, 'green');
            } else {
                log(`❌ 角色驗證失敗: 期望 ${account.expected_role}, 實際 ${user.role_id}`, 'red');
            }
            
            return { success: true, user };
        } else {
            log(`❌ 登入失敗: ${data.error || data.message}`, 'red');
            return { success: false, error: data.error };
        }
    } catch (error) {
        log(`❌ 登入請求錯誤: ${error.message}`, 'red');
        return { success: false, error: error.message };
    }
}

// 測試錯誤登入
async function testInvalidLogin() {
    log('\n🚫 測試錯誤登入', 'cyan');
    
    const invalidCredentials = [
        { employee_id: 'invalid', password: 'invalid', description: '無效帳號' },
        { employee_id: 'admin', password: 'wrongpassword', description: '錯誤密碼' },
        { employee_id: '', password: 'admin123', description: '空帳號' },
        { employee_id: 'admin', password: '', description: '空密碼' }
    ];
    
    for (const cred of invalidCredentials) {
        try {
            const response = await fetch(`${BASE_URL}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_id: cred.employee_id,
                    password: cred.password
                })
            });
            
            const data = await response.json();
            
            if (!data.success) {
                log(`✅ ${cred.description}: 正確拒絕登入`, 'green');
            } else {
                log(`❌ ${cred.description}: 意外允許登入`, 'red');
            }
        } catch (error) {
            log(`❌ ${cred.description}: 請求錯誤 - ${error.message}`, 'red');
        }
    }
}

// 主測試函數
async function runTests() {
    log('🧪 Han AttendanceOS 登入功能測試開始', 'magenta');
    log('=' * 60, 'magenta');
    
    // 測試前端頁面
    await testFrontendPages();
    
    // 測試 API 健康檢查
    await testAPIHealth();
    
    // 測試管理員登入
    const adminResult = await testLogin('admin');
    
    // 測試員工登入
    const employeeResult = await testLogin('employee');
    
    // 測試錯誤登入
    await testInvalidLogin();
    
    // 總結報告
    log('\n📊 測試總結', 'magenta');
    log('=' * 60, 'magenta');
    
    const results = {
        admin: adminResult?.success || false,
        employee: employeeResult?.success || false
    };
    
    if (results.admin && results.employee) {
        log('🎉 所有登入測試通過！', 'green');
    } else {
        log('⚠️  部分測試失敗，請檢查問題', 'yellow');
        if (!results.admin) log('   - 管理員登入失敗', 'red');
        if (!results.employee) log('   - 員工登入失敗', 'red');
    }
    
    log('\n✅ 測試完成', 'cyan');
}

// 執行測試
if (require.main === module) {
    runTests().catch(error => {
        log(`❌ 測試執行錯誤: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = { runTests, testLogin, testAPIHealth };
