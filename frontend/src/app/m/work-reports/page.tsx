'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import {
    ArrowLeft,
    Plus,
    FileText,
    Calendar,
    Clock,
    Camera,
    Send,
    Eye,
    MessageSquare,
    CheckCircle,
    AlertCircle,
    User,
    X,
    Upload,
    Image as ImageIcon,
    Trash2
} from 'lucide-react'

interface WorkReport {
    id: number
    employee_id: string
    report_date: string
    report_time: string
    category: string
    sub_category?: string
    content: string
    photos: string[]
    is_read: number
    supervisor_feedback: string | null
    feedback_date: string | null
    created_at: string
}

export default function WorkReportsPage() {
    const { user, loading: authLoading } = useAuth()
    const router = useRouter()
    const [reports, setReports] = useState<WorkReport[]>([])
    const [loading, setLoading] = useState(true)
    const [showCreateModal, setShowCreateModal] = useState(false)
    const [reportForm, setReportForm] = useState({
        category: 'work_report',
        content: '',
        photos: [] as string[],
        subCategory: '' // 新增子類別欄位
    })
    const [selectedReport, setSelectedReport] = useState<WorkReport | null>(null)
    const [showDetailModal, setShowDetailModal] = useState(false)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [reportToDelete, setReportToDelete] = useState<WorkReport | null>(null)
    const [showPhotoModal, setShowPhotoModal] = useState(false)
    const [selectedPhoto, setSelectedPhoto] = useState<string>('')
    const fileInputRef = useRef<HTMLInputElement>(null)
    const [uploadedFiles, setUploadedFiles] = useState<File[]>([])

    const [notification, setNotification] = useState<{
        show: boolean
        type: 'success' | 'error' | 'info'
        title: string
        message: string
    }>({
        show: false,
        type: 'success',
        title: '',
        message: ''
    })

    // 顯示通知
    const showNotification = (type: 'success' | 'error' | 'info', title: string, message: string) => {
        setNotification({
            show: true,
            type,
            title,
            message
        })

        // 3秒後自動隱藏
        setTimeout(() => {
            setNotification(prev => ({ ...prev, show: false }))
        }, 3000)
    }

    // 🌐 獲取API基礎URL
    const getApiBaseUrl = () => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname
            console.log('🌐 員工工作回報頁面檢測到的主機:', hostname)

            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:7072'
            } else {
                const apiUrl = `http://${hostname}:7072`
                console.log('📱 員工工作回報頁面使用API URL:', apiUrl)
                return apiUrl
            }
        }
        return 'http://localhost:7072'
    }

    // 載入我的工作回報
    const loadMyReports = async () => {
        if (!user?.employee_id) {
            console.log('用戶資訊未載入，跳過載入工作回報')
            setLoading(false)
            return
        }

        try {
            console.log('開始載入工作回報，員工ID:', user.employee_id)
            setLoading(true)
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports?employee_id=${user.employee_id}`
            console.log('📋 載入我的工作回報 URL:', url)

            const response = await fetch(url)
            console.log('API響應狀態:', response.status)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result = await response.json()
            console.log('API響應結果:', result)

            if (result.success) {
                setReports(result.data || [])
                console.log('工作回報載入成功，數量:', result.data?.length || 0)
            } else {
                console.error('API返回失敗:', result.message)
                showNotification('error', '載入失敗', result.message || '無法載入工作回報')
            }
        } catch (error) {
            console.error('載入工作回報失敗:', error)
            showNotification('error', '網路錯誤', '載入工作回報失敗，請檢查網路連線')
        } finally {
            setLoading(false)
        }
    }

    // 處理文件選擇
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files
        if (files) {
            handleFiles(Array.from(files))
        }
    }

    // 手機版不需要拖拽功能，已移除相關函數

    // 刪除工作回報
    const deleteReport = async (reportId: number) => {
        try {
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports/${reportId}`
            console.log('🗑️ 刪除工作回報 URL:', url)

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            console.log('刪除API響應狀態:', response.status)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result = await response.json()
            console.log('刪除響應結果:', result)

            if (result.success) {
                showNotification('success', '刪除成功', '工作回報已成功刪除')
                // 重新載入回報列表
                loadMyReports()
            } else {
                showNotification('error', '刪除失敗', result.message || '未知錯誤，請稍後再試')
            }
        } catch (error) {
            console.error('刪除工作回報失敗:', error)
            showNotification('error', '網路錯誤', '刪除失敗，請檢查網路連線後重試')
        }
    }

    // 顯示刪除確認模態框
    const confirmDelete = (report: WorkReport) => {
        setReportToDelete(report)
        setShowDeleteModal(true)
    }

    // 執行刪除
    const handleDelete = () => {
        if (reportToDelete) {
            deleteReport(reportToDelete.id)
            setShowDeleteModal(false)
            setReportToDelete(null)
        }
    }

    // 處理文件
    const handleFiles = (files: File[]) => {
        const validFiles = files.filter(file => {
            const isValidType = file.type.startsWith('image/')
            const isValidSize = file.size <= 5 * 1024 * 1024 // 5MB

            if (!isValidType) {
                showNotification('error', '檔案格式錯誤', `${file.name} 不是有效的圖片格式`)
                return false
            }
            if (!isValidSize) {
                showNotification('error', '檔案過大', `${file.name} 檔案大小超過 5MB`)
                return false
            }
            return true
        })

        setUploadedFiles(prev => [...prev, ...validFiles])
    }

    // 移除文件
    const removeFile = (index: number) => {
        setUploadedFiles(prev => {
            const fileToRemove = prev[index]
            if (fileToRemove) {
                // 釋放 URL.createObjectURL 創建的對象URL
                URL.revokeObjectURL(URL.createObjectURL(fileToRemove))
            }
            return prev.filter((_, i) => i !== index)
        })
    }

    // 轉換文件為 Base64
    // 上傳照片到伺服器
    const uploadPhotos = async (files: File[]): Promise<string[]> => {
        if (files.length === 0) return []

        const formData = new FormData()
        files.forEach(file => {
            formData.append('photos', file)
        })

        const apiBaseUrl = getApiBaseUrl()
        const response = await fetch(`${apiBaseUrl}/api/work-reports/upload-photos`, {
            method: 'POST',
            body: formData
        })

        if (!response.ok) {
            throw new Error(`照片上傳失敗: ${response.status}`)
        }

        const result = await response.json()
        if (!result.success) {
            throw new Error(result.message || '照片上傳失敗')
        }

        return result.files || []
    }

    // 提交工作回報
    const submitReport = async () => {
        if (!reportForm.content.trim()) {
            showNotification('error', '提交失敗', '請輸入回報內容')
            return
        }

        // 申訴回報必須選擇子類別
        if (reportForm.category === 'appeal' && !reportForm.subCategory) {
            showNotification('error', '提交失敗', '請選擇申訴類型')
            return
        }

        if (!user?.employee_id || !user?.name) {
            showNotification('error', '登錄錯誤', '用戶資訊不完整，請重新登錄')
            return
        }

        try {
            console.log('開始提交工作回報')

            // 先上傳照片到伺服器，獲取檔案路徑
            const photoPaths = await uploadPhotos(uploadedFiles)

            // 獲取當前時間
            const now = new Date()
            const reportDate = now.toISOString().split('T')[0] // YYYY-MM-DD
            const reportTime = now.toTimeString().split(' ')[0] // HH:MM:SS

            const submitData = {
                employee_id: user.employee_id,
                employee_name: user.name,
                report_date: reportDate,
                report_time: reportTime,
                category: reportForm.category,
                sub_category: reportForm.subCategory || null,
                content: reportForm.content,
                photos: photoPaths
            }

            console.log('提交數據:', submitData)

            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports`
            console.log('📤 提交工作回報 URL:', url)

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData)
            })

            console.log('提交響應狀態:', response.status)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result = await response.json()
            console.log('提交響應結果:', result)

            if (result.success) {
                showNotification('success', '提交成功', '工作回報已成功提交，感謝您的回報！')
                // 重置表單
                setReportForm({
                    category: 'work_report',
                    content: '',
                    photos: [],
                    subCategory: ''
                })
                setUploadedFiles([])
                setShowCreateModal(false)
                // 重新載入回報列表
                loadMyReports()
            } else {
                showNotification('error', '提交失敗', result.message || '未知錯誤，請稍後再試')
            }
        } catch (error) {
            console.error('提交工作回報失敗:', error)
            showNotification('error', '網路錯誤', '提交失敗，請檢查網路連線後重試')
        }
    }

    // 載入數據
    useEffect(() => {
        if (user) {
            loadMyReports()
        }
    }, [user])

    // 清理對象URL以避免內存洩漏
    useEffect(() => {
        return () => {
            uploadedFiles.forEach(file => {
                URL.revokeObjectURL(URL.createObjectURL(file))
            })
        }
    }, [uploadedFiles])

    // 類別名稱映射
    const getCategoryName = (category: string) => {
        const categoryMap: { [key: string]: string } = {
            'work_report': '工作回報',
            'feedback': '反饋',
            'suggestion': '建議',
            'issue': '問題回報',
            'progress': '進度更新',
            'appeal': '申訴回報'
        }
        return categoryMap[category] || category
    }

    // 子類別名稱映射
    const getSubCategoryName = (subCategory: string) => {
        const subCategoryMap: { [key: string]: string } = {
            'appeal_reaction': '申訴反應'
        }
        return subCategoryMap[subCategory] || subCategory
    }

    // 申訴回報子類別選項
    const getAppealSubCategories = () => {
        return [
            { value: 'appeal_reaction', label: '申訴反應' }
        ]
    }

    // 類別顏色
    const getCategoryColor = (category: string) => {
        const colorMap: { [key: string]: string } = {
            'work_report': 'bg-blue-100 text-blue-800 border-blue-200',
            'feedback': 'bg-green-100 text-green-800 border-green-200',
            'suggestion': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'issue': 'bg-red-100 text-red-800 border-red-200',
            'progress': 'bg-purple-100 text-purple-800 border-purple-200',
            'appeal': 'bg-orange-100 text-orange-800 border-orange-200'
        }
        return colorMap[category] || 'bg-gray-100 text-gray-800 border-gray-200'
    }

    // 格式化日期時間
    const formatDateTime = (dateStr: string, timeStr: string) => {
        const date = new Date(`${dateStr}T${timeStr}`)
        return date.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        })
    }

    // 如果正在載入認證狀態
    if (authLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                        <div className="relative">
                            <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                        </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                    <p className="text-gray-500">正在驗證用戶身份</p>
                </div>
            </div>
        )
    }

    // 如果用戶未登錄
    if (!user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-50 to-orange-50 rounded-2xl mb-4 shadow-lg">
                        <AlertCircle className="w-8 h-8 text-red-500" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-700 mb-2">未登錄</h4>
                    <p className="text-gray-500 mb-4">請先登錄後再使用此功能</p>
                    <Button onClick={() => router.push('/m/login')}>
                        前往登錄
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
            {/* 背景裝飾 */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
            </div>

            <div className="relative z-10 h-screen flex flex-col">
                {/* 🔙 返回按鈕 */}
                <div className="fixed top-4 left-4 z-50">
                    <Button
                        onClick={() => router.push('/m')}
                        className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl rounded-full px-4 py-2"
                    >
                        <ArrowLeft className="w-4 h-4 text-gray-700" />
                        <span className="text-gray-700 font-medium">返回</span>
                    </Button>
                </div>

                {/* 可滾動內容區域 */}
                <div className="flex-1 overflow-auto p-6 pt-20">
                    {/* 新增回報表單 */}
                    {showCreateModal && (
                        <div className="bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden mb-6">
                            {/* 🎨 表單表頭 - 包含用戶資訊和操作按鈕 */}
                            <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-6 text-white">
                                <div className="flex items-center justify-between">
                                    {/* 📍 左側用戶資訊 */}
                                    <div className="flex items-center space-x-3">
                                        <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl shadow-lg">
                                            <FileText className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h2 className="text-2xl font-bold text-white">工作回報</h2>
                                            <p className="text-indigo-100 text-sm font-medium">當前用戶：{user.name} ({user.employee_id})</p>
                                        </div>
                                    </div>

                                    {/* 📍 右側操作按鈕 - 上下排列 */}
                                    <div className="flex flex-col space-y-2">
                                        <Button
                                            onClick={submitReport}
                                            className="bg-white text-indigo-600 hover:bg-indigo-50 font-bold py-2 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                                        >
                                            <Send className="w-4 h-4 mr-2" />
                                            提交回報
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                setShowCreateModal(false)
                                                setReportForm({
                                                    category: 'work_report',
                                                    content: '',
                                                    photos: [],
                                                    subCategory: ''
                                                })
                                                setUploadedFiles([])
                                            }}
                                            className="bg-white/20 hover:bg-white/30 text-white border border-white/30 font-medium py-2 px-6 rounded-xl shadow-lg transition-all duration-300 backdrop-blur-sm"
                                        >
                                            <X className="w-4 h-4 mr-2" />
                                            取消
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* 📝 表單內容區域 */}
                            <div className="p-6">
                                <div className="space-y-6">
                                    {/* 回報類別 */}
                                    <div>
                                        <label className="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                            <FileText className="w-5 h-5 mr-2 text-indigo-600" />
                                            回報類別 <span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <select
                                            value={reportForm.category}
                                            onChange={(e) => setReportForm(prev => ({
                                                ...prev,
                                                category: e.target.value,
                                                subCategory: '' // 重置子類別
                                            }))}
                                            className="w-full p-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white/80 backdrop-blur-sm text-gray-700 font-medium"
                                        >
                                            <option value="work_report">工作回報</option>
                                            <option value="feedback">反饋</option>
                                            <option value="suggestion">建議</option>
                                            <option value="issue">問題回報</option>
                                            <option value="progress">進度更新</option>
                                            <option value="appeal">申訴回報</option>
                                        </select>
                                    </div>

                                    {/* 申訴回報子類別 */}
                                    {reportForm.category === 'appeal' && (
                                        <div>
                                            <label className="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                                <FileText className="w-5 h-5 mr-2 text-yellow-600" />
                                                申訴類型 <span className="text-red-500 ml-1">*</span>
                                            </label>
                                            <select
                                                value={reportForm.subCategory}
                                                onChange={(e) => setReportForm(prev => ({ ...prev, subCategory: e.target.value }))}
                                                className="w-full p-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white/80 backdrop-blur-sm text-gray-700 font-medium"
                                            >
                                                <option value="">請選擇申訴類型</option>
                                                {getAppealSubCategories().map(subCat => (
                                                    <option key={subCat.value} value={subCat.value}>
                                                        {subCat.label}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    )}

                                    {/* 回報內容 */}
                                    <div>
                                        <label className="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                            <MessageSquare className="w-5 h-5 mr-2 text-indigo-600" />
                                            回報內容 <span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <textarea
                                            value={reportForm.content}
                                            onChange={(e) => setReportForm(prev => ({ ...prev, content: e.target.value }))}
                                            placeholder="請詳細描述您的工作內容、進度或問題..."
                                            rows={6}
                                            className="w-full p-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white/80 backdrop-blur-sm text-gray-700 resize-none"
                                        />
                                    </div>

                                    {/* 照片上傳 */}
                                    <div>
                                        <label className="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                            <Camera className="w-5 h-5 mr-2 text-indigo-600" />
                                            附加照片
                                        </label>

                                        {/* 文件輸入 */}
                                        <input
                                            ref={fileInputRef}
                                            type="file"
                                            multiple
                                            accept="image/*"
                                            onChange={handleFileSelect}
                                            className="hidden"
                                        />

                                        {/* 手機版照片上傳按鈕 */}
                                        <div className="grid grid-cols-2 gap-3 mb-4">
                                            {/* 拍照按鈕 */}
                                            <Button
                                                type="button"
                                                onClick={() => {
                                                    if (fileInputRef.current) {
                                                        // 設置為使用後置相機
                                                        fileInputRef.current.setAttribute('capture', 'environment')
                                                        fileInputRef.current.setAttribute('accept', 'image/*')
                                                        fileInputRef.current.click()
                                                    }
                                                }}
                                                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-2xl p-4 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
                                            >
                                                <div className="flex flex-col items-center space-y-2">
                                                    <Camera className="w-6 h-6" />
                                                    <span className="text-sm font-medium">拍照</span>
                                                </div>
                                            </Button>

                                            {/* 選擇檔案按鈕 */}
                                            <Button
                                                type="button"
                                                onClick={() => {
                                                    if (fileInputRef.current) {
                                                        // 移除相機屬性，允許從相簿選擇
                                                        fileInputRef.current.removeAttribute('capture')
                                                        fileInputRef.current.setAttribute('accept', 'image/*')
                                                        fileInputRef.current.click()
                                                    }
                                                }}
                                                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-2xl p-4 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
                                            >
                                                <div className="flex flex-col items-center space-y-2">
                                                    <ImageIcon className="w-6 h-6" />
                                                    <span className="text-sm font-medium">選擇檔案</span>
                                                </div>
                                            </Button>
                                        </div>

                                        <p className="text-xs text-gray-500 text-center mb-4">支援 JPG、PNG 格式，最大 5MB</p>

                                        {/* 已上傳照片預覽 */}
                                        {uploadedFiles.length > 0 && (
                                            <div className="mt-4 space-y-3">
                                                <p className="text-sm font-medium text-gray-700">已選擇的照片：</p>
                                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                                    {uploadedFiles.map((file, index) => (
                                                        <div key={index} className="relative group">
                                                            <div className="bg-white rounded-xl overflow-hidden shadow-md border border-gray-200 hover:border-indigo-300 transition-colors duration-200">
                                                                {/* 照片預覽 */}
                                                                <div className="aspect-square relative">
                                                                    <img
                                                                        src={URL.createObjectURL(file)}
                                                                        alt={`預覽 ${index + 1}`}
                                                                        className="w-full h-full object-cover"
                                                                    />
                                                                    {/* 懸停遮罩 */}
                                                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                                                                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                                                            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1">
                                                                                <p className="text-xs text-gray-700 font-medium">
                                                                                    {(file.size / 1024 / 1024).toFixed(1)} MB
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {/* 文件名 */}
                                                                <div className="p-2">
                                                                    <p className="text-xs text-gray-600 truncate" title={file.name}>
                                                                        {file.name}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            {/* 刪除按鈕 */}
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation()
                                                                    removeFile(index)
                                                                }}
                                                                className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600 shadow-lg"
                                                            >
                                                                <X className="w-4 h-4" />
                                                            </button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 新增回報按鈕 - 當沒有顯示表單時 */}
                    {!showCreateModal && (
                        <div className="mb-6 text-center">
                            <Button
                                onClick={() => {
                                    // 🔧 重置表單狀態
                                    setReportForm({
                                        category: 'work_report',
                                        content: '',
                                        photos: [],
                                        subCategory: ''
                                    })
                                    setUploadedFiles([])
                                    setShowCreateModal(true)
                                }}
                                className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                            >
                                <Plus className="w-5 h-5 mr-2" />
                                新增工作回報
                            </Button>
                        </div>
                    )}

                    {/* 回報列表 */}
                    <div className="bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden">
                        {loading ? (
                            <div className="flex items-center justify-center py-16">
                                <div className="text-center">
                                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                                        <div className="relative">
                                            <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                                        </div>
                                    </div>
                                    <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                                    <p className="text-gray-500">正在獲取工作回報</p>
                                </div>
                            </div>
                        ) : reports.length === 0 ? (
                            <div className="flex items-center justify-center py-16">
                                <div className="text-center">
                                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl mb-4 shadow-lg">
                                        <FileText className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-gray-700 mb-2">暫無工作回報</h4>
                                    <p className="text-gray-500">點擊上方按鈕新增您的第一個工作回報</p>
                                </div>
                            </div>
                        ) : (
                            <div className="divide-y divide-gray-100">
                                {reports.map((report) => (
                                    <div key={report.id} className="p-6 hover:bg-indigo-50/50 transition-colors duration-200">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center space-x-2 mb-3 flex-wrap">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(report.category)}`}>
                                                        {getCategoryName(report.category)}
                                                        {report.category === 'appeal' && report.sub_category && (
                                                            <span className="ml-1 text-xs">
                                                                - {getSubCategoryName(report.sub_category)}
                                                            </span>
                                                        )}
                                                    </span>
                                                    <div className="flex items-center text-xs text-gray-500 space-x-1">
                                                        <Calendar className="w-3 h-3" />
                                                        <span>{formatDateTime(report.report_date, report.report_time)}</span>
                                                    </div>
                                                    {report.is_read ? (
                                                        <div className="flex items-center text-xs text-green-600">
                                                            <CheckCircle className="w-3 h-3 mr-1" />
                                                            已讀
                                                        </div>
                                                    ) : (
                                                        <div className="flex items-center text-xs text-orange-600">
                                                            <Clock className="w-3 h-3 mr-1" />
                                                            未讀
                                                        </div>
                                                    )}
                                                </div>
                                                <p className="text-gray-700 mb-3 line-clamp-3">{report.content}</p>

                                                {/* 📷 顯示上傳的照片 */}
                                                {report.photos && report.photos.length > 0 && (
                                                    <div className="mb-3">
                                                        <div className="flex items-center text-sm text-gray-600 mb-2">
                                                            <Camera className="w-4 h-4 mr-1" />
                                                            附加照片 ({report.photos.length} 張)
                                                        </div>
                                                        <div className="grid grid-cols-2 gap-2">
                                                            {report.photos.slice(0, 4).map((photo, photoIndex) => {
                                                                // 🔧 修正照片URL，確保使用完整的API路徑
                                                                const photoUrl = photo.startsWith('http')
                                                                    ? photo
                                                                    : `${getApiBaseUrl()}/${photo.startsWith('/') ? photo.slice(1) : photo}`

                                                                return (
                                                                    <div key={photoIndex} className="relative group">
                                                                        <img
                                                                            src={photoUrl}
                                                                            alt={`照片 ${photoIndex + 1}`}
                                                                            className="w-full max-h-24 object-contain rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors duration-200 cursor-pointer bg-gray-50"
                                                                            onClick={() => {
                                                                                // 🔍 點擊放大查看照片
                                                                                setSelectedPhoto(photoUrl)
                                                                                setShowPhotoModal(true)
                                                                            }}
                                                                            onError={(e) => {
                                                                                // 🚨 照片載入失敗時的處理
                                                                                console.error('照片載入失敗:', photoUrl)
                                                                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4TTE2IDEzSDEyTTEyIDEzSDhNMTYgMTdIMTJNMTIgMTdIOCIgc3Ryb2tlPSIjOUI5QkEwIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K'
                                                                            }}
                                                                        />
                                                                        {/* 如果照片超過4張，在第4張顯示剩餘數量 */}
                                                                        {photoIndex === 3 && report.photos.length > 4 && (
                                                                            <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                                                                                <span className="text-white text-sm font-medium">
                                                                                    +{report.photos.length - 4}
                                                                                </span>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )
                                                            })}
                                                        </div>
                                                    </div>
                                                )}

                                                {report.supervisor_feedback && (
                                                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mt-3">
                                                        <div className="flex items-center text-sm text-blue-700 mb-2">
                                                            <MessageSquare className="w-4 h-4 mr-1" />
                                                            主管回覆
                                                        </div>
                                                        <p className="text-blue-800">{report.supervisor_feedback}</p>
                                                    </div>
                                                )}
                                            </div>
                                            {/* 📍 優雅的操作圖示區域 */}
                                            <div className="ml-4 flex items-center space-x-3">
                                                {/* 查看圖示 - 藝術感單線條設計 */}
                                                <button
                                                    onClick={() => {
                                                        setSelectedReport(report)
                                                        setShowDetailModal(true)
                                                    }}
                                                    className="group relative p-2.5 rounded-full hover:bg-blue-50/80 transition-all duration-300 transform hover:scale-110 active:scale-95"
                                                    title="查看詳情"
                                                >
                                                    <Eye
                                                        className="w-5 h-5 text-blue-500 stroke-[1.2] group-hover:text-blue-600 transition-all duration-200"
                                                        style={{
                                                            filter: 'drop-shadow(0 3px 6px rgba(59, 130, 246, 0.2)) drop-shadow(0 1px 3px rgba(59, 130, 246, 0.1))'
                                                        }}
                                                    />
                                                    {/* 光暈效果 */}
                                                    <div className="absolute inset-0 rounded-full bg-blue-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                                                </button>

                                                {/* 刪除圖示 - 藝術感單線條設計 */}
                                                <button
                                                    onClick={() => confirmDelete(report)}
                                                    className="group relative p-2.5 rounded-full hover:bg-red-50/80 transition-all duration-300 transform hover:scale-110 active:scale-95"
                                                    title="刪除回報"
                                                >
                                                    <Trash2
                                                        className="w-5 h-5 text-red-400 stroke-[1.2] group-hover:text-red-500 transition-all duration-200"
                                                        style={{
                                                            filter: 'drop-shadow(0 3px 6px rgba(239, 68, 68, 0.2)) drop-shadow(0 1px 3px rgba(239, 68, 68, 0.1))'
                                                        }}
                                                    />
                                                    {/* 光暈效果 */}
                                                    <div className="absolute inset-0 rounded-full bg-red-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* 詳情模態框 */}
            {showDetailModal && selectedReport && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-auto">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-2xl font-bold text-gray-900">工作回報詳情</h3>
                                <Button
                                    onClick={() => setShowDetailModal(false)}
                                    className="bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-xl p-2"
                                >
                                    <X className="w-5 h-5" />
                                </Button>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(selectedReport.category)}`}>
                                        {getCategoryName(selectedReport.category)}
                                        {selectedReport.category === 'appeal' && selectedReport.sub_category && (
                                            <span className="ml-2 text-xs">
                                                - {getSubCategoryName(selectedReport.sub_category)}
                                            </span>
                                        )}
                                    </span>
                                    <div className="flex items-center text-sm text-gray-500 space-x-2">
                                        <Calendar className="w-4 h-4" />
                                        <span>{formatDateTime(selectedReport.report_date, selectedReport.report_time)}</span>
                                    </div>
                                </div>

                                {/* 💬 人員反應 - 靠右邊的對話框 */}
                                <div className="flex justify-end mb-4">
                                    <div className="max-w-[80%] bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl rounded-tr-md p-4 shadow-lg">
                                        <div className="flex items-center space-x-2 mb-2">
                                            <User className="w-4 h-4 text-white/80" />
                                            <span className="text-sm font-medium text-white/90">人員反應</span>
                                        </div>
                                        <p className="text-white whitespace-pre-wrap leading-relaxed">{selectedReport.content}</p>
                                    </div>
                                </div>

                                {/* 📷 詳情模態框中的照片顯示 */}
                                {selectedReport.photos && selectedReport.photos.length > 0 && (
                                    <div>
                                        <h4 className="font-semibold text-gray-700 mb-2">附加照片：</h4>
                                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                            {selectedReport.photos.map((photo, photoIndex) => {
                                                // 🔧 修正照片URL，確保使用完整的API路徑
                                                const photoUrl = photo.startsWith('http')
                                                    ? photo
                                                    : `${getApiBaseUrl()}/${photo.startsWith('/') ? photo.slice(1) : photo}`

                                                return (
                                                    <div key={photoIndex} className="relative group">
                                                        <img
                                                            src={photoUrl}
                                                            alt={`照片 ${photoIndex + 1}`}
                                                            className="w-full max-h-32 object-contain rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors duration-200 cursor-pointer bg-gray-50"
                                                            onClick={() => {
                                                                // 🔍 點擊放大查看照片
                                                                setSelectedPhoto(photoUrl)
                                                                setShowPhotoModal(true)
                                                            }}
                                                            onError={(e) => {
                                                                // 🚨 照片載入失敗時的處理
                                                                console.error('照片載入失敗:', photoUrl)
                                                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4TTE2IDEzSDEyTTEyIDEzSDhNMTYgMTdIMTJNMTIgMTdIOCIgc3Ryb2tlPSIjOUI5QkEwIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K'
                                                            }}
                                                        />
                                                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors duration-200 flex items-center justify-center">
                                                            <Eye className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </div>
                                    </div>
                                )}

                                {/* 💬 主管回覆 - 靠左邊的對話框 */}
                                {selectedReport.supervisor_feedback && (
                                    <div className="flex justify-start mb-4">
                                        <div className="max-w-[80%] bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl rounded-tl-md p-4 shadow-lg">
                                            <div className="flex items-center space-x-2 mb-2">
                                                <MessageSquare className="w-4 h-4 text-white/80" />
                                                <span className="text-sm font-medium text-white/90">主管回覆</span>
                                            </div>
                                            <p className="text-white whitespace-pre-wrap leading-relaxed">{selectedReport.supervisor_feedback}</p>
                                            {selectedReport.feedback_date && (
                                                <p className="text-xs text-white/70 mt-3 border-t border-white/20 pt-2">
                                                    回覆時間：{new Date(selectedReport.feedback_date).toLocaleString()}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 🔍 照片放大查看模態框 */}
            {showPhotoModal && selectedPhoto && (
                <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
                    <div className="relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center">
                        {/* 關閉按鈕 */}
                        <Button
                            onClick={() => {
                                setShowPhotoModal(false)
                                setSelectedPhoto('')
                            }}
                            className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-3 transition-colors duration-200"
                        >
                            <X className="w-6 h-6" />
                        </Button>

                        {/* 放大的照片 */}
                        <img
                            src={selectedPhoto}
                            alt="放大查看"
                            className="max-w-full max-h-full object-contain rounded-2xl shadow-2xl"
                            onClick={() => {
                                setShowPhotoModal(false)
                                setSelectedPhoto('')
                            }}
                            onError={(e) => {
                                console.error('放大照片載入失敗:', selectedPhoto)
                                showNotification('error', '照片載入失敗', '無法顯示此照片，請稍後再試')
                                setShowPhotoModal(false)
                                setSelectedPhoto('')
                            }}
                        />

                        {/* 點擊背景關閉 */}
                        <div
                            className="absolute inset-0 -z-10"
                            onClick={() => {
                                setShowPhotoModal(false)
                                setSelectedPhoto('')
                            }}
                        />
                    </div>
                </div>
            )}

            {/* 🗑️ 刪除確認模態框 */}
            {showDeleteModal && reportToDelete && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-3xl shadow-2xl max-w-md w-full">
                        <div className="p-6">
                            {/* 標題區域 */}
                            <div className="flex items-center justify-center mb-4">
                                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                    <Trash2 className="w-8 h-8 text-red-600" />
                                </div>
                            </div>

                            <div className="text-center mb-6">
                                <h3 className="text-xl font-bold text-gray-900 mb-2">確認刪除</h3>
                                <p className="text-gray-600 mb-4">確定要刪除這個工作回報嗎？此操作無法復原。</p>

                                {/* 回報預覽 */}
                                <div className="bg-gray-50 rounded-xl p-4 text-left">
                                    <div className="flex items-center space-x-2 mb-2">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(reportToDelete.category)}`}>
                                            {getCategoryName(reportToDelete.category)}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                            {formatDateTime(reportToDelete.report_date, reportToDelete.report_time)}
                                        </span>
                                    </div>
                                    <p className="text-sm text-gray-700 line-clamp-2">
                                        {reportToDelete.content}
                                    </p>
                                    {reportToDelete.photos && reportToDelete.photos.length > 0 && (
                                        <p className="text-xs text-gray-500 mt-2">
                                            📷 包含 {reportToDelete.photos.length} 張照片
                                        </p>
                                    )}
                                </div>
                            </div>

                            {/* 按鈕區域 */}
                            <div className="flex space-x-3">
                                <Button
                                    onClick={() => {
                                        setShowDeleteModal(false)
                                        setReportToDelete(null)
                                    }}
                                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl py-3 transition-colors duration-200"
                                >
                                    取消
                                </Button>
                                <Button
                                    onClick={handleDelete}
                                    className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-xl py-3 transition-colors duration-200"
                                >
                                    確認刪除
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 🎉 Toast 通知 */}
            {notification.show && (
                <div className="fixed top-4 right-4 z-[60] animate-in slide-in-from-top-2 duration-300">
                    <div className={`max-w-md w-full bg-white rounded-2xl shadow-2xl border-l-4 overflow-hidden ${notification.type === 'success' ? 'border-green-500' :
                        notification.type === 'error' ? 'border-red-500' : 'border-blue-500'
                        }`}>
                        <div className="p-4">
                            <div className="flex items-start">
                                <div className="flex-shrink-0">
                                    {notification.type === 'success' && (
                                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <CheckCircle className="w-5 h-5 text-green-600" />
                                        </div>
                                    )}
                                    {notification.type === 'error' && (
                                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <AlertCircle className="w-5 h-5 text-red-600" />
                                        </div>
                                    )}
                                    {notification.type === 'info' && (
                                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <AlertCircle className="w-5 h-5 text-blue-600" />
                                        </div>
                                    )}
                                </div>
                                <div className="ml-3 flex-1">
                                    <h3 className={`text-sm font-semibold ${notification.type === 'success' ? 'text-green-800' :
                                        notification.type === 'error' ? 'text-red-800' : 'text-blue-800'
                                        }`}>
                                        {notification.title}
                                    </h3>
                                    <p className="text-sm text-gray-600 mt-1">
                                        {notification.message}
                                    </p>
                                </div>
                                <button
                                    onClick={() => setNotification(prev => ({ ...prev, show: false }))}
                                    className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                                >
                                    <X className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                        {/* 進度條 */}
                        <div className={`h-1 bg-gradient-to-r ${notification.type === 'success' ? 'from-green-500 to-green-400' :
                            notification.type === 'error' ? 'from-red-500 to-red-400' : 'from-blue-500 to-blue-400'
                            } animate-pulse`}></div>
                    </div>
                </div>
            )}
        </div>
    )
} 