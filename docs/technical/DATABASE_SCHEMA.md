# AttendanceOS Elite - 資料庫結構說明文檔

## 📋 文檔說明

此文檔記錄了 AttendanceOS Elite 系統的完整資料庫結構，包含所有表格的欄位定義、資料類型、約束條件和關聯關係。

**⚠️ 重要提醒：**
- 所有開發人員在編寫代碼前，請先查閱此文檔確認欄位名稱
- 如有任何資料庫結構修改，必須同步更新此文檔
- 欄位名稱請嚴格按照此文檔定義使用

**🚨 常見錯誤預防：**
- ❌ 不要使用 `timestamp` 欄位，正確的是 `check_in` 和 `check_out`
- ❌ 不要使用 `clock_type` 欄位，正確的是 `status`
- ❌ 員工編號欄位是 `employee_id`（在 employees 表中），不是 `employee_code`
- ✅ API 返回的 `employee_code` 實際上是 employees.employee_id 的別名
- ✅ API 返回的 `employee_name` 和 `department_name` 是透過 JOIN 查詢獲得

**🔧 開發檢查清單：**
1. 編寫 SQL 查詢前，先檢查此文檔確認欄位名稱
2. 編寫前端代碼前，先查看 API 返回的實際欄位名稱
3. 如有疑問，使用 `python check_actual_schema.py` 檢查實際資料庫結構
4. 測試 API 時，使用 `python test_api_format.py` 確認返回格式

**📊 資料庫統計資訊：**
- 總表格數：29 個 🆕
- 最後更新：2025-06-09 🆕
- 總資料筆數：約 3,600+ 筆 🆕

---

## 🗂️ 表格總覽

| 表格名稱 | 中文名稱 | 主要用途 | 資料筆數 |
|---------|---------|---------|---------|
| `attendance` | 考勤記錄表 | 記錄員工考勤資料（整合後） | 731 |
| `punch_records` | 打卡原始資料表 | 儲存打卡機原始打卡資料 | 8 |
| `attendance_machines` | 打卡機設備表 | 管理打卡機設備 | 0 |
| `clock_raw_records` | 原始打卡記錄表 | 儲存原始打卡資料（舊版） | 0 |
| `clock_status_types` | 打卡狀態類型表 | 定義打卡狀態類型 | 7 |
| `departments` | 部門表 | 管理公司部門資訊 | 316 |
| `education_levels` | 學歷基本資料表 | 學歷選項管理 | 6 |
| `employees` | 員工表 | 管理員工基本資料 | 39 |
| `leave_types` | 假別基本資料表 | 假別類型管理 | 9 |
| `leaves` | 請假記錄表 | 管理請假申請 | 34 |
| `notification_rules` | 通知規則表 | 通知規則設定 | 237 |
| `notifications` | 通知記錄表 | 通知發送記錄 | 4 |
| `permissions` | 權限表 | 管理用戶權限角色 | 237 |
| `positions` | 職位基本資料表 | 職位選項管理 | 11 |
| `salary_grades` | 薪資等級表 | 薪資等級管理 | 6 |
| `schedule_rules` | 排班規則表 | 排班規則設定 | 1,264 |
| `schedule_templates` | 排班模板表 | 排班模板管理 | 0 |
| `schedules` | 排班表 | 管理員工排班資訊 | 356 |
| `shifts` | 班別定義表 | 定義工作班別 | 6 |
| `skills` | 技能基本資料表 | 技能選項管理 | 10 |
| `system_settings` | 系統設定表 | 系統參數設定 | 14 |
| `work_locations` | 工作地點表 | 工作地點管理 | 4 |
| `overtime_requests` | 加班申請表 | 管理加班申請記錄 | 15 | 🆕
| `overtime_types` | 加班類型表 | 定義加班類型 | 4 | 🆕
| `promotion_types` | 升遷類型表 | 定義升遷類型 | 5 | 🆕
| `employee_promotions` | 員工升遷紀錄表 | 記錄員工升遷歷史 | 6 | 🆕
| `reward_types` | 獎懲類型表 | 定義獎懲類型 | 8 | 🆕
| `employee_rewards` | 員工獎懲紀錄表 | 記錄員工獎懲歷史 | 9 | 🆕

---

## 📊 詳細表格結構

### 1. attendance (考勤記錄表)

**用途**：記錄員工的打卡資料，包含上下班時間和考勤狀態

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 記錄ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 員工ID |
| `check_in` | TIMESTAMP | | 上班打卡時間 |
| `check_out` | TIMESTAMP | | 下班打卡時間 |
| `status` | TEXT | NOT NULL | 考勤狀態 (normal/late/early_leave/absent/manual) |
| `device_id` | TEXT | | 打卡設備ID |
| `note` | TEXT | | 備註 |
| `work_date` | DATE | | 工作日期，記錄考勤記錄所屬的工作日 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `clock_status_code` | TEXT | | 打卡狀態代碼 |
| `shift_id` | INTEGER | FOREIGN KEY → shifts(id) | 班表ID（用於換班功能） | 🆕
| `date_type` | TEXT | | 日期類型（workday/weekend/holiday） | 🆕
| `late_minutes` | INTEGER | DEFAULT 0 | 遲到分鐘數 | 🆕
| `early_leave_minutes` | INTEGER | DEFAULT 0 | 早退分鐘數 | 🆕
| `overtime_minutes` | INTEGER | DEFAULT 0 | 加班分鐘數 | 🆕
| `work_hours` | DECIMAL(4,2) | DEFAULT 0 | 工作時數 | 🆕

**索引**：
- `idx_attendance_status` (一般)
- `idx_attendance_date` (一般)
- `idx_attendance_employee` (一般)
- `idx_attendance_shift` (一般) 🆕
- `idx_attendance_work_date` (一般) 🆕

### 2. punch_records (打卡原始資料表)

**用途**：儲存從打卡機匯入的原始打卡資料，每筆記錄只包含一個時間戳

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 記錄ID |
| `device_id` | TEXT | NOT NULL | 打卡機編號/設備ID |
| `employee_id` | TEXT | NOT NULL | 員工編號（來自打卡機的原始員工編號） |
| `punch_date` | DATE | NOT NULL | 打卡日期 |
| `punch_time` | TIME | NOT NULL | 打卡時間 |
| `punch_datetime` | TIMESTAMP | NOT NULL | 完整的打卡日期時間 |
| `status_code` | TEXT | NOT NULL | 打卡狀態碼（0=上班, 1=下班, 2=外出等） |
| `raw_data` | TEXT | | 原始匯入資料（保留完整的原始記錄） |
| `processed` | BOOLEAN | DEFAULT 0 | 是否已處理成考勤記錄（0=未處理, 1=已處理） |
| `attendance_id` | INTEGER | FOREIGN KEY → attendance(id) | 關聯的考勤記錄ID（處理後填入） |
| `note` | TEXT | | 備註 |
| `imported_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 匯入時間 |
| `processed_at` | TIMESTAMP | | 處理時間 |

**索引**：
- `idx_punch_records_employee` (一般)
- `idx_punch_records_date` (一般)
- `idx_punch_records_datetime` (一般)
- `idx_punch_records_device` (一般)
- `idx_punch_records_processed` (一般)
- `idx_punch_records_status` (一般)

**資料流程**：
```
打卡機原始資料 → punch_records表 → 處理整合 → attendance表 (考勤記錄)
```

**示例資料**：
- EMP001: 08:25上班, 17:35下班
- EMP002: 08:45上班（遲到）, 17:30下班
- EMP003: 08:30上班, 12:00外出, 13:00回來, 17:28下班

### 3. attendance_machines (打卡機設備表)

**用途**：管理打卡機設備資訊

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 設備ID |
| `machine_code` | TEXT | NOT NULL UNIQUE | 設備代碼 |
| `location` | TEXT | NOT NULL | 設備位置 |
| `ip_address` | TEXT | | IP地址 |
| `model` | TEXT | | 設備型號 |
| `status` | TEXT | DEFAULT 'active' | 設備狀態 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

### 4. clock_raw_records (原始打卡記錄表)

**用途**：儲存從打卡機匯入的原始打卡資料

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 記錄ID |
| `employee_code` | TEXT | NOT NULL | 員工代碼 |
| `machine_code` | TEXT | | 打卡機代碼 |
| `timestamp` | TIMESTAMP | NOT NULL | 打卡時間 |
| `status_code` | TEXT | NOT NULL | 狀態代碼 |
| `attendance_id` | INTEGER | FOREIGN KEY → attendance(id) | 對應的考勤記錄ID |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

**索引**：
- `idx_clock_raw_attendance` (一般)
- `idx_clock_raw_timestamp` (一般)
- `idx_clock_raw_employee` (一般)

### 5. clock_status_types (打卡狀態類型表)

**用途**：定義打卡狀態類型

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 狀態ID |
| `status_code` | TEXT | NOT NULL UNIQUE | 狀態代碼 |
| `status_name` | TEXT | NOT NULL | 狀態名稱 |
| `description` | TEXT | | 狀態描述 |
| `sort_order` | INTEGER | DEFAULT 0 | 排序順序 |
| `is_active` | INTEGER | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
- 0: 上班
- 1: 下班
- 2: 外出
- 3: 外出返回
- 4: 加班開始
- 5: 加班結束
- 6: 其他

### 6. departments (部門表)

**用途**：管理公司部門資訊

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 部門ID |
| `name` | TEXT | NOT NULL | 部門名稱 |
| `manager_id` | INTEGER | FOREIGN KEY → employees(id) | 部門主管ID |
| `description` | TEXT | | 部門描述 |
| `permission_id` | INTEGER | | 權限ID |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 7. education_levels (學歷基本資料表)

**用途**：管理學歷選項

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 學歷ID |
| `name` | TEXT | NOT NULL UNIQUE | 學歷名稱 |
| `level_order` | INTEGER | NOT NULL | 學歷等級順序 |
| `description` | TEXT | | 學歷描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 國中
2. 高中職
3. 專科
4. 大學
5. 碩士
6. 博士

### 8. employees (員工表)

**用途**：管理員工基本資料

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 員工ID |
| `name` | TEXT | NOT NULL | 員工姓名 |
| `employee_id` | TEXT | UNIQUE NOT NULL | 員工編號 |
| `department_id` | INTEGER | NOT NULL, FOREIGN KEY → departments(id) | 部門ID |
| `position` | TEXT | NOT NULL | 職位 |
| `email` | TEXT | UNIQUE | 電子郵件 |
| `phone` | TEXT | | 電話號碼 |
| `password` | TEXT | | 登錄密碼 |
| `hire_date` | DATE | | 入職日期 |
| `status` | TEXT | DEFAULT 'active' | 員工狀態 (active/inactive/trial) |
| `salary_level` | TEXT | | 薪資等級 |
| `id_number` | TEXT | | 身分證號 |
| `address` | TEXT | | 聯絡地址 |
| `emergency_contact` | TEXT | | 緊急聯絡人 |
| `emergency_phone` | TEXT | | 緊急聯絡電話 |
| `role_id` | INTEGER | FOREIGN KEY → permissions(id) | 角色權限ID |
| `manager_id` | INTEGER | FOREIGN KEY → employees(id) | 直屬主管ID |
| `photo_url` | TEXT | | 員工照片連結 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `card_number` | TEXT | | 卡號 |
| `shift_type` | TEXT | DEFAULT '正常班' | 班別類型 |

**索引**：
- `idx_employees_role` (一般)
- `idx_employees_department` (一般)
- `sqlite_autoindex_employees_1` (唯一 - employee_id)
- `sqlite_autoindex_employees_2` (唯一 - email)

### 9. leave_types (假別基本資料表)

**用途**：管理假別類型

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 假別ID |
| `name` | TEXT | NOT NULL UNIQUE | 假別名稱 |
| `code` | TEXT | NOT NULL UNIQUE | 假別代碼 |
| `max_days_per_year` | INTEGER | | 每年最大天數 |
| `is_paid` | BOOLEAN | DEFAULT 1 | 是否為有薪假 |
| `requires_approval` | BOOLEAN | DEFAULT 1 | 是否需要審核 |
| `advance_notice_days` | INTEGER | DEFAULT 1 | 需要提前申請天數 |
| `description` | TEXT | | 假別描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
- 年假 (annual)
- 病假 (sick)
- 事假 (personal)
- 產假 (maternity)
- 陪產假 (paternity)
- 婚假 (marriage)
- 喪假 (bereavement)
- 公假 (official)
- 特休 (special)

### 10. leaves (請假記錄表)

**用途**：管理請假申請記錄

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 請假ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 員工ID |
| `leave_type` | TEXT | NOT NULL | 假別類型 |
| `start_date` | DATE | NOT NULL | 開始日期 |
| `end_date` | DATE | NOT NULL | 結束日期 |
| `leave_hours` | FLOAT | | 請假時數 |
| `status` | TEXT | NOT NULL | 審核狀態 (pending/approved/rejected) |
| `reason` | TEXT | | 請假原因 |
| `comment` | TEXT | | 審核意見 |
| `substitute_id` | INTEGER | FOREIGN KEY → employees(id) | 代理人ID |
| `approver_id` | INTEGER | FOREIGN KEY → employees(id) | 審核主管ID |
| `emergency_contact` | TEXT | | 緊急聯絡方式 |
| `approved_at` | TIMESTAMP | | 審核時間 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

**索引**：
- `idx_leaves_date` (一般)
- `idx_leaves_status` (一般)
- `idx_leaves_employee` (一般)

### 11. notification_rules (通知規則表)

**用途**：設定通知規則

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 規則ID |
| `event_type` | TEXT | NOT NULL | 事件類型 |
| `notification_type` | TEXT | NOT NULL | 通知類型 |
| `recipients` | TEXT | NOT NULL | 接收者（JSON格式） |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 12. notifications (通知記錄表)

**用途**：記錄通知發送狀況

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 通知ID |
| `type` | TEXT | NOT NULL | 通知類型 |
| `recipients` | TEXT | NOT NULL | 接收者（JSON格式） |
| `message` | TEXT | NOT NULL | 通知訊息 |
| `status` | TEXT | NOT NULL | 發送狀態 (pending/sent/failed) |
| `sent_at` | TIMESTAMP | | 發送時間 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

**索引**：
- `idx_notifications_status` (一般)

### 13. permissions (權限表)

**用途**：管理用戶權限角色

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 權限ID |
| `role_name` | TEXT | NOT NULL | 角色名稱 |
| `permission_level` | INTEGER | NOT NULL | 權限等級 |
| `description` | TEXT | | 權限描述 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |

### 14. positions (職位基本資料表)

**用途**：管理職位選項

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 職位ID |
| `name` | TEXT | NOT NULL UNIQUE | 職位名稱 |
| `department_id` | INTEGER | FOREIGN KEY → departments(id) | 所屬部門 |
| `level_order` | INTEGER | NOT NULL | 職位等級順序 |
| `salary_range_min` | DECIMAL(10,2) | | 薪資範圍最低 |
| `salary_range_max` | DECIMAL(10,2) | | 薪資範圍最高 |
| `description` | TEXT | | 職位描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 實習生
2. 助理
3. 專員
4. 資深專員
5. 主任
6. 副理
7. 經理
8. 資深經理
9. 協理
10. 副總
11. 總經理

### 15. salary_grades (薪資等級表)

**用途**：管理薪資等級

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 薪資等級ID |
| `name` | TEXT | NOT NULL UNIQUE | 等級名稱 |
| `grade_code` | TEXT | NOT NULL UNIQUE | 等級代碼 |
| `level_order` | INTEGER | NOT NULL | 等級順序 |
| `min_salary` | DECIMAL(10,2) | NOT NULL | 最低薪資 |
| `max_salary` | DECIMAL(10,2) | NOT NULL | 最高薪資 |
| `description` | TEXT | | 等級描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 初級 (junior): 28,000 - 35,000
2. 中級 (intermediate): 35,000 - 50,000
3. 高級 (senior): 50,000 - 70,000
4. 資深 (expert): 70,000 - 100,000
5. 主管 (manager): 80,000 - 120,000
6. 高階主管 (executive): 120,000 - 200,000

### 16. schedule_rules (排班規則表)

**用途**：設定排班規則

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 規則ID |
| `rule_type` | TEXT | NOT NULL | 規則類型 |
| `rule_value` | TEXT | NOT NULL | 規則值 |
| `department_id` | INTEGER | FOREIGN KEY → departments(id) | 部門ID（NULL表示全公司適用） |
| `shift_id` | INTEGER | FOREIGN KEY → shifts(id) | 班別ID（NULL表示所有班別適用） |
| `description` | TEXT | | 規則描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

### 17. schedule_templates (排班模板表)

**用途**：管理排班模板

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 模板ID |
| `name` | TEXT | NOT NULL | 模板名稱 |
| `description` | TEXT | | 模板描述 |
| `template_data` | TEXT | NOT NULL | JSON格式的模板資料 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

### 18. schedules (排班表)

**用途**：管理員工排班資訊

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 排班ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 員工ID |
| `shift_date` | DATE | NOT NULL | 排班日期 |
| `shift_id` | INTEGER | NOT NULL, FOREIGN KEY → shifts(id) | 班別ID |
| `status` | TEXT | DEFAULT 'scheduled' | 排班狀態 (scheduled/completed/cancelled) |
| `actual_start_time` | TIME | | 實際上班時間 |
| `actual_end_time` | TIME | | 實際下班時間 |
| `overtime_hours` | DECIMAL(4,2) | DEFAULT 0 | 加班時數 |
| `pre_overtime_hours` | DECIMAL(4,2) | DEFAULT 0 | 上班前加班時數 |
| `post_overtime_hours` | DECIMAL(4,2) | DEFAULT 0 | 下班後加班時數 |
| `break_time_minutes` | INTEGER | DEFAULT 0 | 實際休息時間（分鐘） |
| `note` | TEXT | | 備註 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**索引**：
- `idx_schedules_date` (一般)
- `idx_schedules_employee_date` (一般)

### 19. shifts (班別定義表)

**用途**：定義工作班別

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 班別ID |
| `name` | TEXT | NOT NULL UNIQUE | 班別名稱 |
| `code` | TEXT | NOT NULL UNIQUE | 班別代碼 |
| `start_time` | TIME | NOT NULL | 上班時間 |
| `end_time` | TIME | NOT NULL | 下班時間 |
| `break_start_time` | TIME | | 休息開始時間 |
| `break_duration_minutes` | INTEGER | DEFAULT 60 | 休息時間長度（分鐘） |
| `pre_overtime_threshold_minutes` | INTEGER | DEFAULT 0 | 上班前加班門檻（分鐘） |
| `post_overtime_threshold_minutes` | INTEGER | DEFAULT 0 | 下班後加班門檻（分鐘） |
| `enable_pre_overtime` | BOOLEAN | DEFAULT 0 | 是否啟用上班前加班 |
| `enable_post_overtime` | BOOLEAN | DEFAULT 0 | 是否啟用下班後加班 |
| `auto_calculate_overtime` | BOOLEAN | DEFAULT 1 | 是否自動計算加班 |
| `color_code` | TEXT | DEFAULT '#3B82F6' | 班別顏色代碼 |
| `description` | TEXT | | 班別描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**索引**：
- `idx_shifts_code` (一般)
- `idx_shifts_active` (一般)
- `sqlite_autoindex_shifts_1` (唯一 - name)
- `sqlite_autoindex_shifts_2` (唯一 - code)

### 20. skills (技能基本資料表)

**用途**：管理技能選項

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 技能ID |
| `name` | TEXT | NOT NULL UNIQUE | 技能名稱 |
| `category` | TEXT | | 技能分類 |
| `description` | TEXT | | 技能描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
- 程式設計 (技術)
- 專案管理 (管理)
- 英語 (語言)
- 日語 (語言)
- 資料分析 (技術)
- 行銷企劃 (行銷)
- 財務分析 (財務)
- 人力資源 (人資)
- 客戶服務 (服務)
- 設計 (創意)

### 21. system_settings (系統設定表)

**用途**：管理系統參數設定

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 設定ID |
| `category` | TEXT | NOT NULL | 設定分類 |
| `setting_key` | TEXT | NOT NULL | 設定鍵值 |
| `setting_value` | TEXT | NOT NULL | 設定值 |
| `description` | TEXT | | 設定描述 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**唯一約束**：`UNIQUE(category, setting_key)`

**索引**：
- `idx_system_settings_category` (一般)

### 22. work_locations (工作地點表)

**用途**：管理工作地點

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 地點ID |
| `name` | TEXT | NOT NULL UNIQUE | 地點名稱 |
| `address` | TEXT | | 詳細地址 |
| `city` | TEXT | | 城市 |
| `country` | TEXT | DEFAULT '台灣' | 國家 |
| `timezone` | TEXT | DEFAULT 'Asia/Taipei' | 時區 |
| `is_remote` | BOOLEAN | DEFAULT 0 | 是否為遠端工作地點 |
| `description` | TEXT | | 地點描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 總公司 (台北市信義區)
2. 台中分公司 (台中市西屯區)
3. 高雄分公司 (高雄市前鎮區)
4. 遠端工作

### 23. overtime_requests (加班申請表) 🆕

**用途**：管理員工加班申請記錄

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 申請ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 申請員工ID |
| `overtime_date` | DATE | NOT NULL | 加班日期 |
| `start_time` | TIME | NOT NULL | 加班開始時間 |
| `end_time` | TIME | NOT NULL | 加班結束時間 |
| `overtime_hours` | DECIMAL(4,2) | NOT NULL | 加班時數 |
| `overtime_type_id` | INTEGER | NOT NULL, FOREIGN KEY → overtime_types(id) | 加班類型ID |
| `reason` | TEXT | NOT NULL | 加班原因 |
| `status` | TEXT | DEFAULT 'pending' | 申請狀態 (pending/approved/rejected) |
| `approver_id` | INTEGER | FOREIGN KEY → employees(id) | 審核人ID |
| `approved_at` | TIMESTAMP | | 審核時間 |
| `comment` | TEXT | | 審核意見 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 申請時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**索引**：
- `idx_overtime_requests_employee` (一般)
- `idx_overtime_requests_date` (一般)
- `idx_overtime_requests_status` (一般)
- `idx_overtime_requests_type` (一般)

### 24. overtime_types (加班類型表) 🆕

**用途**：定義加班類型和相關設定

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 類型ID |
| `name` | TEXT | NOT NULL UNIQUE | 類型名稱 |
| `code` | TEXT | NOT NULL UNIQUE | 類型代碼 |
| `multiplier` | DECIMAL(3,2) | DEFAULT 1.0 | 加班費倍數 |
| `description` | TEXT | | 類型描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 平日加班 (WEEKDAY) - 倍數 1.33
2. 假日加班 (WEEKEND) - 倍數 1.66
3. 國定假日加班 (HOLIDAY) - 倍數 2.0
4. 緊急加班 (EMERGENCY) - 倍數 1.5

**索引**：
- `idx_overtime_types_code` (一般)
- `idx_overtime_types_active` (一般)

### 25. promotion_types (升遷類型表) 🆕

**用途**：定義員工升遷類型

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 類型ID |
| `name` | TEXT | NOT NULL UNIQUE | 升遷類型名稱 |
| `code` | TEXT | NOT NULL UNIQUE | 升遷類型代碼 |
| `description` | TEXT | | 類型描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 職位晉升 (POSITION_PROMOTION)
2. 薪資調整 (SALARY_ADJUSTMENT)
3. 部門調動 (DEPARTMENT_TRANSFER)
4. 職責擴大 (RESPONSIBILITY_EXPANSION)
5. 管理職晉升 (MANAGEMENT_PROMOTION)

**索引**：
- `idx_promotion_types_code` (一般)
- `idx_promotion_types_active` (一般)

### 26. employee_promotions (員工升遷紀錄表) 🆕

**用途**：記錄員工升遷歷史

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 紀錄ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 員工ID |
| `promotion_type_id` | INTEGER | NOT NULL, FOREIGN KEY → promotion_types(id) | 升遷類型ID |
| `promotion_date` | DATE | NOT NULL | 升遷日期 |
| `from_position` | TEXT | | 原職位 |
| `to_position` | TEXT | | 新職位 |
| `from_department` | TEXT | | 原部門 |
| `to_department` | TEXT | | 新部門 |
| `from_salary` | DECIMAL(10,2) | | 原薪資 |
| `to_salary` | DECIMAL(10,2) | | 新薪資 |
| `reason` | TEXT | NOT NULL | 升遷原因 |
| `approver_id` | INTEGER | FOREIGN KEY → employees(id) | 核准人ID |
| `effective_date` | DATE | | 生效日期 |
| `note` | TEXT | | 備註 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**索引**：
- `idx_employee_promotions_employee` (一般)
- `idx_employee_promotions_date` (一般)
- `idx_employee_promotions_type` (一般)

### 27. reward_types (獎懲類型表) 🆕

**用途**：定義員工獎懲類型

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 類型ID |
| `name` | TEXT | NOT NULL UNIQUE | 獎懲類型名稱 |
| `code` | TEXT | NOT NULL UNIQUE | 獎懲類型代碼 |
| `category` | TEXT | NOT NULL | 類別 (reward/punishment) |
| `points` | INTEGER | DEFAULT 0 | 獎懲分數 |
| `description` | TEXT | | 類型描述 |
| `is_active` | BOOLEAN | DEFAULT 1 | 是否啟用 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**預設資料**：
1. 嘉獎 (COMMENDATION) - 獎勵 +5分
2. 小功 (MINOR_MERIT) - 獎勵 +10分
3. 大功 (MAJOR_MERIT) - 獎勵 +20分
4. 獎金 (BONUS) - 獎勵 +15分
5. 口頭警告 (VERBAL_WARNING) - 懲處 -3分
6. 書面警告 (WRITTEN_WARNING) - 懲處 -5分
7. 小過 (MINOR_DEMERIT) - 懲處 -10分
8. 大過 (MAJOR_DEMERIT) - 懲處 -20分

**索引**：
- `idx_reward_types_code` (一般)
- `idx_reward_types_category` (一般)
- `idx_reward_types_active` (一般)

### 28. employee_rewards (員工獎懲紀錄表) 🆕

**用途**：記錄員工獎懲歷史

| 欄位名稱 | 資料類型 | 約束條件 | 說明 |
|---------|---------|---------|------|
| `id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 紀錄ID |
| `employee_id` | INTEGER | NOT NULL, FOREIGN KEY → employees(id) | 員工ID |
| `reward_type_id` | INTEGER | NOT NULL, FOREIGN KEY → reward_types(id) | 獎懲類型ID |
| `reward_date` | DATE | NOT NULL | 獎懲日期 |
| `reason` | TEXT | NOT NULL | 獎懲原因 |
| `amount` | DECIMAL(10,2) | | 獎金金額（如適用） |
| `points` | INTEGER | DEFAULT 0 | 獎懲分數 |
| `approver_id` | INTEGER | FOREIGN KEY → employees(id) | 核准人ID |
| `effective_date` | DATE | | 生效日期 |
| `note` | TEXT | | 備註 |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 創建時間 |
| `updated_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新時間 |

**索引**：
- `idx_employee_rewards_employee` (一般)
- `idx_employee_rewards_date` (一般)
- `idx_employee_rewards_type` (一般)

---

## 🔗 關聯關係圖

```
departments (部門)
    ↓ (1:N)
employees (員工)
    ↓ (1:N)
attendance (考勤記錄)
    ↓ (1:1)
clock_raw_records (原始打卡記錄)

employees (員工)
    ↓ (1:N)
schedules (排班) ← shifts (班別) (N:1)

employees (員工)
    ↓ (1:N)
leaves (請假記錄)

employees (員工) ← permissions (權限) (N:1)

employees (員工)
    ↓ (1:N)
overtime_requests (加班申請) ← overtime_types (加班類型) (N:1)

employees (員工)
    ↓ (1:N)
employee_promotions (升遷紀錄) ← promotion_types (升遷類型) (N:1)

employees (員工)
    ↓ (1:N)
employee_rewards (獎懲紀錄) ← reward_types (獎懲類型) (N:1)

attendance_machines (打卡機) → clock_raw_records (原始記錄)

clock_status_types (狀態類型) → clock_raw_records (原始記錄)
```

---

## 📝 常用查詢範例

### 1. 獲取員工考勤記錄
```sql
SELECT 
    e.name as employee_name,
    e.employee_id,
    d.name as department_name,
    a.check_in,
    a.check_out,
    a.status,
    a.note
FROM attendance a
JOIN employees e ON a.employee_id = e.id
JOIN departments d ON e.department_id = d.id
WHERE a.check_in >= '2025-06-01'
ORDER BY a.check_in DESC;
```

### 2. 獲取員工排班資訊
```sql
SELECT 
    e.name as employee_name,
    e.employee_id,
    s.shift_date,
    sh.name as shift_name,
    sh.start_time,
    sh.end_time,
    s.status
FROM schedules s
JOIN employees e ON s.employee_id = e.id
JOIN shifts sh ON s.shift_id = sh.id
WHERE s.shift_date BETWEEN '2025-06-01' AND '2025-06-30'
ORDER BY s.shift_date, e.name;
```

---

## 🚨 重要注意事項

### 資料完整性
1. **外鍵約束**：確保所有外鍵關係的完整性
2. **唯一約束**：員工編號、電子郵件等必須唯一
3. **必填欄位**：確保重要欄位不為空

### 效能優化
1. **索引使用**：查詢時善用已建立的索引
2. **分頁查詢**：大量資料查詢時使用 LIMIT 和 OFFSET
3. **避免 N+1 查詢**：使用 JOIN 而非多次查詢

### 資料安全
1. **密碼加密**：員工密碼必須加密儲存
2. **敏感資料**：身分證號等敏感資料需要特別保護
3. **存取控制**：根據權限等級控制資料存取

### 打卡資料流程 🆕
1. **原始資料保留**：punch_records表保留完整的原始打卡資料
2. **資料整合**：系統自動將兩筆打卡記錄整合為一筆考勤記錄
3. **狀態追蹤**：processed欄位追蹤資料處理狀態
4. **關聯維護**：attendance_id欄位維護與考勤記錄的關聯

---

## 📅 更新記錄

| 日期 | 版本 | 更新內容 |
|------|------|---------|
| 2025-06-09 | v2.4.0 | 新增員工升遷和獎懲管理功能：promotion_types、employee_promotions、reward_types、employee_rewards四個表格 | 🆕
| 2025-06-08 | v2.3.0 | 新增overtime_requests和overtime_types表，完善加班申請功能；更新attendance表結構，新增換班和考勤計算欄位 |
| 2025-06-05 | v2.2.0 | 新增punch_records打卡原始資料表，完善打卡資料流程架構 |
| 2025-06-04 | v2.1.0 | 完整更新資料庫結構文檔，新增所有表格詳細說明 |
| 2025-06-02 | v2.0.0 | 新增基本資料表格和系統設定 |
| 2025-06-01 | v1.0.0 | 初始版本 |

---

**📞 技術支援**：如有任何資料庫相關問題，請聯繫開發團隊。