import sqlite3
import json
from flask import g
from contextlib import contextmanager

class MultiTenantDB:
    """
    多租戶資料庫查詢包裝器
    自動添加company_id過濾條件，確保數據隔離
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
    
    @contextmanager
    def get_connection(self):
        """
        獲取資料庫連接
        
        返回：
        sqlite3.Connection: 資料庫連接
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            # 設定當前公司ID
            company_id = g.get('company_id', 'default')
            if company_id != 'default':
                company_record = self.get_company_by_code(company_id)
                if company_record:
                    conn.execute("PRAGMA temp.current_company_id = ?", (company_record['id'],))
            yield conn
        finally:
            conn.close()
    
    def get_company_by_code(self, company_code):
        """
        根據公司代碼獲取公司資訊
        
        參數：
        company_code (str): 公司代碼
        
        返回：
        dict: 公司資訊
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                "SELECT * FROM companies WHERE company_code = ?", 
                (company_code,)
            )
            return dict(cursor.fetchone()) if cursor.fetchone() else None
    
    def query_employees(self, company_id=None, filters=None):
        """
        查詢員工資料（自動添加公司過濾）
        
        參數：
        company_id (int): 公司ID，None則使用當前公司
        filters (dict): 額外過濾條件
        
        返回：
        list: 員工列表
        """
        if company_id is None:
            company_code = g.get('company_id', 'default')
            company = self.get_company_by_code(company_code)
            company_id = company['id'] if company else 1
        
        with self.get_connection() as conn:
            sql = "SELECT * FROM employees WHERE company_id = ?"
            params = [company_id]
            
            # 添加額外過濾條件
            if filters:
                for key, value in filters.items():
                    if key == 'status':
                        sql += " AND status = ?"
                        params.append(value)
                    elif key == 'department_id':
                        sql += " AND department_id = ?"
                        params.append(value)
            
            cursor = conn.execute(sql, params)
            employees = [dict(row) for row in cursor.fetchall()]
            
            # 解析custom_fields JSON
            for emp in employees:
                if emp['custom_fields']:
                    emp['custom_fields'] = json.loads(emp['custom_fields'])
                else:
                    emp['custom_fields'] = {}
            
            return employees
    
    def query_attendance(self, company_id=None, date_range=None, employee_id=None):
        """
        查詢考勤記錄（自動添加公司過濾）
        
        參數：
        company_id (int): 公司ID
        date_range (tuple): 日期範圍 (start_date, end_date)
        employee_id (int): 員工ID
        
        返回：
        list: 考勤記錄列表
        """
        if company_id is None:
            company_code = g.get('company_id', 'default')
            company = self.get_company_by_code(company_code)
            company_id = company['id'] if company else 1
        
        with self.get_connection() as conn:
            sql = """
                SELECT a.*, e.name as employee_name, e.employee_id as emp_code
                FROM attendance_records a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.company_id = ?
            """
            params = [company_id]
            
            if date_range:
                sql += " AND a.date BETWEEN ? AND ?"
                params.extend(date_range)
            
            if employee_id:
                sql += " AND a.employee_id = ?"
                params.append(employee_id)
            
            sql += " ORDER BY a.date DESC, e.employee_id"
            
            cursor = conn.execute(sql, params)
            records = [dict(row) for row in cursor.fetchall()]
            
            # 解析custom_fields JSON
            for record in records:
                if record['custom_fields']:
                    record['custom_fields'] = json.loads(record['custom_fields'])
                else:
                    record['custom_fields'] = {}
            
            return records
    
    def insert_employee(self, employee_data):
        """
        新增員工（自動添加company_id）
        
        參數：
        employee_data (dict): 員工資料
        
        返回：
        int: 新員工ID
        """
        company_code = g.get('company_id', 'default')
        company = self.get_company_by_code(company_code)
        company_id = company['id'] if company else 1
        
        # 確保custom_fields是JSON字串
        custom_fields = employee_data.get('custom_fields', {})
        if isinstance(custom_fields, dict):
            custom_fields = json.dumps(custom_fields)
        
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO employees (
                    company_id, employee_id, name, email, 
                    department_id, position, custom_fields
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                company_id,
                employee_data['employee_id'],
                employee_data['name'],
                employee_data.get('email'),
                employee_data.get('department_id'),
                employee_data.get('position'),
                custom_fields
            ))
            conn.commit()
            return cursor.lastrowid
    
    def insert_attendance(self, attendance_data):
        """
        新增考勤記錄（自動添加company_id）
        
        參數：
        attendance_data (dict): 考勤資料
        
        返回：
        int: 新記錄ID
        """
        company_code = g.get('company_id', 'default')
        company = self.get_company_by_code(company_code)
        company_id = company['id'] if company else 1
        
        # 確保custom_fields是JSON字串
        custom_fields = attendance_data.get('custom_fields', {})
        if isinstance(custom_fields, dict):
            custom_fields = json.dumps(custom_fields)
        
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO attendance_records (
                    company_id, employee_id, date, shift_id,
                    check_in_time, check_out_time, status, custom_fields
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                company_id,
                attendance_data['employee_id'],
                attendance_data['date'],
                attendance_data.get('shift_id'),
                attendance_data.get('check_in_time'),
                attendance_data.get('check_out_time'),
                attendance_data.get('status', 'present'),
                custom_fields
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_company_stats(self, company_id=None):
        """
        獲取公司統計資料
        
        參數：
        company_id (int): 公司ID
        
        返回：
        dict: 統計資料
        """
        if company_id is None:
            company_code = g.get('company_id', 'default')
            company = self.get_company_by_code(company_code)
            company_id = company['id'] if company else 1
        
        with self.get_connection() as conn:
            # 員工總數
            cursor = conn.execute(
                "SELECT COUNT(*) as count FROM employees WHERE company_id = ? AND status = 'active'",
                (company_id,)
            )
            employee_count = cursor.fetchone()['count']
            
            # 今日出勤數
            cursor = conn.execute("""
                SELECT COUNT(*) as count FROM attendance_records 
                WHERE company_id = ? AND date = date('now') AND status = 'present'
            """, (company_id,))
            today_attendance = cursor.fetchone()['count']
            
            # 本月請假數
            cursor = conn.execute("""
                SELECT COUNT(*) as count FROM leave_requests 
                WHERE company_id = ? AND strftime('%Y-%m', start_date) = strftime('%Y-%m', 'now')
            """, (company_id,))
            monthly_leaves = cursor.fetchone()['count']
            
            return {
                'employee_count': employee_count,
                'today_attendance': today_attendance,
                'monthly_leaves': monthly_leaves,
                'attendance_rate': round((today_attendance / employee_count * 100) if employee_count > 0 else 0, 1)
            }

# 全域實例
multi_tenant_db = MultiTenantDB('attendance.db') 