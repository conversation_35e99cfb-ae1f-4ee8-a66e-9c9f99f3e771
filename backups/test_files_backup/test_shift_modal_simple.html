<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>換班模態框測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 p-8">

    <h1 class="text-2xl font-bold mb-4">換班模態框測試</h1>

    <button onclick="testShowShiftModal()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
    測試顯示換班模態框
</button>

    <button onclick="testShowEditModal()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 ml-4">
    測試顯示編輯模態框（對照組）
</button>

    <!-- 換班模態框 -->
    <div id="shiftChangeModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
                    <h3 class="text-xl font-bold">🔄 換班模態框測試</h3>
                </div>
                <div class="p-6">
                    <p class="text-lg">如果您能看到這個內容，表示換班模態框顯示正常！</p>
                </div>
                <div class="p-6 border-t bg-gray-50 flex justify-end space-x-4">
                    <button onclick="closeShiftModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors">
                    關閉
                </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 編輯模態框（對照組） -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
                <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-6">
                    <h3 class="text-xl font-bold">✏️ 編輯模態框測試</h3>
                </div>
                <div class="p-6">
                    <p class="text-lg">如果您能看到這個內容，表示編輯模態框顯示正常！</p>
                </div>
                <div class="p-6 border-t bg-gray-50 flex justify-end space-x-4">
                    <button onclick="closeEditModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors">
                    關閉
                </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testShowShiftModal() {
            console.log('🔵 ===============================');
            console.log('🔵 開始測試換班模態框');
            console.log('🔵 ===============================');
            console.log('🔵 當前時間:', new Date().toLocaleTimeString());

            console.log('🔵 Step 1: 查找DOM元素');
            const modal = document.getElementById('shiftChangeModal');
            console.log('🔵 getElementById結果:', modal);
            console.log('🔵 元素類型:', typeof modal);
            console.log('🔵 元素是否為HTMLElement:', modal instanceof HTMLElement);

            if (modal) {
                console.log('🔵 Step 2: 檢查初始狀態');
                console.log('🔵 初始classList:', modal.classList.toString());
                console.log('🔵 包含hidden類別:', modal.classList.contains('hidden'));
                console.log('🔵 初始style.display:', modal.style.display);

                console.log('🔵 Step 3: 檢查計算樣式（移除hidden前）');
                let computedStyle = window.getComputedStyle(modal);
                console.log('🔵 計算的display:', computedStyle.display);
                console.log('🔵 計算的visibility:', computedStyle.visibility);
                console.log('🔵 計算的opacity:', computedStyle.opacity);
                console.log('🔵 計算的z-index:', computedStyle.zIndex);
                console.log('🔵 計算的position:', computedStyle.position);

                console.log('🔵 Step 4: 移除hidden類別');
                modal.classList.remove('hidden');
                console.log('🔵 移除後classList:', modal.classList.toString());
                console.log('🔵 移除後還包含hidden:', modal.classList.contains('hidden'));

                console.log('🔵 Step 5: 檢查計算樣式（移除hidden後）');
                computedStyle = window.getComputedStyle(modal);
                console.log('🔵 新的計算display:', computedStyle.display);
                console.log('🔵 新的計算visibility:', computedStyle.visibility);
                console.log('🔵 新的計算opacity:', computedStyle.opacity);

                console.log('🔵 Step 6: 強制設置樣式測試');
                modal.style.display = 'flex';
                modal.style.visibility = 'visible';
                modal.style.opacity = '1';
                console.log('🔵 已強制設置樣式');
                console.log('🔵 style.display:', modal.style.display);
                console.log('🔵 style.visibility:', modal.style.visibility);
                console.log('🔵 style.opacity:', modal.style.opacity);

                console.log('🔵 Step 7: 檢查元素位置');
                const rect = modal.getBoundingClientRect();
                console.log('🔵 getBoundingClientRect:', rect);
                console.log('🔵 寬度:', rect.width);
                console.log('🔵 高度:', rect.height);
                console.log('🔵 是否在視窗內:', rect.top >= 0 && rect.left >= 0);

                console.log('🔵 Step 8: 檢查父容器');
                console.log('🔵 父元素:', modal.parentElement);
                console.log('🔵 在DOM樹中:', document.contains(modal));

                console.log('🔵 ===============================');
                console.log('🔵 換班模態框測試完成 - 應該可見');
                console.log('🔵 ===============================');
            } else {
                console.log('🔵 ❌❌❌ 找不到換班模態框元素！');
                console.log('🔵 所有帶有ID的元素:');
                const allElements = document.querySelectorAll('[id]');
                allElements.forEach(el => console.log('🔵 ID:', el.id, '元素:', el));
            }
        }

        function testShowEditModal() {
            console.log('🟠 ===============================');
            console.log('🟠 開始測試編輯模態框（對照組）');
            console.log('🟠 ===============================');
            console.log('🟠 當前時間:', new Date().toLocaleTimeString());

            console.log('🟠 Step 1: 查找DOM元素');
            const modal = document.getElementById('editModal');
            console.log('🟠 getElementById結果:', modal);
            console.log('🟠 元素類型:', typeof modal);
            console.log('🟠 元素是否為HTMLElement:', modal instanceof HTMLElement);

            if (modal) {
                console.log('🟠 Step 2: 檢查初始狀態');
                console.log('🟠 初始classList:', modal.classList.toString());
                console.log('🟠 包含hidden類別:', modal.classList.contains('hidden'));
                console.log('🟠 初始style.display:', modal.style.display);

                console.log('🟠 Step 3: 移除hidden類別');
                modal.classList.remove('hidden');
                console.log('🟠 移除後classList:', modal.classList.toString());
                console.log('🟠 移除後還包含hidden:', modal.classList.contains('hidden'));

                console.log('🟠 Step 4: 檢查計算樣式');
                const computedStyle = window.getComputedStyle(modal);
                console.log('🟠 計算的display:', computedStyle.display);
                console.log('🟠 計算的visibility:', computedStyle.visibility);
                console.log('🟠 計算的opacity:', computedStyle.opacity);

                console.log('🟠 Step 5: 檢查元素位置');
                const rect = modal.getBoundingClientRect();
                console.log('🟠 getBoundingClientRect:', rect);
                console.log('🟠 寬度:', rect.width);
                console.log('🟠 高度:', rect.height);
                console.log('🟠 是否可見:', rect.width > 0 && rect.height > 0);

                console.log('🟠 ===============================');
                console.log('🟠 編輯模態框測試完成 - 應該可見');
                console.log('🟠 ===============================');
            } else {
                console.log('🟠 ❌❌❌ 找不到編輯模態框元素！');
            }
        }

        function closeShiftModal() {
            console.log('關閉換班模態框');
            const modal = document.getElementById('shiftChangeModal');
            if (modal) {
                modal.classList.add('hidden');
                modal.style.display = '';
            }
        }

        function closeEditModal() {
            console.log('關閉編輯模態框');
            const modal = document.getElementById('editModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        // 頁面載入完成後的測試
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 ===============================');
            console.log('📄 頁面載入完成 - 初始化檢查');
            console.log('📄 ===============================');
            console.log('📄 當前時間:', new Date().toLocaleTimeString());
            console.log('📄 User Agent:', navigator.userAgent);
            console.log('📄 頁面URL:', window.location.href);

            console.log('📄 檢查換班模態框元素:');
            const shiftModal = document.getElementById('shiftChangeModal');
            console.log('📄 換班模態框存在:', !!shiftModal);
            if (shiftModal) {
                console.log('📄 換班模態框初始classList:', shiftModal.classList.toString());
                console.log('📄 換班模態框初始display:', window.getComputedStyle(shiftModal).display);
            }

            console.log('📄 檢查編輯模態框元素:');
            const editModal = document.getElementById('editModal');
            console.log('📄 編輯模態框存在:', !!editModal);
            if (editModal) {
                console.log('📄 編輯模態框初始classList:', editModal.classList.toString());
                console.log('📄 編輯模態框初始display:', window.getComputedStyle(editModal).display);
            }

            console.log('📄 檢查Tailwind CSS載入:');
            const testDiv = document.createElement('div');
            testDiv.className = 'hidden';
            document.body.appendChild(testDiv);
            const hiddenDisplay = window.getComputedStyle(testDiv).display;
            document.body.removeChild(testDiv);
            console.log('📄 Tailwind hidden類別display值:', hiddenDisplay);
            console.log('📄 Tailwind是否正確載入:', hiddenDisplay === 'none');

            console.log('📄 ===============================');
            console.log('📄 初始化檢查完成');
            console.log('📄 ===============================');
        });
    </script>

</body>

</html>