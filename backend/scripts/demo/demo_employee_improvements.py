#!/usr/bin/env python3
"""
員工管理照片功能演示腳本
展示新的照片預覽和頭像功能
"""

import webbrowser
import time
import requests

BASE_URL = "http://localhost:7072"

def demo_employee_photo_features():
    """演示員工管理照片功能"""
    print("📸 員工管理照片功能演示")
    print("=" * 50)
    
    # 檢查服務是否運行
    try:
        response = requests.get(f"{BASE_URL}/elite/employees", timeout=5)
        if response.status_code != 200:
            print("❌ 服務未運行，請先啟動應用程式")
            return
    except:
        print("❌ 無法連接到服務，請確認應用程式已啟動")
        return
    
    print("✅ 服務運行正常")
    
    # 檢查員工照片統計
    try:
        response = requests.get(f"{BASE_URL}/api/employees")
        if response.status_code == 200:
            data = response.json()
            employees = data.get('employees', [])
            
            photo_count = sum(1 for emp in employees if emp.get('photo_url') and emp['photo_url'].strip())
            no_photo_count = len(employees) - photo_count
            
            print(f"\n📊 員工照片統計:")
            print(f"   總員工數: {len(employees)} 位")
            print(f"   有照片: {photo_count} 位")
            print(f"   無照片: {no_photo_count} 位")
            
    except Exception as e:
        print(f"⚠️ 無法獲取員工統計: {e}")
    
    print("\n🚀 即將開啟員工管理頁面...")
    print("\n💡 新照片功能介紹：")
    print("   1. 📷 照片預覽顯示")
    print("      - 有照片的員工會顯示實際照片")
    print("      - 沒有照片的員工會顯示個性化頭像")
    print("   2. 🎨 個性化預設頭像")
    print("      - 根據姓名首字母生成")
    print("      - 使用不同顏色區分")
    print("   3. 📤 照片上傳功能")
    print("      - 點擊相機圖標上傳照片")
    print("      - 支援拖拽上傳")
    print("      - 自動文件大小檢查")
    print("   4. 🔗 照片連結輸入")
    print("      - 支援網路圖片連結")
    print("      - 即時預覽更新")
    print("   5. ⚡ 即時頭像更新")
    print("      - 修改姓名即時更新頭像")
    print("      - 智慧錯誤處理")
    
    print("\n📋 測試步驟：")
    print("   1. 點擊任一員工的「編輯」按鈕")
    print("   2. 觀察左上角的照片預覽區域")
    print("   3. 有照片的員工會顯示圓形照片")
    print("   4. 無照片的員工會顯示彩色首字母頭像")
    print("   5. 點擊右下角的相機圖標上傳新照片")
    print("   6. 或在「照片連結」欄位輸入圖片網址")
    print("   7. 修改員工姓名觀察頭像即時變化")
    
    print("\n🎯 特色功能測試：")
    print("   • 新增員工 → 輸入姓名 → 觀察頭像生成")
    print("   • 編輯無照片員工 → 觀察個性化頭像")
    print("   • 編輯有照片員工 → 觀察照片顯示")
    print("   • 上傳照片 → 測試文件大小限制")
    print("   • 輸入無效連結 → 測試錯誤處理")
    print("   • 修改姓名 → 觀察頭像顏色變化")
    
    print("\n🎨 設計亮點：")
    print("   • 圓角設計：現代化的圓角照片容器")
    print("   • 漸層背景：美觀的漸層色彩頭像")
    print("   • 陰影效果：立體感的視覺效果")
    print("   • 響應式佈局：適配不同螢幕尺寸")
    print("   • 懸停效果：互動式的按鈕反饋")
    
    print(f"\n🌐 正在開啟瀏覽器...")
    print(f"   URL: {BASE_URL}/elite/employees")
    
    # 開啟瀏覽器
    webbrowser.open(f"{BASE_URL}/elite/employees")
    
    print("\n✨ 頁面已開啟，請在瀏覽器中測試照片功能！")
    print("   按 Ctrl+C 結束演示")
    
    try:
        # 保持腳本運行，讓用戶有時間測試
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n👋 演示結束，感謝使用！")
        print("💡 提示：照片功能讓員工管理更加直觀和人性化")

if __name__ == "__main__":
    demo_employee_photo_features() 