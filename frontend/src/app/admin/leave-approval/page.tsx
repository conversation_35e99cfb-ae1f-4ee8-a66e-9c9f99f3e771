"use client"

import { useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { login } from '@/lib/api-client'
import {
  ArrowLeft,
  Clock,
  PlusCircle,
  CheckCircle,
  XCircle,
  Calendar,
  User,
  FileText,
  Eye,
  Check,
  X,
  AlertCircle,
  Search,
  Filter,
  Download,
  RefreshCw,
  MessageSquare,
  Building
} from 'lucide-react'

interface LeaveRequest {
  id: number
  employee_id: string
  employee_name: string
  department_name: string
  leave_type: string
  start_date: string
  end_date: string
  days_count: number
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  submitted_at: string
  created_at: string
  approved_by?: string
  approved_at?: string
  rejection_reason?: string
  emergency_contact: string
  substitute_id?: number
  substitute_name?: string
}

export default function LeaveApprovalPage() {
  const { user, login: authLogin, loading } = useAuth()
  const router = useRouter()
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
  const [dataLoading, setDataLoading] = useState(false)
  const [autoLoginLoading, setAutoLoginLoading] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null)
  const [showApprovalModal, setShowApprovalModal] = useState(false)
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve')
  const [approvalComment, setApprovalComment] = useState('')
  const [searchParams, setSearchParams] = useState({
    employee_name: '',
    status: '',
    leave_type: ''
  })
  const [filteredRequests, setFilteredRequests] = useState<LeaveRequest[]>([])
  const [stats, setStats] = useState({
    pending: 0,
    todayNew: 0,
    approved: 0,
    rejected: 0
  })

  // 請假類型對應
  const leaveTypeMap: { [key: string]: string } = {
    annual: '年假',
    sick: '病假',
    personal: '事假',
    maternity: '產假',
    paternity: '陪產假',
    marriage: '婚假',
    funeral: '喪假',
    compensatory: '補休',
    other: '其他'
  }

  // 自動登入功能 - 使用管理員測試帳號
  const handleAutoLogin = async () => {
    setAutoLoginLoading(true)
    try {
      console.log('開始自動登入管理員帳號')
      const response = await login({
        employee_id: 'admin',
        password: 'admin123'
      })

      if (response && response.user) {
        const userData = response.user
        const user = {
          id: userData.employee_id,
          name: userData.employee_name,
          employee_id: userData.employee_code,
          department_id: userData.department_id,
          position: userData.role_id === 999 ? '系統管理員' : '員工',
          email: userData.email,
          role_id: userData.role_id,
          department_name: userData.department_name
        }

        authLogin(user)
        console.log('自動登入成功:', user)
      } else {
        console.error('自動登入失敗:', response)
      }
    } catch (error) {
      console.error('自動登入錯誤:', error)
    } finally {
      setAutoLoginLoading(false)
    }
  }

  // 頁面載入時自動登入（如果未登入）
  useEffect(() => {
    if (!loading && !user) {
      console.log('檢測到未登入，執行自動登入')
      handleAutoLogin()
    }
  }, [loading, user])

  // 載入真實的請假申請數據
  useEffect(() => {
    const fetchLeaveRequests = async () => {
      try {
        setDataLoading(true)
        const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
          ? 'http://localhost:7072'
          : `http://${window.location.hostname}:7072`
        const response = await fetch(`${apiBaseUrl}/api/leave-requests`)

        if (!response.ok) {
          throw new Error('Failed to fetch leave requests')
        }

        const data = await response.json()
        const rawRequests = data.records || []

        // 映射 API 數據到前端期望的格式
        const requests = rawRequests.map((item: any) => {
          // 安全的日期計算
          let daysCount = 1
          try {
            const startDate = new Date(item.start_date)
            const endDate = new Date(item.end_date)
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
              daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
            }
          } catch (error) {
            console.warn('日期計算錯誤:', item.start_date, item.end_date, error)
          }

          return {
            id: item.id,
            employee_id: item.employee_code || item.employee_id || '',
            employee_name: item.employee_name || '未知員工',
            department_name: item.department_name || '未知部門',
            leave_type: item.leave_type,
            start_date: item.start_date,
            end_date: item.end_date,
            days_count: daysCount,
            reason: item.reason || '無',
            status: item.status,
            submitted_at: item.created_at,
            created_at: item.created_at,
            approved_by: item.approver_name,
            approved_at: item.approved_at,
            rejection_reason: item.comment,
            emergency_contact: item.emergency_contact || '無',
            substitute_id: item.substitute_id,
            substitute_name: item.substitute_name || '無'
          }
        })

        console.log('API 原始數據筆數:', rawRequests.length)
        console.log('映射後數據筆數:', requests.length)
        console.log('前3筆映射後的數據:', requests.slice(0, 3))

        setLeaveRequests(requests)
        setFilteredRequests(requests)

        // 計算統計數據
        const newStats = {
          pending: requests.filter((r: LeaveRequest) => r.status === 'pending').length,
          todayNew: requests.filter((r: LeaveRequest) =>
            new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()
          ).length,
          approved: requests.filter((r: LeaveRequest) => r.status === 'approved').length,
          rejected: requests.filter((r: LeaveRequest) => r.status === 'rejected').length
        }
        setStats(newStats)
      } catch (error) {
        console.error('Error fetching leave requests:', error)
        // 如果API失敗，設置空數據
        setLeaveRequests([])
        setFilteredRequests([])
        setStats({ pending: 0, todayNew: 0, approved: 0, rejected: 0 })
      } finally {
        setDataLoading(false)
      }
    }

    fetchLeaveRequests()
  }, [])

  // 搜索和篩選
  const handleSearch = () => {
    let filtered = leaveRequests

    if (searchParams.employee_name) {
      filtered = filtered.filter(request =>
        request.employee_name?.toLowerCase().includes(searchParams.employee_name.toLowerCase())
      )
    }
    if (searchParams.status) {
      filtered = filtered.filter(request => request.status === searchParams.status)
    }
    if (searchParams.leave_type) {
      filtered = filtered.filter(request => request.leave_type === searchParams.leave_type)
    }

    setFilteredRequests(filtered)
  }

  const handleReset = () => {
    setSearchParams({
      employee_name: '',
      status: '',
      leave_type: ''
    })
    setFilteredRequests(leaveRequests)
  }

  // 處理審核
  const handleApproval = async () => {
    if (!selectedRequest) return

    try {
      // 調用真實的API
      const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
        ? 'http://localhost:7072'
        : `http://${window.location.hostname}:7072`
      const response = await fetch(`${apiBaseUrl}/api/approval/leaves/${selectedRequest.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: approvalAction,
          comment: approvalAction === 'reject' ? approvalComment : undefined
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update leave request')
      }

      // 重新載入數據
      const fetchResponse = await fetch(`${apiBaseUrl}/api/leave-requests`)
      if (fetchResponse.ok) {
        const data = await fetchResponse.json()
        const rawRequests = data.records || []

        // 映射 API 數據到前端期望的格式
        const requests = rawRequests.map((item: any) => {
          // 安全的日期計算
          let daysCount = 1
          try {
            const startDate = new Date(item.start_date)
            const endDate = new Date(item.end_date)
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
              daysCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
            }
          } catch (error) {
            console.warn('日期計算錯誤:', item.start_date, item.end_date, error)
          }

          return {
            id: item.id,
            employee_id: item.employee_code || item.employee_id || '',
            employee_name: item.employee_name || '未知員工',
            department_name: item.department_name || '未知部門',
            leave_type: item.leave_type,
            start_date: item.start_date,
            end_date: item.end_date,
            days_count: daysCount,
            reason: item.reason || '無',
            status: item.status,
            submitted_at: item.created_at,
            created_at: item.created_at,
            approved_by: item.approver_name,
            approved_at: item.approved_at,
            rejection_reason: item.comment,
            emergency_contact: item.emergency_contact || '無',
            substitute_id: item.substitute_id,
            substitute_name: item.substitute_name || '無'
          }
        })

        setLeaveRequests(requests)
        setFilteredRequests(requests.filter((request: LeaveRequest) => {
          let match = true
          if (searchParams.employee_name) {
            match = match && request.employee_name?.toLowerCase().includes(searchParams.employee_name.toLowerCase())
          }
          if (searchParams.status) {
            match = match && request.status === searchParams.status
          }
          if (searchParams.leave_type) {
            match = match && request.leave_type === searchParams.leave_type
          }
          return match
        }))

        // 更新統計
        const newStats = {
          pending: requests.filter((r: LeaveRequest) => r.status === 'pending').length,
          todayNew: requests.filter((r: LeaveRequest) =>
            new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()
          ).length,
          approved: requests.filter((r: LeaveRequest) => r.status === 'approved').length,
          rejected: requests.filter((r: LeaveRequest) => r.status === 'rejected').length
        }
        setStats(newStats)
      }

      setShowApprovalModal(false)
      setSelectedRequest(null)
      setApprovalComment('')
    } catch (error) {
      console.error('Error updating leave request:', error)
      alert('審核失敗，請稍後再試')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-warning-600 bg-warning-50 border-warning-200'
      case 'approved': return 'text-success-600 bg-success-50 border-success-200'
      case 'rejected': return 'text-error-600 bg-error-50 border-error-200'
      default: return 'text-neutral-600 bg-neutral-50 border-neutral-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待審核'
      case 'approved': return '已核准'
      case 'rejected': return '已拒絕'
      default: return '未知'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />
      case 'approved': return <CheckCircle className="w-4 h-4" />
      case 'rejected': return <XCircle className="w-4 h-4" />
      default: return <AlertCircle className="w-4 h-4" />
    }
  }

  if (loading || autoLoginLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {autoLoginLoading ? '自動登入中...' : '載入中...'}
          </p>
        </div>
      </div>
    )
  }

  if (!user || user.role_id !== 999) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">權限不足</h2>
          <p className="text-gray-600 mb-4">您需要管理員權限才能訪問此頁面</p>
          <Button onClick={() => router.push('/admin')}>
            返回管理後台
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      {/* 🎨 頁面標題 - 統一的標題設計 */}
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
        <div className="flex items-center justify-between">
          {/* 📍 左側標題區 */}
          <div className="relative z-10">
            <h1 className="text-3xl font-bold mb-2 text-white">請假審核管理</h1>
            <div className="flex items-center space-x-2">
              {/* 🔙 返回按鈕 - 圖標+文字設計 */}
              <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
              </Link>
              <p className="text-indigo-100 text-base font-medium">管理待審核的請假申請與其他審核事項</p>
            </div>
          </div>

          {/* 📍 右側資訊區 */}
          <div className="flex items-center space-x-3 text-right">
            <div>
              <p className="text-sm font-medium text-white">管理員模式</p>
              <p className="text-xs text-indigo-100">請假審核</p>
            </div>
            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
              <FileText className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 頁面標題 */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">請假審核管理中心</h2>
          <p className="text-lg text-gray-600">處理員工請假申請，確保工作流程順暢</p>
        </div>

        {/* 統計卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* 待審核總數 */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
            <div className="flex items-center justify-between">
              <div className="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-warning-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                <p className="text-sm text-gray-500">待審核</p>
              </div>
            </div>
          </div>

          {/* 今日新增 */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
            <div className="flex items-center justify-between">
              <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                <PlusCircle className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.todayNew}</p>
                <p className="text-sm text-gray-500">今日新增</p>
              </div>
            </div>
          </div>

          {/* 已核准 */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
            <div className="flex items-center justify-between">
              <div className="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-success-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                <p className="text-sm text-gray-500">已核准</p>
              </div>
            </div>
          </div>

          {/* 已拒絕 */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
            <div className="flex items-center justify-between">
              <div className="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                <XCircle className="w-6 h-6 text-error-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                <p className="text-sm text-gray-500">已拒絕</p>
              </div>
            </div>
          </div>
        </div>

        {/* 查詢工具欄 */}
        <div className="bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8">
          {/* 標題區域 */}
          <div className="bg-gradient-to-r from-purple-500/5 to-pink-500/5 px-8 py-6 border-b border-gray-100/60">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Filter className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">篩選條件</h3>
                  <p className="text-sm text-gray-500 mt-1">快速找到特定的請假申請</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重新載入</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>匯出報表</span>
                </Button>
              </div>
            </div>
          </div>

          {/* 查詢表單 */}
          <div className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              {/* 員工姓名搜尋 */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-blue-600" />
                  <label className="block text-sm font-semibold text-gray-700">員工姓名</label>
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchParams.employee_name}
                    onChange={(e) => setSearchParams({ ...searchParams, employee_name: e.target.value })}
                    placeholder="搜尋員工姓名..."
                    className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                  />
                </div>
              </div>

              {/* 審核狀態篩選 */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <label className="block text-sm font-semibold text-gray-700">審核狀態</label>
                </div>
                <select
                  value={searchParams.status}
                  onChange={(e) => setSearchParams({ ...searchParams, status: e.target.value })}
                  className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm"
                >
                  <option value="">所有狀態</option>
                  <option value="pending">待審核</option>
                  <option value="approved">已核准</option>
                  <option value="rejected">已拒絕</option>
                </select>
              </div>

              {/* 請假類型篩選 */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-purple-600" />
                  <label className="block text-sm font-semibold text-gray-700">請假類型</label>
                </div>
                <select
                  value={searchParams.leave_type}
                  onChange={(e) => setSearchParams({ ...searchParams, leave_type: e.target.value })}
                  className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm"
                >
                  <option value="">所有類型</option>
                  {Object.entries(leaveTypeMap).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* 操作按鈕 */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-100">
              <div className="flex items-center space-x-3">
                <Button variant="primary" onClick={handleSearch} className="flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>查詢</span>
                </Button>

                <Button variant="outline" onClick={handleReset} className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4" />
                  <span>重置</span>
                </Button>


              </div>

              <div className="text-sm text-gray-500">
                共找到 {filteredRequests.length} 筆申請
              </div>
            </div>
          </div>
        </div>

        {/* 請假申請列表 */}
        <div className="bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
          {/* 標題區域 */}
          <div className="bg-gradient-to-r from-green-500/5 to-blue-500/5 px-8 py-6 border-b border-gray-100/60">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">請假申請列表</h3>
                  <p className="text-sm text-gray-500 mt-1">管理員工的請假申請</p>
                </div>
              </div>
            </div>
          </div>

          {dataLoading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-500">載入請假申請中...</p>
              </div>
            </div>
          ) : filteredRequests.length > 0 ? (
            <>
              {/* 桌面版表格 */}
              <div className="hidden lg:block overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gradient-to-r from-green-500 to-blue-600 text-white">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">申請人</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">請假類型</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">請假日期</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">天數</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">狀態</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">申請時間</th>
                      <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {request.employee_name?.charAt(0) || '?'}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{request.employee_name}</div>
                              <div className="text-sm text-gray-500">{request.employee_id}</div>
                              <div className="text-xs text-gray-400">{request.department_name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-600 border border-blue-200">
                            {leaveTypeMap[request.leave_type] || request.leave_type || '未知'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          <div>{new Date(request.start_date).toLocaleDateString('zh-TW')}</div>
                          {request.start_date !== request.end_date && (
                            <div className="text-xs text-gray-500">
                              至 {new Date(request.end_date).toLocaleDateString('zh-TW')}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {request.days_count} 天
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                            {getStatusIcon(request.status)}
                            <span className="ml-1">{getStatusText(request.status)}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {new Date(request.created_at || request.submitted_at).toLocaleDateString('zh-TW')}
                          <div className="text-xs text-gray-500">
                            {new Date(request.created_at || request.submitted_at).toLocaleTimeString('zh-TW', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedRequest(request)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            {request.status === 'pending' && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedRequest(request)
                                    setApprovalAction('approve')
                                    setShowApprovalModal(true)
                                  }}
                                >
                                  <Check className="w-4 h-4 text-green-600" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedRequest(request)
                                    setApprovalAction('reject')
                                    setShowApprovalModal(true)
                                  }}
                                >
                                  <X className="w-4 h-4 text-red-600" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 手機版卡片 */}
              <div className="lg:hidden space-y-4 p-6">
                {filteredRequests.map((request) => (
                  <div key={request.id} className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {request.employee_name?.charAt(0) || '?'}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{request.employee_name}</h3>
                          <p className="text-sm text-gray-500">{request.department_name}</p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                        {getStatusIcon(request.status)}
                        <span className="ml-1">{getStatusText(request.status)}</span>
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                      <div>
                        <span className="text-gray-500">類型:</span>
                        <span className="ml-2 text-gray-900">{leaveTypeMap[request.leave_type] || request.leave_type || '未知'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">天數:</span>
                        <span className="ml-2 text-gray-900">{request.days_count} 天</span>
                      </div>
                      <div>
                        <span className="text-gray-500">開始:</span>
                        <span className="ml-2 text-gray-900">
                          {new Date(request.start_date).toLocaleDateString('zh-TW')}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">結束:</span>
                        <span className="ml-2 text-gray-900">
                          {new Date(request.end_date).toLocaleDateString('zh-TW')}
                        </span>
                      </div>
                    </div>

                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-500 text-sm">原因:</span>
                      <p className="text-gray-900 text-sm mt-1">{request.reason}</p>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="text-xs text-gray-500">
                        申請於 {new Date(request.created_at || request.submitted_at).toLocaleDateString('zh-TW')}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedRequest(request)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {request.status === 'pending' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedRequest(request)
                                setApprovalAction('approve')
                                setShowApprovalModal(true)
                              }}
                            >
                              <Check className="w-4 h-4 text-green-600" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedRequest(request)
                                setApprovalAction('reject')
                                setShowApprovalModal(true)
                              }}
                            >
                              <X className="w-4 h-4 text-red-600" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">查無請假申請</h3>
              <p className="text-gray-500">請調整搜尋條件或等待新的申請</p>
            </div>
          )}
        </div>
      </main>

      {/* 審核Modal */}
      {showApprovalModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  {approvalAction === 'approve' ? '核准請假申請' : '拒絕請假申請'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowApprovalModal(false)}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              {/* 申請詳情 */}
              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <h4 className="font-semibold text-gray-900 mb-4">申請詳情</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">申請人:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.employee_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">部門:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.department_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">請假類型:</span>
                    <span className="ml-2 text-gray-900">{leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || '未知'}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">請假天數:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.days_count} 天</span>
                  </div>
                  <div>
                    <span className="text-gray-500">開始日期:</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(selectedRequest.start_date).toLocaleDateString('zh-TW')}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">結束日期:</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(selectedRequest.end_date).toLocaleDateString('zh-TW')}
                    </span>
                  </div>
                  {selectedRequest.substitute_name && (
                    <div>
                      <span className="text-gray-500">代理人:</span>
                      <span className="ml-2 text-gray-900">{selectedRequest.substitute_name}</span>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-500">緊急聯絡:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.emergency_contact}</span>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-gray-500 text-sm">請假原因:</span>
                  <p className="text-gray-900 mt-1">{selectedRequest.reason}</p>
                </div>
              </div>

              {/* 審核意見 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {approvalAction === 'approve' ? '核准意見（可選）' : '拒絕原因（必填）'}
                </label>
                <textarea
                  value={approvalComment}
                  onChange={(e) => setApprovalComment(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
                  placeholder={
                    approvalAction === 'approve'
                      ? '輸入核准意見...'
                      : '請說明拒絕原因...'
                  }
                  required={approvalAction === 'reject'}
                />
              </div>

              {/* 操作按鈕 */}
              <div className="flex items-center justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setShowApprovalModal(false)}
                >
                  取消
                </Button>
                <Button
                  variant={approvalAction === 'approve' ? 'success' : 'error'}
                  onClick={handleApproval}
                  disabled={approvalAction === 'reject' && !approvalComment.trim()}
                  className="flex items-center space-x-2"
                >
                  {approvalAction === 'approve' ? (
                    <>
                      <Check className="w-4 h-4" />
                      <span>核准申請</span>
                    </>
                  ) : (
                    <>
                      <X className="w-4 h-4" />
                      <span>拒絕申請</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 詳情Modal */}
      {selectedRequest && !showApprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-900">請假申請詳情</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedRequest(null)}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              {/* 申請人資訊 */}
              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <h4 className="font-semibold text-gray-900 mb-4">申請人資訊</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">姓名:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.employee_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">員工編號:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.employee_id}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">部門:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.department_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">緊急聯絡:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.emergency_contact}</span>
                  </div>
                </div>
              </div>

              {/* 請假資訊 */}
              <div className="bg-blue-50 rounded-2xl p-6 mb-6">
                <h4 className="font-semibold text-gray-900 mb-4">請假資訊</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">請假類型:</span>
                    <span className="ml-2 text-gray-900">{leaveTypeMap[selectedRequest.leave_type] || selectedRequest.leave_type || '未知'}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">請假天數:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.days_count} 天</span>
                  </div>
                  <div>
                    <span className="text-gray-500">開始日期:</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(selectedRequest.start_date).toLocaleDateString('zh-TW')}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">結束日期:</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(selectedRequest.end_date).toLocaleDateString('zh-TW')}
                    </span>
                  </div>
                  {selectedRequest.substitute_name && (
                    <div className="col-span-2">
                      <span className="text-gray-500">代理人:</span>
                      <span className="ml-2 text-gray-900">{selectedRequest.substitute_name}</span>
                    </div>
                  )}
                </div>
                <div className="mt-4">
                  <span className="text-gray-500 text-sm">請假原因:</span>
                  <p className="text-gray-900 mt-1">{selectedRequest.reason}</p>
                </div>
              </div>

              {/* 審核狀態 */}
              <div className={`rounded-2xl p-6 mb-6 ${selectedRequest.status === 'approved' ? 'bg-green-50' :
                selectedRequest.status === 'rejected' ? 'bg-red-50' : 'bg-yellow-50'
                }`}>
                <h4 className="font-semibold text-gray-900 mb-4">審核狀態</h4>
                <div className="flex items-center space-x-3 mb-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(selectedRequest.status)}`}>
                    {getStatusIcon(selectedRequest.status)}
                    <span className="ml-1">{getStatusText(selectedRequest.status)}</span>
                  </span>
                  <span className="text-sm text-gray-500">
                    申請於 {new Date(selectedRequest.created_at || selectedRequest.submitted_at).toLocaleString('zh-TW')}
                  </span>
                </div>
                {selectedRequest.approved_by && (
                  <div className="text-sm">
                    <span className="text-gray-500">審核人:</span>
                    <span className="ml-2 text-gray-900">{selectedRequest.approved_by}</span>
                    <span className="ml-4 text-gray-500">審核時間:</span>
                    <span className="ml-2 text-gray-900">
                      {selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString('zh-TW')}
                    </span>
                  </div>
                )}
                {selectedRequest.rejection_reason && (
                  <div className="mt-3 p-3 bg-red-100 rounded-lg">
                    <span className="text-red-700 text-sm font-medium">拒絕原因:</span>
                    <p className="text-red-800 text-sm mt-1">{selectedRequest.rejection_reason}</p>
                  </div>
                )}
              </div>

              {/* 操作按鈕 */}
              <div className="flex items-center justify-end space-x-4">
                {selectedRequest.status === 'pending' && (
                  <>
                    <Button
                      variant="success"
                      onClick={() => {
                        setApprovalAction('approve')
                        setShowApprovalModal(true)
                      }}
                      className="flex items-center space-x-2"
                    >
                      <Check className="w-4 h-4" />
                      <span>核准</span>
                    </Button>
                    <Button
                      variant="error"
                      onClick={() => {
                        setApprovalAction('reject')
                        setShowApprovalModal(true)
                      }}
                      className="flex items-center space-x-2"
                    >
                      <X className="w-4 h-4" />
                      <span>拒絕</span>
                    </Button>
                  </>
                )}
                <Button
                  variant="outline"
                  onClick={() => setSelectedRequest(null)}
                >
                  關閉
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}