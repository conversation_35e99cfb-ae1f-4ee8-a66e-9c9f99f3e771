#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料庫結構查詢工具

快速查看資料庫表格結構和欄位資訊的命令行工具。
使用方法：
    python db_schema_check.py                    # 查看所有表格
    python db_schema_check.py employees          # 查看特定表格
    python db_schema_check.py employees attendance  # 查看多個表格
"""

import sqlite3
import sys

def create_connection():
    """創建資料庫連接"""
    try:
        conn = sqlite3.connect('../../attendance.db')
        return conn
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return None

def get_all_tables():
    """獲取所有表格名稱"""
    conn = create_connection()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        print(f"❌ 獲取表格列表失敗: {e}")
        conn.close()
        return []

def get_table_structure(table_name):
    """獲取表格結構資訊"""
    conn = create_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        
        # 獲取表格結構
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # 獲取外鍵資訊
        cursor.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = cursor.fetchall()
        
        # 獲取索引資訊
        cursor.execute(f"PRAGMA index_list({table_name})")
        indexes = cursor.fetchall()
        
        conn.close()
        
        return {
            'columns': columns,
            'foreign_keys': foreign_keys,
            'indexes': indexes
        }
    except Exception as e:
        print(f"❌ 獲取表格 {table_name} 結構失敗: {e}")
        conn.close()
        return None

def print_table(headers, rows, title=None):
    """簡單的表格打印函數"""
    if title:
        print(f"\n{title}")
    
    # 計算每列的最大寬度
    col_widths = []
    for i, header in enumerate(headers):
        max_width = len(str(header))
        for row in rows:
            if i < len(row):
                max_width = max(max_width, len(str(row[i])))
        col_widths.append(max_width + 2)  # 加2個空格的padding
    
    # 打印分隔線
    separator = "+" + "+".join("-" * width for width in col_widths) + "+"
    print(separator)
    
    # 打印標題行
    header_row = "|"
    for i, header in enumerate(headers):
        header_row += f" {str(header).ljust(col_widths[i]-1)}|"
    print(header_row)
    print(separator)
    
    # 打印數據行
    for row in rows:
        data_row = "|"
        for i, cell in enumerate(row):
            if i < len(col_widths):
                data_row += f" {str(cell).ljust(col_widths[i]-1)}|"
        print(data_row)
    
    print(separator)

def format_column_info(columns, foreign_keys):
    """格式化欄位資訊"""
    # 建立外鍵映射
    fk_map = {}
    for fk in foreign_keys:
        fk_map[fk[3]] = f"{fk[2]}({fk[4]})"  # 欄位名 -> 參考表(參考欄位)
    
    formatted_columns = []
    for col in columns:
        cid, name, data_type, not_null, default_value, pk = col
        
        # 建立約束條件字符串
        constraints = []
        if pk:
            constraints.append("PRIMARY KEY")
        if not_null:
            constraints.append("NOT NULL")
        if name in fk_map:
            constraints.append(f"FK → {fk_map[name]}")
        if default_value is not None:
            constraints.append(f"DEFAULT {default_value}")
        
        constraint_str = ", ".join(constraints) if constraints else ""
        
        formatted_columns.append([
            name,
            data_type,
            constraint_str
        ])
    
    return formatted_columns

def display_table_structure(table_name):
    """顯示表格結構"""
    print(f"\n📋 表格: {table_name}")
    print("=" * 60)
    
    structure = get_table_structure(table_name)
    if not structure:
        print(f"❌ 無法獲取表格 {table_name} 的結構")
        return
    
    if not structure['columns']:
        print(f"⚠️  表格 {table_name} 不存在或沒有欄位")
        return
    
    # 顯示欄位資訊
    formatted_columns = format_column_info(structure['columns'], structure['foreign_keys'])
    headers = ["欄位名稱", "資料類型", "約束條件"]
    print_table(headers, formatted_columns)
    
    # 顯示索引資訊
    if structure['indexes']:
        print(f"\n🔍 索引資訊:")
        for idx in structure['indexes']:
            print(f"   - {idx[1]} ({'UNIQUE' if idx[2] else 'NON-UNIQUE'})")
    
    # 顯示記錄數量
    conn = create_connection()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"\n📊 記錄數量: {count} 筆")
            conn.close()
        except:
            conn.close()

def display_all_tables():
    """顯示所有表格概覽"""
    tables = get_all_tables()
    if not tables:
        print("❌ 沒有找到任何表格")
        return
    
    print("🗂️  資料庫表格總覽")
    print("=" * 60)
    
    table_info = []
    for table in tables:
        structure = get_table_structure(table)
        if structure and structure['columns']:
            column_count = len(structure['columns'])
            
            # 獲取記錄數量
            conn = create_connection()
            record_count = 0
            if conn:
                try:
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    record_count = cursor.fetchone()[0]
                    conn.close()
                except:
                    conn.close()
            
            table_info.append([table, column_count, record_count])
    
    headers = ["表格名稱", "欄位數量", "記錄數量"]
    print_table(headers, table_info)
    
    print(f"\n💡 使用方法:")
    print(f"   python {sys.argv[0]} <表格名稱>     # 查看特定表格結構")
    print(f"   python {sys.argv[0]} employees      # 查看員工表結構")

def main():
    """主函數"""
    print("🔍 AttendanceOS Elite - 資料庫結構查詢工具")
    print("📋 完整文檔請參閱: DATABASE_SCHEMA.md")
    
    if len(sys.argv) == 1:
        # 沒有參數，顯示所有表格
        display_all_tables()
    else:
        # 有參數，顯示指定表格
        for table_name in sys.argv[1:]:
            display_table_structure(table_name)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程式已中斷")
    except Exception as e:
        print(f"\n❌ 程式執行錯誤: {e}") 