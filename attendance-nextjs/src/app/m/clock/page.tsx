"use client"

import { withAuth, useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { clockIn, clockOut, getTodayAttendance } from '@/lib/api-client'
import type { AttendanceRecord } from '@/types'
import {
    Clock,
    MapPin,
    ArrowLeft
} from 'lucide-react'
import Link from 'next/link'

function ClockPage() {
    const { user } = useAuth()
    const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null)
    const [loading, setLoading] = useState(true)
    const [clockingIn, setClockinIn] = useState(false)
    const [clockingOut, setClockingOut] = useState(false)
    const [currentTime, setCurrentTime] = useState(new Date())

    // 更新當前時間
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date())
        }, 1000)

        return () => clearInterval(timer)
    }, [])

    // 載入今日考勤記錄
    useEffect(() => {
        const fetchTodayAttendance = async () => {
            if (!user?.employee_id) return

            try {
                const response = await getTodayAttendance(user.employee_id)
                if (response.success && response.data) {
                    setTodayAttendance(response.data)
                }
            } catch (error) {
                console.error('載入今日考勤記錄失敗:', error)
            } finally {
                setLoading(false)
            }
        }

        fetchTodayAttendance()
    }, [user])

    const handleClockIn = async () => {
        if (!user?.employee_id) return

        setClockinIn(true)
        try {
            const response = await clockIn({ employee_id: user.employee_id })
            if (response.success) {
                // 重新載入今日考勤記錄
                const attendanceResponse = await getTodayAttendance(user.employee_id)
                if (attendanceResponse.success && attendanceResponse.data) {
                    setTodayAttendance(attendanceResponse.data)
                }
            } else {
                alert(response.error || '打卡失敗')
            }
        } catch (error) {
            alert('打卡失敗，請稍後再試')
        } finally {
            setClockinIn(false)
        }
    }

    const handleClockOut = async () => {
        if (!user?.employee_id) return

        setClockingOut(true)
        try {
            const response = await clockOut({ employee_id: user.employee_id })
            if (response.success) {
                // 重新載入今日考勤記錄
                const attendanceResponse = await getTodayAttendance(user.employee_id)
                if (attendanceResponse.success && attendanceResponse.data) {
                    setTodayAttendance(attendanceResponse.data)
                }
            } else {
                alert(response.error || '打卡失敗')
            }
        } catch (error) {
            alert('打卡失敗，請稍後再試')
        } finally {
            setClockingOut(false)
        }
    }

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700">
            {/* Header */}
            <header className="bg-white/10 backdrop-blur-xl border-b border-white/20 sticky top-0 z-10">
                <div className="px-4 py-3">
                    <div className="flex items-center">
                        <Link href="/m">
                            <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
                                <ArrowLeft className="w-4 h-4" />
                            </Button>
                        </Link>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="px-6 py-8">
                {/* 當前時間顯示 */}
                <div className="text-center mb-12">
                    <div className="bg-white/15 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                        <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg">
                            <Clock className="w-8 h-8 text-white" />
                        </div>
                        <h2 className="text-4xl font-bold text-white mb-3">
                            {currentTime.toLocaleTimeString('zh-TW', {
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            })}
                        </h2>
                        <p className="text-lg text-white/90 mb-4">
                            {currentTime.toLocaleDateString('zh-TW', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                weekday: 'long'
                            })}
                        </p>

                        {/* 用戶資料區域 */}
                        <div className="flex items-center justify-center space-x-3 mb-4">
                            {/* 預留人頭照片位置 */}
                            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center border-2 border-white/30">
                                <span className="text-white font-bold text-lg">{user?.name?.charAt(0) || 'U'}</span>
                            </div>
                            <div className="text-left">
                                <p className="text-white font-semibold text-lg">{user?.name || '用戶'}</p>
                                <p className="text-white/80 text-sm">{user?.employee_id || 'E001'}</p>
                            </div>
                        </div>

                        <div className="flex items-center justify-center space-x-2 text-white/80">
                            <MapPin className="w-5 h-5" />
                            <span className="text-base">台北辦公室</span>
                        </div>
                    </div>
                </div>

                {/* 打卡按鈕區域 */}
                <div className="flex justify-center space-x-8 mb-12">
                    {/* 上班打卡 */}
                    <div className="text-center">
                        <button
                            onClick={handleClockIn}
                            disabled={!!todayAttendance?.check_in || clockingIn}
                            className={`
                                w-36 h-36 rounded-full flex flex-col items-center justify-center text-white font-bold text-lg transition-all duration-300 transform
                                ${!todayAttendance?.check_in && !clockingIn
                                    ? 'bg-gradient-to-br from-green-500 via-emerald-600 to-teal-700 hover:scale-110 shadow-2xl hover:shadow-green-500/50 shadow-green-500/30'
                                    : 'bg-gray-400 cursor-not-allowed opacity-70'
                                }
                                ${!todayAttendance?.check_in && !clockingIn ? 'animate-pulse' : ''}
                            `}
                            style={{
                                boxShadow: !todayAttendance?.check_in && !clockingIn
                                    ? '0 0 30px rgba(34, 197, 94, 0.6), 0 0 60px rgba(34, 197, 94, 0.3)'
                                    : 'none'
                            }}
                        >
                            <span className="text-xl mb-1">上班</span>
                        </button>
                        <p className="text-white/90 text-sm mt-3 font-medium">
                            {todayAttendance?.check_in ? '已打卡' : '尚未打卡'}
                        </p>
                        {todayAttendance?.check_in && (
                            <p className="text-green-300 text-xs mt-1">
                                {new Date(todayAttendance.check_in).toLocaleTimeString('zh-TW', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </p>
                        )}
                    </div>

                    {/* 下班打卡 */}
                    <div className="text-center">
                        <button
                            onClick={handleClockOut}
                            disabled={!todayAttendance?.check_in || !!todayAttendance?.check_out || clockingOut}
                            className={`
                                w-36 h-36 rounded-full flex flex-col items-center justify-center text-white font-bold text-lg transition-all duration-300 transform
                                ${todayAttendance?.check_in && !todayAttendance?.check_out && !clockingOut
                                    ? 'bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 hover:scale-110 shadow-2xl hover:shadow-blue-500/50 shadow-blue-500/30'
                                    : 'bg-gray-400 cursor-not-allowed opacity-70'
                                }
                                ${todayAttendance?.check_in && !todayAttendance?.check_out && !clockingOut ? 'animate-pulse' : ''}
                            `}
                            style={{
                                boxShadow: todayAttendance?.check_in && !todayAttendance?.check_out && !clockingOut
                                    ? '0 0 30px rgba(59, 130, 246, 0.6), 0 0 60px rgba(59, 130, 246, 0.3)'
                                    : 'none'
                            }}
                        >
                            <span className="text-xl mb-1">下班</span>
                            <span className="text-xs opacity-90">Clock Out</span>
                        </button>
                        <p className="text-white/90 text-sm mt-3 font-medium">
                            {todayAttendance?.check_out ? '已打卡' :
                                todayAttendance?.check_in ? '尚未打卡' : '請先上班打卡'}
                        </p>
                        {todayAttendance?.check_out && (
                            <p className="text-blue-300 text-xs mt-1">
                                {new Date(todayAttendance.check_out).toLocaleTimeString('zh-TW', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </p>
                        )}
                    </div>
                </div>

                {/* 今日考勤狀態 */}
                {todayAttendance && (
                    <div className="bg-white/15 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-xl font-bold text-white flex items-center">
                                <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg mr-3 flex items-center justify-center">
                                    <Clock className="w-4 h-4 text-white" />
                                </div>
                                今日考勤
                            </h3>
                            <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full ${todayAttendance.check_out ? 'bg-gray-400' :
                                    todayAttendance.check_in ? 'bg-green-400 animate-pulse' :
                                        'bg-yellow-400'
                                    }`}></div>
                                <span className="text-white/90 text-sm font-medium">
                                    {todayAttendance.check_out ? '工作中' :
                                        todayAttendance.check_in ? '已下班' :
                                            '未上班'}
                                </span>
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="bg-white/10 rounded-2xl p-4 text-center">
                                <p className="text-white/70 text-sm mb-1">上班打卡</p>
                                <p className="text-white text-lg font-bold">
                                    {todayAttendance.check_in ?
                                        new Date(todayAttendance.check_in).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' }) :
                                        '09:00:00'
                                    }
                                </p>
                            </div>
                            <div className="bg-white/10 rounded-2xl p-4 text-center">
                                <p className="text-white/70 text-sm mb-1">下班打卡</p>
                                <p className="text-white text-lg font-bold">
                                    {todayAttendance.check_out ?
                                        new Date(todayAttendance.check_out).toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' }) :
                                        '尚未打卡'
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </main>
        </div>
    )
}

export default withAuth(ClockPage) 