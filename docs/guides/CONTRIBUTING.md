# 貢獻指南 (Contributing Guidelines)

感謝您對智慧考勤系統的興趣！我們歡迎所有形式的貢獻，包括但不限於程式碼、文件、測試、問題回報和功能建議。

## 📋 目錄
- [開發環境設定](#開發環境設定)
- [程式碼規範](#程式碼規範)
- [提交規範](#提交規範)
- [測試規範](#測試規範)
- [文件規範](#文件規範)
- [Pull Request 流程](#pull-request-流程)
- [問題回報](#問題回報)
- [程式碼審查](#程式碼審查)

## 🛠️ 開發環境設定

### 必要條件
- Python 3.8+
- Git
- 程式碼編輯器 (推薦 VS Code)

### 設定步驟
1. **Fork 專案**
   ```bash
   # 在 GitHub 上 fork 專案，然後複製到本地
   git clone https://github.com/your-username/attend.git
   cd attend
   ```

2. **設定虛擬環境**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Unix/macOS
   # 或
   .venv\Scripts\activate     # Windows
   ```

3. **安裝開發相依套件**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # 如果有的話
   ```

4. **設定 pre-commit hooks**
   ```bash
   pip install pre-commit
   pre-commit install
   ```

5. **初始化資料庫**
   ```bash
   python database.py
   ```

## 📝 程式碼規範

### Python 程式碼風格
我們遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 規範：

```python
# ✅ 良好範例
def calculate_overtime_hours(start_time: datetime, end_time: datetime) -> float:
    """
    計算加班時數。
    
    Args:
        start_time: 上班時間
        end_time: 下班時間
        
    Returns:
        加班時數
        
    Raises:
        ValueError: 當時間格式不正確時
    """
    if end_time <= start_time:
        raise ValueError("結束時間必須大於開始時間")
        
    work_hours = (end_time - start_time).total_seconds() / 3600
    return max(0, work_hours - 8)  # 8小時為標準工時

# ❌ 不良範例
def calc_ot(st,et):
    h=(et-st).total_seconds()/3600
    return h-8 if h>8 else 0
```

### 命名規範
- **變數和函數**：使用 snake_case
- **類別**：使用 PascalCase
- **常數**：使用 UPPER_CASE
- **私有變數/函數**：使用前綴 _

```python
# ✅ 良好範例
class AttendanceManager:
    MAX_WORKING_HOURS = 12
    
    def __init__(self):
        self._connection = None
    
    def get_employee_attendance(self, employee_id: str) -> List[Dict]:
        """取得員工考勤記錄"""
        pass
    
    def _validate_time_format(self, time_str: str) -> bool:
        """私有方法：驗證時間格式"""
        pass
```

### 註解與文檔字串
- 使用有意義的註解解釋 **為什麼**，而不是 **做什麼**
- 所有公開函數必須有文檔字串
- 使用 Google 風格的文檔字串

```python
def process_attendance_data(raw_data: List[Dict]) -> List[AttendanceRecord]:
    """
    處理原始考勤資料並轉換為標準格式。
    
    此函數會自動識別遲到、早退等狀態，並進行資料清理。
    
    Args:
        raw_data: 來自打卡機的原始資料列表
        
    Returns:
        處理後的考勤記錄列表
        
    Raises:
        DataValidationError: 當資料格式不符合預期時
        
    Example:
        >>> raw_data = [{'employee_id': 'E001', 'timestamp': '2024-03-15 09:15:00'}]
        >>> records = process_attendance_data(raw_data)
        >>> len(records)
        1
    """
    # 預處理：移除無效的時間戳記
    # 這是必要的，因為某些舊式打卡機會產生錯誤的時間戳記
    valid_data = [record for record in raw_data if _is_valid_timestamp(record.get('timestamp'))]
    
    # 轉換為標準格式
    processed_records = []
    for record in valid_data:
        # ... 處理邏輯
        pass
    
    return processed_records
```

### 錯誤處理
- 使用明確的例外類型
- 提供有意義的錯誤訊息
- 適當的日誌記錄

```python
import logging

logger = logging.getLogger(__name__)

def update_employee_schedule(employee_id: str, schedule_data: Dict) -> bool:
    """更新員工排班資料"""
    try:
        # 驗證員工是否存在
        if not _employee_exists(employee_id):
            raise EmployeeNotFoundError(f"員工 {employee_id} 不存在")
        
        # 驗證排班資料
        if not _validate_schedule_data(schedule_data):
            raise InvalidScheduleError("排班資料格式不正確")
        
        # 執行更新
        result = _perform_schedule_update(employee_id, schedule_data)
        logger.info(f"成功更新員工 {employee_id} 的排班資料")
        return result
        
    except DatabaseError as e:
        logger.error(f"資料庫錯誤：{e}")
        raise ScheduleUpdateError(f"無法更新排班：{e}")
    except Exception as e:
        logger.error(f"未預期的錯誤：{e}")
        raise
```

## 📤 提交規範

### Commit Message 格式
使用 [Conventional Commits](https://www.conventionalcommits.org/) 規範：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交類型
- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文檔更新
- `style`: 程式碼格式調整（不影響功能）
- `refactor`: 程式碼重構
- `test`: 測試相關
- `chore`: 建置工具或輔助工具的變動

### 最新功能開發記錄 🆕

#### v2025.6.9 - 員工升遷和獎懲管理功能
**功能概述**：完整的人力資源管理功能，包含員工升遷紀錄和獎懲紀錄管理

**主要實現**：
1. **資料庫設計**
   - `promotion_types` 表：升遷類型管理
   - `employee_promotions` 表：員工升遷紀錄
   - `reward_types` 表：獎懲類型管理  
   - `employee_rewards` 表：員工獎懲紀錄

2. **API端點開發**
   - `/api/employees/<id>/promotions` (GET/POST)：升遷紀錄管理
   - `/api/promotion-types` (GET)：升遷類型查詢
   - `/api/employees/<id>/rewards` (GET/POST)：獎懲紀錄管理
   - `/api/reward-types` (GET)：獎懲類型查詢

3. **前端界面設計**
   - Apple風格的升遷紀錄區塊（藍色系漸層）
   - Apple風格的獎懲紀錄區塊（橙色系漸層）
   - 完整的新增、編輯、刪除功能
   - 響應式設計，支援各種螢幕尺寸

4. **測試資料**
   - 為三個員工生成豐富的測試資料
   - 涵蓋各種升遷和獎懲情境

**開發經驗**：
- 前端JavaScript函數必須正確處理async/await
- API數據載入需要完整的錯誤處理機制
- 模態框設計要保持與系統其他部分的一致性
- 資料庫關聯設計要考慮完整性約束

**提交範例**：
```bash
feat(hr): 新增員工升遷和獎懲管理功能

- 新增promotion_types和employee_promotions表格
- 新增reward_types和employee_rewards表格  
- 實現完整的API端點支援
- 添加Apple風格的前端界面
- 包含完整測試資料和錯誤處理

Closes #123
```
- `docs`: 文件變更
- `style`: 格式變更（不影響程式碼邏輯）
- `refactor`: 重構（既不是新功能也不是錯誤修復）
- `test`: 測試相關
- `chore`: 建置過程或輔助工具的變更

### 範例
```bash
# ✅ 良好範例
git commit -m "feat(attendance): add overtime calculation function"
git commit -m "fix(auth): resolve login session timeout issue"
git commit -m "docs(api): update employee management endpoint documentation"
git commit -m "refactor(database): optimize attendance query performance"

# ❌ 不良範例
git commit -m "fix bug"
git commit -m "update code"
git commit -m "changes"
```

### 提交頻率
- 進行邏輯完整的小幅提交
- 避免大型的單一提交
- 每個提交都應該能夠獨立編譯和運行

## 🧪 測試規範

### 測試結構
```
tests/
├── unit/           # 單元測試
├── integration/    # 整合測試
├── fixtures/       # 測試資料
└── conftest.py     # pytest 配置
```

### 測試命名
```python
class TestAttendanceCalculator:
    def test_calculate_normal_working_hours_returns_eight(self):
        """測試標準工時計算返回8小時"""
        pass
    
    def test_calculate_overtime_with_invalid_time_raises_error(self):
        """測試無效時間輸入引發錯誤"""
        pass
    
    def test_calculate_late_arrival_updates_status_correctly(self):
        """測試遲到狀態正確更新"""
        pass
```

### 測試覆蓋率
- 目標：新程式碼測試覆蓋率 > 80%
- 執行測試：`python -m pytest --cov=.`
- 生成報告：`python -m pytest --cov=. --cov-report=html`

## 📚 文件規範

### README 更新
當新增功能時，請更新相關文件：
- API 端點變更 → 更新 API 文檔
- 配置選項變更 → 更新安裝說明
- 新功能 → 更新功能特點章節

### API 文檔
使用以下格式記錄 API：
```markdown
### POST /api/employees

建立新員工資料。

**請求參數：**
- `name` (string, required): 員工姓名
- `employee_id` (string, required): 員工編號
- `department_id` (integer, required): 部門 ID

**請求範例：**
```json
{
  "name": "張三",
  "employee_id": "E001",
  "department_id": 1
}
```

**回應範例：**
```json
{
  "message": "員工新增成功",
  "id": 123
}
```

**錯誤代碼：**
- `400`: 參數缺失或格式錯誤
- `409`: 員工編號已存在
```

## 🔄 Pull Request 流程

### 提交前檢查清單
- [ ] 程式碼遵循專案規範
- [ ] 所有測試通過
- [ ] 新功能有對應測試
- [ ] 文件已更新
- [ ] 提交訊息符合規範
- [ ] 沒有合併衝突

### PR 描述模板
```markdown
## 變更說明
<!-- 簡述這個 PR 的目的和內容 -->

## 變更類型
- [ ] 錯誤修復 (fix)
- [ ] 新功能 (feature)
- [ ] 重構 (refactor)
- [ ] 文件更新 (docs)
- [ ] 其他: ___________

## 測試
<!-- 描述如何測試這些變更 -->
- [ ] 單元測試
- [ ] 整合測試
- [ ] 手動測試

## 檢查清單
- [ ] 我已閱讀並遵循貢獻指南
- [ ] 我的程式碼遵循專案的程式碼風格
- [ ] 我已進行自我審查
- [ ] 我已新增必要的測試
- [ ] 新舊測試都通過
- [ ] 我已更新相關文件

## 相關 Issue
<!-- 如果有相關的 Issue，請在此列出 -->
Closes #(issue_number)
```

### 審查流程
1. **自動檢查**：CI/CD 流程會自動運行測試
2. **程式碼審查**：至少需要一位維護者審查
3. **測試驗證**：確保所有測試通過
4. **合併**：通過審查後合併到主分支

## 🐛 問題回報

### 回報前檢查
- 搜尋現有 Issues 確認問題未被回報
- 確認問題在最新版本中仍然存在
- 收集相關的錯誤訊息和日誌

### 問題回報模板
```markdown
## 問題描述
<!-- 清楚描述遇到的問題 -->

## 復現步驟
1. 進入 '...'
2. 點擊 '....'
3. 滾動到 '....'
4. 看到錯誤

## 預期行為
<!-- 描述應該發生什麼 -->

## 實際行為
<!-- 描述實際發生什麼 -->

## 環境資訊
- OS: [例如 macOS 12.0]
- Python 版本: [例如 3.9.0]
- 瀏覽器: [例如 Chrome 95.0]

## 額外資訊
<!-- 任何有助於解決問題的額外資訊 -->
- 錯誤日誌
- 螢幕截圖
- 相關配置
```

## 👀 程式碼審查

### 審查重點
1. **功能性**：程式碼是否達到預期功能？
2. **可讀性**：程式碼是否易於理解？
3. **效能**：是否有效能問題？
4. **安全性**：是否有安全漏洞？
5. **測試**：測試是否充分？

### 審查態度
- 建設性回饋，避免人身攻擊
- 解釋 **為什麼** 需要修改
- 提供改善建議或範例
- 讚美好的程式碼實作

### 回應審查
- 感謝審查者的時間和建議
- 對不同意的意見進行討論
- 及時回應和修正問題

---

## 🎯 最佳實踐摘要

1. **小步快跑**：頻繁提交小幅變更
2. **測試先行**：撰寫程式碼前先考慮測試
3. **文件同步**：程式碼變更時同步更新文件
4. **積極溝通**：有疑問時主動詢問和討論
5. **持續學習**：關注最新的最佳實踐和工具

感謝您的貢獻！讓我們一起建構更好的智慧考勤系統。