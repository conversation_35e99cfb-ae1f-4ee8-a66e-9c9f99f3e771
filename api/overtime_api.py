"""
加班申請管理API模組

提供加班申請的完整管理功能，包括：
- 加班申請的新增、查詢、修改、刪除
- 加班審核流程
- 加班統計和報表
- 加班類型管理

作者: AI助手
日期: 2025-06-06
版本: 1.0.0
"""

from flask import Blueprint, request, jsonify
import sqlite3
import logging
from datetime import datetime, timedelta
from database import create_connection

# 創建藍圖
overtime_bp = Blueprint('overtime', __name__)
logger = logging.getLogger(__name__)

@overtime_bp.route("/api/overtime/requests", methods=["GET"])
def get_overtime_requests():
    """
    獲取加班申請列表
    
    參數：
    - employee_id: 員工ID（可選）
    - status: 狀態篩選（可選）
    - start_date: 開始日期（可選）
    - end_date: 結束日期（可選）
    - page: 頁碼（預設1）
    - limit: 每頁筆數（預設20）
    
    返回：
    - records: 加班申請記錄列表
    - pagination: 分頁資訊
    """
    try:
        # 獲取查詢參數
        employee_id = request.args.get('employee_id', type=int)
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if employee_id:
            where_conditions.append("ot.employee_id = ?")
            params.append(employee_id)
            
        if status:
            where_conditions.append("ot.status = ?")
            params.append(status)
            
        if start_date:
            where_conditions.append("ot.overtime_date >= ?")
            params.append(start_date)
            
        if end_date:
            where_conditions.append("ot.overtime_date <= ?")
            params.append(end_date)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 查詢總數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM overtime_requests ot
            WHERE {where_clause}
        """
        total = conn.execute(count_query, params).fetchone()['total']
        
        # 查詢記錄
        query = f"""
            SELECT 
                ot.*,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                a.name as approver_name,
                ott.name as overtime_type_name,
                ott.rate as overtime_rate
            FROM overtime_requests ot
            LEFT JOIN employees e ON ot.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON ot.approver_id = a.id
            LEFT JOIN overtime_types ott ON ot.overtime_type = ott.code
            WHERE {where_clause}
            ORDER BY ot.created_at DESC
            LIMIT ? OFFSET ?
        """
        
        params.extend([limit, offset])
        records = conn.execute(query, params).fetchall()
        
        # 轉換為字典格式
        result_records = []
        for record in records:
            record_dict = dict(record)
            result_records.append(record_dict)
        
        # 計算分頁資訊
        total_pages = (total + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        conn.close()
        
        return jsonify({
            "success": True,
            "records": result_records,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        })
        
    except Exception as e:
        logger.error(f"獲取加班申請列表失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/requests", methods=["POST"])
def create_overtime_request():
    """
    新增加班申請
    
    參數：
    - employee_id: 員工ID
    - overtime_date: 加班日期
    - start_time: 開始時間
    - end_time: 結束時間
    - overtime_type: 加班類型
    - reason: 申請原因
    - work_content: 工作內容
    - approver_id: 審核主管ID
    
    返回：
    - success: 是否成功
    - overtime_id: 新建的加班申請ID
    """
    try:
        data = request.get_json()
        
        # 驗證必要欄位
        required_fields = ['employee_id', 'overtime_date', 'start_time', 'end_time', 'overtime_type', 'reason']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"缺少必要欄位: {field}"}), 400
        
        # 計算加班時數
        start_time = datetime.strptime(data['start_time'], '%H:%M')
        end_time = datetime.strptime(data['end_time'], '%H:%M')
        
        # 處理跨日情況
        if end_time < start_time:
            end_time += timedelta(days=1)
        
        overtime_hours = (end_time - start_time).total_seconds() / 3600
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 插入加班申請
        query = """
            INSERT INTO overtime_requests 
            (employee_id, overtime_date, start_time, end_time, overtime_hours, 
             overtime_type, reason, work_content, approver_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
        """
        
        cursor = conn.execute(query, (
            data['employee_id'],
            data['overtime_date'],
            data['start_time'],
            data['end_time'],
            overtime_hours,
            data['overtime_type'],
            data['reason'],
            data.get('work_content', ''),
            data.get('approver_id')
        ))
        
        overtime_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"新增加班申請成功: ID={overtime_id}")
        
        return jsonify({
            "success": True,
            "overtime_id": overtime_id,
            "message": "加班申請提交成功"
        })
        
    except Exception as e:
        logger.error(f"新增加班申請失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/requests/<int:overtime_id>", methods=["GET"])
def get_overtime_request(overtime_id):
    """
    獲取單一加班申請詳情
    
    參數：
    - overtime_id: 加班申請ID
    
    返回：
    - 加班申請詳細資訊
    """
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        query = """
            SELECT 
                ot.*,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                a.name as approver_name,
                ott.name as overtime_type_name,
                ott.rate as overtime_rate
            FROM overtime_requests ot
            LEFT JOIN employees e ON ot.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON ot.approver_id = a.id
            LEFT JOIN overtime_types ott ON ot.overtime_type = ott.code
            WHERE ot.id = ?
        """
        
        record = conn.execute(query, (overtime_id,)).fetchone()
        conn.close()
        
        if not record:
            return jsonify({"error": "找不到指定的加班申請"}), 404
        
        return jsonify({
            "success": True,
            "record": dict(record)
        })
        
    except Exception as e:
        logger.error(f"獲取加班申請詳情失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/requests/<int:overtime_id>", methods=["PUT"])
def update_overtime_request(overtime_id):
    """
    更新加班申請
    
    參數：
    - overtime_id: 加班申請ID
    - 其他可更新的欄位
    
    返回：
    - success: 是否成功
    """
    try:
        data = request.get_json()
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 檢查申請是否存在且可編輯
        check_query = "SELECT status FROM overtime_requests WHERE id = ?"
        record = conn.execute(check_query, (overtime_id,)).fetchone()
        
        if not record:
            conn.close()
            return jsonify({"error": "找不到指定的加班申請"}), 404
        
        if record['status'] != 'pending':
            conn.close()
            return jsonify({"error": "只能修改待審核的申請"}), 400
        
        # 構建更新語句
        update_fields = []
        params = []
        
        updatable_fields = ['overtime_date', 'start_time', 'end_time', 'overtime_type', 'reason', 'work_content', 'approver_id']
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                params.append(data[field])
        
        # 重新計算加班時數
        if 'start_time' in data or 'end_time' in data:
            # 獲取當前時間
            current_record = conn.execute("SELECT start_time, end_time FROM overtime_requests WHERE id = ?", (overtime_id,)).fetchone()
            start_time_str = data.get('start_time', current_record['start_time'])
            end_time_str = data.get('end_time', current_record['end_time'])
            
            start_time = datetime.strptime(start_time_str, '%H:%M')
            end_time = datetime.strptime(end_time_str, '%H:%M')
            
            if end_time < start_time:
                end_time += timedelta(days=1)
            
            overtime_hours = (end_time - start_time).total_seconds() / 3600
            update_fields.append("overtime_hours = ?")
            params.append(overtime_hours)
        
        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(overtime_id)
            
            query = f"UPDATE overtime_requests SET {', '.join(update_fields)} WHERE id = ?"
            conn.execute(query, params)
            conn.commit()
        
        conn.close()
        
        return jsonify({
            "success": True,
            "message": "加班申請更新成功"
        })
        
    except Exception as e:
        logger.error(f"更新加班申請失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/requests/<int:overtime_id>", methods=["DELETE"])
def delete_overtime_request(overtime_id):
    """
    刪除加班申請
    
    參數：
    - overtime_id: 加班申請ID
    
    返回：
    - success: 是否成功
    """
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 檢查申請是否存在且可刪除
        check_query = "SELECT status FROM overtime_requests WHERE id = ?"
        record = conn.execute(check_query, (overtime_id,)).fetchone()
        
        if not record:
            conn.close()
            return jsonify({"error": "找不到指定的加班申請"}), 404
        
        if record['status'] not in ['pending', 'rejected']:
            conn.close()
            return jsonify({"error": "只能刪除待審核和已拒絕的申請"}), 400
        
        # 刪除申請
        conn.execute("DELETE FROM overtime_requests WHERE id = ?", (overtime_id,))
        conn.commit()
        conn.close()
        
        return jsonify({
            "success": True,
            "message": "加班申請刪除成功"
        })
        
    except Exception as e:
        logger.error(f"刪除加班申請失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/requests/<int:overtime_id>/approve", methods=["POST"])
def approve_overtime_request(overtime_id):
    """
    審核加班申請
    
    參數：
    - overtime_id: 加班申請ID
    - action: 'approve' 或 'reject'
    - comment: 審核意見（可選）
    
    返回：
    - success: 是否成功
    """
    try:
        data = request.get_json()
        action = data.get('action')
        comment = data.get('comment', '')
        
        if action not in ['approve', 'reject']:
            return jsonify({"error": "無效的審核動作"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 檢查申請是否存在
        check_query = "SELECT status FROM overtime_requests WHERE id = ?"
        record = conn.execute(check_query, (overtime_id,)).fetchone()
        
        if not record:
            conn.close()
            return jsonify({"error": "找不到指定的加班申請"}), 404
        
        if record['status'] != 'pending':
            conn.close()
            return jsonify({"error": "該申請已經審核過了"}), 400
        
        # 更新審核狀態
        new_status = 'approved' if action == 'approve' else 'rejected'
        
        query = """
            UPDATE overtime_requests 
            SET status = ?, approved_at = CURRENT_TIMESTAMP, reject_reason = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        
        conn.execute(query, (new_status, comment if action == 'reject' else None, overtime_id))
        conn.commit()
        conn.close()
        
        action_text = "核准" if action == 'approve' else "拒絕"
        
        return jsonify({
            "success": True,
            "message": f"加班申請{action_text}成功"
        })
        
    except Exception as e:
        logger.error(f"審核加班申請失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/types", methods=["GET"])
def get_overtime_types():
    """
    獲取加班類型列表
    
    返回：
    - records: 加班類型列表
    """
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, code, name, rate, description, is_active, created_at
            FROM overtime_types 
            WHERE is_active = 1 
            ORDER BY code
        """)
        
        records = []
        for row in cursor.fetchall():
            record = {
                "id": row[0],
                "code": row[1],
                "name": row[2],
                "rate": row[3],
                "description": row[4],
                "is_active": bool(row[5]),
                "created_at": row[6]
            }
            records.append(record)
        
        conn.close()
        
        return jsonify({
            "success": True,
            "records": records
        })
        
    except Exception as e:
        logger.error(f"獲取加班類型失敗: {e}")
        return jsonify({"error": str(e)}), 500

@overtime_bp.route("/api/overtime/statistics", methods=["GET"])
def get_overtime_statistics():
    """
    獲取加班統計資訊
    
    參數：
    - employee_id: 員工ID（可選）
    - start_date: 開始日期（可選）
    - end_date: 結束日期（可選）
    
    返回：
    - 加班統計資訊
    """
    try:
        employee_id = request.args.get('employee_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 構建查詢條件
        where_conditions = ["1=1"]
        params = []
        
        if employee_id:
            where_conditions.append("employee_id = ?")
            params.append(employee_id)
            
        if start_date:
            where_conditions.append("overtime_date >= ?")
            params.append(start_date)
            
        if end_date:
            where_conditions.append("overtime_date <= ?")
            params.append(end_date)
        
        where_clause = " AND ".join(where_conditions)
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        
        # 統計查詢
        stats_query = f"""
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
                COALESCE(SUM(CASE WHEN status = 'approved' THEN overtime_hours ELSE 0 END), 0) as total_approved_hours,
                COALESCE(SUM(overtime_hours), 0) as total_requested_hours
            FROM overtime_requests
            WHERE {where_clause}
        """
        
        stats = conn.execute(stats_query, params).fetchone()
        
        # 按類型統計
        type_stats_query = f"""
            SELECT 
                overtime_type,
                COUNT(*) as count,
                COALESCE(SUM(overtime_hours), 0) as total_hours
            FROM overtime_requests
            WHERE {where_clause}
            GROUP BY overtime_type
        """
        
        type_stats = conn.execute(type_stats_query, params).fetchall()
        
        conn.close()
        
        return jsonify({
            "success": True,
            "total_requests": stats['total_requests'],
            "pending_count": stats['pending_requests'],
            "approved_count": stats['approved_requests'],
            "rejected_count": stats['rejected_requests'],
            "total_hours": round(stats['total_approved_hours'], 2),
            "total_requested_hours": round(stats['total_requested_hours'], 2),
            "statistics": {
                "total_requests": stats['total_requests'],
                "pending_requests": stats['pending_requests'],
                "approved_requests": stats['approved_requests'],
                "rejected_requests": stats['rejected_requests'],
                "total_approved_hours": round(stats['total_approved_hours'], 2),
                "total_requested_hours": round(stats['total_requested_hours'], 2),
                "by_type": [dict(record) for record in type_stats]
            }
        })
        
    except Exception as e:
        logger.error(f"獲取加班統計失敗: {e}")
        return jsonify({"error": str(e)}), 500 