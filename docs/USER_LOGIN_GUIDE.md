# 員工登錄系統使用指南

## 📋 系統概述

員工登錄系統為考勤管理系統提供安全的身份驗證功能，確保只有授權員工才能訪問自助服務系統。

## 🔐 登錄流程

### 1. 訪問登錄頁面
- 瀏覽器訪問：`http://localhost:7072/user`
- 系統會自動檢查登錄狀態
- 未登錄用戶將看到專業的登錄界面

### 2. 輸入登錄資訊
- **員工編號**：輸入您的員工編號（如：E001 或 e001，不區分大小寫）
- **密碼**：輸入您的登錄密碼
- 點擊「登錄」按鈕

### 3. 登錄成功
- 系統驗證成功後，自動跳轉到員工自助服務頁面
- 右上角顯示您的姓名、員工編號和部門資訊

## 👤 測試帳號

系統提供以下測試帳號供開發和測試使用：

| 員工編號 | 姓名   | 密碼        | 部門   |
|----------|--------|-------------|--------|
| E001     | 黎麗玲 | password123 | 技術部 |
| E002     | 蔡秀娟 | password123 | 人事部 |
| E003     | 劉志偉 | password123 | 財務部 |
| E005     | 張文祥 | password123 | 業務部 |
| E007     | 廖彥廷 | password123 | 技術部 |

## 🎨 設計特色

### Apple風格設計語言
- **簡潔至上**：清晰的視覺層次，去除不必要的裝飾
- **毛玻璃效果**：現代化的背景模糊效果
- **響應式布局**：適配各種螢幕尺寸
- **專業圖標**：使用Lucide圖標庫

### 用戶體驗優化
- **即時反饋**：登錄過程中的狀態提示
- **錯誤處理**：友善的錯誤訊息顯示
- **安全性**：密碼輸入遮罩保護
- **記住我**：可選的登錄狀態保持

## 🔧 技術實現

### 前端技術
- **HTML5 + CSS3**：現代化的網頁標準
- **Tailwind CSS**：實用優先的CSS框架
- **Lucide Icons**：現代化的圖標系統
- **JavaScript ES6+**：原生JavaScript實現

### 後端API
- **Flask Session**：安全的會話管理
- **SQLite資料庫**：員工資料存儲
- **RESTful API**：標準化的API設計
- **錯誤處理**：完整的異常處理機制

## 🛡️ 安全特性

### 身份驗證
- **員工編號驗證**：確保員工身份有效
- **密碼驗證**：安全的密碼比對機制
- **會話管理**：安全的登錄狀態維護
- **自動登出**：閒置時間過長自動登出

### 資料保護
- **密碼保護**：前端密碼輸入遮罩
- **會話安全**：Flask安全會話機制
- **錯誤日誌**：詳細的安全事件記錄
- **權限控制**：基於角色的訪問控制

## 📱 響應式設計

### 桌面版 (≥1024px)
- 居中的登錄卡片設計
- 寬敞的輸入欄位和按鈕
- 完整的品牌標識顯示

### 平板版 (768px-1023px)
- 適中的卡片尺寸
- 優化的觸控體驗
- 保持視覺平衡

### 手機版 (<768px)
- 全寬度的登錄表單
- 大尺寸的觸控按鈕
- 簡化的界面元素

## 🔄 登出功能

### 手動登出
- 點擊右上角用戶頭像
- 選擇「登出」選項
- 系統清除會話並返回登錄頁面

### 自動登出
- 瀏覽器關閉時自動清除會話
- 長時間未操作自動登出
- 安全性考量的強制登出

## 🚀 API端點

### 登錄API
```http
POST /api/login
Content-Type: application/json

{
  "username": "E001",
  "password": "password123",
  "remember_me": false
}
```

### 登出API
```http
POST /api/logout
```

### 驗證API
```http
GET /api/auth/verify
```

## 🐛 故障排除

### 常見問題

**Q: 無法登錄，提示「員工編號或密碼錯誤」**
- 檢查員工編號是否正確（系統支援大小寫不敏感）
- 確認密碼輸入正確
- 聯繫系統管理員確認帳號狀態

**Q: 登錄後頁面空白或錯誤**
- 檢查瀏覽器控制台是否有JavaScript錯誤
- 清除瀏覽器快取和Cookie
- 嘗試使用無痕模式訪問

**Q: 登錄頁面樣式異常**
- 檢查網路連線是否正常
- 確認CDN資源載入正常
- 嘗試重新整理頁面

### 技術支援
- 查看瀏覽器開發者工具的控制台錯誤
- 檢查網路請求是否成功
- 聯繫技術團隊獲得協助

## 📈 未來規劃

### 功能增強
- [ ] 雙因素認證（2FA）
- [ ] 社交登錄整合
- [ ] 生物識別登錄
- [ ] 單點登錄（SSO）

### 用戶體驗
- [ ] 深色模式支援
- [ ] 多語言支援
- [ ] 個性化主題
- [ ] 無障礙功能增強

### 安全性提升
- [ ] 密碼強度檢查
- [ ] 登錄嘗試限制
- [ ] 異常登錄檢測
- [ ] 安全審計日誌

---

*最後更新：2025年6月6日*  
*版本：v1.0.0*  
*維護團隊：AttendanceOS開發團隊* 