<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABC公司 - 考勤系統</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>

<body class="bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- ABC公司專用頂部導航 -->
    <nav class="bg-blue-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-white">ABC公司考勤系統</h1>
                    <span class="ml-4 px-3 py-1 bg-blue-500 text-white text-sm rounded-full">專案追蹤版</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 px-4">
        <!-- ABC公司專用統計卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 標準考勤統計 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <i data-lucide="clock" class="h-8 w-8 text-blue-600"></i>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">今日出勤</p>
                        <p class="text-2xl font-bold text-gray-900">95%</p>
                    </div>
                </div>
            </div>

            <!-- ABC專用：專案工時統計 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <i data-lucide="briefcase" class="h-8 w-8 text-green-600"></i>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">專案工時</p>
                        <p class="text-2xl font-bold text-gray-900">1,240h</p>
                    </div>
                </div>
            </div>

            <!-- ABC專用：成本中心統計 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <i data-lucide="dollar-sign" class="h-8 w-8 text-yellow-600"></i>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">成本中心</p>
                        <p class="text-2xl font-bold text-gray-900">12個</p>
                    </div>
                </div>
            </div>

            <!-- ABC專用：計費工時 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <i data-lucide="calculator" class="h-8 w-8 text-purple-600"></i>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">計費工時</p>
                        <p class="text-2xl font-bold text-gray-900">980h</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- ABC專用：專案工時表格 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">專案工時明細</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">員工</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">專案代碼</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">成本中心</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">工時</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">計費狀態</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="projectHoursTable">
                        <!-- 動態載入專案工時數據 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // ABC公司專用JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            loadProjectHours();
        });

        function loadProjectHours() {
            // 調用ABC公司專用API
            fetch('/api/v2/attendance', {
                    headers: {
                        'X-Company-ID': 'company_abc'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('projectHoursTable');
                    tbody.innerHTML = '';

                    data.attendance.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.employee_id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.project_code || 'N/A'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CC100</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.billable_hours || 8}h</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                已計費
                            </span>
                        </td>
                    `;
                        tbody.appendChild(row);
                    });
                });
        }
    </script>
</body>

</html>