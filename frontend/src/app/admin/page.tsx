"use client"

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
    Users,
    Clock,
    Calendar,
    BarChart3,
    Settings,
    FileText,
    Shield,
    LogIn,
    Home,
    CheckCircle,
    AlertTriangle,
    TrendingUp,
    UserCheck,
    Database,
    CalendarClock,
    Upload
} from 'lucide-react'
import Link from 'next/link'
import { login } from '@/lib/api-client'

export default function AdminPage() {
    const { user, login: authLogin, loading } = useAuth()
    const router = useRouter()
    const [autoLoginLoading, setAutoLoginLoading] = useState(false)
    const [mounted, setMounted] = useState(false)

    // 確保組件已掛載，避免 hydration 錯誤
    useEffect(() => {
        setMounted(true)
    }, [])

    // 自動登入功能 - 使用管理員測試帳號
    const handleAutoLogin = async () => {
        setAutoLoginLoading(true)
        try {
            console.log('開始自動登入管理員帳號')
            const response = await login({
                employee_id: 'admin',
                password: 'admin123'
            })

            if (response && response.user) {
                const userData = response.user
                const user = {
                    id: userData.employee_id,
                    name: userData.employee_name,
                    employee_id: userData.employee_code,
                    department_id: userData.department_id,
                    position: userData.role_id === 999 ? '系統管理員' : '員工',
                    email: userData.email,
                    role_id: userData.role_id,
                    department_name: userData.department_name
                }

                authLogin(user)
                console.log('自動登入成功:', user)
            } else {
                console.error('自動登入失敗:', response)
            }
        } catch (error) {
            console.error('自動登入錯誤:', error)
        } finally {
            setAutoLoginLoading(false)
        }
    }

    // 頁面載入時檢查登入狀態 - 增加延遲避免過早重定向
    useEffect(() => {
        if (mounted && !loading && !user) {
            console.log('檢測到未登入，延遲重定向到登錄頁面')
            const timer = setTimeout(() => {
                router.push('/admin/login')
            }, 500) // 給AuthContext更多時間載入

            return () => clearTimeout(timer)
        }
    }, [mounted, loading, user, router])

    // 在組件未掛載時顯示載入狀態，避免 hydration 錯誤
    if (!mounted || loading || autoLoginLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center" suppressHydrationWarning>
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">
                        {autoLoginLoading ? '自動登入中...' : '載入中...'}
                    </p>
                </div>
            </div>
        )
    }

    // 如果沒有用戶，顯示登入選項
    if (!user) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center" suppressHydrationWarning>
                <div className="text-center max-w-md mx-auto p-8">
                    <Shield className="w-16 h-16 text-indigo-600 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">管理員登入</h2>
                    <p className="text-gray-600 mb-6">請登入以訪問管理功能</p>
                    <div className="space-y-4">
                        <Button
                            onClick={handleAutoLogin}
                            loading={autoLoginLoading}
                            className="w-full"
                        >
                            使用測試帳號登入 (admin/admin123)
                        </Button>
                        <Link href="/admin/login">
                            <Button variant="outline" className="w-full">
                                手動登入
                            </Button>
                        </Link>
                        <Link href="/">
                            <Button variant="ghost" className="w-full">
                                返回首頁
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        )
    }

    // 如果不是管理員，顯示權限不足
    if (user.role_id !== 999) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center" suppressHydrationWarning>
                <div className="text-center">
                    <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">權限不足</h2>
                    <p className="text-gray-600 mb-4">您需要管理員權限才能訪問此頁面</p>
                    <div className="space-x-4">
                        <Link href="/">
                            <Button variant="outline">返回首頁</Button>
                        </Link>
                        <Link href="/admin/login">
                            <Button>重新登入</Button>
                        </Link>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            {/* 頂部導航 */}
            <header className="bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                                <Shield className="w-5 h-5 text-white" />
                            </div>
                            <div>
                                <h1 className="text-xl font-bold text-gray-900">管理後台</h1>
                                <p className="text-sm text-gray-600">Han AttendanceOS</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-600">
                                歡迎，{user.name} ({user.position})
                            </span>
                            <Link href="/">
                                <Button variant="ghost" size="sm" icon={<Home className="w-4 h-4" />}>
                                    返回首頁
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </header>

            {/* 主要內容 */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* 歡迎區塊 */}
                <div className="mb-8">
                    <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl">
                        <div className="relative z-10">
                            <h2 className="text-3xl font-bold mb-2">管理員控制台</h2>
                            <p className="text-indigo-100 text-base">
                                歡迎使用 Han AttendanceOS 管理後台，您可以在這裡管理所有系統功能
                            </p>
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl"></div>
                    </div>
                </div>

                {/* 快速統計 */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                                <Users className="w-6 h-6 text-white" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">總員工數</p>
                                <p className="text-2xl font-bold text-gray-900">3</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                                <CheckCircle className="w-6 h-6 text-white" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">今日出勤</p>
                                <p className="text-2xl font-bold text-gray-900">3</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                                <Calendar className="w-6 h-6 text-white" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">待審核請假</p>
                                <p className="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                                <TrendingUp className="w-6 h-6 text-white" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">本月工時</p>
                                <p className="text-2xl font-bold text-gray-900">168h</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 管理功能模組 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* 考勤管理 */}
                    <Link href="/admin/attendance-management">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-4">
                                <Clock className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">考勤管理</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                管理員工考勤記錄、班表設定、工時計算和異常處理
                            </p>
                            <div className="flex items-center text-indigo-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 員工管理 */}
                    <Link href="/admin/employees">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4">
                                <Users className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">員工管理</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                管理員工基本資料、部門分組、角色權限和個人設定
                            </p>
                            <div className="flex items-center text-green-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 請假審核 */}
                    <Link href="/admin/leave-approval">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4">
                                <Calendar className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">請假審核</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                審核員工請假申請、管理假期餘額和請假政策設定
                            </p>
                            <div className="flex items-center text-purple-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 加班審核 */}
                    <Link href="/admin/overtime-approval">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mb-4">
                                <Clock className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">加班審核</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                審核員工加班申請、管理加班時數和加班政策設定
                            </p>
                            <div className="flex items-center text-orange-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 打卡記錄 */}
                    <Link href="/admin/punch-records">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4">
                                <UserCheck className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">打卡記錄</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                查看和管理所有員工的打卡記錄和考勤統計
                            </p>
                            <div className="flex items-center text-orange-600 text-sm font-medium">
                                <span>進入查看</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 數據報表 */}
                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 opacity-75">
                        <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mb-4">
                            <BarChart3 className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">數據報表</h3>
                        <p className="text-gray-600 text-sm mb-4">
                            生成各種考勤報表、統計分析和趨勢預測
                        </p>
                        <div className="flex items-center text-gray-400 text-sm font-medium">
                            <span>開發中...</span>
                        </div>
                    </div>

                    {/* 排班管理 */}
                    <Link href="/admin/shifts">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                                <CalendarClock className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">排班管理</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                設定班別時間、加班規則、休息時間和工時計算方式
                            </p>
                            <div className="flex items-center text-teal-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 基本資料管理 */}
                    <Link href="/admin/masterdata">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4">
                                <Database className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">基本資料管理</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                管理學歷、職位、假別、薪資等級 基本資料設定
                            </p>
                            <div className="flex items-center text-cyan-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 工作回報管理 */}
                    <Link href="/admin/work-reports">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center mb-4">
                                <FileText className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">工作回報管理</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                查看員工工作回報、提供回覆與指導、管理工作進度
                            </p>
                            <div className="flex items-center text-violet-600 text-sm font-medium">
                                <span>進入管理</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 匯入考勤資料 */}
                    <Link href="/admin/import-attendance">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4">
                                <Upload className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">匯入考勤資料</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                從打卡機匯出的文字檔案匯入考勤記錄和員工資料
                            </p>
                            <div className="flex items-center text-emerald-600 text-sm font-medium">
                                <span>進入匯入</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>

                    {/* 系統設定 */}
                    <Link href="/admin/settings">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-4">
                                <Settings className="w-6 h-6 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">系統設定</h3>
                            <p className="text-gray-600 text-sm mb-4">
                                配置系統參數、考勤規則、通知設定和權限管理
                            </p>
                            <div className="flex items-center text-indigo-600 text-sm font-medium">
                                <span>進入設定</span>
                                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>
                </div>

                {/* 快速操作 */}
                <div className="mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                    <div className="flex flex-wrap gap-3">
                        <Button variant="outline" size="sm" icon={<FileText className="w-4 h-4" />}>
                            匯出本月考勤報表
                        </Button>
                        <Button variant="outline" size="sm" icon={<Calendar className="w-4 h-4" />}>
                            批量審核請假
                        </Button>
                        <Button variant="outline" size="sm" icon={<Users className="w-4 h-4" />}>
                            新增員工
                        </Button>
                        <Button variant="outline" size="sm" icon={<Settings className="w-4 h-4" />}>
                            系統備份
                        </Button>
                    </div>
                </div>
            </main>
        </div>
    )
}
