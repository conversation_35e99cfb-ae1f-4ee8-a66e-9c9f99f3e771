"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時檢查登入狀態 - 增加延遲避免過早重定向\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted && !loading && !user) {\n            console.log(\"檢測到未登入，延遲重定向到登錄頁面\");\n            const timer = setTimeout(()=>{\n                router.push(\"/admin/login\");\n            }, 500) // 給AuthContext更多時間載入\n            ;\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        mounted,\n        loading,\n        user,\n        router\n    ]);\n    // 在組件未掛載時顯示載入狀態，避免 hydration 錯誤\n    if (!mounted || loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 90,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果沒有用戶，顯示登入選項\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-16 h-16 text-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"管理員登入\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"請登入以訪問管理功能\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAutoLogin,\n                                loading: autoLoginLoading,\n                                className: \"w-full\",\n                                children: \"使用測試帳號登入 (admin/admin123)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"手動登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果不是管理員，顯示權限不足\n    if (user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    children: \"重新登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"管理後台\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Han AttendanceOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"歡迎，\",\n                                            user.name,\n                                            \" (\",\n                                            user.position,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 73\n                                            }, void 0),\n                                            children: \"返回首頁\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-2\",\n                                            children: \"管理員控制台\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base\",\n                                            children: \"歡迎使用 Han AttendanceOS 管理後台，您可以在這裡管理所有系統功能\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"總員工數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"今日出勤\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"待審核請假\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"本月工時\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"168h\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/attendance-management\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"考勤管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工考勤記錄、班表設定、工時計算和異常處理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/employees\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"員工管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工基本資料、部門分組、角色權限和個人設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/leave-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工請假申請、管理假期餘額和請假政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-purple-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/overtime-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"加班審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工加班申請、管理加班時數和加班政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/punch-records\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"打卡記錄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看和管理所有員工的打卡記錄和考勤統計\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入查看\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 opacity-75\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"數據報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"生成各種考勤報表、統計分析和趨勢預測\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-gray-400 text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"開發中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/shifts\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"排班管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"設定班別時間、加班規則、休息時間和工時計算方式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/masterdata\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"基本資料管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理學歷、職位、假別、薪資等級 基本資料設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cyan-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/work-reports\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"工作回報管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看員工工作回報、提供回覆與指導、管理工作進度\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-violet-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/import-attendance\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"匯入考勤資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"從打卡機匯出的文字檔案匯入考勤記錄和員工資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-emerald-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入匯入\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"系統設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"配置系統參數、考勤規則、通知設定和權限管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"快速操作\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"匯出本月考勤報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"批量審核請假\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"新增員工\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"系統備份\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 9\n    }, this);\n}\n_s(AdminPage, \"FHDnuiI1nXHZA22bmtSNVUxE0pk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});