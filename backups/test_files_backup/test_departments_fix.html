<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部門API修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <h1>🔧 部門API修復測試</h1>

    <div class="test-section info">
        <h2>測試說明</h2>
        <p>這個頁面用來測試考勤作業管理頁面的"false"錯誤修復效果。</p>
        <ul>
            <li>✅ 清理了重複的部門記錄（從1048個減少到4個）</li>
            <li>✅ 修復了前端JavaScript的數據處理邏輯</li>
            <li>✅ 移除了重複的showNotification函數定義</li>
            <li>✅ 改進了錯誤處理機制</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>1. 部門API測試</h2>
        <button onclick="testDepartmentsAPI()">測試部門API</button>
        <div id="apiResult"></div>
    </div>

    <div class="test-section">
        <h2>2. 前端JavaScript測試</h2>
        <button onclick="testFrontendLogic()">測試前端邏輯</button>
        <select id="testDepartmentSelect">
            <option value="">請選擇部門</option>
        </select>
        <div id="frontendResult"></div>
    </div>

    <div class="test-section">
        <h2>3. 錯誤處理測試</h2>
        <button onclick="testErrorHandling()">測試錯誤處理</button>
        <div id="errorResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 考勤作業管理頁面測試</h2>
        <button onclick="window.open('/elite/attendance-management', '_blank')">打開考勤作業管理頁面</button>
        <p>請檢查頁面是否正常載入，沒有"false"錯誤彈窗。</p>
    </div>

    <script>
        // 通知系統模擬
        function showNotification(message, type = 'info') {
            const colors = {
                'success': '#d4edda',
                'error': '#f8d7da',
                'warning': '#fff3cd',
                'info': '#d1ecf1'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                padding: 12px 20px; border-radius: 5px; color: #333;
                background-color: ${colors[type] || colors.info};
                border: 1px solid #ccc; box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // 測試部門API
        async function testDepartmentsAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>正在測試...</p>';

            try {
                const response = await fetch('/api/departments');
                const data = await response.json();

                if (response.ok) {
                    const departments = data.departments || data;
                    const isCorrectFormat = Array.isArray(departments);
                    const departmentCount = departments.length;

                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ API測試成功</h3>
                            <p><strong>數據格式：</strong> ${isCorrectFormat ? '正確（陣列）' : '錯誤'}</p>
                            <p><strong>部門數量：</strong> ${departmentCount}</p>
                            <p><strong>部門列表：</strong></p>
                            <pre>${JSON.stringify(departments, null, 2)}</pre>
                        </div>
                    `;

                    if (departmentCount === 4 && isCorrectFormat) {
                        showNotification('部門API測試通過！', 'success');
                    } else {
                        showNotification('部門API數據異常', 'warning');
                    }
                } else {
                    throw new Error(data.error || '未知錯誤');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ API測試失敗</h3>
                        <p><strong>錯誤：</strong> ${error.message}</p>
                    </div>
                `;
                showNotification('部門API測試失敗', 'error');
            }
        }

        // 測試前端邏輯
        async function testFrontendLogic() {
            const resultDiv = document.getElementById('frontendResult');
            const select = document.getElementById('testDepartmentSelect');

            resultDiv.innerHTML = '<p>正在測試前端邏輯...</p>';

            try {
                // 模擬loadDepartments函數
                const response = await fetch('/api/departments');
                const data = await response.json();

                // 清空選擇器
                select.innerHTML = '<option value="">請選擇部門</option>';

                // 使用與考勤頁面相同的邏輯
                const departments = data.departments || data;

                if (!Array.isArray(departments)) {
                    throw new Error('departments不是陣列');
                }

                departments.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ 前端邏輯測試成功</h3>
                        <p><strong>成功載入：</strong> ${departments.length} 個部門</p>
                        <p><strong>選擇器更新：</strong> 完成</p>
                    </div>
                `;

                showNotification('前端邏輯測試通過！', 'success');

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 前端邏輯測試失敗</h3>
                        <p><strong>錯誤：</strong> ${error.message}</p>
                        <p>這可能是導致"false"錯誤的原因。</p>
                    </div>
                `;
                showNotification('前端邏輯測試失敗', 'error');
            }
        }

        // 測試錯誤處理
        async function testErrorHandling() {
            const resultDiv = document.getElementById('errorResult');
            resultDiv.innerHTML = '<p>正在測試錯誤處理...</p>';

            try {
                // 測試工具函數庫初始化
                let utilsLoaded = false;
                let notificationSystemAvailable = false;

                try {
                    if (window.UtilsLoader && typeof window.UtilsLoader.initPageUtils === 'function') {
                        const result = await window.UtilsLoader.initPageUtils('test-page');
                        utilsLoaded = result === true;
                    }
                } catch (e) {
                    console.log('工具函數庫未載入，這是正常的');
                }

                // 測試通知系統
                try {
                    if (window.NotificationSystem) {
                        notificationSystemAvailable = true;
                    }
                } catch (e) {
                    console.log('通知系統未載入，使用降級處理');
                }

                resultDiv.innerHTML = `
                    <div class="info">
                        <h3>🔍 錯誤處理測試結果</h3>
                        <p><strong>工具函數庫：</strong> ${utilsLoaded ? '已載入' : '未載入（正常）'}</p>
                        <p><strong>通知系統：</strong> ${notificationSystemAvailable ? '可用' : '使用降級處理'}</p>
                        <p><strong>錯誤處理：</strong> 正常運行，沒有拋出未捕獲的錯誤</p>
                    </div>
                `;

                showNotification('錯誤處理測試完成', 'info');

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 錯誤處理測試失敗</h3>
                        <p><strong>錯誤：</strong> ${error.message}</p>
                    </div>
                `;
                showNotification('錯誤處理測試失敗', 'error');
            }
        }

        // 頁面載入時自動運行基本測試
        window.addEventListener('DOMContentLoaded', function() {
            showNotification('測試頁面載入完成', 'info');

            // 延遲執行API測試
            setTimeout(() => {
                testDepartmentsAPI();
            }, 1000);
        });
    </script>
</body>

</html>