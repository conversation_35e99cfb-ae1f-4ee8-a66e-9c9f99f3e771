#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
簡化版應用程式測試
"""

from flask import Flask, jsonify
from datetime import datetime

app = Flask(__name__)

@app.route("/health")
def health_check():
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "test"
    })

@app.route("/test")
def test_route():
    return jsonify({"message": "測試成功"})

if __name__ == "__main__":
    print("啟動簡化版應用程式...")
    app.run(host="0.0.0.0", port=7072, debug=False) 