# UI 模態框設計標準 - Han AttendanceOS

## 📋 概述

本文檔定義了 Han AttendanceOS 系統中模態框的專業級設計標準，確保所有模態框具有一致的視覺風格、交互體驗和功能實現。

## 🎨 設計原則

### 1. Apple 設計語言
- **簡潔至上**：去除不必要的裝飾元素
- **人性化交互**：直觀的操作流程
- **視覺層次**：清晰的信息架構
- **一致性**：統一的設計語言

### 2. 專業級標準
- **企業級外觀**：適合商業環境使用
- **功能完整性**：包含所有必要的交互元素
- **響應式設計**：適配不同螢幕尺寸
- **無障礙設計**：支援鍵盤導航和螢幕閱讀器

## 🏗️ 結構標準

### 模態框基本結構
```html
<div class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50 overflow-y-auto">
    <div class="min-h-screen px-4 text-center">
        <span class="inline-block h-screen align-middle" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-3xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-lg mx-auto">
            <!-- 標題區域 -->
            <!-- 表單內容區域 -->
            <!-- 底部按鈕區 -->
        </div>
    </div>
</div>
```

### 1. 標題區域設計標準

#### 視覺規範
- **背景**：`bg-gradient-to-r from-{color}-500/10 to-{color2}-500/10`
- **內邊距**：`px-6 py-6`
- **邊框**：`border-b border-gray-100`

#### 內容結構
```html
<div class="flex items-center justify-between">
    <div class="flex items-center space-x-4">
        <div class="w-12 h-12 bg-gradient-to-br from-{color}-500 to-{color2}-600 rounded-2xl flex items-center justify-center shadow-lg">
            <i data-lucide="{icon}" class="w-6 h-6 text-white"></i>
        </div>
        <div>
            <h3 class="text-xl font-bold text-gray-900">{標題}</h3>
            <p class="text-sm text-gray-500 mt-1">{描述文字}</p>
        </div>
    </div>
    <button type="button" onclick="{hideFunction}()" class="p-2 hover:bg-gray-100 rounded-xl transition-colors">
        <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
    </button>
</div>
```

### 2. 表單欄位設計標準

#### 欄位容器
- **間距**：`space-y-6`（欄位間）、`space-y-3`（欄位內）
- **內邊距**：`px-6 py-6`

#### 標籤設計
```html
<div class="flex items-center space-x-2">
    <i data-lucide="{icon}" class="w-4 h-4 text-{color}-600"></i>
    <label class="block text-sm font-semibold text-gray-700">{標籤文字}</label>
    <span class="text-red-500 text-sm">*</span> <!-- 必填標記 -->
</div>
```

#### 輸入框設計
```html
<div class="relative">
    <input class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-{color}-500 focus:border-transparent transition-all duration-200 text-sm">
    <i data-lucide="{icon}" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
</div>
```

#### 下拉選單設計
```html
<div class="relative">
    <select class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-{color}-500 focus:border-transparent text-sm">
        <option value="">選擇選項</option>
    </select>
    <i data-lucide="chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
</div>
```

### 3. 計算結果顯示區域

#### 設計規範
```html
<div class="bg-gradient-to-r from-{color}-50 to-{color2}-50 border border-{color}-200 rounded-xl p-4">
    <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-br from-{color}-500 to-{color2}-600 rounded-xl flex items-center justify-center">
            <i data-lucide="calculator" class="w-5 h-5 text-white"></i>
        </div>
        <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
                <p class="text-sm font-semibold text-{color}-700">{計算標題}</p>
                <p class="text-xs text-{color}-600">系統自動計算</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-lg font-bold text-{color}-800">{主要結果}</div>
                <div class="text-sm text-{color}-600">{次要結果}</div>
            </div>
        </div>
    </div>
</div>
```

### 4. 底部按鈕區域

#### 設計規範
```html
<div class="px-6 py-4 bg-gray-50 border-t border-gray-100 flex space-x-3">
    <button type="button" class="flex-1 bg-white hover:bg-gray-50 text-gray-700 py-3 px-4 rounded-xl font-semibold transition-all duration-200 border border-gray-200 text-sm">
        取消
    </button>
    <button type="submit" class="flex-1 bg-gradient-to-r from-{color}-500 to-{color2}-600 hover:from-{color}-600 hover:to-{color2}-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
        <i data-lucide="send" class="w-4 h-4 inline mr-2"></i>
        提交申請
    </button>
</div>
```

## 🎯 顏色配置標準

### 主題色彩方案

#### 加班申請（橙色系）
- **主色**：`orange-500` / `red-600`
- **背景**：`from-orange-50 to-red-50`
- **邊框**：`border-orange-200`
- **文字**：`text-orange-700` / `text-orange-800`

#### 請假申請（綠色系）
- **主色**：`green-500` / `blue-500`
- **背景**：`from-green-50 to-blue-50`
- **邊框**：`border-green-200`
- **文字**：`text-green-700` / `text-green-800`

#### 考勤管理（藍色系）
- **主色**：`blue-500` / `indigo-600`
- **背景**：`from-blue-50 to-indigo-50`
- **邊框**：`border-blue-200`
- **文字**：`text-blue-700` / `text-blue-800`

## 📱 響應式設計

### 螢幕尺寸適配
- **手機**：`max-w-sm`（384px）
- **平板**：`max-w-md`（448px）
- **桌面**：`max-w-lg`（512px）

### PWA 優化
- **最大高度**：`max-h-[95vh]`
- **滾動處理**：`overflow-y-auto`
- **觸控優化**：適當的點擊區域大小

## 🔧 JavaScript 功能標準

### 必要函數
1. **顯示模態框**：`show{Type}Modal()`
2. **隱藏模態框**：`hide{Type}Modal()`
3. **計算功能**：`calculate{Type}Hours()` / `calculate{Type}Days()`
4. **數據載入**：`load{Type}Types()`
5. **表單提交**：`submit{Type}Form()`

### 計算函數規範
```javascript
/**
 * 計算功能函數
 * 
 * 功能描述：
 * - 根據輸入參數計算結果
 * - 動態顯示計算結果
 * - 處理邊界情況
 * 
 * 計算規則：
 * - 具體的計算邏輯說明
 * - 單位轉換規則
 * - 精度處理方式
 */
function calculate{Type}Hours() {
    // 實現邏輯
}
```

## ✅ 檢查清單

### 設計檢查
- [ ] 標題區域包含圖標、標題、描述文字
- [ ] 每個欄位都有對應圖標
- [ ] 必填欄位有紅色星號標記
- [ ] 計算結果有專門顯示區域
- [ ] 底部按鈕區域設計一致

### 功能檢查
- [ ] 模態框顯示/隱藏動畫流暢
- [ ] 表單驗證完整
- [ ] 計算功能準確
- [ ] 數據載入正常
- [ ] 響應式設計適配

### 交互檢查
- [ ] 鍵盤導航支援
- [ ] 焦點管理正確
- [ ] 錯誤提示清晰
- [ ] 成功反饋及時
- [ ] 載入狀態顯示

## 📚 實現範例

### 完整的加班申請模態框
參考：`templates/user-dashboard.html` 中的 `#overtimeModal`

### 完整的請假申請模態框
參考：`templates/user-dashboard.html` 中的 `#leaveModal`

## 🔄 版本歷史

- **v1.0** (2025-06-07)：建立基本設計標準
- **v1.1** (2025-06-07)：添加計算功能規範
- **v1.2** (2025-06-07)：完善響應式設計標準

---

**維護者**：Han AttendanceOS 開發團隊  
**最後更新**：2025年6月7日 