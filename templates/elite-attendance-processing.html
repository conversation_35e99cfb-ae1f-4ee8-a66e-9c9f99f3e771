<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤整理 - AttendanceOS Elite</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Tailwind CSS 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            50: '#f0f7ff',
                            100: '#e0effe',
                            200: '#b9e0fe',
                            300: '#7cc7fd',
                            400: '#36abfa',
                            500: '#0c8ce9',
                            600: '#0369a1',
                            700: '#0c4a6e',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .processing-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #0c8ce9 0%, #36abfa 100%);
            transition: width 0.3s ease;
        }
        
        .step-indicator {
            position: relative;
        }
        
        .step-indicator::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -12px;
            width: 24px;
            height: 2px;
            background: #e2e8f0;
            transform: translateY(-50%);
        }
        
        .step-indicator:last-child::after {
            display: none;
        }
        
        .step-indicator.active::after {
            background: #0c8ce9;
        }
        
        .log-entry {
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .log-entry.success {
            border-left-color: #22c55e;
            background-color: #f0fdf4;
        }
        
        .log-entry.warning {
            border-left-color: #f59e0b;
            background-color: #fffbeb;
        }
        
        .log-entry.error {
            border-left-color: #ef4444;
            background-color: #fef2f2;
        }
        
        .processing-step {
            transition: all 0.3s ease;
        }
        
        .processing-step.active {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        
        .processing-step.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .progress-bar {
            transition: width 0.5s ease;
        }
        
        .log-entry {
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%,
            100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- 導航 -->
    <nav class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 text-white px-4 py-2 rounded-xl hover:from-brand-600 hover:to-brand-700 transition-all duration-200 shadow-lg">
                    <i data-lucide="arrow-left" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">返回儀表板</span>
                </a>
                <div class="h-6 w-px bg-gray-300"></div>
                <h1 class="text-xl font-semibold text-gray-900">考勤整理系統</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">自動處理模式：</span>
                <div class="flex items-center">
                    <input type="checkbox" id="autoMode" class="sr-only">
                    <label for="autoMode" class="relative inline-flex items-center cursor-pointer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:bg-brand-500 transition-colors duration-200">
                            <div class="w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 translate-x-0.5 peer-checked:translate-x-5"></div>
                        </div>
                    </label>
                    <span class="ml-2 text-sm text-success-600 font-medium">啟用</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- 狀態總覽 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-brand-50 rounded-lg flex items-center justify-center">
                        <i data-lucide="calendar-days" class="w-6 h-6 text-brand-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-500">待處理天數</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingDays">7</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-warning-50 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-warning-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-500">待處理員工</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingEmployees">156</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-success-50 rounded-lg flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-success-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-500">預估時間</p>
                        <p class="text-2xl font-bold text-gray-900" id="estimatedTime">2分鐘</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-error-50 rounded-lg flex items-center justify-center">
                        <i data-lucide="alert-triangle" class="w-6 h-6 text-error-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-500">異常記錄</p>
                        <p class="text-2xl font-bold text-gray-900" id="errorRecords">3</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 處理設定 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="border-b border-gray-200 p-6">
                        <h2 class="text-lg font-semibold text-gray-900">考勤資料處理</h2>
                        <p class="text-sm text-gray-500 mt-1">選擇要處理的日期範圍和員工範圍</p>
                    </div>

                    <div class="p-6 space-y-6">
                        <!-- 日期範圍選擇 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">開始日期</label>
                                <input type="date" id="startDate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                                <input type="date" id="endDate" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>

                        <!-- 快速選擇 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">快速選擇</label>
                            <div class="flex flex-wrap gap-2">
                                <button class="quick-date-btn px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors" data-days="1">昨天</button>
                                <button class="quick-date-btn px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors" data-days="7">最近7天</button>
                                <button class="quick-date-btn px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors" data-days="30">最近30天</button>
                                <button class="quick-date-btn px-4 py-2 text-sm bg-brand-100 text-brand-700 rounded-lg hover:bg-brand-200 transition-colors" data-days="current-month">本月</button>
                            </div>
                        </div>

                        <!-- 員工範圍 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">員工範圍</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="employeeRange" value="all" checked class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">所有員工</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="employeeRange" value="department" class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">指定部門</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="employeeRange" value="specific" class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">指定員工</span>
                                </label>
                            </div>
                        </div>

                        <!-- 部門選擇 -->
                        <div id="departmentSelection" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">選擇部門</label>
                            <select multiple class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent" size="4">
                                <option value="it">資訊技術部</option>
                                <option value="hr">人力資源部</option>
                                <option value="finance">財務部</option>
                                <option value="sales">業務部</option>
                                <option value="marketing">行銷部</option>
                            </select>
                        </div>

                        <!-- 處理選項 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">處理選項</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">計算遲到時間</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">計算早退時間</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">計算加班時間</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">整合請假資料</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">覆蓋已存在的計算結果</span>
                                </label>
                            </div>
                        </div>

                        <!-- 操作按鈕 -->
                        <div class="flex space-x-4 pt-4">
                            <button id="startProcessing" class="flex-1 bg-brand-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-brand-700 transition-colors flex items-center justify-center space-x-2">
                                <i data-lucide="play" class="w-5 h-5"></i>
                                <span>開始處理</span>
                            </button>
                            <button id="previewProcessing" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center space-x-2">
                                <i data-lucide="eye" class="w-5 h-5"></i>
                                <span>預覽</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 處理進度和記錄 -->
            <div class="space-y-6">
                <!-- 處理進度 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="border-b border-gray-200 p-4">
                        <h3 class="text-lg font-medium text-gray-900">處理進度</h3>
                    </div>
                    <div class="p-4">
                        <div class="space-y-4">
                            <!-- 總進度 -->
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">總進度</span>
                                    <span class="text-sm text-gray-500" id="overallProgress">0%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="progress-bar h-2 rounded-full" style="width: 0%" id="overallProgressBar"></div>
                                </div>
                            </div>

                            <!-- 處理步驟 -->
                            <div class="space-y-3">
                                <div class="step-indicator flex items-center space-x-3 p-2 rounded-lg">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="database" class="w-4 h-4 text-gray-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-700">資料載入</p>
                                        <p class="text-xs text-gray-500">載入考勤和請假資料</p>
                                    </div>
                                </div>

                                <div class="step-indicator flex items-center space-x-3 p-2 rounded-lg">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="calculator" class="w-4 h-4 text-gray-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-700">時間計算</p>
                                        <p class="text-xs text-gray-500">計算遲到、早退、加班</p>
                                    </div>
                                </div>

                                <div class="step-indicator flex items-center space-x-3 p-2 rounded-lg">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="merge" class="w-4 h-4 text-gray-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-700">資料整合</p>
                                        <p class="text-xs text-gray-500">整合請假和異常</p>
                                    </div>
                                </div>

                                <div class="step-indicator flex items-center space-x-3 p-2 rounded-lg">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="save" class="w-4 h-4 text-gray-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-700">儲存結果</p>
                                        <p class="text-xs text-gray-500">更新資料庫</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 處理記錄 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="border-b border-gray-200 p-4 flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">處理記錄</h3>
                        <button class="text-sm text-gray-500 hover:text-gray-700" onclick="clearLogs()">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <div id="processingLogs" class="space-y-2 max-h-96 overflow-y-auto">
                            <div class="log-entry p-3 rounded-lg border border-gray-100">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">系統就緒，等待處理指令</span>
                                    <span class="text-xs text-gray-400">14:30:25</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 預覽模態框 -->
        <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                    <div class="border-b border-gray-200 p-6 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">處理預覽</h3>
                        <button onclick="closePreviewModal()" class="p-2 hover:bg-gray-100 rounded-lg">
                            <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>
                    <div class="p-6 overflow-y-auto" style="max-height: calc(90vh - 140px);">
                        <div id="previewContent">
                            <!-- 預覽內容將在這裡顯示 -->
                        </div>
                    </div>
                    <div class="border-t border-gray-200 p-6 flex justify-end space-x-4">
                        <button onclick="closePreviewModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            取消
                        </button>
                        <button onclick="confirmProcessing()" class="px-6 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700">
                            確認處理
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 設置預設日期
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            document.getElementById('startDate').value = yesterday.toISOString().split('T')[0];
            document.getElementById('endDate').value = yesterday.toISOString().split('T')[0];

            // 快速日期選擇
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const days = this.dataset.days;
                    const today = new Date();
                    let startDate, endDate;

                    if (days === 'current-month') {
                        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    } else {
                        const daysNum = parseInt(days);
                        endDate = new Date(today);
                        endDate.setDate(endDate.getDate() - 1);
                        startDate = new Date(endDate);
                        startDate.setDate(startDate.getDate() - daysNum + 1);
                    }

                    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
                    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

                    // 更新按鈕狀態
                    document.querySelectorAll('.quick-date-btn').forEach(b => {
                        b.classList.remove('bg-brand-100', 'text-brand-700');
                        b.classList.add('bg-gray-100', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-700');
                    this.classList.add('bg-brand-100', 'text-brand-700');

                    updateStatistics();
                });
            });

            // 員工範圍選擇
            document.querySelectorAll('input[name="employeeRange"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const departmentDiv = document.getElementById('departmentSelection');
                    if (this.value === 'department') {
                        departmentDiv.classList.remove('hidden');
                    } else {
                        departmentDiv.classList.add('hidden');
                    }
                    updateStatistics();
                });
            });

            // 處理按鈕事件
            document.getElementById('startProcessing').addEventListener('click', startProcessing);
            document.getElementById('previewProcessing').addEventListener('click', showPreview);

            // 初始統計更新
            updateStatistics();
        });

        /**
         * 更新統計資料
         * 根據選擇的日期範圍和員工範圍計算統計數據
         */
        function updateStatistics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const employeeRange = document.querySelector('input[name="employeeRange"]:checked').value;

            if (!startDate || !endDate) return;

            // 計算天數
            const start = new Date(startDate);
            const end = new Date(endDate);
            const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

            // 模擬計算員工數量
            let employees;
            switch (employeeRange) {
                case 'all':
                    employees = 156;
                    break;
                case 'department':
                    employees = 45;
                    break;
                case 'specific':
                    employees = 12;
                    break;
                default:
                    employees = 156;
            }

            // 更新顯示
            document.getElementById('pendingDays').textContent = days;
            document.getElementById('pendingEmployees').textContent = employees;

            // 估算時間（每個員工每天大約0.5秒）
            const estimatedSeconds = Math.ceil(days * employees * 0.5);
            let timeText;
            if (estimatedSeconds < 60) {
                timeText = `${estimatedSeconds}秒`;
            } else if (estimatedSeconds < 3600) {
                timeText = `${Math.ceil(estimatedSeconds / 60)}分鐘`;
            } else {
                timeText = `${Math.ceil(estimatedSeconds / 3600)}小時`;
            }
            document.getElementById('estimatedTime').textContent = timeText;

            // 模擬異常記錄數量
            const errorRecords = Math.floor(Math.random() * 10);
            document.getElementById('errorRecords').textContent = errorRecords;
        }

        /**
         * 開始處理考勤資料
         */
        function startProcessing() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('請選擇日期範圍');
                return;
            }

            // 禁用按鈕
            const btn = document.getElementById('startProcessing');
            btn.disabled = true;
            btn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin"></i><span>處理中...</span>';
            lucide.createIcons();

            // 模擬處理流程
            simulateProcessing();
        }

        /**
         * 模擬處理流程
         */
        function simulateProcessing() {
            const steps = ['資料載入', '時間計算', '資料整合', '儲存結果'];
            let currentStep = 0;
            let progress = 0;

            addLog('info', '開始處理考勤資料...');

            const interval = setInterval(() => {
                progress += Math.random() * 15 + 5;

                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);

                    // 完成處理
                    updateProgress(progress);
                    addLog('success', '處理完成！');
                    addLog(`成功處理 ${document.getElementById('pendingEmployees').textContent} 名員工的考勤資料`);

                    // 重置按鈕
                    const btn = document.getElementById('startProcessing');
                    btn.disabled = false;
                    btn.innerHTML = '<i data-lucide="play" class="w-5 h-5"></i><span>開始處理</span>';
                    lucide.createIcons();

                    // 重置進度
                    setTimeout(() => {
                        updateProgress(0);
                        resetSteps();
                    }, 3000);
                } else {
                    updateProgress(progress);

                    // 更新步驟狀態
                    const stepIndex = Math.floor(progress / 25);
                    if (stepIndex > currentStep && stepIndex < steps.length) {
                        addLog('info', `正在執行：${steps[stepIndex]}`);
                        updateStepStatus(stepIndex);
                        currentStep = stepIndex;
                    }
                }
            }, 200);
        }

        /**
         * 更新進度條
         */
        function updateProgress(percentage) {
            document.getElementById('overallProgress').textContent = Math.round(percentage) + '%';
            document.getElementById('overallProgressBar').style.width = percentage + '%';
        }

        /**
         * 更新步驟狀態
         */
        function updateStepStatus(activeIndex) {
            const indicators = document.querySelectorAll('.step-indicator');
            indicators.forEach((indicator, index) => {
                const icon = indicator.querySelector('i');
                const circle = indicator.querySelector('div');

                if (index <= activeIndex) {
                    indicator.classList.add('active');
                    circle.classList.remove('bg-gray-100');
                    circle.classList.add('bg-brand-100');
                    icon.classList.remove('text-gray-400');
                    icon.classList.add('text-brand-600');
                }
            });
        }

        /**
         * 重置步驟狀態
         */
        function resetSteps() {
            const indicators = document.querySelectorAll('.step-indicator');
            indicators.forEach(indicator => {
                const icon = indicator.querySelector('i');
                const circle = indicator.querySelector('div');

                indicator.classList.remove('active');
                circle.classList.remove('bg-brand-100');
                circle.classList.add('bg-gray-100');
                icon.classList.remove('text-brand-600');
                icon.classList.add('text-gray-400');
            });
        }

        /**
         * 添加處理記錄
         */
        function addLog(type, message) {
            const logsContainer = document.getElementById('processingLogs');
            const time = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry p-3 rounded-lg border border-gray-100 ${type}`;
            logEntry.innerHTML = `
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">${message}</span>
                    <span class="text-xs text-gray-400">${time}</span>
                </div>
            `;

            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            // 限制日誌數量
            if (logsContainer.children.length > 20) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        /**
         * 清除處理記錄
         */
        function clearLogs() {
            const logsContainer = document.getElementById('processingLogs');
            logsContainer.innerHTML = `
                <div class="log-entry p-3 rounded-lg border border-gray-100">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">系統就緒，等待處理指令</span>
                        <span class="text-xs text-gray-400">${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
            `;
        }

        /**
         * 顯示預覽
         */
        function showPreview() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('請選擇日期範圍');
                return;
            }

            // 生成預覽內容
            const previewContent = `
                <div class="space-y-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-2">處理範圍預覽</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-blue-700">日期範圍：</span>
                                <span class="font-medium">${startDate} 至 ${endDate}</span>
                            </div>
                            <div>
                                <span class="text-blue-700">員工數量：</span>
                                <span class="font-medium">${document.getElementById('pendingEmployees').textContent} 人</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left">員工姓名</th>
                                    <th class="px-4 py-2 text-left">部門</th>
                                    <th class="px-4 py-2 text-left">待處理天數</th>
                                    <th class="px-4 py-2 text-left">預計處理項目</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-2">張小明</td>
                                    <td class="px-4 py-2">資訊技術部</td>
                                    <td class="px-4 py-2">${document.getElementById('pendingDays').textContent} 天</td>
                                    <td class="px-4 py-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">遲到計算</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 ml-1">加班計算</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-2">李美玲</td>
                                    <td class="px-4 py-2">人力資源部</td>
                                    <td class="px-4 py-2">${document.getElementById('pendingDays').textContent} 天</td>
                                    <td class="px-4 py-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">請假整合</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-2">王大華</td>
                                    <td class="px-4 py-2">業務部</td>
                                    <td class="px-4 py-2">${document.getElementById('pendingDays').textContent} 天</td>
                                    <td class="px-4 py-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">早退計算</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 ml-1">加班計算</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 class="font-medium text-yellow-900 mb-2">⚠️ 處理注意事項</h4>
                        <ul class="text-sm text-yellow-800 space-y-1">
                            <li>• 處理過程中請勿關閉瀏覽器</li>
                            <li>• 已計算的資料將被覆蓋（如有勾選覆蓋選項）</li>
                            <li>• 建議在非高峰時段進行大量資料處理</li>
                            <li>• 處理完成後將自動生成處理報告</li>
                        </ul>
                    </div>
                </div>
            `;

            document.getElementById('previewContent').innerHTML = previewContent;
            document.getElementById('previewModal').classList.remove('hidden');
        }

        /**
         * 關閉預覽模態框
         */
        function closePreviewModal() {
            document.getElementById('previewModal').classList.add('hidden');
        }

        /**
         * 確認處理
         */
        function confirmProcessing() {
            closePreviewModal();
            startProcessing();
        }

        // 初始化 Lucide 圖標
        lucide.createIcons();
    </script>
</body>

</html>