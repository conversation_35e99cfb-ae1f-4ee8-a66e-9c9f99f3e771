<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>遠漢科技考勤系統 - Han AttendanceOS</title>

    <!-- PWA 支援 -->
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#0ea5e9">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Han考勤">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#6366f1',
                            600: '#4f46e5',
                        }
                    }
                }
            }
        }
    </script>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>

    <style>
        /* 修復日期選擇器在模態框中的顯示問題 */
        
        .date-input {
            position: relative !important;
            z-index: 1 !important;
        }
        /* 確保日期選擇器彈窗可以正常顯示 */
        
        .date-input::-webkit-calendar-picker-indicator {
            position: relative;
            z-index: 2;
            cursor: pointer;
            opacity: 1;
            background: none;
            border: none;
            padding: 0;
            margin: 0;
        }
        /* 修復模態框中的日期選擇器層級問題 */
        
        .fixed.z-50 .date-input {
            position: relative;
            z-index: auto;
        }
        /* 確保日期選擇器彈窗在模態框上方 */
        
        input[type="date"]::-webkit-calendar-picker-indicator {
            z-index: 9999 !important;
        }
        /* 修復模態框中的日期選擇器問題 */
        
        .fixed.z-50 input[type="date"] {
            position: relative !important;
            z-index: 10000 !important;
        }
        /* 確保日期選擇器彈窗能夠顯示 */
        
        input[type="date"]::-webkit-datetime-edit,
        input[type="date"]::-webkit-inner-spin-button,
        input[type="date"]::-webkit-clear-button {
            z-index: 10000 !important;
        }
        /* 時間選項卡片樣式 */
        
        .time-option input[type="radio"]:checked+.time-option-card {
            border-color: #10b981;
            background-color: #d1fae5;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .time-option:hover .time-option-card {
            border-color: #10b981;
            transform: translateY(-1px);
        }
        /* 動畫效果 */
        
        .time-option-card {
            transition: all 0.2s ease;
        }
        /* 響應式調整 */
        
        @media (max-width: 640px) {
            .time-option-card {
                padding: 0.5rem;
            }
        }
    </style>
</head>

<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- 頂部導航 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">Han AttendanceOS</h1>
                        <p class="text-sm text-gray-500">遠漢科技考勤系統</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p id="userName" class="text-sm font-medium text-gray-900">載入中...</p>
                        <p id="userInfo" class="text-xs text-gray-500">載入中...</p>
                    </div>
                    <div class="relative">
                        <button id="userMenuButton" class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:shadow-lg transition-all duration-200">
                            <i data-lucide="user" class="w-5 h-5 text-white"></i>
                        </button>
                        <!-- 用戶選單 -->
                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 z-50">
                            <div class="py-2">
                                <button onclick="changePassword()" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                    <i data-lucide="key" class="w-4 h-4 mr-3"></i>
                                    修改密碼
                                </button>
                                <hr class="my-1">
                                <button onclick="logout()" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
                                    <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
                                    登出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主內容區 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 歡迎區域 -->
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">歡迎回來！</h2>
            <p class="text-lg text-gray-600">選擇您需要的服務</p>
        </div>

        <!-- 功能卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <!-- 加班申請 -->
            <div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="clock-4" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">加班申請</h3>
                    <p class="text-gray-600 mb-6">加班申請作業，請確實登載</p>
                    <button onclick="showOvertimeModal()" class="w-full bg-gradient-to-r from-orange-400 to-red-500 text-white py-3 px-6 rounded-lg font-medium hover:from-orange-500 hover:to-red-600 transition-all duration-200">
                        立即申請
                    </button>
                </div>
            </div>

            <!-- 請假申請 -->
            <div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="calendar-days" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">請假申請</h3>
                    <p class="text-gray-600 mb-6">申請各類假別，線上審核流程</p>
                    <button onclick="showLeaveModal()" class="w-full bg-gradient-to-r from-green-400 to-blue-500 text-white py-3 px-6 rounded-lg font-medium hover:from-green-500 hover:to-blue-600 transition-all duration-200">
                        立即申請
                    </button>
                </div>
            </div>

            <!-- 線上打卡 -->
            <div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="fingerprint" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">線上打卡</h3>
                    <p class="text-gray-600 mb-6">遠端工作打卡，需要權限開通</p>
                    <button id="clockButton" onclick="window.location.href='/elite/online-clock'" class="w-full bg-gradient-to-r from-purple-400 to-pink-500 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-500 hover:to-pink-600 transition-all duration-200">
                        線上打卡
                    </button>
                </div>
            </div>
        </div>

        <!-- 我的狀態區塊已隱藏 -->

        <!-- 我的申請記錄 - Apple/Google 大廠風格 -->
        <div class="mt-12 bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-100/50 border border-gray-100/60 overflow-hidden">
            <!-- 標題區域 -->
            <div class="bg-gradient-to-r from-indigo-500/5 to-purple-500/5 px-8 py-6 border-b border-gray-100/60">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 tracking-tight">我的申請記錄</h3>
                            <p class="text-sm text-gray-500 mt-1">管理您的加班與請假申請</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div id="requestsCount" class="text-sm text-gray-500 font-medium">載入中...</div>
                        <div class="text-xs text-gray-400 mt-1">實時更新</div>
                    </div>
                </div>
            </div>

            <!-- 內容區域 -->
            <div class="p-8">

                <!-- 篩選標籤已移除 -->

                <!-- 申請列表 -->
                <div id="requestsList" class="space-y-6">
                    <!-- 載入中提示 -->
                    <div id="loadingRequests" class="text-center py-16">
                        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-3xl mb-6 shadow-lg">
                            <div class="relative">
                                <svg class="w-10 h-10 text-indigo-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                </svg>
                                <div class="absolute inset-0 bg-indigo-500/20 rounded-full animate-ping"></div>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">載入申請記錄中</h4>
                        <p class="text-sm text-gray-500">正在為您準備最新的申請狀態...</p>
                    </div>

                    <!-- 空狀態 -->
                    <div id="emptyRequests" class="text-center py-16" style="display: none;">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl mb-8 shadow-lg">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                            </svg>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-700 mb-3">還沒有申請記錄</h4>
                        <p class="text-gray-500 mb-8 max-w-md mx-auto leading-relaxed">您還沒有提交任何加班或請假申請，點擊下方按鈕開始您的第一筆申請。</p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button onclick="showOvertimeModal()" class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                申請加班
                            </button>
                            <button onclick="showLeaveModal()" class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-2xl font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                申請請假
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 加班申請模態框 - 專業級設計 -->
    <div id="overtimeModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-3">
            <!-- 模態框內容 -->
            <div class="bg-white rounded-3xl w-full max-w-lg mx-auto shadow-xl transform transition-all max-h-[95vh] flex flex-col">

                <!-- 標題區域 - 專業級設計 -->
                <div class="bg-gradient-to-r from-orange-500/10 to-red-500/10 px-6 py-6 border-b border-gray-100 rounded-t-3xl flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i data-lucide="clock-4" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">加班申請</h3>
                                <p class="text-sm text-gray-500 mt-1">填寫以下資訊提交加班申請</p>
                            </div>
                        </div>
                        <button type="button" onclick="hideOvertimeModal()" class="p-2 hover:bg-gray-100 rounded-xl transition-colors">
                            <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>
                </div>

                <!-- 表單內容區域 -->
                <div class="px-6 py-6 flex-1 min-h-0 overflow-y-auto">
                    <form id="overtimeForm" class="space-y-6">

                        <!-- 加班日期 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="calendar" class="w-4 h-4 text-orange-600"></i>
                                <label class="block text-sm font-semibold text-gray-700">加班日期</label>
                                <span class="text-red-500 text-sm">*</span>
                            </div>
                            <div class="relative">
                                <input type="text" id="overtimeDate" readonly class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-sm cursor-pointer" placeholder="點擊選擇日期">
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                    <i data-lucide="calendar" class="w-5 h-5"></i>
                                </div>

                                <!-- 自定義日曆彈窗 -->
                                <div id="customCalendar" class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 hidden w-80">
                                    <div class="p-4">
                                        <!-- 日曆標題 -->
                                        <div class="flex items-center justify-between mb-4">
                                            <button type="button" id="prevMonth" class="p-2 hover:bg-gray-100 rounded-lg">
                                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                                            </button>
                                            <h3 id="calendarTitle" class="font-semibold text-gray-800"></h3>
                                            <button type="button" id="nextMonth" class="p-2 hover:bg-gray-100 rounded-lg">
                                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                                            </button>
                                        </div>

                                        <!-- 星期標題 -->
                                        <div class="grid grid-cols-7 gap-1 mb-2">
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">日</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">一</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">二</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">三</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">四</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">五</div>
                                            <div class="text-center text-sm font-medium text-gray-500 py-2">六</div>
                                        </div>

                                        <!-- 日期網格 -->
                                        <div id="calendarGrid" class="grid grid-cols-7 gap-1">
                                            <!-- 日期將由JavaScript動態生成 -->
                                        </div>

                                        <!-- 今天按鈕 -->
                                        <div class="mt-4 pt-3 border-t border-gray-200">
                                            <button type="button" id="todayBtn" class="w-full py-2 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                                                今天
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 加班時間 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="clock" class="w-4 h-4 text-blue-600"></i>
                                <label class="block text-sm font-semibold text-gray-700">加班時間</label>
                                <span class="text-red-500 text-sm">*</span>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <i data-lucide="play" class="w-3 h-3 text-blue-500"></i>
                                        <label class="block text-xs font-medium text-blue-600">開始時間</label>
                                        <span class="text-red-500 text-xs">*</span>
                                    </div>
                                    <select id="overtimeStart" class="w-full bg-gray-50 border border-gray-200 rounded-xl px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" onchange="calculateOvertimeHours()">
                                        <option value="">選擇時間</option>
                                        <option value="00:00">00:00</option>
                                        <option value="00:30">00:30</option>
                                        <option value="01:00">01:00</option>
                                        <option value="01:30">01:30</option>
                                        <option value="02:00">02:00</option>
                                        <option value="02:30">02:30</option>
                                        <option value="03:00">03:00</option>
                                        <option value="03:30">03:30</option>
                                        <option value="04:00">04:00</option>
                                        <option value="04:30">04:30</option>
                                        <option value="05:00">05:00</option>
                                        <option value="05:30">05:30</option>
                                        <option value="06:00">06:00</option>
                                        <option value="06:30">06:30</option>
                                        <option value="07:00">07:00</option>
                                        <option value="07:30">07:30</option>
                                        <option value="08:00">08:00</option>
                                        <option value="08:30">08:30</option>
                                        <option value="09:00">09:00</option>
                                        <option value="09:30">09:30</option>
                                        <option value="10:00">10:00</option>
                                        <option value="10:30">10:30</option>
                                        <option value="11:00">11:00</option>
                                        <option value="11:30">11:30</option>
                                        <option value="12:00">12:00</option>
                                        <option value="12:30">12:30</option>
                                        <option value="13:00">13:00</option>
                                        <option value="13:30">13:30</option>
                                        <option value="14:00">14:00</option>
                                        <option value="14:30">14:30</option>
                                        <option value="15:00">15:00</option>
                                        <option value="15:30">15:30</option>
                                        <option value="16:00">16:00</option>
                                        <option value="16:30">16:30</option>
                                        <option value="17:00">17:00</option>
                                        <option value="17:30">17:30</option>
                                        <option value="18:00" selected>18:00</option>
                                        <option value="18:30">18:30</option>
                                        <option value="19:00">19:00</option>
                                        <option value="19:30">19:30</option>
                                        <option value="20:00">20:00</option>
                                        <option value="20:30">20:30</option>
                                        <option value="21:00">21:00</option>
                                        <option value="21:30">21:30</option>
                                        <option value="22:00">22:00</option>
                                        <option value="22:30">22:30</option>
                                        <option value="23:00">23:00</option>
                                        <option value="23:30">23:30</option>
                                    </select>
                                </div>
                                <div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <i data-lucide="square" class="w-3 h-3 text-blue-500"></i>
                                        <label class="block text-xs font-medium text-blue-600">結束時間</label>
                                        <span class="text-red-500 text-xs">*</span>
                                    </div>
                                    <select id="overtimeEnd" class="w-full bg-gray-50 border border-gray-200 rounded-xl px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" onchange="calculateOvertimeHours()">
                                        <option value="">選擇時間</option>
                                        <option value="00:00">00:00</option>
                                        <option value="00:30">00:30</option>
                                        <option value="01:00">01:00</option>
                                        <option value="01:30">01:30</option>
                                        <option value="02:00">02:00</option>
                                        <option value="02:30">02:30</option>
                                        <option value="03:00">03:00</option>
                                        <option value="03:30">03:30</option>
                                        <option value="04:00">04:00</option>
                                        <option value="04:30">04:30</option>
                                        <option value="05:00">05:00</option>
                                        <option value="05:30">05:30</option>
                                        <option value="06:00">06:00</option>
                                        <option value="06:30">06:30</option>
                                        <option value="07:00">07:00</option>
                                        <option value="07:30">07:30</option>
                                        <option value="08:00">08:00</option>
                                        <option value="08:30">08:30</option>
                                        <option value="09:00">09:00</option>
                                        <option value="09:30">09:30</option>
                                        <option value="10:00">10:00</option>
                                        <option value="10:30">10:30</option>
                                        <option value="11:00">11:00</option>
                                        <option value="11:30">11:30</option>
                                        <option value="12:00">12:00</option>
                                        <option value="12:30">12:30</option>
                                        <option value="13:00">13:00</option>
                                        <option value="13:30">13:30</option>
                                        <option value="14:00">14:00</option>
                                        <option value="14:30">14:30</option>
                                        <option value="15:00">15:00</option>
                                        <option value="15:30">15:30</option>
                                        <option value="16:00">16:00</option>
                                        <option value="16:30">16:30</option>
                                        <option value="17:00">17:00</option>
                                        <option value="17:30">17:30</option>
                                        <option value="18:00">18:00</option>
                                        <option value="18:30">18:30</option>
                                        <option value="19:00" selected>19:00</option>
                                        <option value="19:30">19:30</option>
                                        <option value="20:00">20:00</option>
                                        <option value="20:30">20:30</option>
                                        <option value="21:00">21:00</option>
                                        <option value="21:30">21:30</option>
                                        <option value="22:00">22:00</option>
                                        <option value="22:30">22:30</option>
                                        <option value="23:00">23:00</option>
                                        <option value="23:30">23:30</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 加班類型 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="tag" class="w-4 h-4 text-purple-600"></i>
                                <label class="block text-sm font-semibold text-gray-700">加班類型</label>
                                <span class="text-red-500 text-sm">*</span>
                            </div>
                            <div class="relative">
                                <select id="overtimeType" class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm appearance-none">
                                    <option value="">選擇類型</option>
                                </select>
                                <i data-lucide="chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"></i>
                            </div>
                        </div>

                        <!-- 加班原因 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="message-square" class="w-4 h-4 text-green-600"></i>
                                <label class="block text-sm font-semibold text-gray-700">加班原因</label>
                            </div>
                            <textarea id="overtimeReason" rows="3" class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none text-sm" placeholder="請詳細說明加班原因..."></textarea>
                        </div>

                        <!-- 加班時數計算顯示區域 -->
                        <div id="overtimeHoursDisplay" class="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-4" style="display: none;">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
                                    <i data-lucide="calculator" class="w-5 h-5 text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <p class="text-sm font-semibold text-orange-700">預計加班時數</p>
                                        <p class="text-xs text-orange-600">系統自動計算</p>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div id="overtimeHoursText" class="text-lg font-bold text-orange-800">加班時數：0 小時</div>
                                        <div id="overtimeDaysText" class="text-sm text-orange-600">0 天</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>

                <!-- 底部按鈕區 - 固定在底部 -->
                <div class="px-6 py-4 border-t border-gray-100 rounded-b-3xl flex-shrink-0">
                    <div class="flex space-x-3">
                        <button type="button" onclick="hideOvertimeModal()" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 rounded-xl font-semibold transition-all duration-200 text-sm">
                            取消
                        </button>
                        <button type="submit" form="overtimeForm" class="flex-1 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-3 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                            <i data-lucide="send" class="w-4 h-4 inline mr-2"></i>
                            提交申請
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 請假申請模態框 - PWA手機優化版 -->
    <div id="leaveModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-3">
            <div class="bg-white rounded-3xl w-full max-w-2xl mx-auto shadow-2xl transform transition-all max-h-[95vh] flex flex-col">
                <!-- 標題區域 - 精英版風格 -->
                <div class="bg-gradient-to-r from-green-500/10 to-blue-500/10 px-6 py-4 border-b border-gray-100 rounded-t-3xl flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i data-lucide="calendar-plus" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">請假申請</h3>
                                <p class="text-xs text-gray-500">填寫請假資訊</p>
                            </div>
                        </div>
                        <button type="button" onclick="hideLeaveModal()" class="p-2 hover:bg-gray-100 rounded-xl transition-colors">
                            <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>
                </div>

                <!-- 表單內容區域 - 支援滾動設計 -->
                <div class="px-6 py-4 flex-1 min-h-0 overflow-y-auto">
                    <form id="leaveForm" class="space-y-4">
                        <!-- 響應式網格布局 -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <!-- 左側：基本資訊 -->
                            <div class="space-y-4">
                                <!-- 區塊1: 請假類型與時間類型 -->
                                <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-4 border border-green-200">
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-semibold text-green-700 mb-2 flex items-center">
                                                <i data-lucide="tag" class="w-4 h-4 mr-2"></i>
                                                請假類型 <span class="text-red-500 ml-1">*</span>
                                            </label>
                                            <select id="leaveType" class="w-full bg-white/70 border border-green-200 rounded-xl px-3 py-2.5 focus:ring-2 focus:ring-green-500 text-sm">
                                                <option value="">請選擇請假類型</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-green-700 mb-2 flex items-center">
                                                <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                                                請假時間 <span class="text-red-500 ml-1">*</span>
                                            </label>
                                            <div class="grid grid-cols-2 gap-2">
                                                <label class="relative cursor-pointer time-option">
                                                    <input type="radio" name="leaveTimeType" value="full_day" checked onchange="toggleTimeInputs()" class="sr-only">
                                                    <div class="time-option-card bg-white/70 border-2 border-green-200 rounded-lg p-3 text-center transition-all duration-200">
                                                        <i data-lucide="sun" class="w-4 h-4 mx-auto mb-1 text-green-600"></i>
                                                        <span class="text-xs font-medium text-green-700">全天</span>
                                                    </div>
                                                </label>
                                                <label class="relative cursor-pointer time-option">
                                                    <input type="radio" name="leaveTimeType" value="partial_day" onchange="toggleTimeInputs()" class="sr-only">
                                                    <div class="time-option-card bg-white/70 border-2 border-green-200 rounded-lg p-3 text-center transition-all duration-200">
                                                        <i data-lucide="clock" class="w-4 h-4 mx-auto mb-1 text-green-600"></i>
                                                        <span class="text-xs font-medium text-green-700">部分工時</span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 區塊2: 日期選擇 -->
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-200">
                                    <label class="block text-sm font-semibold text-blue-700 mb-2 flex items-center">
                                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                        請假日期 <span class="text-red-500 ml-1">*</span>
                                    </label>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label class="block text-xs text-blue-600 mb-1">開始</label>
                                            <input type="date" id="leaveStart" class="w-full bg-white/70 border border-blue-200 rounded-lg px-2 py-2 focus:ring-2 focus:ring-blue-500 text-sm" onchange="calculateLeaveDays()">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-blue-600 mb-1">結束</label>
                                            <input type="date" id="leaveEnd" class="w-full bg-white/70 border border-blue-200 rounded-lg px-2 py-2 focus:ring-2 focus:ring-blue-500 text-sm" onchange="calculateLeaveDays()">
                                        </div>
                                    </div>

                                    <!-- 部分工時時間選擇 -->
                                    <div id="partialTimeInputs" class="mt-3 hidden">
                                        <div class="grid grid-cols-2 gap-2">
                                            <div>
                                                <label class="block text-xs text-blue-600 mb-1">開始時間</label>
                                                <select id="leaveStartTime" class="w-full bg-white/70 border border-blue-200 rounded-lg px-2 py-1.5 focus:ring-2 focus:ring-blue-500 text-xs" onchange="calculateLeaveDays()">
                                                    <option value="08:00" selected>08:00</option>
                                                    <option value="09:00">09:00</option>
                                                    <option value="10:00">10:00</option>
                                                    <option value="11:00">11:00</option>
                                                    <option value="13:30">13:30</option>
                                                    <option value="14:00">14:00</option>
                                                    <option value="15:00">15:00</option>
                                                    <option value="16:00">16:00</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-blue-600 mb-1">結束時間</label>
                                                <select id="leaveEndTime" class="w-full bg-white/70 border border-blue-200 rounded-lg px-2 py-1.5 focus:ring-2 focus:ring-blue-500 text-xs" onchange="calculateLeaveDays()">
                                                    <option value="12:00">12:00</option>
                                                    <option value="13:00">13:00</option>
                                                    <option value="14:00">14:00</option>
                                                    <option value="15:00">15:00</option>
                                                    <option value="16:00">16:00</option>
                                                    <option value="17:00">17:00</option>
                                                    <option value="17:30" selected>17:30</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 右側：代理人與原因 -->
                            <div class="space-y-4">

                                <!-- 區塊3: 代理人與原因 -->
                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 border border-purple-200">
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-semibold text-purple-700 mb-2 flex items-center">
                                                <i data-lucide="user-check" class="w-4 h-4 mr-2"></i>
                                                工作代理人 <span class="text-red-500 ml-1">*</span>
                                            </label>
                                            <select id="leaveSubstitute" class="w-full bg-white/70 border border-purple-200 rounded-xl px-3 py-2.5 focus:ring-2 focus:ring-purple-500 text-sm">
                                                <option value="">請選擇代理人</option>
                                            </select>
                                            <p class="text-xs text-purple-600 mt-1">請選擇在您請假期間代理工作的同事</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-purple-700 mb-2 flex items-center">
                                                <i data-lucide="message-square" class="w-4 h-4 mr-2"></i>
                                                請假原因 <span class="text-red-500 ml-1">*</span>
                                            </label>
                                            <textarea id="leaveReason" rows="4" class="w-full bg-white/70 border border-purple-200 rounded-xl px-3 py-2.5 focus:ring-2 focus:ring-purple-500 resize-none text-sm" placeholder="請說明請假原因..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 區塊4: 請假天數顯示 - 全寬度 -->
                        <div class="col-span-1 lg:col-span-2">

                            <!-- 區塊4: 請假天數顯示 -->
                            <div id="leaveDaysDisplay" class="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 rounded-2xl p-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center shadow-lg">
                                            <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                                        </div>
                                        <div>
                                            <p class="text-lg font-bold text-emerald-700">請假時數計算</p>
                                            <p class="text-sm text-emerald-600">系統自動計算</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="bg-white/70 rounded-xl p-3 border border-emerald-200">
                                            <div id="leaveDaysText" class="text-lg font-bold text-emerald-700 mb-1">請假天數：-- 天</div>
                                            <div id="leaveHoursText" class="text-sm font-medium text-emerald-600">請假時數：-- 小時</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 底部按鈕區 - 固定在底部 -->
                <div class="px-6 py-4 border-t border-gray-100 rounded-b-3xl flex-shrink-0">
                    <div class="flex space-x-3">
                        <button type="button" onclick="hideLeaveModal()" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 rounded-xl font-semibold transition-all duration-200 text-sm">
                            取消
                        </button>
                        <button type="submit" form="leaveForm" class="flex-1 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white py-3 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                            <i data-lucide="send" class="w-4 h-4 inline mr-2"></i>
                            提交申請
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </form>
    </div>
    </div>
    </div>

    <!-- 刪除確認對話框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl p-6 max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="deleteConfirmContent">
            <div class="text-center">
                <!-- 警告圖標 -->
                <div class="w-16 h-16 bg-gradient-to-r from-red-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>

                <!-- 標題和描述 -->
                <h3 class="text-xl font-bold text-gray-900 mb-2">確認刪除</h3>
                <p class="text-sm text-gray-500 mb-4">此操作無法復原</p>

                <!-- 確認訊息 -->
                <div class="bg-gray-50 rounded-xl p-4 mb-6">
                    <p id="deleteConfirmMessage" class="text-gray-700 font-medium">確定要刪除這筆申請記錄嗎？</p>
                </div>

                <!-- 按鈕組 -->
                <div class="flex space-x-3">
                    <button type="button" onclick="hideDeleteConfirm()" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                        取消
                    </button>
                    <button type="button" onclick="confirmDelete()" class="flex-1 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl">
                        確認刪除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 過濾功能已移除

        // 初始化（已移至頁面底部統一處理）

        // 載入用戶統計（已隱藏狀態區塊，此函數保留但不執行任何操作）
        async function loadUserStats() {
            // 狀態區塊已隱藏，不需要載入統計數據
            console.log('狀態區塊已隱藏');
        }

        // 檢查打卡權限
        async function checkClockPermission() {
            try {
                const response = await fetch('/api/auth/clock-permission');
                const clockButton = document.getElementById('clockButton');

                if (response.ok) {
                    const result = await response.json();
                    const hasPermission = result.allow_online_punch;

                    console.log('線上打卡權限檢查結果:', result);

                    if (!hasPermission) {
                        // 禁用按鈕
                        clockButton.classList.remove('bg-gradient-to-r', 'from-purple-400', 'to-pink-500', 'hover:from-purple-500', 'hover:to-pink-600');
                        clockButton.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                        clockButton.textContent = '無權限';
                        clockButton.disabled = true;
                        clockButton.onclick = null;

                        // 顯示提示訊息
                        clockButton.title = '您沒有線上打卡權限，請聯繫管理員';

                        console.log('線上打卡權限已禁用');
                    } else {
                        // 確保按鈕可用
                        clockButton.classList.remove('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                        clockButton.classList.add('bg-gradient-to-r', 'from-purple-400', 'to-pink-500', 'hover:from-purple-500', 'hover:to-pink-600');
                        clockButton.textContent = '線上打卡';
                        clockButton.disabled = false;
                        clockButton.onclick = () => window.location.href = '/elite/online-clock';
                        clockButton.title = '點擊進行線上打卡';

                        console.log('線上打卡權限已啟用');
                    }
                } else {
                    console.error('權限檢查API調用失敗:', response.status);
                    // API調用失敗時，為安全起見禁用按鈕
                    clockButton.classList.remove('bg-gradient-to-r', 'from-purple-400', 'to-pink-500', 'hover:from-purple-500', 'hover:to-pink-600');
                    clockButton.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                    clockButton.textContent = '權限檢查失敗';
                    clockButton.disabled = true;
                    clockButton.onclick = null;
                }
            } catch (error) {
                console.error('檢查權限失敗:', error);
                // 網路錯誤時，為安全起見禁用按鈕
                const clockButton = document.getElementById('clockButton');
                clockButton.classList.remove('bg-gradient-to-r', 'from-purple-400', 'to-pink-500', 'hover:from-purple-500', 'hover:to-pink-600');
                clockButton.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                clockButton.textContent = '網路錯誤';
                clockButton.disabled = true;
                clockButton.onclick = null;
            }
        }

        // 自定義日曆變數
        let currentCalendarDate = new Date();
        let selectedDate = null;

        // 加班申請相關
        function showOvertimeModal() {
            document.getElementById('overtimeModal').classList.remove('hidden');

            // 初始化日期
            const today = new Date();
            selectedDate = today;
            currentCalendarDate = new Date(today);

            // 設定加班日期預設為今天
            document.getElementById('overtimeDate').value = formatDateForDisplay(today);

            // 初始化自定義日曆
            initCustomCalendar();

            loadOvertimeTypes();

            // 初始計算加班時數
            calculateOvertimeHours();
        }

        // 格式化日期顯示
        function formatDateForDisplay(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 初始化自定義日曆
        function initCustomCalendar() {
            const dateInput = document.getElementById('overtimeDate');
            const calendar = document.getElementById('customCalendar');
            const prevBtn = document.getElementById('prevMonth');
            const nextBtn = document.getElementById('nextMonth');
            const todayBtn = document.getElementById('todayBtn');

            // 移除之前的事件監聽器（避免重複綁定）
            dateInput.replaceWith(dateInput.cloneNode(true));
            const newDateInput = document.getElementById('overtimeDate');

            // 點擊輸入框顯示日曆
            newDateInput.addEventListener('click', function() {
                calendar.classList.toggle('hidden');
                if (!calendar.classList.contains('hidden')) {
                    renderCalendar();
                }
            });

            // 點擊外部關閉日曆
            document.addEventListener('click', function(e) {
                if (!newDateInput.contains(e.target) && !calendar.contains(e.target)) {
                    calendar.classList.add('hidden');
                }
            });

            // 上一個月
            prevBtn.onclick = function() {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
                renderCalendar();
            };

            // 下一個月
            nextBtn.onclick = function() {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
                renderCalendar();
            };

            // 今天按鈕
            todayBtn.onclick = function() {
                const today = new Date();
                selectedDate = today;
                currentCalendarDate = new Date(today);
                newDateInput.value = formatDateForDisplay(today);
                calendar.classList.add('hidden');
                calculateOvertimeHours();
            };
        }

        // 渲染日曆
        function renderCalendar() {
            const title = document.getElementById('calendarTitle');
            const grid = document.getElementById('calendarGrid');

            const year = currentCalendarDate.getFullYear();
            const month = currentCalendarDate.getMonth();

            // 設定標題
            title.textContent = `${year}年 ${month + 1}月`;

            // 清空網格
            grid.innerHTML = '';

            // 獲取當月第一天和最後一天
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            // 生成42個日期格子（6週）
            for (let i = 0; i < 42; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);

                const dayElement = document.createElement('button');
                dayElement.type = 'button';
                dayElement.className = 'w-8 h-8 text-sm rounded-lg hover:bg-gray-100 transition-colors';
                dayElement.textContent = date.getDate();

                // 判斷是否為當月日期
                if (date.getMonth() !== month) {
                    dayElement.className += ' text-gray-300';
                } else {
                    dayElement.className += ' text-gray-700';
                }

                // 判斷是否為今天
                const today = new Date();
                if (date.toDateString() === today.toDateString()) {
                    dayElement.className += ' bg-blue-100 text-blue-600 font-semibold';
                }

                // 判斷是否為選中日期
                if (selectedDate && date.toDateString() === selectedDate.toDateString()) {
                    dayElement.className += ' bg-orange-500 text-white font-semibold';
                }

                // 點擊事件
                dayElement.onclick = function() {
                    selectedDate = new Date(date);
                    document.getElementById('overtimeDate').value = formatDateForDisplay(date);
                    document.getElementById('customCalendar').classList.add('hidden');
                    calculateOvertimeHours();
                };

                grid.appendChild(dayElement);
            }
        }

        /**
         * 計算加班時數並顯示結果
         * 
         * 功能描述：
         * - 根據開始時間和結束時間計算加班小時數
         * - 將小時數轉換為天數（8小時=1天）
         * - 動態顯示計算結果
         * 
         * 計算規則：
         * - 加班時數 = 結束時間 - 開始時間
         * - 加班天數 = 加班時數 ÷ 8（四捨五入到小數點後2位）
         */
        function calculateOvertimeHours() {
            const startTime = document.getElementById('overtimeStart').value;
            const endTime = document.getElementById('overtimeEnd').value;
            const displayDiv = document.getElementById('overtimeHoursDisplay');
            const hoursText = document.getElementById('overtimeHoursText');
            const daysText = document.getElementById('overtimeDaysText');

            if (!startTime || !endTime) {
                displayDiv.style.display = 'none';
                return;
            }

            // 將時間字符串轉換為分鐘數
            function timeToMinutes(timeStr) {
                const [hours, minutes] = timeStr.split(':').map(Number);
                return hours * 60 + minutes;
            }

            const startMinutes = timeToMinutes(startTime);
            const endMinutes = timeToMinutes(endTime);

            // 計算時間差（分鐘）
            let diffMinutes = endMinutes - startMinutes;

            // 如果結束時間小於開始時間，表示跨日
            if (diffMinutes < 0) {
                diffMinutes += 24 * 60; // 加上24小時
            }

            // 轉換為小時數（保留1位小數）
            const hours = Math.round(diffMinutes / 60 * 10) / 10;

            // 計算天數（8小時=1天，保留2位小數）
            const days = Math.round(hours / 8 * 100) / 100;

            if (hours > 0) {
                hoursText.textContent = `加班時數：${hours} 小時`;
                daysText.textContent = `${days} 天`;
                displayDiv.style.display = 'block';
            } else {
                displayDiv.style.display = 'none';
            }
        }

        function hideOvertimeModal() {
            document.getElementById('overtimeModal').classList.add('hidden');
            document.getElementById('overtimeForm').reset();
        }

        async function loadOvertimeTypes() {
            try {
                const response = await fetch('/api/overtime/types');
                const data = await response.json();

                const select = document.getElementById('overtimeType');
                if (data.success) {
                    select.innerHTML = '<option value="">選擇類型</option>' +
                        data.records.map(type => {
                            // 判斷是否為平日加班（假設 code 為 'weekday' 或 name 包含 '平日'）
                            const isWeekday = type.name.includes('平日') || type.code === 'weekday';
                            const selected = isWeekday ? ' selected' : '';
                            return `<option value="${type.code}"${selected}>${type.name}</option>`;
                        }).join('');
                }
            } catch (error) {
                console.error('載入加班類型失敗:', error);
            }
        }

        // 請假申請相關
        function showLeaveModal() {
            document.getElementById('leaveModal').classList.remove('hidden');

            // 設定請假日期預設為今天
            const today = new Date();
            const todayString = today.getFullYear() + '-' +
                String(today.getMonth() + 1).padStart(2, '0') + '-' +
                String(today.getDate()).padStart(2, '0');
            document.getElementById('leaveStart').value = todayString;
            document.getElementById('leaveEnd').value = todayString;

            // 設定時間預設值
            document.getElementById('leaveStartTime').value = '08:00';
            document.getElementById('leaveEndTime').value = '17:00';

            loadLeaveTypes();
            loadSubstitutes();

            // 立即計算並顯示請假統計
            calculateLeaveDays();
        }

        function hideLeaveModal() {
            document.getElementById('leaveModal').classList.add('hidden');
            document.getElementById('leaveForm').reset();
            // 隱藏天數顯示
            document.getElementById('leaveDaysDisplay').style.display = 'none';
            // 隱藏部分工時時間選擇
            document.getElementById('partialTimeInputs').style.display = 'none';
        }

        // 計算請假天數函數已移至下方統一實現

        async function loadLeaveTypes() {
            try {
                const response = await fetch('/api/leave-types');
                const data = await response.json();

                const select = document.getElementById('leaveType');
                if (data.leave_types) {
                    select.innerHTML = '<option value="">選擇假別</option>' +
                        data.leave_types.map(type => {
                            // 判斷是否為事假（假設 code 為 'personal' 或 name 包含 '事假'）
                            const isPersonalLeave = type.name.includes('事假') || type.code === 'personal';
                            const selected = isPersonalLeave ? ' selected' : '';
                            return `<option value="${type.code}"${selected}>${type.name}</option>`;
                        }).join('');
                }
            } catch (error) {
                console.error('載入請假類型失敗:', error);
            }
        }

        // 載入代理人選項
        async function loadSubstitutes() {
            try {
                console.log('開始載入代理人選項...');
                const response = await fetch('/api/employees');
                const data = await response.json();
                console.log('員工API響應:', data);

                const select = document.getElementById('leaveSubstitute');
                const currentEmployeeId = await getCurrentEmployeeId();
                console.log('當前員工ID:', currentEmployeeId);

                if (data.employees && Array.isArray(data.employees)) {
                    // 過濾掉申請人自己，其他人都可以作為代理人
                    const substitutes = data.employees.filter(emp => emp.id !== currentEmployeeId);
                    console.log('可選代理人:', substitutes.length, '位');

                    select.innerHTML = '<option value="">請選擇代理人</option>' +
                        substitutes.map(emp =>
                            `<option value="${emp.id}">${emp.name} (${emp.employee_id}) - ${emp.department_name || emp.department || '未設定部門'}</option>`
                        ).join('');

                    console.log('代理人選項載入完成');
                } else {
                    console.error('員工數據格式錯誤:', data);
                }
            } catch (error) {
                console.error('載入代理人選項失敗:', error);
            }
        }

        // 線上打卡功能已移至專門頁面 /elite-online-clock





        // 獲取日期文字
        function getDateText(request) {
            if (request.type === 'overtime') {
                return `${request.overtime_date}`;
            } else {
                // 請假申請
                if (request.time_type === 'partial_day' && request.start_time && request.end_time) {
                    // 部分工時：顯示具體時間
                    if (request.start_date === request.end_date) {
                        return `${request.start_date} ${request.start_time}-${request.end_time}`;
                    } else {
                        return `${request.start_date} ${request.start_time} 至 ${request.end_date} ${request.end_time}`;
                    }
                } else {
                    // 全天請假
                    return `${request.start_date} 至 ${request.end_date}`;
                }
            }
        }

        // 獲取詳細文字
        function getDetailText(request) {
            if (request.type === 'overtime') {
                return `${request.overtime_type_name || request.overtime_type || '一般加班'} (${request.overtime_hours || request.hours || 0}小時)`;
            } else {
                // 請假申請
                if (request.time_type === 'partial_day') {
                    // 部分工時
                    const hours = request.leave_hours || 0;
                    return `${request.leave_type_name || request.leave_type || '請假'} (${hours}小時)`;
                } else {
                    // 全天請假
                    const days = calculateDays(request.start_date, request.end_date);
                    return `${request.leave_type_name || request.leave_type || '請假'} (${days}天)`;
                }
            }
        }

        // 計算天數
        function calculateDays(startDate, endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const timeDiff = end.getTime() - start.getTime();
            return Math.floor(timeDiff / (1000 * 3600 * 24)) + 1;
        }

        // 切換時間輸入顯示
        function toggleTimeInputs() {
            const timeType = document.querySelector('input[name="leaveTimeType"]:checked').value;
            const partialTimeInputs = document.getElementById('partialTimeInputs');

            if (timeType === 'partial_day') {
                partialTimeInputs.style.display = 'grid';
            } else {
                partialTimeInputs.style.display = 'none';
            }

            calculateLeaveDays();
        }

        // 計算請假天數/時數
        function calculateLeaveDays() {
            const startDate = document.getElementById('leaveStart').value;
            const endDate = document.getElementById('leaveEnd').value;
            const timeType = document.querySelector('input[name="leaveTimeType"]:checked').value;
            const leaveDaysDisplay = document.getElementById('leaveDaysDisplay');
            const leaveDaysText = document.getElementById('leaveDaysText');
            const leaveHoursText = document.getElementById('leaveHoursText');

            if (!startDate || !endDate) {
                leaveDaysDisplay.style.display = 'none';
                return;
            }

            const start = new Date(startDate);
            const end = new Date(endDate);

            if (start > end) {
                // 結束日期早於開始日期
                leaveDaysText.textContent = '結束日期不能早於開始日期';
                leaveHoursText.textContent = '';
                leaveDaysDisplay.style.display = 'block';

                // 設定錯誤樣式
                const displayDiv = leaveDaysDisplay.querySelector('div');
                displayDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-3';
                leaveDaysText.className = 'text-red-700 font-medium';
                leaveHoursText.className = 'text-red-700 font-medium';
                return;
            }

            if (timeType === 'full_day') {
                // 全天請假 - 顯示天數和時數（每天8小時）
                const days = Math.floor((end.getTime() - start.getTime()) / (1000 * 3600 * 24)) + 1;
                const hours = days * 8; // 每天8小時

                leaveDaysText.textContent = `請假天數：${days} 天`;
                leaveHoursText.textContent = `請假時數：${hours} 小時`;
                leaveDaysDisplay.style.display = 'block';

                // 設定正常樣式
                const displayDiv = leaveDaysDisplay.querySelector('div');
                displayDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3';
                leaveDaysText.className = 'text-blue-700 font-medium';
                leaveHoursText.className = 'text-blue-700 font-medium';
            } else {
                // 部分工時請假
                const startTime = document.getElementById('leaveStartTime').value;
                const endTime = document.getElementById('leaveEndTime').value;

                if (startTime && endTime) {
                    const hours = calculatePartialLeaveHours(startDate, endDate, startTime, endTime);
                    // 根據實際時數計算天數比例（每天8小時）
                    const actualDays = Math.round((hours / 8) * 100) / 100; // 保留兩位小數

                    leaveDaysText.textContent = `請假天數：${actualDays} 天`;
                    leaveHoursText.textContent = `請假時數：${hours} 小時`;
                    leaveDaysDisplay.style.display = 'block';

                    // 設定正常樣式
                    const displayDiv = leaveDaysDisplay.querySelector('div');
                    displayDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3';
                    leaveDaysText.className = 'text-blue-700 font-medium';
                    leaveHoursText.className = 'text-blue-700 font-medium';
                } else {
                    // 如果還沒選擇時間，顯示提示
                    leaveDaysText.textContent = `請假天數：請選擇時間`;
                    leaveHoursText.textContent = `請假時數：請選擇時間`;
                    leaveDaysDisplay.style.display = 'block';

                    // 設定正常樣式
                    const displayDiv = leaveDaysDisplay.querySelector('div');
                    displayDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3';
                    leaveDaysText.className = 'text-blue-700 font-medium';
                    leaveHoursText.className = 'text-blue-700 font-medium';
                }
            }
        }

        // 計算部分工時請假的時數
        function calculatePartialLeaveHours(startDate, endDate, startTime, endTime) {
            if (startDate === endDate) {
                // 同一天的部分工時
                const start = new Date(`${startDate}T${startTime}`);
                const end = new Date(`${endDate}T${endTime}`);

                // 計算總分鐘數
                const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);

                // 扣除午休時間（12:00-13:00）
                let lunchBreakMinutes = 0;
                const lunchStart = new Date(`${startDate}T12:00`);
                const lunchEnd = new Date(`${startDate}T13:00`);

                if (start < lunchEnd && end > lunchStart) {
                    // 請假時間跨越午休時間
                    const overlapStart = new Date(Math.max(start.getTime(), lunchStart.getTime()));
                    const overlapEnd = new Date(Math.min(end.getTime(), lunchEnd.getTime()));
                    lunchBreakMinutes = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60);
                }

                const actualMinutes = diffMinutes - lunchBreakMinutes;
                return Math.round((actualMinutes / 60) * 10) / 10; // 保留一位小數
            } else {
                // 跨天的部分工時請假
                let totalHours = 0;

                // 第一天：從開始時間到下班時間（17:00）
                const firstDayStart = new Date(`${startDate}T${startTime}`);
                const firstDayEnd = new Date(`${startDate}T17:00`);
                if (firstDayStart < firstDayEnd) {
                    const firstDayMinutes = (firstDayEnd.getTime() - firstDayStart.getTime()) / (1000 * 60);
                    // 扣除午休時間
                    let firstDayLunchMinutes = 0;
                    const lunchStart = new Date(`${startDate}T12:00`);
                    const lunchEnd = new Date(`${startDate}T13:00`);
                    if (firstDayStart < lunchEnd && firstDayEnd > lunchStart) {
                        const overlapStart = new Date(Math.max(firstDayStart.getTime(), lunchStart.getTime()));
                        const overlapEnd = new Date(Math.min(firstDayEnd.getTime(), lunchEnd.getTime()));
                        firstDayLunchMinutes = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60);
                    }
                    totalHours += (firstDayMinutes - firstDayLunchMinutes) / 60;
                }

                // 中間的完整天數（如果有的話）
                const startDateObj = new Date(startDate);
                const endDateObj = new Date(endDate);
                const daysDiff = Math.floor((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 3600 * 24));

                if (daysDiff > 1) {
                    totalHours += (daysDiff - 1) * 8; // 中間每天8小時
                }

                // 最後一天：從上班時間（08:00）到結束時間
                const lastDayStart = new Date(`${endDate}T08:00`);
                const lastDayEnd = new Date(`${endDate}T${endTime}`);
                if (lastDayEnd > lastDayStart) {
                    const lastDayMinutes = (lastDayEnd.getTime() - lastDayStart.getTime()) / (1000 * 60);
                    // 扣除午休時間
                    let lastDayLunchMinutes = 0;
                    const lunchStart = new Date(`${endDate}T12:00`);
                    const lunchEnd = new Date(`${endDate}T13:00`);
                    if (lastDayStart < lunchEnd && lastDayEnd > lunchStart) {
                        const overlapStart = new Date(Math.max(lastDayStart.getTime(), lunchStart.getTime()));
                        const overlapEnd = new Date(Math.min(lastDayEnd.getTime(), lunchEnd.getTime()));
                        lastDayLunchMinutes = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60);
                    }
                    totalHours += (lastDayMinutes - lastDayLunchMinutes) / 60;
                }

                return Math.round(totalHours * 10) / 10; // 保留一位小數
            }
        }

        // 計算請假時數（支援跨天）
        function calculateLeaveHours(startDate, endDate, startTime, endTime) {
            const start = new Date(`${startDate}T${startTime}`);
            const end = new Date(`${endDate}T${endTime}`);

            if (startDate === endDate) {
                // 同一天
                const diffMs = end.getTime() - start.getTime();
                return Math.round(diffMs / (1000 * 60 * 60) * 10) / 10; // 保留一位小數
            } else {
                // 跨天請假
                let totalHours = 0;

                // 第一天：從開始時間到下班時間
                const firstDayEnd = new Date(`${startDate}T18:00`); // 假設下班時間是18:00
                if (start < firstDayEnd) {
                    totalHours += (firstDayEnd.getTime() - start.getTime()) / (1000 * 60 * 60);
                }

                // 中間的完整天數
                const startDateObj = new Date(startDate);
                const endDateObj = new Date(endDate);
                const daysDiff = Math.floor((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 3600 * 24));

                if (daysDiff > 1) {
                    totalHours += (daysDiff - 1) * 8; // 假設每天工作8小時
                }

                // 最後一天：從上班時間到結束時間
                const lastDayStart = new Date(`${endDate}T09:00`); // 假設上班時間是09:00
                if (end > lastDayStart) {
                    totalHours += (end.getTime() - lastDayStart.getTime()) / (1000 * 60 * 60);
                }

                return Math.round(totalHours * 10) / 10; // 保留一位小數
            }
        }

        // 格式化日期時間
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }



        // 表單提交
        document.getElementById('overtimeForm').addEventListener('submit', async(e) => {
            e.preventDefault();

            // 獲取當前登錄員工ID
            const currentEmployeeId = await getCurrentEmployeeId();
            if (!currentEmployeeId) {
                alert('無法獲取員工資訊，請重新登錄');
                return;
            }

            const formData = {
                employee_id: currentEmployeeId,
                overtime_date: document.getElementById('overtimeDate').value,
                start_time: document.getElementById('overtimeStart').value,
                end_time: document.getElementById('overtimeEnd').value,
                overtime_type: document.getElementById('overtimeType').value,
                reason: document.getElementById('overtimeReason').value
            };

            if (!formData.overtime_date || !formData.start_time || !formData.end_time || !formData.overtime_type) {
                alert('請填寫所有必填欄位');
                return;
            }

            try {
                const response = await fetch('/api/overtime/requests', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification('加班申請提交成功！', 'success');
                    hideOvertimeModal();

                    // 直接重新載入頁面，確保數據更新
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    const error = await response.json();
                    showNotification('提交失敗：' + error.error, 'error');
                }
            } catch (error) {
                console.error('提交失敗:', error);
                showNotification('提交失敗，請重試', 'error');
            }
        });

        document.getElementById('leaveForm').addEventListener('submit', async(e) => {
            e.preventDefault();

            // 獲取當前登錄員工ID
            const currentEmployeeId = await getCurrentEmployeeId();
            if (!currentEmployeeId) {
                alert('無法獲取員工資訊，請重新登錄');
                return;
            }

            const timeType = document.querySelector('input[name="leaveTimeType"]:checked').value;
            const formData = {
                employee_id: currentEmployeeId,
                leave_type: document.getElementById('leaveType').value,
                start_date: document.getElementById('leaveStart').value,
                end_date: document.getElementById('leaveEnd').value,
                reason: document.getElementById('leaveReason').value,
                time_type: timeType,
                substitute_id: document.getElementById('leaveSubstitute').value
            };

            // 如果是部分工時，添加時間欄位
            if (timeType === 'partial_day') {
                formData.start_time = document.getElementById('leaveStartTime').value;
                formData.end_time = document.getElementById('leaveEndTime').value;

                if (!formData.start_time || !formData.end_time) {
                    alert('請填寫開始時間和結束時間');
                    return;
                }
            }

            if (!formData.leave_type || !formData.start_date || !formData.end_date || !formData.substitute_id) {
                alert('請填寫所有必填欄位（包含代理人）');
                return;
            }

            try {
                const response = await fetch('/api/leaves', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('請假申請API響應:', result);

                    showNotification('請假申請提交成功！', 'success');
                    hideLeaveModal();

                    // 直接重新載入頁面，確保數據更新
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    const error = await response.json();
                    showNotification('提交失敗：' + error.error, 'error');
                }
            } catch (error) {
                console.error('提交失敗:', error);
                showNotification('提交失敗，請重試', 'error');
            }
        });

        // 申請記錄相關變數
        let allRequests = [];
        let isDeleting = false; // 防止刪除期間重新載入數據

        // 獲取當前登錄員工ID
        async function getCurrentEmployeeId() {
            try {
                const response = await fetch('/api/auth/verify');
                if (response.ok) {
                    const data = await response.json();
                    return data.user ? data.user.employee_id : null;
                }
                return null;
            } catch (error) {
                console.error('獲取員工ID失敗:', error);
                return null;
            }
        }

        // 載入我的申請記錄
        async function loadMyRequests(forceReload = false) {
            // 如果正在刪除且不是強制重新載入，不要重新載入
            if (isDeleting && !forceReload) {
                console.log('正在刪除中，跳過重新載入');
                return;
            }

            console.log('開始載入申請記錄...');
            try {
                document.getElementById('loadingRequests').style.display = 'block';
                document.getElementById('emptyRequests').style.display = 'none';

                // 獲取當前登錄員工ID
                const currentEmployeeId = await getCurrentEmployeeId();
                if (!currentEmployeeId) {
                    console.error('無法獲取當前員工ID');
                    return;
                }

                // 載入加班申請
                const overtimeResponse = await fetch(`/api/overtime/requests?employee_id=${currentEmployeeId}`);
                const overtimeData = await overtimeResponse.json();

                // 載入請假申請
                const leaveResponse = await fetch(`/api/leaves?employee_id=${currentEmployeeId}`);
                const leaveData = await leaveResponse.json();

                // 合併申請記錄
                allRequests = [];

                if (overtimeData.success && overtimeData.records) {
                    overtimeData.records.forEach(request => {
                        allRequests.push({
                            ...request,
                            type: 'overtime',
                            typeName: '加班申請',
                            icon: 'clock',
                            color: 'orange'
                        });
                    });
                }

                if (leaveData.success && leaveData.records) {
                    leaveData.records.forEach(request => {
                        allRequests.push({
                            ...request,
                            type: 'leave',
                            typeName: '請假申請',
                            icon: 'calendar',
                            color: 'green'
                        });
                    });
                }

                // 過濾：顯示今天往前10天以及未來的所有記錄
                const tenDaysAgo = new Date();
                tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);
                const tenDaysAgoStr = tenDaysAgo.toISOString().split('T')[0]; // YYYY-MM-DD

                allRequests = allRequests.filter(request => {
                    const requestDate = request.overtime_date || request.start_date || request.created_at;
                    if (!requestDate) return false;

                    // 提取日期部分進行比較
                    const requestDateStr = requestDate.split('T')[0]; // 處理 ISO 格式
                    return requestDateStr >= tenDaysAgoStr; // 顯示10天前到未來的所有記錄
                });

                // 按狀態優先排序（待審核在前），然後按日期排序（最新的在前）
                allRequests.sort((a, b) => {
                    // 首先按狀態排序：pending > approved > rejected
                    const statusPriority = {
                        'pending': 3,
                        'approved': 2,
                        'rejected': 1
                    };

                    const statusDiff = (statusPriority[b.status] || 0) - (statusPriority[a.status] || 0);
                    if (statusDiff !== 0) {
                        return statusDiff;
                    }

                    // 狀態相同時，按日期排序（最新的在前）
                    const dateA = new Date(a.overtime_date || a.start_date || a.created_at);
                    const dateB = new Date(b.overtime_date || b.start_date || b.created_at);
                    return dateB - dateA;
                });

                renderRequests();

            } catch (error) {
                console.error('載入申請記錄失敗:', error);
                document.getElementById('loadingRequests').style.display = 'none';
                document.getElementById('emptyRequests').style.display = 'block';
            }
        }

        // 渲染申請記錄
        function renderRequests() {
            const requestsList = document.getElementById('requestsList');
            const requestsCount = document.getElementById('requestsCount');

            document.getElementById('loadingRequests').style.display = 'none';

            if (allRequests.length === 0) {
                document.getElementById('emptyRequests').style.display = 'block';
                requestsList.innerHTML = '';
                requestsCount.textContent = '共 0 筆記錄';
                return;
            }

            document.getElementById('emptyRequests').style.display = 'none';

            // 統計不同狀態的申請數量
            const pendingCount = allRequests.filter(r => r.status === 'pending').length;
            const approvedCount = allRequests.filter(r => r.status === 'approved').length;
            const rejectedCount = allRequests.filter(r => r.status === 'rejected').length;

            requestsCount.innerHTML = `
                <span class="text-gray-700 font-semibold">共 ${allRequests.length} 筆記錄</span>
                <span class="mx-2 text-gray-400">|</span>
                <span class="text-orange-600 font-medium">${pendingCount} 待審</span>
                <span class="mx-1 text-gray-400">•</span>
                <span class="text-emerald-600 font-medium">${approvedCount} 通過</span>
                <span class="mx-1 text-gray-400">•</span>
                <span class="text-red-600 font-medium">${rejectedCount} 拒絕</span>
            `;

            const requestsHtml = allRequests.map(request => createRequestCard(request)).join('');
            requestsList.innerHTML = requestsHtml;
        }

        // 創建申請卡片 - Apple/Google 大廠風格
        function createRequestCard(request) {
            const statusConfig = getStatusConfig(request.status);
            const dateText = getDateText(request);
            const detailText = getDetailText(request);
            const brandIcon = getBrandIcon(request.type);
            const statusIndicator = getStatusIndicator(request.status);

            return `
                <div class="group relative bg-white rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-gray-100/50 border border-gray-100/60 hover:border-gray-200/80 hover:-translate-y-0.5">
                    <!-- 頂部狀態條 -->
                    <div class="absolute top-0 left-0 right-0 h-1 ${statusConfig.accentBar}"></div>
                    
                    <div class="p-5">
                        <div class="flex items-start space-x-4">
                            <!-- 品牌圖標區域 -->
                            <div class="relative flex-shrink-0">
                                <div class="w-12 h-12 ${statusConfig.iconBg} rounded-2xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-300">
                                    ${brandIcon}
                                </div>
                                <!-- 狀態指示器 -->
                                ${statusIndicator}
                            </div>
                            
                            <!-- 主要內容 -->
                            <div class="flex-1 min-w-0">
                                <!-- 標題行 -->
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900 leading-tight">${request.typeName}</h3>
                                    ${(request.status === 'pending' || request.status === 'rejected') ? `
                                        <button onclick="deleteRequest('${request.type}', ${request.id})" 
                                                class="ml-3 p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-xl transition-all duration-200 group/delete">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    ` : ''}
                                </div>
                                
                                <!-- 狀態標籤 -->
                                <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusConfig.badgeClass} mb-3">
                                    ${statusConfig.badgeIcon}
                                    <span class="ml-1.5">${statusConfig.text}</span>
                                </div>
                                
                                <!-- 日期和詳情 -->
                                <div class="space-y-2">
                                    <div class="flex items-center text-sm text-gray-700">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        <span class="font-medium">${dateText}</span>
                                    </div>
                                    
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>${detailText}</span>
                                    </div>
                                    
                                    ${request.reason ? `
                                        <div class="mt-3 p-3 bg-gray-50/70 rounded-xl border border-gray-100">
                                            <div class="flex items-start space-x-2">
                                                <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.001 8.001 0 01-7.748-6M3 12c0-4.418 3.582-8 8-8a8.001 8.001 0 017.748 6"/>
                                                </svg>
                                                <p class="text-sm text-gray-600 leading-relaxed">${request.reason}</p>
                                            </div>
                                        </div>
                                    ` : ''}
                                    
                                    ${request.comment && request.status === 'rejected' ? `
                                        <div class="mt-3 p-3 bg-red-50/70 rounded-xl border border-red-100">
                                            <div class="flex items-start space-x-2">
                                                <svg class="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                <p class="text-sm text-red-700 leading-relaxed font-medium">${request.comment}</p>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                                
                                <!-- 時間戳記 -->
                                <div class="mt-4 pt-3 border-t border-gray-100">
                                    <p class="text-xs text-gray-400 font-medium">
                                        申請時間：${formatDateTime(request.created_at || request.overtime_date || request.start_date)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 獲取申請類型圖標
        function getRequestTypeIcon(type, color) {
            const colorClass = color === 'orange' ? 'text-orange-500' : 
                              color === 'green' ? 'text-green-500' : 
                              'text-blue-500';
            
            if (type === 'overtime') {
                return `<svg class="w-6 h-6 sm:w-7 sm:h-7 ${colorClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>`;
            } else {
                return `<svg class="w-6 h-6 sm:w-7 sm:h-7 ${colorClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>`;
            }
        }

        // 獲取品牌風格圖標 - 現代鮮豔色彩
        function getBrandIcon(type) {
            if (type === 'overtime') {
                return `<svg class="w-7 h-7 text-blue-600 drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>`;
            } else {
                return `<svg class="w-7 h-7 text-emerald-600 drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>`;
            }
        }

        // 獲取狀態指示器 - 現代鮮豔風格
        function getStatusIndicator(status) {
            const configs = {
                'pending': {
                    dot: `<div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-orange-400 to-yellow-500 rounded-full border-2 border-white shadow-lg">
                        <div class="w-full h-full bg-gradient-to-r from-orange-500 to-red-500 rounded-full animate-pulse opacity-80"></div>
                    </div>`
                },
                'approved': {
                    dot: `<div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-emerald-500 to-green-600 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                        <svg class="w-3 h-3 text-white drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>`
                },
                'rejected': {
                    dot: `<div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-600 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                        <svg class="w-3 h-3 text-white drop-shadow-sm" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>`
                }
            };
            return configs[status]?.dot || '';
        }

        // 獲取狀態配置 - 現代鮮豔色彩風格
        function getStatusConfig(status) {
            const configs = {
                'pending': { 
                    text: '待審核', 
                    accentBar: 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500',
                    iconBg: 'bg-gradient-to-br from-blue-500/10 to-indigo-600/20',
                    badgeClass: 'bg-gradient-to-r from-orange-100 to-yellow-100 text-orange-800 border border-orange-300 shadow-sm',
                    badgeIcon: `<div class="w-2.5 h-2.5 bg-gradient-to-r from-orange-400 to-yellow-500 rounded-full animate-pulse shadow-sm"></div>`
                },
                'approved': { 
                    text: '已通過', 
                    accentBar: 'bg-gradient-to-r from-green-400 via-emerald-500 to-teal-600',
                    iconBg: 'bg-gradient-to-br from-emerald-500/10 to-green-600/20',
                    badgeClass: 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border border-emerald-300 shadow-sm',
                    badgeIcon: `<svg class="w-3.5 h-3.5 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>`
                },
                'rejected': { 
                    text: '已拒絕', 
                    accentBar: 'bg-gradient-to-r from-red-500 via-pink-500 to-rose-600',
                    iconBg: 'bg-gradient-to-br from-red-500/10 to-pink-600/20',
                    badgeClass: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-300 shadow-sm',
                    badgeIcon: `<svg class="w-3.5 h-3.5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>`
                }
            };
            return configs[status] || { 
                text: '未知', 
                accentBar: 'bg-gradient-to-r from-gray-400 to-gray-500',
                iconBg: 'bg-gradient-to-br from-gray-500/10 to-gray-600/20',
                badgeClass: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border border-gray-300',
                badgeIcon: `<div class="w-2.5 h-2.5 bg-gray-400 rounded-full"></div>`
            };
        }

        // 獲取日期文字
        function getDateText(request) {
            if (request.type === 'overtime') {
                return `${request.overtime_date}`;
            } else {
                // 請假申請
                if (request.time_type === 'partial_day' && request.start_time && request.end_time) {
                    // 部分工時：顯示具體時間
                    if (request.start_date === request.end_date) {
                        return `${request.start_date} ${request.start_time}-${request.end_time}`;
                    } else {
                        return `${request.start_date} ${request.start_time} 至 ${request.end_date} ${request.end_time}`;
                    }
                } else {
                    // 全天請假
                    return `${request.start_date} 至 ${request.end_date}`;
                }
            }
        }

        // 格式化日期時間
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });
        }



        // 更新申請提交後的回調
        function refreshRequestsAfterSubmit() {
            loadMyRequests();
            loadUserStats();
        }

        // 刪除申請記錄相關變數
        let deleteRequestData = null;

        // 顯示刪除確認對話框
        function deleteRequest(type, id) {
            deleteRequestData = { type, id };
            
            // 設置確認訊息
            const typeName = type === 'overtime' ? '加班申請' : '請假申請';
            document.getElementById('deleteConfirmMessage').textContent = `確定要刪除這筆${typeName}記錄嗎？`;
            
            // 顯示確認對話框
            const modal = document.getElementById('deleteConfirmModal');
            const content = document.getElementById('deleteConfirmContent');
            
            modal.classList.remove('hidden');
            
            // 添加進入動畫
            setTimeout(() => {
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        // 隱藏刪除確認對話框
        function hideDeleteConfirm() {
            const modal = document.getElementById('deleteConfirmModal');
            const content = document.getElementById('deleteConfirmContent');
            
            // 添加退出動畫
            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                deleteRequestData = null;
            }, 300);
        }

        // 確認刪除
        async function confirmDelete() {
            if (!deleteRequestData) return;

            const { type, id } = deleteRequestData;
            
            // 設置刪除狀態，防止重新載入
            isDeleting = true;
            
            // 隱藏確認對話框
            hideDeleteConfirm();

            try {
                let response;
                if (type === 'overtime') {
                    response = await fetch(`/api/overtime/requests/${id}`, {
                        method: 'DELETE'
                    });
                } else if (type === 'leave') {
                    response = await fetch(`/api/leaves/${id}`, {
                        method: 'DELETE'
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        // 立即從畫面移除該項目
                        removeRequestFromUI(id);
                        
                        // 顯示簡潔的成功通知
                        showNotification('已刪除', 'success');
                    } else {
                        showNotification('刪除失敗: ' + (result.error || '未知錯誤'), 'error');
                    }
                } else {
                    // HTTP錯誤狀態
                    const errorData = await response.json().catch(() => ({}));
                    showNotification('刪除失敗: ' + (errorData.error || `HTTP ${response.status}`), 'error');
                }
            } catch (error) {
                console.error('刪除申請記錄失敗:', error);
                showNotification('刪除失敗: 網路錯誤', 'error');
            } finally {
                // 重置刪除狀態
                setTimeout(() => {
                    isDeleting = false;
                    console.log('刪除狀態已重置');
                }, 1000);
            }
        }



        // 從UI中移除申請記錄 - 修復版本
        function removeRequestFromUI(id) {
            const numericId = parseInt(id);
            console.log('準備移除記錄 ID:', numericId);
            
            // 方法1：直接從DOM移除（立即生效）
            const requestCards = document.querySelectorAll('.group.relative.bg-white.rounded-2xl');
            let removedFromDOM = false;
            
            requestCards.forEach(card => {
                const deleteButton = card.querySelector('button[onclick*="deleteRequest"]');
                if (deleteButton) {
                    const onclickAttr = deleteButton.getAttribute('onclick');
                    if (onclickAttr && onclickAttr.includes(`, ${numericId})`)) {
                        card.style.transition = 'all 0.3s ease';
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(-20px) scale(0.95)';
                        
                        setTimeout(() => {
                            if (card.parentNode) {
                                card.parentNode.removeChild(card);
                            }
                        }, 300);
                        
                        removedFromDOM = true;
                        console.log('已從DOM移除記錄:', numericId);
                    }
                }
            });
            
            // 方法2：從陣列移除（防止重新載入時再次出現）
            const originalLength = allRequests.length;
            allRequests = allRequests.filter(request => request.id !== numericId);
            console.log(`從陣列移除記錄 ${numericId}，移除前: ${originalLength}，移除後: ${allRequests.length}`);
            
            // 方法3：如果DOM移除失敗，強制重新渲染
            if (!removedFromDOM) {
                console.log('DOM移除失敗，強制重新渲染');
                renderRequests();
            }
            
            // 更新計數器
            renderRequests();
            
            // 檢查是否還有記錄
            if (allRequests.length === 0) {
                document.getElementById('emptyRequests').style.display = 'block';
                document.getElementById('requestsList').innerHTML = '';
            }
        }

        // 顯示通知訊息
        function showNotification(message, type = 'info') {
            // 創建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-3 py-2 rounded-lg text-white z-50 transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                'bg-blue-500'
            }`;
            
            // 添加圖標和文字
            const icon = type === 'success' ? 'fas fa-check' : 
                        type === 'error' ? 'fas fa-times' : 
                        'fas fa-info';
            
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="${icon}"></i>
                    <span class="text-sm font-medium">${message}</span>
                </div>
            `;
            
            // 添加到頁面
            document.body.appendChild(notification);
            
            // 動畫進入
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 10);
            
            // 2秒後自動移除
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 2000);
        }

        // ===== 用戶認證和資訊管理 =====
        
        // 載入用戶資訊
        async function loadUserInfo() {
            try {
                const response = await fetch('/api/auth/verify');
                if (response.ok) {
                    const result = await response.json();
                    if (result.authenticated && result.user) {
                        const user = result.user;
                        
                        // 更新用戶資訊顯示
                        document.getElementById('userName').textContent = user.employee_name || '未知用戶';
                        document.getElementById('userInfo').textContent = 
                            `${user.employee_code || 'N/A'} • ${user.department_name || '未分配部門'}`;
                        
                        // 儲存用戶資訊到全域變數
                        window.currentUser = user;
                    } else {
                        // 認證失敗，跳轉到登錄頁面
                        window.location.href = '/user';
                    }
                } else {
                    // 認證失敗，跳轉到登錄頁面
                    window.location.href = '/user';
                }
            } catch (error) {
                console.error('載入用戶資訊失敗:', error);
                // 網路錯誤，跳轉到登錄頁面
                window.location.href = '/user';
            }
        }

        // 用戶選單控制
        function initUserMenu() {
            const userMenuButton = document.getElementById('userMenuButton');
            const userMenu = document.getElementById('userMenu');
            
            // 點擊用戶頭像顯示/隱藏選單
            userMenuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
            
            // 點擊其他地方隱藏選單
            document.addEventListener('click', function(e) {
                if (!userMenu.contains(e.target) && !userMenuButton.contains(e.target)) {
                    userMenu.classList.add('hidden');
                }
            });
        }

        // 登出功能
        async function logout() {
            try {
                // 隱藏用戶選單
                document.getElementById('userMenu').classList.add('hidden');
                
                // 顯示載入狀態
                showNotification('正在登出...', 'info');
                
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    showNotification('登出成功', 'success');
                    
                    // 延遲跳轉，讓用戶看到成功訊息
                    setTimeout(() => {
                        window.location.href = '/user';
                    }, 1000);
                } else {
                    const result = await response.json();
                    showNotification('登出失敗: ' + (result.error || '未知錯誤'), 'error');
                }
            } catch (error) {
                console.error('登出錯誤:', error);
                showNotification('登出失敗: 網路錯誤', 'error');
            }
        }

        // 修改密碼功能（暫時顯示提示）
        function changePassword() {
            // 隱藏用戶選單
            document.getElementById('userMenu').classList.add('hidden');
            
            // 暫時顯示提示訊息
            showNotification('修改密碼功能開發中...', 'info');
            
            // TODO: 實現修改密碼模態框
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 載入用戶資訊
            loadUserInfo();
            
            // 初始化用戶選單
            initUserMenu();
            
            // 初始化圖標
            lucide.createIcons();
            
            // 載入用戶統計和權限檢查
            loadUserStats();
            checkClockPermission();
            
            // 載入申請記錄
            loadMyRequests();
        });
    </script>
</body>

</html>