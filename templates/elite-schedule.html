<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e5e7eb;
        }
        
        .calendar-cell {
            background: white;
            min-height: 120px;
            padding: 8px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .calendar-cell:hover {
            background: #f8fafc;
        }
        
        .calendar-cell.today {
            background: #eff6ff;
            border: 2px solid #3b82f6;
        }
        
        .shift-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin: 1px 0;
            display: block;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .shift-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .morning-shift {
            background: #dbeafe;
            color: #1e40af;
            border-left: 3px solid #3b82f6;
        }
        
        .evening-shift {
            background: #fef3c7;
            color: #92400e;
            border-left: 3px solid #f59e0b;
        }
        
        .night-shift {
            background: #f3e8ff;
            color: #7c2d12;
            border-left: 3px solid #8b5cf6;
        }
        
        .standard-shift {
            background: #ecfdf5;
            color: #065f46;
            border-left: 3px solid #10b981;
        }
        
        .flexible-shift {
            background: #fdf2f8;
            color: #9d174d;
            border-left: 3px solid #ec4899;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">排班管理</h1>
            <p class="text-gray-600">智能排班管理，優化人力資源配置</p>
        </div>

        <!-- 操作工具欄 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <select id="monthSelect" class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <!-- 動態生成月份選項 -->
                    </select>

                    <select id="departmentSelect" class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有部門</option>
                        <!-- 動態載入部門選項 -->
                    </select>

                    <select id="employeeSelect" class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有員工</option>
                        <!-- 動態載入員工選項 -->
                    </select>
                </div>

                <div class="flex items-center space-x-3">
                    <button id="addScheduleBtn" class="bg-brand-500 text-white px-4 py-2 rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新增排班</span>
                    </button>

                    <button id="autoScheduleBtn" class="bg-success-500 text-white px-4 py-2 rounded-lg hover:bg-success-600 transition-colors flex items-center space-x-2">
                        <i data-lucide="zap" class="w-4 h-4"></i>
                        <span>自動排班</span>
                    </button>

                    <button id="exportBtn" class="border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>匯出</span>
                    </button>

                    <button id="refreshBtn" class="border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 載入狀態 -->
        <div id="loadingContainer" class="bg-white rounded-2xl p-8 shadow-lg text-center" style="display: none;">
            <div class="loading"></div>
            <p class="mt-4 text-gray-600">正在載入排班數據...</p>
        </div>

        <!-- 排班日曆 -->
        <div id="calendarContainer" class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <!-- 日曆標題 -->
            <div class="bg-gradient-to-r from-brand-500 to-brand-600 text-white p-6">
                <div class="flex items-center justify-between">
                    <h2 id="calendarTitle" class="text-xl font-semibold">排班表</h2>
                    <div class="flex items-center space-x-2">
                        <button id="prevMonth" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                            <i data-lucide="chevron-left" class="w-5 h-5"></i>
                        </button>
                        <button id="nextMonth" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                            <i data-lucide="chevron-right" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 星期標題 -->
            <div class="grid grid-cols-7 bg-gray-50">
                <div class="p-4 text-center font-medium text-gray-700">週日</div>
                <div class="p-4 text-center font-medium text-gray-700">週一</div>
                <div class="p-4 text-center font-medium text-gray-700">週二</div>
                <div class="p-4 text-center font-medium text-gray-700">週三</div>
                <div class="p-4 text-center font-medium text-gray-700">週四</div>
                <div class="p-4 text-center font-medium text-gray-700">週五</div>
                <div class="p-4 text-center font-medium text-gray-700">週六</div>
            </div>

            <!-- 日曆網格 -->
            <div id="calendarGrid" class="calendar-grid">
                <!-- 動態生成日曆格子 -->
            </div>
        </div>

        <!-- 統計資訊 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-brand-600"></i>
                    </div>
                    <div>
                        <p id="totalEmployees" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">排班人數</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-success-600"></i>
                    </div>
                    <div>
                        <p id="totalHours" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">總工作時數</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar" class="w-6 h-6 text-warning-600"></i>
                    </div>
                    <div>
                        <p id="totalSchedules" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">排班記錄</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="alert-circle" class="w-6 h-6 text-error-600"></i>
                    </div>
                    <div>
                        <p id="conflicts" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">排班衝突</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/編輯排班模態框 -->
    <div id="scheduleModal" class="modal">
        <div class="bg-white rounded-2xl p-6 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">新增排班</h3>
                <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="scheduleForm">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">日期</label>
                        <input type="date" id="scheduleDate" required class="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">員工</label>
                        <select id="scheduleEmployee" required class="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option value="">選擇員工</option>
                            <!-- 動態載入員工選項 -->
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">班別</label>
                        <select id="scheduleShift" required class="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option value="">選擇班別</option>
                            <!-- 動態載入班別選項 -->
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">備註</label>
                        <textarea id="scheduleNote" rows="3" class="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent" placeholder="選填..."></textarea>
                    </div>
                </div>

                <div class="flex space-x-3 mt-6">
                    <button type="submit" class="flex-1 bg-brand-500 text-white py-2 px-4 rounded-lg hover:bg-brand-600 transition-colors">
                        <span id="submitText">保存</span>
                    </button>
                    <button type="button" id="cancelBtn" class="flex-1 border border-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 排班詳情模態框 -->
    <div id="scheduleDetailModal" class="modal">
        <div class="bg-white rounded-2xl p-6 max-w-lg w-full mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">排班詳情</h3>
                <button id="closeDetailModal" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div id="scheduleDetailContent">
                <!-- 動態載入排班詳情 -->
            </div>

            <div class="flex space-x-3 mt-6">
                <button id="editScheduleBtn" class="bg-brand-500 text-white py-2 px-4 rounded-lg hover:bg-brand-600 transition-colors">
                    編輯
                </button>
                <button id="deleteScheduleBtn" class="bg-error-500 text-white py-2 px-4 rounded-lg hover:bg-error-600 transition-colors">
                    刪除
                </button>
                <button id="closeDetailBtn" class="border border-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors">
                    關閉
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全域變數
        let currentDate = new Date();
        let employees = [];
        let shifts = [];
        let schedules = [];
        let selectedSchedule = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            initializeCalendar();
            loadInitialData();
            bindEvents();
        });

        // 綁定事件
        function bindEvents() {
            // 月份切換
            document.getElementById('prevMonth').addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() - 1);
                loadCalendar();
            });

            document.getElementById('nextMonth').addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() + 1);
                loadCalendar();
            });

            // 篩選變更
            document.getElementById('monthSelect').addEventListener('change', (e) => {
                const [year, month] = e.target.value.split('-');
                currentDate.setFullYear(parseInt(year));
                currentDate.setMonth(parseInt(month) - 1);
                loadCalendar();
            });

            document.getElementById('departmentSelect').addEventListener('change', loadCalendar);
            document.getElementById('employeeSelect').addEventListener('change', loadCalendar);

            // 按鈕事件
            document.getElementById('addScheduleBtn').addEventListener('click', openScheduleModal);
            document.getElementById('refreshBtn').addEventListener('click', loadCalendar);
            document.getElementById('closeModal').addEventListener('click', closeScheduleModal);
            document.getElementById('cancelBtn').addEventListener('click', closeScheduleModal);
            document.getElementById('closeDetailModal').addEventListener('click', closeDetailModal);
            document.getElementById('closeDetailBtn').addEventListener('click', closeDetailModal);

            // 表單提交
            document.getElementById('scheduleForm').addEventListener('submit', saveSchedule);
        }

        // 初始化日曆
        function initializeCalendar() {
            // 生成月份選項
            const monthSelect = document.getElementById('monthSelect');
            const now = new Date();

            for (let i = -6; i <= 6; i++) {
                const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
                const option = document.createElement('option');
                option.value = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                option.textContent = `${date.getFullYear()}年${date.getMonth() + 1}月`;
                if (i === 0) option.selected = true;
                monthSelect.appendChild(option);
            }
        }

        // 載入初始數據
        async function loadInitialData() {
            await Promise.all([
                loadDepartments(),
                loadEmployees(),
                loadShifts(),
                loadCalendar()
            ]);
        }

        // 載入部門列表
        async function loadDepartments() {
            try {
                const response = await fetch('/api/departments');
                const data = await response.json();

                const select = document.getElementById('departmentSelect');
                data.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入部門失敗:', error);
            }
        }

        // 載入員工列表
        async function loadEmployees() {
            try {
                const response = await fetch('/api/employees');
                const data = await response.json();
                employees = data.employees || data;

                const selects = [
                    document.getElementById('employeeSelect'),
                    document.getElementById('scheduleEmployee')
                ];

                selects.forEach(select => {
                    // 清除現有選項（保留第一個預設選項）
                    while (select.children.length > 1) {
                        select.removeChild(select.lastChild);
                    }

                    employees.forEach(emp => {
                        const option = document.createElement('option');
                        option.value = emp.id;
                        option.textContent = `${emp.name} (${emp.employee_id})`;
                        select.appendChild(option);
                    });
                });
            } catch (error) {
                console.error('載入員工失敗:', error);
            }
        }

        // 載入班別列表
        async function loadShifts() {
            try {
                const response = await fetch('/api/shifts');
                const data = await response.json();
                shifts = data.shifts || data;

                const select = document.getElementById('scheduleShift');
                // 清除現有選項（保留第一個預設選項）
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }

                shifts.forEach(shift => {
                    const option = document.createElement('option');
                    option.value = shift.id;
                    option.textContent = `${shift.name} (${shift.start_time}-${shift.end_time})`;
                    option.dataset.shift = JSON.stringify(shift);
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入班別失敗:', error);
            }
        }

        // 載入日曆
        async function loadCalendar() {
            showLoading(true);

            try {
                const year = currentDate.getFullYear();
                const month = currentDate.getMonth() + 1;
                const departmentId = document.getElementById('departmentSelect').value;
                const employeeId = document.getElementById('employeeSelect').value;

                let url = `/api/schedules/calendar?year=${year}&month=${month}`;
                if (departmentId) url += `&department_id=${departmentId}`;
                if (employeeId) url += `&employee_id=${employeeId}`;

                const response = await fetch(url);
                const data = await response.json();

                schedules = data.schedules || [];
                renderCalendar();
                updateStatistics();

            } catch (error) {
                console.error('載入日曆失敗:', error);
                showError('載入排班資料失敗');
            } finally {
                showLoading(false);
            }
        }

        // 渲染日曆
        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            // 更新標題
            document.getElementById('calendarTitle').textContent =
                `${year}年${month + 1}月排班表`;

            // 計算日曆數據
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            const grid = document.getElementById('calendarGrid');
            grid.innerHTML = '';

            // 生成42個格子（6週）
            for (let i = 0; i < 42; i++) {
                const cellDate = new Date(startDate);
                cellDate.setDate(startDate.getDate() + i);

                const cell = createCalendarCell(cellDate, month);
                grid.appendChild(cell);
            }
        }

        // 創建日曆格子
        function createCalendarCell(date, currentMonth) {
            const cell = document.createElement('div');
            cell.className = 'calendar-cell';

            const day = date.getDate();
            const isCurrentMonth = date.getMonth() === currentMonth;
            const isToday = isToday_(date);

            if (isToday) cell.classList.add('today');

            // 日期數字
            const dayDiv = document.createElement('div');
            dayDiv.className = `text-sm mb-2 ${isCurrentMonth ? 'font-medium text-gray-900' : 'text-gray-400'}`;
            dayDiv.textContent = day;
            cell.appendChild(dayDiv);

            // 排班記錄
            const dateStr = formatDate(date);
            const daySchedules = schedules.filter(s => s.shift_date === dateStr);

            daySchedules.forEach(schedule => {
                const badge = createShiftBadge(schedule);
                cell.appendChild(badge);
            });

            // 點擊事件
            cell.addEventListener('click', () => {
                if (isCurrentMonth) {
                    openScheduleModal(date);
                }
            });

            return cell;
        }

        // 創建班別標籤
        function createShiftBadge(schedule) {
            const badge = document.createElement('span');
            badge.className = 'shift-badge';

            const shift = shifts.find(s => s.id === schedule.shift_id);
            const employee = employees.find(e => e.id === schedule.employee_id);

            if (shift && employee) {
                badge.textContent = `${employee.name} ${shift.name}`;

                // 根據班別類型設定樣式
                const shiftType = getShiftType(shift);
                badge.classList.add(`${shiftType}-shift`);

                badge.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showScheduleDetail(schedule);
                });
            }

            return badge;
        }

        // 獲取班別類型
        function getShiftType(shift) {
            if (shift.code === 'STANDARD_DAY') return 'standard';
            if (shift.code === 'MORNING') return 'morning';
            if (shift.code === 'EVENING') return 'evening';
            if (shift.code === 'NIGHT') return 'night';
            if (shift.code === 'FLEXIBLE') return 'flexible';
            return 'standard';
        }

        // 打開排班模態框
        function openScheduleModal(date = null) {
            const modal = document.getElementById('scheduleModal');
            const dateInput = document.getElementById('scheduleDate');

            if (date) {
                dateInput.value = formatDate(date);
            } else {
                dateInput.value = formatDate(new Date());
            }

            // 重置表單
            document.getElementById('scheduleForm').reset();
            dateInput.value = dateInput.value; // 保持日期值

            modal.classList.add('active');
        }

        // 關閉排班模態框
        function closeScheduleModal() {
            const modal = document.getElementById('scheduleModal');
            modal.classList.remove('active');
            selectedSchedule = null;
        }

        // 保存排班
        async function saveSchedule(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const scheduleData = {
                shift_date: document.getElementById('scheduleDate').value,
                employee_id: parseInt(document.getElementById('scheduleEmployee').value),
                shift_id: parseInt(document.getElementById('scheduleShift').value),
                note: document.getElementById('scheduleNote').value || null,
                status: 'scheduled'
            };

            try {
                const method = selectedSchedule ? 'PUT' : 'POST';
                const url = selectedSchedule ?
                    `/api/schedules/${selectedSchedule.id}` :
                    '/api/schedules';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(scheduleData)
                });

                if (response.ok) {
                    closeScheduleModal();
                    loadCalendar();
                    showSuccess(selectedSchedule ? '排班已更新' : '排班已新增');
                } else {
                    const error = await response.json();
                    showError(error.error || '操作失敗');
                }
            } catch (error) {
                console.error('保存排班失敗:', error);
                showError('保存失敗');
            }
        }

        // 顯示排班詳情
        function showScheduleDetail(schedule) {
            selectedSchedule = schedule;
            const modal = document.getElementById('scheduleDetailModal');
            const content = document.getElementById('scheduleDetailContent');

            const shift = shifts.find(s => s.id === schedule.shift_id);
            const employee = employees.find(e => e.id === schedule.employee_id);

            content.innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">日期</label>
                            <p class="text-gray-900">${formatDisplayDate(schedule.shift_date)}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">狀態</label>
                            <p class="text-gray-900">${getStatusText(schedule.status)}</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">員工</label>
                        <p class="text-gray-900">${employee ? `${employee.name} (${employee.employee_id})` : '未知'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">班別</label>
                        <p class="text-gray-900">${shift ? `${shift.name} (${shift.start_time}-${shift.end_time})` : '未知'}</p>
                    </div>
                    ${schedule.note ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-500">備註</label>
                        <p class="text-gray-900">${schedule.note}</p>
                    </div>
                    ` : ''}
                </div>
            `;
            
            modal.classList.add('active');
        }

        // 關閉詳情模態框
        function closeDetailModal() {
            const modal = document.getElementById('scheduleDetailModal');
            modal.classList.remove('active');
            selectedSchedule = null;
        }

        // 更新統計資訊
        function updateStatistics() {
            // 計算統計數據
            const uniqueEmployees = new Set(schedules.map(s => s.employee_id)).size;
            const totalSchedules = schedules.length;
            
            // 計算總工時（簡化計算）
            let totalHours = 0;
            schedules.forEach(schedule => {
                const shift = shifts.find(s => s.id === schedule.shift_id);
                if (shift) {
                    // 簡化的工時計算
                    const startTime = parseTime(shift.start_time);
                    const endTime = parseTime(shift.end_time);
                    let hours = endTime - startTime;
                    if (hours < 0) hours += 24; // 跨日班別
                    totalHours += hours;
                }
            });
            
            // 更新顯示
            document.getElementById('totalEmployees').textContent = uniqueEmployees;
            document.getElementById('totalSchedules').textContent = totalSchedules;
            document.getElementById('totalHours').textContent = Math.round(totalHours);
            document.getElementById('conflicts').textContent = '0'; // TODO: 實作衝突檢測
        }

        // 工具函數
        function isToday_(date) {
            const today = new Date();
            return date.toDateString() === today.toDateString();
        }

        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function formatDisplayDate(dateString) {
            const date = new Date(dateString);
            return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
        }

        function parseTime(timeString) {
            const [hours, minutes] = timeString.split(':').map(Number);
            return hours + minutes / 60;
        }

        function getStatusText(status) {
            const statusMap = {
                'scheduled': '已排班',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        function showLoading(show) {
            const loading = document.getElementById('loadingContainer');
            const calendar = document.getElementById('calendarContainer');
            
            if (show) {
                loading.style.display = 'block';
                calendar.style.display = 'none';
            } else {
                loading.style.display = 'none';
                calendar.style.display = 'block';
            }
        }

        function showSuccess(message) {
            // TODO: 實作成功訊息顯示
            alert(message);
        }

        function showError(message) {
            // TODO: 實作錯誤訊息顯示
            alert('錯誤: ' + message);
        }
    </script>
</body>

</html>