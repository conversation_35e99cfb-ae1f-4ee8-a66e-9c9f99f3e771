/**
 * 按鈕組件系統 - Han AttendanceOS v2005.6.12
 * 遠漢科技考勤系統統一按鈕規範
 * 
 * 設計原則：
 * - 一致性：統一的視覺風格和交互行為
 * - 可訪問性：符合無障礙設計標準
 * - 層次性：清晰的重要性層級
 * - 響應性：適配各種設備和狀態
 */


/* ========== 基礎按鈕樣式 ========== */

.btn,
.button:not([style*="display"]) {
    /* 基礎屬性 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    /* 字體設定 */
    font-family: var(--font-sans);
    font-weight: var(--font-medium);
    text-decoration: none;
    text-align: center;
    white-space: nowrap;
    /* 邊框和圓角 */
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    /* 過渡效果 */
    transition: all var(--transition-fast);
    /* 游標和選擇 */
    cursor: pointer;
    user-select: none;
    /* 焦點樣式 */
    outline: none;
    /* 防止雙擊選中 */
    -webkit-tap-highlight-color: transparent;
}

.btn:focus,
.button:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

.btn:disabled,
.button:disabled {
    opacity: var(--opacity-50);
    cursor: not-allowed;
    pointer-events: none;
}


/* ========== 按鈕尺寸系統 ========== */


/* 超小尺寸 */

.btn-xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-xs);
    line-height: var(--leading-tight);
    border-radius: var(--radius-sm);
    min-height: 1.5rem;
}


/* 小尺寸 */

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    line-height: var(--leading-tight);
    border-radius: var(--radius-md);
    min-height: 2rem;
}


/* 標準尺寸 */

.btn-md,
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    border-radius: var(--radius-lg);
    min-height: 2.5rem;
}


/* 大尺寸 */

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-lg);
    line-height: var(--leading-normal);
    border-radius: var(--radius-xl);
    min-height: 3rem;
}


/* 超大尺寸 */

.btn-xl {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--text-xl);
    line-height: var(--leading-normal);
    border-radius: var(--radius-xl);
    min-height: 3.5rem;
}


/* ========== 主要按鈕樣式 ========== */


/* 主要按鈕 - 最重要的操作 */

.btn-primary {
    background: var(--gradient-primary);
    color: var(--color-white);
    border-color: var(--color-primary-600);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--color-primary-700);
    border-color: var(--color-primary-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-primary:active {
    background: var(--color-primary-800);
    border-color: var(--color-primary-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* 次要按鈕 - 重要但非主要操作 */

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--color-white);
    border-color: var(--color-secondary-600);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--color-secondary-700);
    border-color: var(--color-secondary-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary:active {
    background: var(--color-secondary-800);
    border-color: var(--color-secondary-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* 成功按鈕 - 確認和完成操作 */

.btn-success {
    background: var(--gradient-success);
    color: var(--color-white);
    border-color: var(--color-success-600);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    background: var(--color-success-700);
    border-color: var(--color-success-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-success:active {
    background: var(--color-success-800);
    border-color: var(--color-success-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* 警告按鈕 - 需要注意的操作 */

.btn-warning {
    background: var(--gradient-warning);
    color: var(--color-white);
    border-color: var(--color-warning-600);
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
    background: var(--color-warning-700);
    border-color: var(--color-warning-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-warning:active {
    background: var(--color-warning-800);
    border-color: var(--color-warning-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* 危險按鈕 - 刪除和危險操作 */

.btn-danger,
.btn-error {
    background: var(--gradient-error);
    color: var(--color-white);
    border-color: var(--color-error-600);
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover,
.btn-error:hover {
    background: var(--color-error-700);
    border-color: var(--color-error-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-danger:active,
.btn-error:active {
    background: var(--color-error-800);
    border-color: var(--color-error-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* 資訊按鈕 - 提示和說明操作 */

.btn-info {
    background: var(--color-info-500);
    color: var(--color-white);
    border-color: var(--color-info-600);
    box-shadow: var(--shadow-sm);
}

.btn-info:hover {
    background: var(--color-info-700);
    border-color: var(--color-info-700);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-info:active {
    background: var(--color-info-800);
    border-color: var(--color-info-800);
    box-shadow: var(--shadow-sm);
    transform: translateY(0);
}


/* ========== 輪廓按鈕樣式 ========== */

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-medium);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--border-dark);
}

.btn-outline-primary {
    background: transparent;
    color: var(--color-primary-600);
    border-color: var(--color-primary-300);
}

.btn-outline-primary:hover {
    background: var(--color-primary-50);
    color: var(--color-primary-700);
    border-color: var(--color-primary-400);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--color-secondary-600);
    border-color: var(--color-secondary-300);
}

.btn-outline-secondary:hover {
    background: var(--color-secondary-50);
    color: var(--color-secondary-700);
    border-color: var(--color-secondary-400);
}

.btn-outline-success {
    background: transparent;
    color: var(--color-success-600);
    border-color: var(--color-success-300);
}

.btn-outline-success:hover {
    background: var(--color-success-50);
    color: var(--color-success-700);
    border-color: var(--color-success-400);
}

.btn-outline-warning {
    background: transparent;
    color: var(--color-warning-600);
    border-color: var(--color-warning-300);
}

.btn-outline-warning:hover {
    background: var(--color-warning-50);
    color: var(--color-warning-700);
    border-color: var(--color-warning-400);
}

.btn-outline-danger {
    background: transparent;
    color: var(--color-error-600);
    border-color: var(--color-error-300);
}

.btn-outline-danger:hover {
    background: var(--color-error-50);
    color: var(--color-error-700);
    border-color: var(--color-error-400);
}


/* ========== 幽靈按鈕樣式 ========== */

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
    box-shadow: none;
}

.btn-ghost:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-ghost:active {
    background: var(--bg-tertiary);
}


/* ========== 鏈接按鈕樣式 ========== */

.btn-link {
    background: transparent;
    color: var(--text-brand);
    border: none;
    box-shadow: none;
    text-decoration: underline;
    text-underline-offset: 2px;
    padding: 0;
    min-height: auto;
}

.btn-link:hover {
    color: var(--color-primary-700);
    text-decoration: underline;
}

.btn-link:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}


/* ========== 特殊按鈕樣式 ========== */


/* 毛玻璃按鈕 */

.btn-glass {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    box-shadow: var(--shadow-lg);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-xl);
}


/* 漸變按鈕 */

.btn-gradient {
    background: var(--gradient-brand);
    color: var(--color-white);
    border: none;
    box-shadow: var(--shadow-lg);
}

.btn-gradient:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}


/* 圓形按鈕 */

.btn-circle {
    border-radius: var(--radius-full);
    padding: var(--spacing-sm);
    width: 2.5rem;
    height: 2.5rem;
    min-height: auto;
}

.btn-circle.btn-sm {
    width: 2rem;
    height: 2rem;
    padding: var(--spacing-xs);
}

.btn-circle.btn-lg {
    width: 3rem;
    height: 3rem;
    padding: var(--spacing-md);
}


/* ========== 按鈕組 ========== */

.btn-group {
    display: inline-flex;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
    position: relative;
    z-index: 1;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-right-width: 1px;
}

.btn-group .btn:hover,
.btn-group .btn:focus {
    z-index: 2;
}


/* ========== 按鈕狀態 ========== */


/* 載入狀態 */

.btn-loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: var(--radius-full);
    animation: btn-spin 0.8s linear infinite;
}

@keyframes btn-spin {
    to {
        transform: rotate(360deg);
    }
}


/* 活躍狀態 */

.btn-active {
    background: var(--color-primary-700);
    color: var(--color-white);
    border-color: var(--color-primary-700);
    box-shadow: var(--shadow-inner);
}


/* ========== 全寬按鈕 ========== */

.btn-block {
    display: flex;
    width: 100%;
}


/* ========== 浮動操作按鈕 ========== */

.btn-fab {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    color: var(--color-white);
    border: none;
    box-shadow: var(--shadow-xl);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.btn-fab:hover {
    box-shadow: var(--shadow-2xl);
    transform: scale(1.1);
}

.btn-fab:active {
    transform: scale(1.05);
}


/* ========== 圖標按鈕 ========== */

.btn-icon {
    padding: var(--spacing-sm);
    min-width: 2.5rem;
    min-height: 2.5rem;
}

.btn-icon.btn-sm {
    padding: var(--spacing-xs);
    min-width: 2rem;
    min-height: 2rem;
}

.btn-icon.btn-lg {
    padding: var(--spacing-md);
    min-width: 3rem;
    min-height: 3rem;
}


/* 圖標和文字組合 */

.btn .icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

.btn.btn-sm .icon {
    width: 0.875rem;
    height: 0.875rem;
}

.btn.btn-lg .icon {
    width: 1.25rem;
    height: 1.25rem;
}


/* ========== 響應式設計 ========== */

@media (max-width: 640px) {
    .btn-fab {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 3rem;
        height: 3rem;
    }
    .btn-group {
        flex-direction: column;
    }
    .btn-group .btn {
        border-radius: 0;
        border-right-width: 1px;
        border-bottom-width: 0;
    }
    .btn-group .btn:first-child {
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    .btn-group .btn:last-child {
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        border-bottom-width: 1px;
    }
}


/* ========== 無障礙支援 ========== */

@media (prefers-reduced-motion: reduce) {
    .btn,
    .button {
        transition: none;
    }
    .btn:hover,
    .button:hover {
        transform: none;
    }
    .btn-fab:hover {
        transform: none;
    }
    @keyframes btn-spin {
        to {
            transform: none;
        }
    }
}


/* 高對比度模式 */

@media (prefers-contrast: high) {
    .btn,
    .button {
        border-width: 2px;
    }
    .btn:focus,
    .button:focus {
        outline-width: 3px;
    }
}


/* ========== 列印樣式 ========== */

@media print {
    .btn,
    .button {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
        box-shadow: none !important;
    }
    .btn-fab {
        display: none !important;
    }
}