/**
 * 請假管理模組
 * 
 * 此模組負責處理請假申請、審批和查詢相關的前端邏輯
 */

// 全局變數
let currentUser = null;
let leavesList = [];
let employeesList = [];
let departmentsList = [];

// DOM元素載入完成後執行
document.addEventListener('DOMContentLoaded', () => {
    // 檢查用戶登入狀態
    checkLoginStatus();

    // 註冊事件監聽器
    registerEventListeners();

    // 初始化日期選擇器
    initDatePickers();
});

/**
 * 檢查用戶登入狀態
 */
function checkLoginStatus() {
    const userInfo = localStorage.getItem('userInfo');
    if (!userInfo) {
        window.location.href = '/login.html';
        return;
    }

    try {
        currentUser = JSON.parse(userInfo);
        document.getElementById('current-user-name').textContent = currentUser.name;

        // 根據用戶角色載入不同的視圖
        if (currentUser.role === 'admin' || currentUser.role === 'manager') {
            document.getElementById('leave-approval-section').classList.remove('hidden');
            loadPendingLeaves();
        }

        loadMyLeaves();
        loadEmployeesList();
        loadDepartmentsList();
    } catch (error) {
        console.error('解析用戶資訊錯誤', error);
        localStorage.removeItem('userInfo');
        window.location.href = '/login.html';
    }
}

/**
 * 註冊事件監聽器
 */
function registerEventListeners() {
    // 請假申請表單提交
    const leaveForm = document.getElementById('leave-application-form');
    if (leaveForm) {
        leaveForm.addEventListener('submit', (e) => {
            e.preventDefault();
            submitLeaveApplication();
        });
    }

    // 請假審批按鈕
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('approve-leave-btn')) {
            const leaveId = e.target.getAttribute('data-leave-id');
            approveLeave(leaveId, 'approved');
        } else if (e.target.classList.contains('reject-leave-btn')) {
            const leaveId = e.target.getAttribute('data-leave-id');
            approveLeave(leaveId, 'rejected');
        }
    });

    // 請假類型變更時更新UI
    const leaveTypeSelect = document.getElementById('leave-type');
    if (leaveTypeSelect) {
        leaveTypeSelect.addEventListener('change', updateLeaveTypeUI);
    }
}

/**
 * 初始化日期選擇器
 */
function initDatePickers() {
    // 如果使用原生日期選擇器，無需額外初始化
    // 如果使用第三方日期選擇器，在這裡初始化
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');

    if (startDateInput && endDateInput) {
        // 設置最小日期為今天
        const today = new Date().toISOString().split('T')[0];
        startDateInput.min = today;
        endDateInput.min = today;

        // 當開始日期變更時，確保結束日期不早於開始日期
        startDateInput.addEventListener('change', () => {
            endDateInput.min = startDateInput.value;
            if (endDateInput.value && new Date(endDateInput.value) < new Date(startDateInput.value)) {
                endDateInput.value = startDateInput.value;
            }
        });
    }
}

/**
 * 載入我的請假記錄
 */
async function loadMyLeaves() {
    try {
        const response = await fetch(`/api/leaves?employee_id=${currentUser.user_id}`);
        if (!response.ok) {
            throw new Error('載入請假記錄失敗');
        }

        const data = await response.json();
        leavesList = data;

        renderMyLeavesList(data);
    } catch (error) {
        console.error('載入請假記錄錯誤', error);
        showNotification('載入請假記錄失敗，請重試', 'error');
    }
}

/**
 * 渲染我的請假列表
 * @param {Array} leaves 請假記錄陣列
 */
function renderMyLeavesList(leaves) {
    const container = document.getElementById('my-leaves-list');
    if (!container) return;

    if (!leaves || leaves.length === 0) {
        container.innerHTML = '<tr><td colspan="5" class="text-center">無請假記錄</td></tr>';
        return;
    }

    const statusLabels = {
        'pending': '<span class="badge bg-warning">審核中</span>',
        'approved': '<span class="badge bg-success">已批准</span>',
        'rejected': '<span class="badge bg-danger">已拒絕</span>'
    };

    const leaveTypeLabels = {
        'annual': '年假',
        'sick': '病假',
        'personal': '事假',
        'maternity': '產假',
        'paternity': '陪產假',
        'wedding': '婚假',
        'funeral': '喪假',
        'other': '其他'
    };

    let html = '';
    leaves.forEach(leave => {
        html += `
        <tr>
            <td>${leave.id}</td>
            <td>${leaveTypeLabels[leave.leave_type] || leave.leave_type}</td>
            <td>${formatDate(leave.start_date)} ~ ${formatDate(leave.end_date)}</td>
            <td>${statusLabels[leave.status] || leave.status}</td>
            <td>${formatDateTime(leave.created_at)}</td>
        </tr>
        `;
    });

    container.innerHTML = html;
}

/**
 * 載入待審批的請假申請
 */
async function loadPendingLeaves() {
    try {
        let url = '/api/leaves?status=pending';

        // 如果是部門主管，只顯示其部門的請假申請
        if (currentUser.role === 'manager') {
            url += `&department_id=${currentUser.department_id}`;
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('載入待審批請假失敗');
        }

        const data = await response.json();
        renderPendingLeavesList(data);
    } catch (error) {
        console.error('載入待審批請假錯誤', error);
        showNotification('載入待審批請假失敗，請重試', 'error');
    }
}

/**
 * 渲染待審批請假列表
 * @param {Array} leaves 請假記錄陣列
 */
function renderPendingLeavesList(leaves) {
    const container = document.getElementById('pending-leaves-list');
    if (!container) return;

    if (!leaves || leaves.length === 0) {
        container.innerHTML = '<tr><td colspan="7" class="text-center">無待審批請假</td></tr>';
        return;
    }

    const leaveTypeLabels = {
        'annual': '年假',
        'sick': '病假',
        'personal': '事假',
        'maternity': '產假',
        'paternity': '陪產假',
        'wedding': '婚假',
        'funeral': '喪假',
        'other': '其他'
    };

    let html = '';
    leaves.forEach(leave => {
        html += `
        <tr>
            <td>${leave.id}</td>
            <td>${leave.employee_name}</td>
            <td>${leave.department_name}</td>
            <td>${leaveTypeLabels[leave.leave_type] || leave.leave_type}</td>
            <td>${formatDate(leave.start_date)} ~ ${formatDate(leave.end_date)}</td>
            <td>${leave.reason || '-'}</td>
            <td>
                <button class="btn btn-sm btn-success approve-leave-btn" data-leave-id="${leave.id}">批准</button>
                <button class="btn btn-sm btn-danger reject-leave-btn" data-leave-id="${leave.id}">拒絕</button>
            </td>
        </tr>
        `;
    });

    container.innerHTML = html;
}

/**
 * 載入員工列表
 */
async function loadEmployeesList() {
    try {
        const response = await fetch('/api/employees');
        if (!response.ok) {
            throw new Error('載入員工列表失敗');
        }

        const data = await response.json();
        employeesList = data;
    } catch (error) {
        console.error('載入員工列表錯誤', error);
    }
}

/**
 * 載入部門列表
 */
async function loadDepartmentsList() {
    try {
        const response = await fetch('/api/departments');
        if (!response.ok) {
            throw new Error('載入部門列表失敗');
        }

        const data = await response.json();
        departmentsList = data;
    } catch (error) {
        console.error('載入部門列表錯誤', error);
    }
}

/**
 * 提交請假申請
 */
async function submitLeaveApplication() {
    const form = document.getElementById('leave-application-form');
    const startDate = form.querySelector('#start-date').value;
    const endDate = form.querySelector('#end-date').value;
    const leaveType = form.querySelector('#leave-type').value;
    const reason = form.querySelector('#leave-reason').value;

    if (!startDate || !endDate || !leaveType) {
        showNotification('請填寫所有必填欄位', 'warning');
        return;
    }

    // 驗證日期
    if (new Date(startDate) > new Date(endDate)) {
        showNotification('結束日期不能早於開始日期', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/leaves', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                employee_id: currentUser.user_id,
                leave_type: leaveType,
                start_date: startDate,
                end_date: endDate,
                reason: reason,
                status: 'pending'
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '請假申請提交失敗');
        }

        const result = await response.json();
        showNotification('請假申請提交成功，等待審批', 'success');

        // 重置表單
        form.reset();

        // 重新載入請假列表
        loadMyLeaves();

    } catch (error) {
        console.error('提交請假申請錯誤', error);
        showNotification('請假申請提交失敗: ' + error.message, 'error');
    }
}

/**
 * 審批請假申請
 * @param {number} leaveId 請假記錄ID
 * @param {string} status 審批狀態 (approved, rejected)
 */
async function approveLeave(leaveId, status) {
    try {
        const response = await fetch(`/api/leaves/${leaveId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: status,
                approver_id: currentUser.user_id
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '請假審批失敗');
        }

        const result = await response.json();
        showNotification(`請假申請已${status === 'approved' ? '批准' : '拒絕'}`, 'success');

        // 重新載入待審批列表
        loadPendingLeaves();

    } catch (error) {
        console.error('審批請假錯誤', error);
        showNotification('請假審批失敗: ' + error.message, 'error');
    }
}

/**
 * 根據請假類型更新UI
 */
function updateLeaveTypeUI() {
    const leaveType = document.getElementById('leave-type').value;
    const reasonField = document.getElementById('leave-reason-field');

    // 針對不同的請假類型顯示不同的表單欄位
    switch (leaveType) {
        case 'sick':
            reasonField.querySelector('label').textContent = '病假原因及症狀';
            reasonField.classList.remove('hidden');
            break;
        case 'personal':
            reasonField.querySelector('label').textContent = '事假原因';
            reasonField.classList.remove('hidden');
            break;
        case 'other':
            reasonField.querySelector('label').textContent = '請假原因（請詳細說明）';
            reasonField.classList.remove('hidden');
            break;
        case 'annual':
            reasonField.classList.add('hidden');
            break;
        default:
            reasonField.querySelector('label').textContent = '請假原因';
            reasonField.classList.remove('hidden');
    }
}

/**
 * 顯示通知消息
 * @param {string} message 消息內容
 * @param {string} type 消息類型 (success, warning, error)
 */
function showNotification(message, type = 'info') {
    // 檢查是否存在通知容器，如果不存在則創建
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '10px';
        notificationContainer.style.right = '10px';
        notificationContainer.style.zIndex = '9999';
        document.body.appendChild(notificationContainer);
    }

    // 創建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.role = 'alert';

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // 將通知添加到容器
    notificationContainer.appendChild(notification);

    // 3秒後自動關閉
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notificationContainer.removeChild(notification);
        }, 150);
    }, 3000);
}

/**
 * 格式化日期
 * @param {string} dateString 日期字串
 * @returns {string} 格式化後的日期字串
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW');
}

/**
 * 格式化日期時間
 * @param {string} dateTimeString 日期時間字串
 * @returns {string} 格式化後的日期時間字串
 */
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return '-';
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-TW');
}