# 智慧考勤系統環境變數配置範本
# 複製此檔案為 .env 並修改相應值

# 資料庫設定
DATABASE_PATH=attendance.db

# 應用程式設定
DEBUG=False
SECRET_KEY=your_secret_key_here_please_change_in_production
PORT=7072

# 檔案上傳設定
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# 日誌設定
LOG_LEVEL=INFO
LOG_FILE=app.log

# API 設定 (可選)
OPENAI_API_KEY=your_openai_key_here

# 安全性設定 (建議在生產環境中設定)
# JWT_SECRET_KEY=your_jwt_secret_key
# BCRYPT_ROUNDS=12

# 資料庫備份設定 (可選)
# BACKUP_SCHEDULE=daily
# BACKUP_RETENTION_DAYS=30

# 郵件設定 (用於通知功能，可選)
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password

# Redis 設定 (用於快取，可選)
# REDIS_URL=redis://localhost:6379/0