"use client"

import Link from 'next/link'
import { Shield, Smartphone, Clock, Users, BarChart3, Settings } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 背景裝飾 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-indigo-300/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-300/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-300/10 rounded-full blur-3xl"></div>
      </div>

      {/* 主要內容 */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-4xl">
          {/* Logo 和標題 */}
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-2xl">
              <Clock className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-neutral-900 mb-4">Han AttendanceOS</h1>
            <p className="text-xl text-neutral-600 mb-2">智慧考勤管理系統</p>
            <p className="text-neutral-500">請選擇您要使用的功能</p>
          </div>

          {/* 功能選擇卡片 */}
          <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {/* 管理後台 */}
            <Link href="/admin">
              <div className="group relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer overflow-hidden">
                {/* 背景裝飾 */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5 group-hover:from-indigo-500/10 group-hover:via-purple-500/10 group-hover:to-pink-500/10 transition-all duration-300"></div>
                <div className="absolute top-4 right-4 w-8 h-8 bg-indigo-100 rounded-full opacity-50 group-hover:opacity-70 transition-opacity"></div>
                <div className="absolute bottom-4 left-4 w-6 h-6 bg-purple-100 rounded-full opacity-30 group-hover:opacity-50 transition-opacity"></div>

                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                    <Shield className="w-8 h-8 text-white" />
                  </div>

                  <h2 className="text-2xl font-bold text-neutral-900 mb-4 text-center">管理後台</h2>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <Users className="w-5 h-5 text-indigo-500" />
                      <span>員工管理與審核</span>
                    </div>
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <BarChart3 className="w-5 h-5 text-indigo-500" />
                      <span>數據分析報表</span>
                    </div>
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <Settings className="w-5 h-5 text-indigo-500" />
                      <span>系統設定管理</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl font-semibold group-hover:from-indigo-600 group-hover:to-purple-700 transition-all">
                      進入管理後台
                      <Shield className="w-4 h-4 ml-2" />
                    </div>
                  </div>
                </div>
              </div>
            </Link>

            {/* 行動版 */}
            <Link href="/m">
              <div className="group relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer overflow-hidden">
                {/* 背景裝飾 */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-blue-500/5 to-teal-500/5 group-hover:from-green-500/10 group-hover:via-blue-500/10 group-hover:to-teal-500/10 transition-all duration-300"></div>
                <div className="absolute top-4 right-4 w-8 h-8 bg-green-100 rounded-full opacity-50 group-hover:opacity-70 transition-opacity"></div>
                <div className="absolute bottom-4 left-4 w-6 h-6 bg-blue-100 rounded-full opacity-30 group-hover:opacity-50 transition-opacity"></div>

                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                    <Smartphone className="w-8 h-8 text-white" />
                  </div>

                  <h2 className="text-2xl font-bold text-neutral-900 mb-4 text-center">員工行動版</h2>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <Clock className="w-5 h-5 text-green-500" />
                      <span>線上打卡功能</span>
                    </div>
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <BarChart3 className="w-5 h-5 text-green-500" />
                      <span>考勤記錄查詢</span>
                    </div>
                    <div className="flex items-center space-x-3 text-neutral-700">
                      <Users className="w-5 h-5 text-green-500" />
                      <span>請假申請提交</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-xl font-semibold group-hover:from-green-600 group-hover:to-blue-700 transition-all">
                      進入行動版
                      <Smartphone className="w-4 h-4 ml-2" />
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* 版權資訊 */}
          <div className="text-center mt-12">
            <p className="text-neutral-500 mb-2">
              © 2024 遠漢科技. Han AttendanceOS v2025.6.12
            </p>
            <p className="text-sm text-neutral-400">
              智慧考勤管理系統 - 提升企業效率的最佳選擇
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
