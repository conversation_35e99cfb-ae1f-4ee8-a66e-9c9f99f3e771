"use client"

import { useEffect } from 'react'

export default function ServiceWorkerRegistration() {
    useEffect(() => {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker
                .register('/sw.js')
                .then((registration) => {
                    console.log('Service Worker 註冊成功:', registration.scope)

                    // 檢查更新
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing
                        if (newWorker) {
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // 有新版本可用
                                    console.log('新版本可用，建議重新載入頁面')

                                    // 可以在這裡顯示更新提示
                                    if (confirm('發現新版本，是否立即更新？')) {
                                        newWorker.postMessage({ type: 'SKIP_WAITING' })
                                        window.location.reload()
                                    }
                                }
                            })
                        }
                    })
                })
                .catch((error) => {
                    console.error('Service Worker 註冊失敗:', error)
                })

            // 監聽Service Worker控制權變更
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                console.log('Service Worker 控制權已變更')
                window.location.reload()
            })
        } else {
            console.log('此瀏覽器不支援 Service Worker')
        }
    }, [])

    return null
} 