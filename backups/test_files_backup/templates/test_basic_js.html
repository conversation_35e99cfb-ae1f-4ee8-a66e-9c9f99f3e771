<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本JavaScript測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>

<body>
    <h1>基本JavaScript測試</h1>

    <div id="results">
        <div class="test-result info">測試開始...</div>
    </div>

    <script>
        console.log('🚀 基本JavaScript測試開始');

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(div);
            console.log(message);
        }

        // 測試1: 基本JavaScript功能
        try {
            addResult('✅ 基本JavaScript執行正常', 'success');
        } catch (error) {
            addResult(`❌ 基本JavaScript執行失敗: ${error.message}`, 'error');
        }

        // 測試2: DOM操作
        try {
            const testDiv = document.createElement('div');
            testDiv.textContent = 'DOM測試';
            addResult('✅ DOM操作正常', 'success');
        } catch (error) {
            addResult(`❌ DOM操作失敗: ${error.message}`, 'error');
        }

        // 測試3: Fetch API
        try {
            if (typeof fetch !== 'undefined') {
                addResult('✅ Fetch API可用', 'success');
            } else {
                addResult('❌ Fetch API不可用', 'error');
            }
        } catch (error) {
            addResult(`❌ Fetch API檢查失敗: ${error.message}`, 'error');
        }

        // 測試4: Promise支援
        try {
            if (typeof Promise !== 'undefined') {
                addResult('✅ Promise支援正常', 'success');
            } else {
                addResult('❌ Promise不支援', 'error');
            }
        } catch (error) {
            addResult(`❌ Promise檢查失敗: ${error.message}`, 'error');
        }

        // 測試5: async/await支援
        try {
            (async function() {
                addResult('✅ async/await支援正常', 'success');
            })();
        } catch (error) {
            addResult(`❌ async/await不支援: ${error.message}`, 'error');
        }

        // 測試6: DOMContentLoaded事件
        document.addEventListener('DOMContentLoaded', function() {
            addResult('✅ DOMContentLoaded事件觸發正常', 'success');

            // 測試7: 簡單的API調用
            setTimeout(async function() {
                try {
                    addResult('🔄 開始測試API調用...', 'info');

                    const response = await fetch('/api/approval/leaves');
                    addResult(`📡 API回應狀態: ${response.status}`, response.ok ? 'success' : 'error');

                    const data = await response.json();
                    addResult(`📊 API數據載入成功，記錄數: ${data.records ? data.records.length : 0}`, 'success');

                    // 測試8: 簡單的DOM更新
                    const testContent = document.createElement('div');
                    testContent.className = 'test-result success';
                    testContent.textContent = `${new Date().toLocaleTimeString()}: ✅ 完整測試流程成功完成`;
                    document.getElementById('results').appendChild(testContent);

                } catch (error) {
                    addResult(`❌ API測試失敗: ${error.message}`, 'error');
                }
            }, 1000);
        });

        addResult('📝 測試腳本載入完成', 'info');
    </script>
</body>

</html>