/**
 * 動畫效果系統 - Han AttendanceOS v2005.6.12
 * 遠漢科技考勤系統統一動畫規範
 * 
 * 設計原則：
 * - 自然流暢：模擬真實世界的物理運動
 * - 性能優先：使用GPU加速的屬性
 * - 用戶友好：支援減少動畫偏好設定
 * - 品牌一致：統一的動畫時長和緩動函數
 */

:root {
    /* ========== 動畫時長系統 ========== */
    --duration-instant: 0ms;
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    --duration-slower: 750ms;
    --duration-slowest: 1000ms;
    /* ========== 緩動函數系統 ========== */
    /* 標準緩動 */
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    /* 自然緩動 */
    --ease-natural: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
    /* 彈性緩動 */
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    /* 品牌緩動 */
    --ease-brand: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-brand-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-brand-out: cubic-bezier(0, 0, 0.2, 1);
}


/* ========== 基礎過渡效果 ========== */

.transition-none {
    transition: none;
}

.transition-all {
    transition: all var(--duration-normal) var(--ease-brand);
}

.transition-colors {
    transition: color var(--duration-fast) var(--ease-brand), background-color var(--duration-fast) var(--ease-brand), border-color var(--duration-fast) var(--ease-brand);
}

.transition-opacity {
    transition: opacity var(--duration-normal) var(--ease-brand);
}

.transition-transform {
    transition: transform var(--duration-normal) var(--ease-brand);
}

.transition-shadow {
    transition: box-shadow var(--duration-normal) var(--ease-brand);
}


/* ========== 淡入淡出動畫 ========== */

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-1rem);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(1rem);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}


/* ========== 縮放動畫 ========== */

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}


/* ========== 滑動動畫 ========== */

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOutUp {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-100%);
    }
}

@keyframes slideOutDown {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}


/* ========== 旋轉動畫 ========== */

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spinReverse {
    from {
        transform: rotate(360deg);
    }
    to {
        transform: rotate(0deg);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

@keyframes rotateOut {
    from {
        opacity: 1;
        transform: rotate(0);
    }
    to {
        opacity: 0;
        transform: rotate(200deg);
    }
}


/* ========== 彈跳動畫 ========== */

@keyframes bounce {
    0%,
    20%,
    53%,
    80%,
    100% {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: translateY(0);
    }
    40%,
    43% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translateY(-1rem);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translateY(-0.5rem);
    }
    90% {
        transform: translateY(-0.25rem);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceOut {
    20% {
        transform: scale(0.9);
    }
    50%,
    55% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 0;
        transform: scale(0.3);
    }
}


/* ========== 脈衝動畫 ========== */

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes pulseOpacity {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}


/* ========== 搖擺動畫 ========== */

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-0.5rem);
    }
    20%,
    40%,
    60%,
    80% {
        transform: translateX(0.5rem);
    }
}

@keyframes wobble {
    0% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
    100% {
        transform: translateX(0%);
    }
}

@keyframes swing {
    20% {
        transform: rotate(15deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(5deg);
    }
    80% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}


/* ========== 載入動畫 ========== */

@keyframes loading {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loadingDots {
    0%,
    80%,
    100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes loadingBars {
    0%,
    40%,
    100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}


/* ========== 動畫類別 ========== */


/* 淡入淡出 */

.animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-brand);
}

.animate-fade-out {
    animation: fadeOut var(--duration-normal) var(--ease-brand);
}

.animate-fade-in-up {
    animation: fadeInUp var(--duration-normal) var(--ease-brand);
}

.animate-fade-in-down {
    animation: fadeInDown var(--duration-normal) var(--ease-brand);
}

.animate-fade-in-left {
    animation: fadeInLeft var(--duration-normal) var(--ease-brand);
}

.animate-fade-in-right {
    animation: fadeInRight var(--duration-normal) var(--ease-brand);
}


/* 縮放 */

.animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-brand);
}

.animate-scale-out {
    animation: scaleOut var(--duration-normal) var(--ease-brand);
}

.animate-zoom-in {
    animation: zoomIn var(--duration-normal) var(--ease-brand);
}

.animate-zoom-out {
    animation: zoomOut var(--duration-normal) var(--ease-brand);
}


/* 滑動 */

.animate-slide-in-up {
    animation: slideInUp var(--duration-normal) var(--ease-brand);
}

.animate-slide-in-down {
    animation: slideInDown var(--duration-normal) var(--ease-brand);
}

.animate-slide-in-left {
    animation: slideInLeft var(--duration-normal) var(--ease-brand);
}

.animate-slide-in-right {
    animation: slideInRight var(--duration-normal) var(--ease-brand);
}

.animate-slide-out-up {
    animation: slideOutUp var(--duration-normal) var(--ease-brand);
}

.animate-slide-out-down {
    animation: slideOutDown var(--duration-normal) var(--ease-brand);
}

.animate-slide-out-left {
    animation: slideOutLeft var(--duration-normal) var(--ease-brand);
}

.animate-slide-out-right {
    animation: slideOutRight var(--duration-normal) var(--ease-brand);
}


/* 旋轉 */

.animate-spin {
    animation: spin var(--duration-slowest) var(--ease-linear) infinite;
}

.animate-spin-reverse {
    animation: spinReverse var(--duration-slowest) var(--ease-linear) infinite;
}

.animate-rotate-in {
    animation: rotateIn var(--duration-normal) var(--ease-brand);
}

.animate-rotate-out {
    animation: rotateOut var(--duration-normal) var(--ease-brand);
}


/* 彈跳 */

.animate-bounce {
    animation: bounce var(--duration-slowest) var(--ease-brand) infinite;
}

.animate-bounce-in {
    animation: bounceIn var(--duration-slow) var(--ease-brand);
}

.animate-bounce-out {
    animation: bounceOut var(--duration-slow) var(--ease-brand);
}


/* 脈衝 */

.animate-pulse {
    animation: pulse var(--duration-slower) var(--ease-brand) infinite;
}

.animate-pulse-opacity {
    animation: pulseOpacity var(--duration-slower) var(--ease-brand) infinite;
}

.animate-heartbeat {
    animation: heartbeat var(--duration-slowest) var(--ease-brand) infinite;
}


/* 搖擺 */

.animate-shake {
    animation: shake var(--duration-slow) var(--ease-brand);
}

.animate-wobble {
    animation: wobble var(--duration-slowest) var(--ease-brand);
}

.animate-swing {
    animation: swing var(--duration-slowest) var(--ease-brand);
}


/* 載入 */

.animate-loading {
    animation: loading var(--duration-slowest) var(--ease-linear) infinite;
}

.animate-loading-dots {
    animation: loadingDots var(--duration-slower) var(--ease-brand) infinite;
}

.animate-loading-bars {
    animation: loadingBars var(--duration-slower) var(--ease-brand) infinite;
}

.animate-progress {
    animation: progress var(--duration-slower) var(--ease-brand) infinite;
}


/* ========== 動畫延遲 ========== */

.animate-delay-75 {
    animation-delay: 75ms;
}

.animate-delay-100 {
    animation-delay: 100ms;
}

.animate-delay-150 {
    animation-delay: 150ms;
}

.animate-delay-200 {
    animation-delay: 200ms;
}

.animate-delay-300 {
    animation-delay: 300ms;
}

.animate-delay-500 {
    animation-delay: 500ms;
}

.animate-delay-700 {
    animation-delay: 700ms;
}

.animate-delay-1000 {
    animation-delay: 1000ms;
}


/* ========== 動畫持續時間 ========== */

.animate-duration-75 {
    animation-duration: 75ms;
}

.animate-duration-100 {
    animation-duration: 100ms;
}

.animate-duration-150 {
    animation-duration: 150ms;
}

.animate-duration-200 {
    animation-duration: 200ms;
}

.animate-duration-300 {
    animation-duration: 300ms;
}

.animate-duration-500 {
    animation-duration: 500ms;
}

.animate-duration-700 {
    animation-duration: 700ms;
}

.animate-duration-1000 {
    animation-duration: 1000ms;
}


/* ========== 懸停效果 ========== */

.hover-lift {
    transition: transform var(--duration-fast) var(--ease-brand);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-grow {
    transition: transform var(--duration-fast) var(--ease-brand);
}

.hover-grow:hover {
    transform: scale(1.05);
}

.hover-shrink {
    transition: transform var(--duration-fast) var(--ease-brand);
}

.hover-shrink:hover {
    transform: scale(0.95);
}

.hover-rotate {
    transition: transform var(--duration-fast) var(--ease-brand);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow var(--duration-fast) var(--ease-brand);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}


/* ========== 焦點效果 ========== */

.focus-ring {
    transition: box-shadow var(--duration-fast) var(--ease-brand);
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focus-scale {
    transition: transform var(--duration-fast) var(--ease-brand);
}

.focus-scale:focus {
    transform: scale(1.02);
}


/* ========== 載入狀態 ========== */

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin var(--duration-slowest) var(--ease-linear) infinite;
}

.loading-dots {
    display: inline-flex;
    gap: 0.25rem;
}

.loading-dots::before,
.loading-dots::after,
.loading-dots {
    content: '';
    width: 0.5rem;
    height: 0.5rem;
    background-color: currentColor;
    border-radius: 50%;
    animation: loadingDots var(--duration-slower) var(--ease-brand) infinite;
}

.loading-dots::before {
    animation-delay: -0.32s;
}

.loading-dots::after {
    animation-delay: -0.16s;
}

.loading-bars {
    display: inline-flex;
    gap: 0.125rem;
    align-items: center;
    height: 1rem;
}

.loading-bars::before,
.loading-bars::after,
.loading-bars {
    content: '';
    width: 0.25rem;
    height: 100%;
    background-color: currentColor;
    animation: loadingBars var(--duration-slower) var(--ease-brand) infinite;
}

.loading-bars::before {
    animation-delay: -0.32s;
}

.loading-bars::after {
    animation-delay: -0.16s;
}


/* ========== 無障礙支援 ========== */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    .animate-spin,
    .animate-spin-reverse,
    .animate-bounce,
    .animate-pulse,
    .animate-pulse-opacity,
    .animate-heartbeat,
    .animate-loading,
    .animate-loading-dots,
    .animate-loading-bars,
    .animate-progress {
        animation: none !important;
    }
    .loading-spinner,
    .loading-dots,
    .loading-bars {
        animation: none !important;
    }
    .hover-lift:hover,
    .hover-grow:hover,
    .hover-shrink:hover,
    .hover-rotate:hover {
        transform: none !important;
    }
}


/* ========== 性能優化 ========== */

.will-change-auto {
    will-change: auto;
}

.will-change-scroll {
    will-change: scroll-position;
}

.will-change-contents {
    will-change: contents;
}

.will-change-transform {
    will-change: transform;
}


/* 強制GPU加速 */

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}


/* ========== 列印樣式 ========== */

@media print {
    *,
    *::before,
    *::after {
        animation: none !important;
        transition: none !important;
    }
}