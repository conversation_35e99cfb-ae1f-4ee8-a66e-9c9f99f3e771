# Han AttendanceOS 工具函數庫

## 概述

這個工具函數庫旨在解決 Han AttendanceOS 系統中的代碼重複問題，提供統一、可重用的工具函數，提升開發效率和代碼質量。

## 📁 目錄結構

```
static/js/utils/
├── index.js              # 統一載入器
├── leave-calculator.js   # 請假時數計算
├── notification.js       # 統一通知系統
├── attendance-helper.js  # 考勤輔助函數
├── form-validator.js     # 表單驗證
└── README.md            # 使用說明
```

## 🚀 快速開始

### 1. 自動載入（推薦）

在 HTML 頁面中引入統一載入器：

```html
<script src="/static/js/utils/index.js"></script>
```

載入器會自動檢測頁面類型並載入相應的工具函數庫。

### 2. 手動載入

```javascript
// 載入單個工具函數庫
await loadUtil('notification');

// 載入多個工具函數庫
await loadUtils(['notification', 'leave-calculator']);

// 載入所有工具函數庫
await UtilsLoader.loadAllUtils();
```

## 📚 工具函數庫詳解

### 1. 通知系統 (notification.js)

統一的通知顯示系統，支援滑動通知、確認對話框等。

#### 基本用法

```javascript
// 顯示成功通知
NotificationSystem.success('操作成功！');

// 顯示錯誤通知
NotificationSystem.error('操作失敗，請重試');

// 顯示警告通知
NotificationSystem.warning('請注意檢查資料');

// 顯示資訊通知
NotificationSystem.info('系統維護通知');
```

#### 高級用法

```javascript
// 自定義通知選項
NotificationSystem.showSlideNotification('自定義訊息', 'success', {
    duration: 5000,        // 顯示時間
    position: 'top-left',  // 顯示位置
    closable: true,        // 是否可關閉
    autoHide: false        // 是否自動隱藏
});

// 顯示確認對話框
const result = await NotificationSystem.showConfirm('確定要刪除嗎？', {
    title: '確認刪除',
    confirmText: '刪除',
    cancelText: '取消',
    type: 'error'
});

if (result) {
    console.log('用戶確認刪除');
}

// 顯示載入狀態
const loading = NotificationSystem.showLoading('處理中...');
// 處理完成後隱藏
NotificationSystem.hideLoading();
```

#### 向後兼容函數

```javascript
// 這些函數仍然可用，會自動調用新的通知系統
showSlideNotification('訊息', 'success');
showSuccess('成功訊息');
showError('錯誤訊息');
showWarning('警告訊息');
showInfo('資訊訊息');
```

### 2. 請假時數計算 (leave-calculator.js)

統一的請假時數計算邏輯，支援全天、部分天、小時制請假。

#### 基本用法

```javascript
// 計算請假時數
const hours = LeaveCalculator.calculateLeaveHours(
    '2025-06-10',  // 開始日期
    '2025-06-12',  // 結束日期
    '09:00',       // 開始時間（可選）
    '17:00',       // 結束時間（可選）
    'full_day'     // 請假類型
);

console.log(`請假時數: ${hours} 小時`);

// 格式化時數顯示
const formatted = LeaveCalculator.formatHours(hours);
console.log(formatted); // "3天"

// 計算請假天數
const days = LeaveCalculator.calculateLeaveDays('2025-06-10', '2025-06-12');
console.log(`請假天數: ${days} 天`);
```

#### 驗證請假時間

```javascript
const validation = LeaveCalculator.validateLeaveTime(
    '2025-06-10',  // 開始日期
    '2025-06-12',  // 結束日期
    '09:00',       // 開始時間
    '17:00'        // 結束時間
);

if (!validation.isValid) {
    console.log('驗證錯誤:', validation.errors);
}
```

#### 請假類型

- `full_day`: 全天請假
- `partial_day`: 部分天請假
- `hourly`: 小時制請假

#### 向後兼容函數

```javascript
// 這些函數仍然可用
calculateLeaveHours(startDate, endDate, startTime, endTime);
formatLeaveHours(hours);
calculateLeaveDays(startDate, endDate);
```

### 3. 考勤輔助函數 (attendance-helper.js)

考勤相關的工具函數，包括時間格式化、狀態判斷、統計計算等。

#### 時間格式化

```javascript
// 格式化日期時間
const formatted = AttendanceHelper.formatDateTime('2025-06-10 14:30:00', 'DISPLAY_TIME');
console.log(formatted); // "14:30"

// 支援的格式
// 'DATE': '2025-06-10'
// 'TIME': '14:30'
// 'DATETIME': '2025-06-10 14:30'
// 'DISPLAY_DATE': '06/10'
// 'DISPLAY_TIME': '14:30'
// 'DISPLAY_DATETIME': '06/10 14:30'
```

#### 考勤狀態處理

```javascript
// 格式化考勤狀態
const statusInfo = AttendanceHelper.formatStatus('late', { 
    showBadge: true,
    showIcon: true 
});

console.log(statusInfo.text);  // "遲到"
console.log(statusInfo.html);  // 包含圖標和樣式的HTML

// 判斷考勤狀態
const record = {
    check_in: '2025-06-10 09:15:00',
    check_out: '2025-06-10 18:00:00',
    scheduled_start: '09:00',
    scheduled_end: '18:00'
};

const status = AttendanceHelper.determineStatus(record);
console.log(status); // "late"
```

#### 工作時數計算

```javascript
// 計算工作時數
const workHours = AttendanceHelper.calculateWorkHours(
    '2025-06-10 09:00:00',  // 上班時間
    '2025-06-10 18:00:00',  // 下班時間
    60                      // 休息時間（分鐘）
);

console.log(workHours); // 8.0

// 格式化工作時數
const formatted = AttendanceHelper.formatWorkHours(workHours);
console.log(formatted); // "8小時"
```

#### 生成考勤摘要

```javascript
const record = {
    check_in: '2025-06-10 09:15:00',
    check_out: '2025-06-10 18:00:00',
    work_date: '2025-06-10'
};

const summary = AttendanceHelper.generateSummary(record);
console.log(summary);
// {
//   status: 'late',
//   workHours: 7.25,
//   workHoursText: '7小時15分',
//   lateMinutes: 15,
//   checkInTime: '09:15',
//   checkOutTime: '18:00',
//   ...
// }
```

#### 創建考勤記錄卡片

```javascript
const cardHtml = AttendanceHelper.createRecordCard(record, {
    showActions: true,
    compact: false
});

document.getElementById('attendance-list').innerHTML = cardHtml;
```

### 4. 表單驗證 (form-validator.js)

統一的表單驗證系統，支援即時驗證、批量驗證等。

#### 基本驗證規則

```javascript
// 驗證單個欄位
const result = FormValidator.validateField('<EMAIL>', 'email', '電子郵件');

if (!result.isValid) {
    console.log('驗證錯誤:', result.errors);
}
```

#### 支援的驗證規則

- `required`: 必填
- `email`: 電子郵件格式
- `phone`: 電話號碼格式
- `idNumber`: 身分證字號格式
- `password`: 密碼長度（至少6位）
- `strongPassword`: 強密碼（包含大小寫字母、數字，至少8位）
- `number`: 數字格式
- `positiveNumber`: 正數
- `integer`: 整數
- `date`: 日期格式
- `time`: 時間格式 (HH:MM)
- `minLength`: 最小長度
- `maxLength`: 最大長度
- `min`: 最小值
- `max`: 最大值
- `employeeId`: 員工ID格式
- `deptCode`: 部門代碼格式

#### 表單驗證

```javascript
// 定義驗證規則
const schema = {
    employee_id: ['required', 'employeeId'],
    name: ['required', { name: 'minLength', params: [2] }],
    email: ['required', 'email'],
    phone: ['phone'],
    salary: ['required', 'positiveNumber']
};

// 驗證表單數據
const formData = {
    employee_id: 'E001',
    name: '張三',
    email: '<EMAIL>',
    phone: '0912345678',
    salary: '50000'
};

const result = FormValidator.validateForm(formData, schema);

if (result.isValid) {
    console.log('表單驗證通過');
} else {
    console.log('驗證錯誤:', result.errors);
}
```

#### 設置即時驗證

```javascript
const form = document.getElementById('employeeForm');
const schema = {
    name: ['required'],
    email: ['required', 'email'],
    phone: ['phone']
};

FormValidator.setupFormValidation(form, schema, {
    validateOnInput: true,   // 輸入時驗證
    validateOnBlur: true,    // 失去焦點時驗證
    onSubmit: (data, result) => {
        console.log('表單提交:', data);
        // 處理表單提交
    }
});
```

#### 自定義驗證規則

```javascript
// 添加自定義驗證規則
FormValidator.addCustomRule('customRule', (value) => {
    return value.includes('特定字符');
}, '必須包含特定字符');

// 使用自定義規則
const result = FormValidator.validateField('測試值', 'customRule');
```

## 🔧 頁面集成

### 在模板中使用

在 HTML 模板的 `<head>` 部分添加：

```html
<!-- 載入工具函數庫 -->
<script src="/static/js/utils/index.js"></script>
```

### 頁面特定初始化

工具函數庫會根據頁面類型自動載入相應的工具：

- **user-dashboard**: notification, leave-calculator, form-validator
- **user-login**: notification, form-validator
- **elite-approval**: notification, leave-calculator
- **elite-attendance**: notification, attendance-helper, form-validator
- **elite-employees**: notification, form-validator
- **elite-shifts**: notification, form-validator
- **elite-reports**: notification, attendance-helper
- **elite-overtime**: notification, form-validator

### 手動指定頁面類型

```html
<body class="page-user-dashboard">
<!-- 或者 -->
<body data-page-type="user-dashboard">
```

## 🎯 最佳實踐

### 1. 使用統一的通知系統

```javascript
// ❌ 避免使用原生 alert/confirm
alert('操作成功');
confirm('確定要刪除嗎？');

// ✅ 使用統一的通知系統
NotificationSystem.success('操作成功');
const result = await NotificationSystem.showConfirm('確定要刪除嗎？');
```

### 2. 使用統一的時間格式化

```javascript
// ❌ 避免重複的時間格式化邏輯
function formatTime(dateTime) {
    const date = new Date(dateTime);
    return `${date.getHours()}:${date.getMinutes()}`;
}

// ✅ 使用統一的格式化函數
const formatted = AttendanceHelper.formatDateTime(dateTime, 'DISPLAY_TIME');
```

### 3. 使用統一的表單驗證

```javascript
// ❌ 避免重複的驗證邏輯
function validateEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

// ✅ 使用統一的驗證系統
const result = FormValidator.validateField(email, 'email');
```

### 4. 使用統一的請假計算

```javascript
// ❌ 避免重複的計算邏輯
function calculateLeaveHours(startDate, endDate) {
    // 複雜的計算邏輯...
}

// ✅ 使用統一的計算函數
const hours = LeaveCalculator.calculateLeaveHours(startDate, endDate);
```

## 🔍 調試和開發

### 檢查載入狀態

```javascript
// 檢查工具函數庫是否已載入
console.log('已載入的工具函數庫:', UtilsLoader.getLoadedUtils());

// 檢查特定工具函數庫
if (UtilsLoader.isLoaded('notification')) {
    console.log('通知系統已載入');
}
```

### 重新載入工具函數庫

```javascript
// 用於開發調試，重新載入工具函數庫
await UtilsLoader.reloadUtil('notification');
```

### 手動初始化

```javascript
// 手動初始化特定頁面類型
await UtilsLoader.initPageUtils('user-dashboard');
```

## 📈 性能優化

1. **按需載入**: 工具函數庫會根據頁面類型自動載入所需的工具
2. **依賴管理**: 自動處理工具函數庫之間的依賴關係
3. **重複載入防護**: 避免重複載入同一個工具函數庫
4. **向後兼容**: 保持與現有代碼的兼容性

## 🚨 注意事項

1. **載入順序**: 確保在使用工具函數之前已經載入完成
2. **全域變數**: 工具函數庫會創建全域變數，避免命名衝突
3. **瀏覽器兼容性**: 支援現代瀏覽器，IE11+ 
4. **錯誤處理**: 工具函數庫包含完整的錯誤處理機制

## 📝 更新日誌

### v2005.6.12
- 初始版本發布
- 包含四個核心工具函數庫
- 支援自動載入和頁面特定初始化
- 完整的向後兼容性支援

## 🤝 貢獻指南

1. 遵循現有的代碼風格和註釋規範
2. 添加新功能時確保向後兼容性
3. 提供完整的 JSDoc 註釋
4. 添加相應的使用示例和測試

## 📞 技術支援

如有問題或建議，請聯繫開發團隊或查看系統文檔。 