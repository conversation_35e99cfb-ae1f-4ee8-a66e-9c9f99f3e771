"""
完整API系統測試腳本

測試所有API模組的載入、整合和基本功能
"""

import sys
import os
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_modules_import():
    """
    測試所有API模組的導入
    
    返回：
    bool: 所有模組是否成功導入
    """
    logger.info("🔍 測試API模組導入...")
    
    try:
        # 測試考勤API模組
        from api.attendance_api import attendance_bp
        logger.info("✅ attendance_api 模組導入成功")
        
        # 測試員工管理API模組
        from api.employee_api import employee_bp
        logger.info("✅ employee_api 模組導入成功")
        
        # 測試班表管理API模組
        from api.shift_api import shift_bp
        logger.info("✅ shift_api 模組導入成功")
        
        # 測試請假管理API模組
        from api.leave_api import leave_bp
        logger.info("✅ leave_api 模組導入成功")
        
        # 測試報表分析API模組
        from api.report_api import report_bp
        logger.info("✅ report_api 模組導入成功")
        
        # 測試系統功能API模組
        from api.system_api import system_bp
        logger.info("✅ system_api 模組導入成功")
        
        # 測試認證權限API模組
        from api.auth_api import auth_bp
        logger.info("✅ auth_api 模組導入成功")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ API模組導入失敗: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 未預期的錯誤: {e}")
        return False

def test_database_connection():
    """
    測試資料庫連接
    
    返回：
    bool: 資料庫連接是否成功
    """
    logger.info("🔍 測試資料庫連接...")
    
    try:
        from database import create_connection
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 測試基本查詢
        cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
        employee_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM departments WHERE is_active = 1")
        department_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM shifts WHERE is_active = 1")
        shift_count = cursor.fetchone()[0]
        
        conn.close()
        
        logger.info(f"✅ 資料庫連接成功")
        logger.info(f"   📊 活躍員工數: {employee_count}")
        logger.info(f"   📊 活躍部門數: {department_count}")
        logger.info(f"   📊 活躍班別數: {shift_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 資料庫連接失敗: {e}")
        return False

def test_flask_app_creation():
    """
    測試Flask應用程式創建和藍圖註冊
    
    返回：
    bool: 應用程式創建是否成功
    """
    logger.info("🔍 測試Flask應用程式創建...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # 檢查藍圖註冊
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['attendance', 'employee', 'shift', 'leave', 'report', 'system', 'auth']
        
        registered_count = 0
        for expected_bp in expected_blueprints:
            if expected_bp in blueprint_names:
                registered_count += 1
                logger.info(f"   ✅ {expected_bp} 藍圖已註冊")
            else:
                logger.warning(f"   ⚠️ {expected_bp} 藍圖未註冊")
        
        # 檢查路由數量
        route_count = len(list(app.url_map.iter_rules()))
        logger.info(f"✅ Flask應用程式創建成功")
        logger.info(f"   📊 已註冊藍圖數: {registered_count}/{len(expected_blueprints)}")
        logger.info(f"   📊 總路由數: {route_count}")
        
        return registered_count == len(expected_blueprints)
        
    except Exception as e:
        logger.error(f"❌ Flask應用程式創建失敗: {e}")
        return False

def test_api_endpoints():
    """
    測試主要API端點的可用性
    
    返回：
    bool: API端點測試是否成功
    """
    logger.info("🔍 測試API端點可用性...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # 測試主要API端點
        test_endpoints = [
            # 考勤相關
            '/api/attendance/records',
            '/api/attendance/import',
            '/api/attendance/manual',
            
            # 員工管理
            '/api/employees',
            '/api/departments',
            
            # 班表管理
            '/api/shifts',
            '/api/schedules/batch',
            
            # 請假管理
            '/api/leave-types',
            '/api/leave-requests',
            
            # 報表分析
            '/api/dashboard/stats',
            '/api/reports/attendance',
            
            # 系統功能
            '/api/health',
            '/api/settings',
            '/api/masterdata/departments',
            
            # 認證權限
            '/api/login',
            '/api/auth/verify',
            '/api/approval/leaves'
        ]
        
        available_routes = [rule.rule for rule in app.url_map.iter_rules()]
        
        available_count = 0
        for endpoint in test_endpoints:
            if endpoint in available_routes:
                available_count += 1
                logger.info(f"   ✅ {endpoint}")
            else:
                logger.warning(f"   ⚠️ {endpoint} 不可用")
        
        logger.info(f"✅ API端點測試完成")
        logger.info(f"   📊 可用端點: {available_count}/{len(test_endpoints)}")
        
        return available_count >= len(test_endpoints) * 0.8  # 80%以上可用即視為成功
        
    except Exception as e:
        logger.error(f"❌ API端點測試失敗: {e}")
        return False

def test_api_module_functions():
    """
    測試各API模組的主要函數
    
    返回：
    bool: 函數測試是否成功
    """
    logger.info("🔍 測試API模組函數...")
    
    try:
        # 測試考勤API函數
        from api.attendance_api import get_attendance_records
        logger.info("   ✅ attendance_api.get_attendance_records")
        
        # 測試員工API函數
        from api.employee_api import get_employees
        logger.info("   ✅ employee_api.get_employees")
        
        # 測試班表API函數
        from api.shift_api import get_shifts
        logger.info("   ✅ shift_api.get_shifts")
        
        # 測試請假API函數
        from api.leave_api import get_leave_types
        logger.info("   ✅ leave_api.get_leave_types")
        
        # 測試報表API函數
        from api.report_api import get_dashboard_stats
        logger.info("   ✅ report_api.get_dashboard_stats")
        
        # 測試系統API函數
        from api.system_api import health_check
        logger.info("   ✅ system_api.health_check")
        
        # 測試認證API函數
        from api.auth_api import login
        logger.info("   ✅ auth_api.login")
        
        logger.info("✅ API模組函數測試成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ API模組函數測試失敗: {e}")
        return False

def generate_api_summary():
    """
    生成API系統摘要報告
    
    返回：
    dict: 系統摘要資訊
    """
    logger.info("📊 生成API系統摘要...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # 統計路由信息
        routes = list(app.url_map.iter_rules())
        api_routes = [r for r in routes if r.rule.startswith('/api/')]
        page_routes = [r for r in routes if not r.rule.startswith('/api/')]
        
        # 按模組分類API
        api_by_module = {
            'attendance': [r for r in api_routes if '/attendance' in r.rule],
            'employee': [r for r in api_routes if '/employee' in r.rule or '/department' in r.rule],
            'shift': [r for r in api_routes if '/shift' in r.rule or '/schedule' in r.rule],
            'leave': [r for r in api_routes if '/leave' in r.rule],
            'report': [r for r in api_routes if '/dashboard' in r.rule or '/report' in r.rule or '/analytics' in r.rule],
            'system': [r for r in api_routes if '/health' in r.rule or '/setting' in r.rule or '/masterdata' in r.rule or '/metrics' in r.rule],
            'auth': [r for r in api_routes if '/login' in r.rule or '/auth' in r.rule or '/approval' in r.rule]
        }
        
        summary = {
            'total_routes': len(routes),
            'api_routes': len(api_routes),
            'page_routes': len(page_routes),
            'modules': {
                module: len(module_routes) 
                for module, module_routes in api_by_module.items()
            },
            'blueprints': list(app.blueprints.keys()),
            'generated_at': datetime.now().isoformat()
        }
        
        logger.info("✅ API系統摘要生成完成")
        logger.info(f"   📊 總路由數: {summary['total_routes']}")
        logger.info(f"   📊 API路由數: {summary['api_routes']}")
        logger.info(f"   📊 頁面路由數: {summary['page_routes']}")
        logger.info(f"   📊 已註冊藍圖: {len(summary['blueprints'])}")
        
        for module, count in summary['modules'].items():
            logger.info(f"   📊 {module} 模組: {count} 個API")
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ API系統摘要生成失敗: {e}")
        return {}

def main():
    """
    主測試函數
    """
    logger.info("🚀 開始完整API系統測試")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 執行所有測試
    test_results['module_import'] = test_api_modules_import()
    test_results['database_connection'] = test_database_connection()
    test_results['flask_app_creation'] = test_flask_app_creation()
    test_results['api_endpoints'] = test_api_endpoints()
    test_results['module_functions'] = test_api_module_functions()
    
    # 生成摘要
    summary = generate_api_summary()
    
    logger.info("=" * 60)
    logger.info("📋 測試結果摘要:")
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"📊 測試通過率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有測試通過！API系統已完全整合")
        logger.info("🚀 系統已準備就緒，可以啟動服務")
    else:
        logger.warning("⚠️ 部分測試失敗，請檢查相關模組")
    
    logger.info("=" * 60)
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 