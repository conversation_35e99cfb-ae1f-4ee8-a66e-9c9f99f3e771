# 多階段構建減少容器大小
FROM python:3.11-slim AS builder

# 安裝依賴
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# 生產階段
FROM python:3.11-slim

# 複製依賴
COPY --from=builder /root/.local /root/.local

# 設定工作目錄
WORKDIR /app

# 複製應用程式
COPY . .

# 設定環境變數
ENV PATH=/root/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV FLASK_APP=app.py

# 暴露端口
EXPOSE 5000

# 使用gunicorn運行
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--timeout", "120", "app:app"]