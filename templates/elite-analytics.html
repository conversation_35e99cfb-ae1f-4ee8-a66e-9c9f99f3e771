<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>數據分析 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">數據分析</h1>
            <p class="text-gray-600">深度洞察考勤數據，優化管理決策</p>
        </div>

        <!-- 時間範圍選擇 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">時間範圍：</label>
                        <select class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option>最近7天</option>
                            <option>最近30天</option>
                            <option>最近3個月</option>
                            <option>最近1年</option>
                            <option>自定義</option>
                        </select>
                    </div>

                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">部門：</label>
                        <select class="border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option>所有部門</option>
                            <option>技術部</option>
                            <option>行銷部</option>
                            <option>財務部</option>
                            <option>人事部</option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <button class="bg-brand-500 text-white px-4 py-2 rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>更新數據</span>
                    </button>

                    <button class="border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>匯出報告</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 關鍵指標 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="trending-up" class="w-6 h-6 text-brand-600"></i>
                    </div>
                    <span class="text-success-600 text-sm font-medium">+5.2%</span>
                </div>
                <p class="text-2xl font-bold text-gray-900 mb-1">94.8%</p>
                <p class="text-sm text-gray-500">平均出勤率</p>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-warning-600"></i>
                    </div>
                    <span class="text-error-600 text-sm font-medium">+12.3%</span>
                </div>
                <p class="text-2xl font-bold text-gray-900 mb-1">8.2分鐘</p>
                <p class="text-sm text-gray-500">平均遲到時間</p>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-success-600"></i>
                    </div>
                    <span class="text-success-600 text-sm font-medium">+2.1%</span>
                </div>
                <p class="text-2xl font-bold text-gray-900 mb-1">156人</p>
                <p class="text-sm text-gray-500">活躍員工數</p>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar-x" class="w-6 h-6 text-error-600"></i>
                    </div>
                    <span class="text-warning-600 text-sm font-medium">-1.8%</span>
                </div>
                <p class="text-2xl font-bold text-gray-900 mb-1">3.2%</p>
                <p class="text-sm text-gray-500">缺勤率</p>
            </div>
        </div>

        <!-- 圖表區域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 出勤趨勢圖 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">出勤趨勢分析</h3>
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2 text-sm">
                            <div class="w-3 h-3 bg-brand-500 rounded-full"></div>
                            <span class="text-gray-600">正常</span>
                        </div>
                        <div class="flex items-center space-x-2 text-sm">
                            <div class="w-3 h-3 bg-warning-500 rounded-full"></div>
                            <span class="text-gray-600">遲到</span>
                        </div>
                        <div class="flex items-center space-x-2 text-sm">
                            <div class="w-3 h-3 bg-error-500 rounded-full"></div>
                            <span class="text-gray-600">缺勤</span>
                        </div>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="attendanceTrendChart"></canvas>
                </div>
            </div>

            <!-- 部門出勤率對比 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">部門出勤率對比</h3>
                </div>
                <div class="h-80">
                    <canvas id="departmentChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 詳細分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 時間分布分析 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">打卡時間分布</h3>
                <div class="h-64">
                    <canvas id="timeDistributionChart"></canvas>
                </div>
            </div>

            <!-- 請假類型統計 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">請假類型統計</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-brand-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">年假</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-20 h-2 bg-gray-100 rounded-full">
                                <div class="w-3/4 h-full bg-brand-500 rounded-full"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900">45%</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-success-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">病假</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-20 h-2 bg-gray-100 rounded-full">
                                <div class="w-1/3 h-full bg-success-500 rounded-full"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900">25%</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-warning-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">事假</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-20 h-2 bg-gray-100 rounded-full">
                                <div class="w-1/5 h-full bg-warning-500 rounded-full"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900">20%</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">其他</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-20 h-2 bg-gray-100 rounded-full">
                                <div class="w-1/10 h-full bg-purple-500 rounded-full"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900">10%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 效率指標 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">效率指標</h3>
                <div class="space-y-6">
                    <div class="text-center">
                        <div class="relative w-24 h-24 mx-auto mb-4">
                            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="transparent" class="text-gray-200"/>
                                <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="transparent" stroke-dasharray="251.2" stroke-dashoffset="37.68" class="text-brand-500" stroke-linecap="round"/>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xl font-bold text-gray-900">85%</span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500">工作效率</p>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">準時率</span>
                            <span class="text-sm font-medium text-gray-900">92%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">加班頻率</span>
                            <span class="text-sm font-medium text-gray-900">15%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">滿勤率</span>
                            <span class="text-sm font-medium text-gray-900">78%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        lucide.createIcons();

        // 全域變數
        const API_BASE = '/api';
        let trendChart = null;
        let departmentChart = null;
        let timeDistributionChart = null;
        let analyticsData = {};

        // 載入分析資料
        async function loadAnalyticsData() {
            try {
                showLoading();

                // 並行載入多個分析API
                const [
                    attendanceTrends,
                    departmentStats,
                    timeDistribution,
                    leaveStats,
                    efficiencyMetrics
                ] = await Promise.all([
                    fetch(`${API_BASE}/analytics/attendance-trends`).then(res => res.json()),
                    fetch(`${API_BASE}/analytics/department-stats`).then(res => res.json()),
                    fetch(`${API_BASE}/analytics/time-distribution`).then(res => res.json()),
                    fetch(`${API_BASE}/analytics/leave-stats`).then(res => res.json()),
                    fetch(`${API_BASE}/analytics/efficiency`).then(res => res.json())
                ]);

                analyticsData = {
                    attendanceTrends,
                    departmentStats,
                    timeDistribution,
                    leaveStats,
                    efficiencyMetrics
                };

                updateKPICards();
                updateCharts();
                updateLeaveAnalysis();
                updateEfficiencyMetrics();
                hideLoading();

            } catch (error) {
                console.error('載入分析資料失敗:', error);
                showError('載入分析資料失敗');
                hideLoading();
            }
        }

        // 更新KPI卡片
        function updateKPICards() {
            const {
                attendanceTrends
            } = analyticsData;

            if (attendanceTrends && attendanceTrends.summary) {
                const kpiCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-4 .bg-white');

                if (kpiCards.length >= 4) {
                    // 總出勤人數
                    kpiCards[0].querySelector('.text-3xl').textContent = attendanceTrends.summary.totalAttendance || 0;

                    // 準時率
                    const onTimeRate = attendanceTrends.summary.onTimeRate || 0;
                    kpiCards[1].querySelector('.text-3xl').textContent = `${onTimeRate}%`;

                    // 平均工時
                    kpiCards[2].querySelector('.text-3xl').textContent = `${attendanceTrends.summary.avgWorkHours || 0}h`;

                    // 缺勤率
                    const absentRate = attendanceTrends.summary.absentRate || 0;
                    kpiCards[3].querySelector('.text-3xl').textContent = `${absentRate}%`;
                }
            }
        }

        // 更新圖表
        function updateCharts() {
            updateAttendanceTrendChart();
            updateDepartmentChart();
            updateTimeDistributionChart();
        }

        // 更新出勤趨勢圖
        function updateAttendanceTrendChart() {
            const {
                attendanceTrends
            } = analyticsData;

            if (trendChart) {
                trendChart.destroy();
            }

            const trendCtx = document.getElementById('attendanceTrendChart').getContext('2d');
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: attendanceTrends && attendanceTrends.labels || ['週一', '週二', '週三', '週四', '週五', '週六', '週日'],
                    datasets: [{
                        label: '正常出勤',
                        data: attendanceTrends && attendanceTrends.normal || [145, 152, 148, 156, 142, 89, 45],
                        borderColor: '#7c6df2',
                        backgroundColor: 'rgba(124, 109, 242, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '遲到',
                        data: attendanceTrends && attendanceTrends.late || [8, 12, 6, 8, 15, 3, 2],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '缺勤',
                        data: attendanceTrends && attendanceTrends.absent || [2, 1, 3, 1, 2, 1, 0],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // 更新部門對比圖
        function updateDepartmentChart() {
            const {
                departmentStats
            } = analyticsData;

            if (departmentChart) {
                departmentChart.destroy();
            }

            const deptCtx = document.getElementById('departmentChart').getContext('2d');
            departmentChart = new Chart(deptCtx, {
                type: 'bar',
                data: {
                    labels: departmentStats && departmentStats.labels || ['技術部', '行銷部', '財務部', '人事部'],
                    datasets: [{
                        label: '出勤率',
                        data: departmentStats && departmentStats.attendanceRates || [96, 98, 92, 89],
                        backgroundColor: [
                            'rgba(124, 109, 242, 0.8)',
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ],
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `出勤率: ${context.parsed.y}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 更新時間分布圖
        function updateTimeDistributionChart() {
            const {
                timeDistribution
            } = analyticsData;

            if (timeDistributionChart) {
                timeDistributionChart.destroy();
            }

            const timeCtx = document.getElementById('timeDistributionChart').getContext('2d');
            timeDistributionChart = new Chart(timeCtx, {
                type: 'doughnut',
                data: {
                    labels: timeDistribution && timeDistribution.labels || ['8:00-8:30', '8:30-9:00', '9:00-9:30', '9:30+'],
                    datasets: [{
                        data: timeDistribution && timeDistribution.data || [45, 35, 15, 5],
                        backgroundColor: [
                            '#22c55e',
                            '#7c6df2',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((context.parsed / total) * 100);
                                    return `${context.label}: ${percentage}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新請假分析
        function updateLeaveAnalysis() {
            const {
                leaveStats
            } = analyticsData;

            if (leaveStats) {
                const leaveContainer = document.querySelector('.bg-white.rounded-2xl.p-6.shadow-lg:nth-child(2) .space-y-4');

                if (leaveContainer) {
                    leaveContainer.innerHTML = '';

                    leaveStats.forEach(stat => {
                        const leaveItem = document.createElement('div');
                        leaveItem.className = 'flex items-center justify-between';

                        leaveItem.innerHTML = `
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 ${getLeaveColor(stat.type)} rounded-full"></div>
                                <span class="text-sm text-gray-700">${getLeaveTypeName(stat.type)}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 h-2 bg-gray-100 rounded-full">
                                    <div class="h-full ${getLeaveColor(stat.type)} rounded-full" style="width: ${stat.percentage}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">${stat.percentage}%</span>
                            </div>
                        `;

                        leaveContainer.appendChild(leaveItem);
                    });
                }
            }
        }

        // 獲取請假類型顏色
        function getLeaveColor(type) {
            const colors = {
                'sick': 'bg-error-500',
                'annual': 'bg-brand-500',
                'personal': 'bg-warning-500',
                'other': 'bg-purple-500'
            };
            return colors[type] || 'bg-gray-500';
        }

        // 獲取請假類型名稱
        function getLeaveTypeName(type) {
            const names = {
                'sick': '病假',
                'annual': '年假',
                'personal': '事假',
                'other': '其他'
            };
            return names[type] || '未知';
        }

        // 更新效率指標
        function updateEfficiencyMetrics() {
            const {
                efficiencyMetrics
            } = analyticsData;

            if (efficiencyMetrics) {
                const efficiencyContainer = document.querySelector('.bg-white.rounded-2xl.p-6.shadow-lg:last-child');

                // 更新工作效率圓形圖
                const efficiencyPercent = efficiencyMetrics.workEfficiency || 85;
                const circle = efficiencyContainer.querySelector('circle:last-child');
                const circumference = 2 * Math.PI * 40;
                const offset = circumference - (efficiencyPercent / 100) * circumference;

                circle.style.strokeDasharray = circumference;
                circle.style.strokeDashoffset = offset;

                efficiencyContainer.querySelector('.text-xl.font-bold').textContent = `${efficiencyPercent}%`;

                // 更新其他指標
                const metrics = efficiencyContainer.querySelectorAll('.flex.justify-between.items-center');
                if (metrics.length >= 3) {
                    metrics[0].querySelector('.text-sm.font-medium').textContent = `${efficiencyMetrics.punctualityRate || 92}%`;
                    metrics[1].querySelector('.text-sm.font-medium').textContent = `${efficiencyMetrics.overtimeFrequency || 15}%`;
                    metrics[2].querySelector('.text-sm.font-medium').textContent = `${efficiencyMetrics.fullAttendanceRate || 78}%`;
                }
            }
        }

        // 匯出報告
        function exportReport() {
            const reportType = document.querySelector('select').value;
            window.open(`${API_BASE}/analytics/export?type=${reportType}`, '_blank');
        }

        // 重新整理數據
        function refreshData() {
            loadAnalyticsData();
        }

        // 顯示載入狀態
        function showLoading() {
            const loadingHtml = `
                <div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-600"></div>
                        <span class="text-gray-700">載入分析資料中...</span>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHtml);
        }

        // 隱藏載入狀態
        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.remove();
            }
        }

        // 顯示成功訊息
        function showSuccess(message) {
            const successHtml = `
                <div class="fixed top-4 right-4 bg-success-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', successHtml);

            const element = document.querySelector('.fixed.top-4.right-4');
            setTimeout(() => element.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                element.classList.add('translate-x-full');
                setTimeout(() => element.remove(), 300);
            }, 3000);
        }

        // 顯示錯誤訊息
        function showError(message) {
            const errorHtml = `
                <div class="fixed top-4 right-4 bg-error-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', errorHtml);

            const element = document.querySelector('.fixed.top-4.right-4');
            setTimeout(() => element.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                element.classList.add('translate-x-full');
                setTimeout(() => element.remove(), 300);
            }, 5000);
        }

        // 事件監聽器
        document.addEventListener('DOMContentLoaded', function() {
            // 工具欄按鈕
            document.querySelector('[data-lucide="download"]').parentElement.addEventListener('click', exportReport);
            document.querySelector('[data-lucide="refresh-cw"]').parentElement.addEventListener('click', refreshData);

            // 載入初始資料
            loadAnalyticsData();

            // 設定自動重新整理（每5分鐘）
            setInterval(loadAnalyticsData, 5 * 60 * 1000);
        });
    </script>
</body>

</html>