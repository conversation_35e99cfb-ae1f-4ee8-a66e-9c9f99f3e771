<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匯入文字檔 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">匯入文字檔</h1>
            <p class="text-gray-600">從打卡機匯出的文字檔案匯入考勤記錄</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 主要內容區 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 檔案上傳區 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">檔案上傳</h3>

                    <!-- 拖拽上傳區 -->
                    <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-brand-500 transition-colors duration-200 cursor-pointer">
                        <div class="flex flex-col items-center space-y-4">
                            <div class="w-16 h-16 bg-brand-50 rounded-full flex items-center justify-center">
                                <i data-lucide="upload" class="w-8 h-8 text-brand-500"></i>
                            </div>
                            <div>
                                <p class="text-lg font-medium text-gray-900">拖拽檔案到此處或點擊選擇</p>
                                <p class="text-sm text-gray-500 mt-1">支援 .txt 格式的打卡資料檔案</p>
                            </div>
                            <input type="file" id="fileInput" accept=".txt" class="hidden">
                            <button type="button" onclick="document.getElementById('fileInput').click()" class="bg-brand-500 text-white px-6 py-2 rounded-lg hover:bg-brand-600 transition-colors">
                                選擇檔案
                            </button>
                        </div>
                    </div>

                    <!-- 檔案資訊 -->
                    <div id="fileInfo" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i data-lucide="file-text" class="w-5 h-5 text-gray-500"></i>
                                <div>
                                    <p id="fileName" class="font-medium text-gray-900"></p>
                                    <p id="fileSize" class="text-sm text-gray-500"></p>
                                </div>
                            </div>
                            <button id="removeFile" class="text-error-500 hover:text-error-600">
                                <i data-lucide="x" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 匯入選項 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">匯入選項</h3>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-gray-900">覆蓋現有記錄</p>
                                <p class="text-sm text-gray-500">如果該員工該日期已有記錄，是否覆蓋</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="overwriteOption" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-gray-900">自動創建員工</p>
                                <p class="text-sm text-gray-500">當找不到對應員工時，自動創建新員工記錄</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="autoCreateOption" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                            </label>
                        </div>
                    </div>

                    <!-- 匯入按鈕 -->
                    <div class="mt-6">
                        <button id="importButton" disabled class="w-full bg-gray-300 text-gray-500 py-3 px-6 rounded-lg font-medium cursor-not-allowed">
                            請先選擇檔案
                        </button>
                    </div>
                </div>

                <!-- 匯入結果 -->
                <div id="importResult" class="hidden bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">匯入結果</h3>
                    <div id="resultContent"></div>
                </div>
            </div>

            <!-- 側邊欄 -->
            <div class="space-y-6">
                <!-- 檔案格式說明 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">檔案格式說明</h3>

                    <div class="space-y-4">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">支援格式</h4>
                            <p class="text-sm text-gray-600">打卡機匯出的 txt 檔案，每行包含 5 個欄位，以逗號分隔</p>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">欄位說明</h4>
                            <div class="text-sm text-gray-600 space-y-1">
                                <p><span class="font-medium">機器編號</span>：打卡機識別碼</p>
                                <p><span class="font-medium">員工編號</span>：員工卡號</p>
                                <p><span class="font-medium">日期</span>：YYYYMMDD 格式</p>
                                <p><span class="font-medium">時間</span>：HHMM 格式</p>
                                <p><span class="font-medium">狀態</span>：打卡狀態碼</p>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">範例資料</h4>
                            <div class="bg-gray-50 rounded-lg p-3 text-xs font-mono">
                                <div class="text-gray-600">011,00000701,20241220,0945,0</div>
                                <div class="text-gray-600">011,00000701,20241220,1800,0</div>
                                <div class="text-gray-600">011,00000702,20241220,0830,0</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 匯入統計 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">匯入統計</h3>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">成功匯入</span>
                            <span id="successCount" class="font-medium text-success-600">-</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">跳過記錄</span>
                            <span id="skippedCount" class="font-medium text-warning-600">-</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">錯誤記錄</span>
                            <span id="errorCount" class="font-medium text-error-600">-</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">新增員工</span>
                            <span id="newEmployeeCount" class="font-medium text-brand-600">-</span>
                        </div>
                        <div class="flex items-center justify-between text-sm border-t pt-3">
                            <span class="text-gray-600 font-medium">總計處理</span>
                            <span id="totalCount" class="font-medium text-gray-900">-</span>
                        </div>
                    </div>
                </div>

                <!-- 注意事項 -->
                <div class="bg-warning-50 border border-warning-200 rounded-2xl p-6">
                    <h3 class="text-lg font-semibold text-warning-900 mb-4">注意事項</h3>

                    <div class="space-y-2 text-sm text-warning-800">
                        <p>• 匯入前請確保檔案格式正確</p>
                        <p>• 建議先備份現有資料</p>
                        <p>• 大檔案匯入可能需要較長時間</p>
                        <p>• 新增的員工需要手動完善資訊</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        lucide.createIcons();

        // 全域變數
        let selectedFile = null;

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();

            // 頁面載入動畫
            const cards = document.querySelectorAll('.bg-white');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease-out';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // 初始化事件監聽器
        function initializeEventListeners() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');
            const importButton = document.getElementById('importButton');
            const removeFileButton = document.getElementById('removeFile');

            // 拖拽事件
            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('dragleave', handleDragLeave);
            dropZone.addEventListener('drop', handleDrop);

            // 檔案選擇事件
            fileInput.addEventListener('change', handleFileSelect);

            // 移除檔案事件
            removeFileButton.addEventListener('click', removeFile);

            // 匯入按鈕事件
            importButton.addEventListener('click', importFile);
        }

        // 處理拖拽懸停
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('border-brand-500', 'bg-brand-50');
        }

        // 處理拖拽離開
        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('border-brand-500', 'bg-brand-50');
        }

        // 處理檔案拖拽
        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('border-brand-500', 'bg-brand-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 處理檔案選擇
        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 處理檔案
        function handleFile(file) {
            // 檢查檔案類型
            if (!file.name.toLowerCase().endsWith('.txt')) {
                alert('請選擇 .txt 格式的檔案');
                return;
            }

            // 檢查檔案大小（限制 10MB）
            if (file.size > 10 * 1024 * 1024) {
                alert('檔案大小不能超過 10MB');
                return;
            }

            selectedFile = file;
            showFileInfo(file);
            enableImportButton();
        }

        // 顯示檔案資訊
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        // 格式化檔案大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 移除檔案
        function removeFile() {
            selectedFile = null;
            document.getElementById('fileInfo').classList.add('hidden');
            document.getElementById('fileInput').value = '';
            disableImportButton();
        }

        // 啟用匯入按鈕
        function enableImportButton() {
            const button = document.getElementById('importButton');
            button.disabled = false;
            button.className = 'w-full bg-brand-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-brand-600 transition-colors cursor-pointer';
            button.textContent = '開始匯入';
        }

        // 禁用匯入按鈕
        function disableImportButton() {
            const button = document.getElementById('importButton');
            button.disabled = true;
            button.className = 'w-full bg-gray-300 text-gray-500 py-3 px-6 rounded-lg font-medium cursor-not-allowed';
            button.textContent = '請先選擇檔案';
        }

        // 匯入檔案
        async function importFile() {
            if (!selectedFile) {
                alert('請先選擇檔案');
                return;
            }

            const button = document.getElementById('importButton');
            const originalText = button.textContent;

            try {
                // 更新按鈕狀態
                button.disabled = true;
                button.textContent = '匯入中...';
                button.className = 'w-full bg-warning-500 text-white py-3 px-6 rounded-lg font-medium cursor-not-allowed';

                // 準備表單資料
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('overwrite', document.getElementById('overwriteOption').checked);
                formData.append('auto_create_employee', document.getElementById('autoCreateOption').checked);

                // 發送請求
                const response = await fetch('/api/attendance/import-text', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showImportResult(result);
                    updateStatistics(result);

                    // 成功狀態
                    button.textContent = '匯入成功！';
                    button.className = 'w-full bg-success-500 text-white py-3 px-6 rounded-lg font-medium cursor-not-allowed';
                } else {
                    throw new Error(result.error || '匯入失敗');
                }

            } catch (error) {
                console.error('匯入錯誤:', error);
                showImportError(error.message);

                // 錯誤狀態
                button.textContent = '匯入失敗';
                button.className = 'w-full bg-error-500 text-white py-3 px-6 rounded-lg font-medium cursor-not-allowed';
            }

            // 3秒後恢復按鈕
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                button.className = 'w-full bg-brand-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-brand-600 transition-colors cursor-pointer';
            }, 3000);
        }

        // 顯示匯入結果
        function showImportResult(result) {
            const resultDiv = document.getElementById('importResult');
            const contentDiv = document.getElementById('resultContent');

            const html = `
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 text-success-600">
                        <i data-lucide="check-circle" class="w-5 h-5"></i>
                        <span class="font-medium">匯入完成！</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-success-50 p-3 rounded-lg">
                            <p class="text-success-800 font-medium">成功匯入</p>
                            <p class="text-2xl font-bold text-success-600">${result.imported || 0}</p>
                        </div>
                        <div class="bg-warning-50 p-3 rounded-lg">
                            <p class="text-warning-800 font-medium">跳過記錄</p>
                            <p class="text-2xl font-bold text-warning-600">${result.skipped || 0}</p>
                        </div>
                        <div class="bg-error-50 p-3 rounded-lg">
                            <p class="text-error-800 font-medium">錯誤記錄</p>
                            <p class="text-2xl font-bold text-error-600">${result.errors || 0}</p>
                        </div>
                        <div class="bg-brand-50 p-3 rounded-lg">
                            <p class="text-brand-800 font-medium">新增員工</p>
                            <p class="text-2xl font-bold text-brand-600">${result.created_employees || 0}</p>
                        </div>
                    </div>
                    
                    ${result.created_employees > 0 ? `
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start space-x-2">
                                <i data-lucide="info" class="w-5 h-5 text-blue-500 mt-0.5"></i>
                                <div>
                                    <p class="font-medium text-blue-900">提醒</p>
                                    <p class="text-sm text-blue-800">已自動創建 ${result.created_employees} 筆新員工記錄，請到員工管理頁面手動修改這些員工的姓名和其他資訊。</p>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;

            contentDiv.innerHTML = html;
            resultDiv.classList.remove('hidden');
            
            // 重新初始化圖標
            lucide.createIcons();
        }

        // 顯示匯入錯誤
        function showImportError(errorMessage) {
            const resultDiv = document.getElementById('importResult');
            const contentDiv = document.getElementById('resultContent');

            const html = `
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 text-error-600">
                        <i data-lucide="x-circle" class="w-5 h-5"></i>
                        <span class="font-medium">匯入失敗</span>
                    </div>
                    
                    <div class="bg-error-50 border border-error-200 rounded-lg p-4">
                        <p class="text-error-800">${errorMessage}</p>
                    </div>
                    
                    <div class="text-sm text-gray-600">
                        <p class="font-medium mb-2">可能的解決方案：</p>
                        <ul class="list-disc list-inside space-y-1">
                            <li>檢查檔案格式是否正確</li>
                            <li>確認檔案編碼為 UTF-8</li>
                            <li>檢查網路連接狀態</li>
                            <li>聯繫系統管理員</li>
                        </ul>
                    </div>
                </div>
            `;

            contentDiv.innerHTML = html;
            resultDiv.classList.remove('hidden');
            
            // 重新初始化圖標
            lucide.createIcons();
        }

        // 更新統計資訊
        function updateStatistics(result) {
            document.getElementById('successCount').textContent = result.imported || 0;
            document.getElementById('skippedCount').textContent = result.skipped || 0;
            document.getElementById('errorCount').textContent = result.errors || 0;
            document.getElementById('newEmployeeCount').textContent = result.created_employees || 0;
            document.getElementById('totalCount').textContent = result.total || 0;
        }
    </script>
</body>

</html>