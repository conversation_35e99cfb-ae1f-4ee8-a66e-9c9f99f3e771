"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/calculator.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Calculator; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"16\",\n            height: \"20\",\n            x: \"4\",\n            y: \"2\",\n            rx: \"2\",\n            key: \"1nb95v\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"16\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"x4nwl0\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"16\",\n            y1: \"14\",\n            y2: \"18\",\n            key: \"wjye3r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 10h.01\",\n            key: \"1m94wz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 10h.01\",\n            key: \"1nrarc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 10h.01\",\n            key: \"19clt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 14h.01\",\n            key: \"1etili\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 14h.01\",\n            key: \"6423bh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18h.01\",\n            key: \"mhygvu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 18h.01\",\n            key: \"lrp35t\"\n        }\n    ]\n];\nconst Calculator = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"calculator\", __iconNode);\n //# sourceMappingURL=calculator.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calculator,Calendar,CalendarClock,CheckCircle,Clock,Database,FileText,Home,Settings,Shield,TrendingUp,Upload,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const { user, login: authLogin, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [autoLoginLoading, setAutoLoginLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 自動登入功能 - 使用管理員測試帳號\n    const handleAutoLogin = async ()=>{\n        setAutoLoginLoading(true);\n        try {\n            console.log(\"開始自動登入管理員帳號\");\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_6__.login)({\n                employee_id: \"admin\",\n                password: \"admin123\"\n            });\n            if (response && response.user) {\n                const userData = response.user;\n                const user = {\n                    id: userData.employee_id,\n                    name: userData.employee_name,\n                    employee_id: userData.employee_code,\n                    department_id: userData.department_id,\n                    position: userData.role_id === 999 ? \"系統管理員\" : \"員工\",\n                    email: userData.email,\n                    role_id: userData.role_id,\n                    department_name: userData.department_name\n                };\n                authLogin(user);\n                console.log(\"自動登入成功:\", user);\n            } else {\n                console.error(\"自動登入失敗:\", response);\n            }\n        } catch (error) {\n            console.error(\"自動登入錯誤:\", error);\n        } finally{\n            setAutoLoginLoading(false);\n        }\n    };\n    // 頁面載入時檢查登入狀態 - 增加延遲避免過早重定向\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted && !loading && !user) {\n            console.log(\"檢測到未登入，延遲重定向到登錄頁面\");\n            const timer = setTimeout(()=>{\n                router.push(\"/admin/login\");\n            }, 500) // 給AuthContext更多時間載入\n            ;\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        mounted,\n        loading,\n        user,\n        router\n    ]);\n    // 在組件未掛載時顯示載入狀態，避免 hydration 錯誤\n    if (!mounted || loading || autoLoginLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: autoLoginLoading ? \"自動登入中...\" : \"載入中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 90,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果沒有用戶，顯示登入選項\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-16 h-16 text-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"管理員登入\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"請登入以訪問管理功能\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAutoLogin,\n                                loading: autoLoginLoading,\n                                className: \"w-full\",\n                                children: \"使用測試帳號登入 (admin/admin123)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: \"手動登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 13\n        }, this);\n    }\n    // 如果不是管理員，顯示權限不足\n    if (user.role_id !== 999) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"權限不足\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"您需要管理員權限才能訪問此頁面\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: \"返回首頁\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    children: \"重新登入\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"管理後台\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Han AttendanceOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"歡迎，\",\n                                            user.name,\n                                            \" (\",\n                                            user.position,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 73\n                                            }, void 0),\n                                            children: \"返回首頁\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-2\",\n                                            children: \"管理員控制台\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-base\",\n                                            children: \"歡迎使用 Han AttendanceOS 管理後台，您可以在這裡管理所有系統功能\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"總員工數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"今日出勤\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"待審核請假\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"本月工時\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"168h\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/attendance-management\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"考勤管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工考勤記錄、班表設定、工時計算和異常處理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/attendance-processing\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"考勤資料整理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"自動計算遲到、早退、加班時間，整合請假資料並更新考勤狀態\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cyan-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入整理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/employees\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"員工管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理員工基本資料、部門分組、角色權限和個人設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/leave-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"請假審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工請假申請、管理假期餘額和請假政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-purple-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/overtime-approval\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"加班審核\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"審核員工加班申請、管理加班時數和加班政策設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/punch-records\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"打卡記錄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看和管理所有員工的打卡記錄和考勤統計\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-orange-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入查看\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 opacity-75\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"數據報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"生成各種考勤報表、統計分析和趨勢預測\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-gray-400 text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"開發中...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/shifts\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"排班管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"設定班別時間、加班規則、休息時間和工時計算方式\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/masterdata\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"基本資料管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"管理學歷、職位、假別、薪資等級 基本資料設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cyan-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/work-reports\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"工作回報管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"查看員工工作回報、提供回覆與指導、管理工作進度\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-violet-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/import-attendance\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"匯入考勤資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"從打卡機匯出的文字檔案匯入考勤記錄和員工資料\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-emerald-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入匯入\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin/settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"系統設定\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: \"配置系統參數、考勤規則、通知設定和權限管理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-indigo-600 text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"進入設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"快速操作\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"匯出本月考勤報表\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"批量審核請假\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"新增員工\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calculator_Calendar_CalendarClock_CheckCircle_Clock_Database_FileText_Home_Settings_Shield_TrendingUp_Upload_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 67\n                                        }, void 0),\n                                        children: \"系統備份\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/admin/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 9\n    }, this);\n}\n_s(AdminPage, \"FHDnuiI1nXHZA22bmtSNVUxE0pk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});