#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六月份測試請假資料生成腳本

功能：
- 自動產生六月份的測試請假資料
- 包含不同類型的請假（病假、事假、年假等）
- 包含全天請假和部分請假
- 測試考勤整理功能的請假記錄整合
"""

import sqlite3
import random
from datetime import datetime, timedelta
from database import create_connection

def create_leave_types():
    """
    創建請假類型資料
    
    返回：
    - 請假類型ID列表
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    # 定義請假類型
    leave_types = [
        ('annual', '年假', '年度休假', 1),
        ('sick', '病假', '因病請假', 1),
        ('personal', '事假', '個人事務請假', 1),
        ('maternity', '產假', '產假/陪產假', 1),
        ('compensatory', '補休', '加班補休', 1),
        ('marriage', '婚假', '結婚請假', 1),
        ('funeral', '喪假', '喪葬請假', 1),
        ('official', '公假', '公務請假', 1)
    ]
    
    # 插入請假類型
    for leave_type in leave_types:
        cursor.execute("""
            INSERT OR REPLACE INTO leave_types (code, name, description, is_active)
            VALUES (?, ?, ?, ?)
        """, leave_type)
    
    # 獲取請假類型ID
    cursor.execute("SELECT id, code, name FROM leave_types WHERE is_active = 1")
    leave_type_data = cursor.fetchall()
    
    conn.commit()
    conn.close()
    
    print(f"✅ 創建了 {len(leave_types)} 種請假類型")
    for lt_id, code, name in leave_type_data:
        print(f"   - {name} (ID: {lt_id}, Code: {code})")
    
    return leave_type_data

def get_active_employees():
    """
    獲取在職員工列表
    
    返回：
    - 員工資料列表
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT e.id, e.name, e.employee_id, d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE (e.status IS NULL OR e.status = 'active')
        ORDER BY e.id
    """)
    
    employees = cursor.fetchall()
    conn.close()
    
    print(f"✅ 找到 {len(employees)} 名在職員工")
    return employees

def generate_june_leave_requests(employees, leave_types):
    """
    生成六月份的請假申請
    
    參數：
    - employees: 員工列表
    - leave_types: 請假類型列表
    
    返回：
    - 生成的請假記錄數量
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    # 清除六月份現有的請假記錄
    cursor.execute("""
        DELETE FROM leave_requests 
        WHERE DATE(start_time) >= '2025-06-01' AND DATE(start_time) <= '2025-06-30'
    """)
    deleted_count = cursor.rowcount
    print(f"🗑️ 清除了 {deleted_count} 筆現有的六月份請假記錄")
    
    # 六月份的工作日（排除週末）
    june_workdays = []
    start_date = datetime(2025, 6, 1)
    end_date = datetime(2025, 6, 30)
    
    current_date = start_date
    while current_date <= end_date:
        # 0=週一, 6=週日，排除週六(5)和週日(6)
        if current_date.weekday() < 5:
            june_workdays.append(current_date.date())
        current_date += timedelta(days=1)
    
    print(f"📅 六月份工作日共 {len(june_workdays)} 天")
    
    # 請假模式定義
    leave_patterns = [
        {
            'name': '全天請假',
            'hours': 8,
            'start_hour': 9,
            'end_hour': 17,
            'probability': 0.6  # 60%機率是全天請假
        },
        {
            'name': '上午請假',
            'hours': 4,
            'start_hour': 9,
            'end_hour': 13,
            'probability': 0.2  # 20%機率是上午請假
        },
        {
            'name': '下午請假',
            'hours': 4,
            'start_hour': 13,
            'end_hour': 17,
            'probability': 0.15  # 15%機率是下午請假
        },
        {
            'name': '短時請假',
            'hours': 2,
            'start_hour': 10,
            'end_hour': 12,
            'probability': 0.05  # 5%機率是短時請假
        }
    ]
    
    generated_count = 0
    
    # 為每個員工隨機生成請假記錄
    for emp_id, emp_name, emp_code, dept_name in employees:
        # 每個員工有30%的機率在六月份請假
        if random.random() > 0.3:
            continue
        
        # 隨機選擇請假天數（1-3天）
        leave_days = random.randint(1, 3)
        
        # 隨機選擇請假日期
        selected_days = random.sample(june_workdays, min(leave_days, len(june_workdays)))
        
        for leave_date in selected_days:
            # 隨機選擇請假類型
            leave_type_id, leave_code, leave_name = random.choice(leave_types)
            
            # 隨機選擇請假模式
            rand_val = random.random()
            cumulative_prob = 0
            selected_pattern = leave_patterns[0]  # 預設全天請假
            
            for pattern in leave_patterns:
                cumulative_prob += pattern['probability']
                if rand_val <= cumulative_prob:
                    selected_pattern = pattern
                    break
            
            # 計算請假時間
            start_time = datetime.combine(leave_date, datetime.min.time().replace(hour=selected_pattern['start_hour']))
            end_time = datetime.combine(leave_date, datetime.min.time().replace(hour=selected_pattern['end_hour']))
            
            # 生成請假原因
            reasons = {
                'annual': ['年假休息', '家庭旅遊', '個人休假'],
                'sick': ['感冒發燒', '身體不適', '醫院看診', '健康檢查'],
                'personal': ['家庭事務', '個人事務處理', '銀行辦事', '搬家'],
                'compensatory': ['加班補休', '週末工作補休'],
                'marriage': ['結婚典禮', '蜜月旅行'],
                'funeral': ['家屬喪事', '參加告別式'],
                'official': ['公務出差', '教育訓練', '會議參與']
            }
            
            reason_list = reasons.get(leave_code, ['其他事務'])
            reason = random.choice(reason_list)
            
            # 插入請假申請
            cursor.execute("""
                INSERT INTO leave_requests (
                    employee_id, leave_type, start_date, end_date,
                    start_time, end_time, hours, reason, status, 
                    approved_at, approver_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                emp_id,
                leave_type_id,
                leave_date.strftime('%Y-%m-%d'),
                leave_date.strftime('%Y-%m-%d'),
                start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time.strftime('%Y-%m-%d %H:%M:%S'),
                selected_pattern['hours'],
                f"{reason} - {selected_pattern['name']}",
                'approved',  # 直接設為已核准
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                1  # 假設審核主管ID為1
            ))
            
            generated_count += 1
            print(f"   📝 {emp_name} ({emp_code}) - {leave_date} {leave_name} {selected_pattern['name']} ({selected_pattern['hours']}小時)")
    
    conn.commit()
    conn.close()
    
    print(f"✅ 成功生成 {generated_count} 筆六月份請假記錄")
    return generated_count

def verify_leave_data():
    """
    驗證生成的請假資料
    
    返回：
    - 驗證結果統計
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    # 統計請假記錄
    cursor.execute("""
        SELECT 
            COUNT(*) as total_requests,
            COUNT(DISTINCT employee_id) as employees_with_leave,
            COUNT(DISTINCT DATE(start_time)) as leave_dates,
            SUM(hours) as total_leave_hours
        FROM leave_requests 
        WHERE DATE(start_time) >= '2025-06-01' AND DATE(start_time) <= '2025-06-30'
        AND status = 'approved'
    """)
    
    stats = cursor.fetchone()
    
    # 按請假類型統計
    cursor.execute("""
        SELECT lt.name, COUNT(*) as count, SUM(lr.hours) as total_hours
        FROM leave_requests lr
        LEFT JOIN leave_types lt ON lr.leave_type = lt.id
        WHERE DATE(lr.start_time) >= '2025-06-01' AND DATE(lr.start_time) <= '2025-06-30'
        AND lr.status = 'approved'
        GROUP BY lt.name
        ORDER BY count DESC
    """)
    
    type_stats = cursor.fetchall()
    
    # 按日期統計
    cursor.execute("""
        SELECT DATE(start_time) as leave_date, COUNT(*) as requests, SUM(hours) as hours
        FROM leave_requests 
        WHERE DATE(start_time) >= '2025-06-01' AND DATE(start_time) <= '2025-06-30'
        AND status = 'approved'
        GROUP BY DATE(start_time)
        ORDER BY leave_date
    """)
    
    daily_stats = cursor.fetchall()
    
    conn.close()
    
    print("\n📊 請假資料驗證結果：")
    print(f"   總請假申請數：{stats[0]}")
    print(f"   有請假的員工數：{stats[1]}")
    print(f"   請假日期數：{stats[2]}")
    print(f"   總請假時數：{stats[3]}")
    
    print("\n📈 按請假類型統計：")
    for type_name, count, hours in type_stats:
        print(f"   {type_name}：{count} 筆，{hours} 小時")
    
    print("\n📅 每日請假統計（前10天）：")
    for i, (date, requests, hours) in enumerate(daily_stats[:10]):
        print(f"   {date}：{requests} 筆請假，{hours} 小時")
    
    return {
        'total_requests': stats[0],
        'employees_with_leave': stats[1],
        'leave_dates': stats[2],
        'total_leave_hours': stats[3],
        'type_breakdown': type_stats,
        'daily_breakdown': daily_stats
    }

def main():
    """
    主函數：執行請假資料生成流程
    """
    print("🚀 開始生成六月份測試請假資料...")
    print("=" * 60)
    
    try:
        # 1. 創建請假類型
        print("\n1️⃣ 創建請假類型...")
        leave_types = create_leave_types()
        
        # 2. 獲取員工列表
        print("\n2️⃣ 獲取在職員工...")
        employees = get_active_employees()
        
        if not employees:
            print("❌ 沒有找到在職員工，無法生成請假資料")
            return
        
        # 3. 生成請假申請
        print("\n3️⃣ 生成六月份請假申請...")
        generated_count = generate_june_leave_requests(employees, leave_types)
        
        # 4. 驗證生成的資料
        print("\n4️⃣ 驗證生成的請假資料...")
        verification_result = verify_leave_data()
        
        print("\n" + "=" * 60)
        print("✅ 六月份測試請假資料生成完成！")
        print(f"📝 總共生成 {generated_count} 筆請假記錄")
        print(f"👥 涉及 {verification_result['employees_with_leave']} 名員工")
        print(f"📅 覆蓋 {verification_result['leave_dates']} 個工作日")
        print(f"⏰ 總計 {verification_result['total_leave_hours']} 小時")
        
        print("\n💡 接下來可以執行考勤整理功能來測試請假記錄整合")
        
    except Exception as e:
        print(f"❌ 生成請假資料時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 