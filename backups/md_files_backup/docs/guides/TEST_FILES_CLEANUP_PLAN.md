# 測試檔案清理計劃

## 📋 測試檔案分類與清理建議

### ✅ **保留檔案** (核心測試)
這些檔案對系統維護和未來開發很重要：

#### 🔧 **核心功能測試** (必須保留)
- `test_basic.py` - 基礎功能測試
- `test_attendance_complete_system.py` - 完整考勤系統測試
- `test_attendance_records_system.py` - 考勤記錄系統測試
- `test_system_integration.py` - 系統整合測試
- `test_simple_api.py` - 簡單API測試

#### 📊 **主要功能測試** (建議保留)
- `test_shift_management.py` - 排班管理測試
- `test_leave_complete.py` - 請假系統測試
- `test_settings_complete.py` - 系統設定測試
- `test_masterdata_complete.py` - 主資料測試

#### 🛠️ **工具腳本** (保留)
- `create_attendance_test_data.py` - 測試資料生成
- `generate_attendance_records.py` - 考勤記錄生成
- `init_complete_test_data.py` - 完整測試資料初始化

### 🗑️ **可清理檔案** (開發過程檔案)

#### 📝 **重複/過時測試**
- `test_attendance_enhanced.py` - 已被完整系統測試取代
- `test_cross_day_attendance.py` - 特定場景測試，已整合
- `test_shift_comprehensive.py` - 已被管理測試取代
- `test_shift_multiple_scenarios.py` - 多場景測試，已整合
- `test_shift_overtime_comprehensive.py` - 加班測試，已整合
- `test_shift_management_focused.py` - 重複功能

#### 🔍 **除錯測試檔案**
- `test_api_debug.py` - 除錯用途
- `test_employee_debug.py` - 除錯用途
- `test_employee_fields.py` - 欄位測試，已完成
- `test_employee_ui_improvements.py` - UI改進測試，已完成
- `test_photo_feature.py` - 照片功能測試，已完成

#### 📊 **測試報告檔案** (全部可清理)
所有 `*test_report*.json` 檔案都是執行結果，可以清理：
- `attendance_complete_system_test_report_*.json` (5個檔案)
- `attendance_enhanced_test_report_*.json` (6個檔案)
- `shift_*_test_report*.json` (4個檔案)
- 其他測試報告檔案 (4個檔案)

#### 🔄 **完成階段測試**
- `test_complete_system_verification.py` - 系統驗證，已完成
- `test_final_complete.py` - 最終測試，已完成
- `test_leave_time_selection.py` - 請假時間選擇，已整合

### 📈 **清理效益**

**預估清理空間：**
- 測試腳本：約 1.5MB (保留 0.7MB 核心測試)
- 測試報告：約 100KB (全部清理)
- **總計節省：約 1.6MB**

**清理後保留：**
- 核心測試檔案：約 10-12 個
- 工具腳本：3-4 個
- 總大小：約 0.7MB

### 🎯 **建議執行步驟**

1. **創建測試檔案備份**
2. **清理過時測試報告**
3. **移除重複/除錯測試**
4. **保留核心測試套件**
5. **更新測試文檔**

### 📋 **保留的測試架構**

清理後的測試結構將更清晰：
```
tests/
├── core/
│   ├── test_basic.py
│   ├── test_attendance_complete_system.py
│   └── test_system_integration.py
├── features/
│   ├── test_shift_management.py
│   ├── test_leave_complete.py
│   └── test_settings_complete.py
└── tools/
    ├── create_attendance_test_data.py
    └── generate_attendance_records.py
```

### ⚠️ **注意事項**

- 清理前確保已完成完整備份
- 保留的測試檔案應該能覆蓋主要功能
- 可考慮將清理的檔案移至 `archive/` 目錄而非直接刪除 