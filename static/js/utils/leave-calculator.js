/**
 * 請假時數計算工具函數庫
 * 統一處理所有請假時數相關的計算邏輯
 * 
 * <AUTHOR> AttendanceOS
 * @version v2005.6.12
 */

class LeaveCalculator {

    /**
     * 標準工作時間配置
     */
    static WORK_CONFIG = {
        DAILY_HOURS: 8, // 每日標準工作時數
        WORK_START: '09:00', // 標準上班時間
        WORK_END: '18:00', // 標準下班時間
        LUNCH_START: '12:00', // 午休開始時間
        LUNCH_END: '13:00', // 午休結束時間
        LUNCH_DURATION: 60 // 午休時長（分鐘）
    };

    /**
     * 計算請假時數（主要函數）
     * @param {string} startDate - 開始日期 (YYYY-MM-DD)
     * @param {string} endDate - 結束日期 (YYYY-MM-DD)
     * @param {string} startTime - 開始時間 (HH:MM)
     * @param {string} endTime - 結束時間 (HH:MM)
     * @param {string} leaveType - 請假類型 ('full_day', 'partial_day', 'hourly')
     * @returns {number} 請假時數
     */
    static calculateLeaveHours(startDate, endDate, startTime = null, endTime = null, leaveType = 'full_day') {
        try {
            // 參數驗證
            if (!startDate || !endDate) {
                throw new Error('開始日期和結束日期不能為空');
            }

            const startDateObj = new Date(startDate);
            const endDateObj = new Date(endDate);

            // 日期有效性檢查
            if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
                throw new Error('日期格式無效');
            }

            if (startDateObj > endDateObj) {
                throw new Error('開始日期不能晚於結束日期');
            }

            // 根據請假類型計算
            switch (leaveType) {
                case 'full_day':
                    return this.calculateFullDayLeave(startDateObj, endDateObj);

                case 'partial_day':
                    return this.calculatePartialDayLeave(startDate, endDate, startTime, endTime);

                case 'hourly':
                    return this.calculateHourlyLeave(startDate, endDate, startTime, endTime);

                default:
                    return this.calculateFullDayLeave(startDateObj, endDateObj);
            }

        } catch (error) {
            console.error('請假時數計算錯誤:', error);
            return 0;
        }
    }

    /**
     * 計算全天請假時數
     * @param {Date} startDate - 開始日期
     * @param {Date} endDate - 結束日期
     * @returns {number} 請假時數
     */
    static calculateFullDayLeave(startDate, endDate) {
        const timeDiff = endDate.getTime() - startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

        // 排除週末（可選功能）
        let workDays = 0;
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
            const dayOfWeek = currentDate.getDay();
            // 0 = 週日, 6 = 週六
            if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                workDays++;
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return workDays * this.WORK_CONFIG.DAILY_HOURS;
    }

    /**
     * 計算部分天請假時數
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {number} 請假時數
     */
    static calculatePartialDayLeave(startDate, endDate, startTime, endTime) {
        if (!startTime || !endTime) {
            // 如果沒有指定時間，預設為半天
            return this.WORK_CONFIG.DAILY_HOURS / 2;
        }

        if (startDate === endDate) {
            // 同一天的部分請假
            return this.calculateSameDayHours(startDate, startTime, endTime);
        } else {
            // 跨天的部分請假
            return this.calculateCrossDayHours(startDate, endDate, startTime, endTime);
        }
    }

    /**
     * 計算小時制請假時數
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {number} 請假時數
     */
    static calculateHourlyLeave(startDate, endDate, startTime, endTime) {
        if (!startTime || !endTime) {
            throw new Error('小時制請假必須指定開始和結束時間');
        }

        if (startDate === endDate) {
            return this.calculateSameDayHours(startDate, startTime, endTime);
        } else {
            return this.calculateCrossDayHours(startDate, endDate, startTime, endTime);
        }
    }

    /**
     * 計算同一天內的請假時數
     * @param {string} date - 日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {number} 請假時數
     */
    static calculateSameDayHours(date, startTime, endTime) {
        const startDateTime = new Date(`${date}T${startTime}`);
        const endDateTime = new Date(`${date}T${endTime}`);

        if (startDateTime >= endDateTime) {
            throw new Error('開始時間不能晚於或等於結束時間');
        }

        // 計算總分鐘數
        const totalMinutes = (endDateTime - startDateTime) / (1000 * 60);

        // 扣除午休時間
        const lunchMinutes = this.calculateLunchBreakMinutes(date, startTime, endTime);
        const actualMinutes = totalMinutes - lunchMinutes;

        // 轉換為小時並保留一位小數
        return Math.round(actualMinutes / 60 * 10) / 10;
    }

    /**
     * 計算跨天請假時數
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {number} 請假時數
     */
    static calculateCrossDayHours(startDate, endDate, startTime, endTime) {
        let totalHours = 0;

        // 第一天：從開始時間到下班時間
        const firstDayHours = this.calculateSameDayHours(
            startDate,
            startTime,
            this.WORK_CONFIG.WORK_END
        );
        totalHours += firstDayHours;

        // 中間的完整天數
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const daysDiff = Math.floor((endDateObj - startDateObj) / (1000 * 3600 * 24));

        if (daysDiff > 1) {
            // 計算中間完整工作天數（排除週末）
            const middleDays = daysDiff - 1;
            let workDays = 0;

            const currentDate = new Date(startDateObj);
            currentDate.setDate(currentDate.getDate() + 1);

            for (let i = 0; i < middleDays; i++) {
                const dayOfWeek = currentDate.getDay();
                if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                    workDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }

            totalHours += workDays * this.WORK_CONFIG.DAILY_HOURS;
        }

        // 最後一天：從上班時間到結束時間
        const lastDayHours = this.calculateSameDayHours(
            endDate,
            this.WORK_CONFIG.WORK_START,
            endTime
        );
        totalHours += lastDayHours;

        return Math.round(totalHours * 10) / 10;
    }

    /**
     * 計算午休時間重疊分鐘數
     * @param {string} date - 日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {number} 午休重疊分鐘數
     */
    static calculateLunchBreakMinutes(date, startTime, endTime) {
        const startDateTime = new Date(`${date}T${startTime}`);
        const endDateTime = new Date(`${date}T${endTime}`);
        const lunchStart = new Date(`${date}T${this.WORK_CONFIG.LUNCH_START}`);
        const lunchEnd = new Date(`${date}T${this.WORK_CONFIG.LUNCH_END}`);

        // 檢查是否與午休時間重疊
        if (startDateTime < lunchEnd && endDateTime > lunchStart) {
            const overlapStart = new Date(Math.max(startDateTime, lunchStart));
            const overlapEnd = new Date(Math.min(endDateTime, lunchEnd));
            return (overlapEnd - overlapStart) / (1000 * 60);
        }

        return 0;
    }

    /**
     * 格式化請假時數顯示
     * @param {number} hours - 時數
     * @returns {string} 格式化後的時數字符串
     */
    static formatHours(hours) {
        if (hours === 0) return '0小時';
        if (hours < 1) return `${Math.round(hours * 60)}分鐘`;

        const wholeHours = Math.floor(hours);
        const minutes = Math.round((hours - wholeHours) * 60);

        if (minutes === 0) {
            return `${wholeHours}小時`;
        } else {
            return `${wholeHours}小時${minutes}分鐘`;
        }
    }

    /**
     * 計算請假天數
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {boolean} excludeWeekends - 是否排除週末
     * @returns {number} 請假天數
     */
    static calculateLeaveDays(startDate, endDate, excludeWeekends = true) {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);

        if (excludeWeekends) {
            let workDays = 0;
            const currentDate = new Date(startDateObj);

            while (currentDate <= endDateObj) {
                const dayOfWeek = currentDate.getDay();
                if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                    workDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }

            return workDays;
        } else {
            const timeDiff = endDateObj.getTime() - startDateObj.getTime();
            return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        }
    }

    /**
     * 驗證請假時間的合理性
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {Object} 驗證結果
     */
    static validateLeaveTime(startDate, endDate, startTime = null, endTime = null) {
        const errors = [];

        // 日期驗證
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (isNaN(startDateObj.getTime())) {
            errors.push('開始日期格式無效');
        }

        if (isNaN(endDateObj.getTime())) {
            errors.push('結束日期格式無效');
        }

        if (startDateObj > endDateObj) {
            errors.push('開始日期不能晚於結束日期');
        }

        if (startDateObj < today) {
            errors.push('不能申請過去的日期');
        }

        // 時間驗證
        if (startTime && endTime) {
            if (startDate === endDate) {
                const startDateTime = new Date(`${startDate}T${startTime}`);
                const endDateTime = new Date(`${endDate}T${endTime}`);

                if (startDateTime >= endDateTime) {
                    errors.push('開始時間不能晚於或等於結束時間');
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// 導出為全域變數（向後兼容）
window.LeaveCalculator = LeaveCalculator;

// 提供簡化的全域函數（向後兼容）
window.calculateLeaveHours = function (startDate, endDate, startTime, endTime) {
    return LeaveCalculator.calculateLeaveHours(startDate, endDate, startTime, endTime, 'hourly');
};

window.formatLeaveHours = function (hours) {
    return LeaveCalculator.formatHours(hours);
};

window.calculateLeaveDays = function (startDate, endDate) {
    return LeaveCalculator.calculateLeaveDays(startDate, endDate);
};