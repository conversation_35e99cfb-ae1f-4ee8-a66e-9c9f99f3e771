#!/usr/bin/env python3
"""
測試請假統計API
"""

import sqlite3
from datetime import datetime

def test_leave_stats_query():
    """測試請假統計查詢"""
    try:
        conn = sqlite3.connect('attendance.db')
        cursor = conn.cursor()
        
        year = 2025
        
        # 測試修復後的查詢
        cursor.execute("""
            SELECT 
                lt.name as leave_type,
                COUNT(lr.id) as request_count,
                SUM(lr.leave_hours) as total_hours,
                COUNT(CASE WHEN lr.status = 'approved' THEN 1 END) as approved_count,
                COUNT(CASE WHEN lr.status = 'rejected' THEN 1 END) as rejected_count,
                COUNT(CASE WHEN lr.status = 'pending' THEN 1 END) as pending_count
            FROM leave_types lt
            LEFT JOIN leaves lr ON lt.code = lr.leave_type 
                AND strftime('%Y', lr.start_date) = ?
            WHERE 1=1
            GROUP BY lt.id, lt.name
            ORDER BY total_hours DESC
        """, (str(year),))
        
        print("請假類型統計:")
        leave_type_stats = []
        for row in cursor.fetchall():
            stat = {
                "leave_type": row[0],
                "request_count": row[1],
                "total_hours": row[2] or 0,
                "approved_count": row[3],
                "rejected_count": row[4],
                "pending_count": row[5],
                "approval_rate": round((row[3] / row[1] * 100) if row[1] > 0 else 0, 1)
            }
            leave_type_stats.append(stat)
            print(f"  {stat}")
        
        # 按月份統計
        cursor.execute("""
            SELECT 
                strftime('%m', start_date) as month,
                COUNT(*) as request_count,
                SUM(leave_hours) as total_hours
            FROM leaves
            WHERE strftime('%Y', start_date) = ? AND status = 'approved'
            GROUP BY strftime('%m', start_date)
            ORDER BY month
        """, (str(year),))
        
        print("\n月份統計:")
        monthly_stats = []
        for row in cursor.fetchall():
            stat = {
                "month": int(row[0]),
                "request_count": row[1],
                "total_hours": row[2]
            }
            monthly_stats.append(stat)
            print(f"  {stat}")
        
        result = {
            "year": year,
            "leave_type_stats": leave_type_stats,
            "monthly_stats": monthly_stats,
            "generated_at": datetime.now().isoformat()
        }
        
        print(f"\n最終結果: {result}")
        return result
        
    except Exception as e:
        print(f"錯誤: {e}")
        return None
    finally:
        conn.close()

if __name__ == "__main__":
    test_leave_stats_query() 