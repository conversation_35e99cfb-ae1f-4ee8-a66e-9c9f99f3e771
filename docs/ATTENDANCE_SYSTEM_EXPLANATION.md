# 考勤系統說明文檔

## 📋 系統概述

本考勤管理系統包含兩個核心概念：**打卡記錄** 和 **考勤記錄**，這兩者有明確的區別和用途。

## 🔍 核心概念區分

### 1. 打卡記錄 (Clock Raw Records)
- **資料表**: `clock_raw_records`
- **來源**: 直接來自打卡機的原始數據
- **特性**: 
  - 未經處理的原始時間戳記
  - 可能包含重複打卡、錯誤打卡等異常數據
  - 僅記錄打卡時間和設備資訊
  - 不包含業務邏輯計算

**範例數據結構**:
```sql
CREATE TABLE clock_raw_records (
    id INTEGER PRIMARY KEY,
    employee_id INTEGER,
    clock_time TIMESTAMP,
    device_id TEXT,
    clock_type TEXT  -- IN/OUT
);
```

### 2. 考勤記錄 (Attendance Records)
- **資料表**: `attendance_records`
- **來源**: 經過系統處理和計算的考勤數據
- **特性**:
  - 經過業務邏輯處理的結構化數據
  - 包含上下班時間、工作時數、遲到早退等計算結果
  - 與班表規則結合計算
  - 包含考勤狀態判定

**範例數據結構**:
```sql
CREATE TABLE attendance_records (
    id INTEGER PRIMARY KEY,
    employee_id INTEGER,
    work_date DATE,
    check_in TIMESTAMP,
    check_out TIMESTAMP,
    shift_id INTEGER,
    status TEXT,  -- normal, late, early_leave, absent, overtime
    late_minutes INTEGER DEFAULT 0,
    early_leave_minutes INTEGER DEFAULT 0,
    overtime_minutes INTEGER DEFAULT 0,
    work_hours REAL DEFAULT 0,
    note TEXT
);
```

## 🔄 數據處理流程

### 階段1: 打卡數據收集
```
打卡機 → clock_raw_records (原始打卡記錄)
```

### 階段2: 數據清理與整理
```
clock_raw_records → 數據清理 → attendance_records (考勤記錄)
```
- 去除重複打卡
- 配對上下班時間
- 處理異常數據

### 階段3: 業務邏輯計算
```
attendance_records + schedules + shifts → 計算結果更新
```
- 根據班表計算遲到、早退
- 計算工作時數和加班時間
- 判定考勤狀態

## 🛠️ 班表修改功能

### 功能說明
當員工的實際工作時間與原定班表不符時，可以修改其班表並重新計算考勤記錄。

### 使用場景
- 員工臨時調班
- 班表安排錯誤需要修正
- 特殊工作安排

### API端點
```http
PUT /api/attendance/management/update-shift
Content-Type: application/json

{
    "attendance_id": 510,
    "shift_id": 1
}
```

### 修改流程
1. **驗證數據**: 檢查考勤記錄和班別是否存在
2. **更新排班**: 在schedules表中更新或創建排班記錄
3. **重新計算**: 根據新班表重新計算考勤狀態
4. **更新記錄**: 將計算結果更新到attendance_records

### 計算邏輯
```python
# 遲到計算
if 實際上班時間 > 班表上班時間:
    遲到分鐘數 = (實際上班時間 - 班表上班時間).minutes

# 早退計算  
if 實際下班時間 < 班表下班時間:
    早退分鐘數 = (班表下班時間 - 實際下班時間).minutes

# 加班計算
if 實際下班時間 > 班表下班時間:
    加班分鐘數 = (實際下班時間 - 班表下班時間).minutes
```

## 📊 實際案例

### 修改前
- **員工**: 洪志鴻 (E005)
- **原班表**: 晚班 (14:00-22:00)
- **實際打卡**: 08:27上班, 17:09下班
- **計算結果**: 早退291分鐘 (因為17:09 < 22:00)

### 修改後
- **新班表**: 標準日班 (08:30-17:30)
- **重新計算**: 早退21分鐘 (因為17:09 < 17:30)
- **狀態**: 從"早退291分鐘"改為"早退21分鐘"

## 🔧 系統架構

### 資料表關聯
```
employees (員工) ←→ schedules (排班) ←→ shifts (班別)
    ↓
attendance_records (考勤記錄)
```

### API模組化
- `attendance_api.py`: 考勤記錄管理
- `employee_api.py`: 員工資料管理  
- `shift_api.py`: 班表排班管理
- `auth_api.py`: 認證授權管理

## 📝 重要提醒

1. **數據一致性**: 修改班表會同時更新schedules和attendance_records兩個表
2. **歷史記錄**: 所有修改都會記錄updated_at時間戳
3. **權限控制**: 班表修改需要適當的權限驗證
4. **日誌記錄**: 所有操作都會記錄在系統日誌中

## 🚀 前端整合

前端頁面可以通過以下方式調用班表修改功能：

```javascript
// 修改班表
async function updateShift(attendanceId, shiftId) {
    const response = await fetch('/api/attendance/management/update-shift', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            attendance_id: attendanceId,
            shift_id: shiftId
        })
    });
    
    const result = await response.json();
    if (result.success) {
        // 重新載入考勤記錄
        refreshAttendanceData();
    }
}
```

這個系統設計確保了數據的準確性和一致性，同時提供了靈活的班表管理功能。

## 📊 班表系統

### 班表定義 (Shifts)
- **資料表**: `shifts`
- **內容**: 定義各種班別的時間規則

### 排班記錄 (Schedules)
- **資料表**: `schedules`
- **內容**: 員工的具體排班安排

## 🛠️ 修改班表功能

當需要修改員工的班表時，系統會：

1. **更新排班記錄**: 修改 `schedules` 表中的班別
2. **重新計算考勤**: 根據新的班表規則重新計算考勤記錄
3. **更新狀態**: 重新判定遲到、早退、加班等狀態
4. **保留歷史**: 保留原始打卡記錄，僅更新考勤記錄

## 📝 API 端點說明

### 打卡記錄相關
- `GET /api/clock/raw` - 獲取原始打卡記錄
- `POST /api/clock/import` - 匯入打卡機數據

### 考勤記錄相關
- `GET /api/attendance/records` - 獲取考勤記錄
- `POST /api/attendance/manual` - 手動新增考勤記錄
- `PUT /api/attendance/recalculate` - 重新計算考勤記錄

### 班表管理相關
- `GET /api/schedules` - 獲取排班記錄
- `PUT /api/schedules/update` - 修改員工班表
- `POST /api/schedules/recalculate` - 重新計算指定日期的考勤

## 🔧 系統維護

### 數據一致性
- 定期檢查打卡記錄和考勤記錄的一致性
- 確保班表變更後正確重新計算考勤

### 異常處理
- 處理打卡機故障導致的數據缺失
- 處理員工忘記打卡的情況
- 處理班表臨時變更的情況

## 📈 報表功能

系統基於考勤記錄生成各種報表：
- 出勤統計報表
- 遲到早退統計
- 加班時數統計
- 部門考勤分析

---

**重要提醒**: 
- 打卡記錄是原始數據，一般不應修改
- 考勤記錄是業務數據，可根據業務需要調整
- 班表變更會觸發考勤記錄的重新計算 