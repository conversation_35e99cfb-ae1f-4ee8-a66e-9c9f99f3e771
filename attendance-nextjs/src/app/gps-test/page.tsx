'use client'

import { useState, useEffect } from 'react'
import { MapPin, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react'

export default function GPSTestPage() {
    const [location, setLocation] = useState<string>('檢測中...')
    const [error, setError] = useState<string>('')
    const [isLoading, setIsLoading] = useState(false)
    const [permissions, setPermissions] = useState<any>(null)

    const checkPermissions = async () => {
        if ('permissions' in navigator) {
            try {
                const result = await navigator.permissions.query({ name: 'geolocation' as PermissionName })
                setPermissions(result.state)
                console.log('定位權限狀態:', result.state)
            } catch (err) {
                console.log('無法檢查權限:', err)
            }
        }
    }

    const testGPS = () => {
        setIsLoading(true)
        setError('')
        setLocation('正在獲取位置...')

        if (!navigator.geolocation) {
            setLocation('❌ 瀏覽器不支援定位')
            setIsLoading(false)
            return
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude.toFixed(6)
                const lng = position.coords.longitude.toFixed(6)
                const accuracy = position.coords.accuracy.toFixed(0)
                setLocation(`✅ ${lat}, ${lng}`)
                setError(`精確度: ${accuracy}公尺`)
                setIsLoading(false)
                console.log('GPS成功:', position)
            },
            (error) => {
                setIsLoading(false)
                console.error('GPS錯誤:', error)

                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        setLocation('❌ 用戶拒絕定位權限')
                        setError('請在瀏覽器設定中允許位置存取')
                        break
                    case error.POSITION_UNAVAILABLE:
                        setLocation('❌ 位置資訊無法取得')
                        setError('GPS信號不佳或服務不可用')
                        break
                    case error.TIMEOUT:
                        setLocation('❌ 定位請求超時')
                        setError('請檢查網路連線或重試')
                        break
                    default:
                        setLocation('❌ 定位失敗')
                        setError(`錯誤代碼: ${error.code}, 訊息: ${error.message}`)
                        break
                }
            },
            {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 0
            }
        )
    }

    useEffect(() => {
        checkPermissions()
        testGPS()
    }, [])

    const getPermissionColor = () => {
        switch (permissions) {
            case 'granted': return 'text-green-600'
            case 'denied': return 'text-red-600'
            case 'prompt': return 'text-yellow-600'
            default: return 'text-gray-600'
        }
    }

    const getPermissionText = () => {
        switch (permissions) {
            case 'granted': return '✅ 已允許'
            case 'denied': return '❌ 已拒絕'
            case 'prompt': return '⚠️ 等待用戶決定'
            default: return '❓ 未知'
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
            <div className="max-w-md mx-auto">
                {/* 標題 */}
                <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <MapPin className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">GPS 定位測試</h1>
                    <p className="text-gray-600">診斷定位功能問題</p>
                </div>

                {/* 權限狀態 */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">權限狀態</h2>
                    <div className="flex items-center justify-between">
                        <span className="text-gray-700">定位權限:</span>
                        <span className={`font-semibold ${getPermissionColor()}`}>
                            {getPermissionText()}
                        </span>
                    </div>
                </div>

                {/* 當前位置 */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">當前位置</h2>
                    <div className="text-center">
                        <div className="text-lg font-mono text-gray-800 mb-2">
                            {location}
                        </div>
                        {error && (
                            <div className="text-sm text-red-600 bg-red-50 rounded-lg p-3">
                                {error}
                            </div>
                        )}
                    </div>
                </div>

                {/* 重新測試按鈕 */}
                <button
                    onClick={testGPS}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold py-4 px-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                    <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
                    <span>{isLoading ? '測試中...' : '重新測試'}</span>
                </button>

                {/* 說明 */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">如何開啟定位權限</h3>
                    <div className="space-y-3 text-sm text-gray-700">
                        <div className="flex items-start space-x-2">
                            <span className="text-blue-500 font-semibold">1.</span>
                            <span>點擊網址列左側的 🔒 圖示</span>
                        </div>
                        <div className="flex items-start space-x-2">
                            <span className="text-blue-500 font-semibold">2.</span>
                            <span>選擇「網站設定」或「權限」</span>
                        </div>
                        <div className="flex items-start space-x-2">
                            <span className="text-blue-500 font-semibold">3.</span>
                            <span>將「位置」設定為「允許」</span>
                        </div>
                        <div className="flex items-start space-x-2">
                            <span className="text-blue-500 font-semibold">4.</span>
                            <span>重新載入頁面</span>
                        </div>
                    </div>
                </div>

                {/* 技術資訊 */}
                <div className="bg-gray-50 rounded-2xl p-6 mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">技術資訊</h3>
                    <div className="space-y-2 text-sm text-gray-600">
                        <div>協議: {window.location.protocol}</div>
                        <div>主機: {window.location.hostname}</div>
                        <div>瀏覽器: {navigator.userAgent.split(' ')[0]}</div>
                        <div>支援定位: {navigator.geolocation ? '✅' : '❌'}</div>
                    </div>
                </div>
            </div>
        </div>
    )
} 