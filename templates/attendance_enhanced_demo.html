<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤系統增強功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            border: 1px solid #e1e5e9;
            border-radius: 15px;
            padding: 25px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .result-box {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-box pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .stats-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .stats-card p {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.active {
            display: block;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 考勤系統增強功能演示</h1>
            <p>靈活的加班計算、每日考勤摘要、月度報告等新功能測試平台</p>
        </div>

        <div class="content">
            <!-- 加班計算設定區域 -->
            <div class="section">
                <h2>⚙️ 加班計算設定管理</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="calculationUnit">計算單位：</label>
                        <select id="calculationUnit">
                            <option value="minutes">分鐘（精確計算）</option>
                            <option value="half_hour">半小時（30分鐘為單位）</option>
                            <option value="hour">小時（60分鐘為單位）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="roundingRule">捨去規則：</label>
                        <select id="roundingRule">
                            <option value="down">向下捨去</option>
                            <option value="up">向上進位</option>
                            <option value="nearest">四捨五入</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="lateTolerance">遲到容忍分鐘數：</label>
                        <input type="number" id="lateTolerance" value="10" min="0" max="60">
                    </div>
                    <div class="form-group">
                        <label for="earlyLeaveTolerance">早退容忍分鐘數：</label>
                        <input type="number" id="earlyLeaveTolerance" value="10" min="0" max="60">
                    </div>
                    <div class="form-group">
                        <label for="minimumOvertime">最小加班分鐘數：</label>
                        <input type="number" id="minimumOvertime" value="30" min="0" max="120">
                    </div>
                </div>
                <button class="btn" onclick="loadOvertimeSettings()">載入當前設定</button>
                <button class="btn btn-success" onclick="saveOvertimeSettings()">保存設定</button>
                <div id="overtimeSettingsResult" class="result-box" style="display: none;"></div>
            </div>

            <!-- 進階加班計算測試 -->
            <div class="section">
                <h2>⏰ 進階加班計算測試</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="testShiftId">班別ID：</label>
                        <input type="number" id="testShiftId" value="1" min="1">
                    </div>
                    <div class="form-group">
                        <label for="actualStartTime">實際上班時間：</label>
                        <input type="time" id="actualStartTime" value="07:30">
                    </div>
                    <div class="form-group">
                        <label for="actualEndTime">實際下班時間：</label>
                        <input type="time" id="actualEndTime" value="19:00">
                    </div>
                </div>
                <button class="btn" onclick="calculateAdvancedOvertime()">計算加班時數</button>
                <button class="btn btn-warning" onclick="testMultipleScenarios()">測試多種情境</button>
                <div id="overtimeCalculationResult" class="result-box" style="display: none;"></div>
            </div>

            <!-- 每日考勤摘要 -->
            <div class="section">
                <h2>📋 每日考勤摘要報告</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="reportDate">查詢日期：</label>
                        <input type="date" id="reportDate">
                    </div>
                    <div class="form-group">
                        <label for="reportEmployeeId">員工ID（可選）：</label>
                        <input type="number" id="reportEmployeeId" placeholder="留空查詢所有員工">
                    </div>
                    <div class="form-group">
                        <label for="reportDepartmentId">部門ID（可選）：</label>
                        <input type="number" id="reportDepartmentId" placeholder="留空查詢所有部門">
                    </div>
                </div>
                <button class="btn" onclick="getDailyAttendanceReport()">獲取每日報告</button>
                <div id="dailyReportLoading" class="loading">
                    <div class="spinner"></div>
                    <p>正在載入考勤報告...</p>
                </div>
                <div id="dailyReportResult" class="result-box" style="display: none;"></div>
            </div>

            <!-- 月度考勤摘要 -->
            <div class="section">
                <h2>📊 月度考勤摘要</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="monthlyYearMonth">查詢年月：</label>
                        <input type="month" id="monthlyYearMonth">
                    </div>
                    <div class="form-group">
                        <label for="monthlyEmployeeId">員工ID（可選）：</label>
                        <input type="number" id="monthlyEmployeeId" placeholder="留空查詢所有員工">
                    </div>
                    <div class="form-group">
                        <label for="monthlyDepartmentId">部門ID（可選）：</label>
                        <input type="number" id="monthlyDepartmentId" placeholder="留空查詢所有部門">
                    </div>
                </div>
                <button class="btn" onclick="getMonthlyAttendanceSummary()">獲取月度摘要</button>
                <div id="monthlySummaryLoading" class="loading">
                    <div class="spinner"></div>
                    <p>正在載入月度摘要...</p>
                </div>
                <div id="monthlySummaryResult" class="result-box" style="display: none;"></div>
            </div>

            <!-- 班表列表查看 -->
            <div class="section">
                <h2>📅 班表列表查看</h2>
                <p style="color: #6c757d; margin-bottom: 20px;">查看所有設定的班表，方便對帳檢查</p>
                <button class="btn" onclick="loadAllShifts()">載入所有班表</button>
                <button class="btn btn-secondary" onclick="loadActiveShifts()">載入啟用班表</button>
                <div id="shiftsLoading" class="loading">
                    <div class="spinner"></div>
                    <p>正在載入班表資料...</p>
                </div>
                <div id="shiftsResult" class="result-box" style="display: none;"></div>
            </div>

            <!-- 系統狀態檢查 -->
            <div class="section">
                <h2>🔍 系統狀態檢查</h2>
                <button class="btn btn-secondary" onclick="checkSystemHealth()">健康檢查</button>
                <button class="btn btn-secondary" onclick="runFullTest()">完整功能測試</button>
                <div id="systemCheckResult" class="result-box" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 設定預設日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('reportDate').value = today;

            const currentMonth = new Date().toISOString().slice(0, 7);
            document.getElementById('monthlyYearMonth').value = currentMonth;

            // 載入當前設定
            loadOvertimeSettings();

            // 自動載入班表列表
            loadActiveShifts();
        });

        // 顯示結果
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = isError ?
                `<div class="alert alert-danger">${content}</div>` :
                `<div class="alert alert-success">操作成功</div><pre>${JSON.stringify(content, null, 2)}</pre>`;
        }

        // 顯示載入狀態
        function showLoading(loadingId, show = true) {
            const element = document.getElementById(loadingId);
            if (show) {
                element.classList.add('active');
            } else {
                element.classList.remove('active');
            }
        }

        // 載入加班計算設定
        async function loadOvertimeSettings() {
            try {
                const response = await fetch('/api/attendance/overtime-settings');
                const data = await response.json();

                if (response.ok) {
                    // 更新表單
                    document.getElementById('calculationUnit').value = data.overtime_calculation_unit || 'minutes';
                    document.getElementById('roundingRule').value = data.overtime_rounding_rule || 'down';
                    document.getElementById('lateTolerance').value = data.late_tolerance_minutes || '10';
                    document.getElementById('earlyLeaveTolerance').value = data.early_leave_tolerance_minutes || '10';
                    document.getElementById('minimumOvertime').value = data.minimum_overtime_minutes || '30';

                    showResult('overtimeSettingsResult', data);
                } else {
                    showResult('overtimeSettingsResult', `載入失敗: ${data.error}`, true);
                }
            } catch (error) {
                showResult('overtimeSettingsResult', `載入失敗: ${error.message}`, true);
            }
        }

        // 保存加班計算設定
        async function saveOvertimeSettings() {
            const settings = {
                overtime_calculation_unit: document.getElementById('calculationUnit').value,
                overtime_rounding_rule: document.getElementById('roundingRule').value,
                late_tolerance_minutes: document.getElementById('lateTolerance').value,
                early_leave_tolerance_minutes: document.getElementById('earlyLeaveTolerance').value,
                minimum_overtime_minutes: document.getElementById('minimumOvertime').value
            };

            try {
                const response = await fetch('/api/attendance/overtime-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('overtimeSettingsResult', data);
                } else {
                    showResult('overtimeSettingsResult', `保存失敗: ${data.error}`, true);
                }
            } catch (error) {
                showResult('overtimeSettingsResult', `保存失敗: ${error.message}`, true);
            }
        }

        // 計算進階加班
        async function calculateAdvancedOvertime() {
            const testData = {
                shift_id: parseInt(document.getElementById('testShiftId').value),
                actual_start_time: document.getElementById('actualStartTime').value,
                actual_end_time: document.getElementById('actualEndTime').value
            };

            try {
                const response = await fetch('/api/shifts/calculate-overtime-advanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('overtimeCalculationResult', data);
                } else {
                    showResult('overtimeCalculationResult', `計算失敗: ${data.error}`, true);
                }
            } catch (error) {
                showResult('overtimeCalculationResult', `計算失敗: ${error.message}`, true);
            }
        }

        // 測試多種情境
        async function testMultipleScenarios() {
            const scenarios = [{
                name: '正常上下班',
                start: '08:30',
                end: '17:30'
            }, {
                name: '早到45分鐘',
                start: '07:45',
                end: '17:30'
            }, {
                name: '晚走90分鐘',
                start: '08:30',
                end: '19:00'
            }, {
                name: '早到晚走',
                start: '07:30',
                end: '19:30'
            }, {
                name: '早到不足門檻',
                start: '08:10',
                end: '17:30'
            }];

            const shiftId = parseInt(document.getElementById('testShiftId').value);
            const results = [];

            for (const scenario of scenarios) {
                try {
                    const response = await fetch('/api/shifts/calculate-overtime-advanced', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            shift_id: shiftId,
                            actual_start_time: scenario.start,
                            actual_end_time: scenario.end
                        })
                    });

                    const data = await response.json();
                    results.push({
                        scenario: scenario.name,
                        start_time: scenario.start,
                        end_time: scenario.end,
                        result: data
                    });
                } catch (error) {
                    results.push({
                        scenario: scenario.name,
                        error: error.message
                    });
                }
            }

            showResult('overtimeCalculationResult', {
                scenarios: results
            });
        }

        // 獲取每日考勤報告
        async function getDailyAttendanceReport() {
            showLoading('dailyReportLoading', true);

            const params = new URLSearchParams();
            params.append('date', document.getElementById('reportDate').value);

            const employeeId = document.getElementById('reportEmployeeId').value;
            if (employeeId) params.append('employee_id', employeeId);

            const departmentId = document.getElementById('reportDepartmentId').value;
            if (departmentId) params.append('department_id', departmentId);

            try {
                const response = await fetch(`/api/attendance/daily-report?${params}`);
                const data = await response.json();

                showLoading('dailyReportLoading', false);

                if (response.ok) {
                    // 格式化顯示報告
                    const formattedResult = formatDailyReport(data);
                    document.getElementById('dailyReportResult').style.display = 'block';
                    document.getElementById('dailyReportResult').innerHTML = formattedResult;
                } else {
                    showResult('dailyReportResult', `獲取失敗: ${data.error}`, true);
                }
            } catch (error) {
                showLoading('dailyReportLoading', false);
                showResult('dailyReportResult', `獲取失敗: ${error.message}`, true);
            }
        }

        // 格式化每日報告
        function formatDailyReport(data) {
            const summary = data.summary;
            const reports = data.daily_reports;

            let html = `
                <div class="alert alert-info">
                    <h4>📊 ${data.date} 考勤摘要</h4>
                </div>
                <div class="stats-grid">
                    <div class="stats-card">
                        <h3>${summary.total_employees}</h3>
                        <p>總員工數</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.attended_employees}</h3>
                        <p>出勤人數</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.attendance_rate}%</h3>
                        <p>出勤率</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.late_employees}</h3>
                        <p>遲到人數</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.early_leave_employees}</h3>
                        <p>早退人數</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.overtime_employees}</h3>
                        <p>加班人數</p>
                    </div>
                    <div class="stats-card">
                        <h3>${summary.leave_employees}</h3>
                        <p>請假人數</p>
                    </div>
                </div>
            `;

            if (reports.length > 0) {
                html += `
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>員工姓名</th>
                                    <th>部門</th>
                                    <th>班別</th>
                                    <th>打卡時間</th>
                                    <th>遲到(分)</th>
                                    <th>早退(分)</th>
                                    <th>加班(時)</th>
                                    <th>請假類型</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                reports.forEach(report => {
                    const checkIn = report.actual_check_in ? new Date(report.actual_check_in).toLocaleTimeString() : '-';
                    const checkOut = report.actual_check_out ? new Date(report.actual_check_out).toLocaleTimeString() : '-';
                    const leaveTypes = report.leaves.map(l => l.leave_type).join(', ') || '-';

                    html += `
                        <tr>
                            <td>${report.employee_name}</td>
                            <td>${report.department_name || '-'}</td>
                            <td>${report.shift_name || '-'}</td>
                            <td>${checkIn} - ${checkOut}</td>
                            <td>${report.late_minutes}</td>
                            <td>${report.early_leave_minutes}</td>
                            <td>${report.total_overtime_hours}</td>
                            <td>${leaveTypes}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            return html;
        }

        // 獲取月度考勤摘要
        async function getMonthlyAttendanceSummary() {
            showLoading('monthlySummaryLoading', true);

            const params = new URLSearchParams();
            params.append('year_month', document.getElementById('monthlyYearMonth').value);

            const employeeId = document.getElementById('monthlyEmployeeId').value;
            if (employeeId) params.append('employee_id', employeeId);

            const departmentId = document.getElementById('monthlyDepartmentId').value;
            if (departmentId) params.append('department_id', departmentId);

            try {
                const response = await fetch(`/api/attendance/monthly-summary?${params}`);
                const data = await response.json();

                showLoading('monthlySummaryLoading', false);

                if (response.ok) {
                    const formattedResult = formatMonthlySummary(data);
                    document.getElementById('monthlySummaryResult').style.display = 'block';
                    document.getElementById('monthlySummaryResult').innerHTML = formattedResult;
                } else {
                    showResult('monthlySummaryResult', `獲取失敗: ${data.error}`, true);
                }
            } catch (error) {
                showLoading('monthlySummaryLoading', false);
                showResult('monthlySummaryResult', `獲取失敗: ${error.message}`, true);
            }
        }

        // 格式化月度摘要
        function formatMonthlySummary(data) {
            const summaries = data.monthly_summaries;

            let html = `
                <div class="alert alert-info">
                    <h4>📅 ${data.year_month} 月度考勤摘要</h4>
                    <p>統計期間: ${data.period}</p>
                </div>
            `;

            if (summaries.length > 0) {
                html += `
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>員工姓名</th>
                                    <th>部門</th>
                                    <th>出勤天數</th>
                                    <th>正常</th>
                                    <th>遲到</th>
                                    <th>早退</th>
                                    <th>缺勤</th>
                                    <th>請假天數</th>
                                    <th>出勤率</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                summaries.forEach(summary => {
                    const stats = summary.attendance_stats;
                    const leaveStats = summary.leave_stats;

                    html += `
                        <tr>
                            <td>${summary.employee_name}</td>
                            <td>${summary.department_name || '-'}</td>
                            <td>${stats.total_attendance_days}</td>
                            <td>${stats.normal_days}</td>
                            <td>${stats.late_days}</td>
                            <td>${stats.early_leave_days}</td>
                            <td>${stats.absent_days}</td>
                            <td>${leaveStats.total_leave_days}</td>
                            <td>${summary.attendance_rate}%</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            return html;
        }

        // 系統健康檢查
        async function checkSystemHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                if (response.ok) {
                    showResult('systemCheckResult', {
                        status: 'healthy',
                        ...data
                    });
                } else {
                    showResult('systemCheckResult', `健康檢查失敗: ${data.error}`, true);
                }
            } catch (error) {
                showResult('systemCheckResult', `健康檢查失敗: ${error.message}`, true);
            }
        }

        // 載入所有班表
        async function loadAllShifts() {
            showLoading('shiftsLoading', true);

            try {
                const response = await fetch('/api/shifts');
                const data = await response.json();

                showLoading('shiftsLoading', false);

                if (response.ok) {
                    const formattedResult = formatShiftsList(data.shifts || data, '所有班表');
                    document.getElementById('shiftsResult').style.display = 'block';
                    document.getElementById('shiftsResult').innerHTML = formattedResult;
                } else {
                    showResult('shiftsResult', `載入失敗: ${data.error}`, true);
                }
            } catch (error) {
                showLoading('shiftsLoading', false);
                showResult('shiftsResult', `載入失敗: ${error.message}`, true);
            }
        }

        // 載入啟用班表
        async function loadActiveShifts() {
            showLoading('shiftsLoading', true);

            try {
                const response = await fetch('/api/shifts?status=active');
                const data = await response.json();

                showLoading('shiftsLoading', false);

                if (response.ok) {
                    const shifts = data.shifts || data;
                    const activeShifts = shifts.filter(shift => !shift.is_deleted);
                    const formattedResult = formatShiftsList(activeShifts, '啟用班表');
                    document.getElementById('shiftsResult').style.display = 'block';
                    document.getElementById('shiftsResult').innerHTML = formattedResult;
                } else {
                    showResult('shiftsResult', `載入失敗: ${data.error}`, true);
                }
            } catch (error) {
                showLoading('shiftsLoading', false);
                showResult('shiftsResult', `載入失敗: ${error.message}`, true);
            }
        }

        // 格式化班表列表顯示
        function formatShiftsList(shifts, title) {
            if (!shifts || shifts.length === 0) {
                return `
                    <div class="alert alert-info">
                        <h4>📅 ${title}</h4>
                        <p>目前沒有設定的班表</p>
                    </div>
                `;
            }

            let html = `
                <div class="alert alert-info">
                    <h4>📅 ${title} (共 ${shifts.length} 個班表)</h4>
                    <p>以下是所有設定的班表資訊，方便對帳檢查</p>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>班表名稱</th>
                                <th>代碼</th>
                                <th>上班時間</th>
                                <th>下班時間</th>
                                <th>休息時間</th>
                                <th>工作時數</th>
                                <th>加班設定</th>
                                <th>顏色</th>
                                <th>狀態</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            shifts.forEach(shift => {
                const workHours = calculateWorkHours(shift.start_time, shift.end_time, shift.break_duration_minutes);
                const overtimeSettings = getOvertimeSettings(shift);
                const status = shift.is_deleted ? '已刪除' : '啟用';
                const statusClass = shift.is_deleted ? 'style="color: #dc3545;"' : 'style="color: #28a745;"';

                html += `
                    <tr>
                        <td><strong>${shift.id}</strong></td>
                        <td><strong>${shift.name}</strong></td>
                        <td>${shift.code || '-'}</td>
                        <td>${shift.start_time}</td>
                        <td>${shift.end_time}</td>
                        <td>${formatBreakTime(shift)}</td>
                        <td>${workHours}小時</td>
                        <td>${overtimeSettings}</td>
                        <td>
                            <span style="display: inline-block; width: 20px; height: 20px; background-color: ${shift.color_code || '#6c757d'}; border-radius: 3px; margin-right: 5px;"></span>
                            ${shift.color_code || '-'}
                        </td>
                        <td ${statusClass}><strong>${status}</strong></td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            return html;
        }

        // 計算工作時數
        function calculateWorkHours(startTime, endTime, breakMinutes = 0) {
            const start = new Date(`2000-01-01 ${startTime}`);
            const end = new Date(`2000-01-01 ${endTime}`);

            // 處理跨日情況
            if (end < start) {
                end.setDate(end.getDate() + 1);
            }

            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);
            const workHours = diffHours - (breakMinutes / 60);

            return workHours.toFixed(1);
        }

        // 格式化休息時間
        function formatBreakTime(shift) {
            if (!shift.break_start_time || !shift.break_duration_minutes) {
                return '-';
            }

            const breakStart = shift.break_start_time;
            const duration = shift.break_duration_minutes;
            const endTime = addMinutes(breakStart, duration);

            return `${breakStart}-${endTime} (${duration}分鐘)`;
        }

        // 時間加上分鐘數
        function addMinutes(timeStr, minutes) {
            const time = new Date(`2000-01-01 ${timeStr}`);
            time.setMinutes(time.getMinutes() + minutes);
            return time.toTimeString().slice(0, 5);
        }

        // 獲取加班設定描述
        function getOvertimeSettings(shift) {
            const settings = [];

            if (shift.enable_pre_overtime) {
                settings.push(`上班前: ${shift.pre_overtime_threshold_minutes || 30}分鐘門檻`);
            }

            if (shift.enable_post_overtime) {
                settings.push(`下班後: ${shift.post_overtime_threshold_minutes || 60}分鐘門檻`);
            }

            if (shift.auto_calculate_overtime) {
                settings.push('自動計算');
            }

            return settings.length > 0 ? settings.join('<br>') : '未啟用';
        }

        // 運行完整功能測試
        async function runFullTest() {
            const testResults = [];

            // 測試1: 載入設定
            try {
                const settingsResponse = await fetch('/api/attendance/overtime-settings');
                testResults.push({
                    test: '載入加班設定',
                    status: settingsResponse.ok ? 'PASS' : 'FAIL',
                    response: await settingsResponse.json()
                });
            } catch (error) {
                testResults.push({
                    test: '載入加班設定',
                    status: 'ERROR',
                    error: error.message
                });
            }

            // 測試2: 每日報告
            try {
                const today = new Date().toISOString().split('T')[0];
                const dailyResponse = await fetch(`/api/attendance/daily-report?date=${today}`);
                testResults.push({
                    test: '每日考勤報告',
                    status: dailyResponse.ok ? 'PASS' : 'FAIL',
                    response: await dailyResponse.json()
                });
            } catch (error) {
                testResults.push({
                    test: '每日考勤報告',
                    status: 'ERROR',
                    error: error.message
                });
            }

            // 測試3: 月度摘要
            try {
                const currentMonth = new Date().toISOString().slice(0, 7);
                const monthlyResponse = await fetch(`/api/attendance/monthly-summary?year_month=${currentMonth}`);
                testResults.push({
                    test: '月度考勤摘要',
                    status: monthlyResponse.ok ? 'PASS' : 'FAIL',
                    response: await monthlyResponse.json()
                });
            } catch (error) {
                testResults.push({
                    test: '月度考勤摘要',
                    status: 'ERROR',
                    error: error.message
                });
            }

            showResult('systemCheckResult', {
                test_summary: {
                    total: testResults.length,
                    passed: testResults.filter(r => r.status === 'PASS').length,
                    failed: testResults.filter(r => r.status === 'FAIL').length,
                    errors: testResults.filter(r => r.status === 'ERROR').length
                },
                test_results: testResults
            });
        }
    </script>
</body>

</html>