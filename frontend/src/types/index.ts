/**
 * 全域類型定義 - Han AttendanceOS Next.js 版本
 * 對應 Flask API 的資料結構，保持與後端完全一致
 */

// =============================================================================
// 基礎類型
// =============================================================================

export interface BaseEntity {
  id: number
  created_at?: string
  updated_at?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// =============================================================================
// 用戶與認證 - 對應 Flask API
// =============================================================================

export interface User {
  id: number
  name: string
  employee_id: string
  department_id: number
  position: string
  email?: string
  role_id?: number
  department_name?: string
  allow_online_clock?: boolean
}

export interface LoginCredentials {
  employee_id: string
  password: string
}

export interface LoginResponse {
  success: boolean
  message?: string
  user?: User
  error?: string
}

export interface UserSession {
  employee_id: number
  employee_name: string
  department_name: string
  role: string
  permissions: string[]
  token: string
  expires_at: string
}

// =============================================================================
// 員工管理 - 對應 employees 表
// =============================================================================

export interface Employee extends BaseEntity {
  name: string
  employee_id: string
  department_id: number
  department_name?: string
  position: string
  email?: string
  phone?: string
  hire_date?: string
  status: string  // 改為 string 以匹配 API 響應
  salary_level?: string
  role_id?: number
  manager_id?: number
  photo_url?: string
  education_level_id?: number
}

export interface Department extends BaseEntity {
  name: string
  manager_id?: number
  description?: string
  permission_id?: number
}

// =============================================================================
// 考勤管理 - 對應 attendance 表
// =============================================================================

export interface AttendanceRecord extends BaseEntity {
  employee_id: number
  employee_name?: string
  employee_code?: string
  department_name?: string
  check_in?: string
  check_out?: string
  status: string  // 改為 string 以匹配 API 響應
  work_date?: string
  late_minutes?: number
  early_leave_minutes?: number
  overtime_minutes?: number
  work_hours?: number
  note?: string
}

export interface ClockInData {
  employee_id: string
  device_id?: string
  note?: string
}

export interface AttendanceStats {
  total_records: number
  present_count: number
  late_count: number
  absent_count: number
  early_leave_count: number
  average_work_hours: number
}

// =============================================================================
// 打卡原始資料 - 對應 punch_records 表
// =============================================================================

export interface PunchRecord extends BaseEntity {
  device_id: string
  employee_id: string
  punch_date: string
  punch_time: string
  punch_datetime: string
  status_code: string
  raw_data?: string
  processed: boolean
  attendance_id?: number
  note?: string
  imported_at: string
  processed_at?: string
}

// =============================================================================
// 班表與排班 - 對應 shifts 和 schedules 表
// =============================================================================

export interface Shift extends BaseEntity {
  name: string
  code: string
  start_time: string
  end_time: string
  break_start_time?: string
  break_duration_minutes: number
  pre_overtime_threshold_minutes: number
  post_overtime_threshold_minutes: number
  enable_pre_overtime: boolean
  enable_post_overtime: boolean
  auto_calculate_overtime: boolean
  color_code: string
  description?: string
  is_active: boolean
  updated_at: string
}

export interface Schedule extends BaseEntity {
  employee_id: number
  employee_name?: string
  shift_date: string
  shift_id: number
  shift_name?: string
  status: 'scheduled' | 'completed' | 'cancelled'
  actual_start_time?: string
  actual_end_time?: string
  overtime_hours: number
  pre_overtime_hours: number
  post_overtime_hours: number
  break_time_minutes: number
  note?: string
  updated_at: string
}

// =============================================================================
// 請假管理 - 對應 leaves 表
// =============================================================================

export interface LeaveRequest extends BaseEntity {
  employee_id: number
  employee_name?: string
  department_name?: string
  leave_type: string
  leave_type_name?: string
  start_date: string
  end_date: string
  leave_hours?: number
  status: 'pending' | 'approved' | 'rejected'
  reason?: string
  comment?: string
  substitute_id?: number
  substitute_name?: string
  approver_id?: number
  approver_name?: string
  emergency_contact?: string
  approved_at?: string
}

export interface LeaveType extends BaseEntity {
  code: string
  name: string
  description?: string
  max_days_per_year?: number
  is_active: boolean
}

export interface LeaveStats {
  total_requests: number
  pending_count: number
  approved_count: number
  rejected_count: number
  total_hours: number
}

// =============================================================================
// 加班管理 - 對應 overtime_requests 表
// =============================================================================

export interface OvertimeRequest extends BaseEntity {
  employee_id: number
  employee_name?: string
  department_name?: string
  overtime_date: string
  start_time: string
  end_time: string
  overtime_hours: number
  overtime_type: string
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  approver_id?: number
  approver_name?: string
  approved_at?: string
  updated_at: string
}

export interface OvertimeType extends BaseEntity {
  code: string
  name: string
  rate_multiplier: number
  description?: string
}

// =============================================================================
// 審核系統
// =============================================================================

export interface ApprovalRequest {
  id: number
  type: 'leave' | 'overtime'
  employee_id: number
  employee_name: string
  department_name: string
  title: string
  description: string
  submitted_at: string
  status: 'pending' | 'approved' | 'rejected'
  priority: 'low' | 'medium' | 'high'
  details?: LeaveRequest | OvertimeRequest
}

// =============================================================================
// 報表與分析
// =============================================================================

export interface AttendanceReport {
  employee_id: number
  employee_name: string
  department_name: string
  total_work_days: number
  present_days: number
  absent_days: number
  late_days: number
  early_leave_days: number
  total_work_hours: number
  total_overtime_hours: number
  total_leave_hours: number
  attendance_rate: number
}

export interface DepartmentReport {
  department_id: number
  department_name: string
  total_employees: number
  present_employees: number
  absent_employees: number
  late_employees: number
  attendance_rate: number
  total_work_hours: number
  total_overtime_hours: number
}

export interface DashboardStats {
  total_employees: number
  present_today: number
  absent_today: number
  late_today: number
  pending_approvals: number
  total_departments: number
  attendance_rate: number
  overtime_hours_this_month: number
}

// =============================================================================
// 系統設定 - 對應 system_settings 表
// =============================================================================

export interface SystemSetting extends BaseEntity {
  category: string
  setting_key: string
  setting_value: string
  description?: string
  updated_at: string
}

export interface ClockStatusType extends BaseEntity {
  status_code: string
  status_name: string
  description?: string
  sort_order: number
  is_active: boolean
  updated_at: string
}

export interface SystemSettings {
  company_name: string
  work_start_time: string
  work_end_time: string
  break_duration: number
  tolerance_minutes: number
  overtime_rate: number
  timezone: string
  date_format: string
  time_format: string
}

// =============================================================================
// 通知系統
// =============================================================================

export interface Notification extends BaseEntity {
  user_id: number
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  is_read: boolean
  action_url?: string
}

// =============================================================================
// 表單類型
// =============================================================================

export interface LoginForm {
  employee_id: string
  password: string
}

export interface LeaveRequestForm {
  leave_type: string
  start_date: string
  end_date: string
  start_time?: string
  end_time?: string
  is_full_day: boolean
  reason: string
  substitute_id?: number
  emergency_contact?: string
}

export interface OvertimeRequestForm {
  overtime_date: string
  start_time: string
  end_time: string
  overtime_type: string
  reason: string
}

export interface AttendanceEditForm {
  check_in?: string
  check_out?: string
  status: string
  shift_id?: number
  note?: string
}

// =============================================================================
// 查詢參數
// =============================================================================

export interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
}

export interface FilterParams {
  start_date?: string
  end_date?: string
  department_id?: string
  employee_id?: string
  status?: string
  [key: string]: any
}

export interface SortParams {
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface SearchParams extends PaginationParams, FilterParams, SortParams {}

// =============================================================================
// UI 組件類型
// =============================================================================

export interface TableColumn {
  key: string
  title: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, record: any) => React.ReactNode
}

export interface ActionButton {
  key: string
  label: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  icon?: React.ReactNode
  onClick: (record: any) => void
  disabled?: (record: any) => boolean
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime' | 'select' | 'textarea' | 'time'
  required?: boolean
  placeholder?: string
  options?: { value: string | number; label: string }[]
  validation?: any
  disabled?: boolean
  description?: string
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  children: React.ReactNode
  showCloseButton?: boolean
}

export interface FormProps<T = any> {
  initialData?: T
  onSubmit: (data: T) => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  fields: FormField[]
  submitText?: string
  cancelText?: string
}