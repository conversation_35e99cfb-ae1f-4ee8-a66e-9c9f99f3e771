"""
考勤管理API模組 - 清理版本
提供考勤相關的所有API端點
"""

from flask import Blueprint, request, jsonify, send_file
import sqlite3
from datetime import datetime, timedelta
import logging
from database import create_connection
import io
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
import os
import calendar
import xlsxwriter

# 設定日誌
logger = logging.getLogger(__name__)

# 創建Blueprint
attendance_bp = Blueprint('attendance', __name__)

@attendance_bp.route("/api/attendance/clock-in", methods=["POST"])
def clock_in():
    """員工打卡上班"""
    try:
        data = request.get_json()
        employee_code = data.get('employee_id')  # 前端傳送的是員工編號
        
        if not employee_code:
            return jsonify({"success": False, "error": "員工ID不能為空"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 🔧 根據員工編號查找員工的數字ID
        cursor.execute("""
            SELECT id FROM employees WHERE employee_id = ?
        """, (employee_code,))
        
        employee = cursor.fetchone()
        if not employee:
            return jsonify({"success": False, "error": "員工不存在"}), 400
        
        employee_id = employee['id']  # 獲取數字ID
        
        # 檢查今天是否已經打過卡
        today = datetime.now().date()
        cursor.execute("""
            SELECT id FROM attendance 
            WHERE employee_id = ? AND DATE(check_in) = ?
        """, (employee_id, today))
        
        existing = cursor.fetchone()
        if existing:
            return jsonify({"success": False, "error": "今天已經打過上班卡"}), 400
        
        # 插入打卡記錄
        now = datetime.now()
        cursor.execute("""
            INSERT INTO attendance (employee_id, check_in, work_date, status, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, (employee_id, now, today, 'present', now))
        
        conn.commit()
        conn.close()
        
        return jsonify({"success": True, "message": "上班打卡成功"})
        
    except Exception as e:
        logger.error(f"上班打卡錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/clock-out", methods=["POST"])
def clock_out():
    """員工打卡下班"""
    try:
        data = request.get_json()
        employee_code = data.get('employee_id')  # 前端傳送的是員工編號
        
        if not employee_code:
            return jsonify({"success": False, "error": "員工ID不能為空"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 🔧 根據員工編號查找員工的數字ID
        cursor.execute("""
            SELECT id FROM employees WHERE employee_id = ?
        """, (employee_code,))
        
        employee = cursor.fetchone()
        if not employee:
            return jsonify({"success": False, "error": "員工不存在"}), 400
        
        employee_id = employee['id']  # 獲取數字ID
        
        # 查找今天的打卡記錄
        today = datetime.now().date()
        cursor.execute("""
            SELECT id FROM attendance 
            WHERE employee_id = ? AND DATE(check_in) = ?
        """, (employee_id, today))
        
        record = cursor.fetchone()
        if not record:
            return jsonify({"success": False, "error": "請先打上班卡"}), 400
        
        # 更新下班時間
        now = datetime.now()
        cursor.execute("""
            UPDATE attendance 
            SET check_out = ?
            WHERE id = ?
        """, (now, record['id']))
        
        conn.commit()
        conn.close()
        
        return jsonify({"success": True, "message": "下班打卡成功"})
        
    except Exception as e:
        logger.error(f"下班打卡錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/records", methods=["GET"])
def get_attendance_records():
    """
    獲取考勤記錄列表
    """
    try:
        # 獲取查詢參數
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        department_id = request.args.get('department_id')
        employee_id = request.args.get('employee_id')  # 添加員工ID參數
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_name = request.args.get('employee_name')
        
        # 計算偏移量
        offset = (page - 1) * per_page
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        if employee_id:
            # 支援兩種查詢方式：整數ID或員工編號
            if str(employee_id).isdigit():
                where_conditions.append("a.employee_id = ?")
                params.append(int(employee_id))
            else:
                where_conditions.append("e.employee_id = ?")
                params.append(employee_id)
        
        if start_date:
            where_conditions.append("a.work_date >= ?")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("a.work_date <= ?")
            params.append(end_date)
        
        if employee_name:
            where_conditions.append("e.name LIKE ?")
            params.append(f"%{employee_name}%")
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢總數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE {where_clause}
        """
        
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查詢記錄
        query = f"""
            SELECT 
                a.id,
                a.employee_id,
                a.work_date,
                a.weekday,
                a.date_type,
                a.check_in,
                a.check_out,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.status,
                a.note,
                a.shift_id,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.leave_type
                END as leave_type,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.start_date
                END as leave_start_date,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.end_date
                END as leave_end_date,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.reason
                END as leave_reason
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN shifts s ON a.shift_id = s.id
            LEFT JOIN leaves l ON l.employee_id = a.employee_id 
                AND a.work_date BETWEEN l.start_date AND l.end_date
                AND l.status = 'approved'
                AND a.date_type = 'workday'
            WHERE {where_clause}
            ORDER BY a.work_date DESC, e.name
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(query, params + [per_page, offset])
        records = cursor.fetchall()
        
        # 轉換為字典列表並去除重複記錄
        records_dict = {}
        for record in records:
            record_dict = dict(record)
            # 計算加班時數
            if record_dict['overtime_minutes']:
                record_dict['overtime_hours'] = round(record_dict['overtime_minutes'] / 60, 2)
            else:
                record_dict['overtime_hours'] = 0
            
            # 使用 (employee_id, work_date) 作為唯一鍵
            key = (record_dict['employee_id'], record_dict['work_date'])
            
            # 如果已存在相同日期的記錄，合併請假資訊
            if key in records_dict:
                existing = records_dict[key]
                # 如果新記錄有請假類型而舊記錄沒有
                if record_dict.get('leave_type') and not existing.get('leave_type'):
                    records_dict[key] = record_dict
                elif record_dict.get('leave_type') and existing.get('leave_type'):
                    # 定義請假類型優先級（數字越小優先級越高）
                    leave_priority = {
                        '喪假': 1, '婚假': 2, '產假': 3, '陪產假': 4, 
                        '年假': 5, '特休': 6, '病假': 7, '事假': 8
                    }
                    new_priority = leave_priority.get(record_dict['leave_type'], 99)
                    existing_priority = leave_priority.get(existing['leave_type'], 99)
                    
                    # 選擇優先級較高的請假類型作為主要類型
                    if new_priority < existing_priority:
                        # 保留原有的leave_hours（已經是總和），但更新leave_type和leave_reason
                        existing['leave_type'] = record_dict['leave_type']
                        # 合併請假原因
                        if existing.get('leave_reason') and record_dict.get('leave_reason'):
                            if existing['leave_reason'] != record_dict['leave_reason']:
                                existing['leave_reason'] = f"{record_dict['leave_reason']}; {existing['leave_reason']}"
                        elif record_dict.get('leave_reason'):
                            existing['leave_reason'] = record_dict['leave_reason']
                    else:
                        # 保持現有的leave_type，但合併請假原因
                        if existing.get('leave_reason') and record_dict.get('leave_reason'):
                            if existing['leave_reason'] != record_dict['leave_reason']:
                                existing['leave_reason'] = f"{existing['leave_reason']}; {record_dict['leave_reason']}"
                        elif record_dict.get('leave_reason') and not existing.get('leave_reason'):
                            existing['leave_reason'] = record_dict['leave_reason']
                # 如果都沒有請假類型，保持原有記錄
            else:
                records_dict[key] = record_dict
        
        # 轉換回列表
        records_list = list(records_dict.values())
        
        conn.close()
        
        return jsonify({
            "success": True,
            "records": records_list,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        logger.error(f"查詢考勤記錄錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/records/<int:record_id>", methods=["GET"])
def get_attendance_record_detail(record_id):
    """
    獲取單個考勤記錄詳情
    """
    try:
        if record_id <= 0:
            return jsonify({"success": False, "error": "記錄ID必須是正整數"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.work_date,
                a.weekday,
                a.date_type,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.note,
                a.created_at,
                a.shift_id,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.leave_type
                END as leave_type,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.start_date
                END as leave_start_date,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.end_date
                END as leave_end_date,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL THEN NULL
                    ELSE l.reason
                END as leave_reason
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN shifts s ON a.shift_id = s.id
            LEFT JOIN leaves l ON l.employee_id = a.employee_id 
                AND a.work_date BETWEEN l.start_date AND l.end_date
                AND l.status = 'approved'
                AND a.date_type = 'workday'
            WHERE a.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            return jsonify({"success": False, "error": "找不到指定的考勤記錄"}), 404
        
        record_dict = dict(record)
        
        # 計算加班時數
        if record_dict['overtime_minutes']:
            record_dict['overtime_hours'] = round(record_dict['overtime_minutes'] / 60, 2)
        else:
            record_dict['overtime_hours'] = 0
        
        conn.close()
        
        return jsonify({
            "success": True,
            "record": record_dict
        })
        
    except Exception as e:
        logger.error(f"查詢考勤記錄詳情錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/management/update-shift", methods=["POST"])
def update_attendance_shift():
    """
    更新考勤記錄的班表
    """
    try:
        data = request.get_json()
        record_id = data.get('record_id')
        shift_id = data.get('shift_id')
        
        if not record_id or not shift_id:
            return jsonify({"success": False, "error": "記錄ID和班表ID不能為空"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 獲取考勤記錄
        cursor.execute("SELECT * FROM attendance WHERE id = ?", (record_id,))
        attendance_row = cursor.fetchone()
        
        if not attendance_row:
            return jsonify({"success": False, "error": "找不到考勤記錄"}), 404
        
        # 將資料庫行轉換為字典
        attendance = dict(attendance_row)
        
        # 獲取班表資訊（包含容許時間）
        cursor.execute("SELECT * FROM shifts WHERE id = ?", (shift_id,))
        shift_row = cursor.fetchone()
        
        if not shift_row:
            return jsonify({"success": False, "error": "找不到班表"}), 404
        
        # 將資料庫行轉換為字典
        shift = dict(shift_row)
        
        # 更新考勤記錄的班表ID
        logger.info(f"正在更新考勤記錄 {record_id} 的班表ID從 {attendance.get('shift_id')} 到 {shift_id}")
        cursor.execute("UPDATE attendance SET shift_id = ? WHERE id = ?", (shift_id, record_id))
        
        # 檢查更新是否成功
        cursor.execute("SELECT shift_id FROM attendance WHERE id = ?", (record_id,))
        updated_shift_id = cursor.fetchone()[0]
        logger.info(f"更新後的班表ID: {updated_shift_id}")
        
        # 重新計算考勤指標
        recalculated_data = recalculate_single_attendance(attendance, shift, cursor)
        
        # 確保事務提交
        conn.commit()
        logger.info(f"事務已提交，班表更新完成")
        
        # 最終驗證
        cursor.execute("SELECT shift_id FROM attendance WHERE id = ?", (record_id,))
        final_shift_id = cursor.fetchone()[0]
        logger.info(f"最終驗證班表ID: {final_shift_id}")
        
        conn.close()
        
        return jsonify({
            "success": True, 
            "message": "班表更新成功，已重新計算考勤數據",
            "recalculated_data": recalculated_data,
            "updated_shift_id": final_shift_id
        })
        
    except Exception as e:
        logger.error(f"更新班表錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def recalculate_single_attendance(attendance, shift, cursor):
    """
    重新計算單個考勤記錄的指標
    
    參數：
    attendance: 考勤記錄字典
    shift: 班表記錄字典
    cursor: 資料庫游標
    
    返回：
    dict: 重新計算後的數據
    """
    try:
        # 確保 attendance 是字典格式
        if not isinstance(attendance, dict):
            attendance = dict(attendance)
            
        # 確保 shift 是字典格式
        if not isinstance(shift, dict):
            shift = dict(shift)
            
        # 計算工時、遲到、早退、加班等
        check_in = attendance.get('check_in')
        check_out = attendance.get('check_out')
        
        if not check_in or not check_out:
            return {
                "status": "incomplete",
                "message": "缺少打卡記錄"
            }
        
        # 解析時間 - 修復時間格式解析問題
        try:
            # 嘗試使用 fromisoformat 解析
            check_in_time = datetime.fromisoformat(check_in.replace('Z', '+00:00'))
            check_out_time = datetime.fromisoformat(check_out.replace('Z', '+00:00'))
        except ValueError:
            # 如果失敗，使用 strptime 解析標準格式
            check_in_time = datetime.strptime(check_in, '%Y-%m-%d %H:%M:%S')
            check_out_time = datetime.strptime(check_out, '%Y-%m-%d %H:%M:%S')
        
        # 計算工時
        work_duration = check_out_time - check_in_time
        work_hours = work_duration.total_seconds() / 3600
        
        # 計算遲到（考慮容許時間）
        shift_start_parts = shift['start_time'].split(':')
        shift_start = time(int(shift_start_parts[0]), int(shift_start_parts[1]))
        actual_start = check_in_time.time()
        
        late_minutes = 0
        if actual_start > shift_start:
            # 使用同一天的日期進行計算，避免跨日問題
            today = datetime.today().date()
            late_delta = datetime.combine(today, actual_start) - datetime.combine(today, shift_start)
            actual_late_minutes = late_delta.total_seconds() / 60
            # 扣除容許時間
            late_tolerance = shift.get('late_tolerance_minutes', 5)
            late_minutes = max(0, actual_late_minutes - late_tolerance)
        
        # 計算早退（考慮容許時間）
        shift_end_parts = shift['end_time'].split(':')
        shift_end = time(int(shift_end_parts[0]), int(shift_end_parts[1]))
        actual_end = check_out_time.time()
        
        early_leave_minutes = 0
        if actual_end < shift_end:
            # 使用同一天的日期進行計算，避免跨日問題
            today = datetime.today().date()
            early_delta = datetime.combine(today, shift_end) - datetime.combine(today, actual_end)
            actual_early_minutes = early_delta.total_seconds() / 60
            # 扣除容許時間
            early_tolerance = shift.get('early_leave_tolerance_minutes', 5)
            early_leave_minutes = max(0, actual_early_minutes - early_tolerance)
        
        # 計算加班
        overtime_minutes = 0
        overtime_hours = 0
        if actual_end > shift_end:
            # 使用同一天的日期進行計算，避免跨日問題
            today = datetime.today().date()
            overtime_delta = datetime.combine(today, actual_end) - datetime.combine(today, shift_end)
            overtime_minutes = overtime_delta.total_seconds() / 60
            overtime_hours = overtime_minutes / 60
        
        # 計算請假小時
        leave_hours = 0
        work_date = attendance.get('work_date')
        employee_id = attendance.get('employee_id')
        
        if work_date and employee_id:
            # 查詢當天是否有已核准的請假申請
            cursor.execute("""
                SELECT SUM(
                    CASE 
                        WHEN time_type = 'full_day' THEN 8.0
                        WHEN time_type = 'partial_day' AND start_time IS NOT NULL AND end_time IS NOT NULL THEN
                            (CAST(SUBSTR(end_time, 1, 2) AS INTEGER) * 60 + CAST(SUBSTR(end_time, 4, 2) AS INTEGER) -
                             CAST(SUBSTR(start_time, 1, 2) AS INTEGER) * 60 - CAST(SUBSTR(start_time, 4, 2) AS INTEGER)) / 60.0
                        ELSE 0
                    END
                ) as total_leave_hours
                FROM leaves 
                WHERE employee_id = ? 
                    AND status = 'approved' 
                    AND ? BETWEEN start_date AND end_date
            """, (employee_id, work_date))
            
            result = cursor.fetchone()
            if result and result[0]:
                leave_hours = float(result[0])
        
        # 計算狀態
        status = 'normal'
        if leave_hours > 0:
            status = 'leave'
        elif late_minutes > 0:
            status = 'late'
        elif early_leave_minutes > 0:
            status = 'early_leave'
        elif not check_in or not check_out:
            status = 'absent'
        
        # 更新記錄
        try:
            cursor.execute("""
                UPDATE attendance 
                SET work_hours = ?, leave_hours = ?, late_minutes = ?, early_leave_minutes = ?, 
                    overtime_minutes = ?, status = ?
                WHERE id = ?
            """, (work_hours, leave_hours, late_minutes, early_leave_minutes, overtime_minutes, status, attendance['id']))
            logger.info(f"成功更新考勤記錄 {attendance['id']} 的計算結果，請假小時: {leave_hours}")
        except Exception as update_error:
            logger.error(f"更新考勤記錄失敗: {update_error}")
            raise update_error
        
        # 返回計算結果
        return {
            "shift_name": shift['name'],
            "shift_time": f"{shift['start_time']} - {shift['end_time']}",
            "work_hours": round(work_hours, 2),
            "leave_hours": round(leave_hours, 2),
            "late_minutes": int(late_minutes),
            "early_leave_minutes": int(early_leave_minutes),
            "overtime_minutes": int(overtime_minutes),
            "overtime_hours": round(overtime_hours, 2),
            "status": status,
            "check_in_time": check_in,
            "check_out_time": check_out
        }
        
    except Exception as e:
        logger.error(f"重新計算考勤指標錯誤: {e}")
        return {
            "status": "error",
            "message": f"計算錯誤: {str(e)}"
        }

@attendance_bp.route("/api/attendance/management/daily-completion-check", methods=["GET"])
def daily_attendance_completion_check():
    """
    檢查每日考勤完成度
    """
    try:
        date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 查詢當日考勤統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                SUM(CASE WHEN check_in IS NOT NULL AND check_out IS NOT NULL THEN 1 ELSE 0 END) as complete_records,
                SUM(CASE WHEN check_in IS NULL OR check_out IS NULL THEN 1 ELSE 0 END) as incomplete_records
            FROM attendance 
            WHERE work_date = ?
        """, (date,))
        
        stats = cursor.fetchone()
        conn.close()
        
        return jsonify({
            "success": True,
            "date": date,
            "stats": {
                "total_records": stats['total_records'] or 0,
                "complete_records": stats['complete_records'] or 0,
                "incomplete_records": stats['incomplete_records'] or 0,
                "completion_rate": round((stats['complete_records'] or 0) / max(stats['total_records'] or 1, 1) * 100, 2)
            }
        })
        
    except Exception as e:
        logger.error(f"檢查考勤完成度錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/recent", methods=["GET"])
def get_recent_attendance():
    """獲取最近考勤記錄"""
    try:
        limit = int(request.args.get('limit', 10))
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.work_date,
                a.check_in,
                a.check_out,
                a.status,
                e.name as employee_name,
                d.name as department_name
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            ORDER BY a.work_date DESC, a.check_in DESC
            LIMIT ?
        """
        
        cursor.execute(query, (limit,))
        records = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            "success": True,
            "recent_attendance": records
        })
        
    except Exception as e:
        logger.error(f"獲取最近考勤記錄錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/trends", methods=["GET"])
def get_attendance_trends():
    """獲取考勤趨勢數據"""
    try:
        days = int(request.args.get('days', 7))
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 獲取過去N天的考勤統計
        query = """
            SELECT 
                a.work_date,
                COUNT(*) as total_attendance,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
            FROM attendance a
            WHERE a.work_date >= date('now', '-{} days')
            GROUP BY a.work_date
            ORDER BY a.work_date
        """.format(days)
        
        cursor.execute(query)
        records = cursor.fetchall()
        
        # 格式化數據
        labels = []
        normal_data = []
        late_data = []
        
        for record in records:
            # 格式化日期為星期幾
            date_obj = datetime.strptime(record['work_date'], '%Y-%m-%d')
            weekday = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'][date_obj.weekday()]
            labels.append(weekday)
            normal_data.append(record['normal_count'])
            late_data.append(record['late_count'])
        
        conn.close()
        
        return jsonify({
            "success": True,
            "labels": labels,
            "normal": normal_data,
            "late": late_data
        })
        
    except Exception as e:
        logger.error(f"獲取考勤趨勢錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/today/<employee_code>", methods=["GET"])
def get_today_attendance(employee_code):
    """獲取員工今日考勤狀態"""
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 🔧 根據員工編號查找員工的數字ID
        cursor.execute("""
            SELECT id FROM employees WHERE employee_id = ?
        """, (employee_code,))
        
        employee = cursor.fetchone()
        if not employee:
            return jsonify({"success": False, "error": "員工不存在"}), 400
        
        employee_id = employee['id']  # 獲取數字ID
        
        today = datetime.now().date()
        query = """
            SELECT 
                id,
                employee_id,
                check_in,
                check_out,
                work_date,
                status
            FROM attendance
            WHERE employee_id = ? AND work_date = ?
        """
        
        cursor.execute(query, (employee_id, today))
        record = cursor.fetchone()
        
        conn.close()
        
        if record:
            return jsonify({
                "success": True,
                "data": dict(record)
            })
        else:
            return jsonify({
                "success": True,
                "data": None
            })
        
    except Exception as e:
        logger.error(f"獲取今日考勤狀態錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/today-punches/<int:employee_id>", methods=["GET"])
def get_today_punch_records(employee_id):
    """
    獲取員工今日所有打卡記錄
    
    功能說明：
    - 獲取指定員工今天的所有打卡記錄
    - 按時間順序排列
    - 用於線上打卡頁面顯示第一筆和最後一筆打卡時間
    
    參數：
    - employee_id: 員工ID
    
    返回：
    - 今日所有打卡記錄列表
    - 第一筆打卡時間（上班時間）
    - 最後一筆打卡時間（下班時間）
    """
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        today = datetime.now().date()
        
        # 查詢今日考勤記錄（從attendance表，只取有打卡時間的記錄）
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.work_date
            FROM attendance a
            WHERE a.employee_id = ? AND DATE(a.work_date) = ?
            AND (a.check_in IS NOT NULL OR a.check_out IS NOT NULL)
            ORDER BY a.id DESC
            LIMIT 1
        """
        
        cursor.execute(query, (employee_id, today))
        record = cursor.fetchone()
        
        # 構建打卡記錄列表
        punch_records = []
        first_punch = None
        last_punch = None
        
        if record:
            record_dict = dict(record)
            
            # 如果有上班打卡時間
            if record_dict['check_in']:
                first_punch = record_dict['check_in']
                punch_records.append({
                    'punch_datetime': record_dict['check_in'],
                    'type': 'check_in',
                    'status': '上班打卡'
                })
            
            # 如果有下班打卡時間
            if record_dict['check_out']:
                last_punch = record_dict['check_out']
                punch_records.append({
                    'punch_datetime': record_dict['check_out'],
                    'type': 'check_out',
                    'status': '下班打卡'
                })
        
        total_punches = len(punch_records)
        
        conn.close()
        
        return jsonify({
            "success": True,
            "punch_records": punch_records,
            "summary": {
                "total_punches": total_punches,
                "first_punch": first_punch,
                "last_punch": last_punch,
                "work_date": today.strftime('%Y-%m-%d')
            }
        })
        
    except Exception as e:
        logger.error(f"獲取今日打卡記錄錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/punch/records", methods=["GET"])
def get_punch_records():
    """
    查詢打卡原始記錄 API
    
    功能說明：
    - 支援分頁查詢打卡原始記錄（punch_records表）
    - 支援多種篩選條件（員工、部門、日期範圍、狀態）
    - 返回統計資訊和詳細記錄列表
    
    查詢參數：
    - page: 頁碼 (預設: 1, 最小值: 1)
    - limit: 每頁筆數 (預設: 20, 範圍: 1-100)
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - status_code: 打卡狀態代碼 (可選)
    
    返回：
    - 分頁的打卡原始記錄列表和統計資訊
    """
    try:
        logger.info("開始處理打卡原始記錄查詢請求")
        
        # ===== 參數解析與驗證 =====
        page = max(1, request.args.get('page', 1, type=int))
        limit = max(1, min(100, request.args.get('limit', 20, type=int)))
        employee_id = request.args.get('employee_id')  # 員工編號是字符串，不是整數
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status_code = request.args.get('status_code')
        
        logger.info(f"查詢參數驗證通過 - page: {page}, limit: {limit}, employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, status_code: {status_code}")
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        if employee_id:
            where_conditions.append("p.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        if start_date:
            where_conditions.append("DATE(p.punch_date) >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(p.punch_date) <= ?")
            query_params.append(end_date)
        
        if status_code:
            where_conditions.append("p.status_code = ?")
            query_params.append(status_code)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        # ===== 查詢總記錄數 =====
        count_query = f"""
            SELECT COUNT(*) as total
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        logger.debug(f"執行總數查詢 SQL: {count_query}")
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()['total']
        logger.info(f"查詢到總記錄數: {total_records}")
        
        # ===== 查詢詳細記錄 =====
        records_query = f"""
            SELECT 
                p.id,
                p.employee_id,
                p.device_id,
                p.punch_date,
                p.punch_time,
                p.punch_datetime,
                p.status_code,
                p.raw_data,
                p.processed,
                p.attendance_id,
                p.note,
                p.imported_at,
                p.processed_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
            ORDER BY p.punch_datetime DESC, p.id DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            # 格式化時間欄位
            if record.get('punch_datetime'):
                record['punch_datetime_formatted'] = record['punch_datetime']
            if record.get('imported_at'):
                record['imported_at_formatted'] = record['imported_at']
            if record.get('processed_at'):
                record['processed_at_formatted'] = record['processed_at']
            
            records.append(record)
        
        # ===== 計算統計資訊 =====
        stats_query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT p.employee_id) as total_employees,
                COUNT(DISTINCT DATE(p.punch_date)) as total_days,
                COUNT(CASE WHEN p.status_code = '0' THEN 1 END) as checkin_count,
                COUNT(CASE WHEN p.status_code = '1' THEN 1 END) as checkout_count,
                COUNT(CASE WHEN p.processed = 1 THEN 1 END) as processed_count,
                COUNT(CASE WHEN p.processed = 0 THEN 1 END) as unprocessed_count
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        cursor.execute(stats_query, query_params)
        stats_row = cursor.fetchone()
        
        statistics = {
            "total_records": stats_row['total_records'],
            "total_employees": stats_row['total_employees'],
            "total_days": stats_row['total_days'],
            "avg_records_per_day": round(stats_row['total_records'] / max(stats_row['total_days'], 1), 1),
            "status_breakdown": {
                "checkin": stats_row['checkin_count'],
                "checkout": stats_row['checkout_count'],
                "processed": stats_row['processed_count'],
                "unprocessed": stats_row['unprocessed_count']
            }
        }
        
        conn.close()
        
        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit
        
        # 構建響應資料
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "statistics": statistics,
            "query_info": {
                "filters_applied": len(where_conditions),
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        logger.info(f"打卡原始記錄查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        error_msg = f"查詢打卡原始記錄時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@attendance_bp.route("/api/punch/records/<int:record_id>", methods=["GET"])
def get_punch_record_detail(record_id):
    """
    獲取單個打卡原始記錄詳情 API
    
    功能說明：
    - 根據記錄ID獲取打卡原始記錄的詳細資訊
    - 包含員工資訊、部門資訊、打卡詳情等
    
    路徑參數：
    - record_id: 打卡記錄ID (必需, 整數)
    
    返回：
    - 打卡原始記錄的詳細資訊
    """
    try:
        logger.info(f"開始處理打卡原始記錄詳情查詢請求 - record_id: {record_id}")
        
        # ===== 參數驗證 =====
        if not isinstance(record_id, int) or record_id <= 0:
            return jsonify({
                "success": False,
                "error": "無效的記錄ID",
                "error_code": "INVALID_RECORD_ID"
            }), 400
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # ===== 查詢記錄詳情 =====
        query = """
            SELECT 
                p.id,
                p.employee_id,
                p.device_id,
                p.punch_date,
                p.punch_time,
                p.punch_datetime,
                p.status_code,
                p.raw_data,
                p.processed,
                p.attendance_id,
                p.note,
                p.imported_at,
                p.processed_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id,
                a.work_date as attendance_work_date,
                a.status as attendance_status
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN attendance a ON p.attendance_id = a.id
            WHERE p.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            conn.close()
            return jsonify({
                "success": False,
                "error": "找不到指定的打卡記錄",
                "error_code": "RECORD_NOT_FOUND"
            }), 404
        
        # 轉換為字典格式
        record_dict = dict(record)
        
        # 格式化時間欄位
        if record_dict.get('punch_datetime'):
            record_dict['punch_datetime_formatted'] = record_dict['punch_datetime']
        if record_dict.get('imported_at'):
            record_dict['imported_at_formatted'] = record_dict['imported_at']
        if record_dict.get('processed_at'):
            record_dict['processed_at_formatted'] = record_dict['processed_at']
        
        conn.close()
        
        logger.info(f"打卡原始記錄詳情查詢成功完成 - record_id: {record_id}")
        
        return jsonify({
            "success": True,
            "record": record_dict,
            "query_info": {
                "record_id": record_id,
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })
        
    except Exception as e:
        error_msg = f"查詢打卡原始記錄詳情時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@attendance_bp.route("/api/attendance/records/export", methods=["GET"])
def export_attendance_excel():
    """
    匯出考勤記錄為Excel檔案
    """
    try:
        # 獲取查詢參數
        employee_id = request.args.get('employee_id')
        department_id = request.args.get('department_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if employee_id:
            if str(employee_id).isdigit():
                where_conditions.append("a.employee_id = ?")
                params.append(int(employee_id))
            else:
                where_conditions.append("e.employee_id = ?")
                params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        if start_date:
            where_conditions.append("a.work_date >= ?")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("a.work_date <= ?")
            params.append(end_date)
        
        if status:
            where_conditions.append("a.status = ?")
            params.append(status)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢考勤記錄
        query = f"""
            SELECT 
                a.id,
                a.employee_id,
                a.work_date as '工作日期',
                CASE a.weekday
                    WHEN 1 THEN '週一'
                    WHEN 2 THEN '週二'
                    WHEN 3 THEN '週三'
                    WHEN 4 THEN '週四'
                    WHEN 5 THEN '週五'
                    WHEN 6 THEN '週六'
                    WHEN 7 THEN '週日'
                    ELSE '未知'
                END as '星期',
                a.date_type,
                e.name as '員工姓名',
                e.employee_id as '員工代碼',
                d.name as '部門',
                s.name as '班別',
                a.check_in as '上班時間',
                a.check_out as '下班時間',
                a.work_hours as '工作時數',
                a.late_minutes as '遲到分鐘',
                a.early_leave_minutes as '早退分鐘',
                a.overtime_minutes as '加班分鐘',
                a.leave_hours as '請假小時',
                COALESCE(lt.name, lt2.name, l.leave_type) as '請假類型',
                CASE a.status
                    WHEN 'normal' THEN '正常'
                    WHEN 'late' THEN '遲到'
                    WHEN 'early_leave' THEN '早退'
                    WHEN 'overtime' THEN '加班'
                    WHEN 'leave' THEN '請假'
                    WHEN 'absent' THEN '曠職'
                    WHEN 'holiday' THEN '休假'
                    WHEN 'weekend' THEN '週末'
                    WHEN 'incomplete' THEN '不完整'
                    ELSE a.status
                END as '狀態',
                a.note as '備註'
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN shifts s ON a.shift_id = s.id
            LEFT JOIN leaves l ON l.employee_id = a.employee_id 
                AND a.work_date BETWEEN l.start_date AND l.end_date
                AND l.status = 'approved'
                AND a.date_type = 'workday'
            LEFT JOIN leave_types lt ON l.leave_type = lt.code
            LEFT JOIN leave_types lt2 ON l.leave_type = lt2.name
            WHERE {where_clause}
            ORDER BY e.name ASC, a.work_date ASC
        """
        
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        if not records:
            return jsonify({"success": False, "error": "沒有找到考勤記錄"}), 404
        
        # 去除重複記錄並合併請假資訊（同一天多個請假記錄的問題）
        records_dict = {}
        for record in records:
            record_dict = dict(record)
            key = (record_dict['employee_id'], record_dict['工作日期'])
            
            if key in records_dict:
                existing = records_dict[key]
                # 如果新記錄有請假類型而舊記錄沒有
                if record_dict.get('請假類型') and not existing.get('請假類型'):
                    records_dict[key] = record_dict
                elif record_dict.get('請假類型') and existing.get('請假類型'):
                    # 定義請假類型優先級（數字越小優先級越高）
                    leave_priority = {
                        '喪假': 1, '婚假': 2, '產假': 3, '陪產假': 4, 
                        '年假': 5, '特休': 6, '病假': 7, '事假': 8
                    }
                    new_priority = leave_priority.get(record_dict['請假類型'], 99)
                    existing_priority = leave_priority.get(existing['請假類型'], 99)
                    
                    # 選擇優先級較高的請假類型作為主要類型
                    if new_priority < existing_priority:
                        # 保留原有的請假小時（已經是總和），但更新請假類型
                        existing['請假類型'] = record_dict['請假類型']
                    # 注意：Excel匯出沒有請假原因欄位，所以不需要合併原因
            else:
                records_dict[key] = record_dict
        
        # 轉換回列表並按員工姓名和日期排序
        records = sorted(records_dict.values(), key=lambda x: (x['員工姓名'], x['工作日期']))
        
        # 創建Excel檔案
        buffer = io.BytesIO()
        workbook = xlsxwriter.Workbook(buffer, {'in_memory': True})
        worksheet = workbook.add_worksheet('考勤記錄')
        
        # 設定格式
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        # 寫入標題行
        headers = [
            '工作日期', '星期', '員工姓名', '員工代碼', '部門', '班別',
            '上班時間', '下班時間', '工作時數', '遲到分鐘', '早退分鐘',
            '加班一', '加班二', '請假小時', '請假類型', '狀態', '備註'
        ]
        
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # 寫入數據行
        for row, record in enumerate(records, 1):
            # 格式化時間欄位
            # 處理上班時間顯示
            if record['上班時間'] and ' ' in str(record['上班時間']):
                check_in = record['上班時間'].split(' ')[1]
            elif record['上班時間']:
                check_in = record['上班時間']
            elif record['date_type'] == 'weekend' or record['狀態'] in ['週末', '休假']:
                check_in = '假日'
            else:
                check_in = '未打卡'
            
            # 處理下班時間顯示
            if record['下班時間'] and ' ' in str(record['下班時間']):
                check_out = record['下班時間'].split(' ')[1]
            elif record['下班時間']:
                check_out = record['下班時間']
            elif record['date_type'] == 'weekend' or record['狀態'] in ['週末', '休假']:
                check_out = '假日'
            else:
                check_out = '未打卡'
            
            # 計算加班一和加班二
            overtime_minutes = record.get('加班分鐘', 0) or 0
            if overtime_minutes > 0:
                # 加班一：前2小時（120分鐘）
                overtime1_minutes = min(overtime_minutes, 120)
                overtime1 = f"{overtime1_minutes/60:.1f}時" if overtime1_minutes > 0 else ''
                
                # 加班二：第3小時以後
                overtime2_minutes = max(0, overtime_minutes - 120)
                overtime2 = f"{overtime2_minutes/60:.1f}時" if overtime2_minutes > 0 else ''
            else:
                overtime1 = ''
                overtime2 = ''
            
            data = [
                record['工作日期'],
                record['星期'],
                record['員工姓名'],
                record['員工代碼'],
                record['部門'],
                record['班別'],
                check_in,
                check_out,
                record['工作時數'],
                record['遲到分鐘'],
                record['早退分鐘'],
                overtime1,
                overtime2,
                record['請假小時'],
                record['請假類型'],
                record['狀態'],
                record['備註']
            ]
            
            for col, value in enumerate(data):
                worksheet.write(row, col, value or '', cell_format)
        
        # 調整欄寬（新增加班二欄位）
        column_widths = [12, 10, 10, 12, 10, 10, 10, 8, 8, 8, 8, 8, 8, 10, 8, 15]
        for col, width in enumerate(column_widths):
            worksheet.set_column(col, col, width)
        
        workbook.close()
        buffer.seek(0)
        conn.close()
        
        # 設定檔案名稱
        if employee_id:
            # 獲取員工姓名
            conn = create_connection()
            cursor = conn.cursor()
            if str(employee_id).isdigit():
                cursor.execute("SELECT name FROM employees WHERE id = ?", (int(employee_id),))
            else:
                cursor.execute("SELECT name FROM employees WHERE employee_id = ?", (employee_id,))
            employee = cursor.fetchone()
            employee_name = employee[0] if employee else f"員工{employee_id}"
            conn.close()
            filename = f"考勤記錄_{employee_name}_{start_date}_{end_date}.xlsx"
        else:
            filename = f"考勤記錄_{start_date}_{end_date}.xlsx"
        
        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.ms-excel'
        )
        
    except Exception as e:
        logger.error(f"Excel匯出錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@attendance_bp.route("/api/attendance/records/export-pdf", methods=["GET"])
def export_attendance_pdf():
    """
    產生考勤記錄PDF報表
    每個A4頁面顯示一個員工的資料，包含統計摘要
    """
    try:
        # 獲取查詢參數
        employee_id = request.args.get('employee_id')
        department_id = request.args.get('department_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        
        # 如果沒有指定員工，則生成所有員工的PDF
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if employee_id:
            if str(employee_id).isdigit():
                where_conditions.append("a.employee_id = ?")
                params.append(int(employee_id))
            else:
                where_conditions.append("e.employee_id = ?")
                params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        if start_date:
            where_conditions.append("a.work_date >= ?")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("a.work_date <= ?")
            params.append(end_date)
        
        if status:
            where_conditions.append("a.status = ?")
            params.append(status)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢考勤記錄
        query = f"""
            SELECT 
                a.id,
                a.employee_id,
                a.work_date,
                a.weekday,
                a.date_type,
                a.check_in,
                a.check_out,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.status,
                a.note,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time,
                COALESCE(lt.name, lt2.name, l.leave_type) as leave_type,
                l.reason as leave_reason
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN shifts s ON a.shift_id = s.id
            LEFT JOIN leaves l ON l.employee_id = a.employee_id 
                AND a.work_date BETWEEN l.start_date AND l.end_date
                AND l.status = 'approved'
                AND a.date_type = 'workday'
            LEFT JOIN leave_types lt ON l.leave_type = lt.code
            LEFT JOIN leave_types lt2 ON l.leave_type = lt2.name
            WHERE {where_clause}
            ORDER BY e.name ASC, a.work_date ASC
        """
        
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        if not records:
            return jsonify({"success": False, "error": "沒有找到考勤記錄"}), 404
        
        # 去除重複記錄並合併請假資訊（同一天多個請假記錄的問題）
        records_dict = {}
        for record in records:
            record_dict = dict(record)
            key = (record_dict['employee_id'], record_dict['work_date'])
            
            if key in records_dict:
                existing = records_dict[key]
                # 如果新記錄有請假類型而舊記錄沒有
                if record_dict.get('leave_type') and not existing.get('leave_type'):
                    records_dict[key] = record_dict
                elif record_dict.get('leave_type') and existing.get('leave_type'):
                    # 定義請假類型優先級（數字越小優先級越高）
                    leave_priority = {
                        '喪假': 1, '婚假': 2, '產假': 3, '陪產假': 4, 
                        '年假': 5, '特休': 6, '病假': 7, '事假': 8
                    }
                    new_priority = leave_priority.get(record_dict['leave_type'], 99)
                    existing_priority = leave_priority.get(existing['leave_type'], 99)
                    
                    # 選擇優先級較高的請假類型作為主要類型
                    if new_priority < existing_priority:
                        # 保留原有的leave_hours（已經是總和），但更新leave_type和leave_reason
                        existing['leave_type'] = record_dict['leave_type']
                        # 合併請假原因
                        if existing.get('leave_reason') and record_dict.get('leave_reason'):
                            if existing['leave_reason'] != record_dict['leave_reason']:
                                existing['leave_reason'] = f"{record_dict['leave_reason']}; {existing['leave_reason']}"
                        elif record_dict.get('leave_reason'):
                            existing['leave_reason'] = record_dict['leave_reason']
                    else:
                        # 保持現有的leave_type，但合併請假原因
                        if existing.get('leave_reason') and record_dict.get('leave_reason'):
                            if existing['leave_reason'] != record_dict['leave_reason']:
                                existing['leave_reason'] = f"{existing['leave_reason']}; {record_dict['leave_reason']}"
                        elif record_dict.get('leave_reason') and not existing.get('leave_reason'):
                            existing['leave_reason'] = record_dict['leave_reason']
            else:
                records_dict[key] = record_dict
        
        # 按員工分組記錄
        employees_data = {}
        for record_dict in records_dict.values():
            emp_id = record_dict['employee_id']
            
            if emp_id not in employees_data:
                employees_data[emp_id] = {
                    'employee_name': record_dict['employee_name'],
                    'employee_code': record_dict['employee_code'],
                    'department_name': record_dict['department_name'],
                    'records': [],
                    'total_late_minutes': 0,
                    'total_early_minutes': 0,
                    'total_overtime_minutes': 0,
                    'total_leave_hours': 0,
                    'leave_types_stats': {}
                }
            
            employees_data[emp_id]['records'].append(record_dict)
            
            # 累計統計
            employees_data[emp_id]['total_late_minutes'] += record_dict.get('late_minutes', 0) or 0
            employees_data[emp_id]['total_early_minutes'] += record_dict.get('early_leave_minutes', 0) or 0
            employees_data[emp_id]['total_overtime_minutes'] += record_dict.get('overtime_minutes', 0) or 0
            employees_data[emp_id]['total_leave_hours'] += record_dict.get('leave_hours', 0) or 0
            
            # 假別統計
            if record_dict.get('leave_type'):
                leave_type = record_dict['leave_type']
                leave_hours = record_dict.get('leave_hours', 0) or 0
                if leave_type in employees_data[emp_id]['leave_types_stats']:
                    employees_data[emp_id]['leave_types_stats'][leave_type] += leave_hours
                else:
                    employees_data[emp_id]['leave_types_stats'][leave_type] = leave_hours
        
        conn.close()
        
        # 註冊中文字體
        chinese_font = 'Helvetica'  # 預設字體
        try:
            # 嘗試註冊中文字體
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',  # macOS
                '/System/Library/Fonts/STHeiti Light.ttc',  # macOS 黑體
                '/System/Library/Fonts/Hiragino Sans GB.ttc',  # macOS
                'C:/Windows/Fonts/msyh.ttc',  # Windows 微軟雅黑
                'C:/Windows/Fonts/simsun.ttc',  # Windows 宋體
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        # 檢查是否已經註冊過
                        registered_fonts = pdfmetrics.getRegisteredFontNames()
                        if 'ChineseFont' not in registered_fonts:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        chinese_font = 'ChineseFont'
                        logger.info(f"成功註冊中文字體: {font_path}")
                        break
                    except Exception as font_error:
                        logger.warning(f"字體註冊失敗 {font_path}: {font_error}")
                        continue
                        
        except Exception as e:
            logger.error(f"字體註冊過程出錯: {e}")
            
        logger.info(f"使用字體: {chinese_font}")
        
        # 生成PDF - 縮小邊距以容納更多內容
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.3*inch, bottomMargin=0.3*inch, leftMargin=0.3*inch, rightMargin=0.3*inch)
        
        # 建立內容列表
        story = []
        
        # 為每個員工生成一頁
        for emp_id, emp_data in employees_data.items():
            # 員工資訊（移除大標題，直接顯示員工資訊）
            info_style = ParagraphStyle(
                'InfoStyle',
                parent=getSampleStyleSheet()['Normal'],
                fontSize=10,
                spaceAfter=12,
                fontName=chinese_font
            )
            
            info_text = f"""
            員工姓名：{emp_data['employee_name']} ({emp_data['employee_code']})<br/>
            部門：{emp_data['department_name']}<br/>
            報表期間：{start_date} 至 {end_date}<br/>
            產生時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            info_para = Paragraph(info_text, info_style)
            story.append(info_para)
            
            # 詳細記錄表格
            detail_data = [
                ['日期', '星期', '班別', '出勤', '實際到班', '實際下班', '遲到', '早退', '加班一', '加班二', '請假', '請假小時', '狀態']
            ]
            
            for record in emp_data['records']:
                work_date = record['work_date'] if record['work_date'] else '-'
                # 轉換星期數字為中文
                weekday_text = '-'
                if record['weekday']:
                    weekday_map = {1: '週一', 2: '週二', 3: '週三', 4: '週四', 5: '週五', 6: '週六', 7: '週日'}
                    weekday_text = weekday_map.get(record['weekday'], '未知')
                
                shift_name = record['shift_name'] if record['shift_name'] else '未設定'
                
                # 處理上班時間顯示
                if record['check_in']:
                    check_in = record['check_in'].split(' ')[1]
                elif record['date_type'] == 'weekend' or record['status'] in ['weekend', 'holiday']:
                    check_in = '假日'
                else:
                    check_in = '未打卡'
                
                # 處理下班時間顯示
                if record['check_out']:
                    check_out = record['check_out'].split(' ')[1]
                elif record['date_type'] == 'weekend' or record['status'] in ['weekend', 'holiday']:
                    check_out = '假日'
                else:
                    check_out = '未打卡'
                
                late_min = f"{record['late_minutes']}分" if record['late_minutes'] and record['late_minutes'] > 0 else '-'
                early_min = f"{record['early_leave_minutes']}分" if record['early_leave_minutes'] and record['early_leave_minutes'] > 0 else '-'
                
                # 計算加班一和加班二
                overtime_minutes = record['overtime_minutes'] or 0
                if overtime_minutes > 0:
                    # 加班一：前2小時（120分鐘）
                    overtime1_minutes = min(overtime_minutes, 120)
                    overtime1 = f"{overtime1_minutes/60:.1f}時" if overtime1_minutes > 0 else '-'
                    
                    # 加班二：第3小時以後
                    overtime2_minutes = max(0, overtime_minutes - 120)
                    overtime2 = f"{overtime2_minutes/60:.1f}時" if overtime2_minutes > 0 else '-'
                else:
                    overtime1 = '-'
                    overtime2 = '-'
                
                # 顯示請假類型名稱（不是代碼）
                leave_type_name = record['leave_type'] if record['leave_type'] else '-'
                leave_hours = f"{record['leave_hours']}時" if record['leave_hours'] and record['leave_hours'] > 0 else '-'
                status = get_status_text_for_pdf(record['status'])
                
                # 判斷出勤狀態
                if record.get('check_in') or record.get('check_out'):
                    attendance_status = '✓'  # 有打卡記錄
                elif record['date_type'] == 'weekend' or record['status'] in ['weekend', 'holiday']:
                    attendance_status = '-'  # 假日
                else:
                    attendance_status = '✗'  # 未出勤
                
                detail_data.append([
                    work_date, weekday_text, shift_name, attendance_status, check_in, check_out, 
                    late_min, early_min, overtime1, overtime2, leave_type_name, leave_hours, status
                ])
            
            # 計算總加班一和加班二
            total_overtime1_minutes = 0
            total_overtime2_minutes = 0
            for record in emp_data['records']:
                overtime_minutes = record.get('overtime_minutes', 0) or 0
                if overtime_minutes > 0:
                    total_overtime1_minutes += min(overtime_minutes, 120)
                    total_overtime2_minutes += max(0, overtime_minutes - 120)
            
            # 計算實際出勤天數（有打卡記錄的天數）
            actual_attendance_days = 0
            for record in emp_data['records']:
                # 如果有上班打卡或下班打卡，就算出勤
                if record.get('check_in') or record.get('check_out'):
                    actual_attendance_days += 1
            
            # 添加統計合計行
            detail_data.append([
                '合計', 
                '-',
                f'{len(emp_data["records"])}天', 
                f'{actual_attendance_days}天',  # 實際出勤天數
                '-', 
                f'{emp_data["total_late_minutes"]}分' if emp_data["total_late_minutes"] > 0 else '-', 
                f'{emp_data["total_early_minutes"]}分' if emp_data["total_early_minutes"] > 0 else '-', 
                f'{total_overtime1_minutes/60:.1f}時' if total_overtime1_minutes > 0 else '-',
                f'{total_overtime2_minutes/60:.1f}時' if total_overtime2_minutes > 0 else '-',
                '-', 
                f'{emp_data["total_leave_hours"]}時' if emp_data["total_leave_hours"] > 0 else '-', 
                '-'
            ])
            
            # 計算合計行的起始位置
            summary_start_row = len(emp_data['records']) + 1
            
            # 優化欄位寬度以適應A4頁面，總寬度約7.5英寸（新增出勤欄位）
            detail_table = Table(detail_data, colWidths=[0.45*inch, 0.3*inch, 0.45*inch, 0.3*inch, 0.55*inch, 0.55*inch, 0.3*inch, 0.3*inch, 0.3*inch, 0.3*inch, 0.4*inch, 0.45*inch, 0.4*inch])
            detail_table.setStyle(TableStyle([
                # 標題行樣式
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), chinese_font),
                ('FONTSIZE', (0, 0), (-1, 0), 7),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
                ('TOPPADDING', (0, 0), (-1, 0), 4),
                
                # 資料行樣式
                ('BACKGROUND', (0, 1), (-1, summary_start_row-1), colors.beige),
                ('FONTNAME', (0, 1), (-1, summary_start_row-1), chinese_font),
                ('FONTSIZE', (0, 1), (-1, summary_start_row-1), 6),
                ('BOTTOMPADDING', (0, 1), (-1, summary_start_row-1), 2),
                ('TOPPADDING', (0, 1), (-1, summary_start_row-1), 2),
                
                # 合計行樣式
                ('BACKGROUND', (0, summary_start_row), (-1, -1), colors.lightblue),
                ('FONTNAME', (0, summary_start_row), (-1, -1), chinese_font),
                ('FONTSIZE', (0, summary_start_row), (-1, -1), 7),
                ('TEXTCOLOR', (0, summary_start_row), (-1, -1), colors.black),
                ('BOTTOMPADDING', (0, summary_start_row), (-1, -1), 3),
                ('TOPPADDING', (0, summary_start_row), (-1, -1), 3),
                
                # 邊框和對齊
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(detail_table)
            
            # 添加請假統計表格
            if emp_data['leave_types_stats']:
                # 添加間距
                from reportlab.platypus import Spacer
                story.append(Spacer(1, 0.2*inch))
                
                # 固定的請假類型順序
                fixed_leave_types = ['病假', '事假', '年假', '特休', '婚假', '喪假', '產假', '陪產假']
                
                # 創建請假統計表格
                leave_stats_data = [['請假類型統計', '時數']]
                
                for leave_type in fixed_leave_types:
                    hours = emp_data['leave_types_stats'].get(leave_type, 0)
                    leave_stats_data.append([leave_type, f'{hours}時' if hours > 0 else '0時'])
                
                # 添加其他未列出的請假類型
                for leave_type, hours in emp_data['leave_types_stats'].items():
                    if leave_type not in fixed_leave_types:
                        leave_stats_data.append([leave_type, f'{hours}時'])
                
                leave_stats_table = Table(leave_stats_data, colWidths=[2*inch, 1*inch])
                leave_stats_table.setStyle(TableStyle([
                    # 標題行樣式
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), chinese_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
                    ('TOPPADDING', (0, 0), (-1, 0), 4),
                    
                    # 資料行樣式
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('FONTNAME', (0, 1), (-1, -1), chinese_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 2),
                    ('TOPPADDING', (0, 1), (-1, -1), 2),
                    
                    # 邊框
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))
                
                story.append(leave_stats_table)
            
            # 如果不是最後一個員工，添加分頁符
            if emp_id != list(employees_data.keys())[-1]:
                from reportlab.platypus import PageBreak
                story.append(PageBreak())
        
        # 建立PDF
        doc.build(story)
        buffer.seek(0)
        
        # 設定檔案名稱
        if employee_id:
            # 單一員工
            first_emp = list(employees_data.values())[0]
            filename = f"考勤報表_{first_emp['employee_name']}_{start_date}_{end_date}.pdf"
        else:
            # 所有員工
            filename = f"考勤報表_全員_{start_date}_{end_date}.pdf"
        
        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )
        
    except Exception as e:
        logger.error(f"PDF生成錯誤: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def generate_full_month_dates(start_date, end_date):
    """
    生成完整月份的日期列表，包含星期幾和日期類型
    
    返回格式：
    [
        {
            'date': '2025-06-01',
            'weekday': '週一',
            'weekday_num': 0,  # 0=週一, 6=週日
            'date_type': 'workday',  # workday, weekend, holiday
            'is_weekend': False
        },
        ...
    ]
    """
    from datetime import datetime, timedelta
    
    # 解析開始和結束日期
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    # 如果是查詢整月，則從月初到月底
    if start.day == 1:
        # 獲取該月的最後一天
        year = start.year
        month = start.month
        last_day = calendar.monthrange(year, month)[1]
        end = datetime(year, month, last_day)
    
    dates = []
    current = start
    
    weekday_names = ['週一', '週二', '週三', '週四', '週五', '週六', '週日']
    
    while current <= end:
        weekday_num = current.weekday()  # 0=週一, 6=週日
        weekday_name = weekday_names[weekday_num]
        is_weekend = weekday_num >= 5  # 週六、週日
        
        # 判斷日期類型
        if is_weekend:
            date_type = 'weekend'
        else:
            date_type = 'workday'
        
        dates.append({
            'date': current.strftime('%Y-%m-%d'),
            'weekday': weekday_name,
            'weekday_num': weekday_num,
            'date_type': date_type,
            'is_weekend': is_weekend
        })
        
        current += timedelta(days=1)
    
    return dates

def get_status_text_for_pdf(status):
    """PDF用的狀態文字轉換"""
    statuses = {
        'normal': '正常',
        'late': '遲到',
        'early_leave': '早退',
        'overtime': '加班',
        'leave': '請假',
        'absent': '曠職',
        'holiday': '休假',
        'weekend': '週末',
        'incomplete': '不完整'
    }
    return statuses.get(status, status)