# Han AttendanceOS v2005.6.12 - UI設計系統指南

## 📋 概述

本文檔介紹遠漢科技考勤系統的統一UI設計系統，旨在確保整個系統的視覺一致性和用戶體驗品質。

## 🎨 設計理念

### 核心原則
- **簡潔至上**：遵循Apple設計語言的簡潔原則
- **人性化交互**：注重用戶體驗和操作流暢性  
- **視覺層次**：清晰的信息架構和視覺引導
- **品牌一致性**：統一的色彩、字體和組件風格

### 設計目標
- 提升用戶體驗和操作效率
- 降低開發和維護成本
- 建立專業的品牌形象
- 確保無障礙設計標準

## 📁 文件結構

```
static/css/
├── design-system.css    # 主要設計系統文件
├── colors.css          # 色彩系統
├── typography.css      # 字體系統
├── buttons.css         # 按鈕組件
├── forms.css          # 表單組件
└── animations.css     # 動畫效果
```

## 🎯 使用方法

### 1. 引入設計系統

在HTML頁面中引入主要設計系統文件：

```html
<link rel="stylesheet" href="/static/css/design-system.css">
```

設計系統會自動載入所有子組件。

### 2. 基礎HTML結構

```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Han AttendanceOS</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
</head>
<body>
    <div class="container">
        <!-- 頁面內容 -->
    </div>
</body>
</html>
```

## 🎨 色彩系統

### 主色調
```css
/* 遠漢科技品牌藍 */
--color-primary-500: #3b82f6;  /* 主色調 */
--color-primary-600: #2563eb;  /* 深色變體 */
--color-primary-700: #1d4ed8;  /* 更深色變體 */
```

### 功能色彩
```css
/* 考勤專用色彩 */
--color-attendance-present: #22c55e;   /* 出勤 - 綠色 */
--color-attendance-absent: #ef4444;    /* 缺勤 - 紅色 */
--color-attendance-late: #f59e0b;      /* 遲到 - 黃色 */
--color-attendance-overtime: #a855f7;  /* 加班 - 紫色 */
```

### 使用範例
```html
<!-- 背景色彩 -->
<div class="bg-primary">主要背景</div>
<div class="bg-success">成功背景</div>
<div class="bg-warning">警告背景</div>

<!-- 文字色彩 -->
<p class="text-primary">主要文字</p>
<p class="text-success">成功文字</p>
<p class="text-error">錯誤文字</p>

<!-- 邊框色彩 -->
<div class="border border-primary">主要邊框</div>
```

## ✍️ 字體系統

### 字體族
```css
--font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto'...
--font-mono: 'SF Mono', 'Monaco', 'Inconsolata'...
```

### 標題層級
```html
<h1 class="heading-1">主標題 (48px)</h1>
<h2 class="heading-2">次標題 (36px)</h2>
<h3 class="heading-3">三級標題 (30px)</h3>
<h4 class="heading-4">四級標題 (24px)</h4>
<h5 class="heading-5">五級標題 (20px)</h5>
<h6 class="heading-6">六級標題 (18px)</h6>
```

### 段落樣式
```html
<p class="paragraph">標準段落文字</p>
<p class="paragraph-large">大段落文字</p>
<p class="paragraph-small">小段落文字</p>
<p class="caption">說明文字</p>
```

### 工具類別
```html
<!-- 字體大小 -->
<span class="text-xs">超小文字 (12px)</span>
<span class="text-sm">小文字 (14px)</span>
<span class="text-base">標準文字 (16px)</span>
<span class="text-lg">大文字 (18px)</span>

<!-- 字重 -->
<span class="font-light">細體</span>
<span class="font-normal">標準</span>
<span class="font-medium">中等</span>
<span class="font-semibold">半粗體</span>
<span class="font-bold">粗體</span>

<!-- 文字對齊 -->
<p class="text-left">左對齊</p>
<p class="text-center">置中對齊</p>
<p class="text-right">右對齊</p>
```

## 🔘 按鈕系統

### 基礎按鈕
```html
<!-- 主要按鈕 -->
<button class="btn btn-primary">主要操作</button>
<button class="btn btn-secondary">次要操作</button>
<button class="btn btn-success">確認操作</button>
<button class="btn btn-warning">警告操作</button>
<button class="btn btn-danger">危險操作</button>
```

### 按鈕尺寸
```html
<button class="btn btn-primary btn-xs">超小按鈕</button>
<button class="btn btn-primary btn-sm">小按鈕</button>
<button class="btn btn-primary">標準按鈕</button>
<button class="btn btn-primary btn-lg">大按鈕</button>
<button class="btn btn-primary btn-xl">超大按鈕</button>
```

### 輪廓按鈕
```html
<button class="btn btn-outline-primary">主要輪廓</button>
<button class="btn btn-outline-secondary">次要輪廓</button>
<button class="btn btn-ghost">幽靈按鈕</button>
<button class="btn btn-link">鏈接按鈕</button>
```

### 特殊按鈕
```html
<!-- 圓形按鈕 -->
<button class="btn btn-primary btn-circle">
    <i class="icon-plus"></i>
</button>

<!-- 載入狀態 -->
<button class="btn btn-primary btn-loading">載入中...</button>

<!-- 全寬按鈕 -->
<button class="btn btn-primary btn-block">全寬按鈕</button>

<!-- 浮動操作按鈕 -->
<button class="btn-fab">
    <i class="icon-plus"></i>
</button>
```

### 按鈕組
```html
<div class="btn-group">
    <button class="btn btn-outline-primary">左</button>
    <button class="btn btn-outline-primary">中</button>
    <button class="btn btn-outline-primary">右</button>
</div>
```

## 📝 表單系統

### 基礎表單
```html
<form class="form">
    <div class="form-group">
        <label class="form-label required">員工姓名</label>
        <input type="text" class="form-input" placeholder="請輸入員工姓名">
        <div class="form-help">請輸入完整的中文姓名</div>
    </div>
    
    <div class="form-group">
        <label class="form-label">部門</label>
        <select class="form-select">
            <option>請選擇部門</option>
            <option>技術部</option>
            <option>業務部</option>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label">備註</label>
        <textarea class="form-textarea" placeholder="請輸入備註"></textarea>
    </div>
</form>
```

### 表單尺寸
```html
<input type="text" class="form-input form-input-sm" placeholder="小輸入框">
<input type="text" class="form-input" placeholder="標準輸入框">
<input type="text" class="form-input form-input-lg" placeholder="大輸入框">
```

### 複選框和單選框
```html
<!-- 複選框 -->
<div class="form-checkbox">
    <input type="checkbox" id="agree">
    <label for="agree">我同意服務條款</label>
</div>

<!-- 單選框 -->
<div class="form-radio">
    <input type="radio" id="male" name="gender" value="male">
    <label for="male">男性</label>
</div>
<div class="form-radio">
    <input type="radio" id="female" name="gender" value="female">
    <label for="female">女性</label>
</div>
```

### 開關按鈕
```html
<div class="form-switch">
    <input type="checkbox" id="notifications">
    <label for="notifications">接收通知</label>
</div>
```

### 文件上傳
```html
<div class="form-file">
    <input type="file" id="avatar">
    <label for="avatar" class="form-file-label">
        <i class="icon-upload"></i>
        選擇文件
    </label>
</div>
```

### 輸入組合
```html
<div class="input-group">
    <div class="input-group-prepend">$</div>
    <input type="number" class="form-input" placeholder="金額">
    <div class="input-group-append">.00</div>
</div>
```

### 表單狀態
```html
<!-- 成功狀態 -->
<div class="form-group">
    <input type="text" class="form-input is-valid" value="正確的輸入">
    <div class="form-success">輸入正確</div>
</div>

<!-- 錯誤狀態 -->
<div class="form-group">
    <input type="text" class="form-input is-invalid" value="錯誤的輸入">
    <div class="form-error">請輸入有效的資料</div>
</div>
```

### 表單佈局
```html
<!-- 水平佈局 -->
<form class="form form-horizontal">
    <div class="form-group">
        <label class="form-label">姓名</label>
        <div class="form-control">
            <input type="text" class="form-input">
        </div>
    </div>
</form>

<!-- 內聯佈局 -->
<form class="form form-inline">
    <div class="form-group">
        <label class="form-label">搜尋</label>
        <input type="text" class="form-input">
    </div>
    <button class="btn btn-primary">搜尋</button>
</form>

<!-- 行佈局 -->
<div class="form-row">
    <div class="form-col">
        <input type="text" class="form-input" placeholder="姓">
    </div>
    <div class="form-col">
        <input type="text" class="form-input" placeholder="名">
    </div>
</div>
```

## 🎬 動畫系統

### 基礎動畫
```html
<!-- 淡入效果 -->
<div class="animate-fade-in">淡入內容</div>
<div class="animate-fade-in-up">從下方淡入</div>
<div class="animate-fade-in-left">從左側淡入</div>

<!-- 縮放效果 -->
<div class="animate-scale-in">縮放進入</div>
<div class="animate-zoom-in">放大進入</div>

<!-- 滑動效果 -->
<div class="animate-slide-in-up">從下方滑入</div>
<div class="animate-slide-in-right">從右側滑入</div>
```

### 循環動畫
```html
<!-- 旋轉載入 -->
<div class="animate-spin">旋轉元素</div>

<!-- 脈衝效果 -->
<div class="animate-pulse">脈衝元素</div>

<!-- 彈跳效果 -->
<div class="animate-bounce">彈跳元素</div>
```

### 懸停效果
```html
<div class="hover-lift">懸停上升</div>
<div class="hover-grow">懸停放大</div>
<div class="hover-glow">懸停發光</div>
```

### 載入狀態
```html
<!-- 旋轉載入器 -->
<div class="loading-spinner"></div>

<!-- 點狀載入器 -->
<div class="loading-dots"></div>

<!-- 條狀載入器 -->
<div class="loading-bars"></div>
```

### 動畫控制
```html
<!-- 延遲動畫 -->
<div class="animate-fade-in animate-delay-200">延遲200ms</div>

<!-- 自定義持續時間 -->
<div class="animate-scale-in animate-duration-500">持續500ms</div>
```

## 📐 間距系統

### CSS變數
```css
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
--spacing-2xl: 3rem;      /* 48px */
--spacing-3xl: 4rem;      /* 64px */
```

### 使用範例
```css
.custom-component {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-sm);
}
```

## 🔄 圓角系統

### CSS變數
```css
--radius-sm: 0.25rem;     /* 4px */
--radius-md: 0.5rem;      /* 8px */
--radius-lg: 0.75rem;     /* 12px */
--radius-xl: 1rem;        /* 16px */
--radius-2xl: 1.5rem;     /* 24px */
--radius-full: 9999px;    /* 完全圓角 */
```

## 🌑 陰影系統

### CSS變數
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
--shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
```

## 📱 響應式設計

### 斷點系統
```css
--breakpoint-sm: 640px;   /* 小屏幕 */
--breakpoint-md: 768px;   /* 平板 */
--breakpoint-lg: 1024px;  /* 桌面 */
--breakpoint-xl: 1280px;  /* 大桌面 */
--breakpoint-2xl: 1536px; /* 超大桌面 */
```

### 響應式工具類別
```html
<!-- 隱藏元素 -->
<div class="sm:hidden">小屏幕隱藏</div>
<div class="md:hidden">平板隱藏</div>
<div class="lg:hidden">桌面隱藏</div>
```

## ♿ 無障礙設計

### 減少動畫偏好
系統自動支援 `prefers-reduced-motion` 媒體查詢，為有需要的用戶減少動畫效果。

### 高對比度模式
系統支援 `prefers-contrast: high` 媒體查詢，提供更高的對比度。

### 焦點管理
```html
<!-- 自動焦點環 -->
<button class="btn btn-primary focus-ring">可訪問按鈕</button>

<!-- 跳過鏈接 -->
<a href="#main-content" class="sr-only">跳到主要內容</a>
```

## 🎯 最佳實踐

### 1. 組件組合
```html
<!-- 好的做法：使用設計系統類別 -->
<button class="btn btn-primary btn-lg">
    <i class="icon-save"></i>
    儲存
</button>

<!-- 避免：自定義樣式 -->
<button style="background: blue; padding: 20px;">儲存</button>
```

### 2. 語義化HTML
```html
<!-- 好的做法：語義化結構 -->
<article class="card">
    <header class="card-header">
        <h2 class="heading-4">文章標題</h2>
    </header>
    <main class="card-body">
        <p class="paragraph">文章內容...</p>
    </main>
    <footer class="card-footer">
        <button class="btn btn-primary">閱讀更多</button>
    </footer>
</article>
```

### 3. 一致性原則
- 在同一頁面中使用相同的按鈕樣式
- 保持間距的一致性
- 使用統一的色彩語義

### 4. 性能考量
```html
<!-- 使用 will-change 優化動畫性能 -->
<div class="animate-fade-in will-change-transform">動畫內容</div>

<!-- 使用 GPU 加速 -->
<div class="gpu-accelerated">高性能內容</div>
```

## 🔧 自定義擴展

### 添加自定義色彩
```css
:root {
    --color-brand-custom: #your-color;
}

.bg-brand-custom {
    background-color: var(--color-brand-custom);
}
```

### 創建自定義組件
```css
.custom-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

.custom-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}
```

## 📋 檢查清單

在使用設計系統時，請確保：

- [ ] 引入了 `design-system.css`
- [ ] 使用了語義化的HTML結構
- [ ] 遵循了色彩和字體規範
- [ ] 考慮了響應式設計
- [ ] 測試了無障礙功能
- [ ] 驗證了在不同瀏覽器的兼容性

## 🆘 常見問題

### Q: 如何覆蓋設計系統的樣式？
A: 建議使用CSS變數進行自定義，而不是直接覆蓋類別：

```css
/* 推薦做法 */
:root {
    --color-primary-500: #your-brand-color;
}

/* 避免做法 */
.btn-primary {
    background-color: #your-brand-color !important;
}
```

### Q: 如何添加新的組件？
A: 遵循現有的命名規範和結構模式：

```css
.new-component {
    /* 使用設計系統變數 */
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}
```

### Q: 動畫在某些設備上性能不佳怎麼辦？
A: 使用性能優化類別：

```html
<div class="animate-fade-in gpu-accelerated will-change-transform">
    內容
</div>
```

## 📞 支援

如有任何問題或建議，請聯繫開發團隊或查看項目文檔。

---

**Han AttendanceOS v2005.6.12** - 遠漢科技考勤系統  
© 2024 遠漢科技有限公司. 保留所有權利. 