console.log('✅ static/js/utils/index.js 檔案開始載入...');

/**
 * Han AttendanceOS 工具函數庫統一載入器
 * 提供一站式的工具函數載入和初始化
 * 
 * <AUTHOR> AttendanceOS
 * @version v2005.6.12
 */

class UtilsLoader {

    /**
     * 工具函數庫配置
     */
    static UTILS_CONFIG = {
        'leave-calculator': {
            path: '/static/js/utils/leave-calculator.js',
            dependencies: [],
            global: 'LeaveCalculator'
        },
        'notification': {
            path: '/static/js/utils/notification.js',
            dependencies: [],
            global: 'NotificationSystem'
        },
        'attendance-helper': {
            path: '/static/js/utils/attendance-helper.js',
            dependencies: [],
            global: 'AttendanceHelper'
        },
        'form-validator': {
            path: '/static/js/utils/form-validator.js',
            dependencies: ['notification'],
            global: 'FormValidator'
        }
    };

    /**
     * 已載入的工具函數庫
     */
    static loadedUtils = new Set();

    /**
     * 載入中的工具函數庫
     */
    static loadingUtils = new Map();

    /**
     * 載入單個工具函數庫
     * @param {string} utilName - 工具函數庫名稱
     * @returns {Promise} 載入Promise
     */
    static async loadUtil(utilName) {
        console.log(`ℹ️ 嘗試載入工具函數庫: ${utilName}`);
        // 如果已經載入，直接返回
        if (this.loadedUtils.has(utilName)) {
            return Promise.resolve();
        }

        // 如果正在載入，返回現有的Promise
        if (this.loadingUtils.has(utilName)) {
            return this.loadingUtils.get(utilName);
        }

        const config = this.UTILS_CONFIG[utilName];
        if (!config) {
            throw new Error(`未知的工具函數庫: ${utilName}`);
        }

        // 創建載入Promise
        const loadPromise = this._loadUtilScript(utilName, config);
        this.loadingUtils.set(utilName, loadPromise);

        try {
            await loadPromise;
            this.loadedUtils.add(utilName);
            this.loadingUtils.delete(utilName);
            console.log(`✅ 工具函數庫載入成功: ${utilName}`);
        } catch (error) {
            this.loadingUtils.delete(utilName);
            console.error(`❌ 工具函數庫載入失敗: ${utilName}`, error);
            throw error;
        }

        return loadPromise;
    }

    /**
     * 載入工具函數庫腳本
     * @param {string} utilName - 工具函數庫名稱
     * @param {Object} config - 配置
     * @returns {Promise} 載入Promise
     */
    static async _loadUtilScript(utilName, config) {
        // 先載入依賴
        if (config.dependencies && config.dependencies.length > 0) {
            await Promise.all(config.dependencies.map(dep => this.loadUtil(dep)));
        }

        // 檢查是否已經存在全域變數
        if (config.global && window[config.global]) {
            return Promise.resolve();
        }

        // 載入腳本
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = config.path;
            script.async = true;

            script.onload = () => {
                // 驗證全域變數是否存在
                if (config.global && !window[config.global]) {
                    reject(new Error(`全域變數 ${config.global} 未找到`));
                    return;
                }
                resolve();
            };

            script.onerror = () => {
                reject(new Error(`腳本載入失敗: ${config.path}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * 批量載入工具函數庫
     * @param {Array} utilNames - 工具函數庫名稱列表
     * @returns {Promise} 載入Promise
     */
    static async loadUtils(utilNames) {
        const loadPromises = utilNames.map(name => this.loadUtil(name));
        return Promise.all(loadPromises);
    }

    /**
     * 載入所有工具函數庫
     * @returns {Promise} 載入Promise
     */
    static async loadAllUtils() {
        const allUtils = Object.keys(this.UTILS_CONFIG);
        return this.loadUtils(allUtils);
    }

    /**
     * 檢查工具函數庫是否已載入
     * @param {string} utilName - 工具函數庫名稱
     * @returns {boolean} 是否已載入
     */
    static isLoaded(utilName) {
        return this.loadedUtils.has(utilName);
    }

    /**
     * 獲取已載入的工具函數庫列表
     * @returns {Array} 已載入的工具函數庫列表
     */
    static getLoadedUtils() {
        return Array.from(this.loadedUtils);
    }

    /**
     * 初始化頁面工具函數（根據頁面類型自動載入）
     * @param {string} pageType - 頁面類型
     * @returns {Promise} 初始化Promise
     */
    static async initPageUtils(pageType) {
        const pageUtilsMap = {
            // 用戶頁面
            'user-dashboard': ['notification', 'leave-calculator', 'form-validator'],
            'user-login': ['notification', 'form-validator'],

            // 管理頁面
            'elite-dashboard': ['notification', 'attendance-helper'],
            'elite-approval': ['notification', 'leave-calculator'],
            'elite-attendance': ['notification', 'attendance-helper', 'form-validator'],
            'elite-employees': ['notification', 'form-validator'],
            'elite-shifts': ['notification', 'form-validator'],
            'elite-reports': ['notification', 'attendance-helper'],
            'elite-overtime': ['notification', 'form-validator'],

            // 通用頁面
            'default': ['notification']
        };

        const requiredUtils = pageUtilsMap[pageType] || pageUtilsMap['default'];

        try {
            await this.loadUtils(requiredUtils);
            console.log(`🎯 頁面工具函數初始化完成: ${pageType}`, requiredUtils);

            // 執行頁面特定的初始化
            this._initPageSpecificFeatures(pageType);

            return true; // 明確返回成功狀態

        } catch (error) {
            console.error(`❌ 頁面工具函數初始化失敗: ${pageType}`, error);
            throw error; // 拋出錯誤而不是靜默失敗
        }
    }

    /**
     * 執行頁面特定的初始化功能
     * @param {string} pageType - 頁面類型
     */
    static _initPageSpecificFeatures(pageType) {
        switch (pageType) {
            case 'user-dashboard':
                this._initUserDashboard();
                break;
            case 'elite-approval':
                this._initApprovalPage();
                break;
            case 'elite-attendance':
                this._initAttendancePage();
                break;
            default:
                // 通用初始化
                this._initCommonFeatures();
                break;
        }
    }

    /**
     * 初始化用戶儀表板特定功能
     */
    static _initUserDashboard() {
        // 設置請假表單驗證
        const leaveForm = document.getElementById('leaveForm');
        if (leaveForm && window.FormValidator) {
            const schema = {
                leave_type: ['required'],
                start_date: ['required', 'date'],
                end_date: ['required', 'date'],
                substitute_id: ['required'],
                reason: ['required', { name: 'minLength', params: [5] }]
            };

            FormValidator.setupFormValidation(leaveForm, schema, {
                onSubmit: (data) => {
                    // 這裡會被具體的提交函數覆蓋
                    console.log('請假表單提交:', data);
                }
            });
        }

        // 設置加班表單驗證
        const overtimeForm = document.getElementById('overtimeForm');
        if (overtimeForm && window.FormValidator) {
            const schema = {
                overtime_date: ['required', 'date'],
                start_time: ['required', 'time'],
                end_time: ['required', 'time'],
                reason: ['required', { name: 'minLength', params: [5] }]
            };

            FormValidator.setupFormValidation(overtimeForm, schema);
        }
    }

    /**
     * 初始化審核頁面特定功能
     */
    static _initApprovalPage() {
        // 替換所有確認對話框為滑動通知
        if (window.NotificationSystem) {
            // 覆蓋全域confirm函數
            window.originalConfirm = window.confirm;
            window.confirm = function (message) {
                return NotificationSystem.showConfirm(message);
            };
        }
    }

    /**
     * 初始化考勤頁面特定功能
     */
    static _initAttendancePage() {
        // 設置考勤記錄顯示格式
        if (window.AttendanceHelper) {
            // 可以在這裡設置考勤相關的全域配置
            console.log('考勤頁面工具函數已初始化');
        }
    }

    /**
     * 初始化通用功能
     */
    static _initCommonFeatures() {
        // 設置全域錯誤處理
        if (window.NotificationSystem) {
            window.addEventListener('error', (event) => {
                console.error('全域錯誤:', event.error);
                // 可以選擇是否顯示錯誤通知
                // NotificationSystem.error('系統發生錯誤，請重新整理頁面');
            });
        }

        // 設置AJAX錯誤處理
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function (...args) {
                return originalFetch.apply(this, args)
                    .catch(error => {
                        console.error('網路請求錯誤:', error);
                        if (window.NotificationSystem) {
                            NotificationSystem.error('網路連線錯誤，請檢查網路狀態');
                        }
                        throw error;
                    });
            };
        }
    }

    /**
     * 重新載入工具函數庫（用於開發調試）
     * @param {string} utilName - 工具函數庫名稱
     */
    static async reloadUtil(utilName) {
        // 移除已載入標記
        this.loadedUtils.delete(utilName);

        // 移除現有腳本
        const config = this.UTILS_CONFIG[utilName];
        if (config) {
            const existingScript = document.querySelector(`script[src="${config.path}"]`);
            if (existingScript) {
                existingScript.remove();
            }

            // 清除全域變數
            if (config.global && window[config.global]) {
                delete window[config.global];
            }
        }

        // 重新載入
        return this.loadUtil(utilName);
    }
}

// 導出為全域變數
window.UtilsLoader = UtilsLoader;

// 提供簡化的全域函數
window.loadUtils = function (utilNames) {
    return UtilsLoader.loadUtils(utilNames);
};

window.loadUtil = function (utilName) {
    return UtilsLoader.loadUtil(utilName);
};

// 在腳本載入後自動初始化工具函數（根據頁面URL）
// document.addEventListener('DOMContentLoaded', function() {
//     const path = window.location.pathname;
//     let pageType = 'default';

//     if (path.includes('/user-dashboard')) {
//         pageType = 'user-dashboard';
//     } else if (path.includes('/user-login')) {
//         pageType = 'user-login';
//     } else if (path.includes('/elite-dashboard')) {
//         pageType = 'elite-dashboard';
//     } else if (path.includes('/elite-approval')) {
//         pageType = 'elite-approval';
//     } else if (path.includes('/elite-attendance')) {
//         pageType = 'elite-attendance';
//     } else if (path.includes('/elite-employees')) {
//         pageType = 'elite-employees';
//     } else if (path.includes('/elite-shifts')) {
//         pageType = 'elite-shifts';
//     } else if (path.includes('/elite-reports')) {
//         pageType = 'elite-reports';
//     } else if (path.includes('/elite-overtime')) {
//         pageType = 'elite-overtime';
//     }

//     UtilsLoader.initPageUtils(pageType)
//         .then(() => console.log('✅ 工具函數庫自動初始化完成'))
//         .catch(error => console.warn('⚠️ 工具函數庫自動初始化失敗:', error));
// });