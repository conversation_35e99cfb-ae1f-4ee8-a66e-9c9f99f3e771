<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>員工管理 - Han AttendanceOS</title>

    <!-- PWA 支援 -->
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#0ea5e9">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Han考勤">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#6366f1',
                            600: '#4f46e5',
                        }
                    }
                }
            }
        }
    </script>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>

    <style>
        /* Apple/Google 大廠風格樣式 */
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .gradient-border {
            background: linear-gradient(white, white) padding-box, linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
            border: 2px solid transparent;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen font-sans">
    <!-- 頂部導航 - Apple風格 -->
    <header class="glass-effect shadow-sm sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-4">
                    <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-2xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5">
                        <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
                        <span class="text-sm font-medium text-white">返回儀表板</span>
                    </a>
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">員工管理</h1>
                        <p class="text-sm text-gray-500">Han AttendanceOS - 員工資訊管理</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">管理員模式</p>
                        <p class="text-xs text-gray-500">員工資料管理</p>
                    </div>
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                        <i data-lucide="users" class="w-5 h-5 text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主內容區 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- 歡迎區域 -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">員工資訊管理中心</h2>
            <p class="text-lg text-gray-600">管理員工資訊、部門配置與權限設定</p>
        </div>

        <!-- 查詢工具欄 - Apple風格卡片 -->
        <div class="glass-effect rounded-3xl shadow-xl shadow-gray-100/50 border border-gray-100/60 overflow-hidden mb-8 card-hover">
            <!-- 標題區域 -->
            <div class="bg-gradient-to-r from-blue-500/5 to-purple-500/5 px-8 py-6 border-b border-gray-100/60">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i data-lucide="search" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 tracking-tight">智能查詢</h3>
                        <p class="text-sm text-gray-500 mt-1">快速找到您需要的員工資訊</p>
                    </div>
                </div>
            </div>

            <!-- 查詢表單 -->
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                    <!-- 員工姓名搜尋 -->
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="user" class="w-4 h-4 text-blue-600"></i>
                            <label class="block text-sm font-semibold text-gray-700">員工姓名</label>
                        </div>
                        <div class="relative">
                            <input type="text" id="nameSearch" placeholder="搜尋員工姓名..." class="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 員工編號搜尋 -->
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="hash" class="w-4 h-4 text-green-600"></i>
                            <label class="block text-sm font-semibold text-gray-700">員工編號</label>
                        </div>
                        <div class="relative">
                            <input type="text" id="employeeIdSearch" placeholder="員工編號..." class="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm">
                            <i data-lucide="hash" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 職位搜尋 -->
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="briefcase" class="w-4 h-4 text-purple-600"></i>
                            <label class="block text-sm font-semibold text-gray-700">職位</label>
                        </div>
                        <div class="relative">
                            <input type="text" id="positionSearch" placeholder="職位..." class="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm">
                            <i data-lucide="briefcase" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 部門篩選 -->
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="building" class="w-4 h-4 text-orange-600"></i>
                            <label class="block text-sm font-semibold text-gray-700">部門</label>
                        </div>
                        <select id="departmentFilter" class="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-sm">
                            <option value="">所有部門</option>
                            <!-- 動態載入部門選項 -->
                        </select>
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="flex flex-wrap items-center justify-between gap-4 pt-6 border-t border-gray-100">
                    <div class="flex items-center space-x-3">
                        <button id="searchBtn" class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                            <i data-lucide="search" class="w-4 h-4"></i>
                            <span class="font-medium">查詢</span>
                        </button>

                        <button id="resetBtn" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl hover:bg-gray-200 transition-all duration-200 flex items-center space-x-2">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            <span class="font-medium">重置</span>
                        </button>
                    </div>

                    <div class="flex items-center space-x-3">
                        <button id="addEmployeeBtn" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span class="font-medium">新增員工</span>
                        </button>

                        <button class="bg-gray-100 text-gray-700 px-4 py-3 rounded-xl hover:bg-gray-200 transition-all duration-200 flex items-center space-x-2">
                            <i data-lucide="upload" class="w-4 h-4"></i>
                            <span class="font-medium">批量匯入</span>
                        </button>

                        <button class="bg-gray-100 text-gray-700 px-4 py-3 rounded-xl hover:bg-gray-200 transition-all duration-200 flex items-center space-x-2">
                            <i data-lucide="download" class="w-4 h-4"></i>
                            <span class="font-medium">匯出</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查詢結果統計 -->
        <div id="searchStats" class="mb-6 hidden">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                        <i data-lucide="info" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <span class="text-blue-800 font-semibold text-lg" id="statsText">查詢結果統計</span>
                        <p class="text-blue-600 text-sm mt-1">實時更新的查詢結果</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 員工列表 - Apple風格卡片 -->
        <div class="glass-effect rounded-3xl shadow-xl shadow-gray-100/50 border border-gray-100/60 overflow-hidden">
            <!-- 標題區域 -->
            <div class="bg-gradient-to-r from-indigo-500/5 to-purple-500/5 px-8 py-6 border-b border-gray-100/60">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i data-lucide="users" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 tracking-tight">員工列表</h3>
                            <p class="text-sm text-gray-500 mt-1">管理所有員工的詳細資訊</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div id="employeeCount" class="text-sm text-gray-500 font-medium">載入中...</div>
                        <div class="text-xs text-gray-400 mt-1">實時統計</div>
                    </div>
                </div>
            </div>

            <!-- 表格標題 -->
            <div class="bg-gray-50/80 px-8 py-4 border-b border-gray-200/60">
                <div class="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                    <div class="col-span-2 flex items-center space-x-2">
                        <i data-lucide="user" class="w-4 h-4 text-blue-600"></i>
                        <span>員工資訊</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="building" class="w-4 h-4 text-green-600"></i>
                        <span>部門</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="briefcase" class="w-4 h-4 text-purple-600"></i>
                        <span>職位</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="graduation-cap" class="w-4 h-4 text-blue-600"></i>
                        <span>學歷</span>
                    </div>
                    <div class="col-span-2 flex items-center space-x-2">
                        <i data-lucide="award" class="w-4 h-4 text-purple-600"></i>
                        <span>專業技能</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="shield" class="w-4 h-4 text-red-600"></i>
                        <span>角色</span>
                    </div>
                    <div class="col-span-2 flex items-center space-x-2">
                        <i data-lucide="phone" class="w-4 h-4 text-indigo-600"></i>
                        <span>聯絡方式</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="activity" class="w-4 h-4 text-emerald-600"></i>
                        <span>狀態</span>
                    </div>
                    <div class="col-span-1 flex items-center space-x-2">
                        <i data-lucide="settings" class="w-4 h-4 text-gray-600"></i>
                        <span>操作</span>
                    </div>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="flex items-center justify-center py-16">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl mb-6 shadow-lg">
                        <div class="relative">
                            <div class="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                            <div class="absolute inset-0 bg-blue-500/20 rounded-full animate-ping"></div>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-700 mb-2">載入員工資料中</h4>
                    <p class="text-sm text-gray-500">正在為您準備最新的員工資訊...</p>
                </div>
            </div>

            <!-- 員工列表內容 -->
            <div id="employeesList" class="divide-y divide-gray-100/60">
                <!-- 動態載入員工數據 -->
            </div>

            <!-- 無資料提示 -->
            <div id="noDataMessage" class="hidden text-center py-16">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl mb-8 shadow-lg">
                    <i data-lucide="users" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-700 mb-3">暫無符合條件的員工資料</h4>
                <p class="text-gray-500 mb-8 max-w-md mx-auto leading-relaxed">請調整查詢條件或新增員工資料</p>
                <button id="addEmployeeBtn2" class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    新增第一位員工
                </button>
            </div>
        </div>
    </main>

    <!-- 員工詳情/編輯模態框 -->
    <div id="employeeModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden border border-gray-100">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-brand-500 to-brand-600 px-8 py-6 text-white relative overflow-hidden">
                    <!-- 背景裝飾 -->
                    <div class="absolute inset-0 bg-white opacity-10">
                        <div class="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white opacity-20"></div>
                        <div class="absolute top-8 -left-8 w-16 h-16 rounded-full bg-white opacity-15"></div>
                    </div>

                    <div class="relative flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="user-plus" class="w-6 h-6"></i>
                            </div>
                            <div>
                                <h3 id="modalTitle" class="text-2xl font-bold">員工詳情</h3>
                                <p class="text-brand-100 text-sm">管理員工基本資訊與權限設定</p>
                            </div>
                        </div>

                        <!-- 頂部操作按鈕 -->
                        <div class="flex items-center space-x-3">
                            <button type="button" id="topSaveBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm">
                                <i data-lucide="check" class="w-4 h-4"></i>
                                <span>儲存</span>
                            </button>
                            <button type="button" id="topCancelBtn" class="bg-white bg-opacity-10 hover:bg-opacity-20 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm">
                                <i data-lucide="x" class="w-4 h-4"></i>
                                <span>取消</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 模態框內容 -->
                <div class="p-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <form id="employeeForm" class="space-y-8">
                        <!-- 基本資訊區塊 -->
                        <div class="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-100 shadow-sm">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-brand-100 rounded-xl flex items-center justify-center">
                                    <i data-lucide="user" class="w-5 h-5 text-brand-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">基本資訊</h4>
                            </div>

                            <!-- 照片預覽區域 -->
                            <div class="flex items-start space-x-6 mb-6">
                                <!-- 照片顯示區域 -->
                                <div class="flex-shrink-0">
                                    <div class="relative">
                                        <!-- 照片容器 -->
                                        <div id="photoPreview" class="w-24 h-24 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg border-4 border-white">
                                            <!-- 預設頭像 -->
                                            <div id="defaultAvatar" class="text-white text-2xl font-bold">
                                                <i data-lucide="user" class="w-8 h-8"></i>
                                            </div>
                                            <!-- 實際照片 -->
                                            <img id="actualPhoto" src="" alt="員工照片" class="w-full h-full object-cover hidden">
                                        </div>

                                        <!-- 照片上傳按鈕 -->
                                        <button type="button" id="changePhotoBtn" class="absolute -bottom-2 -right-2 w-8 h-8 bg-brand-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-brand-600 transition-colors">
                                            <i data-lucide="camera" class="w-4 h-4"></i>
                                        </button>
                                    </div>

                                    <!-- 照片提示 -->
                                    <p class="text-xs text-gray-500 mt-2 text-center">點擊相機圖標<br>更換照片</p>
                                </div>

                                <!-- 基本資訊表單 -->
                                <div class="flex-1 grid grid-cols-2 gap-6">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">員工姓名 <span class="text-red-500">*</span></label>
                                        <input type="text" id="employeeName" name="name" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">員工編號 <span class="text-red-500">*</span></label>
                                        <input type="text" id="employeeId" name="employee_id" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">電子郵件 <span class="text-red-500">*</span></label>
                                        <input type="email" id="employeeEmail" name="email" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700">聯絡電話</label>
                                        <input type="tel" id="employeePhone" name="phone" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                    </div>
                                </div>
                            </div>

                            <!-- 照片連結輸入 -->
                            <div class="grid grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">照片連結</label>
                                    <input type="url" id="employeePhoto" name="photo_url" placeholder="https://example.com/photo.jpg" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">身分證號</label>
                                    <input type="text" id="employeeIdNumber" name="id_number" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                            </div>
                        </div>

                        <!-- 職位資訊區塊 -->
                        <div class="bg-gradient-to-br from-blue-50 to-white rounded-2xl p-6 border border-blue-100 shadow-sm">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <i data-lucide="briefcase" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">職位資訊</h4>
                            </div>

                            <div class="grid grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">部門 <span class="text-red-500">*</span></label>
                                    <select id="employeeDepartment" name="department_id" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="">請選擇部門</option>
                                        <!-- 動態載入部門選項 -->
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">職位 <span class="text-red-500">*</span></label>
                                    <input type="text" id="employeePosition" name="position" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">角色權限</label>
                                    <select id="employeeRole" name="role_id" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="">請選擇角色</option>
                                        <!-- 動態載入角色選項 -->
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">直屬主管</label>
                                    <select id="employeeManager" name="manager_id" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="">請選擇主管</option>
                                        <!-- 動態載入主管選項 -->
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">到職日期</label>
                                    <input type="date" id="employeeHireDate" name="hire_date" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">薪資等級</label>
                                    <select id="employeeSalaryLevel" name="salary_level" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="junior">初級</option>
                                        <option value="intermediate">中級</option>
                                        <option value="senior">高級</option>
                                        <option value="expert">專家</option>
                                        <option value="lead">主管</option>
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">班表類型 <span class="text-red-500">*</span></label>
                                    <select id="employeeShiftType" name="shift_type" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <!-- 動態載入班表選項 -->
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">線上打卡權限</label>
                                    <div class="relative">
                                        <select id="employeeAllowOnlinePunch" name="allow_online_punch" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                            <option value="1">✅ 允許線上打卡</option>
                                            <option value="0">❌ 禁止線上打卡</option>
                                        </select>
                                        <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                            <i data-lucide="smartphone" class="w-4 h-4 text-gray-400"></i>
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1 bg-blue-50 p-2 rounded-lg border border-blue-100">
                                        <i data-lucide="info" class="w-3 h-3 inline mr-1 text-blue-600"></i> 控制員工是否可以使用手機進行線上打卡
                                    </p>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">學歷等級</label>
                                    <select id="employeeEducationLevel" name="education_level_id" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="">請選擇學歷</option>
                                        <!-- 動態載入學歷選項 -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 專業技能區塊 -->
                        <div class="bg-gradient-to-br from-purple-50 to-white rounded-2xl p-6 border border-purple-100 shadow-sm">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                                        <i data-lucide="award" class="w-5 h-5 text-purple-600"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">專業技能</h4>
                                </div>
                                <button type="button" id="addSkillBtn" class="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-sm">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    <span>新增技能</span>
                                </button>
                            </div>

                            <!-- 技能列表 -->
                            <div id="skillsList" class="space-y-3">
                                <!-- 動態載入技能項目 -->
                            </div>

                            <!-- 無技能提示 -->
                            <div id="noSkillsMessage" class="text-center py-8 text-gray-500 hidden">
                                <i data-lucide="award" class="w-12 h-12 mx-auto mb-3 text-gray-300"></i>
                                <p>尚未添加任何專業技能</p>
                                <p class="text-sm">點擊上方「新增技能」按鈕開始添加</p>
                            </div>
                        </div>

                        <!-- 升遷紀錄區塊 -->
                        <div class="bg-gradient-to-br from-blue-50 to-white rounded-2xl p-6 border border-blue-100 shadow-sm">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                        <i data-lucide="trending-up" class="w-5 h-5 text-blue-600"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">升遷紀錄</h4>
                                </div>
                                <button type="button" id="addPromotionBtn" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-sm">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    <span>新增升遷</span>
                                </button>
                            </div>

                            <!-- 升遷列表 -->
                            <div id="promotionsList" class="space-y-3">
                                <!-- 動態載入升遷項目 -->
                            </div>

                            <!-- 無升遷提示 -->
                            <div id="noPromotionsMessage" class="text-center py-8 text-gray-500 hidden">
                                <i data-lucide="trending-up" class="w-12 h-12 mx-auto mb-3 text-gray-300"></i>
                                <p>尚未添加任何升遷紀錄</p>
                                <p class="text-sm">點擊上方「新增升遷」按鈕開始添加</p>
                            </div>
                        </div>

                        <!-- 獎懲紀錄區塊 -->
                        <div class="bg-gradient-to-br from-orange-50 to-white rounded-2xl p-6 border border-orange-100 shadow-sm">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                                        <i data-lucide="medal" class="w-5 h-5 text-orange-600"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900">獎懲紀錄</h4>
                                </div>
                                <button type="button" id="addRewardBtn" class="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center space-x-2 shadow-sm">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    <span>新增獎懲</span>
                                </button>
                            </div>

                            <!-- 獎懲列表 -->
                            <div id="rewardsList" class="space-y-3">
                                <!-- 動態載入獎懲項目 -->
                            </div>

                            <!-- 無獎懲提示 -->
                            <div id="noRewardsMessage" class="text-center py-8 text-gray-500 hidden">
                                <i data-lucide="medal" class="w-12 h-12 mx-auto mb-3 text-gray-300"></i>
                                <p>尚未添加任何獎懲紀錄</p>
                                <p class="text-sm">點擊上方「新增獎懲」按鈕開始添加</p>
                            </div>
                        </div>

                        <!-- 其他資訊區塊 -->
                        <div class="bg-gradient-to-br from-green-50 to-white rounded-2xl p-6 border border-green-100 shadow-sm">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                    <i data-lucide="settings" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">其他資訊</h4>
                            </div>

                            <div class="grid grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">員工狀態</label>
                                    <select id="employeeStatus" name="status" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                        <option value="active">在職</option>
                                        <option value="trial">試用期</option>
                                        <option value="leave">離職</option>
                                    </select>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">地址</label>
                                    <textarea id="employeeAddress" name="address" rows="3" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm resize-none"></textarea>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">緊急聯絡人</label>
                                    <input type="text" id="employeeEmergencyContact" name="emergency_contact" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">緊急聯絡電話</label>
                                    <input type="tel" id="employeeEmergencyPhone" name="emergency_phone" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                </div>
                            </div>
                        </div>

                        <!-- 安全設定區塊 -->
                        <div id="passwordSection" class="bg-gradient-to-br from-red-50 to-white rounded-2xl p-6 border border-red-100 shadow-sm">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                    <i data-lucide="shield" class="w-5 h-5 text-red-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">安全設定</h4>
                            </div>

                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">密碼</label>
                                <div class="relative">
                                    <input type="password" id="employeePassword" name="password" class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                    <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-4 flex items-center">
                                        <i data-lucide="eye" class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors"></i>
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 mt-2 bg-gray-50 p-3 rounded-lg">
                                    <i data-lucide="info" class="w-4 h-4 inline mr-1"></i> 編輯時留空表示不修改密碼
                                </p>
                            </div>
                        </div>

                        <!-- 底部操作按鈕 -->
                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-100">
                            <!-- 回到頂部/離開按鈕 -->
                            <button type="button" id="backToTopBtn" class="px-8 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center space-x-2 shadow-lg">
                                <i data-lucide="arrow-up" class="w-4 h-4"></i>
                                <span>回到頂部</span>
                            </button>
                            <button type="button" id="exitBtn" class="px-8 py-3 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 shadow-sm">
                                <i data-lucide="log-out" class="w-4 h-4"></i>
                                <span>離開</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let employees = [];
        let departments = [];
        let roles = [];
        let managers = [];
        let currentEditingEmployee = null;
        let originalFormData = {}; // 儲存原始表單資料
        let hasUnsavedChanges = false; // 追蹤是否有未儲存的變更
        let shifts = [];
        let employeeStatusMap = {}; // 動態員工狀態對應表
        let educationLevels = []; // 學歷等級資料
        let allSkills = []; // 所有技能資料
        let promotionTypes = []; // 升遷類型資料
        let rewardTypes = []; // 獎懲類型資料

        // 初始化頁面
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            loadDepartments();
            loadRoles();
            loadManagers();
            loadEmployees();
            loadShifts();
            loadEmployeeStatusTypes();
            loadEducationLevels();
            loadSkills();
            loadPromotionTypes();
            loadRewardTypes();
            bindEvents();
        });

        // 綁定事件
        function bindEvents() {
            // 查詢按鈕
            document.getElementById('searchBtn').addEventListener('click', performSearch);

            // 重置按鈕
            document.getElementById('resetBtn').addEventListener('click', resetSearch);

            // 新增員工按鈕
            document.getElementById('addEmployeeBtn').addEventListener('click', showAddEmployeeModal);

            // 模態框關閉
            document.getElementById('topCancelBtn').addEventListener('click', handleModalClose);

            // 表單提交 - 綁定到底部和頂部的儲存按鈕
            document.getElementById('employeeForm').addEventListener('submit', saveEmployee);
            document.getElementById('topSaveBtn').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('employeeForm').dispatchEvent(new Event('submit'));
            });

            // 回到頂部按鈕
            document.getElementById('backToTopBtn').addEventListener('click', scrollToTop);

            // 離開按鈕
            document.getElementById('exitBtn').addEventListener('click', handleModalClose);

            // 密碼顯示/隱藏切換
            document.getElementById('togglePassword').addEventListener('click', togglePasswordVisibility);

            // 照片相關事件
            document.getElementById('changePhotoBtn').addEventListener('click', changePhoto);
            document.getElementById('employeePhoto').addEventListener('input', updatePhotoPreview);

            // 姓名輸入框變更時更新頭像
            document.getElementById('employeeName').addEventListener('input', function() {
                // 如果沒有照片，則更新預設頭像
                const photoUrl = document.getElementById('employeePhoto').value.trim();
                if (!photoUrl) {
                    generatePersonalizedAvatar(this.value);
                }
            });

            // 輸入框回車搜尋
            document.getElementById('nameSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') performSearch();
            });
            document.getElementById('employeeIdSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') performSearch();
            });

            // 點擊模態框背景關閉
            document.getElementById('employeeModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    handleModalClose();
                }
            });

            // 監聽表單變更
            document.getElementById('employeeForm').addEventListener('input', trackFormChanges);
            document.getElementById('employeeForm').addEventListener('change', trackFormChanges);

            // 新增技能按鈕
            document.getElementById('addSkillBtn').addEventListener('click', showAddSkillModal);

            // 新增升遷按鈕
            document.getElementById('addPromotionBtn').addEventListener('click', showAddPromotionModal);

            // 新增獎懲按鈕
            document.getElementById('addRewardBtn').addEventListener('click', showAddRewardModal);
        }

        // 載入部門資料
        async function loadDepartments() {
            try {
                const response = await fetch('/api/departments');
                const data = await response.json();

                // 正確處理API返回格式
                if (data.departments) {
                    departments = data.departments;
                } else {
                    departments = Array.isArray(data) ? data : [];
                }

                // 填充部門選項
                populateDepartmentOptions();
                console.log('部門資料載入成功:', departments);
            } catch (error) {
                console.error('載入部門資料失敗:', error);
                showNotification('載入部門資料失敗', 'error');
            }
        }

        // 填充部門選項
        function populateDepartmentOptions() {
            const departmentFilter = document.getElementById('departmentFilter');
            const employeeDepartment = document.getElementById('employeeDepartment');

            // 清空現有選項
            departmentFilter.innerHTML = '<option value="">所有部門</option>';
            employeeDepartment.innerHTML = '<option value="">請選擇部門</option>';

            // 添加部門選項
            departments.forEach(dept => {
                const option1 = new Option(dept.name, dept.id);
                const option2 = new Option(dept.name, dept.id);
                departmentFilter.appendChild(option1);
                employeeDepartment.appendChild(option2);
            });
        }

        // 載入角色資料
        async function loadRoles() {
            try {
                // 使用正確的API端點
                const response = await fetch('/api/permissions/roles');
                const data = await response.json();

                if (data.roles) {
                    roles = data.roles;
                } else {
                    // 如果沒有roles屬性，嘗試直接使用返回的數據
                    roles = Array.isArray(data) ? data : [];
                }

                // 填充角色選項
                populateRoleOptions();
            } catch (error) {
                console.error('載入角色資料失敗:', error);
                // 設置預設角色選項
                roles = [{
                    id: 1,
                    role_name: '系統管理員',
                    name: '系統管理員'
                }, {
                    id: 2,
                    role_name: '部門主管',
                    name: '部門主管'
                }, {
                    id: 3,
                    role_name: '一般員工',
                    name: '一般員工'
                }];
                populateRoleOptions();
                showNotification('載入角色資料失敗，使用預設選項', 'warning');
            }
        }

        // 填充角色選項
        function populateRoleOptions() {
            const employeeRole = document.getElementById('employeeRole');

            // 清空現有選項
            employeeRole.innerHTML = '<option value="">請選擇角色</option>';

            // 添加角色選項
            roles.forEach(role => {
                const roleName = role.role_name || role.name || `角色${role.id}`;
                const option = new Option(roleName, role.id);
                employeeRole.appendChild(option);
            });
        }

        // 載入主管資料
        async function loadManagers() {
            try {
                const response = await fetch('/api/employees/managers');
                const data = await response.json();
                managers = data.managers || [];

                // 填充主管選項
                populateManagerOptions();
            } catch (error) {
                console.error('載入主管資料失敗:', error);
                showNotification('載入主管資料失敗', 'error');
            }
        }

        // 填充主管選項
        function populateManagerOptions() {
            const employeeManager = document.getElementById('employeeManager');

            // 清空現有選項
            employeeManager.innerHTML = '<option value="">請選擇主管</option>';

            // 添加主管選項
            managers.forEach(manager => {
                const option = new Option(manager.name, manager.id);
                employeeManager.appendChild(option);
            });
        }

        // 載入員工資料
        async function loadEmployees(queryParams = {}) {
            try {
                showLoading();

                // 構建查詢字串
                const params = new URLSearchParams();
                Object.keys(queryParams).forEach(key => {
                    if (queryParams[key]) {
                        params.append(key, queryParams[key]);
                    }
                });

                const response = await fetch(`/api/employees?${params.toString()}`);
                const data = await response.json();

                employees = data.employees || [];
                displayEmployees(employees);
                updateSearchStats(data);

            } catch (error) {
                console.error('載入員工資料失敗:', error);
                showNotification('載入員工資料失敗', 'error');
            } finally {
                hideLoading();
            }
        }

        // 顯示員工列表
        function displayEmployees(employeeList) {
            const container = document.getElementById('employeesList');
            const noDataMessage = document.getElementById('noDataMessage');

            if (employeeList.length === 0) {
                container.innerHTML = '';
                noDataMessage.classList.remove('hidden');
                return;
            }

            noDataMessage.classList.add('hidden');

            container.innerHTML = employeeList.map(employee => {
                        // 生成頭像HTML
                        const avatarHtml = generateAvatarHtml(employee);

                        return `
                <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
                    <div class="grid grid-cols-12 gap-4 items-center">
                            <div class="col-span-2 flex items-center space-x-3">
                                ${avatarHtml}
                                <div>
                                    <p class="font-medium text-gray-900">${employee.name}</p>
                                    <p class="text-sm text-gray-500">${employee.employee_id}</p>
                                </div>
                            </div>
                            <div class="col-span-1">
                                <span class="text-sm text-gray-900">${employee.department_name || '未設定部門'}</span>
                            </div>
                            <div class="col-span-1">
                                <span class="text-sm text-gray-900">${employee.position}</span>
                            </div>
                            <div class="col-span-1">
                                <span class="text-sm text-gray-900">${employee.education_level_name || '未設定學歷'}</span>
                            </div>
                            <div class="col-span-2">
                                <div class="text-sm">
                                    ${generateSkillsDisplay(employee.skills)}
                                </div>
                            </div>
                            <div class="col-span-1">
                                <span class="text-sm text-gray-900">${employee.role_name || '未設定角色'}</span>
                            </div>
                            <div class="col-span-2">
                                <div class="text-sm">
                                    ${employee.email ? `<p class="text-gray-900">${employee.email}</p>` : ''}
                                    ${employee.phone ? `<p class="text-gray-500">${employee.phone}</p>` : ''}
                                </div>
                            </div>
                            <div class="col-span-1">
                                <span class="${getStatusBadgeClass(employee.status)}">${getStatusText(employee.status)}</span>
                            </div>
                            <div class="col-span-1 flex items-center justify-end space-x-2">
                                <button onclick="event.stopPropagation(); editEmployee(${employee.id})"
                                        style="display: flex !important; align-items: center !important; justify-content: center !important; width: 100%; padding: 8px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #f59e0b, #ea580c); cursor: pointer; text-align: center !important; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
                                        onmouseover="this.style.background='linear-gradient(135deg, #d97706, #dc2626)'; this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.background='linear-gradient(135deg, #f59e0b, #ea580c)'; this.style.transform='scale(1)'">
                                    編輯
                                </button>
                                <button onclick="event.stopPropagation(); viewEmployee(${employee.id})"
                                        style="display: flex !important; align-items: center !important; justify-content: center !important; width: 100%; padding: 8px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #3b82f6, #6366f1); cursor: pointer; text-align: center !important; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
                                        onmouseover="this.style.background='linear-gradient(135deg, #2563eb, #4f46e5)'; this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.background='linear-gradient(135deg, #3b82f6, #6366f1)'; this.style.transform='scale(1)'">
                                    查看
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        /**
         * 生成員工頭像HTML
         * 如果有照片URL則顯示照片，否則顯示姓氏圓圈
         */
        function generateAvatarHtml(employee) {
            if (employee.photo_url && employee.photo_url.trim()) {
                // 有照片時顯示圓形照片
                return `
                    <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img src="${employee.photo_url}" 
                             alt="${employee.name}" 
                             class="w-full h-full object-cover"
                             onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full ${getAvatarColor(employee.name)} rounded-full flex items-center justify-center text-white font-medium\\'>${employee.name.charAt(0)}</div>'">
                    </div>
                `;
            } else {
                // 沒有照片時顯示姓氏圓圈
                const avatarColor = getAvatarColor(employee.name);
                return `
                    <div class="w-10 h-10 ${avatarColor} rounded-full flex items-center justify-center text-white font-medium">
                        ${employee.name.charAt(0)}
                    </div>
                `;
            }
        }

        /**
         * 根據姓名生成頭像背景顏色
         */
        function getAvatarColor(name) {
            const colors = [
                'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 
                'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
                'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-emerald-500'
            ];
            
            // 根據姓名的第一個字符的Unicode值選擇顏色
            const charCode = name.charCodeAt(0);
            return colors[charCode % colors.length];
        }

        /**
         * 生成技能顯示HTML
         */
        function generateSkillsDisplay(skills) {
            if (!skills || skills.length === 0) {
                return '<span class="text-gray-400 text-xs">暫無技能資料</span>';
            }

            // 最多顯示3個技能，其餘用+N表示
            const displaySkills = skills.slice(0, 3);
            const remainingCount = skills.length - 3;

            let html = displaySkills.map(skill => {
                const proficiencyColor = getProficiencyColor(skill.proficiency_level);
                return `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${proficiencyColor} mr-1 mb-1">${skill.name}</span>`;
            }).join('');

            if (remainingCount > 0) {
                html += `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">+${remainingCount}</span>`;
            }

            return html;
        }

        /**
         * 根據熟練度獲取顏色樣式
         */
        function getProficiencyColor(level) {
            const colorMap = {
                'beginner': 'bg-yellow-100 text-yellow-800',
                'intermediate': 'bg-blue-100 text-blue-800',
                'advanced': 'bg-green-100 text-green-800',
                'expert': 'bg-purple-100 text-purple-800'
            };
            return colorMap[level] || 'bg-gray-100 text-gray-800';
        }

        /**
         * 獲取員工狀態顯示文字
         */
        function getStatusText(status) {
            return employeeStatusMap[status] || status || '未知';
        }

        /**
         * 獲取員工狀態徽章樣式
         */
        function getStatusBadgeClass(status) {
            const classMap = {
                'active': 'bg-success-100 text-success-800 text-xs px-2 py-1 rounded-full',
                'trial': 'bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full',
                'leave': 'bg-error-100 text-error-800 text-xs px-2 py-1 rounded-full',
                'inactive': 'bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full'
            };
            return classMap[status] || 'bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full';
        }

        // 執行搜尋
        function performSearch() {
            const queryParams = {
                name: document.getElementById('nameSearch').value.trim(),
                employee_id: document.getElementById('employeeIdSearch').value.trim(),
                department_id: document.getElementById('departmentFilter').value,
                position: document.getElementById('positionSearch').value.trim()
            };
            
            loadEmployees(queryParams);
        }

        // 重置搜尋
        function resetSearch() {
            document.getElementById('nameSearch').value = '';
            document.getElementById('employeeIdSearch').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('positionSearch').value = '';
            
            loadEmployees();
            hideSearchStats();
        }

        // 更新搜尋統計
        function updateSearchStats(data) {
            const statsContainer = document.getElementById('searchStats');
            const statsText = document.getElementById('statsText');
            
            if (data.total === undefined) return;
            
            let message = `找到 ${data.total} 位員工`;
            
            if (data.department_stats) {
                message += `，該部門共有 ${data.department_stats.total_employees} 位員工（在職：${data.department_stats.active_employees} 位）`;
            }
            
            statsText.textContent = message;
            statsContainer.classList.remove('hidden');
        }

        // 隱藏搜尋統計
        function hideSearchStats() {
            document.getElementById('searchStats').classList.add('hidden');
        }

        // 顯示新增員工模態框
        function showAddEmployeeModal() {
            currentEditingEmployee = { skills: [] }; // 初始化為空技能陣列
            document.getElementById('modalTitle').textContent = '新增員工';
            document.getElementById('employeeForm').reset();
            showModal();
            
            // 初始化照片顯示
            setTimeout(() => {
                updatePhotoDisplay('');
            }, 100);
            
            // 初始化技能顯示
            setTimeout(() => {
                renderEmployeeSkills();
            }, 100);
            
            // 儲存原始表單資料狀態
            setTimeout(() => {
                saveOriginalFormData();
            }, 100);
        }

        // 編輯員工
        function editEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            currentEditingEmployee = employee;

            // 填充基本資訊
            document.getElementById('employeeName').value = employee.name || '';
            document.getElementById('employeeId').value = employee.employee_id || '';
            document.getElementById('employeeEmail').value = employee.email || '';
            document.getElementById('employeePhone').value = employee.phone || '';
            document.getElementById('employeePhoto').value = employee.photo_url || '';

            // 填充職位資訊
            document.getElementById('employeeDepartment').value = employee.department_id || '';
            document.getElementById('employeePosition').value = employee.position || '';
            document.getElementById('employeeRole').value = employee.role_id || '';
            document.getElementById('employeeManager').value = employee.manager_id || '';

            // 填充其他資訊
            document.getElementById('employeeHireDate').value = employee.hire_date || '';
            document.getElementById('employeeStatus').value = employee.status || 'active';
            document.getElementById('employeeIdNumber').value = employee.id_number || '';
            document.getElementById('employeeSalaryLevel').value = employee.salary_level || 'junior';
            document.getElementById('employeeShiftType').value = employee.shift_type || shifts[0]?.id || 1;
            document.getElementById('employeeAllowOnlinePunch').value = employee.allow_online_punch !== undefined ? employee.allow_online_punch : 1;
            document.getElementById('employeeAddress').value = employee.address || '';
            document.getElementById('employeeEmergencyContact').value = employee.emergency_contact || '';
            document.getElementById('employeeEmergencyPhone').value = employee.emergency_phone || '';

            // 填充學歷資訊
            document.getElementById('employeeEducationLevel').value = employee.education_level_id || '';

            // 密碼欄位在編輯時留空
            document.getElementById('employeePassword').value = '';

            // 更新模態框標題
            document.getElementById('modalTitle').textContent = '編輯員工';

            // 顯示模態框
            showModal();
            
            // 初始化照片顯示
            setTimeout(() => {
                updatePhotoDisplay(employee.photo_url || '');
            }, 100);
            
            // 渲染員工技能
            setTimeout(() => {
                renderEmployeeSkills();
            }, 100);
            
            // 渲染升遷紀錄
            setTimeout(async () => {
                await renderEmployeePromotions();
            }, 100);
            
            // 渲染獎懲紀錄
            setTimeout(async () => {
                await renderEmployeeRewards();
            }, 100);
            
            // 儲存原始表單資料狀態
            setTimeout(() => {
                saveOriginalFormData();
            }, 100);
        }

        // 查看員工詳情
        function viewEmployee(employeeId) {
            // 可以實現查看詳情的邏輯
            editEmployee(employeeId);
        }

        // 保存員工
        async function saveEmployee(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const employeeData = {
                name: formData.get('name'),
                employee_id: formData.get('employee_id'),
                department_id: formData.get('department_id'),
                position: formData.get('position'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                photo_url: formData.get('photo_url'),
                role_id: formData.get('role_id'),
                manager_id: formData.get('manager_id'),
                hire_date: formData.get('hire_date'),
                status: formData.get('status'),
                id_number: formData.get('id_number'),
                salary_level: formData.get('salary_level'),
                shift_type: formData.get('shift_type'),
                allow_online_punch: parseInt(formData.get('allow_online_punch')) || 1,
                address: formData.get('address'),
                emergency_contact: formData.get('emergency_contact'),
                emergency_phone: formData.get('emergency_phone'),
                education_level_id: formData.get('education_level_id')
            };

            // 添加技能資料
            if (currentEditingEmployee && currentEditingEmployee.skills) {
                employeeData.skills = currentEditingEmployee.skills;
            }
            
            // 添加升遷紀錄
            if (currentEditingEmployee && currentEditingEmployee.promotions) {
                employeeData.promotions = currentEditingEmployee.promotions;
            }
            
            // 添加獎懲紀錄
            if (currentEditingEmployee && currentEditingEmployee.rewards) {
                employeeData.rewards = currentEditingEmployee.rewards;
            }

            // 清理空值
            Object.keys(employeeData).forEach(key => {
                if (employeeData[key] === '' || employeeData[key] === null) {
                    if (key === 'department_id') {
                        // 部門ID不能為空
                        return;
                    }
                    if (key === 'skills') {
                        // 技能資料保持原樣
                        return;
                    }
                    employeeData[key] = null;
                }
            });

            // 如果是編輯模式且密碼欄位有值，才包含密碼
            const password = formData.get('password');
            if (password && password.trim()) {
                employeeData.password = password;
            }

            try {
                let response;
                if (currentEditingEmployee) {
                    // 編輯現有員工
                    response = await fetch(`/api/employees/${currentEditingEmployee.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(employeeData)
                    });
                } else {
                    // 新增員工
                    response = await fetch('/api/employees', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(employeeData)
                    });
                }

                const result = await response.json();

                if (response.ok) {
                    showNotification(result.message || '員工資料儲存成功', 'success');
                    hideModal();
                    loadEmployees(); // 重新載入員工列表
                } else {
                    // 顯示具體的錯誤訊息
                    const errorMessage = result.error || `操作失敗 (${response.status})`;
                    showNotification(errorMessage, 'error');
                    console.error('API錯誤:', result);
                }

            } catch (error) {
                console.error('保存員工資料失敗:', error);
                showNotification('網路錯誤，請稍後再試', 'error');
            }
        }

        // 顯示模態框
        function showModal() {
            document.getElementById('employeeModal').classList.remove('hidden');
        }

        // 隱藏模態框
        function hideModal() {
            document.getElementById('employeeModal').classList.add('hidden');
            currentEditingEmployee = null;
            hasUnsavedChanges = false;
            originalFormData = {};
            
            // 清理技能顯示
            const skillsList = document.getElementById('skillsList');
            const noSkillsMessage = document.getElementById('noSkillsMessage');
            if (skillsList) skillsList.innerHTML = '';
            if (noSkillsMessage) noSkillsMessage.classList.remove('hidden');
            
            // 清理升遷紀錄顯示
            const promotionsList = document.getElementById('promotionsList');
            const noPromotionsMessage = document.getElementById('noPromotionsMessage');
            if (promotionsList) promotionsList.innerHTML = '';
            if (noPromotionsMessage) noPromotionsMessage.classList.remove('hidden');
            
            // 清理獎懲紀錄顯示
            const rewardsList = document.getElementById('rewardsList');
            const noRewardsMessage = document.getElementById('noRewardsMessage');
            if (rewardsList) rewardsList.innerHTML = '';
            if (noRewardsMessage) noRewardsMessage.classList.remove('hidden');
        }

        // 顯示載入指示器
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('employeesList').style.display = 'none';
        }

        // 隱藏載入指示器
        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
            document.getElementById('employeesList').style.display = 'block';
        }

        // 向後兼容的通知函數（使用新的工具函數庫）
        function showNotification(message, type = 'info') {
            if (window.NotificationSystem) {
                switch (type) {
                    case 'success':
                        NotificationSystem.success(message);
                        break;
                    case 'error':
                        NotificationSystem.error(message);
                        break;
                    case 'warning':
                        NotificationSystem.warning(message);
                        break;
                    default:
                        NotificationSystem.info(message);
                }
            } else {
                console.warn('通知系統未載入，使用控制台輸出:', message);
            }
        }

        // 密碼顯示/隱藏切換
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('employeePassword');
            const toggleIcon = document.querySelector('#togglePassword i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.setAttribute('data-lucide', 'eye-off');
            } else {
                passwordInput.type = 'password';
                toggleIcon.setAttribute('data-lucide', 'eye');
            }
            
            // 重新創建圖標
            lucide.createIcons();
        }

        // 回到頂部
        function scrollToTop() {
            const modalContent = document.querySelector('#employeeModal .overflow-y-auto');
            if (modalContent) {
                modalContent.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // 處理模態框關閉
        function handleModalClose() {
            if (hasUnsavedChanges) {
                showConfirmDialog(
                    '確認離開',
                    '您有未儲存的變更，確定要離開嗎？',
                    [
                        {
                            text: '儲存並離開',
                            class: 'bg-brand-500 text-white',
                            action: () => {
                                // 觸發表單提交
                                document.getElementById('employeeForm').dispatchEvent(new Event('submit'));
                            }
                        },
                        {
                            text: '不儲存離開',
                            class: 'bg-gray-500 text-white',
                            action: () => {
                                hasUnsavedChanges = false;
                                hideModal();
                            }
                        },
                        {
                            text: '取消',
                            class: 'border border-gray-300 text-gray-700',
                            action: () => {
                                // 什麼都不做，對話框會自動關閉
                            }
                        }
                    ]
                );
            } else {
                hideModal();
            }
        }

        // 監聽表單變更
        function trackFormChanges() {
            if (!hasFormDataChanged()) {
                hasUnsavedChanges = false;
                return;
            }
            hasUnsavedChanges = true;
        }

        // 檢查表單資料是否有變更
        function hasFormDataChanged() {
            const currentData = getFormData();
            
            // 比較當前資料與原始資料
            for (const key in originalFormData) {
                if (originalFormData[key] !== currentData[key]) {
                    return true;
                }
            }
            
            for (const key in currentData) {
                if (originalFormData[key] !== currentData[key]) {
                    return true;
                }
            }
            
            return false;
        }

        // 獲取表單資料
        function getFormData() {
            const form = document.getElementById('employeeForm');
            const formData = new FormData(form);
            const data = {};
            
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            return data;
        }

        // 儲存原始表單資料
        function saveOriginalFormData() {
            originalFormData = getFormData();
            hasUnsavedChanges = false;
        }

        // 顯示確認對話框
        function showConfirmDialog(title, message, buttons) {
            // 創建對話框HTML
            const dialogHtml = `
                <div id="confirmDialog" class="fixed inset-0 bg-black bg-opacity-60 z-[60] backdrop-blur-sm">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full border border-gray-100">
                            <div class="p-6">
                                <div class="flex items-center space-x-3 mb-4">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                                </div>
                                <p class="text-gray-600 mb-6">${message}</p>
                                <div class="flex flex-col space-y-2">
                                    ${buttons.map(button => `
                                        <button class="px-4 py-2 rounded-lg transition-colors ${button.class}" 
                                                onclick="handleDialogAction(${buttons.indexOf(button)})">
                                            ${button.text}
                                        </button>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到頁面
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
            
            // 儲存按鈕動作
            window.dialogActions = buttons.map(button => button.action);
            
            // 重新創建圖標
            lucide.createIcons();
        }

        // 處理對話框動作
        window.handleDialogAction = function(index) {
            const dialog = document.getElementById('confirmDialog');
            if (dialog) {
                dialog.remove();
            }
            
            if (window.dialogActions && window.dialogActions[index]) {
                window.dialogActions[index]();
            }
            
            // 清理
            delete window.dialogActions;
        }

        // 照片相關事件
        function changePhoto() {
            // 創建文件輸入元素
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';
            
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 檢查文件大小（限制為5MB）
                    if (file.size > 5 * 1024 * 1024) {
                        showNotification('照片文件過大，請選擇小於5MB的圖片', 'error');
                        return;
                    }
                    
                    // 檢查文件類型
                    if (!file.type.startsWith('image/')) {
                        showNotification('請選擇有效的圖片文件', 'error');
                        return;
                    }
                    
                    // 讀取文件並顯示預覽
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const photoUrl = e.target.result;
                        document.getElementById('employeePhoto').value = photoUrl;
                        updatePhotoDisplay(photoUrl);
                        showNotification('照片已更新', 'success');
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // 觸發文件選擇
            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
        }

        function updatePhotoPreview() {
            const photoUrl = document.getElementById('employeePhoto').value.trim();
            updatePhotoDisplay(photoUrl);
        }

        function updatePhotoDisplay(photoUrl) {
            const photoPreview = document.getElementById('photoPreview');
            const defaultAvatar = document.getElementById('defaultAvatar');
            const actualPhoto = document.getElementById('actualPhoto');
            
            if (photoUrl && photoUrl !== '') {
                // 測試圖片是否能正常載入
                const testImg = new Image();
                testImg.onload = function() {
                    // 圖片載入成功，顯示實際照片
                    actualPhoto.src = photoUrl;
                    actualPhoto.classList.remove('hidden');
                    defaultAvatar.classList.add('hidden');
                };
                testImg.onerror = function() {
                    // 圖片載入失敗，顯示預設頭像
                    showDefaultAvatar();
                    if (photoUrl.startsWith('http')) {
                        showNotification('照片載入失敗，請檢查連結是否正確', 'warning');
                    }
                };
                testImg.src = photoUrl;
            } else {
                // 沒有照片URL，顯示預設頭像
                showDefaultAvatar();
            }
        }

        function showDefaultAvatar() {
            const photoPreview = document.getElementById('photoPreview');
            const defaultAvatar = document.getElementById('defaultAvatar');
            const actualPhoto = document.getElementById('actualPhoto');
            
            // 隱藏實際照片，顯示預設頭像
            actualPhoto.classList.add('hidden');
            defaultAvatar.classList.remove('hidden');
            
            // 根據員工姓名生成個性化頭像
            const employeeName = document.getElementById('employeeName').value || '新員工';
            const avatarText = employeeName.charAt(0);
            const avatarColor = getAvatarColor(employeeName);
            
            // 更新頭像顯示
            defaultAvatar.innerHTML = `<span class="text-2xl font-bold">${avatarText}</span>`;
            
            // 更新背景顏色
            photoPreview.className = `w-24 h-24 rounded-2xl overflow-hidden ${avatarColor} flex items-center justify-center shadow-lg border-4 border-white`;
        }

        function generatePersonalizedAvatar(name) {
            const avatarContainer = document.getElementById('defaultAvatar');
            const photoPreview = document.getElementById('photoPreview');
            
            if (!name || name.trim() === '') {
                // 沒有姓名時顯示通用圖標
                avatarContainer.innerHTML = '<i data-lucide="user" class="w-8 h-8"></i>';
                photoPreview.className = 'w-24 h-24 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg border-4 border-white';
            } else {
                // 有姓名時顯示首字母
                const firstChar = name.charAt(0);
                const avatarColor = getAvatarColor(name);
                
                avatarContainer.innerHTML = `<span class="text-2xl font-bold">${firstChar}</span>`;
                photoPreview.className = `w-24 h-24 rounded-2xl overflow-hidden ${avatarColor} flex items-center justify-center shadow-lg border-4 border-white`;
            }
            
            // 重新創建圖標
            lucide.createIcons();
        }

        // 載入班表資料
        async function loadShifts() {
            try {
                const response = await fetch('/api/shifts');
                const data = await response.json();
                
                if (data.shifts) {
                    shifts = data.shifts;
                } else {
                    shifts = Array.isArray(data) ? data : [];
                }

                // 填充班表選項
                populateShiftOptions();
            } catch (error) {
                console.error('載入班表資料失敗:', error);
                // 設置預設班表選項
                shifts = [{
                    id: 1,
                    name: '標準日班',
                    code: 'STANDARD_DAY'
                }];
                populateShiftOptions();
                showNotification('載入班表資料失敗，使用預設選項', 'warning');
            }
        }

        // 填充班表選項
        function populateShiftOptions() {
            const employeeShiftType = document.getElementById('employeeShiftType');

            // 清空現有選項
            employeeShiftType.innerHTML = '';

            // 添加班表選項
            shifts.forEach((shift, index) => {
                const option = new Option(shift.name, shift.id);
                // 第一個班表設為預設選中
                if (index === 0) {
                    option.selected = true;
                }
                employeeShiftType.appendChild(option);
            });
        }

        // 載入員工狀態類型
        async function loadEmployeeStatusTypes() {
            try {
                const response = await fetch('/api/masterdata/employee-status');
                const data = await response.json();
                
                // 正確處理API返回格式
                const statusItems = data.records || data.items || [];
                
                if (statusItems.length > 0) {
                    // 建立狀態代碼到狀態名稱的對應表
                    employeeStatusMap = {};
                    statusItems.forEach(item => {
                        employeeStatusMap[item.code] = item.name;
                    });
                    
                    // 更新狀態選項
                    populateStatusOptions(statusItems);
                    console.log('員工狀態類型載入成功:', employeeStatusMap);
                } else {
                    console.warn('員工狀態類型載入失敗，使用預設對應表');
                    useDefaultEmployeeStatusMap();
                }
            } catch (error) {
                console.error('載入員工狀態類型失敗:', error);
                useDefaultEmployeeStatusMap();
            }
        }

        // 使用預設員工狀態對應表（容錯處理）
        function useDefaultEmployeeStatusMap() {
            employeeStatusMap = {
                'active': '在職',
                'trial': '試用期',
                'leave': '離職',
                'inactive': '停職'
            };
        }

        // 填充狀態選項
        function populateStatusOptions(statusTypes) {
            const statusSelect = document.getElementById('employeeStatus');
            if (!statusSelect) return;
            
            // 清空現有選項
            statusSelect.innerHTML = '';
            
            // 添加動態載入的狀態類型
            statusTypes.forEach(statusType => {
                const option = document.createElement('option');
                option.value = statusType.code;
                option.textContent = statusType.name;
                if (statusType.description) {
                    option.title = statusType.description;
                }
                statusSelect.appendChild(option);
            });
        }

        // 載入學歷等級資料
        async function loadEducationLevels() {
            try {
                const response = await fetch('/api/education-levels');
                const data = await response.json();
                
                if (data.education_levels) {
                    educationLevels = data.education_levels;
                } else {
                    educationLevels = Array.isArray(data) ? data : [];
                }

                // 填充學歷選項
                populateEducationOptions();
                console.log('學歷等級資料載入成功:', educationLevels);
            } catch (error) {
                console.error('載入學歷等級資料失敗:', error);
                showNotification('載入學歷等級資料失敗', 'error');
            }
        }

        // 填充學歷選項
        function populateEducationOptions() {
            const educationSelect = document.getElementById('employeeEducationLevel');
            if (!educationSelect) return;

            // 清空現有選項
            educationSelect.innerHTML = '<option value="">請選擇學歷</option>';

            // 添加學歷選項
            educationLevels.forEach(level => {
                const option = new Option(level.name, level.id);
                educationSelect.appendChild(option);
            });
        }

        // 載入技能資料
        async function loadSkills() {
            try {
                const response = await fetch('/api/skills');
                const data = await response.json();
                
                if (data.skills) {
                    allSkills = data.skills;
                } else {
                    allSkills = Array.isArray(data) ? data : [];
                }

                                 console.log('技能資料載入成功:', allSkills);
             } catch (error) {
                 console.error('載入技能資料失敗:', error);
                 showNotification('載入技能資料失敗', 'error');
             }
         }

        // 顯示新增技能模態框
        function showAddSkillModal() {
            if (!allSkills || allSkills.length === 0) {
                showNotification('技能資料尚未載入完成，請稍後再試', 'warning');
                return;
            }

            // 創建技能選擇模態框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-[70] backdrop-blur-sm';
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden border border-gray-100">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4 text-white">
                            <h3 class="text-xl font-bold">新增專業技能</h3>
                            <p class="text-purple-100 text-sm">為員工添加新的專業技能</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">選擇技能</label>
                                    <select id="skillSelect" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                        <option value="">請選擇技能</option>
                                        ${generateSkillOptions()}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">熟練度</label>
                                    <select id="proficiencySelect" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                        <option value="beginner">初級 (Beginner)</option>
                                        <option value="intermediate" selected>中級 (Intermediate)</option>
                                        <option value="advanced">高級 (Advanced)</option>
                                        <option value="expert">專家 (Expert)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">經驗年數</label>
                                    <input type="number" id="experienceInput" min="0" max="50" value="1" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">相關證照 (選填)</label>
                                    <input type="text" id="certificationInput" placeholder="例如：Microsoft Azure 認證" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                                <button type="button" onclick="closeSkillModal()" class="px-6 py-2 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">取消</button>
                                <button type="button" onclick="addEmployeeSkill()" class="px-6 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-colors">新增技能</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加點擊背景關閉功能
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeSkillModal();
                }
            });

            document.body.appendChild(modal);
        }

        // 生成技能選項
        function generateSkillOptions() {
            const categories = {};
            allSkills.forEach(skill => {
                if (!categories[skill.category]) {
                    categories[skill.category] = [];
                }
                categories[skill.category].push(skill);
            });

            let options = '';
            Object.keys(categories).forEach(category => {
                options += `<optgroup label="${category}">`;
                categories[category].forEach(skill => {
                    options += `<option value="${skill.id}">${skill.name}</option>`;
                });
                options += `</optgroup>`;
            });

            return options;
        }

        // 關閉技能模態框
        function closeSkillModal() {
            const skillModal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-60.z-\\[70\\]');
            if (skillModal) {
                skillModal.remove();
            }
        }

        // 新增員工技能
        function addEmployeeSkill() {
            const skillId = document.getElementById('skillSelect').value;
            const proficiency = document.getElementById('proficiencySelect').value;
            const experience = document.getElementById('experienceInput').value;
            const certification = document.getElementById('certificationInput').value;

            if (!skillId) {
                showNotification('請選擇技能', 'warning');
                return;
            }

            // 找到技能資訊
            const skill = allSkills.find(s => s.id == skillId);
            if (!skill) {
                showNotification('技能資訊錯誤', 'error');
                return;
            }

            // 檢查是否已存在
            if (!currentEditingEmployee) {
                currentEditingEmployee = { skills: [] };
            }
            if (!currentEditingEmployee.skills) {
                currentEditingEmployee.skills = [];
            }

            const existingSkill = currentEditingEmployee.skills.find(s => s.id == skillId);
            if (existingSkill) {
                showNotification('該技能已存在', 'warning');
                return;
            }

            // 添加技能
            const newSkill = {
                id: parseInt(skillId),
                name: skill.name,
                category: skill.category,
                proficiency_level: proficiency,
                years_experience: parseInt(experience),
                certification: certification || null
            };

            currentEditingEmployee.skills.push(newSkill);

            // 更新顯示
            renderEmployeeSkills();

            // 標記表單有變更
            hasUnsavedChanges = true;

            // 關閉技能模態框
            const skillModal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-60.z-\\[70\\]');
            if (skillModal) {
                skillModal.remove();
            }

            showNotification('技能新增成功，請記得儲存員工資料', 'success');
        }

        // 渲染員工技能列表
        function renderEmployeeSkills() {
            const skillsList = document.getElementById('skillsList');
            const noSkillsMessage = document.getElementById('noSkillsMessage');

            if (!currentEditingEmployee || !currentEditingEmployee.skills || currentEditingEmployee.skills.length === 0) {
                skillsList.innerHTML = '';
                noSkillsMessage.classList.remove('hidden');
                return;
            }

            noSkillsMessage.classList.add('hidden');

            skillsList.innerHTML = currentEditingEmployee.skills.map(skill => {
                const proficiencyColor = getProficiencyColor(skill.proficiency_level);
                const proficiencyText = {
                    'beginner': '初級',
                    'intermediate': '中級',
                    'advanced': '高級',
                    'expert': '專家'
                }[skill.proficiency_level] || skill.proficiency_level;

                return `
                    <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    <h5 class="font-semibold text-gray-900">${skill.name}</h5>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${proficiencyColor}">${proficiencyText}</span>
                                    <span class="text-sm text-gray-500">${skill.category}</span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <span>經驗：${skill.years_experience} 年</span>
                                    ${skill.certification ? `<span class="ml-4">證照：${skill.certification}</span>` : ''}
                                </div>
                            </div>
                            <button type="button" onclick="removeEmployeeSkill(${skill.id})" class="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            // 重新創建圖標
            lucide.createIcons();
        }

        // 移除員工技能
        function removeEmployeeSkill(skillId) {
            if (!currentEditingEmployee || !currentEditingEmployee.skills) return;

            currentEditingEmployee.skills = currentEditingEmployee.skills.filter(skill => skill.id !== skillId);
            renderEmployeeSkills();
            
            // 標記表單有變更
            hasUnsavedChanges = true;
            
            showNotification('技能已移除，請記得儲存員工資料', 'success');
        }

        // ==================== 升遷紀錄相關函數 ====================

        // 載入升遷類型
        async function loadPromotionTypes() {
            try {
                const response = await fetch('/api/promotion-types');
                const data = await response.json();
                
                if (response.ok) {
                    promotionTypes = data.promotion_types || [];
                    console.log('升遷類型載入成功:', promotionTypes);
                } else {
                    console.error('載入升遷類型失敗:', data.error);
                    // 使用預設升遷類型
                    promotionTypes = [
                        { id: 1, name: '職位晉升', description: '職位等級提升' },
                        { id: 2, name: '薪資調整', description: '薪資等級調整' },
                        { id: 3, name: '部門調動', description: '部門間調動' },
                        { id: 4, name: '職責擴大', description: '工作職責範圍擴大' },
                        { id: 5, name: '管理職晉升', description: '晉升為管理職位' }
                    ];
                }
            } catch (error) {
                console.error('載入升遷類型失敗:', error);
                // 使用預設升遷類型
                promotionTypes = [
                    { id: 1, name: '職位晉升', description: '職位等級提升' },
                    { id: 2, name: '薪資調整', description: '薪資等級調整' },
                    { id: 3, name: '部門調動', description: '部門間調動' },
                    { id: 4, name: '職責擴大', description: '工作職責範圍擴大' },
                    { id: 5, name: '管理職晉升', description: '晉升為管理職位' }
                ];
            }
        }

        // 顯示新增升遷模態框
        function showAddPromotionModal() {
            if (!promotionTypes || promotionTypes.length === 0) {
                showNotification('升遷類型資料尚未載入完成，請稍後再試', 'warning');
                return;
            }

            // 創建升遷模態框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-[70] backdrop-blur-sm';
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden border border-gray-100">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4 text-white">
                            <h3 class="text-xl font-bold">新增升遷紀錄</h3>
                            <p class="text-blue-100 text-sm">記錄員工的升遷歷程</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">升遷類型</label>
                                    <select id="promotionTypeSelect" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">請選擇升遷類型</option>
                                        ${generatePromotionTypeOptions()}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">升遷日期</label>
                                    <input type="date" id="promotionDateInput" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">原職位</label>
                                    <input type="text" id="fromPositionInput" placeholder="例如：專員" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">新職位</label>
                                    <input type="text" id="toPositionInput" placeholder="例如：主任" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">升遷原因</label>
                                    <textarea id="promotionReasonInput" rows="3" placeholder="例如：工作表現優異，具備管理能力..." class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">備註 (選填)</label>
                                    <input type="text" id="promotionNotesInput" placeholder="其他相關資訊" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                                <button type="button" onclick="closePromotionModal()" class="px-6 py-2 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">取消</button>
                                <button type="button" onclick="addEmployeePromotion()" class="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-colors">新增升遷</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加點擊背景關閉功能
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePromotionModal();
                }
            });

            document.body.appendChild(modal);
        }

        // 生成升遷類型選項
        function generatePromotionTypeOptions() {
            return promotionTypes.map(type => 
                `<option value="${type.id}">${type.name} - ${type.description}</option>`
            ).join('');
        }

        // 關閉升遷模態框
        function closePromotionModal() {
            const promotionModal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-60.z-\\[70\\]');
            if (promotionModal) {
                promotionModal.remove();
            }
        }

        // 新增員工升遷
        async function addEmployeePromotion() {
            const typeId = document.getElementById('promotionTypeSelect').value;
            const date = document.getElementById('promotionDateInput').value;
            const fromPosition = document.getElementById('fromPositionInput').value;
            const toPosition = document.getElementById('toPositionInput').value;
            const reason = document.getElementById('promotionReasonInput').value;
            const notes = document.getElementById('promotionNotesInput').value;

            if (!typeId || !date || !fromPosition || !toPosition || !reason) {
                showNotification('請填寫所有必填欄位', 'warning');
                return;
            }

            // 找到升遷類型資訊
            const promotionType = promotionTypes.find(t => t.id == typeId);
            if (!promotionType) {
                showNotification('升遷類型資訊錯誤', 'error');
                return;
            }

            // 檢查員工物件
            if (!currentEditingEmployee) {
                currentEditingEmployee = { promotions: [] };
            }
            if (!currentEditingEmployee.promotions) {
                currentEditingEmployee.promotions = [];
            }

            // 添加升遷紀錄
            const newPromotion = {
                id: Date.now(), // 臨時ID
                type_id: parseInt(typeId),
                type_name: promotionType.name,
                promotion_date: date,
                from_position: fromPosition,
                to_position: toPosition,
                reason: reason,
                notes: notes || null
            };

            currentEditingEmployee.promotions.push(newPromotion);

            // 更新顯示
            await renderEmployeePromotions();

            // 標記表單有變更
            hasUnsavedChanges = true;

            // 關閉模態框
            closePromotionModal();

            showNotification('升遷紀錄新增成功，請記得儲存員工資料', 'success');
        }

        // 渲染員工升遷列表
        async function renderEmployeePromotions() {
            const promotionsList = document.getElementById('promotionsList');
            const noPromotionsMessage = document.getElementById('noPromotionsMessage');

            if (!currentEditingEmployee) {
                promotionsList.innerHTML = '';
                noPromotionsMessage.classList.remove('hidden');
                return;
            }

            try {
                // 從API載入升遷紀錄
                const response = await fetch(`/api/employees/${currentEditingEmployee.id}/promotions`);
                const data = await response.json();
                
                let promotions = [];
                if (response.ok) {
                    promotions = data.promotions || [];
                    // 將升遷紀錄存到currentEditingEmployee中
                    currentEditingEmployee.promotions = promotions;
                } else {
                    console.error('載入升遷紀錄失敗:', data.error);
                }

                if (promotions.length === 0) {
                    promotionsList.innerHTML = '';
                    noPromotionsMessage.classList.remove('hidden');
                    return;
                }

                noPromotionsMessage.classList.add('hidden');

                // 按日期排序（最新在前）
                const sortedPromotions = [...promotions].sort((a, b) => 
                    new Date(b.promotion_date) - new Date(a.promotion_date)
                );

                promotionsList.innerHTML = sortedPromotions.map(promotion => {
                    const formattedDate = new Date(promotion.promotion_date).toLocaleDateString('zh-TW');
                    
                    return `
                        <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">${promotion.type_name}</span>
                                        <span class="text-sm text-gray-500">${formattedDate}</span>
                                    </div>
                                    <div class="text-sm text-gray-900 mb-2">
                                        <span class="font-medium">${promotion.from_position}</span>
                                        <i data-lucide="arrow-right" class="w-4 h-4 inline mx-2 text-gray-400"></i>
                                        <span class="font-medium text-blue-600">${promotion.to_position}</span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <p><strong>原因：</strong>${promotion.reason}</p>
                                        ${promotion.notes ? `<p class="mt-1"><strong>備註：</strong>${promotion.notes}</p>` : ''}
                                    </div>
                                </div>
                                <button type="button" onclick="removeEmployeePromotion(${promotion.id})" class="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');

                // 重新創建圖標
                lucide.createIcons();
                
            } catch (error) {
                console.error('載入升遷紀錄失敗:', error);
                promotionsList.innerHTML = '';
                noPromotionsMessage.classList.remove('hidden');
            }
        }

        // 移除員工升遷
        async function removeEmployeePromotion(promotionId) {
            if (!currentEditingEmployee || !currentEditingEmployee.promotions) return;

            currentEditingEmployee.promotions = currentEditingEmployee.promotions.filter(promotion => promotion.id !== promotionId);
            await renderEmployeePromotions();
            
            // 標記表單有變更
            hasUnsavedChanges = true;
            
            showNotification('升遷紀錄已移除，請記得儲存員工資料', 'success');
        }

        // ==================== 獎懲紀錄相關函數 ====================

        // 載入獎懲類型
        async function loadRewardTypes() {
            try {
                const response = await fetch('/api/reward-types');
                const data = await response.json();
                
                if (response.ok) {
                    rewardTypes = data.reward_types || [];
                    console.log('獎懲類型載入成功:', rewardTypes);
                } else {
                    console.error('載入獎懲類型失敗:', data.error);
                    // 使用預設獎懲類型
                    rewardTypes = [
                        { id: 1, name: '嘉獎', type: 'reward', description: '表現優異給予嘉獎' },
                        { id: 2, name: '小功', type: 'reward', description: '工作表現傑出' },
                        { id: 3, name: '大功', type: 'reward', description: '重大貢獻或成就' },
                        { id: 4, name: '獎金', type: 'reward', description: '績效獎金或特殊獎勵' },
                        { id: 5, name: '口頭警告', type: 'punishment', description: '輕微違規口頭提醒' },
                        { id: 6, name: '書面警告', type: 'punishment', description: '違規行為書面記錄' },
                        { id: 7, name: '小過', type: 'punishment', description: '違反公司規定' },
                        { id: 8, name: '大過', type: 'punishment', description: '嚴重違規行為' }
                    ];
                }
            } catch (error) {
                console.error('載入獎懲類型失敗:', error);
                // 使用預設獎懲類型
                rewardTypes = [
                    { id: 1, name: '嘉獎', type: 'reward', description: '表現優異給予嘉獎' },
                    { id: 2, name: '小功', type: 'reward', description: '工作表現傑出' },
                    { id: 3, name: '大功', type: 'reward', description: '重大貢獻或成就' },
                    { id: 4, name: '獎金', type: 'reward', description: '績效獎金或特殊獎勵' },
                    { id: 5, name: '口頭警告', type: 'punishment', description: '輕微違規口頭提醒' },
                    { id: 6, name: '書面警告', type: 'punishment', description: '違規行為書面記錄' },
                    { id: 7, name: '小過', type: 'punishment', description: '違反公司規定' },
                    { id: 8, name: '大過', type: 'punishment', description: '嚴重違規行為' }
                ];
            }
        }

        // 顯示新增獎懲模態框
        function showAddRewardModal() {
            if (!rewardTypes || rewardTypes.length === 0) {
                showNotification('獎懲類型資料尚未載入完成，請稍後再試', 'warning');
                return;
            }

            // 創建獎懲模態框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-[70] backdrop-blur-sm';
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden border border-gray-100">
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4 text-white">
                            <h3 class="text-xl font-bold">新增獎懲紀錄</h3>
                            <p class="text-orange-100 text-sm">記錄員工的獎懲情況</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">獎懲類型</label>
                                    <select id="rewardTypeSelect" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                        <option value="">請選擇獎懲類型</option>
                                        ${generateRewardTypeOptions()}
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">獎懲日期</label>
                                    <input type="date" id="rewardDateInput" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">事由</label>
                                    <textarea id="rewardReasonInput" rows="3" placeholder="例如：協助完成重要專案，表現優異..." class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">獎懲金額 (選填)</label>
                                    <input type="number" id="rewardAmountInput" min="0" placeholder="獎金或罰款金額" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">核准人</label>
                                    <input type="text" id="rewardApproverInput" placeholder="例如：人事主管" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">備註 (選填)</label>
                                    <input type="text" id="rewardNotesInput" placeholder="其他相關資訊" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                                <button type="button" onclick="closeRewardModal()" class="px-6 py-2 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">取消</button>
                                <button type="button" onclick="addEmployeeReward()" class="px-6 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-colors">新增獎懲</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加點擊背景關閉功能
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeRewardModal();
                }
            });

            document.body.appendChild(modal);
        }

        // 生成獎懲類型選項
        function generateRewardTypeOptions() {
            let options = '';
            
            // 獎勵類型
            const rewards = rewardTypes.filter(type => type.type === 'reward');
            if (rewards.length > 0) {
                options += '<optgroup label="獎勵類型">';
                rewards.forEach(type => {
                    options += `<option value="${type.id}">${type.name} - ${type.description}</option>`;
                });
                options += '</optgroup>';
            }
            
            // 懲罰類型
            const punishments = rewardTypes.filter(type => type.type === 'punishment');
            if (punishments.length > 0) {
                options += '<optgroup label="懲罰類型">';
                punishments.forEach(type => {
                    options += `<option value="${type.id}">${type.name} - ${type.description}</option>`;
                });
                options += '</optgroup>';
            }
            
            return options;
        }

        // 關閉獎懲模態框
        function closeRewardModal() {
            const rewardModal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-60.z-\\[70\\]');
            if (rewardModal) {
                rewardModal.remove();
            }
        }

        // 新增員工獎懲
        async function addEmployeeReward() {
            const typeId = document.getElementById('rewardTypeSelect').value;
            const date = document.getElementById('rewardDateInput').value;
            const reason = document.getElementById('rewardReasonInput').value;
            const amount = document.getElementById('rewardAmountInput').value;
            const approver = document.getElementById('rewardApproverInput').value;
            const notes = document.getElementById('rewardNotesInput').value;

            if (!typeId || !date || !reason || !approver) {
                showNotification('請填寫所有必填欄位', 'warning');
                return;
            }

            // 找到獎懲類型資訊
            const rewardType = rewardTypes.find(t => t.id == typeId);
            if (!rewardType) {
                showNotification('獎懲類型資訊錯誤', 'error');
                return;
            }

            // 檢查員工物件
            if (!currentEditingEmployee) {
                currentEditingEmployee = { rewards: [] };
            }
            if (!currentEditingEmployee.rewards) {
                currentEditingEmployee.rewards = [];
            }

            // 添加獎懲紀錄
            const newReward = {
                id: Date.now(), // 臨時ID
                type_id: parseInt(typeId),
                type_name: rewardType.name,
                type_category: rewardType.type,
                reward_date: date,
                reason: reason,
                amount: amount ? parseFloat(amount) : null,
                approver: approver,
                notes: notes || null
            };

            currentEditingEmployee.rewards.push(newReward);

            // 更新顯示
            await renderEmployeeRewards();

            // 標記表單有變更
            hasUnsavedChanges = true;

            // 關閉模態框
            closeRewardModal();

            showNotification('獎懲紀錄新增成功，請記得儲存員工資料', 'success');
        }

        // 渲染員工獎懲列表
        async function renderEmployeeRewards() {
            const rewardsList = document.getElementById('rewardsList');
            const noRewardsMessage = document.getElementById('noRewardsMessage');

            if (!currentEditingEmployee) {
                rewardsList.innerHTML = '';
                noRewardsMessage.classList.remove('hidden');
                return;
            }

            try {
                // 從API載入獎懲紀錄
                const response = await fetch(`/api/employees/${currentEditingEmployee.id}/rewards`);
                const data = await response.json();
                
                let rewards = [];
                if (response.ok) {
                    rewards = data.rewards || [];
                    // 將獎懲紀錄存到currentEditingEmployee中
                    currentEditingEmployee.rewards = rewards;
                } else {
                    console.error('載入獎懲紀錄失敗:', data.error);
                }

                if (rewards.length === 0) {
                    rewardsList.innerHTML = '';
                    noRewardsMessage.classList.remove('hidden');
                    return;
                }

                noRewardsMessage.classList.add('hidden');

                // 按日期排序（最新在前）
                const sortedRewards = [...rewards].sort((a, b) => 
                    new Date(b.reward_date) - new Date(a.reward_date)
                );

                rewardsList.innerHTML = sortedRewards.map(reward => {
                    const formattedDate = new Date(reward.reward_date).toLocaleDateString('zh-TW');
                    const isReward = reward.type_category === 'reward';
                    const badgeColor = isReward ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    const icon = isReward ? 'award' : 'alert-triangle';
                    
                    return `
                        <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${badgeColor}">
                                            <i data-lucide="${icon}" class="w-3 h-3 mr-1"></i>
                                            ${reward.type_name}
                                        </span>
                                        <span class="text-sm text-gray-500">${formattedDate}</span>
                                        ${reward.amount ? `<span class="text-sm font-medium ${isReward ? 'text-green-600' : 'text-red-600'}">$${reward.amount.toLocaleString()}</span>` : ''}
                                    </div>
                                    <div class="text-sm text-gray-900 mb-2">
                                        <p><strong>事由：</strong>${reward.reason}</p>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <p><strong>核准人：</strong>${reward.approver}</p>
                                        ${reward.notes ? `<p class="mt-1"><strong>備註：</strong>${reward.notes}</p>` : ''}
                                    </div>
                                </div>
                                <button type="button" onclick="removeEmployeeReward(${reward.id})" class="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');

                // 重新創建圖標
                lucide.createIcons();
                
            } catch (error) {
                console.error('載入獎懲紀錄失敗:', error);
                rewardsList.innerHTML = '';
                noRewardsMessage.classList.remove('hidden');
            }
        }

        // 移除員工獎懲
        async function removeEmployeeReward(rewardId) {
            if (!currentEditingEmployee || !currentEditingEmployee.rewards) return;

            currentEditingEmployee.rewards = currentEditingEmployee.rewards.filter(reward => reward.id !== rewardId);
            await renderEmployeeRewards();
            
            // 標記表單有變更
            hasUnsavedChanges = true;
            
            showNotification('獎懲紀錄已移除，請記得儲存員工資料', 'success');
        }
    </script>
</body>

</html>