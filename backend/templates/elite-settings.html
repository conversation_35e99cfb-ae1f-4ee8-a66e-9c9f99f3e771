<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統設定 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">系統設定</h1>
            <p class="text-gray-600">配置系統參數、管理權限設定</p>
        </div>

        <!-- 設定導航 -->
        <div class="bg-white rounded-2xl shadow-lg mb-6">
            <div class="flex border-b border-gray-200">
                <button class="setting-tab active px-6 py-4 text-sm font-medium border-b-2 border-brand-500 text-brand-600">
                    基本設定
                </button>
                <button class="setting-tab px-6 py-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                    考勤規則
                </button>
                <button class="setting-tab px-6 py-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                    通知設定
                </button>
                <button class="setting-tab px-6 py-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                    權限管理
                </button>
                <button class="setting-tab px-6 py-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                    系統維護
                </button>
            </div>
        </div>

        <!-- 設定內容 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 基本設定 -->
            <div class="setting-content active lg:col-span-2" id="basic-settings">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">基本設定</h3>

                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">公司名稱</label>
                            <input type="text" value="科技創新股份有限公司" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">時區</label>
                                <select class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                    <option>Asia/Taipei (UTC+8)</option>
                                    <option>Asia/Shanghai (UTC+8)</option>
                                    <option>Asia/Tokyo (UTC+9)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">語言</label>
                                <select class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                    <option>繁體中文</option>
                                    <option>簡體中文</option>
                                    <option>English</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">上班時間</label>
                                <input type="time" value="09:00" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">下班時間</label>
                                <input type="time" value="18:00" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">工作日設定</label>
                            <div class="flex flex-wrap gap-2">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週一</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週二</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週三</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週四</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週五</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週六</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="rounded border-gray-300 text-brand-600 focus:ring-brand-500">
                                    <span class="text-sm text-gray-700">週日</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 考勤規則 -->
            <div class="setting-content hidden lg:col-span-2" id="attendance-rules">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">考勤規則設定</h3>

                    <div class="space-y-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">遲到寬限時間（分鐘）</label>
                                <input type="number" value="10" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">早退寬限時間（分鐘）</label>
                                <input type="number" value="10" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">缺勤標記時間（小時）</label>
                                <input type="number" value="2" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">加班最低時數（小時）</label>
                                <input type="number" value="1" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">自動標記缺勤</p>
                                    <p class="text-sm text-gray-500">超過指定時間未打卡自動標記為缺勤</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">GPS 定位驗證</p>
                                    <p class="text-sm text-gray-500">要求在指定範圍內才能打卡</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">人臉識別驗證</p>
                                    <p class="text-sm text-gray-500">使用人臉識別技術驗證身份</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">彈性工時</p>
                                    <p class="text-sm text-gray-500">允許員工在指定時間範圍內彈性上下班</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">GPS 打卡範圍設定</label>
                            <div class="grid grid-cols-3 gap-4">
                                <input type="text" placeholder="緯度" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <input type="text" placeholder="經度" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <input type="number" placeholder="範圍(公尺)" value="100" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知設定 -->
            <div class="setting-content hidden lg:col-span-2" id="notification-settings">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">通知設定</h3>

                    <div class="space-y-6">
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">電子郵件通知</h4>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">遲到提醒</p>
                                    <p class="text-sm text-gray-500">員工遲到時發送郵件通知</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">請假審批</p>
                                    <p class="text-sm text-gray-500">有新的請假申請時通知主管</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">每日報表</p>
                                    <p class="text-sm text-gray-500">每日考勤統計報表</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">系統通知</h4>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">瀏覽器推送</p>
                                    <p class="text-sm text-gray-500">在瀏覽器中顯示即時通知</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">聲音提醒</p>
                                    <p class="text-sm text-gray-500">重要事件時播放提示音</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">郵件伺服器設定</label>
                            <div class="grid grid-cols-2 gap-4">
                                <input type="text" placeholder="SMTP 伺服器" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <input type="number" placeholder="連接埠" value="587" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                            <div class="grid grid-cols-2 gap-4 mt-4">
                                <input type="email" placeholder="發送者郵箱" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <input type="password" placeholder="郵箱密碼" class="border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 權限管理 -->
            <div class="setting-content hidden lg:col-span-2" id="permission-management">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">權限管理</h3>

                    <div class="space-y-6">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">角色管理</h4>
                            <button class="bg-brand-500 text-white px-4 py-2 rounded-lg hover:bg-brand-600 transition-colors text-sm">
                                新增角色
                            </button>
                        </div>

                        <div class="space-y-4">
                            <!-- 系統管理員 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900">系統管理員</h5>
                                        <p class="text-sm text-gray-500">擁有所有系統權限</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="text-brand-600 hover:text-brand-700 text-sm">編輯</button>
                                        <button class="text-error-600 hover:text-error-700 text-sm">刪除</button>
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>員工管理</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>考勤管理</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>系統設定</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>報表查看</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>請假審批</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked disabled class="rounded border-gray-300 text-brand-600">
                                        <span>排班管理</span>
                                    </label>
                                </div>
                            </div>

                            <!-- 部門主管 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900">部門主管</h5>
                                        <p class="text-sm text-gray-500">管理部門員工考勤</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="text-brand-600 hover:text-brand-700 text-sm">編輯</button>
                                        <button class="text-error-600 hover:text-error-700 text-sm">刪除</button>
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="rounded border-gray-300 text-brand-600">
                                        <span>員工管理</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>考勤管理</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="rounded border-gray-300 text-brand-600">
                                        <span>系統設定</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>報表查看</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>請假審批</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>排班管理</span>
                                    </label>
                                </div>
                            </div>

                            <!-- 一般員工 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900">一般員工</h5>
                                        <p class="text-sm text-gray-500">基本考勤功能</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="text-brand-600 hover:text-brand-700 text-sm">編輯</button>
                                        <button class="text-error-600 hover:text-error-700 text-sm">刪除</button>
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="rounded border-gray-300 text-brand-600">
                                        <span>員工管理</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>考勤打卡</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" class="rounded border-gray-300 text-brand-600">
                                        <span>系統設定</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>個人報表</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>請假申請</span>
                                    </label>
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="rounded border-gray-300 text-brand-600">
                                        <span>查看排班</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統維護 -->
            <div class="setting-content hidden lg:col-span-2" id="system-maintenance">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">系統維護</h3>

                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 資料備份 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-4">資料備份</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">最後備份時間</span>
                                        <span class="font-medium">2024-05-28 12:00</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">備份檔案大小</span>
                                        <span class="font-medium">245 MB</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">自動備份</span>
                                        <span class="text-success-600 font-medium">已啟用</span>
                                    </div>
                                    <button class="w-full bg-brand-500 text-white py-2 px-4 rounded-lg hover:bg-brand-600 transition-colors text-sm">
                                        立即備份
                                    </button>
                                </div>
                            </div>

                            <!-- 系統清理 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-4">系統清理</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">日誌檔案</span>
                                        <span class="font-medium">128 MB</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">暫存檔案</span>
                                        <span class="font-medium">45 MB</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">過期資料</span>
                                        <span class="font-medium">89 MB</span>
                                    </div>
                                    <button class="w-full bg-warning-500 text-white py-2 px-4 rounded-lg hover:bg-warning-600 transition-colors text-sm">
                                        清理系統
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 系統監控 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-4">系統監控</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">CPU 使用率</span>
                                        <span class="font-medium">23%</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">記憶體使用率</span>
                                        <span class="font-medium">67%</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">磁碟使用率</span>
                                        <span class="font-medium">45%</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">系統運行時間</span>
                                        <span class="font-medium">15天 8小時</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 系統更新 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-4">系統更新</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">當前版本</span>
                                        <span class="font-medium">v2.1.0</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">最新版本</span>
                                        <span class="font-medium">v2.1.2</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">更新狀態</span>
                                        <span class="text-warning-600 font-medium">有新版本</span>
                                    </div>
                                    <button class="w-full bg-success-500 text-white py-2 px-4 rounded-lg hover:bg-success-600 transition-colors text-sm">
                                        檢查更新
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-error-50 border border-error-200 rounded-lg p-4">
                            <h4 class="font-medium text-error-900 mb-2">危險操作</h4>
                            <p class="text-sm text-error-700 mb-4">以下操作將影響系統運行，請謹慎操作</p>
                            <div class="flex flex-wrap gap-3">
                                <button class="bg-error-500 text-white py-2 px-4 rounded-lg hover:bg-error-600 transition-colors text-sm">
                                    重啟系統
                                </button>
                                <button class="bg-error-600 text-white py-2 px-4 rounded-lg hover:bg-error-700 transition-colors text-sm">
                                    重置系統
                                </button>
                                <button class="bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors text-sm">
                                    匯出日誌
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 側邊欄 - 快速操作 -->
            <div class="space-y-6">
                <!-- 系統狀態 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">系統狀態</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">系統版本</span>
                            <span class="text-sm font-medium text-gray-900">v2.1.0</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">資料庫狀態</span>
                            <span class="flex items-center space-x-1">
                                <div class="w-2 h-2 bg-success-500 rounded-full"></div>
                                <span class="text-sm font-medium text-success-600">正常</span>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">最後備份</span>
                            <span class="text-sm font-medium text-gray-900">2小時前</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">在線用戶</span>
                            <span class="text-sm font-medium text-gray-900">24人</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button class="w-full bg-brand-500 text-white py-2 px-4 rounded-lg hover:bg-brand-600 transition-colors text-sm font-medium">
                            備份資料庫
                        </button>
                        <button class="w-full bg-success-500 text-white py-2 px-4 rounded-lg hover:bg-success-600 transition-colors text-sm font-medium">
                            匯出設定
                        </button>
                        <button class="w-full bg-warning-500 text-white py-2 px-4 rounded-lg hover:bg-warning-600 transition-colors text-sm font-medium">
                            清理日誌
                        </button>
                        <button class="w-full bg-error-500 text-white py-2 px-4 rounded-lg hover:bg-error-600 transition-colors text-sm font-medium">
                            重啟系統
                        </button>
                    </div>
                </div>

                <!-- 最近活動 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">系統日誌</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start space-x-2">
                            <div class="w-2 h-2 bg-success-500 rounded-full mt-2"></div>
                            <div>
                                <p class="text-gray-900">系統備份完成</p>
                                <p class="text-gray-500 text-xs">2小時前</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-2">
                            <div class="w-2 h-2 bg-brand-500 rounded-full mt-2"></div>
                            <div>
                                <p class="text-gray-900">新增用戶：張小明</p>
                                <p class="text-gray-500 text-xs">4小時前</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-2">
                            <div class="w-2 h-2 bg-warning-500 rounded-full mt-2"></div>
                            <div>
                                <p class="text-gray-900">設定變更：考勤規則</p>
                                <p class="text-gray-500 text-xs">1天前</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存按鈕 -->
        <div class="fixed bottom-6 right-6">
            <button class="bg-gradient-to-r from-brand-500 to-brand-600 text-white px-6 py-3 rounded-xl font-medium hover:from-brand-600 hover:to-brand-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2">
                <i data-lucide="save" class="w-4 h-4"></i>
                <span>保存設定</span>
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        lucide.createIcons();

        // 全域變數
        let currentSettings = {};
        let systemStatus = {};

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemSettings();
            loadSystemStatus();
            initializeEventListeners();

            // 頁面載入動畫
            const cards = document.querySelectorAll('.bg-white');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease-out';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // 載入系統設定
        async function loadSystemSettings() {
            try {
                const response = await fetch('/api/settings');
                if (response.ok) {
                    currentSettings = await response.json();
                    populateBasicSettings();
                    loadAttendanceRules();
                    loadNotificationSettings();
                    loadPermissionSettings();
                } else {
                    console.error('載入系統設定失敗');
                }
            } catch (error) {
                console.error('載入系統設定錯誤:', error);
            }
        }

        // 載入系統狀態
        async function loadSystemStatus() {
            try {
                // 暫時移除API調用，避免404錯誤
                // const response = await fetch('/api/settings/system-status');
                // if (response.ok) {
                //     systemStatus = await response.json();
                //     updateSystemStatusDisplay();
                // } else {
                //     console.error('載入系統狀態失敗');
                // }

                // 提供預設的系統狀態資訊
                systemStatus = {
                    status: "healthy",
                    database_status: "connected",
                    services: {
                        web_server: "running",
                        database: "connected",
                        file_system: "accessible"
                    },
                    application: {
                        name: "AttendanceOS Elite",
                        version: "1.0.0",
                        environment: "development"
                    }
                };
                updateSystemStatusDisplay();

            } catch (error) {
                console.error('載入系統狀態錯誤:', error);
            }
        }

        // 填充基本設定
        function populateBasicSettings() {
            const basic = currentSettings.basic || {};

            // 填充表單欄位
            const companyNameInput = document.querySelector('input[value="科技創新股份有限公司"]');
            if (companyNameInput && basic.company_name) {
                companyNameInput.value = basic.company_name;
            }

            const timezoneSelect = document.querySelector('select');
            if (timezoneSelect && basic.timezone) {
                timezoneSelect.value = basic.timezone;
            }

            const startTimeInput = document.querySelector('input[value="09:00"]');
            if (startTimeInput && basic.work_start_time) {
                startTimeInput.value = basic.work_start_time;
            }

            const endTimeInput = document.querySelector('input[value="18:00"]');
            if (endTimeInput && basic.work_end_time) {
                endTimeInput.value = basic.work_end_time;
            }
        }

        // 載入考勤規則
        async function loadAttendanceRules() {
            try {
                const response = await fetch('/api/settings/attendance-rules');
                if (response.ok) {
                    const rules = await response.json();
                    populateAttendanceRules(rules);
                }
            } catch (error) {
                console.error('載入考勤規則錯誤:', error);
            }
        }

        // 填充考勤規則
        function populateAttendanceRules(rules) {
            const attendanceSection = document.getElementById('attendance-rules');
            if (!attendanceSection) return;

            // 更新數值欄位
            const inputs = attendanceSection.querySelectorAll('input[type="number"]');
            inputs.forEach(input => {
                const label = input.previousElementSibling && input.previousElementSibling.textContent;
                if (label && label.includes('遲到寬限')) {
                    input.value = rules.late_tolerance_minutes;
                } else if (label && label.includes('早退寬限')) {
                    input.value = rules.early_leave_tolerance_minutes;
                } else if (label && label.includes('缺勤標記')) {
                    input.value = rules.absent_mark_hours;
                } else if (label && label.includes('加班最低')) {
                    input.value = rules.overtime_minimum_hours;
                }
            });
        }

        // 載入通知設定
        async function loadNotificationSettings() {
            try {
                const response = await fetch('/api/settings/notifications');
                if (response.ok) {
                    const settings = await response.json();
                    populateNotificationSettings(settings);
                }
            } catch (error) {
                console.error('載入通知設定錯誤:', error);
            }
        }

        // 填充通知設定
        function populateNotificationSettings(settings) {
            // 這裡可以根據實際的通知設定頁面結構來填充
            console.log('通知設定:', settings);
        }

        // 載入權限設定
        async function loadPermissionSettings() {
            try {
                const response = await fetch('/api/settings/permissions');
                if (response.ok) {
                    const data = await response.json();
                    populatePermissionSettings(data.roles);
                }
            } catch (error) {
                console.error('載入權限設定錯誤:', error);
            }
        }

        // 填充權限設定
        function populatePermissionSettings(roles) {
            const permissionSection = document.getElementById('permission-management');
            if (!permissionSection) return;

            // 找到角色容器
            const roleContainer = permissionSection.querySelector('.space-y-4');
            if (!roleContainer) return;

            // 清空現有內容（保留標題）
            const existingRoles = roleContainer.querySelectorAll('.border');
            existingRoles.forEach(role => role.remove());

            // 動態生成角色卡片
            roles.forEach(role => {
                const roleCard = createRoleCard(role);
                roleContainer.appendChild(roleCard);
            });

            console.log('權限設定已載入:', roles);
        }

        // 創建角色卡片
        function createRoleCard(role) {
            const div = document.createElement('div');
            div.className = 'border border-gray-200 rounded-lg p-4';

            const isSystemAdmin = role.permission_level >= 99;
            const permissionCheckboxes = role.permissions.map(permission => `
                <label class="flex items-center space-x-2">
                    <input type="checkbox" checked ${isSystemAdmin ? 'disabled' : ''} class="rounded border-gray-300 text-brand-600">
                    <span>${permission}</span>
                </label>
            `).join('');

            div.innerHTML = `
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h5 class="font-medium text-gray-900">${role.role_name}</h5>
                        <p class="text-sm text-gray-500">${role.description} (等級: ${role.permission_level})</p>
                        <p class="text-xs text-gray-400">使用者數量: ${role.user_count}人</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="text-brand-600 hover:text-brand-700 text-sm edit-role-btn" data-role-id="${role.id}">編輯</button>
                        ${!isSystemAdmin ? '<button class="text-error-600 hover:text-error-700 text-sm delete-role-btn" data-role-id="' + role.id + '">刪除</button>' : ''}
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-4 text-sm">
                    ${permissionCheckboxes}
                </div>
            `;

            // 添加編輯功能
            const editBtn = div.querySelector('.edit-role-btn');
            if (editBtn) {
                editBtn.addEventListener('click', () => editRole(role));
            }

            return div;
        }

        // 編輯角色功能
        function editRole(role) {
            const newLevel = prompt(`請輸入 "${role.role_name}" 的新權限等級 (1-99):`, role.permission_level);

            if (newLevel && !isNaN(newLevel)) {
                const level = parseInt(newLevel);
                if (level >= 1 && level <= 99) {
                    updateRolePermission(role.id, level);
                } else {
                    alert('權限等級必須在 1-99 之間');
                }
            }
        }

        // 更新角色權限
        async function updateRolePermission(roleId, permissionLevel) {
            try {
                const response = await fetch('/api/settings/permissions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        role_id: roleId,
                        permission_level: permissionLevel
                    })
                });

                if (response.ok) {
                    alert('權限更新成功！');
                    // 重新載入權限設定
                    loadPermissionSettings();
                } else {
                    const error = await response.json();
                    alert('權限更新失敗: ' + error.error);
                }
            } catch (error) {
                console.error('更新權限錯誤:', error);
                alert('權限更新失敗');
            }
        }

        // 更新系統狀態顯示
        function updateSystemStatusDisplay() {
            // 更新系統版本
            const versionElement = document.querySelector('.text-right .font-medium');
            if (versionElement && systemStatus.system_version) {
                versionElement.textContent = systemStatus.system_version;
            }

            // 更新資料庫狀態
            const statusElements = document.querySelectorAll('.text-right .text-success-500');
            if (statusElements.length > 0 && systemStatus.database_status) {
                statusElements[0].textContent = systemStatus.database_status;
            }

            // 更新在線用戶數
            const userCountElement = document.querySelector('.text-right .font-medium:last-child');
            if (userCountElement && systemStatus.online_users) {
                userCountElement.textContent = systemStatus.online_users + '人';
            }
        }

        // 初始化事件監聽器
        function initializeEventListeners() {
            // TAB切換功能
            document.querySelectorAll('.setting-tab').forEach((tab, index) => {
                tab.addEventListener('click', function() {
                    const tabName = this.textContent.trim();

                    // 移除所有活動狀態
                    document.querySelectorAll('.setting-tab').forEach(t => {
                        t.classList.remove('active', 'border-brand-500', 'text-brand-600');
                        t.classList.add('text-gray-500');
                    });

                    // 隱藏所有內容區域
                    document.querySelectorAll('.setting-content').forEach(content => {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    });

                    // 添加活動狀態到當前標籤
                    this.classList.add('active', 'border-brand-500', 'text-brand-600');
                    this.classList.remove('text-gray-500');

                    // 顯示對應的內容區域
                    let targetId;
                    switch (tabName) {
                        case '基本設定':
                            targetId = 'basic-settings';
                            break;
                        case '考勤規則':
                            targetId = 'attendance-rules';
                            break;
                        case '通知設定':
                            targetId = 'notification-settings';
                            break;
                        case '權限管理':
                            targetId = 'permission-management';
                            break;
                        case '系統維護':
                            targetId = 'system-maintenance';
                            break;
                        default:
                            targetId = 'basic-settings';
                    }

                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');

                        // 添加淡入動畫
                        targetContent.style.opacity = '0';
                        setTimeout(() => {
                            targetContent.style.transition = 'opacity 0.3s ease-in-out';
                            targetContent.style.opacity = '1';
                        }, 50);
                    }
                });
            });

            // 保存設定按鈕
            const saveButton = document.querySelector('button[class*="from-brand-500"]');
            if (saveButton) {
                saveButton.addEventListener('click', saveAllSettings);
            }

            // 快速操作按鈕功能
            document.querySelectorAll('.space-y-3 button').forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.textContent.trim();

                    if (action === '備份資料庫') {
                        backupDatabase();
                    } else if (action === '匯出設定') {
                        exportSettings();
                    } else if (action === '清理日誌') {
                        clearLogs();
                    } else if (action === '重啟系統') {
                        restartSystem();
                    } else {
                        // 通用處理
                        handleQuickAction(this, action);
                    }
                });
            });
        }

        // 保存所有設定
        async function saveAllSettings() {
            const button = document.querySelector('button[class*="from-brand-500"]');
            const originalText = button.querySelector('span').textContent;

            try {
                button.querySelector('span').textContent = '保存中...';
                button.disabled = true;

                // 收集基本設定
                const basicSettings = collectBasicSettings();

                // 收集考勤規則
                const attendanceRules = collectAttendanceRules();

                // 保存基本設定
                if (Object.keys(basicSettings).length > 0) {
                    await saveSettings('basic', basicSettings);
                }

                // 保存考勤規則
                if (Object.keys(attendanceRules).length > 0) {
                    await saveAttendanceRules(attendanceRules);
                }

                button.querySelector('span').textContent = '保存成功！';
                button.classList.remove('from-brand-500', 'to-brand-600');
                button.classList.add('from-success-500', 'to-success-600');

                setTimeout(() => {
                    button.querySelector('span').textContent = originalText;
                    button.disabled = false;
                    button.classList.remove('from-success-500', 'to-success-600');
                    button.classList.add('from-brand-500', 'to-brand-600');
                }, 2000);

            } catch (error) {
                console.error('保存設定錯誤:', error);
                button.querySelector('span').textContent = '保存失敗';
                button.classList.remove('from-brand-500', 'to-brand-600');
                button.classList.add('bg-red-500', 'hover:bg-red-600');

                setTimeout(() => {
                    button.querySelector('span').textContent = originalText;
                    button.disabled = false;
                    button.classList.remove('bg-red-500', 'hover:bg-red-600');
                    button.classList.add('from-brand-500', 'to-brand-600');
                }, 2000);
            }
        }

        // 收集基本設定
        function collectBasicSettings() {
            const settings = {};

            const companyNameInput = document.querySelector('input[type="text"]');
            if (companyNameInput) {
                settings.company_name = companyNameInput.value;
            }

            const timezoneSelect = document.querySelector('select');
            if (timezoneSelect) {
                settings.timezone = timezoneSelect.value;
            }

            const timeInputs = document.querySelectorAll('input[type="time"]');
            if (timeInputs.length >= 2) {
                settings.work_start_time = timeInputs[0].value;
                settings.work_end_time = timeInputs[1].value;
            }

            return settings;
        }

        // 收集考勤規則
        function collectAttendanceRules() {
            const rules = {};
            const attendanceSection = document.getElementById('attendance-rules');

            if (attendanceSection) {
                const inputs = attendanceSection.querySelectorAll('input[type="number"]');
                inputs.forEach(input => {
                    const label = input.previousElementSibling && input.previousElementSibling.textContent;
                    if (label && label.includes('遲到寬限')) {
                        rules.late_tolerance_minutes = input.value;
                    } else if (label && label.includes('早退寬限')) {
                        rules.early_leave_tolerance_minutes = input.value;
                    } else if (label && label.includes('缺勤標記')) {
                        rules.absent_mark_hours = input.value;
                    } else if (label && label.includes('加班最低')) {
                        rules.overtime_minimum_hours = input.value;
                    }
                });
            }

            return rules;
        }

        // 保存設定到後端
        async function saveSettings(category, settings) {
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    [category]: settings
                })
            });

            if (!response.ok) {
                throw new Error('保存設定失敗');
            }
        }

        // 保存考勤規則
        async function saveAttendanceRules(rules) {
            const response = await fetch('/api/settings/attendance-rules', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(rules)
            });

            if (!response.ok) {
                throw new Error('保存考勤規則失敗');
            }
        }

        // 備份資料庫
        async function backupDatabase() {
            try {
                const response = await fetch('/api/settings/backup', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(`資料庫備份成功！\n檔案：${result.backup_file}`);
                } else {
                    alert('資料庫備份失敗');
                }
            } catch (error) {
                console.error('備份錯誤:', error);
                alert('資料庫備份失敗');
            }
        }

        // 匯出設定
        async function exportSettings() {
            try {
                const response = await fetch('/api/settings/export');

                if (response.ok) {
                    const data = await response.json();
                    const blob = new Blob([JSON.stringify(data, null, 2)], {
                        type: 'application/json'
                    });

                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `attendance_settings_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    alert('設定匯出成功！');
                } else {
                    alert('設定匯出失敗');
                }
            } catch (error) {
                console.error('匯出錯誤:', error);
                alert('設定匯出失敗');
            }
        }

        // 清理日誌
        function clearLogs() {
            if (confirm('確定要清理系統日誌嗎？此操作無法復原。')) {
                // 這裡可以添加清理日誌的API調用
                alert('日誌清理功能開發中...');
            }
        }

        // 重啟系統
        function restartSystem() {
            if (confirm('確定要重啟系統嗎？這將中斷所有用戶的連接。')) {
                alert('系統重啟功能開發中...');
            }
        }

        // 通用快速操作處理
        function handleQuickAction(button, action) {
            const originalText = button.textContent;

            // 顯示處理中狀態
            button.textContent = '處理中...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = '完成！';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                }, 1500);
            }, 2000);
        }

        // 切換開關動畫
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // 添加切換動畫效果
                const toggle = this.nextElementSibling;
                if (toggle) {
                    toggle.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        toggle.style.transform = 'scale(1)';
                    }, 150);
                }
            });
        });

        // 權限管理 - 角色編輯功能
        document.querySelectorAll('button[class*="text-brand-600"]').forEach(button => {
            if (button.textContent.trim() === '編輯') {
                button.addEventListener('click', function() {
                    const roleCard = this.closest('.border');
                    const checkboxes = roleCard.querySelectorAll('input[type="checkbox"]:not([disabled])');

                    checkboxes.forEach(cb => {
                        cb.disabled = !cb.disabled;
                    });

                    this.textContent = this.textContent === '編輯' ? '保存' : '編輯';
                });
            }
        });

        // 系統監控 - 實時更新
        function updateSystemStats() {
            loadSystemStatus();
        }

        // 每30秒更新一次系統狀態
        setInterval(updateSystemStats, 30000);
    </script>
</body>

</html>