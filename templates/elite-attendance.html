<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤管理 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .clock-animation {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%,
            100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        .success-ripple {
            animation: ripple 0.6s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- 時間顯示 -->
            <div class="text-center mb-8">
                <div class="text-6xl font-bold text-gray-900 mb-2" id="currentTime">09:00</div>
                <div class="text-lg text-gray-500" id="currentDate">2024年5月28日 星期二</div>
            </div>

            <!-- 打卡區域 -->
            <div class="bg-white rounded-3xl p-8 shadow-xl mb-6">
                <div class="text-center">
                    <!-- 員工選擇 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">選擇員工</label>
                        <select id="employeeSelect" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option value="">請選擇員工</option>
                        </select>
                    </div>

                    <!-- 打卡按鈕 -->
                    <div class="relative mb-6">
                        <button id="clockButton" class="w-32 h-32 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center text-white shadow-2xl hover:shadow-3xl transition-all duration-300 clock-animation mx-auto">
                            <i data-lucide="clock" class="w-12 h-12"></i>
                        </button>
                        <!-- 成功動畫 -->
                        <div id="successRipple" class="absolute inset-0 bg-success-500 rounded-full opacity-0 pointer-events-none"></div>
                    </div>

                    <!-- 狀態顯示 -->
                    <div id="statusDisplay" class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">準備上班打卡</h2>
                        <p class="text-gray-500">點擊上方按鈕進行上班打卡</p>
                    </div>

                    <!-- 快速操作 -->
                    <div class="grid grid-cols-2 gap-4">
                        <button class="flex flex-col items-center justify-center p-4 bg-success-50 rounded-xl hover:bg-success-100 transition-all duration-200">
                            <i data-lucide="log-in" class="w-6 h-6 text-success-600 mb-2"></i>
                            <span class="text-sm font-medium text-success-700">上班打卡</span>
                        </button>

                        <button class="flex flex-col items-center justify-center p-4 bg-error-50 rounded-xl hover:bg-error-100 transition-all duration-200">
                            <i data-lucide="log-out" class="w-6 h-6 text-error-600 mb-2"></i>
                            <span class="text-sm font-medium text-error-700">下班打卡</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 今日記錄 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">今日記錄</h3>
                <div id="todayRecords" class="space-y-3">
                    <!-- 記錄將通過JavaScript動態載入 -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化圖標
            lucide.createIcons();

            // 載入員工列表
            loadEmployees();

            // 添加員工選擇事件監聽器
            document.getElementById('employeeSelect').addEventListener('change', onEmployeeChange);

            // 開始時間更新
            updateTime();
            setInterval(updateTime, 1000);

            // 初始化狀態顯示
            updateAttendanceStatus();
        });

        // 全域變數
        const API_BASE = '/api';
        let currentEmployee = null; // 將改為動態選擇
        let todayAttendance = null;
        let employees = [];

        // 載入員工列表
        async function loadEmployees() {
            try {
                const response = await fetch(`${API_BASE}/employees`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                employees = data.employees || [];
                populateEmployeeSelect();

            } catch (error) {
                console.error('載入員工列表失敗:', error);
                showError('載入員工列表失敗: ' + error.message);
            }
        }

        // 填充員工選擇下拉選單
        function populateEmployeeSelect() {
            const select = document.getElementById('employeeSelect');
            select.innerHTML = '<option value="">請選擇員工</option>';

            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.name} (${employee.employee_id})`;
                select.appendChild(option);
            });

            // 預設選擇第一個員工
            if (employees.length > 0) {
                select.value = employees[0].id;
                onEmployeeChange();
            }
        }

        // 員工選擇變更事件
        function onEmployeeChange() {
            const select = document.getElementById('employeeSelect');
            const selectedEmployeeId = select.value;

            if (selectedEmployeeId) {
                const selectedEmployee = employees.find(emp => emp.id == selectedEmployeeId);
                if (selectedEmployee) {
                    currentEmployee = {
                        id: selectedEmployee.id,
                        name: selectedEmployee.name,
                        employee_id: selectedEmployee.employee_id
                    };

                    // 重新載入考勤狀態
                    loadTodayAttendance();
                }
            } else {
                currentEmployee = null;
                todayAttendance = null;
                updateAttendanceStatus();
                displayTodayRecords();
            }
        }

        // 更新時間
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            const dateString = now.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }

        // 載入今日考勤狀態
        async function loadTodayAttendance() {
            if (!currentEmployee) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/attendance/today/${currentEmployee.id}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                todayAttendance = data;
                updateAttendanceStatus();
                displayTodayRecords();

            } catch (error) {
                console.error('載入今日考勤失敗:', error);
                showError('載入考勤資料失敗: ' + error.message);
            }
        }

        // 更新考勤狀態顯示
        function updateAttendanceStatus() {
            const statusDisplay = document.getElementById('statusDisplay');
            const clockButton = document.getElementById('clockButton');

            if (!currentEmployee) {
                statusDisplay.innerHTML = `
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">請選擇員工</h2>
                    <p class="text-gray-500">請先選擇要打卡的員工</p>
                `;
                clockButton.innerHTML = '<i data-lucide="user" class="w-12 h-12"></i>';
                clockButton.className = 'w-32 h-32 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center text-white shadow-2xl mx-auto';
                clockButton.disabled = true;
                clockButton.onclick = null;
                lucide.createIcons();
                return;
            }

            if (!todayAttendance || !todayAttendance.has_attendance) {
                // 尚未打卡
                statusDisplay.innerHTML = `
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">準備上班打卡</h2>
                    <p class="text-gray-500">員工：${currentEmployee.name}</p>
                    <p class="text-gray-500">點擊上方按鈕進行上班打卡</p>
                `;
                clockButton.innerHTML = '<i data-lucide="log-in" class="w-12 h-12"></i>';
                clockButton.className = 'w-32 h-32 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center text-white shadow-2xl hover:shadow-3xl transition-all duration-300 clock-animation mx-auto';
                clockButton.disabled = false;
                clockButton.onclick = () => performClockIn();

            } else {
                if (todayAttendance.has_checked_in && !todayAttendance.has_checked_out) {
                    // 已上班打卡，等待下班打卡
                    statusDisplay.innerHTML = `
                        <h2 class="text-2xl font-bold text-success-600 mb-2">已上班打卡</h2>
                        <p class="text-gray-500">員工：${currentEmployee.name}</p>
                        <p class="text-gray-500">上班時間：${formatTime(todayAttendance.check_in)}</p>
                        <p class="text-gray-500 mt-2">點擊按鈕進行下班打卡</p>
                    `;
                    clockButton.innerHTML = '<i data-lucide="log-out" class="w-12 h-12"></i>';
                    clockButton.className = 'w-32 h-32 bg-gradient-to-br from-error-500 to-error-600 rounded-full flex items-center justify-center text-white shadow-2xl hover:shadow-3xl transition-all duration-300 clock-animation mx-auto';
                    clockButton.disabled = false;
                    clockButton.onclick = () => performClockOut();

                } else if (todayAttendance.has_checked_in && todayAttendance.has_checked_out) {
                    // 已完成今日考勤
                    statusDisplay.innerHTML = `
                        <h2 class="text-2xl font-bold text-gray-600 mb-2">今日考勤完成</h2>
                        <p class="text-gray-500">員工：${currentEmployee.name}</p>
                        <p class="text-gray-500">上班：${formatTime(todayAttendance.check_in)}</p>
                        <p class="text-gray-500">下班：${formatTime(todayAttendance.check_out)}</p>
                    `;
                    clockButton.innerHTML = '<i data-lucide="check" class="w-12 h-12"></i>';
                    clockButton.className = 'w-32 h-32 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center text-white shadow-2xl hover:shadow-3xl transition-all duration-300 mx-auto';
                    clockButton.disabled = true;
                    clockButton.onclick = null;
                }
            }

            // 重新初始化圖標
            lucide.createIcons();
        }

        // 執行上班打卡
        async function performClockIn() {
            if (!currentEmployee) {
                showError('請先選擇員工');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/attendance/clock-in`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_id: currentEmployee.id,
                        note: '正常上班打卡'
                    })
                });

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                showSuccess(data.message || '上班打卡成功');

                // 觸發成功動畫
                triggerSuccessAnimation();

                // 重新載入考勤狀態
                setTimeout(() => {
                    loadTodayAttendance();
                }, 1000);

            } catch (error) {
                console.error('上班打卡失敗:', error);
                showError('上班打卡失敗: ' + error.message);
            }
        }

        // 執行下班打卡
        async function performClockOut() {
            if (!currentEmployee) {
                showError('請先選擇員工');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/attendance/clock-out`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_id: currentEmployee.id,
                        note: '正常下班打卡'
                    })
                });

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                showSuccess(data.message || '下班打卡成功');

                // 觸發成功動畫
                triggerSuccessAnimation();

                // 重新載入考勤狀態
                setTimeout(() => {
                    loadTodayAttendance();
                }, 1000);

            } catch (error) {
                console.error('下班打卡失敗:', error);
                showError('下班打卡失敗: ' + error.message);
            }
        }

        // 顯示今日記錄
        function displayTodayRecords() {
            const recordsContainer = document.getElementById('todayRecords');
            recordsContainer.innerHTML = '';

            if (!todayAttendance || !todayAttendance.has_attendance) {
                recordsContainer.innerHTML = `
                    <div class="text-center py-4 text-gray-500">
                        <i data-lucide="calendar-x" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>今日尚無考勤記錄</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            let recordsHtml = '';

            if (todayAttendance.check_in) {
                const statusColor = todayAttendance.status === 'late' ? 'warning' : 'success';
                const statusText = todayAttendance.status === 'late' ? '遲到打卡' : '正常上班';

                recordsHtml += `
                    <div class="flex items-center justify-between p-3 bg-${statusColor}-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 rounded-full bg-${statusColor}-500"></div>
                            <span class="font-medium text-gray-900">${statusText}</span>
                        </div>
                        <span class="text-sm text-gray-500">${formatTime(todayAttendance.check_in)}</span>
                    </div>
                `;
            }

            if (todayAttendance.check_out) {
                const statusColor = todayAttendance.status === 'early_leave' ? 'warning' : 'error';
                const statusText = todayAttendance.status === 'early_leave' ? '早退打卡' : '正常下班';

                recordsHtml += `
                    <div class="flex items-center justify-between p-3 bg-${statusColor}-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 rounded-full bg-${statusColor}-500"></div>
                            <span class="font-medium text-gray-900">${statusText}</span>
                        </div>
                        <span class="text-sm text-gray-500">${formatTime(todayAttendance.check_out)}</span>
                    </div>
                `;
            }

            if (recordsHtml) {
                recordsContainer.innerHTML = recordsHtml;
            } else {
                recordsContainer.innerHTML = `
                    <div class="text-center py-4 text-gray-500">
                        <i data-lucide="calendar-x" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>今日尚無考勤記錄</p>
                    </div>
                `;
            }

            lucide.createIcons();
        }

        // 格式化時間
        function formatTime(timeString) {
            if (!timeString) return '';
            const date = new Date(timeString);
            return date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        }

        // 顯示成功訊息
        function showSuccess(message) {
            const successElement = document.createElement('div');
            successElement.className = 'fixed top-4 right-4 bg-success-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            successElement.textContent = message;
            document.body.appendChild(successElement);

            // 顯示動畫
            setTimeout(() => {
                successElement.classList.remove('translate-x-full');
            }, 100);

            // 自動隱藏
            setTimeout(() => {
                successElement.classList.add('translate-x-full');
                setTimeout(() => {
                    successElement.remove();
                }, 300);
            }, 3000);
        }

        // 顯示錯誤訊息
        function showError(message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'fixed top-4 right-4 bg-error-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            errorElement.textContent = message;
            document.body.appendChild(errorElement);

            // 顯示動畫
            setTimeout(() => {
                errorElement.classList.remove('translate-x-full');
            }, 100);

            // 自動隱藏
            setTimeout(() => {
                errorElement.classList.add('translate-x-full');
                setTimeout(() => {
                    errorElement.remove();
                }, 300);
            }, 5000);
        }

        // 快速操作按鈕事件
        document.addEventListener('DOMContentLoaded', function() {
            const quickButtons = document.querySelectorAll('.grid.grid-cols-2 button');

            quickButtons[0].addEventListener('click', performClockIn); // 上班打卡
            quickButtons[1].addEventListener('click', performClockOut); // 下班打卡
        });

        // 觸發成功動畫
        function triggerSuccessAnimation() {
            const ripple = document.getElementById('successRipple');
            if (ripple) {
                ripple.classList.add('success-ripple');
                setTimeout(() => {
                    ripple.classList.remove('success-ripple');
                }, 1000);
            }
        }
    </script>
</body>

</html>