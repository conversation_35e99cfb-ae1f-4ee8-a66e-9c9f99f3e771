"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
    CalendarClock,
    Search,
    Plus,
    Edit,
    Trash2,
    ArrowLeft,
    Clock,
    Timer,
    Coffee,
    Zap,
    Sunrise,
    Sunset,
    Calendar,
    Tag,
    Code,
    Palette,
    FileText,
    Info,
    RefreshCw,
    Save,
    X
} from 'lucide-react'
import Link from 'next/link'

// 班別資料介面
interface Shift {
    id?: number
    name: string
    code: string
    color_code: string
    start_time: string
    end_time: string
    day_start_time: string
    break_start_time?: string
    break_duration_minutes: number
    late_tolerance_minutes: number  // 上班容許遲到分鐘
    early_leave_tolerance_minutes: number  // 下班容許早退分鐘
    pre_overtime_threshold_minutes: number
    post_overtime_threshold_minutes: number
    enable_pre_overtime: boolean
    enable_post_overtime: boolean
    auto_calculate_overtime: boolean
    is_default: boolean  // 是否為預設班表
    description?: string
    created_at?: string
    is_active?: boolean
}

export default function ShiftsPage() {
    const router = useRouter()
    const [shifts, setShifts] = useState<Shift[]>([])
    const [loading, setLoading] = useState(false)
    const [showModal, setShowModal] = useState(false)
    const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
    const [selectedShift, setSelectedShift] = useState<Shift | null>(null)
    const [formData, setFormData] = useState<Shift>({
        name: '',
        code: '',
        color_code: '#3B82F6',
        start_time: '',
        end_time: '',
        day_start_time: '06:00',
        break_start_time: '',
        break_duration_minutes: 60,
        late_tolerance_minutes: 0,
        early_leave_tolerance_minutes: 0,
        pre_overtime_threshold_minutes: 0,
        post_overtime_threshold_minutes: 0,
        enable_pre_overtime: false,
        enable_post_overtime: false,
        auto_calculate_overtime: true,
        is_default: false,
        description: ''
    })

    // 載入班別資料
    const loadShifts = async () => {
        setLoading(true)
        try {
            console.log('開始載入班別資料...')
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/shifts`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                cache: 'no-cache'
            })
            console.log('API 回應狀態:', response.status, response.statusText)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`)
            }

            const data = await response.json()
            console.log('API 回應資料:', data)

            if (data.shifts && Array.isArray(data.shifts)) {
                console.log('成功載入班別數量:', data.shifts.length)
                setShifts(data.shifts)
            } else {
                console.error('載入班別資料失敗: 無效的資料格式', data)
                setShifts([])
            }
        } catch (error) {
            console.error('載入班別資料錯誤:', error)
            if (error instanceof TypeError && error.message.includes('fetch')) {
                alert('網路連線錯誤，請檢查後端服務是否正常運行')
            }
            setShifts([])
        } finally {
            setLoading(false)
        }
    }

    // 初始載入
    useEffect(() => {
        loadShifts()
    }, [])

    // 計算工作時數
    const calculateWorkHours = (startTime: string, endTime: string, breakMinutes: number = 0) => {
        if (!startTime || !endTime) return '0.0'

        const start = new Date(`2000-01-01 ${startTime}`)
        const end = new Date(`2000-01-01 ${endTime}`)

        // 處理跨日情況
        if (end < start) {
            end.setDate(end.getDate() + 1)
        }

        const diffMs = end.getTime() - start.getTime()
        const diffHours = diffMs / (1000 * 60 * 60)
        const workHours = diffHours - (breakMinutes / 60)

        return workHours.toFixed(1)
    }

    // 獲取加班設定文字
    const getOvertimeText = (shift: Shift) => {
        if (!shift.auto_calculate_overtime) {
            return '不計算加班'
        }

        const parts = []
        if (shift.enable_pre_overtime) {
            parts.push(`上班前${shift.pre_overtime_threshold_minutes}分鐘`)
        }
        if (shift.enable_post_overtime) {
            parts.push(`下班後${shift.post_overtime_threshold_minutes}分鐘`)
        }

        if (parts.length === 0) {
            return '無加班設定'
        }

        return `加班門檻：${parts.join('、')}`
    }

    // 新增班別
    const handleAdd = () => {
        setFormData({
            name: '',
            code: '',
            color_code: '#3B82F6',
            start_time: '',
            end_time: '',
            day_start_time: '06:00',
            break_start_time: '',
            break_duration_minutes: 60,
            late_tolerance_minutes: 0,
            early_leave_tolerance_minutes: 0,
            pre_overtime_threshold_minutes: 0,
            post_overtime_threshold_minutes: 0,
            enable_pre_overtime: false,
            enable_post_overtime: false,
            auto_calculate_overtime: true,
            is_default: false,
            description: ''
        })
        setModalMode('add')
        setSelectedShift(null)
        setShowModal(true)
    }

    // 編輯班別
    const handleEdit = (shift: Shift) => {
        setFormData({ ...shift })
        setModalMode('edit')
        setSelectedShift(shift)
        setShowModal(true)
    }

    // 刪除班別
    const handleDelete = async (shift: Shift) => {
        if (!confirm('確定要刪除這個班別嗎？此操作無法復原。')) {
            return
        }

        try {
            console.log('刪除班別:', shift.id, shift.name)
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/shifts/${shift.id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            console.log('刪除響應狀態:', response.status, response.statusText)

            if (response.ok) {
                const result = await response.json()
                console.log('刪除響應結果:', result)
                alert('班別刪除成功！')
                loadShifts()
            } else {
                let errorMessage = '刪除失敗'
                try {
                    const error = await response.json()
                    errorMessage = error.error || error.message || `HTTP ${response.status}: ${response.statusText}`
                } catch (parseError) {
                    errorMessage = `HTTP ${response.status}: ${response.statusText}`
                }
                throw new Error(errorMessage)
            }
        } catch (error) {
            console.error('刪除班別失敗:', error)

            let errorMessage = '刪除班別失敗：'
            if (error instanceof TypeError && error.message.includes('fetch')) {
                errorMessage += '網路連線錯誤，請檢查後端服務是否正常運行'
            } else if (error instanceof Error) {
                errorMessage += error.message
            } else {
                errorMessage += '未知錯誤'
            }

            alert(errorMessage)
        }
    }

    // 儲存班別
    const handleSave = async (e: React.FormEvent) => {
        e.preventDefault()

        try {
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const isEdit = modalMode === 'edit' && selectedShift?.id
            const url = isEdit
                ? `${apiBaseUrl}/api/shifts/${selectedShift.id}`
                : `${apiBaseUrl}/api/shifts`
            const method = isEdit ? 'PUT' : 'POST'

            console.log('發送請求:', { url, method, formData })

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })

            console.log('響應狀態:', response.status, response.statusText)

            if (response.ok) {
                const result = await response.json()
                console.log('響應結果:', result)
                alert(isEdit ? '班別更新成功！' : '班別新增成功！')
                setShowModal(false)
                loadShifts()
            } else {
                let errorMessage = '操作失敗'
                try {
                    const error = await response.json()
                    errorMessage = error.error || error.message || `HTTP ${response.status}: ${response.statusText}`
                } catch (parseError) {
                    errorMessage = `HTTP ${response.status}: ${response.statusText}`
                }
                throw new Error(errorMessage)
            }
        } catch (error) {
            console.error('班別操作失敗:', error)

            // 更詳細的錯誤處理
            let errorMessage = '班別操作失敗：'
            if (error instanceof TypeError && error.message.includes('fetch')) {
                errorMessage += '網路連線錯誤，請檢查後端服務是否正常運行'
            } else if (error instanceof Error) {
                errorMessage += error.message
            } else {
                errorMessage += '未知錯誤'
            }

            alert(errorMessage)
        }
    }

    // 處理表單輸入
    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            {/* 📦 主容器 - 內容區域容器 */}
            <div className="p-6">
                {/* 🎨 頁面標題 - 統一的標題設計 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        {/* 📍 左側標題區 */}
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">排班管理中心</h1>
                            <div className="flex items-center space-x-2">
                                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                    <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                    <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                                </Link>
                                <p className="text-indigo-100 text-base font-medium">靈活的班別設定，智能的加班計算，高效的排班管理</p>
                            </div>
                        </div>

                        {/* 📍 右側資訊區 */}
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">排班管理</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <CalendarClock className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* 📊 工具欄 - 列表標題和統計 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
                    <div className="flex items-center justify-between">
                        {/* 📍 左側標題和統計 */}
                        <div className="flex items-center space-x-4">
                            <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">班別設定列表</h2>
                            <span className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                                {shifts.length} 個班別
                            </span>
                        </div>

                        {/* 📍 右側操作按鈕 */}
                        <div className="flex items-center space-x-3">
                            {/* 🔄 重新整理按鈕 */}
                            <Button
                                onClick={loadShifts}
                                className="flex items-center space-x-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                                <RefreshCw className="w-4 h-4" />
                                <span className="font-medium">重新整理</span>
                            </Button>

                            {/* 🆕 新增班別按鈕 */}
                            <Button
                                onClick={handleAdd}
                                className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                                <Plus className="w-4 h-4" />
                                <span className="font-medium">新增班別</span>
                            </Button>
                        </div>
                    </div>
                </div>

                {/* 📋 數據列表容器 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                    {/* 🔄 載入狀態 */}
                    {loading ? (
                        <div className="flex items-center justify-center py-16">
                            <div className="text-center">
                                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                                    <div className="relative">
                                        <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                                    </div>
                                </div>
                                <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                                <p className="text-gray-500">正在載入班別資料</p>
                            </div>
                        </div>
                    ) : shifts.length === 0 ? (
                        /* 🚫 空狀態 */
                        <div className="text-center py-16">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
                                <CalendarClock className="w-8 h-8 text-gray-400" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">尚未設定任何班別</h3>
                            <p className="text-gray-500 mb-4">開始建立您的第一個班別設定</p>
                            <Button
                                onClick={handleAdd}
                                className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
                            >
                                新增班別
                            </Button>
                        </div>
                    ) : (
                        <>
                            {/* 💻 桌面版表格 */}
                            <div className="hidden lg:block">
                                {/* 🏷️ 表格標題行 */}
                                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                                    <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <Tag className="w-4 h-4 text-indigo-600" />
                                            <span>班別名稱</span>
                                        </div>
                                        <div className="col-span-1 flex items-center space-x-2">
                                            <Code className="w-4 h-4 text-purple-600" />
                                            <span>代碼</span>
                                        </div>
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <Clock className="w-4 h-4 text-blue-600" />
                                            <span>工作時間</span>
                                        </div>
                                        <div className="col-span-1 flex items-center space-x-2">
                                            <Timer className="w-4 h-4 text-green-600" />
                                            <span>工時</span>
                                        </div>
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <Coffee className="w-4 h-4 text-orange-600" />
                                            <span>休息時間</span>
                                        </div>
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <Zap className="w-4 h-4 text-yellow-600" />
                                            <span>加班設定</span>
                                        </div>
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <Edit className="w-4 h-4 text-gray-600" />
                                            <span>操作</span>
                                        </div>
                                    </div>
                                </div>

                                {/* 📊 表格內容區 */}
                                <div className="divide-y divide-gray-100">
                                    {shifts.map((shift) => {
                                        const workHours = calculateWorkHours(shift.start_time, shift.end_time, shift.break_duration_minutes)
                                        const overtimeText = getOvertimeText(shift)

                                        return (
                                            <div key={shift.id} className="px-6 py-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200">
                                                <div className="grid grid-cols-12 gap-4 items-center text-sm">
                                                    <div className="col-span-2 flex items-center space-x-3">
                                                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: shift.color_code }}></div>
                                                        <div className="flex items-center space-x-2">
                                                            <span className="font-medium text-gray-900">{shift.name}</span>
                                                            {shift.is_default && (
                                                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                    <CalendarClock className="w-3 h-3 mr-1" />
                                                                    預設
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="col-span-1">
                                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            {shift.code}
                                                        </span>
                                                    </div>
                                                    <div className="col-span-2 text-gray-600">
                                                        {shift.start_time} - {shift.end_time}
                                                    </div>
                                                    <div className="col-span-1 text-gray-600">
                                                        {workHours} 小時
                                                    </div>
                                                    <div className="col-span-2 text-gray-600">
                                                        {shift.break_start_time ? `${shift.break_start_time} (${shift.break_duration_minutes}分鐘)` : '無設定'}
                                                    </div>
                                                    <div className="col-span-2 text-gray-600">
                                                        {overtimeText}
                                                    </div>
                                                    <div className="col-span-2 flex items-center space-x-2">
                                                        <Button
                                                            onClick={() => handleEdit(shift)}
                                                            size="sm"
                                                            className="flex items-center space-x-1 bg-blue-500 text-white hover:bg-blue-600 transition-all duration-200 shadow-sm hover:shadow-md"
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                            <span>編輯</span>
                                                        </Button>
                                                        <Button
                                                            onClick={() => handleDelete(shift)}
                                                            size="sm"
                                                            className="flex items-center space-x-1 bg-red-500 text-white hover:bg-red-600 transition-all duration-200 shadow-sm hover:shadow-md"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                            <span>刪除</span>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>

                            {/* 📱 手機版卡片 */}
                            <div className="lg:hidden space-y-4 p-4">
                                {shifts.map((shift) => {
                                    const workHours = calculateWorkHours(shift.start_time, shift.end_time, shift.break_duration_minutes)
                                    const overtimeText = getOvertimeText(shift)

                                    return (
                                        <div key={shift.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                                            <div className="flex items-center justify-between mb-3">
                                                <div className="flex items-center space-x-3">
                                                    <div className="w-4 h-4 rounded-full" style={{ backgroundColor: shift.color_code }}></div>
                                                    <div className="flex items-center space-x-2">
                                                        <h3 className="font-semibold text-gray-900">{shift.name}</h3>
                                                        {shift.is_default && (
                                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                <CalendarClock className="w-3 h-3 mr-1" />
                                                                預設
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                                <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">{shift.code}</span>
                                            </div>

                                            <div className="space-y-2 text-sm text-gray-600 mb-4">
                                                <div className="flex items-center space-x-2">
                                                    <Clock className="w-4 h-4" />
                                                    <span>{shift.start_time} - {shift.end_time} ({workHours}小時)</span>
                                                </div>
                                                {shift.break_start_time && (
                                                    <div className="flex items-center space-x-2">
                                                        <Coffee className="w-4 h-4" />
                                                        <span>休息：{shift.break_start_time} ({shift.break_duration_minutes}分鐘)</span>
                                                    </div>
                                                )}
                                                <div className="flex items-center space-x-2">
                                                    <Zap className="w-4 h-4" />
                                                    <span>{overtimeText}</span>
                                                </div>
                                            </div>

                                            <div className="flex items-center justify-between">
                                                <span className="text-xs text-gray-500">{shift.description || '無描述'}</span>
                                                <div className="flex space-x-2">
                                                    <Button
                                                        onClick={() => handleEdit(shift)}
                                                        size="sm"
                                                        className="bg-blue-500 text-white hover:bg-blue-600 transition-all duration-200 shadow-sm hover:shadow-md"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        onClick={() => handleDelete(shift)}
                                                        size="sm"
                                                        className="bg-red-500 text-white hover:bg-red-600 transition-all duration-200 shadow-sm hover:shadow-md"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        </>
                    )}
                </div>
            </div>

            {/* 🔧 新增/編輯班別模態框 */}
            {showModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] flex flex-col">
                        {/* 🎨 固定標題區域 */}
                        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-6 rounded-t-2xl flex-shrink-0">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                        <CalendarClock className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-white">
                                            {modalMode === 'add' ? '新增班別' : '編輯班別'}
                                        </h3>
                                        <p className="text-indigo-100 text-sm">設定班別的基本資訊和工作時間</p>
                                    </div>
                                </div>

                                {/* 🎛️ 右上角操作按鈕區 */}
                                <div className="flex items-center space-x-3">
                                    {/* 📍 取消按鈕 */}
                                    <Button
                                        type="button"
                                        onClick={() => setShowModal(false)}
                                        variant="ghost"
                                        size="sm"
                                        className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200"
                                    >
                                        <X className="w-4 h-4 mr-2" />
                                        <span className="font-medium">取消</span>
                                    </Button>

                                    {/* 📍 儲存按鈕 */}
                                    <Button
                                        type="submit"
                                        form="shift-form"
                                        size="sm"
                                        className="bg-white/90 text-indigo-600 hover:bg-white hover:text-indigo-700 transition-all duration-200 shadow-lg"
                                    >
                                        <Save className="w-4 h-4 mr-2" />
                                        <span className="font-medium">儲存班別</span>
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* 📝 可滾動的表單內容區域 */}
                        <div className="flex-1 overflow-y-auto">
                            <form id="shift-form" onSubmit={handleSave} className="p-6">
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* 左側：基本資訊 */}
                                    <div className="space-y-4">
                                        <h4 className="font-semibold text-gray-700 border-b border-gray-200 pb-2 flex items-center space-x-2">
                                            <Info className="w-4 h-4 text-indigo-600" />
                                            <span>基本資訊</span>
                                        </h4>

                                        {/* 班別名稱 */}
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Tag className="w-4 h-4 text-blue-600" />
                                                <label className="block text-sm font-semibold text-gray-700">班別名稱 <span className="text-red-500">*</span></label>
                                            </div>
                                            <input
                                                type="text"
                                                value={formData.name}
                                                onChange={(e) => handleInputChange('name', e.target.value)}
                                                required
                                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                placeholder="例如：標準日班"
                                            />
                                        </div>

                                        {/* 班別代碼 */}
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Code className="w-4 h-4 text-purple-600" />
                                                <label className="block text-sm font-semibold text-gray-700">班別代碼 <span className="text-red-500">*</span></label>
                                            </div>
                                            <input
                                                type="text"
                                                value={formData.code}
                                                onChange={(e) => handleInputChange('code', e.target.value)}
                                                required
                                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                placeholder="例如：STANDARD_DAY"
                                            />
                                        </div>

                                        {/* 顏色代碼 */}
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Palette className="w-4 h-4 text-pink-600" />
                                                <label className="block text-sm font-semibold text-gray-700">顏色代碼</label>
                                            </div>
                                            <input
                                                type="color"
                                                value={formData.color_code}
                                                onChange={(e) => handleInputChange('color_code', e.target.value)}
                                                className="w-full h-12 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            />
                                        </div>

                                        {/* 描述 */}
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <FileText className="w-4 h-4 text-gray-600" />
                                                <label className="block text-sm font-semibold text-gray-700">描述</label>
                                            </div>
                                            <textarea
                                                value={formData.description}
                                                onChange={(e) => handleInputChange('description', e.target.value)}
                                                rows={3}
                                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                placeholder="班別描述..."
                                            />
                                        </div>

                                        {/* 加班選項設定與預設班表 */}
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            {/* 左側：加班選項設定 */}
                                            <div className="space-y-3">
                                                <h5 className="font-medium text-gray-700 flex items-center space-x-2">
                                                    <Zap className="w-4 h-4 text-yellow-600" />
                                                    <span>加班選項設定</span>
                                                </h5>
                                                <div className="space-y-2">
                                                    <label className="flex items-center space-x-3">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.enable_pre_overtime}
                                                            onChange={(e) => handleInputChange('enable_pre_overtime', e.target.checked)}
                                                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                                        />
                                                        <span className="text-sm font-medium text-gray-700">啟用上班前加班</span>
                                                    </label>
                                                    <label className="flex items-center space-x-3">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.enable_post_overtime}
                                                            onChange={(e) => handleInputChange('enable_post_overtime', e.target.checked)}
                                                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                                        />
                                                        <span className="text-sm font-medium text-gray-700">啟用下班後加班</span>
                                                    </label>
                                                    <label className="flex items-center space-x-3">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.auto_calculate_overtime}
                                                            onChange={(e) => handleInputChange('auto_calculate_overtime', e.target.checked)}
                                                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                                        />
                                                        <span className="text-sm font-medium text-gray-700">自動計算加班</span>
                                                    </label>
                                                </div>
                                            </div>

                                            {/* 右側：預設班表設定 */}
                                            <div className="space-y-3">
                                                <h5 className="font-medium text-gray-700 flex items-center space-x-2">
                                                    <CalendarClock className="w-4 h-4 text-green-600" />
                                                    <span>預設班表</span>
                                                </h5>
                                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                                    <label className="flex items-start space-x-3">
                                                        <input
                                                            type="checkbox"
                                                            checked={formData.is_default}
                                                            onChange={(e) => handleInputChange('is_default', e.target.checked)}
                                                            className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mt-0.5"
                                                        />
                                                        <div>
                                                            <span className="text-sm font-medium text-gray-700">設為預設班表</span>
                                                            <p className="text-xs text-gray-500 mt-1">
                                                                新進員工將自動分配到此班表。<br />
                                                                系統只能有一個預設班表。
                                                            </p>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 右側：時間設定 */}
                                    <div className="space-y-4">
                                        <h4 className="font-semibold text-gray-700 border-b border-gray-200 pb-2 flex items-center space-x-2">
                                            <Clock className="w-4 h-4 text-indigo-600" />
                                            <span>時間設定</span>
                                        </h4>

                                        {/* 工作時間 */}
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <div className="flex items-center space-x-2">
                                                    <Sunrise className="w-4 h-4 text-orange-600" />
                                                    <label className="block text-sm font-semibold text-gray-700">上班時間 <span className="text-red-500">*</span></label>
                                                </div>
                                                <input
                                                    type="time"
                                                    value={formData.start_time}
                                                    onChange={(e) => handleInputChange('start_time', e.target.value)}
                                                    required
                                                    className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex items-center space-x-2">
                                                    <Sunset className="w-4 h-4 text-orange-600" />
                                                    <label className="block text-sm font-semibold text-gray-700">下班時間 <span className="text-red-500">*</span></label>
                                                </div>
                                                <input
                                                    type="time"
                                                    value={formData.end_time}
                                                    onChange={(e) => handleInputChange('end_time', e.target.value)}
                                                    required
                                                    className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                />
                                            </div>
                                        </div>

                                        {/* 當天起算時間 */}
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Calendar className="w-4 h-4 text-green-600" />
                                                <label className="block text-sm font-semibold text-gray-700">當天起算時間 <span className="text-red-500">*</span></label>
                                            </div>
                                            <input
                                                type="time"
                                                value={formData.day_start_time}
                                                onChange={(e) => handleInputChange('day_start_time', e.target.value)}
                                                required
                                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                            />
                                            <p className="text-xs text-gray-500">此時間之前的打卡將歸屬前一天</p>
                                        </div>

                                        {/* 休息時間 */}
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <div className="flex items-center space-x-2">
                                                    <Coffee className="w-4 h-4 text-amber-600" />
                                                    <label className="block text-sm font-semibold text-gray-700">休息開始時間</label>
                                                </div>
                                                <input
                                                    type="time"
                                                    value={formData.break_start_time}
                                                    onChange={(e) => handleInputChange('break_start_time', e.target.value)}
                                                    className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex items-center space-x-2">
                                                    <Timer className="w-4 h-4 text-amber-600" />
                                                    <label className="block text-sm font-semibold text-gray-700">休息時長（分鐘）</label>
                                                </div>
                                                <input
                                                    type="number"
                                                    value={formData.break_duration_minutes}
                                                    onChange={(e) => handleInputChange('break_duration_minutes', parseInt(e.target.value) || 0)}
                                                    min="0"
                                                    max="480"
                                                    className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                />
                                            </div>
                                        </div>

                                        {/* 容許時間設定 */}
                                        <div className="space-y-3">
                                            <h5 className="font-medium text-gray-700 flex items-center space-x-2">
                                                <Clock className="w-4 h-4 text-blue-600" />
                                                <span>容許時間設定</span>
                                            </h5>
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <div className="flex items-center space-x-2">
                                                        <Sunrise className="w-4 h-4 text-green-600" />
                                                        <label className="block text-sm font-semibold text-gray-700">上班容許遲到（分鐘）</label>
                                                    </div>
                                                    <input
                                                        type="number"
                                                        value={formData.late_tolerance_minutes}
                                                        onChange={(e) => handleInputChange('late_tolerance_minutes', parseInt(e.target.value) || 0)}
                                                        min="0"
                                                        max="60"
                                                        className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                        placeholder="0"
                                                    />
                                                    <p className="text-xs text-gray-500">在此時間內不算遲到</p>
                                                </div>
                                                <div className="space-y-2">
                                                    <div className="flex items-center space-x-2">
                                                        <Sunset className="w-4 h-4 text-red-600" />
                                                        <label className="block text-sm font-semibold text-gray-700">下班容許早退（分鐘）</label>
                                                    </div>
                                                    <input
                                                        type="number"
                                                        value={formData.early_leave_tolerance_minutes}
                                                        onChange={(e) => handleInputChange('early_leave_tolerance_minutes', parseInt(e.target.value) || 0)}
                                                        min="0"
                                                        max="60"
                                                        className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                        placeholder="0"
                                                    />
                                                    <p className="text-xs text-gray-500">在此時間內不算早退</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* 加班設定 */}
                                        <div className="space-y-3">
                                            <h5 className="font-medium text-gray-700 flex items-center space-x-2">
                                                <Zap className="w-4 h-4 text-yellow-600" />
                                                <span>加班設定</span>
                                            </h5>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <label className="block text-sm font-semibold text-gray-700">上班前加班門檻（分鐘）</label>
                                                    <input
                                                        type="number"
                                                        value={formData.pre_overtime_threshold_minutes}
                                                        onChange={(e) => handleInputChange('pre_overtime_threshold_minutes', parseInt(e.target.value) || 0)}
                                                        min="0"
                                                        max="240"
                                                        className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <label className="block text-sm font-semibold text-gray-700">下班後加班門檻（分鐘）</label>
                                                    <input
                                                        type="number"
                                                        value={formData.post_overtime_threshold_minutes}
                                                        onChange={(e) => handleInputChange('post_overtime_threshold_minutes', parseInt(e.target.value) || 0)}
                                                        min="0"
                                                        max="240"
                                                        className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </form>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
} 