/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/m/page";
exports.ids = ["app/m/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fm%2Fpage&page=%2Fm%2Fpage&appPaths=%2Fm%2Fpage&pagePath=private-next-app-dir%2Fm%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fm%2Fpage&page=%2Fm%2Fpage&appPaths=%2Fm%2Fpage&pagePath=private-next-app-dir%2Fm%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'm',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/m/page.tsx */ \"(rsc)/./src/app/m/page.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/m/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/m/page\",\n        pathname: \"/m\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fm%2Fpage&page=%2Fm%2Fpage&appPaths=%2Fm%2Fpage&pagePath=private-next-app-dir%2Fm%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstallPrompt.tsx */ \"(ssr)/./src/components/PWAInstallPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ServiceWorkerRegistration.tsx */ \"(ssr)/./src/components/ServiceWorkerRegistration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FPWAInstallPrompt.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcomponents%2FServiceWorkerRegistration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fm%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fm%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/m/page.tsx */ \"(ssr)/./src/app/m/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2V2aW4lMkYyMDI0bmV3ZGV2JTJGYXR0ZW5kX25leHQlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRm0lMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQW9HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvP2RlZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMva2V2aW4vMjAyNG5ld2Rldi9hdHRlbmRfbmV4dC9mcm9udGVuZC9zcmMvYXBwL20vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp%2Fm%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/m/page.tsx":
/*!****************************!*\
  !*** ./src/app/m/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,FileText,History,LogOut,Plus,Settings,Timer,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction MobilePage() {\n    const { user, logout, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"獲取位置中...\");\n    const [todayAttendance, setTodayAttendance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentRecords, setRecentRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [overtimeRequests, setOvertimeRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [leaveRequests, setLeaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWorkReportModal, setShowWorkReportModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [workReportForm, setWorkReportForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        date: \"\",\n        time: \"\",\n        category: \"\",\n        content: \"\",\n        photos: []\n    });\n    const [userAvatar, setUserAvatar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // 🖼️ 載入用戶頭像（從後台API）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUserAvatar = async ()=>{\n            if (!user?.employee_id) return;\n            try {\n                // 從後台API獲取用戶資料\n                const apiUrl = `${getApiBaseUrl()}/api/profile/${user.employee_id}`;\n                console.log(\"\\uD83D\\uDD0D 開始載入頭像，API URL:\", apiUrl);\n                const response = await fetch(apiUrl);\n                console.log(\"\\uD83D\\uDCE1 API回應狀態:\", response.status, response.statusText);\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log(\"\\uD83D\\uDCE6 API回應內容:\", result);\n                    if (result.success && result.profile.avatar && result.profile.avatar.trim()) {\n                        const avatarUrl = `${getApiBaseUrl()}${result.profile.avatar}`;\n                        console.log(\"\\uD83D\\uDDBC️ 構建的頭像URL:\", avatarUrl);\n                        setUserAvatar(avatarUrl);\n                        // 同時更新localStorage作為快取\n                        localStorage.setItem(\"userAvatar\", avatarUrl);\n                        console.log(\"✅ 頭像設定成功，狀態已更新\");\n                        // 測試圖片是否可以載入\n                        const testImg = new Image();\n                        testImg.onload = ()=>{\n                            console.log(\"✅ 頭像圖片載入測試成功\");\n                        };\n                        testImg.onerror = (error)=>{\n                            console.error(\"❌ 頭像圖片載入測試失敗:\", error);\n                        };\n                        testImg.src = avatarUrl;\n                    } else {\n                        console.log(\"\\uD83D\\uDCF7 用戶尚未設定頭像，API回應:\", result);\n                        // 清除localStorage中的舊頭像\n                        localStorage.removeItem(\"userAvatar\");\n                        setUserAvatar(null);\n                    }\n                } else {\n                    console.warn(\"⚠️ 無法載入用戶資料，狀態碼:\", response.status);\n                    // 嘗試從localStorage載入快取\n                    const cachedAvatar = localStorage.getItem(\"userAvatar\");\n                    if (cachedAvatar) {\n                        setUserAvatar(cachedAvatar);\n                        console.log(\"\\uD83D\\uDCE6 使用快取頭像:\", cachedAvatar);\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ 載入頭像失敗:\", error);\n                // 嘗試從localStorage載入快取\n                const cachedAvatar = localStorage.getItem(\"userAvatar\");\n                if (cachedAvatar) {\n                    setUserAvatar(cachedAvatar);\n                    console.log(\"\\uD83D\\uDCE6 載入失敗，使用快取頭像:\", cachedAvatar);\n                }\n            }\n        };\n        loadUserAvatar();\n        // 監聽storage事件，當其他頁面更新頭像時同步更新\n        const handleStorageChange = (e)=>{\n            if (e.key === \"userAvatar\") {\n                setUserAvatar(e.newValue);\n                console.log(\"\\uD83D\\uDD04 頭像已同步更新\");\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>window.removeEventListener(\"storage\", handleStorageChange);\n    }, [\n        user?.employee_id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 改進的 GPS 定位功能\n        const initializeLocation = async ()=>{\n            // 檢查是否為 HTTPS 或 localhost\n            const isSecureContext = window.location.protocol === \"https:\" || window.location.hostname === \"localhost\" || window.location.hostname === \"127.0.0.1\";\n            if (!isSecureContext) {\n                setLocation(\"台北辦公室 (需要HTTPS定位)\");\n                return;\n            }\n            if (!navigator.geolocation) {\n                setLocation(\"台北辦公室 (瀏覽器不支援定位)\");\n                return;\n            }\n            try {\n                // 先檢查權限狀態\n                if (\"permissions\" in navigator) {\n                    const permission = await navigator.permissions.query({\n                        name: \"geolocation\"\n                    });\n                    if (permission.state === \"denied\") {\n                        setLocation(\"台北辦公室 (定位權限被拒絕)\");\n                        return;\n                    }\n                }\n                navigator.geolocation.getCurrentPosition((position)=>{\n                    const lat = position.coords.latitude.toFixed(4);\n                    const lng = position.coords.longitude.toFixed(4);\n                    setLocation(`${lat}, ${lng}`);\n                    console.log(\"✅ GPS定位成功:\", {\n                        lat,\n                        lng\n                    });\n                }, (error)=>{\n                    console.warn(\"GPS定位失敗，使用預設位置:\", error.message);\n                    switch(error.code){\n                        case error.PERMISSION_DENIED:\n                            setLocation(\"台北辦公室 (用戶拒絕定位權限)\");\n                            break;\n                        case error.POSITION_UNAVAILABLE:\n                            setLocation(\"台北辦公室 (位置資訊無法取得)\");\n                            break;\n                        case error.TIMEOUT:\n                            setLocation(\"台北辦公室 (定位請求超時)\");\n                            break;\n                        default:\n                            setLocation(\"台北辦公室 (定位服務不可用)\");\n                            break;\n                    }\n                }, {\n                    enableHighAccuracy: false,\n                    timeout: 5000,\n                    maximumAge: 300000 // 5分鐘內的快取位置可接受\n                });\n            } catch (error) {\n                console.warn(\"GPS初始化失敗:\", error);\n                setLocation(\"台北辦公室 (定位初始化失敗)\");\n            }\n        };\n        initializeLocation();\n    }, []);\n    // 🌐 獲取API基礎URL\n    const getApiBaseUrl = ()=>{\n        if (false) {}\n        return \"http://localhost:7072\";\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            console.log(\"\\uD83D\\uDD0D 手機版主頁 loadData 檢查:\", {\n                authLoading,\n                user: !!user,\n                userDetails: user\n            });\n            if (authLoading) {\n                console.log(\"⏳ AuthContext 還在載入中，等待...\");\n                return;\n            }\n            if (!user) {\n                console.log(\"❌ 沒有用戶資料，重定向到登入頁面\");\n                router.push(\"/m/login\");\n                return;\n            }\n            console.log(\"✅ 用戶已認證，開始載入數據:\", user);\n            // 🔝 滾動到頁面頂部\n            window.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n            const apiBaseUrl = getApiBaseUrl();\n            try {\n                // 載入今日考勤記錄\n                try {\n                    const today = new Date().toISOString().split(\"T\")[0];\n                    const url = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`;\n                    console.log(\"\\uD83D\\uDCC5 載入今日考勤 URL:\", url);\n                    const todayResponse = await fetch(url);\n                    if (todayResponse.ok) {\n                        const todayData = await todayResponse.json();\n                        console.log(\"載入今日考勤記錄:\", todayData);\n                        setTodayAttendance(todayData.data || null);\n                    } else {\n                        console.error(\"載入今日考勤失敗，API返回錯誤\");\n                        setTodayAttendance(null);\n                    }\n                } catch (error) {\n                    console.error(\"載入今日考勤失敗:\", error);\n                    setTodayAttendance(null);\n                }\n                // 載入最近考勤記錄\n                try {\n                    const endDate = new Date().toISOString().split(\"T\")[0];\n                    const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split(\"T\")[0];\n                    const url = `${apiBaseUrl}/api/attendance/records?employee_id=${user.employee_id}&start_date=${startDate}&end_date=${endDate}&limit=5`;\n                    console.log(\"\\uD83D\\uDCCB 載入最近考勤記錄 URL:\", url);\n                    const recordsResponse = await fetch(url);\n                    if (recordsResponse.ok) {\n                        const recordsData = await recordsResponse.json();\n                        console.log(\"載入最近考勤記錄:\", recordsData);\n                        setRecentRecords(recordsData.records || []);\n                    } else {\n                        console.error(\"載入最近考勤記錄失敗，API返回錯誤\");\n                        setRecentRecords([]);\n                    }\n                } catch (error) {\n                    console.error(\"載入最近考勤記錄失敗:\", error);\n                    setRecentRecords([]);\n                }\n                // 載入真實的加班申請記錄\n                try {\n                    const url = `${apiBaseUrl}/api/overtime/requests?employee_id=${user.id}`;\n                    console.log(\"⏰ 載入加班申請記錄 URL:\", url);\n                    const overtimeResponse = await fetch(url);\n                    if (overtimeResponse.ok) {\n                        const overtimeData = await overtimeResponse.json();\n                        console.log(\"載入加班申請記錄:\", overtimeData);\n                        setOvertimeRequests(overtimeData.records || []);\n                    } else {\n                        console.error(\"載入加班申請記錄失敗，API返回錯誤\");\n                        setOvertimeRequests([]);\n                    }\n                } catch (error) {\n                    console.error(\"載入加班申請失敗:\", error);\n                    setOvertimeRequests([]);\n                }\n                // 載入請假記錄\n                try {\n                    const url = `${apiBaseUrl}/api/leave-requests?employee_id=${user.id}&limit=5`;\n                    console.log(\"\\uD83C\\uDFD6️ 載入請假記錄 URL:\", url);\n                    const leaveResponse = await fetch(url);\n                    if (leaveResponse.ok) {\n                        const leaveData = await leaveResponse.json();\n                        console.log(\"載入請假記錄:\", leaveData);\n                        setLeaveRequests(leaveData.records || []);\n                    } else {\n                        console.error(\"載入請假記錄失敗，API返回錯誤\");\n                        setLeaveRequests([]);\n                    }\n                } catch (error) {\n                    console.error(\"載入請假記錄失敗:\", error);\n                    setLeaveRequests([]);\n                }\n            } catch (error) {\n                console.error(\"載入數據失敗:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        user,\n        authLoading,\n        router\n    ]);\n    const handleClockIn = async ()=>{\n        if (!user) return;\n        try {\n            console.log(\"執行上班打卡\");\n            const apiBaseUrl = getApiBaseUrl();\n            const url = `${apiBaseUrl}/api/attendance/clock-in`;\n            console.log(\"⏰ 上班打卡 URL:\", url);\n            const response = await fetch(url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    employee_id: user.employee_id,\n                    location: location\n                })\n            });\n            const result = await response.json();\n            console.log(\"上班打卡結果:\", result);\n            if (result.success) {\n                alert(\"上班打卡成功！\");\n                // 重新載入今日考勤記錄\n                const todayUrl = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`;\n                const todayResponse = await fetch(todayUrl);\n                if (todayResponse.ok) {\n                    const todayData = await todayResponse.json();\n                    setTodayAttendance(todayData.data || null);\n                }\n            } else {\n                alert(\"打卡失敗：\" + (result.error || result.message || \"未知錯誤\"));\n            }\n        } catch (error) {\n            console.error(\"打卡失敗:\", error);\n            alert(\"打卡失敗，請檢查網路連線\");\n        }\n    };\n    const handleClockOut = async ()=>{\n        if (!user) return;\n        try {\n            console.log(\"執行下班打卡\");\n            const apiBaseUrl = getApiBaseUrl();\n            const url = `${apiBaseUrl}/api/attendance/clock-out`;\n            console.log(\"⏰ 下班打卡 URL:\", url);\n            const response = await fetch(url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    employee_id: user.employee_id,\n                    location: location\n                })\n            });\n            const result = await response.json();\n            console.log(\"下班打卡結果:\", result);\n            if (result.success) {\n                alert(\"下班打卡成功！\");\n                // 重新載入今日考勤記錄\n                const todayUrl = `${apiBaseUrl}/api/attendance/today/${user.employee_id}`;\n                const todayResponse = await fetch(todayUrl);\n                if (todayResponse.ok) {\n                    const todayData = await todayResponse.json();\n                    setTodayAttendance(todayData.data || null);\n                }\n            } else {\n                alert(\"打卡失敗：\" + (result.error || result.message || \"未知錯誤\"));\n            }\n        } catch (error) {\n            console.error(\"打卡失敗:\", error);\n            alert(\"打卡失敗，請檢查網路連線\");\n        }\n    };\n    // 🗑️ 取消請假申請函數\n    const handleCancelLeaveRequest = async (requestId)=>{\n        try {\n            if (!user) {\n                alert(\"用戶資訊不存在，請重新登入\");\n                return;\n            }\n            const apiBaseUrl = getApiBaseUrl();\n            // 顯示確認對話框\n            const confirmed = window.confirm(\"確定要取消這個請假申請嗎？\");\n            if (!confirmed) return;\n            const response = await fetch(`${apiBaseUrl}/api/leave-requests/${requestId}`, {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            console.log(\"取消請假申請結果:\", result);\n            if (result.success) {\n                alert(\"請假申請已成功取消！\");\n                // 重新載入請假記錄\n                const leaveUrl = `${apiBaseUrl}/api/leave-requests/employee/${user.employee_id}`;\n                const leaveResponse = await fetch(leaveUrl);\n                if (leaveResponse.ok) {\n                    const leaveData = await leaveResponse.json();\n                    setLeaveRequests(leaveData.data || []);\n                }\n            } else {\n                alert(\"取消失敗：\" + (result.error || result.message || \"未知錯誤\"));\n            }\n        } catch (error) {\n            console.error(\"取消請假申請失敗:\", error);\n            alert(\"取消失敗，請檢查網路連線\");\n        }\n    };\n    // 🗑️ 取消加班申請函數\n    const handleCancelOvertimeRequest = async (requestId)=>{\n        try {\n            if (!user) {\n                alert(\"用戶資訊不存在，請重新登入\");\n                return;\n            }\n            const apiBaseUrl = getApiBaseUrl();\n            // 顯示確認對話框\n            const confirmed = window.confirm(\"確定要取消這個加班申請嗎？\");\n            if (!confirmed) return;\n            const response = await fetch(`${apiBaseUrl}/api/overtime-requests/${requestId}`, {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            console.log(\"取消加班申請結果:\", result);\n            if (result.success) {\n                alert(\"加班申請已成功取消！\");\n                // 重新載入加班記錄\n                const overtimeUrl = `${apiBaseUrl}/api/overtime-requests/employee/${user.employee_id}`;\n                const overtimeResponse = await fetch(overtimeUrl);\n                if (overtimeResponse.ok) {\n                    const overtimeData = await overtimeResponse.json();\n                    setOvertimeRequests(overtimeData.data || []);\n                }\n            } else {\n                alert(\"取消失敗：\" + (result.error || result.message || \"未知錯誤\"));\n            }\n        } catch (error) {\n            console.error(\"取消加班申請失敗:\", error);\n            alert(\"取消失敗，請檢查網路連線\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"normal\":\n                return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n            case \"late\":\n                return \"bg-amber-50 text-amber-700 border-amber-200\";\n            case \"early_leave\":\n                return \"bg-orange-50 text-orange-700 border-orange-200\";\n            case \"absent\":\n                return \"bg-red-50 text-red-700 border-red-200\";\n            case \"working\":\n                return \"bg-blue-50 text-blue-700 border-blue-200\";\n            default:\n                return \"bg-gray-50 text-gray-700 border-gray-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"normal\":\n                return \"正常\";\n            case \"late\":\n                return \"遲到\";\n            case \"early_leave\":\n                return \"早退\";\n            case \"absent\":\n                return \"缺勤\";\n            case \"working\":\n                return \"工作中\";\n            default:\n                return \"未知\";\n        }\n    };\n    if (authLoading || loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 border-4 border-blue-100 border-t-blue-600 rounded-full animate-spin mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-300 rounded-full animate-pulse mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                        children: \"載入中\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在初始化系統...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                lineNumber: 551,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n            lineNumber: 550,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: logout,\n                className: \"fixed top-4 right-4 z-50 w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 border-white/20\",\n                title: \"登出\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                lineNumber: 566,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-2 py-2 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-indigo-600 via-blue-600 to-purple-700 rounded-2xl p-3 text-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold mb-1\",\n                                        children: currentTime.toLocaleTimeString(\"zh-TW\", {\n                                            hour: \"2-digit\",\n                                            minute: \"2-digit\",\n                                            second: \"2-digit\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: currentTime.toLocaleDateString(\"zh-TW\", {\n                                            year: \"numeric\",\n                                            month: \"long\",\n                                            day: \"numeric\",\n                                            weekday: \"long\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center overflow-hidden\",\n                                        children: userAvatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: userAvatar,\n                                            alt: \"用戶頭像\",\n                                            className: \"w-full h-full object-cover\",\n                                            onLoad: ()=>{\n                                                console.log(\"✅ 頭像圖片成功顯示:\", userAvatar);\n                                            },\n                                            onError: (e)=>{\n                                                console.error(\"❌ 頭像圖片載入失敗:\", userAvatar);\n                                                console.error(\"錯誤詳情:\", e);\n                                                // 載入失敗時顯示預設圖標\n                                                setUserAvatar(null);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-10 h-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80 text-base\",\n                                                children: user.employee_id\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: [\n                                                    \"頭像: \",\n                                                    userAvatar ? \"已載入\" : \"未設定\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center space-x-6 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleClockIn,\n                                            disabled: !!todayAttendance?.check_in,\n                                            className: `relative w-20 h-20 rounded-full transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 ${todayAttendance?.check_in ? \"bg-gradient-to-br from-gray-500 to-gray-700 border-gray-400 cursor-not-allowed opacity-50\" : \"bg-gradient-to-br from-green-500 via-emerald-600 to-teal-700 hover:from-green-600 hover:via-emerald-700 hover:to-teal-800 border-green-400 animate-pulse\"}`,\n                                            style: !todayAttendance?.check_in ? {\n                                                boxShadow: \"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.1)\"\n                                            } : {},\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-white block mb-1\",\n                                                        children: \"上班\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base text-white/90 font-medium\",\n                                                        children: todayAttendance?.check_in ? \"已打卡\" : \"尚未打卡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleClockOut,\n                                            disabled: !todayAttendance?.check_in || !!todayAttendance?.check_out,\n                                            className: `relative w-20 h-20 rounded-full transition-all duration-300 transform hover:scale-110 active:scale-95 border-2 ${!todayAttendance?.check_in || todayAttendance?.check_out ? \"bg-gradient-to-br from-gray-500 to-gray-700 border-gray-400 cursor-not-allowed opacity-50\" : \"bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-800 border-blue-400 animate-pulse\"}`,\n                                            style: todayAttendance?.check_in && !todayAttendance?.check_out ? {\n                                                boxShadow: \"0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.1)\"\n                                            } : {},\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-white block mb-1\",\n                                                        children: \"下班\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base text-white/90 font-medium\",\n                                                        children: todayAttendance?.check_out ? \"已打卡\" : \"尚未打卡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-3 border border-gray-100 shadow-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/m/leave-apply\"),\n                                    className: \"group relative p-3 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-white mb-1\",\n                                                    children: \"請假申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base text-blue-100 font-medium\",\n                                                    children: \"提交請假申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/m/overtime-apply\"),\n                                    className: \"group relative p-3 bg-gradient-to-br from-orange-500 via-orange-600 to-red-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-white mb-1\",\n                                                    children: \"加班申請\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base text-orange-100 font-medium\",\n                                                    children: \"申請加班時數\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/m/work-reports\"),\n                                    className: \"group relative p-3 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-white mb-1\",\n                                                    children: \"工作回報\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base text-indigo-100 font-medium\",\n                                                    children: \"記錄工作進度\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/m/settings\"),\n                                    className: \"group relative p-3 bg-gradient-to-br from-green-500 via-emerald-600 to-teal-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-4 h-4 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 left-1 w-3 h-3 bg-white/10 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-2 shadow-lg group-hover:shadow-xl transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-white mb-1\",\n                                                    children: \"員工設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base text-green-100 font-medium\",\n                                                    children: \"個人資料設定\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-3 border border-gray-100 shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-3 h-3 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"最近三天記錄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 760,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: recentRecords.slice(0, 3).map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group p-3 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold text-gray-900 text-xl\",\n                                                    children: record.date\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 rounded-lg p-2 border border-green-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 bg-green-500 rounded-md flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-2 h-2 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                            lineNumber: 781,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-green-700\",\n                                                                        children: \"上班打卡\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-green-800 ml-6\",\n                                                                children: record.check_in || \"未打卡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 rounded-lg p-2 border border-blue-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 bg-blue-500 rounded-md flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"w-2 h-2 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 49\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-blue-700\",\n                                                                        children: \"下班打卡\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-blue-800 ml-6\",\n                                                                children: record.check_out || \"未打卡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, record.id, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 29\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-3 border border-gray-100 shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"最近請假記錄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: leaveRequests.slice(0, 5).length > 0 ? leaveRequests.slice(0, 5).map((request, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 hover:border-blue-200 hover:shadow-md transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-bold text-blue-900 text-xl\",\n                                                                        children: request.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg text-blue-600\",\n                                                                        children: \"請假申請\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    request.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleCancelLeaveRequest(request.id),\n                                                                className: \"p-1 rounded-full bg-red-100 hover:bg-red-200 transition-colors duration-200 group\",\n                                                                title: \"取消申請\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-red-600 group-hover:text-red-700\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-lg font-bold bg-amber-100 text-amber-800 border border-amber-300 animate-pulse\",\n                                                                children: \"\\uD83D\\uDD50 待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 45\n                                                    }, this) : request.status === \"approved\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300\",\n                                                        children: \"✅ 已核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 45\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-lg font-bold bg-red-100 text-red-800 border border-red-300\",\n                                                        children: \"❌ 已拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/60 rounded-lg p-2 border border-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-blue-700\",\n                                                                        children: \"請假期間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-blue-900\",\n                                                                children: [\n                                                                    request.start_date,\n                                                                    \" 至 \",\n                                                                    request.end_date\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg text-blue-600 mt-1\",\n                                                                children: (()=>{\n                                                                    const start = new Date(request.start_date);\n                                                                    const end = new Date(request.end_date);\n                                                                    const diffTime = Math.abs(end.getTime() - start.getTime());\n                                                                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;\n                                                                    return `共 ${diffDays} 天`;\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    request.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/60 rounded-lg p-2 border border-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 882,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-blue-700\",\n                                                                        children: \"請假原因\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg text-blue-800 leading-relaxed\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, request.id, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 33\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 font-medium text-base\",\n                                            children: \"尚無請假記錄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 898,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"您還沒有提交過請假申請\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-3 border border-gray-100 shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"最近加班申請\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: overtimeRequests.slice(0, 5).length > 0 ? overtimeRequests.slice(0, 5).map((request, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-100 hover:border-orange-200 hover:shadow-md transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                    lineNumber: 922,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-bold text-orange-900 text-xl\",\n                                                                        children: request.overtime_date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg text-orange-600\",\n                                                                        children: \"加班申請\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 926,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 924,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    request.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleCancelOvertimeRequest(request.id),\n                                                                className: \"p-1 rounded-full bg-red-100 hover:bg-red-200 transition-colors duration-200 group\",\n                                                                title: \"取消申請\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-red-600 group-hover:text-red-700\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-lg font-bold bg-amber-100 text-amber-800 border border-amber-300 animate-pulse\",\n                                                                children: \"\\uD83D\\uDD50 待審核\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 45\n                                                    }, this) : request.status === \"approved\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300\",\n                                                        children: \"✅ 已核准\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 45\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-lg font-bold bg-red-100 text-red-800 border border-red-300\",\n                                                        children: \"❌ 已拒絕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/60 rounded-lg p-2 border border-orange-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-orange-700\",\n                                                                        children: \"加班時間\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 959,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-orange-900\",\n                                                                children: [\n                                                                    request.start_time,\n                                                                    \" 至 \",\n                                                                    request.end_time\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg text-orange-600 mt-1\",\n                                                                children: (()=>{\n                                                                    const start = new Date(`2000-01-01 ${request.start_time}`);\n                                                                    const end = new Date(`2000-01-01 ${request.end_time}`);\n                                                                    const diffHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);\n                                                                    return `共 ${diffHours.toFixed(1)} 小時`;\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    request.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/60 rounded-lg p-2 border border-orange-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-semibold text-orange-700\",\n                                                                        children: \"加班原因\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                        lineNumber: 979,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg text-orange-800 leading-relaxed\",\n                                                                children: request.reason\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                                lineNumber: 981,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, request.id, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 33\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_FileText_History_LogOut_Plus_Settings_Timer_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 font-medium text-base\",\n                                            children: \"尚無加班申請\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"您還沒有提交過加班申請\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n                lineNumber: 575,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx\",\n        lineNumber: 564,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/m/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstallPrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Monitor,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PWAInstallPrompt() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPrompt, setShowPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 檢測是否為iOS設備\n        const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);\n        setIsIOS(iOS);\n        // 檢測是否已經是獨立模式（已安裝）\n        const standalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n        setIsStandalone(standalone);\n        // 監聽beforeinstallprompt事件\n        const handleBeforeInstallPrompt = (e)=>{\n            e.preventDefault();\n            setDeferredPrompt(e);\n            // 延遲顯示提示，讓用戶先體驗應用\n            setTimeout(()=>{\n                if (!standalone) {\n                    setShowPrompt(true);\n                }\n            }, 3000);\n        };\n        // 監聽應用安裝事件\n        const handleAppInstalled = ()=>{\n            setShowPrompt(false);\n            setDeferredPrompt(null);\n            console.log(\"PWA 已安裝\");\n        };\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        try {\n            await deferredPrompt.prompt();\n            const { outcome } = await deferredPrompt.userChoice;\n            if (outcome === \"accepted\") {\n                console.log(\"用戶接受安裝\");\n            } else {\n                console.log(\"用戶拒絕安裝\");\n            }\n            setDeferredPrompt(null);\n            setShowPrompt(false);\n        } catch (error) {\n            console.error(\"安裝過程中發生錯誤:\", error);\n        }\n    };\n    const handleDismiss = ()=>{\n        setShowPrompt(false);\n        // 24小時後再次顯示\n        if (false) {}\n    };\n    // 如果已經是獨立模式，不顯示提示\n    if (isStandalone) return null;\n    // 檢查是否在24小時內被拒絕過（僅在客戶端執行）\n    if (false) {}\n    // iOS設備顯示手動安裝指引\n    if (isIOS && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"點擊 Safari 底部的分享按鈕 \\uD83D\\uDCE4，然後選擇「加入主畫面」\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDismiss,\n                                    className: \"px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                    children: \"稍後再說\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 101,\n            columnNumber: 13\n        }, this);\n    }\n    // Android/Desktop 顯示安裝按鈕\n    if (deferredPrompt && showPrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold text-gray-900 mb-1\",\n                                children: \"安裝考勤系統\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"安裝到您的設備，享受更好的使用體驗\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleInstallClick,\n                                        className: \"flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"安裝\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDismiss,\n                                        className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                                        children: \"稍後再說\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDismiss,\n                        className: \"w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Monitor_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n                lineNumber: 135,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx\",\n            lineNumber: 134,\n            columnNumber: 13\n        }, this);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstallPrompt.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceWorkerRegistration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ServiceWorkerRegistration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then((registration)=>{\n                console.log(\"Service Worker 註冊成功:\", registration.scope);\n                // 檢查更新\n                registration.addEventListener(\"updatefound\", ()=>{\n                    const newWorker = registration.installing;\n                    if (newWorker) {\n                        newWorker.addEventListener(\"statechange\", ()=>{\n                            if (newWorker.state === \"installed\" && navigator.serviceWorker.controller) {\n                                // 有新版本可用\n                                console.log(\"新版本可用，建議重新載入頁面\");\n                                // 可以在這裡顯示更新提示\n                                if (confirm(\"發現新版本，是否立即更新？\")) {\n                                    newWorker.postMessage({\n                                        type: \"SKIP_WAITING\"\n                                    });\n                                    window.location.reload();\n                                }\n                            }\n                        });\n                    }\n                });\n            }).catch((error)=>{\n                console.error(\"Service Worker 註冊失敗:\", error);\n            });\n            // 監聽Service Worker控制權變更\n            navigator.serviceWorker.addEventListener(\"controllerchange\", ()=>{\n                console.log(\"Service Worker 控制權已變更\");\n                window.location.reload();\n            });\n        } else {\n            console.log(\"此瀏覽器不支援 Service Worker\");\n        }\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ServiceWorkerRegistration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * 按鈕組件 - 遷移自 Han AttendanceOS 設計系統\n * \n * 功能特色：\n * - 完整的變體支援（主要、次要、成功、警告、錯誤等）\n * - 多種尺寸選項（xs, sm, md, lg, xl）\n * - 載入狀態支援\n * - 圖標支援\n * - 無障礙設計\n * - Apple 設計語言風格\n */ const Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"primary\", size = \"md\", loading = false, icon, children, disabled, ...props }, ref)=>{\n    // 基礎樣式\n    const baseStyles = [\n        // 基礎屬性\n        \"inline-flex items-center justify-center gap-2\",\n        \"font-medium text-center whitespace-nowrap\",\n        \"border border-transparent rounded-lg\",\n        \"transition-all duration-150 ease-in-out\",\n        \"cursor-pointer select-none\",\n        \"outline-none focus:outline-none\",\n        // 焦點樣式\n        \"focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n        // 禁用樣式\n        \"disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none\",\n        // 防止雙擊選中\n        \"tap-highlight-transparent\"\n    ];\n    // 尺寸樣式\n    const sizeStyles = {\n        xs: \"px-2 py-1 text-xs leading-tight rounded-sm min-h-[1.5rem]\",\n        sm: \"px-3 py-1.5 text-sm leading-tight rounded-md min-h-[2rem]\",\n        md: \"px-4 py-2 text-base leading-normal rounded-lg min-h-[2.5rem]\",\n        lg: \"px-6 py-3 text-lg leading-normal rounded-xl min-h-[3rem]\",\n        xl: \"px-8 py-4 text-xl leading-normal rounded-xl min-h-[3.5rem]\"\n    };\n    // 變體樣式\n    const variantStyles = {\n        primary: [\n            \"bg-gradient-primary text-white border-primary-600 shadow-soft\",\n            \"hover:bg-primary-700 hover:border-primary-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-primary-800 active:border-primary-800 active:shadow-soft active:translate-y-0\"\n        ],\n        secondary: [\n            \"bg-gradient-secondary text-white border-secondary-600 shadow-soft\",\n            \"hover:bg-secondary-700 hover:border-secondary-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-secondary-800 active:border-secondary-800 active:shadow-soft active:translate-y-0\"\n        ],\n        success: [\n            \"bg-gradient-success text-white border-success-600 shadow-soft\",\n            \"hover:bg-success-700 hover:border-success-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-success-800 active:border-success-800 active:shadow-soft active:translate-y-0\"\n        ],\n        warning: [\n            \"bg-gradient-warning text-white border-warning-600 shadow-soft\",\n            \"hover:bg-warning-700 hover:border-warning-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-warning-800 active:border-warning-800 active:shadow-soft active:translate-y-0\"\n        ],\n        error: [\n            \"bg-gradient-error text-white border-error-600 shadow-soft\",\n            \"hover:bg-error-700 hover:border-error-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-error-800 active:border-error-800 active:shadow-soft active:translate-y-0\"\n        ],\n        info: [\n            \"bg-gradient-to-r from-info-500 to-info-600 text-white border-info-600 shadow-soft\",\n            \"hover:from-info-600 hover:to-info-700 hover:border-info-700 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:from-info-700 active:to-info-800 active:border-info-800 active:shadow-soft active:translate-y-0\"\n        ],\n        outline: [\n            \"bg-transparent text-neutral-700 border-neutral-300\",\n            \"hover:bg-neutral-50 hover:border-neutral-400\",\n            \"active:bg-neutral-100\"\n        ],\n        ghost: [\n            \"bg-transparent text-neutral-700 border-transparent\",\n            \"hover:bg-neutral-100 hover:text-neutral-900\",\n            \"active:bg-neutral-200\"\n        ],\n        link: [\n            \"bg-transparent text-primary-600 border-transparent p-0 h-auto min-h-0\",\n            \"hover:text-primary-700 hover:underline\",\n            \"active:text-primary-800\"\n        ],\n        glass: [\n            \"backdrop-blur-sm bg-white/20 text-neutral-900 border-white/20 shadow-soft\",\n            \"hover:bg-white/30 hover:shadow-medium hover:-translate-y-0.5\",\n            \"active:bg-white/40 active:shadow-soft active:translate-y-0\"\n        ]\n    };\n    // 載入動畫樣式\n    const loadingSpinner = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"animate-spin h-4 w-4\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                className: \"opacity-25\",\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n                lineNumber: 134,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                className: \"opacity-75\",\n                fill: \"currentColor\",\n                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n                lineNumber: 142,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n        lineNumber: 128,\n        columnNumber: 13\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseStyles, sizeStyles[size], variantStyles[variant], className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && loadingSpinner,\n            !loading && icon && icon,\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/components/ui/button.tsx\",\n        lineNumber: 151,\n        columnNumber: 13\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 確保組件已掛載，避免 hydration 錯誤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 檢查本地存儲中的用戶資訊\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return; // 只有在組件掛載後才執行\n        const initAuth = async ()=>{\n            try {\n                console.log(\"開始認證初始化\");\n                // 首先檢查 localStorage\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"本地存儲的用戶資料:\", storedUser);\n                if (storedUser) {\n                    const userData = JSON.parse(storedUser);\n                    console.log(\"解析的用戶資料:\", userData);\n                    setUser(userData);\n                    // 簡化驗證 - 暫時跳過會話驗證，專注解決跳轉問題\n                    console.log(\"用戶已設定，跳過會話驗證\");\n                } else {\n                    console.log(\"沒有本地用戶資料\");\n                }\n            } catch (error) {\n                console.error(\"認證初始化失敗:\", error);\n                if (false) {}\n                setUser(null);\n            } finally{\n                console.log(\"認證初始化完成，設定 loading = false\");\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, [\n        mounted\n    ]);\n    const login = (userData)=>{\n        console.log(\"AuthContext login 被調用:\", userData);\n        setUser(userData);\n        if (false) {}\n        console.log(\"AuthContext 用戶狀態已更新\");\n    };\n    const logout = ()=>{\n        setUser(null);\n        if (false) {}\n        router.push(\"/login\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// 保護路由的 HOC\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        console.log(\"withAuth 檢查:\", {\n            loading,\n            isAuthenticated\n        });\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isAuthenticated) {\n                console.log(\"withAuth: 未認證，導向登入頁面\");\n                router.push(\"/login\");\n            }\n        }, [\n            isAuthenticated,\n            loading,\n            router\n        ]);\n        if (loading) {\n            console.log(\"withAuth: 載入中\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            console.log(\"withAuth: 未認證，返回 null\");\n            return null;\n        }\n        console.log(\"withAuth: 已認證，渲染組件\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateHours: () => (/* binding */ calculateHours),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合併 Tailwind CSS 類名的工具函數\n * 使用 clsx 處理條件類名，使用 tailwind-merge 解決衝突\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期為 YYYY-MM-DD 格式\n */ function formatDate(date) {\n    const d = new Date(date);\n    return d.toISOString().split(\"T\")[0];\n}\n/**\n * 格式化時間為 HH:MM 格式\n */ function formatTime(date) {\n    const d = new Date(date);\n    return d.toTimeString().slice(0, 5);\n}\n/**\n * 格式化日期時間為本地格式\n */ function formatDateTime(date) {\n    const d = new Date(date);\n    return d.toLocaleString(\"zh-TW\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\n/**\n * 計算兩個時間之間的小時差\n */ function calculateHours(startTime, endTime) {\n    const start = new Date(`2000-01-01 ${startTime}`);\n    const end = new Date(`2000-01-01 ${endTime}`);\n    const diffMs = end.getTime() - start.getTime();\n    return Math.round(diffMs / (1000 * 60 * 60) * 100) / 100;\n}\n/**\n * 狀態映射函數\n */ function getStatusColor(status) {\n    const statusMap = {\n        \"active\": \"text-success-600 bg-success-50\",\n        \"inactive\": \"text-neutral-600 bg-neutral-50\",\n        \"pending\": \"text-warning-600 bg-warning-50\",\n        \"approved\": \"text-success-600 bg-success-50\",\n        \"rejected\": \"text-error-600 bg-error-50\",\n        \"present\": \"text-success-600 bg-success-50\",\n        \"absent\": \"text-error-600 bg-error-50\",\n        \"late\": \"text-warning-600 bg-warning-50\"\n    };\n    return statusMap[status] || \"text-neutral-600 bg-neutral-50\";\n}\n/**\n * API 錯誤處理\n */ function handleApiError(error) {\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    }\n    if (error.message) {\n        return error.message;\n    }\n    return \"發生未知錯誤，請稍後再試\";\n}\n/**\n * 防抖函數\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 節流函數\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17a5a9779ec4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXR0ZW5kYW5jZS1uZXh0anMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzA3YWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxN2E1YTk3NzllYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PWAInstallPrompt */ \"(rsc)/./src/components/PWAInstallPrompt.tsx\");\n/* harmony import */ var _components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ServiceWorkerRegistration */ \"(rsc)/./src/components/ServiceWorkerRegistration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Han AttendanceOS\",\n    description: \"遠漢科技考勤管理系統 - 智能打卡、請假申請、工作回報\",\n    applicationName: \"Han AttendanceOS\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"考勤系統\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    manifest: \"/manifest.json\",\n    icons: {\n        icon: [\n            {\n                url: \"/favicon.ico\",\n                type: \"image/x-icon\",\n                sizes: \"16x16\"\n            },\n            {\n                url: \"/icon-32x32.png\",\n                sizes: \"32x32\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/icon-16x16.png\",\n                sizes: \"16x16\",\n                type: \"image/png\"\n            }\n        ],\n        shortcut: \"/favicon.ico\",\n        apple: \"/icons/apple-touch-icon.png\"\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"msapplication-TileColor\": \"#4f46e5\",\n        \"msapplication-TileImage\": \"/icons/icon-144x144.png\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\n// 將 themeColor 移到 viewport 配置\nconst viewport = {\n    themeColor: \"#4f46e5\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-TW\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstallPrompt__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServiceWorkerRegistration__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/2024newdev/attend_next/frontend/src/app/layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/m/page.tsx":
/*!****************************!*\
  !*** ./src/app/m/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/app/m/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/PWAInstallPrompt.tsx":
/*!*********************************************!*\
  !*** ./src/components/PWAInstallPrompt.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/PWAInstallPrompt.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ServiceWorkerRegistration.tsx":
/*!******************************************************!*\
  !*** ./src/components/ServiceWorkerRegistration.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/components/ServiceWorkerRegistration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/2024newdev/attend_next/frontend/src/contexts/AuthContext.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdHRlbmRhbmNlLW5leHRqcy8uL3NyYy9hcHAvZmF2aWNvbi5pY28/NGQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fm%2Fpage&page=%2Fm%2Fpage&appPaths=%2Fm%2Fpage&pagePath=private-next-app-dir%2Fm%2Fpage.tsx&appDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkevin%2F2024newdev%2Fattend_next%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();