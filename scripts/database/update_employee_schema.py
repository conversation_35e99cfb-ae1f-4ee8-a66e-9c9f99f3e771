#!/usr/bin/env python3
"""
更新員工表結構腳本
添加缺少的重要欄位
"""

import sqlite3
import sys

def update_employee_schema():
    """
    更新員工表結構，添加新的欄位。
    
    返回：
    bool: 是否成功更新
    """
    try:
        conn = sqlite3.connect('attendance.db')
        cursor = conn.cursor()
        
        # 要添加的新欄位
        new_columns = [
            ('password', 'TEXT', '登錄密碼'),
            ('hire_date', 'DATE', '入職日期'),
            ('status', 'TEXT DEFAULT "active"', '員工狀態'),
            ('salary_level', 'TEXT', '薪資等級'),
            ('id_number', 'TEXT', '身分證號'),
            ('address', 'TEXT', '聯絡地址'),
            ('emergency_contact', 'TEXT', '緊急聯絡人'),
            ('emergency_phone', 'TEXT', '緊急聯絡電話')
        ]
        
        print("開始更新員工表結構...")
        
        for column_name, column_type, description in new_columns:
            try:
                sql = f'ALTER TABLE employees ADD COLUMN {column_name} {column_type}'
                cursor.execute(sql)
                print(f'✅ 成功添加欄位: {column_name} ({description})')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print(f'⚠️  欄位已存在: {column_name}')
                else:
                    print(f'❌ 添加欄位失敗: {column_name} - {e}')
        
        # 為現有員工設定預設密碼
        cursor.execute("""
            UPDATE employees 
            SET password = 'password123', 
                status = 'active',
                hire_date = date('now', '-' || (id * 30) || ' days')
            WHERE password IS NULL
        """)
        
        conn.commit()
        print(f"✅ 更新了 {cursor.rowcount} 位員工的預設資料")
        
        # 檢查更新後的表結構
        cursor.execute("PRAGMA table_info(employees)")
        columns = cursor.fetchall()
        
        print("\n📋 更新後的員工表結構:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        print("\n🎉 員工表結構更新完成!")
        return True
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        return False

if __name__ == "__main__":
    success = update_employee_schema()
    sys.exit(0 if success else 1) 