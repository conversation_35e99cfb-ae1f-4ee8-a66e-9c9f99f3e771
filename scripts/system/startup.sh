#!/bin/bash

# 智慧考勤系統啟動腳本
# 版本: 2.0
# 更新日期: 2024-05

set -e  # 任何命令失敗時立即退出

# 顯示彩色輸出的函數
function print_green {
    echo -e "\033[0;32m$1\033[0m"
}

function print_yellow {
    echo -e "\033[0;33m$1\033[0m"
}

function print_red {
    echo -e "\033[0;31m$1\033[0m"
}

function print_blue {
    echo -e "\033[0;34m$1\033[0m"
}

# 檢查系統需求
function check_requirements {
    print_blue "檢查系統需求..."
    
    # 檢查 Python 版本
    if ! command -v python3 &> /dev/null; then
        print_red "錯誤: 未找到 Python 3，請先安裝 Python 3.8 或更高版本"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_yellow "Python 版本: $python_version"
    
    # 檢查 pip
    if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
        print_red "錯誤: 未找到 pip，請先安裝 pip"
        exit 1
    fi
}

# 設定環境變數
function setup_environment {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_yellow "複製環境變數範本..."
            cp .env.example .env
            print_yellow "請編輯 .env 檔案設定您的環境變數"
        else
            print_yellow "建立基本 .env 檔案..."
            cat > .env << EOF
DEBUG=False
SECRET_KEY=dev_key_please_change_in_production
PORT=7072
LOG_LEVEL=INFO
EOF
        fi
    fi
}

# 建立虛擬環境
function setup_virtual_env {
    if [ ! -d ".venv" ]; then
        print_yellow "建立虛擬環境..."
        python3 -m venv .venv
        print_green "✅ 虛擬環境建立完成"
    fi
    
    # 激活虛擬環境
    print_yellow "激活虛擬環境..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
    elif [ -f ".venv/Scripts/activate" ]; then
        source .venv/Scripts/activate
    else
        print_red "錯誤: 找不到虛擬環境啟動腳本"
        exit 1
    fi
    print_green "✅ 虛擬環境已激活"
}

# 安裝依賴套件
function install_dependencies {
    print_yellow "安裝依賴套件..."
    
    # 升級 pip
    pip install --upgrade pip
    
    # 安裝生產依賴
    pip install -r requirements.txt
    
    print_green "✅ 依賴套件安裝完成"
}

# 初始化資料庫
function setup_database {
    if [ ! -f "attendance.db" ]; then
        print_yellow "初始化資料庫..."
        python database.py
        
        # 初始化測試資料（開發環境）
        if [ -f "init_test_data.py" ]; then
            python init_test_data.py
        fi
        
        print_green "✅ 資料庫初始化完成"
    else
        print_yellow "資料庫已存在，跳過初始化"
    fi
}

# 建立必要目錄
function create_directories {
    directories=("uploads" "logs")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_yellow "建立目錄: $dir"
        fi
    done
    
    print_green "✅ 目錄結構檢查完成"
}

# 執行基本測試
function run_basic_tests {
    if [ -f "test_basic.py" ]; then
        print_yellow "執行基本功能測試..."
        if python test_basic.py; then
            print_green "✅ 基本功能測試通過"
        else
            print_red "⚠️  基本功能測試失敗，但將繼續啟動"
        fi
    fi
}

# 顯示系統資訊
function show_info {
    print_blue "系統資訊:"
    echo "  📁 專案目錄: $(pwd)"
    echo "  🐍 Python 版本: $(python --version)"
    echo "  💾 資料庫: attendance.db"
    echo "  📊 端口: ${PORT:-7072}"
    echo "  🔧 環境: ${DEBUG:-False}"
}

# 啟動應用程式
function start_application {
    print_green "🚀 啟動智慧考勤系統..."
    echo ""
    print_blue "應用程式將在以下地址運行:"
    echo "  🌐 本機: http://localhost:${PORT:-7072}"
    echo "  🌐 網路: http://$(hostname -I | awk '{print $1}'):${PORT:-7072}"
    echo ""
    print_yellow "按 Ctrl+C 停止應用程式"
    echo ""
    
    # 啟動應用程式
    python app.py
}

# 主函數
function main {
    print_green "======================================"
    print_green "   智慧考勤系統啟動腳本 v2.0"
    print_green "======================================"
    echo ""
    
    check_requirements
    setup_environment
    setup_virtual_env
    install_dependencies
    setup_database
    create_directories
    run_basic_tests
    show_info
    echo ""
    start_application
}

# 錯誤處理
trap 'print_red "❌ 啟動過程中發生錯誤"; exit 1' ERR

# 執行主函數
main "$@"