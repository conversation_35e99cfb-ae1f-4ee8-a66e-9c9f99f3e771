/* 員工管理模組樣式 */

.employee-management {
  max-width: 1400px;
  margin: 0 auto;
}

/* 搜尋區域 */
.search-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
}

.search-container {
  display: flex;
  gap: var(--space-4);
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-input-group {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.search-input-group .form-input {
  padding-right: 50px;
}

.search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.search-btn:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-50%) scale(1.05);
}

.filter-group {
  display: flex;
  gap: var(--space-3);
}

.filter-group .form-select {
  min-width: 150px;
}

/* 員工資訊顯示 */
.employee-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-lg);
}

.employee-details {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 2px;
}

.employee-email {
  font-size: var(--text-sm);
  color: var(--neutral-500);
}

.employee-id {
  background: var(--neutral-100);
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.contact-info div {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin-bottom: 2px;
}

.contact-info div:last-child {
  margin-bottom: 0;
}

/* 動作按鈕 */
.action-buttons {
  display: flex;
  gap: var(--space-2);
}

.action-buttons .btn {
  padding: var(--space-2);
  min-width: auto;
}

/* 列表控制 */
.list-controls {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

/* 排序功能 */
.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color var(--duration-200) var(--ease-out);
}

.sortable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sort-icon {
  font-size: var(--text-sm);
  opacity: 0.6;
  margin-left: var(--space-1);
}

/* 卡片檢視 */
.card-view {
  padding: var(--space-4);
}

.employee-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

.employee-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
  box-shadow: var(--shadow-md);
}

.employee-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.employee-card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-100);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.employee-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--text-2xl);
}

.employee-card-info {
  flex: 1;
}

.employee-card-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--neutral-800);
  margin: 0 0 var(--space-1) 0;
}

.employee-card-id {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--neutral-500);
  margin: 0;
}

.employee-card-actions {
  display: flex;
  gap: var(--space-2);
}

.employee-card-body {
  padding: var(--space-6);
}

.card-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--neutral-100);
}

.card-field:last-child {
  border-bottom: none;
}

.field-label {
  font-weight: 500;
  color: var(--neutral-600);
  font-size: var(--text-sm);
}

.field-value {
  color: var(--neutral-800);
  font-size: var(--text-sm);
}

.employee-card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--neutral-100);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--neutral-50);
}

/* 批次操作 */
.batch-actions {
  position: fixed;
  bottom: var(--space-6);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--space-4) var(--space-6);
  box-shadow: var(--shadow-xl);
  z-index: 100;
  animation: slideInUp var(--duration-300) var(--ease-out);
}

.batch-actions-content {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.batch-buttons {
  display: flex;
  gap: var(--space-3);
}

/* 表單樣式 */
.employee-form {
  max-width: 600px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-grid .form-group:nth-child(n+5) {
  grid-column: span 2;
}

.form-note {
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  color: var(--neutral-600);
}

/* 刪除確認 */
.delete-confirmation {
  text-align: center;
  padding: var(--space-4);
}

.warning-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
}

.warning-text {
  color: var(--error-600);
  font-size: var(--text-sm);
  margin-top: var(--space-2);
}

/* 無資料狀態 */
.no-data {
  text-align: center;
  padding: var(--space-8);
  color: var(--neutral-500);
  font-size: var(--text-lg);
}

/* 響應式設計 */
@media (max-width: 1024px) {
  .search-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input-group {
    min-width: auto;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .employee-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .employee-management {
    padding: 0;
  }
  
  .search-section {
    margin: 0 var(--space-4) var(--space-4) var(--space-4);
    padding: var(--space-4);
  }
  
  .view-header {
    padding: 0 var(--space-4);
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid .form-group:nth-child(n+5) {
    grid-column: span 1;
  }
  
  .employee-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .employee-avatar {
    align-self: center;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-buttons .btn {
    width: 100%;
  }
  
  .batch-actions {
    left: var(--space-4);
    right: var(--space-4);
    transform: none;
  }
  
  .batch-actions-content {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
  
  .employee-cards-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .search-section {
    margin: 0 var(--space-2) var(--space-4) var(--space-2);
    padding: var(--space-3);
  }
  
  .view-header {
    padding: 0 var(--space-2);
  }
  
  .filter-group {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .filter-group .form-select {
    min-width: auto;
  }
  
  /* 隱藏表格的某些欄位以節省空間 */
  .table th:nth-child(4),
  .table td:nth-child(4),
  .table th:nth-child(6),
  .table td:nth-child(6) {
    display: none;
  }
  
  .employee-card-header {
    padding: var(--space-4);
  }
  
  .employee-card-body,
  .employee-card-footer {
    padding: var(--space-4);
  }
}

/* 動畫效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
  .employee-card-footer {
    background: var(--neutral-800);
  }
  
  .form-note {
    background: var(--neutral-800);
  }
  
  .employee-id {
    background: var(--neutral-800);
    color: var(--neutral-200);
  }
}