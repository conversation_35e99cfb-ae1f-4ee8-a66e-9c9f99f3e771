#!/bin/bash

# Han AttendanceOS 登入功能測試腳本
# 使用 curl 來測試 API 端點和前端頁面

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:7072"
FRONTEND_URL="http://localhost:7075"

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 顯示標題
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                Han AttendanceOS 登入功能測試                 ║"
    echo "║                      完整測試報告                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 測試前端頁面可訪問性
test_frontend_pages() {
    log_test "測試前端頁面可訪問性"
    echo "----------------------------------------"
    
    local pages=(
        "$FRONTEND_URL/admin:管理後台"
        "$FRONTEND_URL/m:員工介面"
        "$FRONTEND_URL/admin/login:管理員登入頁"
        "$FRONTEND_URL/m/login:員工登入頁"
    )
    
    local success_count=0
    local total_count=${#pages[@]}
    
    for page_info in "${pages[@]}"; do
        local url="${page_info%:*}"
        local name="${page_info#*:}"
        
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url")
        
        if [ "$status_code" = "200" ]; then
            log_success "✅ $name: $status_code"
            ((success_count++))
        else
            log_error "❌ $name: $status_code"
        fi
    done
    
    echo ""
    log_info "前端頁面測試結果: $success_count/$total_count 通過"
    echo ""
}

# 測試 API 健康檢查
test_api_health() {
    log_test "測試 API 健康檢查"
    echo "----------------------------------------"
    
    local response=$(curl -s "$BASE_URL/api/health")
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/health")
    
    if [ "$status_code" = "200" ]; then
        log_success "✅ API 健康檢查通過: $status_code"
        
        # 解析 JSON 響應 (簡單版本)
        if echo "$response" | grep -q '"status":"healthy"'; then
            log_success "✅ 系統狀態: 健康"
        else
            log_warning "⚠️  系統狀態: 未知"
        fi
        
        if echo "$response" | grep -q '"database"'; then
            log_success "✅ 資料庫連接: 正常"
        else
            log_warning "⚠️  資料庫連接: 未知"
        fi
    else
        log_error "❌ API 健康檢查失敗: $status_code"
    fi
    
    echo ""
}

# 測試登入功能
test_login() {
    local employee_id="$1"
    local password="$2"
    local description="$3"
    
    log_test "測試 $description 登入"
    echo "----------------------------------------"
    
    local response=$(curl -s -X POST "$BASE_URL/api/login" \
        -H "Content-Type: application/json" \
        -d "{\"employee_id\": \"$employee_id\", \"password\": \"$password\"}")
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$BASE_URL/api/login" \
        -H "Content-Type: application/json" \
        -d "{\"employee_id\": \"$employee_id\", \"password\": \"$password\"}")
    
    if [ "$status_code" = "200" ]; then
        if echo "$response" | grep -q '"success":true'; then
            log_success "✅ $description 登入成功"
            
            # 提取用戶信息 (簡單版本)
            if echo "$response" | grep -q '"employee_name"'; then
                local user_info=$(echo "$response" | sed 's/.*"employee_name":"\([^"]*\)".*/\1/')
                log_info "   用戶: $user_info"
            fi
            
            if echo "$response" | grep -q '"role_id"'; then
                local role_id=$(echo "$response" | sed 's/.*"role_id":\([0-9]*\).*/\1/')
                log_info "   角色ID: $role_id"
                
                # 驗證管理員權限
                if [ "$employee_id" = "admin" ] && [ "$role_id" = "999" ]; then
                    log_success "✅ 管理員權限驗證通過"
                elif [ "$employee_id" != "admin" ] && [ "$role_id" != "999" ]; then
                    log_success "✅ 員工權限驗證通過"
                else
                    log_warning "⚠️  權限驗證異常"
                fi
            fi
            
            return 0
        else
            log_error "❌ $description 登入失敗"
            if echo "$response" | grep -q '"error"'; then
                local error_msg=$(echo "$response" | sed 's/.*"error":"\([^"]*\)".*/\1/')
                log_error "   錯誤: $error_msg"
            fi
            return 1
        fi
    else
        log_error "❌ $description 登入請求失敗: $status_code"
        return 1
    fi
}

# 測試錯誤登入
test_invalid_login() {
    log_test "測試錯誤登入處理"
    echo "----------------------------------------"
    
    local test_cases=(
        "invalid:invalid:無效帳號"
        "admin:wrongpassword:錯誤密碼"
        ":admin123:空帳號"
        "admin::空密碼"
    )
    
    local success_count=0
    local total_count=${#test_cases[@]}
    
    for test_case in "${test_cases[@]}"; do
        local employee_id="${test_case%%:*}"
        local temp="${test_case#*:}"
        local password="${temp%%:*}"
        local description="${test_case##*:}"
        
        local response=$(curl -s -X POST "$BASE_URL/api/login" \
            -H "Content-Type: application/json" \
            -d "{\"employee_id\": \"$employee_id\", \"password\": \"$password\"}")
        
        if echo "$response" | grep -q '"success":false'; then
            log_success "✅ $description: 正確拒絕登入"
            ((success_count++))
        else
            log_error "❌ $description: 意外允許登入"
        fi
    done
    
    echo ""
    log_info "錯誤登入測試結果: $success_count/$total_count 通過"
    echo ""
}

# 測試 CORS 和 OPTIONS 請求
test_cors() {
    log_test "測試 CORS 和 OPTIONS 請求"
    echo "----------------------------------------"
    
    local options_response=$(curl -s -X OPTIONS "$BASE_URL/api/login" \
        -H "Origin: http://localhost:7075" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type")
    
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" -X OPTIONS "$BASE_URL/api/login" \
        -H "Origin: http://localhost:7075")
    
    if [ "$status_code" = "200" ]; then
        log_success "✅ OPTIONS 請求成功: $status_code"
        log_success "✅ CORS 配置正常"
    else
        log_error "❌ OPTIONS 請求失敗: $status_code"
    fi
    
    echo ""
}

# 主測試函數
main() {
    show_banner
    
    # 檢查服務是否運行
    log_info "檢查服務狀態..."
    if ! curl -s "$BASE_URL/api/health" > /dev/null; then
        log_error "後端服務未運行，請先啟動系統"
        exit 1
    fi
    
    if ! curl -s "$FRONTEND_URL" > /dev/null; then
        log_error "前端服務未運行，請先啟動系統"
        exit 1
    fi
    
    log_success "✅ 服務狀態檢查通過"
    echo ""
    
    # 執行測試
    test_frontend_pages
    test_api_health
    test_cors
    
    # 測試有效登入
    local admin_success=false
    local employee_success=false
    
    if test_login "admin" "admin123" "系統管理員"; then
        admin_success=true
    fi
    echo ""
    
    if test_login "E001" "password123" "一般員工"; then
        employee_success=true
    fi
    echo ""
    
    # 測試無效登入
    test_invalid_login
    
    # 生成測試報告
    log_test "測試總結報告"
    echo "========================================"
    
    local total_tests=0
    local passed_tests=0
    
    if [ "$admin_success" = true ]; then
        log_success "✅ 管理員登入功能正常"
        ((passed_tests++))
    else
        log_error "❌ 管理員登入功能異常"
    fi
    ((total_tests++))
    
    if [ "$employee_success" = true ]; then
        log_success "✅ 員工登入功能正常"
        ((passed_tests++))
    else
        log_error "❌ 員工登入功能異常"
    fi
    ((total_tests++))
    
    echo ""
    if [ "$passed_tests" -eq "$total_tests" ]; then
        log_success "🎉 所有核心登入測試通過！($passed_tests/$total_tests)"
        log_success "✅ Han AttendanceOS 登入系統運行正常"
    else
        log_warning "⚠️  部分測試失敗 ($passed_tests/$total_tests)"
        log_warning "請檢查失敗的測試項目"
    fi
    
    echo ""
    log_info "📋 測試完成時間: $(date)"
    log_info "🌐 管理後台: $FRONTEND_URL/admin"
    log_info "👥 員工介面: $FRONTEND_URL/m"
    log_info "🔧 API 健康: $BASE_URL/api/health"
}

# 執行測試
main "$@"
