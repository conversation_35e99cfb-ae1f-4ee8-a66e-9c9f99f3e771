"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import {
    ArrowLeft,
    User,
    Lock,
    Bell,
    Smartphone,
    Eye,
    EyeOff,
    Save,
    Camera,
    Mail,
    Phone,
    Building,
    Settings as SettingsIcon,
    Image,
    X,
    Key
} from 'lucide-react'

// 🌐 API基礎URL函數
const getApiBaseUrl = () => {
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:7072'
        } else {
            return `http://${hostname}:7072`
        }
    }
    return 'http://localhost:7072'
}

export default function SettingsPage() {
    const { user } = useAuth()
    const router = useRouter()
    const [loading, setLoading] = useState(false)
    const [activeTab, setActiveTab] = useState('profile')
    const [showPassword, setShowPassword] = useState(false)

    // 個人資料表單
    const [profileForm, setProfileForm] = useState({
        name: '',
        email: '',
        phone: '',
        department: ''
    })

    // 密碼修改表單
    const [passwordForm, setPasswordForm] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    })

    // 頭像上傳狀態
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
    const [showAvatarOptions, setShowAvatarOptions] = useState(false)

    // 載入狀態
    const [dataLoading, setDataLoading] = useState(true)

    // 載入完整的員工資料
    useEffect(() => {
        const loadEmployeeData = async () => {
            if (!user?.employee_id) return

            try {
                setDataLoading(true)
                console.log('🔍 開始載入員工完整資料...')

                // 從後台API獲取員工詳細資料
                const response = await fetch(`${getApiBaseUrl()}/api/profile/${user.employee_id}`)
                console.log('📡 員工資料API回應狀態:', response.status)

                if (response.ok) {
                    const result = await response.json()
                    console.log('📦 員工資料API回應:', result)

                    if (result.success && result.profile) {
                        const profile = result.profile

                        // 更新個人資料表單
                        setProfileForm({
                            name: profile.name || user.name || '',
                            email: profile.email || '',
                            phone: profile.phone || '',
                            department: profile.department || user.department_name || ''
                        })

                        // 更新頭像
                        if (profile.avatar && profile.avatar.trim()) {
                            const avatarUrl = `${getApiBaseUrl()}${profile.avatar}`
                            setAvatarUrl(avatarUrl)
                            localStorage.setItem('userAvatar', avatarUrl)
                            console.log('✅ 頭像載入成功:', avatarUrl)
                        } else {
                            console.log('📷 用戶尚未設定頭像')
                            setAvatarUrl('')
                            localStorage.removeItem('userAvatar')
                        }

                        console.log('✅ 員工資料載入成功')
                    } else {
                        console.warn('⚠️ API回應格式異常')
                        // 使用AuthContext中的基本資料作為備援
                        setProfileForm({
                            name: user.name || '',
                            email: user.email || '',
                            phone: '',
                            department: user.department_name || ''
                        })
                    }
                } else {
                    console.warn('⚠️ 無法載入員工資料，使用基本資料')
                    // 使用AuthContext中的基本資料作為備援
                    setProfileForm({
                        name: user.name || '',
                        email: user.email || '',
                        phone: '',
                        department: user.department_name || ''
                    })
                }
            } catch (error) {
                console.error('❌ 載入員工資料失敗:', error)
                // 使用AuthContext中的基本資料作為備援
                setProfileForm({
                    name: user.name || '',
                    email: user.email || '',
                    phone: '',
                    department: user.department_name || ''
                })
            } finally {
                setDataLoading(false)
            }
        }

        loadEmployeeData()
    }, [user?.employee_id, user?.name, user?.email, user?.department_name])

    // 🔍 測試localStorage功能
    const testLocalStorage = () => {
        try {
            // 測試寫入
            const testKey = 'test_storage'
            const testValue = 'test_value_' + Date.now()
            localStorage.setItem(testKey, testValue)

            // 測試讀取
            const readValue = localStorage.getItem(testKey)

            // 清理測試數據
            localStorage.removeItem(testKey)

            if (readValue === testValue) {
                alert('✅ localStorage 功能正常')
                console.log('localStorage 測試成功')
            } else {
                alert('❌ localStorage 讀寫不一致')
                console.error('localStorage 測試失敗: 讀寫不一致')
            }
        } catch (error) {
            alert('❌ localStorage 不可用: ' + error)
            console.error('localStorage 測試失敗:', error)
        }
    }

    // 🔍 檢查頭像狀態
    const checkAvatarStatus = () => {
        const savedAvatar = localStorage.getItem('userAvatar')
        const currentAvatar = avatarUrl

        const status = {
            localStorage: savedAvatar ? '有數據' : '無數據',
            state: currentAvatar ? '有頭像' : '無頭像',
            dataSize: savedAvatar ? Math.round(savedAvatar.length / 1024) + 'KB' : '0KB'
        }

        alert(`頭像狀態檢查:\n• localStorage: ${status.localStorage}\n• 當前狀態: ${status.state}\n• 數據大小: ${status.dataSize}`)
        console.log('頭像狀態檢查:', status)
    }

    const handleProfileUpdate = async () => {
        setLoading(true)
        try {
            console.log('更新個人資料:', profileForm)
            await new Promise(resolve => setTimeout(resolve, 1000))
            alert('個人資料更新成功！')
        } catch (error) {
            console.error('更新失敗:', error)
            alert('更新失敗，請稍後再試')
        } finally {
            setLoading(false)
        }
    }

    const handlePasswordChange = async () => {
        if (passwordForm.newPassword !== passwordForm.confirmPassword) {
            alert('新密碼與確認密碼不符')
            return
        }

        setLoading(true)
        try {
            console.log('修改密碼')
            await new Promise(resolve => setTimeout(resolve, 1000))
            alert('密碼修改成功！')
            setPasswordForm({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            })
        } catch (error) {
            console.error('密碼修改失敗:', error)
            alert('密碼修改失敗，請稍後再試')
        } finally {
            setLoading(false)
        }
    }

    // 處理個人資料表單輸入
    const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setProfileForm(prev => ({
            ...prev,
            [name]: value
        }))
    }

    // 處理密碼表單輸入
    const handlePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setPasswordForm(prev => ({
            ...prev,
            [name]: value
        }))
    }

    // 📸 處理頭像上傳
    const handleAvatarUpload = (type: 'camera' | 'gallery') => {
        console.log(`開始${type === 'camera' ? '拍照' : '選擇照片'}`)

        try {
            const input = document.createElement('input')
            input.type = 'file'
            input.accept = 'image/*'
            input.multiple = false

            // 設定拍照模式 - 使用正確的屬性設定方式
            if (type === 'camera') {
                input.setAttribute('capture', 'environment')
            }

            // 使用 Promise 包裝檔案選擇
            const handleFileSelect = () => {
                return new Promise<File | null>((resolve) => {
                    input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0] || null
                        console.log('選擇的檔案:', file)
                        resolve(file)
                    }

                    input.oncancel = () => {
                        console.log('用戶取消選擇')
                        resolve(null)
                    }

                    // 觸發檔案選擇
                    input.click()
                })
            }

            // 處理檔案選擇
            handleFileSelect().then((file) => {
                if (file) {
                    console.log('檔案資訊:', {
                        name: file.name,
                        size: file.size,
                        type: file.type
                    })

                    // 檢查檔案大小 (限制10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('圖片檔案過大，請選擇小於10MB的圖片')
                        return
                    }

                    // 檢查檔案類型
                    if (!file.type.startsWith('image/')) {
                        alert('請選擇圖片檔案')
                        return
                    }

                    // 讀取檔案
                    const reader = new FileReader()

                    reader.onload = async (e) => {
                        const result = e.target?.result as string
                        console.log('圖片讀取成功，大小:', Math.round(result.length / 1024), 'KB')

                        try {
                            // 🚀 上傳到後台伺服器
                            console.log('📤 開始上傳頭像到伺服器...')

                            // 獲取當前用戶的員工編號 - 改用localStorage
                            let employeeCode = null

                            try {
                                // 方法1: 從localStorage獲取用戶資訊
                                const userDataStr = localStorage.getItem('user')
                                if (userDataStr) {
                                    const userData = JSON.parse(userDataStr)
                                    employeeCode = userData.employee_id || userData.employee_code
                                    console.log('👤 從localStorage獲取員工編號:', employeeCode)
                                }
                            } catch (e) {
                                console.log('localStorage中無用戶資訊，嘗試API驗證')
                            }

                            // 方法2: 如果localStorage沒有，嘗試API驗證
                            if (!employeeCode) {
                                try {
                                    const authResponse = await fetch(`${getApiBaseUrl()}/api/auth/verify`)
                                    if (authResponse.ok) {
                                        const authData = await authResponse.json()
                                        employeeCode = authData.user.employee_code || authData.user.employee_id
                                        console.log('👤 從API獲取員工編號:', employeeCode)
                                    }
                                } catch (authError) {
                                    console.log('API驗證失敗，使用預設員工編號')
                                }
                            }

                            // 方法3: 如果都失敗，使用預設值
                            if (!employeeCode) {
                                employeeCode = 'E001' // 預設使用E001
                                console.log('👤 使用預設員工編號:', employeeCode)
                            }

                            // 創建FormData上傳檔案
                            const formData = new FormData()
                            formData.append('avatar', file)
                            formData.append('employee_id', employeeCode)

                            // 上傳到後台
                            const uploadResponse = await fetch(`${getApiBaseUrl()}/api/profile/avatar`, {
                                method: 'POST',
                                body: formData
                            })

                            const uploadResult = await uploadResponse.json()
                            console.log('📤 上傳結果:', uploadResult)

                            if (uploadResult.success) {
                                // 設定頭像URL（後台返回的完整路徑）
                                const avatarUrl = `${getApiBaseUrl()}${uploadResult.avatar_url}`
                                console.log('🖼️ 新頭像URL:', avatarUrl)
                                setAvatarUrl(avatarUrl)

                                // 同時保存到localStorage作為快取
                                localStorage.setItem('userAvatar', avatarUrl)
                                console.log('✅ 頭像URL已保存到localStorage')

                                // 觸發storage事件，通知其他頁面更新
                                window.dispatchEvent(new StorageEvent('storage', {
                                    key: 'userAvatar',
                                    newValue: avatarUrl
                                }))
                                console.log('✅ storage事件已觸發')

                                // 顯示成功訊息
                                const message = type === 'camera' ? '📸 拍照上傳成功！' : '🖼️ 照片上傳成功！'
                                alert(message)
                            } else {
                                throw new Error(uploadResult.message || '上傳失敗')
                            }
                        } catch (uploadError) {
                            console.error('❌ 頭像上傳失敗:', uploadError)
                            const errorMessage = uploadError instanceof Error ? uploadError.message : '未知錯誤'
                            alert(`❌ 頭像上傳失敗: ${errorMessage}`)

                            // 上傳失敗時，仍然可以暫時保存到localStorage
                            console.log('💾 上傳失敗，暫時保存到localStorage')
                            setAvatarUrl(result)
                            localStorage.setItem('userAvatar', result)
                        }
                    }

                    reader.onerror = () => {
                        console.error('圖片讀取失敗')
                        alert('❌ 圖片讀取失敗，請重試')
                    }

                    reader.readAsDataURL(file)
                } else {
                    console.log('未選擇檔案或用戶取消')
                }
            }).catch((error) => {
                console.error('檔案選擇錯誤:', error)
                alert('❌ 檔案選擇失敗，請重試')
            })

        } catch (error) {
            console.error('頭像上傳功能錯誤:', error)
            alert('❌ 功能暫時無法使用，請稍後再試')
        }

        // 關閉選項框
        setShowAvatarOptions(false)
    }

    const tabs = [
        { id: 'profile', name: '個人資料', icon: User },
        { id: 'password', name: '密碼安全', icon: Lock },
        { id: 'notifications', name: '通知設定', icon: Bell }
    ]

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
            {/* 🎨 背景裝飾 */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
            </div>

            {/* 🌈 頂部彩虹條紋 */}
            <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-blue-400 via-indigo-400 to-purple-400"></div>

            <div className="relative z-10 p-6">
                {/* 🔙 返回按鈕 */}
                <div className="mb-6">
                    <Button
                        onClick={() => router.push('/m')}
                        className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm text-gray-700 hover:bg-white hover:text-gray-900 rounded-2xl px-4 py-2 shadow-lg border border-white/50 transition-all duration-300 hover:scale-105"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        <span className="font-medium">返回主頁</span>
                    </Button>
                </div>

                {/* 📱 主標題 */}
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl shadow-2xl mb-4">
                        <SettingsIcon className="w-10 h-10 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-neutral-900 mb-2">
                        員工設定
                    </h1>
                    <p className="text-neutral-600 font-medium">個人化您的工作體驗</p>
                </div>

                {/* 📋 標籤頁導航 */}
                <div className="bg-white/90 backdrop-blur-xl rounded-3xl p-2 shadow-2xl border border-white/30 mb-6">
                    <div className="flex space-x-1">
                        {tabs.map((tab) => {
                            const Icon = tab.icon
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-2xl font-medium transition-all duration-300 ${activeTab === tab.id
                                        ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg transform scale-105'
                                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                                        }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    <span className="text-sm">{tab.name}</span>
                                </button>
                            )
                        })}
                    </div>
                </div>

                {/* 📝 內容區域 */}
                <div className="bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden">
                    {/* 個人資料標籤 */}
                    {activeTab === 'profile' && (
                        <div className="p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                    <User className="w-4 h-4 text-white" />
                                </div>
                                <h2 className="text-xl font-bold text-gray-800">個人資料</h2>
                            </div>

                            {/* 載入狀態 */}
                            {dataLoading ? (
                                <div className="flex items-center justify-center py-16">
                                    <div className="text-center">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                                            <div className="relative">
                                                <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                                            </div>
                                        </div>
                                        <h4 className="text-lg font-semibold text-gray-700 mb-2">載入員工資料中...</h4>
                                        <p className="text-gray-500">正在從後台獲取您的個人資料</p>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-6">
                                    {/* 頭像區域 */}
                                    <div className="text-center relative">
                                        <div className="relative inline-block">
                                            <div
                                                className="w-48 h-48 bg-gradient-to-br from-indigo-400 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-2xl overflow-hidden cursor-pointer border-4 border-white/50 backdrop-blur-sm hover:scale-105 transition-all duration-300"
                                                onClick={() => setShowAvatarOptions(true)}
                                            >
                                                {avatarUrl ? (
                                                    <img
                                                        src={avatarUrl}
                                                        alt="頭像"
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <User className="w-24 h-24 text-white" />
                                                )}
                                            </div>
                                            <button
                                                onClick={() => setShowAvatarOptions(true)}
                                                className="absolute bottom-2 right-2 w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full shadow-xl flex items-center justify-center border-3 border-white hover:scale-110 transition-all duration-300 hover:shadow-2xl"
                                            >
                                                <Camera className="w-6 h-6 text-white" />
                                            </button>
                                        </div>
                                        <p className="text-lg text-gray-600 mt-4 font-medium">點擊更換頭像</p>

                                        {/* 📸 頭像選擇選項 */}
                                        {showAvatarOptions && (
                                            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                                                <div className="bg-white rounded-3xl p-6 mx-4 w-full max-w-sm shadow-2xl">
                                                    <div className="flex items-center justify-between mb-6">
                                                        <h3 className="text-xl font-bold text-gray-900">選擇頭像</h3>
                                                        <button
                                                            onClick={() => setShowAvatarOptions(false)}
                                                            className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                                                        >
                                                            <X className="w-4 h-4 text-gray-600" />
                                                        </button>
                                                    </div>

                                                    <div className="grid grid-cols-2 gap-6">
                                                        {/* 拍照選項 */}
                                                        <button
                                                            onClick={() => handleAvatarUpload('camera')}
                                                            className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full hover:from-blue-600 hover:to-blue-700 transition-all duration-300 hover:scale-105 shadow-lg mx-auto"
                                                        >
                                                            <Camera className="w-10 h-10 text-white" />
                                                        </button>

                                                        {/* 選擇照片選項 */}
                                                        <button
                                                            onClick={() => handleAvatarUpload('gallery')}
                                                            className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full hover:from-purple-600 hover:to-purple-700 transition-all duration-300 hover:scale-105 shadow-lg mx-auto"
                                                        >
                                                            <Image className="w-10 h-10 text-white" />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* 表單欄位 */}
                                    <div className="grid grid-cols-1 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                <User className="w-4 h-4 inline mr-2" />
                                                姓名
                                            </label>
                                            <input
                                                type="text"
                                                name="name"
                                                value={profileForm.name}
                                                onChange={handleProfileInputChange}
                                                className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                                placeholder="請輸入姓名"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                <Mail className="w-4 h-4 inline mr-2" />
                                                電子郵件
                                            </label>
                                            <input
                                                type="email"
                                                name="email"
                                                value={profileForm.email}
                                                onChange={handleProfileInputChange}
                                                className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                                placeholder="請輸入電子郵件"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                <Phone className="w-4 h-4 inline mr-2" />
                                                聯絡電話
                                            </label>
                                            <input
                                                type="tel"
                                                name="phone"
                                                value={profileForm.phone}
                                                onChange={handleProfileInputChange}
                                                className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                                placeholder="請輸入聯絡電話"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                <Building className="w-4 h-4 inline mr-2" />
                                                部門
                                            </label>
                                            <input
                                                type="text"
                                                name="department"
                                                value={profileForm.department}
                                                onChange={handleProfileInputChange}
                                                className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                                placeholder="請輸入部門"
                                                readOnly
                                            />
                                        </div>
                                    </div>

                                    <Button
                                        onClick={handleProfileUpdate}
                                        disabled={loading}
                                        className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-bold py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                                    >
                                        <Save className="w-4 h-4 mr-2" />
                                        {loading ? '更新中...' : '保存更改'}
                                    </Button>
                                </div>
                            )}
                        </div>
                    )}

                    {/* 密碼安全標籤 */}
                    {activeTab === 'password' && (
                        <div className="p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                    <Lock className="w-4 h-4 text-white" />
                                </div>
                                <h2 className="text-xl font-bold text-gray-800">密碼安全</h2>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        <Lock className="w-4 h-4 inline mr-2" />
                                        目前密碼
                                    </label>
                                    <div className="relative">
                                        <input
                                            type={showPassword ? "text" : "password"}
                                            name="currentPassword"
                                            value={passwordForm.currentPassword}
                                            onChange={handlePasswordInputChange}
                                            className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                            placeholder="請輸入目前密碼"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowPassword(!showPassword)}
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                                        >
                                            {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        <Key className="w-4 h-4 inline mr-2" />
                                        新密碼
                                    </label>
                                    <input
                                        type="password"
                                        name="newPassword"
                                        value={passwordForm.newPassword}
                                        onChange={handlePasswordInputChange}
                                        className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        placeholder="請輸入新密碼"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        <Key className="w-4 h-4 inline mr-2" />
                                        確認新密碼
                                    </label>
                                    <input
                                        type="password"
                                        name="confirmPassword"
                                        value={passwordForm.confirmPassword}
                                        onChange={handlePasswordInputChange}
                                        className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        placeholder="請再次輸入新密碼"
                                    />
                                </div>

                                <Button
                                    onClick={handlePasswordChange}
                                    disabled={loading}
                                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                                >
                                    <Lock className="w-4 h-4 mr-2" />
                                    {loading ? '修改中...' : '修改密碼'}
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* 通知設定標籤 */}
                    {activeTab === 'notifications' && (
                        <div className="p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                                    <Bell className="w-4 h-4 text-white" />
                                </div>
                                <h2 className="text-xl font-bold text-gray-800">通知設定</h2>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between p-4 bg-indigo-50 border border-indigo-200 rounded-2xl">
                                    <div className="flex items-center space-x-3">
                                        <Mail className="w-5 h-5 text-indigo-600" />
                                        <span className="font-medium text-gray-800">電子郵件通知</span>
                                    </div>
                                    <label className="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" className="sr-only peer" defaultChecked />
                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-500"></div>
                                    </label>
                                </div>

                                <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-2xl">
                                    <div className="flex items-center space-x-3">
                                        <Smartphone className="w-5 h-5 text-blue-600" />
                                        <span className="font-medium text-gray-800">推播通知</span>
                                    </div>
                                    <label className="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" className="sr-only peer" defaultChecked />
                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                                    </label>
                                </div>

                                <div className="flex items-center justify-between p-4 bg-purple-50 border border-purple-200 rounded-2xl">
                                    <div className="flex items-center space-x-3">
                                        <Bell className="w-5 h-5 text-purple-600" />
                                        <span className="font-medium text-gray-800">考勤提醒</span>
                                    </div>
                                    <label className="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" className="sr-only peer" defaultChecked />
                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                                    </label>
                                </div>
                            </div>

                            <Button
                                disabled={loading}
                                className="w-full mt-6 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-bold py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                保存設定
                            </Button>
                        </div>
                    )}
                </div>

                {/* 🔍 測試功能區域 */}
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-2xl">
                    <h3 className="text-lg font-bold text-gray-800 mb-4">🔧 診斷工具</h3>
                    <div className="grid grid-cols-1 gap-3">
                        <Button
                            onClick={testLocalStorage}
                            className="bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-xl"
                        >
                            測試 localStorage 功能
                        </Button>
                        <Button
                            onClick={checkAvatarStatus}
                            className="bg-green-500 hover:bg-green-600 text-white py-2 rounded-xl"
                        >
                            檢查頭像狀態
                        </Button>
                    </div>
                </div>

                {/* 🌈 底部彩虹條紋 */}
                <div className="mt-8 h-2 bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-blue-400 via-indigo-400 to-purple-400 rounded-full"></div>
            </div>
        </div>
    )
} 