"use client"

import { withAuth, useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { getPunchRecords, getPunchRecordDetail, getClockStatusTypes, getDepartments, getEmployees } from '@/lib/api-client'
import type { PunchRecord } from '@/lib/api-client'
import {
    Clock,
    Search,
    Download,
    Filter,
    ArrowLeft,
    Eye,
    Calendar,
    ChevronLeft,
    ChevronRight,
    Users,
    Building,
    Fingerprint,
    TrendingUp,
    RefreshCw,
    ChevronUp,
    ChevronDown
} from 'lucide-react'
import Link from 'next/link'

function PunchRecordsPage() {
    const { user } = useAuth()
    const [records, setRecords] = useState<PunchRecord[]>([])
    const [loading, setLoading] = useState(true)
    const [employees, setEmployees] = useState<any[]>([])
    const [departments, setDepartments] = useState<any[]>([])
    const [statusTypes, setStatusTypes] = useState<Record<string, any>>({})
    const [statistics, setStatistics] = useState<any>({})
    const [searchParams, setSearchParams] = useState({
        start_date: (() => {
            const date = new Date()
            date.setDate(1) // 預設查詢本月1號開始
            return date.toISOString().split('T')[0]
        })(),
        end_date: new Date().toISOString().split('T')[0],
        employee_id: '',
        department_id: '',
        status_code: ''
    })
    const [currentPage, setCurrentPage] = useState(1)
    const [pagination, setPagination] = useState<any>({})
    const [selectedRecord, setSelectedRecord] = useState<PunchRecord | null>(null)
    const [showDetailModal, setShowDetailModal] = useState(false)
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc') // 預設降序（最新在前）

    // 載入基礎數據
    useEffect(() => {
        loadEmployees()
        loadDepartments()
        loadStatusTypes()
    }, [])

    // 載入打卡記錄
    useEffect(() => {
        fetchRecords()
    }, [currentPage])

    const loadEmployees = async () => {
        try {
            const response = await getEmployees()
            if (response.success && response.data) {
                // 確保 employees 是陣列
                const employeeList = Array.isArray(response.data) ? response.data : []
                setEmployees(employeeList)
            }
        } catch (error) {
            console.error('載入員工列表失敗:', error)
            setEmployees([]) // 設定為空陣列作為降級處理
        }
    }

    const loadDepartments = async () => {
        try {
            const response = await getDepartments()
            if (response.success && response.data) {
                // 確保 departments 是陣列
                const departmentList = Array.isArray(response.data) ? response.data : []
                setDepartments(departmentList)
            }
        } catch (error) {
            console.error('載入部門列表失敗:', error)
            setDepartments([]) // 設定為空陣列作為降級處理
        }
    }

    const loadStatusTypes = async () => {
        try {
            const response = await getClockStatusTypes()
            if (response.success && response.data?.status_types) {
                setStatusTypes(response.data.status_types)
            }
        } catch (error) {
            console.error('載入打卡狀態類型失敗:', error)
            setStatusTypes({}) // 設定為空物件作為降級處理
        }
    }

    const fetchRecords = async () => {
        setLoading(true)
        try {
            const params = {
                page: currentPage,
                limit: 20,
                employee_id: searchParams.employee_id || undefined,
                department_id: searchParams.department_id || undefined,
                start_date: searchParams.start_date || undefined,
                end_date: searchParams.end_date || undefined,
                status_code: searchParams.status_code || undefined
            }

            console.log('打卡記錄查詢參數:', params)
            const response = await getPunchRecords(params)
            console.log('打卡記錄 API 響應:', response)

            if (response.success && response.data) {
                console.log('設定記錄數據:', response.data.records?.length || 0, '筆')
                setRecords(response.data.records || [])
                setPagination(response.data.pagination || {})
                setStatistics(response.data.statistics || {})
            } else {
                console.error('API 調用失敗:', response.error)
                setRecords([])
            }
        } catch (error) {
            console.error('載入打卡記錄失敗:', error)
            setRecords([])
        } finally {
            setLoading(false)
        }
    }

    const handleSearch = () => {
        setCurrentPage(1)
        fetchRecords()
    }

    const handleQuickDate = (type: string | number) => {
        const today = new Date()
        const endDate = new Date(today)
        const startDate = new Date(today)

        if (type === 0) {
            // 今天
            startDate.setDate(today.getDate())
        } else if (type === 1) {
            // 昨天
            startDate.setDate(today.getDate() - 1)
            endDate.setDate(today.getDate() - 1)
        } else if (type === 'thisMonth') {
            // 本月：從本月1號到本月最後一天
            startDate.setDate(1) // 設定為本月1號
            endDate.setMonth(today.getMonth() + 1, 0) // 設定為本月最後一天
        } else if (typeof type === 'number') {
            // 最近N天
            startDate.setDate(today.getDate() - type + 1)
        }

        setSearchParams({
            ...searchParams,
            start_date: startDate.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0]
        })
    }

    const showRecordDetail = async (recordId: number) => {
        try {
            const response = await getPunchRecordDetail(recordId)
            if (response.success && response.data?.record) {
                setSelectedRecord(response.data.record)
                setShowDetailModal(true)
            }
        } catch (error) {
            console.error('載入記錄詳情失敗:', error)
        }
    }

    const handleSort = () => {
        const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc'
        setSortOrder(newSortOrder)

        // 對當前記錄進行排序
        const sortedRecords = [...records].sort((a, b) => {
            const dateA = new Date(a.punch_datetime).getTime()
            const dateB = new Date(b.punch_datetime).getTime()

            if (newSortOrder === 'asc') {
                return dateA - dateB // 升序：由小到大（舊到新）
            } else {
                return dateB - dateA // 降序：由大到小（新到舊）
            }
        })

        setRecords(sortedRecords)
    }

    const getStatusText = (statusCode: string) => {
        return statusTypes[statusCode]?.name || statusCode
    }

    const getStatusColor = (statusCode: string) => {
        switch (statusCode) {
            case '0':
            case 'I':
                return 'text-success-600 bg-success-50'
            case '1':
            case 'O':
                return 'text-error-600 bg-error-50'
            case '2':
                return 'text-warning-600 bg-warning-50'
            case '3':
                return 'text-info-600 bg-info-50'
            default:
                return 'text-neutral-600 bg-neutral-50'
        }
    }

    const formatDateTime = (timestamp: string) => {
        if (!timestamp) return '-'
        try {
            return new Date(timestamp).toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            })
        } catch {
            return '-'
        }
    }

    const formatTime = (timestamp: string) => {
        if (!timestamp) return '-'
        try {
            return new Date(timestamp).toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        } catch {
            return '-'
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            {/* 📦 主容器 - 內容區域容器 */}
            <div className="p-6">
                {/* 🎨 頁面標題 - 統一的標題設計 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        {/* 📍 左側標題區 */}
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">打卡原始記錄查詢</h1>
                            <div className="flex items-center space-x-2">
                                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                    <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                    <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                                </Link>
                                <p className="text-indigo-100 text-base font-medium">查詢員工打卡原始記錄，包含設備資訊、狀態代碼等詳細資料</p>
                            </div>
                        </div>

                        {/* 📍 右側資訊區 */}
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">打卡記錄</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <Fingerprint className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>


                {/* 搜尋篩選區域 */}
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft mb-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-neutral-600" />
                        <h2 className="text-lg font-semibold text-neutral-900">篩選條件</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                        <div>
                            <label className="block text-sm font-medium text-neutral-700 mb-2">員工</label>
                            <select
                                value={searchParams.employee_id}
                                onChange={(e) => setSearchParams({ ...searchParams, employee_id: e.target.value })}
                                className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            >
                                <option value="">所有員工</option>
                                {employees.map((emp) => (
                                    <option key={emp.employee_id} value={emp.employee_id}>
                                        {emp.name} ({emp.employee_id})
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-neutral-700 mb-2">部門</label>
                            <select
                                value={searchParams.department_id}
                                onChange={(e) => setSearchParams({ ...searchParams, department_id: e.target.value })}
                                className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            >
                                <option value="">所有部門</option>
                                {departments.map((dept) => (
                                    <option key={dept.id} value={dept.id}>
                                        {dept.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-neutral-700 mb-2">開始日期</label>
                            <input
                                type="date"
                                value={searchParams.start_date}
                                onChange={(e) => setSearchParams({ ...searchParams, start_date: e.target.value })}
                                className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-neutral-700 mb-2">結束日期</label>
                            <input
                                type="date"
                                value={searchParams.end_date}
                                onChange={(e) => setSearchParams({ ...searchParams, end_date: e.target.value })}
                                className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                        </div>

                        <div className="flex items-end">
                            <Button
                                variant="primary"
                                icon={<Search className="w-4 h-4" />}
                                onClick={handleSearch}
                                className="w-full"
                            >
                                查詢
                            </Button>
                        </div>
                    </div>

                    {/* 快速選擇 */}
                    <div className="flex flex-wrap gap-2">
                        <button
                            onClick={() => handleQuickDate(0)}
                            className="px-3 py-1 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300"
                        >
                            今天
                        </button>
                        <button
                            onClick={() => handleQuickDate(1)}
                            className="px-3 py-1 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300"
                        >
                            昨天
                        </button>
                        <button
                            onClick={() => handleQuickDate(7)}
                            className="px-3 py-1 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300"
                        >
                            最近7天
                        </button>
                        <button
                            onClick={() => handleQuickDate('thisMonth')}
                            className="px-3 py-1 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300"
                        >
                            本月
                        </button>
                    </div>
                </div>

                {/* 打卡記錄表格 */}
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20 shadow-soft overflow-hidden">
                    <div className="px-6 py-4 border-b border-neutral-200 bg-gradient-to-r from-indigo-100 to-purple-100">
                        <div className="flex items-center justify-between">
                            <h2 className="text-lg font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                打卡原始記錄
                            </h2>
                            <div className="flex items-center space-x-3">
                                <button
                                    onClick={handleSort}
                                    className="md:hidden flex items-center space-x-1 bg-white text-indigo-700 text-sm px-3 py-1 rounded-full font-medium shadow-sm hover:bg-indigo-50 transition-colors duration-200"
                                >
                                    <span>時間排序</span>
                                    {sortOrder === 'desc' ? (
                                        <ChevronDown className="w-4 h-4" />
                                    ) : (
                                        <ChevronUp className="w-4 h-4" />
                                    )}
                                </button>
                                <span className="bg-white text-indigo-700 text-sm px-3 py-1 rounded-full font-medium shadow-sm">
                                    {pagination.total || 0} 筆記錄
                                </span>
                            </div>
                        </div>
                    </div>

                    {loading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                            <span className="ml-2 text-neutral-600">載入中...</span>
                        </div>
                    ) : records.length > 0 ? (
                        <>
                            {/* 桌面版表格 */}
                            <div className="hidden md:block overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
                                                <button
                                                    onClick={handleSort}
                                                    className="flex items-center space-x-2 hover:text-yellow-200 transition-colors duration-200"
                                                >
                                                    <span>打卡時間</span>
                                                    {sortOrder === 'desc' ? (
                                                        <ChevronDown className="w-4 h-4" />
                                                    ) : (
                                                        <ChevronUp className="w-4 h-4" />
                                                    )}
                                                </button>
                                            </th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
                                                員工
                                            </th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
                                                部門
                                            </th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
                                                打卡狀態
                                            </th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
                                                設備編號
                                            </th>
                                            <th className="px-6 py-4 text-right text-sm font-bold uppercase tracking-wider">
                                                操作
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-neutral-200">
                                        {records.map((record) => (
                                            <tr key={record.id} className="hover:bg-neutral-50 transition-colors">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-neutral-900">
                                                        {formatDateTime(record.punch_datetime)}
                                                    </div>
                                                    <div className="text-sm text-neutral-500">
                                                        {formatTime(record.punch_datetime)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-neutral-900">
                                                        {record.employee_name || record.employee_id}
                                                    </div>
                                                    <div className="text-sm text-neutral-500">
                                                        {record.employee_code || record.employee_id}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
                                                    {record.department_name || '-'}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(record.status_code)}`}>
                                                        {getStatusText(record.status_code)}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                                                    {record.device_id ? (
                                                        <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">
                                                            {record.device_id}
                                                        </span>
                                                    ) : (
                                                        '-'
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        icon={<Eye className="w-4 h-4" />}
                                                        onClick={() => showRecordDetail(record.id)}
                                                    >
                                                        查看
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* 手機版卡片 */}
                            <div className="md:hidden space-y-4 p-4">
                                {records.map((record) => (
                                    <div key={record.id} className="bg-white rounded-xl p-4 border border-neutral-200">
                                        <div className="flex items-center justify-between mb-3">
                                            <div className="flex items-center space-x-2">
                                                <Clock className="w-4 h-4 text-neutral-500" />
                                                <span className="font-medium text-neutral-900">
                                                    {formatDateTime(record.punch_datetime)}
                                                </span>
                                            </div>
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status_code)}`}>
                                                {getStatusText(record.status_code)}
                                            </span>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                                            <div>
                                                <span className="text-neutral-500">員工:</span>
                                                <span className="ml-2 text-neutral-900">
                                                    {record.employee_name || record.employee_id}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-neutral-500">部門:</span>
                                                <span className="ml-2 text-neutral-900">
                                                    {record.department_name || '-'}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-neutral-500">時間:</span>
                                                <span className="ml-2 text-neutral-900">
                                                    {formatTime(record.punch_datetime)}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-neutral-500">設備:</span>
                                                <span className="ml-2 text-neutral-900">
                                                    {record.device_id || '-'}
                                                </span>
                                            </div>
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            icon={<Eye className="w-4 h-4" />}
                                            onClick={() => showRecordDetail(record.id)}
                                            className="w-full"
                                        >
                                            查看詳情
                                        </Button>
                                    </div>
                                ))}
                            </div>

                            {/* 分頁控制 */}
                            {pagination.total_pages > 1 && (
                                <div className="px-6 py-4 border-t border-neutral-200 flex items-center justify-between">
                                    <div className="text-sm text-neutral-700">
                                        第 {pagination.page} 頁，共 {pagination.total_pages} 頁，總共 {pagination.total} 筆記錄
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            icon={<ChevronLeft className="w-4 h-4" />}
                                            disabled={!pagination.has_prev}
                                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        >
                                            上一頁
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            icon={<ChevronRight className="w-4 h-4" />}
                                            disabled={!pagination.has_next}
                                            onClick={() => setCurrentPage(prev => prev + 1)}
                                        >
                                            下一頁
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-12">
                            <Clock className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                            <p className="text-neutral-600">暫無打卡原始記錄</p>
                            <p className="text-sm text-neutral-400 mt-1">調整查詢條件或選擇其他時間範圍</p>
                        </div>
                    )}
                </div>
            </div>

            {/* 詳情模態框 */}
            {showDetailModal && selectedRecord && (
                <div className="fixed inset-0 bg-black bg-opacity-60 z-50 backdrop-blur-sm">
                    <div className="flex items-center justify-center min-h-screen p-4">
                        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                            <div className="bg-gradient-to-r from-brand-500 to-brand-600 px-6 py-4 text-white rounded-t-2xl">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="text-lg font-bold">打卡詳情</h3>
                                        <p className="text-brand-100 text-sm">詳細打卡資訊</p>
                                    </div>
                                    <button
                                        onClick={() => setShowDetailModal(false)}
                                        className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-lg transition-all duration-200"
                                    >
                                        ×
                                    </button>
                                </div>
                            </div>

                            <div className="p-6">
                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">員工資訊</label>
                                            <p className="text-neutral-900">
                                                {selectedRecord.employee_name || selectedRecord.employee_id} ({selectedRecord.employee_code || selectedRecord.employee_id})
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">部門</label>
                                            <p className="text-neutral-900">{selectedRecord.department_name || '-'}</p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">打卡時間</label>
                                            <p className="text-neutral-900">{formatDateTime(selectedRecord.punch_datetime)}</p>
                                            <p className="text-sm text-neutral-500">{formatTime(selectedRecord.punch_datetime)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">打卡狀態</label>
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedRecord.status_code)}`}>
                                                {getStatusText(selectedRecord.status_code)}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">設備編號</label>
                                            <p className="text-neutral-900">{selectedRecord.device_id || '-'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">處理狀態</label>
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${selectedRecord.processed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                                {selectedRecord.processed ? '已處理' : '未處理'}
                                            </span>
                                        </div>
                                    </div>

                                    {selectedRecord.raw_data && (
                                        <div>
                                            <label className="block text-sm font-medium text-neutral-500 mb-1">原始資料</label>
                                            <p className="text-neutral-900 text-sm bg-neutral-50 p-3 rounded">
                                                {selectedRecord.raw_data}
                                            </p>
                                        </div>
                                    )}

                                    <div>
                                        <label className="block text-sm font-medium text-neutral-500 mb-1">匯入時間</label>
                                        <p className="text-neutral-900">{formatDateTime(selectedRecord.imported_at)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default PunchRecordsPage