"""
Flask 應用程式主模組。

此模組負責：
- 設定 Flask 應用程式
- 定義所有 API 路由
- 處理請求和響應
"""

import csv
import io
import json
import logging
import os
import time

import sqlite3
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, send_file, send_from_directory, render_template, redirect, url_for
from flask_cors import CORS

from config import Config
from database import create_connection, init_db
from services.health_monitor import health_monitor
from services.attendance_processor import attendance_processor

# 配置日誌
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.FileHandler(Config.LOG_FILE), logging.StreamHandler()],
)

# 創建logger實例
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置應用程式
app.config["SECRET_KEY"] = Config.SECRET_KEY
app.config["MAX_CONTENT_LENGTH"] = Config.MAX_CONTENT_LENGTH
app.config["UPLOAD_FOLDER"] = Config.UPLOAD_FOLDER

# 確保上傳目錄存在
os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)


@app.route("/")
def index():
    """主頁面 - 重定向到Elite版"""
    return redirect(url_for('elite_dashboard'))


@app.route("/modern")
def modern_index():
    """現代化主頁面 - 全新設計的智慧考勤系統"""
    return render_template("modern-index.html")


@app.route("/professional")
def professional_dashboard():
    """專業版儀表板 - 參考醫療儀表板設計風格"""
    return render_template("professional-dashboard.html")


@app.route("/elite")
def elite_dashboard():
    """Elite版儀表板 - 國際級企業設計標準"""
    return render_template("elite-dashboard.html")


@app.route("/elite/attendance")
def elite_attendance():
    """Elite版考勤打卡頁面"""
    return render_template("elite-attendance.html")


@app.route("/elite/schedule")
def elite_schedule():
    """Elite版排班系統頁面"""
    return render_template("elite-schedule.html")


@app.route("/elite/leaves")
def elite_leaves():
    """Elite版請假管理頁面"""
    return render_template("elite-leaves.html")


@app.route("/elite/employees")
def elite_employees():
    """Elite版員工管理頁面"""
    return render_template("elite-employees.html")


@app.route("/elite/analytics")
def elite_analytics():
    """Elite版數據分析頁面"""
    return render_template("elite-analytics.html")


@app.route("/elite/settings")
def elite_settings():
    """Elite版系統設定頁面"""
    return render_template("elite-settings.html")


@app.route("/elite/approval")
def elite_approval():
    """Elite版審核作業頁面"""
    return render_template("elite-approval.html")


@app.route("/elite/masterdata")
def elite_masterdata():
    """Elite版基本資料管理頁面"""
    return render_template("elite-masterdata.html")


@app.route("/mobile")
def mobile_dashboard():
    """移動端優化版儀表板 - 原生App體驗"""
    return render_template("mobile-dashboard.html")


@app.route("/leaves")
def leaves():
    """請假管理頁面"""
    return render_template("leaves.html")


@app.route("/monitor")
def system_monitor():
    """系統監控頁面"""
    return render_template("system-monitor.html")


@app.route("/elite/shifts")
def elite_shifts():
    """Elite 版本班別管理頁面"""
    return render_template("elite-shifts.html")


@app.route("/elite/features")
def elite_features():
    """Elite 版本功能總覽頁面"""
    return render_template("elite-features.html")

@app.route("/elite/attendance-records")
def elite_attendance_records():
    """打卡紀錄查詢頁面"""
    return render_template("elite-attendance-records.html")

@app.route("/elite/attendance-management")
def elite_attendance_management():
    """考勤作業管理頁面"""
    return render_template("elite-attendance-management.html")

@app.route("/elite/attendance-processing")
def elite_attendance_processing():
    """考勤整理頁面"""
    return render_template("elite-attendance-processing.html")

@app.route("/elite/import-attendance")
def elite_import_attendance():
    """匯入文字檔頁面"""
    return render_template("elite-import-attendance.html")

@app.route("/test/approval-display")
def test_approval_display():
    """審核資料顯示測試頁面"""
    return send_from_directory('.', 'test_approval_display.html')


# ====== 登入與認證模組 ======
@app.route("/api/login", methods=["POST"])
def login():
    """使用者登入"""
    data = request.json
    if not data or "username" not in data or "password" not in data:
        return jsonify({"error": "缺少必要參數"}), 400

    # 在實際應用中，需要實現密碼雜湊和驗證
    # 這裡只是簡單示範
    if data["username"] == "admin" and data["password"] == "admin123":
        return jsonify(
            {"success": True, "user_id": 1, "name": "系統管理員", "role": "admin"}
        )

    return jsonify({"error": "帳號或密碼錯誤"}), 401


@app.route("/api/dashboard/stats", methods=["GET"])
def dashboard_stats():
    """獲取儀表板統計數據"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        # 獲取今日考勤統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute(
            """
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent,
                SUM(CASE WHEN status = 'manual' THEN 1 ELSE 0 END) as manual
            FROM attendance
            WHERE DATE(check_in) = ?
        """,
            (today,),
        )

        attendance_stats = dict(
            zip(
                ["total", "normal", "late", "early_leave", "absent", "manual"],
                cursor.fetchone(),
            )
        )

        # 獲取待審批請假數量
        cursor.execute(
            """
            SELECT COUNT(*) FROM leaves WHERE status = 'pending'
        """
        )
        result = cursor.fetchone()
        pending_leaves = result[0] if result else 0

        # 獲取部門統計
        cursor.execute(
            """
            SELECT d.name, COUNT(e.id)
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            GROUP BY d.id
        """
        )
        departments = [{"name": row[0], "count": row[1]} for row in cursor.fetchall()]

        # 獲取最近的考勤記錄
        cursor.execute(
            """
            SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            ORDER BY a.check_in DESC
            LIMIT 5
        """
        )
        recent_records = [
            {
                "id": row[0],
                "name": row[1],
                "employee_id": row[2],
                "check_in": row[3],
                "check_out": row[4],
                "status": row[5],
            }
            for row in cursor.fetchall()
        ]

        return jsonify(
            {
                "attendance": attendance_stats,
                "pending_leaves": pending_leaves,
                "departments": departments,
                "recent_records": recent_records,
            }
        )
    except sqlite3.Error as e:
        logging.error(f"儀表板統計數據查詢錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/dashboard/reports", methods=["GET"])
def dashboard_reports():
    """獲取儀表板報表數據"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取今日統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkins,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM attendance
            WHERE DATE(check_in) = ?
        """, (today,))
        
        today_stats = cursor.fetchone()
        
        # 獲取本週統計
        week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT COUNT(*) as week_total
            FROM attendance
            WHERE DATE(check_in) >= ?
        """, (week_start,))
        
        week_stats = cursor.fetchone()
        
        # 獲取待審批請假
        cursor.execute("""
            SELECT COUNT(*) as pending_leaves
            FROM leaves
            WHERE status = 'pending'
        """)
        
        leave_stats = cursor.fetchone()
        
        # 獲取部門考勤統計
        cursor.execute("""
            SELECT d.name, 
                   COUNT(a.id) as total_attendance,
                   SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            LEFT JOIN attendance a ON e.id = a.employee_id AND DATE(a.check_in) = ?
            GROUP BY d.id, d.name
            ORDER BY d.name
        """, (today,))
        
        department_stats = []
        for row in cursor.fetchall():
            department_stats.append({
                "department": row[0],
                "total_attendance": row[1] or 0,
                "normal_count": row[2] or 0,
                "attendance_rate": round((row[2] or 0) / max(row[1] or 1, 1) * 100, 1)
            })
        
        # 獲取最近7天的考勤趨勢
        cursor.execute("""
            SELECT DATE(check_in) as date,
                   COUNT(*) as total,
                   SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                   SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
            FROM attendance
            WHERE DATE(check_in) >= DATE('now', '-7 days')
            GROUP BY DATE(check_in)
            ORDER BY DATE(check_in)
        """)
        
        trend_data = []
        for row in cursor.fetchall():
            trend_data.append({
                "date": row[0],
                "total": row[1],
                "normal": row[2],
                "late": row[3],
                "on_time_rate": round((row[2] / max(row[1], 1)) * 100, 1)
            })
        
        conn.close()
        
        return jsonify({
            "today": {
                "total_checkins": today_stats[0] if today_stats else 0,
                "normal_count": today_stats[1] if today_stats else 0,
                "late_count": today_stats[2] if today_stats else 0,
                "early_leave_count": today_stats[3] if today_stats else 0,
                "absent_count": today_stats[4] if today_stats else 0,
                "on_time_rate": round((today_stats[1] / max(today_stats[0], 1)) * 100, 1) if today_stats and today_stats[0] > 0 else 0
            },
            "week": {
                "total_checkins": week_stats[0] if week_stats else 0
            },
            "leaves": {
                "pending_count": leave_stats[0] if leave_stats else 0
            },
            "departments": department_stats,
            "trends": trend_data,
            "summary": {
                "total_employees": len(department_stats),
                "active_departments": len([d for d in department_stats if d["total_attendance"] > 0]),
                "average_attendance_rate": round(sum(d["attendance_rate"] for d in department_stats) / max(len(department_stats), 1), 1)
            }
        })
        
    except Exception as e:
        logging.error(f"獲取儀表板報表失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 資料採集模組 ======
@app.route("/api/attendance/import", methods=["POST"])
def import_attendance():
    """匯入考勤資料（CSV 檔案）"""
    if "file" not in request.files:
        return jsonify({"error": "未找到檔案"}), 400
    
    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "未選擇檔案"}), 400

    try:
        # 讀取 CSV 檔案
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_data = csv.DictReader(stream)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        imported_count = 0
        for row in csv_data:
            cursor.execute(
                """
                INSERT INTO attendance (employee_id, check_in, status)
                VALUES (?, ?, ?)
            """,
                (row["employee_id"], row["clock_time"], row["status"]),
            )
            imported_count += 1
        
        conn.commit()
        conn.close()
        logging.info(f"成功匯入 {imported_count} 筆考勤記錄")
        
        return jsonify({"message": f"成功匯入 {imported_count} 筆記錄"})
    except Exception as e:
        logging.error(f"匯入考勤資料失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/manual", methods=["POST"])
def manual_attendance():
    """手動新增考勤記錄"""
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少必要參數"}), 400
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            """
            INSERT INTO attendance (employee_id, check_in, check_out, status, note)
            VALUES (?, ?, ?, ?, ?)
        """,
            (
                data["employee_id"],
                data.get("check_in"),
                data.get("check_out"),
                data.get("status", "manual"),
                data.get("note", "")
            )
        )
        
        conn.commit()
        attendance_id = cursor.lastrowid
        
        logging.info(f"手動新增考勤記錄: 員工ID {data['employee_id']}, 記錄ID {attendance_id}")
        
        return jsonify({
            "message": "考勤記錄新增成功",
            "attendance_id": attendance_id
        })
        
    except sqlite3.Error as e:
        logging.error(f"手動新增考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/today/<int:employee_id>", methods=["GET"])
def get_today_attendance(employee_id):
    """獲取員工今日考勤記錄"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 獲取員工資訊
        cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({"error": "員工不存在"}), 404
        
        # 獲取今日考勤記錄
        cursor.execute(
            """
            SELECT id, check_in, check_out, status, note
            FROM attendance
            WHERE employee_id = ? AND DATE(check_in) = ?
            ORDER BY check_in DESC
            LIMIT 1
        """,
            (employee_id, today)
        )
        
        attendance = cursor.fetchone()
        
        result = {
            "employee_id": employee[1],
            "employee_name": employee[0],
            "date": today,
            "has_attendance": attendance is not None
        }
        
        if attendance:
            result.update({
                "attendance_id": attendance[0],
                "check_in": attendance[1],
                "check_out": attendance[2],
                "status": attendance[3],
                "note": attendance[4],
                "has_checked_in": attendance[1] is not None,
                "has_checked_out": attendance[2] is not None
            })
        else:
            result.update({
                "attendance_id": None,
                "check_in": None,
                "check_out": None,
                "status": None,
                "note": None,
                "has_checked_in": False,
                "has_checked_out": False
            })
        
        return jsonify(result)
        
    except sqlite3.Error as e:
        logging.error(f"獲取今日考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/clock-in", methods=["POST"])
def clock_in():
    """
    智能打卡API。
    
    支援跨日考勤邏輯，自動判斷上班/下班，處理換日時間。
    """
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少員工ID"}), 400
    
    try:
        employee_id = data["employee_id"]
        punch_datetime = datetime.now()
        device_id = data.get("device_id")
        note = data.get("note")
        
        # 使用考勤處理器處理打卡
        result = attendance_processor.process_punch_record(
            employee_id=employee_id,
            punch_datetime=punch_datetime,
            device_id=device_id,
            note=note
        )
        
        if result['success']:
            # 獲取員工資訊
            conn = create_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
            employee = cursor.fetchone()
            conn.close()
            
            employee_name = employee[0] if employee else "未知員工"
            
            logging.info(f"員工 {employee_name} (ID: {employee_id}) 打卡成功 - {result['punch_type']}")
            
            return jsonify({
                "message": result.get('message', '打卡成功'),
                "employee_name": employee_name,
                "punch_type": result['punch_type'],
                "work_date": result['work_date'],
                "punch_time": result['punch_time'],
                "attendance_id": result.get('attendance_id'),
                **{k: v for k, v in result.items() if k not in ['success', 'message', 'punch_type', 'work_date', 'punch_time', 'attendance_id']}
            })
        else:
            return jsonify({"error": result.get('error', '打卡失敗')}), 500
            
    except Exception as e:
        logging.error(f"打卡API錯誤: {str(e)}")
        return jsonify({"error": "系統錯誤，請稍後再試"}), 500


@app.route("/api/attendance/clock-out", methods=["POST"])
def clock_out():
    """
    下班打卡API（已整合到智能打卡中）。
    
    此API保留向後相容性，實際使用智能打卡邏輯。
    """
    # 直接調用智能打卡API
    return clock_in()


@app.route("/api/attendance/recent", methods=["GET"])
def get_recent_attendance():
    """獲取最近的考勤記錄"""
    try:
        limit = request.args.get('limit', 5, type=int)
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status,
                   d.name as department_name
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            ORDER BY a.check_in DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            records.append({
                "id": row[0],
                "name": row[1],
                "employee_id": row[2],
                "check_in": row[3],
                "check_out": row[4],
                "status": row[5],
                "department_name": row[6]
            })
        
        conn.close()
        return jsonify({"recent_attendance": records})
        
    except Exception as e:
        logging.error(f"獲取最近考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/daily-summary/<int:employee_id>", methods=["GET"])
def get_daily_attendance_summary(employee_id):
    """
    獲取員工指定日期的考勤摘要。
    
    支援跨日考勤邏輯，根據換日時間計算工作日期。
    """
    try:
        # 獲取查詢日期，預設為今天
        date_str = request.args.get('date')
        if date_str:
            work_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            # 根據當前時間和換日設定確定工作日期
            work_date = attendance_processor.get_work_date(datetime.now())
        
        # 獲取考勤摘要
        summary = attendance_processor.get_daily_summary(employee_id, work_date)
        
        return jsonify(summary)
        
    except Exception as e:
        logging.error(f"獲取考勤摘要失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/settings", methods=["GET", "POST"])
def manage_attendance_settings():
    """
    管理考勤設定。
    
    包括換日時間、跨日考勤開關等設定。
    """
    if request.method == "GET":
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 獲取考勤相關設定
            cursor.execute("""
                SELECT rule_type, rule_value, description 
                FROM schedule_rules 
                WHERE rule_type IN (
                    'day_change_time', 
                    'cross_day_attendance', 
                    'first_punch_as_checkin', 
                    'last_punch_as_checkout',
                    'late_tolerance_minutes',
                    'early_leave_tolerance_minutes'
                )
                ORDER BY rule_type
            """)
            
            settings = {}
            for row in cursor.fetchall():
                settings[row[0]] = {
                    'value': row[1],
                    'description': row[2]
                }
            
            conn.close()
            return jsonify({"settings": settings})
            
        except Exception as e:
            logging.error(f"獲取考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500
    
    else:  # POST
        try:
            data = request.json
            if not data:
                return jsonify({"error": "缺少設定資料"}), 400
            
            conn = create_connection()
            cursor = conn.cursor()
            
            # 更新設定
            for rule_type, rule_value in data.items():
                cursor.execute("""
                    UPDATE schedule_rules 
                    SET rule_value = ? 
                    WHERE rule_type = ?
                """, (str(rule_value), rule_type))
                
                if cursor.rowcount == 0:
                    # 如果設定不存在，則插入新設定
                    cursor.execute("""
                        INSERT INTO schedule_rules (rule_type, rule_value, description)
                        VALUES (?, ?, ?)
                    """, (rule_type, str(rule_value), f"考勤設定: {rule_type}"))
            
            conn.commit()
            conn.close()
            
            # 重新載入考勤處理器設定
            attendance_processor._load_settings()
            
            logging.info(f"考勤設定更新成功: {data}")
            return jsonify({"message": "考勤設定更新成功"})
            
        except Exception as e:
            logging.error(f"更新考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/work-date", methods=["GET"])
def get_work_date():
    """
    根據當前時間和換日設定，獲取對應的工作日期。
    """
    try:
        # 獲取查詢時間，預設為當前時間
        time_str = request.args.get('time')
        if time_str:
            query_time = datetime.fromisoformat(time_str)
        else:
            query_time = datetime.now()
        
        work_date = attendance_processor.get_work_date(query_time)
        
        return jsonify({
            "query_time": query_time.isoformat(),
            "work_date": work_date.isoformat(),
            "day_change_time": attendance_processor.day_change_time.strftime('%H:%M'),
            "cross_day_enabled": attendance_processor.cross_day_enabled
        })
        
    except Exception as e:
        logging.error(f"獲取工作日期失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 排班管理模組 ======
@app.route("/api/schedules/batch", methods=["POST"])
def batch_schedule():
    """批次設定班表"""
    data = request.json
    if not isinstance(data, list):
        return jsonify({"error": "資料格式錯誤"}), 400

    conn = create_connection()
    try:
        cursor = conn.cursor()
        for schedule in data:
            cursor.execute(
                """
                INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
                VALUES (?, ?, ?, ?, ?)
            """,
                (
                    schedule["employee_id"],
                    schedule["shift_date"],
                    schedule["shift_type"],
                    schedule["start_time"],
                    schedule["end_time"],
                ),
            )
        
        conn.commit()
        logging.info(f"成功批次設定 {len(data)} 筆班表資料")
        return jsonify({"message": "班表設定成功"})
    except sqlite3.Error as e:
        logging.error(f"批次設定班表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/schedules/rules", methods=["GET", "POST"])
def manage_schedule_rules():
    """管理排班規則"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("SELECT * FROM schedule_rules")
            columns = [col[0] for col in cursor.description]
            rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(rules)
        else:
            data = request.json
            cursor.execute(
                """
                INSERT INTO schedule_rules (rule_type, rule_value, description)
                VALUES (?, ?, ?)
            """,
                (data["rule_type"], data["rule_value"], data["description"]),
            )
            
            conn.commit()
            logging.info(f"成功設定排班規則 {data['rule_type']}")
            return jsonify({"message": "規則設定成功"})
    except sqlite3.Error as e:
        logging.error(f"管理排班規則錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 出勤分析模組 ======
@app.route("/api/attendance/analysis", methods=["GET"])
def analyze_attendance():
    """出勤分析報表"""
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    department = request.args.get("department")

    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 基本查詢條件
        query_conditions = []
        params = []
        
        if start_date and end_date:
            query_conditions.append("DATE(a.check_in) BETWEEN ? AND ?")
            params.extend([start_date, end_date])
        
        if department:
            query_conditions.append("e.department_id = ?")
            params.append(department)
        
        where_clause = " AND ".join(query_conditions)
        where_clause = f"WHERE {where_clause}" if where_clause else ""
        
        # 查詢出勤統計
        cursor.execute(
            f"""
            SELECT 
                d.name as department,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN departments d ON e.department_id = d.id
            {where_clause}
            GROUP BY d.id
        """,
            params,
        )

        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # 如果是按日期範圍查詢，添加每日趨勢數據
        if start_date and end_date:
            cursor.execute(
                f"""
                SELECT 
                    DATE(a.check_in) as date,
                    COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            {where_clause}
                GROUP BY DATE(a.check_in)
                ORDER BY DATE(a.check_in)
            """,
                params,
            )

            columns = [col[0] for col in cursor.description]
            daily_trends = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return jsonify({"summary": results, "daily_trends": daily_trends})

        return jsonify(results)
    except sqlite3.Error as e:
        logging.error(f"出勤分析錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/reports/export", methods=["GET"])
def export_report():
    """匯出報表"""
    report_type = request.args.get("type", "attendance")
    format_type = request.args.get("format", "csv")
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    department = request.args.get("department")
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 構建基本查詢條件
        query_conditions = []
        params = []

        if start_date and end_date:
            query_conditions.append("DATE(a.check_in) BETWEEN ? AND ?")
            params.extend([start_date, end_date])

        if department:
            query_conditions.append("e.department_id = ?")
            params.append(department)

        where_clause = " AND ".join(query_conditions)
        where_clause = f"WHERE {where_clause}" if where_clause else ""

        if report_type == "attendance":
            cursor.execute(
                f"""
                SELECT e.name, d.name as department, a.check_in, a.check_out, a.status, a.note
                FROM attendance a
                JOIN employees e ON a.employee_id = e.id
                JOIN departments d ON e.department_id = d.id
                {where_clause}
                ORDER BY a.check_in DESC
            """,
                params,
            )
            
            if format_type == "csv":
                output = io.StringIO()
                writer = csv.writer(output)
                writer.writerow(
                    ["姓名", "部門", "簽到時間", "簽退時間", "狀態", "備註"]
                )
                writer.writerows(cursor.fetchall())
                
                return send_file(
                    io.BytesIO(output.getvalue().encode("utf-8-sig")),
                    mimetype="text/csv",
                    as_attachment=True,
                    download_name=f'attendance_report_{datetime.now().strftime("%Y%m%d")}.csv',
                )
            
        return jsonify({"error": "不支援的報表類型或格式"}), 400
    except sqlite3.Error as e:
        logging.error(f"匯出報表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 員工管理模組 ======
@app.route("/api/employees", methods=["GET"])
def get_employees():
    """
    獲取員工列表，支援多條件查詢
    
    查詢參數：
    - department_id: 部門ID篩選
    - employee_id: 員工編號搜尋（支援模糊搜尋）
    - name: 員工姓名搜尋（支援模糊搜尋）
    - position: 職位搜尋（支援模糊搜尋）
    
    返回：
    - 員工列表，包含部門名稱、角色名稱、主管姓名等完整資訊
    """
    try:
        # 獲取查詢參數
        department_id = request.args.get('department_id')
        employee_id = request.args.get('employee_id')
        name = request.args.get('name')
        position = request.args.get('position')
        
        # 構建基本查詢
        query = """
            SELECT e.id, e.name, e.employee_id, e.department_id, e.position, 
                   e.email, e.phone, e.role_id, e.manager_id, e.hire_date, 
                   e.status, e.salary_level, e.id_number, e.address, 
                   e.emergency_contact, e.emergency_phone, e.photo_url, e.shift_type,
                   d.name as department_name,
                   r.role_name as role_name,
                   m.name as manager_name,
                   s.name as shift_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions r ON e.role_id = r.id
            LEFT JOIN employees m ON e.manager_id = m.id
            LEFT JOIN shifts s ON e.shift_type = s.id
            WHERE 1=1
        """
        
        params = []
        
        # 添加查詢條件
        if department_id:
            query += " AND e.department_id = ?"
            params.append(department_id)
            
        if employee_id:
            query += " AND e.employee_id LIKE ?"
            params.append(f"%{employee_id}%")
            
        if name:
            query += " AND e.name LIKE ?"
            params.append(f"%{name}%")
            
        if position:
            query += " AND e.position LIKE ?"
            params.append(f"%{position}%")
        
        query += " ORDER BY e.id"
        
        conn = create_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        employees = []
        for row in cursor.fetchall():
            employee = {
                'id': row[0],
                'name': row[1],
                'employee_id': row[2],
                'department_id': row[3],
                'position': row[4],
                'email': row[5],
                'phone': row[6],
                'role_id': row[7],
                'manager_id': row[8],
                'hire_date': row[9],
                'status': row[10],
                'salary_level': row[11],
                'id_number': row[12],
                'address': row[13],
                'emergency_contact': row[14],
                'emergency_phone': row[15],
                'photo_url': row[16],
                'shift_type': row[17],
                'department_name': row[18],
                'role_name': row[19],
                'manager_name': row[20],
                'shift_name': row[21]
            }
            employees.append(employee)
        
        conn.close()
        return jsonify({'employees': employees})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route("/api/employees", methods=["POST"])
def create_employee():
    """
    新增員工
    
    請求體：
    - name: 員工姓名（必填）
    - employee_id: 員工編號（必填）
    - department_id: 部門ID（必填）
    - position: 職位（必填）
    - email: 電子郵件（必填）
    - phone: 電話號碼
    - role_id: 角色ID
    - manager_id: 主管ID
    - password: 密碼
    - hire_date: 到職日期
    - status: 員工狀態
    - salary_level: 薪資等級
    - id_number: 身分證號
    - address: 地址
    - emergency_contact: 緊急聯絡人
    - emergency_phone: 緊急聯絡電話
    - photo_url: 照片連結
    
    返回：
    - 新增的員工資訊
    """
    conn = None
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['name', 'employee_id', 'department_id', 'position', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填欄位: {field}'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 驗證外鍵約束
        if data.get('department_id'):
            cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的部門不存在'}), 400
        
        if data.get('role_id'):
            cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的角色不存在'}), 400
        
        if data.get('manager_id'):
            cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的主管不存在'}), 400
        
        # 檢查員工編號是否已存在
        cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
        if cursor.fetchone():
            return jsonify({'error': '員工編號已存在'}), 400
        
        # 插入新員工
        cursor.execute("""
            INSERT INTO employees (name, employee_id, department_id, position, email, phone, 
                                 role_id, manager_id, password, hire_date, status, salary_level, 
                                 id_number, address, emergency_contact, emergency_phone, photo_url, shift_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['name'],
            data['employee_id'],
            data['department_id'],
            data['position'],
            data['email'],
            data.get('phone', ''),
            data.get('role_id', 3),  # 預設為一般員工
            data.get('manager_id'),
            data.get('password', 'default123'),
            data.get('hire_date', datetime.now().strftime('%Y-%m-%d')),
            data.get('status', 'active'),
            data.get('salary_level', 'junior'),
            data.get('id_number', ''),
            data.get('address', ''),
            data.get('emergency_contact', ''),
            data.get('emergency_phone', ''),
            data.get('photo_url', ''),
            data.get('shift_type', 1)  # 預設為第一個班表
        ))
        
        employee_id = cursor.lastrowid
        conn.commit()
        
        return jsonify({
            'message': '員工新增成功',
            'employee_id': employee_id
        }), 201
        
    except sqlite3.IntegrityError as e:
        error_msg = str(e)
        if "FOREIGN KEY constraint failed" in error_msg:
            return jsonify({'error': '資料關聯錯誤，請檢查部門、角色或主管設定'}), 400
        elif "UNIQUE constraint failed" in error_msg:
            return jsonify({'error': '員工編號已存在，請使用其他編號'}), 400
        else:
            return jsonify({'error': f'資料完整性錯誤: {error_msg}'}), 400
    except Exception as e:
        logging.error(f"新增員工錯誤: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route("/api/employees/<int:employee_id>", methods=["GET", "PUT", "DELETE"])
def manage_employee(employee_id):
    """管理單一員工資料"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 取得特定員工資料
            cursor.execute(
                """
                SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, 
                e.position, e.email, e.phone, e.password, e.hire_date, e.status,
                e.salary_level, e.id_number, e.address, e.emergency_contact, 
                e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                LEFT JOIN permissions p ON e.role_id = p.id
                LEFT JOIN employees m ON e.manager_id = m.id
                WHERE e.id = ?
            """,
                (employee_id,),
            )

            row = cursor.fetchone()
            if not row:
                return jsonify({"error": "找不到員工資料"}), 404

            columns = [col[0] for col in cursor.description]
            employee = dict(zip(columns, row))
            
            # 確保所有欄位都存在，即使是NULL值
            expected_fields = [
                'id', 'name', 'employee_id', 'department_id', 'department_name',
                'position', 'email', 'phone', 'password', 'hire_date', 'status',
                'salary_level', 'id_number', 'address', 'emergency_contact',
                'emergency_phone', 'role_id', 'role_name', 'manager_id', 'manager_name'
            ]
            
            for field in expected_fields:
                if field not in employee:
                    employee[field] = None
            
            return jsonify(employee)

        elif request.method == "PUT":
            # 更新員工資料
            data = request.json
            
            # 驗證外鍵約束
            if "department_id" in data and data["department_id"]:
                cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的部門不存在"}), 400
            
            if "role_id" in data and data["role_id"]:
                cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的角色不存在"}), 400
            
            if "manager_id" in data and data["manager_id"]:
                cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的主管不存在"}), 400
                
                # 防止員工設定自己為主管
                if str(data["manager_id"]) == str(employee_id):
                    return jsonify({"error": "員工不能設定自己為主管"}), 400
            
            set_clause = []
            params = []

            # 處理所有可更新的欄位
            updatable_fields = [
                "name", "employee_id", "department_id", "position", "email", "phone",
                "hire_date", "status", "salary_level", "id_number", "address", 
                "emergency_contact", "emergency_phone", "role_id", "manager_id", "photo_url", "shift_type"
            ]

            for field in updatable_fields:
                if field in data:
                    # 處理空值情況
                    value = data[field]
                    if value == "" or value is None:
                        if field in ["manager_id", "role_id"]:
                            value = None
                        elif field in ["department_id"]:
                            continue  # 部門ID不能為空，跳過
                    set_clause.append(f"{field} = ?")
                    params.append(value)

            # 特殊處理密碼欄位（只有提供且非空時才更新）
            if "password" in data and data["password"] and data["password"].strip():
                set_clause.append("password = ?")
                params.append(data["password"])

            if not set_clause:
                return jsonify({"error": "未提供更新資料"}), 400

            params.append(employee_id)
            
            try:
                cursor.execute(
                    f"""
                    UPDATE employees
                    SET {', '.join(set_clause)}
                    WHERE id = ?
                """,
                    params,
                )

                if cursor.rowcount == 0:
                    return jsonify({"error": "員工不存在或無變更"}), 404

                conn.commit()
                logging.info(f"成功更新員工 ID {employee_id} 的資料")
                return jsonify({"message": "員工資料更新成功"})
                
            except sqlite3.IntegrityError as e:
                error_msg = str(e)
                if "FOREIGN KEY constraint failed" in error_msg:
                    return jsonify({"error": "資料關聯錯誤，請檢查部門、角色或主管設定"}), 400
                elif "UNIQUE constraint failed" in error_msg:
                    return jsonify({"error": "員工編號已存在，請使用其他編號"}), 400
                else:
                    return jsonify({"error": f"資料完整性錯誤: {error_msg}"}), 400

        elif request.method == "DELETE":
            # 刪除員工資料
            cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))

            if cursor.rowcount == 0:
                return jsonify({"error": "找不到員工資料"}), 404

            conn.commit()
            logging.info(f"成功刪除員工 ID {employee_id}")
            return jsonify({"message": "員工刪除成功"})
    except sqlite3.Error as e:
        logging.error(f"管理員工資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/employees/managers", methods=["GET"])
def get_managers():
    """獲取可以作為審核人員的主管列表"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取有主管權限的員工（role_id <= 2 表示系統管理員或部門主管）
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name,
                   p.role_name, e.department_id
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions p ON e.role_id = p.id
            WHERE e.role_id <= 2 OR e.id IN (
                SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL
            )
            ORDER BY e.department_id, e.name
        """)
        
        columns = [col[0] for col in cursor.description]
        managers = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'managers': managers,
            'total': len(managers)
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取主管列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/employees/substitutes/<int:employee_id>", methods=["GET"])
def get_substitutes(employee_id):
    """獲取可以作為代理人的員工列表"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 先獲取當前員工的部門資訊
        cursor.execute("""
            SELECT department_id FROM employees WHERE id = ?
        """, (employee_id,))
        
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "找不到員工資料"}), 404
            
        current_dept_id = result[0]
        
        # 獲取同部門或有權限的員工作為代理人
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE e.id != ? AND (
                e.department_id = ? OR 
                e.role_id <= 2
            )
            ORDER BY 
                CASE WHEN e.department_id = ? THEN 0 ELSE 1 END,
                e.name
        """, (employee_id, current_dept_id, current_dept_id))
        
        columns = [col[0] for col in cursor.description]
        substitutes = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'substitutes': substitutes,
            'total': len(substitutes)
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取代理人列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 部門管理 ======
@app.route("/api/departments", methods=["GET", "POST"])
def manage_departments():
    """管理部門資料"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得所有部門資料
            cursor.execute(
                """
                SELECT d.id, d.name, d.manager_id, e.name as manager_name, d.description
                FROM departments d
                LEFT JOIN employees e ON d.manager_id = e.id
            """
            )

            columns = [col[0] for col in cursor.description]
            departments = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(departments)
        else:
            # 新增部門資料
            data = request.json
            required_fields = ["name"]

            if not all(field in data for field in required_fields):
                return jsonify({"error": "缺少必要參數"}), 400

            cursor.execute(
                """
                INSERT INTO departments (name, manager_id, description)
                VALUES (?, ?, ?)
            """,
                (data["name"], data.get("manager_id"), data.get("description")),
            )

            conn.commit()
            logging.info(f"成功新增部門 {data['name']}")
            return jsonify({"message": "部門新增成功", "id": cursor.lastrowid})
    except sqlite3.Error as e:
        logging.error(f"管理部門資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/<int:dept_id>", methods=["GET", "PUT", "DELETE"])
def manage_single_department(dept_id):
    """管理單一部門"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("SELECT * FROM departments WHERE id = ?", (dept_id,))
            dept = cursor.fetchone()
            if not dept:
                return jsonify({"error": "部門不存在"}), 404
            
            columns = [col[0] for col in cursor.description]
            return jsonify(dict(zip(columns, dept)))
        
        elif request.method == "PUT":
            data = request.json
            
            # 檢查是否為審核操作
            if "status" in data and data["status"] in ["approved", "rejected"]:
                # 審核請假
                status = data.get("status")
                comment = data.get("comment", "")
                
                cursor.execute("""
                    UPDATE leaves 
                    SET status = ?, comment = ?, approved_at = ?
                    WHERE id = ?
                """, (status, comment, datetime.now(), dept_id))
                
                if cursor.rowcount == 0:
                    return jsonify({"error": "請假記錄不存在"}), 404
                
                conn.commit()
                logging.info(f"請假 {dept_id} 審核為 {status}")
                return jsonify({"message": f"請假{status}成功"})
                
            else:
                # 更新請假申請
                cursor.execute("SELECT status FROM leaves WHERE id = ?", (dept_id,))
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({"error": "請假記錄不存在"}), 404
                
                if result[0] != 'pending':
                    return jsonify({"error": "只能編輯待審批的請假申請"}), 400
                
                cursor.execute("""
                    UPDATE leaves 
                    SET leave_type = ?, start_date = ?, end_date = ?, reason = ?
                    WHERE id = ?
                """, (
                    data.get("leave_type"),
                    data.get("start_date"),
                    data.get("end_date"),
                    data.get("reason"),
                    dept_id
                ))
                
                if cursor.rowcount == 0:
                    return jsonify({"error": "更新失敗"}), 404
                
                conn.commit()
                logging.info(f"請假記錄 {dept_id} 已更新")
                return jsonify({"message": "請假申請更新成功"})
        
        elif request.method == "DELETE":
            cursor.execute("DELETE FROM leaves WHERE id = ?", (dept_id,))
            
            if cursor.rowcount == 0:
                return jsonify({"error": "刪除失敗"}), 404
            
            conn.commit()
            logging.info(f"請假記錄 {dept_id} 已被撤回")
            return jsonify({"message": "請假申請已撤回"})
        
    except sqlite3.Error as e:
        logging.error(f"管理請假記錄錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 排班管理模組 ======
@app.route("/api/schedules/employee/<int:employee_id>", methods=["GET"])
def get_employee_schedule(employee_id):
    """獲取員工排班"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取日期範圍
        start_date = request.args.get('start_date', datetime.now().strftime("%Y-%m-%d"))
        end_date = request.args.get('end_date', (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"))
        
        cursor.execute("""
            SELECT * FROM schedules 
            WHERE employee_id = ? AND shift_date BETWEEN ? AND ?
            ORDER BY shift_date, start_time
        """, (employee_id, start_date, end_date))
        
        columns = [col[0] for col in cursor.description]
        schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            "employee_id": employee_id,
            "start_date": start_date,
            "end_date": end_date,
            "schedules": schedules
        })
        
    except sqlite3.Error as e:
        logging.error(f"查詢員工排班錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 數據分析模組 ======

@app.route("/api/analytics/attendance-trends", methods=["GET"])
def get_attendance_trends():
    """獲取出勤趨勢分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        days = int(request.args.get('days', 7))
        
        # 獲取最近N天的出勤統計
        cursor.execute("""
            SELECT 
                strftime('%w', check_in) as day_of_week,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent,
                COUNT(*) as total
            FROM attendance 
            WHERE DATE(check_in) >= DATE('now', '-{} days')
            GROUP BY strftime('%w', check_in)
            ORDER BY day_of_week
        """.format(days))
        
        results = cursor.fetchall()
        
        # 初始化週數據
        week_data = {
            'labels': ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
            'normal': [0] * 7,
            'late': [0] * 7,
            'absent': [0] * 7
        }
        
        # 填充實際數據
        for row in results:
            day_index = int(row[0])
            week_data['normal'][day_index] = row[1]
            week_data['late'][day_index] = row[2]
            week_data['absent'][day_index] = row[3]
        
        # 計算摘要統計
        total_attendance = sum(week_data['normal'])
        total_late = sum(week_data['late'])
        total_absent = sum(week_data['absent'])
        total_records = total_attendance + total_late + total_absent
        
        summary = {
            'totalAttendance': total_attendance,
            'onTimeRate': round((total_attendance / max(total_records, 1)) * 100, 1),
            'avgWorkHours': 8.2,  # 可以從實際工時計算
            'absentRate': round((total_absent / max(total_records, 1)) * 100, 1)
        }
        
        return jsonify({
            'labels': week_data['labels'],
            'normal': week_data['normal'],
            'late': week_data['late'],
            'absent': week_data['absent'],
            'summary': summary
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取出勤趨勢錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/trends", methods=["GET"])
def get_attendance_trends_simple():
    """獲取考勤趨勢分析（簡化版）"""
    return get_attendance_trends()


@app.route("/api/analytics/department-stats", methods=["GET"])
def get_department_stats():
    """獲取部門統計分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取各部門出勤統計
        cursor.execute("""
            SELECT 
                d.name as department,
                COUNT(a.id) as total_records,
                SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            LEFT JOIN attendance a ON e.id = a.employee_id
            WHERE a.check_in >= DATE('now', '-30 days')
            GROUP BY d.id, d.name
            ORDER BY d.name
        """)
        
        results = cursor.fetchall()
        
        labels = []
        attendance_rates = []
        
        for row in results:
            department = row[0]
            total_records = row[1] or 0
            normal_count = row[2] or 0
            
            attendance_rate = round((normal_count / max(total_records, 1)) * 100, 1)
            
            labels.append(department)
            attendance_rates.append(attendance_rate)
        
        return jsonify({
            'labels': labels,
            'attendanceRates': attendance_rates
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取部門統計錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/time-distribution", methods=["GET"])
def get_time_distribution():
    """獲取打卡時間分布分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取打卡時間分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN strftime('%H:%M', check_in) BETWEEN '08:00' AND '08:30' THEN '8:00-8:30'
                    WHEN strftime('%H:%M', check_in) BETWEEN '08:30' AND '09:00' THEN '8:30-9:00'
                    WHEN strftime('%H:%M', check_in) BETWEEN '09:00' AND '09:30' THEN '9:00-9:30'
                    ELSE '9:30+'
                END as time_range,
                COUNT(*) as count
            FROM attendance 
            WHERE check_in >= DATE('now', '-30 days')
            GROUP BY time_range
            ORDER BY time_range
        """)
        
        results = cursor.fetchall()
        
        # 初始化時間分布數據
        time_data = {
            '8:00-8:30': 0,
            '8:30-9:00': 0,
            '9:00-9:30': 0,
            '9:30+': 0
        }
        
        # 填充實際數據
        for row in results:
            time_range = row[0]
            count = row[1]
            time_data[time_range] = count
        
        return jsonify({
            'labels': list(time_data.keys()),
            'data': list(time_data.values())
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取時間分布錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/leave-stats", methods=["GET"])
def get_leave_stats():
    """獲取請假統計分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取請假類型統計
        cursor.execute("""
            SELECT 
                leave_type,
                COUNT(*) as count
            FROM leaves 
            WHERE created_at >= DATE('now', '-30 days')
            GROUP BY leave_type
        """)
        
        results = cursor.fetchall()
        
        # 計算總數
        total_leaves = sum(row[1] for row in results)
        
        # 生成統計數據
        leave_stats = []
        for row in results:
            leave_type = row[0]
            count = row[1]
            percentage = round((count / max(total_leaves, 1)) * 100, 1)
            
            leave_stats.append({
                'type': leave_type,
                'count': count,
                'percentage': percentage
            })
        
        return jsonify(leave_stats)
        
    except sqlite3.Error as e:
        logging.error(f"獲取請假統計錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/efficiency", methods=["GET"])
def get_efficiency_metrics():
    """獲取效率指標分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 計算準時率
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as on_time
            FROM attendance 
            WHERE check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_records = result[0] or 0
        on_time_records = result[1] or 0
        punctuality_rate = round((on_time_records / max(total_records, 1)) * 100, 1)
        
        # 計算加班頻率（假設超過18:00為加班）
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkouts,
                SUM(CASE WHEN strftime('%H', check_out) >= '18' THEN 1 ELSE 0 END) as overtime_count
            FROM attendance 
            WHERE check_out IS NOT NULL 
            AND check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_checkouts = result[0] or 0
        overtime_count = result[1] or 0
        overtime_frequency = round((overtime_count / max(total_checkouts, 1)) * 100, 1)
        
        # 計算滿勤率（無缺勤記錄的員工比例）
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT e.id) as total_employees,
                COUNT(DISTINCT e.id) - COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN e.id END) as full_attendance_employees
            FROM employees e
            LEFT JOIN attendance a ON e.id = a.employee_id 
            AND a.check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_employees = result[0] or 0
        full_attendance_employees = result[1] or 0
        full_attendance_rate = round((full_attendance_employees / max(total_employees, 1)) * 100, 1)
        
        # 綜合工作效率（基於準時率和滿勤率的加權平均）
        work_efficiency = round((punctuality_rate * 0.6 + full_attendance_rate * 0.4), 1)
        
        return jsonify({
            'workEfficiency': work_efficiency,
            'punctualityRate': punctuality_rate,
            'overtimeFrequency': overtime_frequency,
            'fullAttendanceRate': full_attendance_rate
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取效率指標錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/export", methods=["GET"])
def export_analytics_report():
    """匯出分析報告"""
    report_type = request.args.get('type', 'summary')
    
    # 這裡可以實現不同類型的報告匯出
    # 目前返回一個簡單的JSON響應
    return jsonify({
        "message": f"正在準備{report_type}報告...",
        "download_url": f"/downloads/analytics_{report_type}_{datetime.now().strftime('%Y%m%d')}.pdf"
    })


# ====== 系統設定管理模組 ======
@app.route("/api/settings", methods=["GET", "POST"])
def manage_system_settings():
    """系統設定管理"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取系統設定
            cursor.execute("SELECT * FROM system_settings ORDER BY category, setting_key")
            columns = [col[0] for col in cursor.description]
            settings = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # 按類別組織設定
            organized_settings = {}
            for setting in settings:
                category = setting['category']
                if category not in organized_settings:
                    organized_settings[category] = {}
                organized_settings[category][setting['setting_key']] = setting['setting_value']
            
            return jsonify(organized_settings)
            
        else:
            # 更新系統設定
            data = request.json
            
            for category, settings in data.items():
                for key, value in settings.items():
                    cursor.execute("""
                        INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                        VALUES (?, ?, ?, ?)
                    """, (category, key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("系統設定已更新")
            return jsonify({"message": "設定保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"系統設定管理錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/settings/attendance-rules", methods=["GET", "POST"])
def manage_attendance_rules():
    """考勤規則設定"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT setting_key, setting_value FROM system_settings 
                WHERE category = 'attendance_rules'
            """)
            rules = dict(cursor.fetchall())
            
            # 設定預設值
            default_rules = {
                'late_tolerance_minutes': '10',
                'early_leave_tolerance_minutes': '10',
                'absent_mark_hours': '2',
                'overtime_minimum_hours': '1',
                'auto_mark_absent': 'true',
                'require_location': 'false',
                'allow_mobile_checkin': 'true'
            }
            
            # 合併預設值和實際設定
            for key, default_value in default_rules.items():
                if key not in rules:
                    rules[key] = default_value
            
            return jsonify(rules)
            
        else:
            # 更新考勤規則
            data = request.json
            
            for key, value in data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, ?)
                """, ('attendance_rules', key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("考勤規則已更新")
            return jsonify({"message": "考勤規則保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"考勤規則設定錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/settings/notifications", methods=["GET", "POST"])
def manage_notification_settings():
    """通知設定管理"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT setting_key, setting_value FROM system_settings 
                WHERE category = 'notifications'
            """)
            settings = dict(cursor.fetchall())
            
            # 設定預設值
            default_settings = {
                'email_enabled': 'true',
                'sms_enabled': 'false',
                'push_enabled': 'true',
                'late_notification': 'true',
                'absent_notification': 'true',
                'leave_approval_notification': 'true',
                'system_maintenance_notification': 'true'
            }
            
            for key, default_value in default_settings.items():
                if key not in settings:
                    settings[key] = default_value
            
            return jsonify(settings)
            
        else:
            # 更新通知設定
            data = request.json
            
            for key, value in data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, ?)
                """, ('notifications', key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("通知設定已更新")
            return jsonify({"message": "通知設定保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"通知設定錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/permissions/roles", methods=["GET", "POST"])
def manage_permissions():
    """管理權限角色"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得所有權限角色
            cursor.execute(
                """
                SELECT p.id, p.role_name, p.permission_level, p.description,
                       COUNT(e.id) as user_count
                FROM permissions p
                LEFT JOIN employees e ON p.id = e.role_id
                GROUP BY p.id, p.role_name, p.permission_level, p.description
                ORDER BY p.permission_level DESC
            """
            )

            columns = [col[0] for col in cursor.description]
            roles = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify({"roles": roles})
        else:
            # 新增權限角色
            data = request.json
            required_fields = ["role_name", "permission_level"]

            if not all(field in data for field in required_fields):
                return jsonify({"error": "缺少必要參數"}), 400

            cursor.execute(
                """
                INSERT INTO permissions (role_name, permission_level, description)
                VALUES (?, ?, ?)
            """,
                (
                    data["role_name"],
                    data["permission_level"],
                    data.get("description", ""),
                ),
            )

            conn.commit()
            logging.info(f"成功新增權限角色 {data['role_name']}")
            return jsonify({"message": "權限角色新增成功", "id": cursor.lastrowid})
    except sqlite3.Error as e:
        logging.error(f"管理權限角色錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/permissions", methods=["GET", "POST"])
def manage_department_permissions():
    """管理部門權限"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得部門權限設定
            cursor.execute(
                """
                SELECT d.id, d.name, d.manager_id, e.name as manager_name
                FROM departments d
                LEFT JOIN employees e ON d.manager_id = e.id
            """
            )

            columns = [col[0] for col in cursor.description]
            departments = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(departments)
        else:
            # 更新部門權限設定
            data = request.json
            dept_id = data.get("department_id")
            manager_id = data.get("manager_id")

            if not dept_id:
                return jsonify({"error": "缺少部門ID"}), 400

            cursor.execute(
                """
                UPDATE departments SET manager_id = ? WHERE id = ?
            """,
                (manager_id, dept_id),
            )

            conn.commit()
            logging.info(f"成功更新部門 {dept_id} 的權限設定")
            return jsonify({"message": "部門權限設定成功"})
    except sqlite3.Error as e:
        logging.error(f"管理部門權限錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/stats", methods=["GET"])
def get_departments_stats():
    """獲取部門統計資料"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取部門員工統計
        cursor.execute("""
            SELECT d.id, d.name, 
                   COUNT(e.id) as total_employees,
                   SUM(CASE WHEN e.status = 'active' THEN 1 ELSE 0 END) as active_employees,
                   SUM(CASE WHEN e.status = 'trial' THEN 1 ELSE 0 END) as trial_employees
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            GROUP BY d.id, d.name
            ORDER BY d.name
        """)
        
        departments = []
        for row in cursor.fetchall():
            departments.append({
                "id": row[0],
                "name": row[1],
                "total_employees": row[2],
                "active_employees": row[3],
                "trial_employees": row[4]
            })
        
        conn.close()
        return jsonify({"departments": departments})
        
    except Exception as e:
        logging.error(f"獲取部門統計失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/reports/dashboard", methods=["GET"])
def get_dashboard_reports():
    """獲取儀表板報表數據"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取今日統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkins,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM attendance
            WHERE DATE(check_in) = ?
        """, (today,))
        
        today_stats = cursor.fetchone()
        
        # 獲取本週統計
        week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT COUNT(*) as week_total
            FROM attendance
            WHERE DATE(check_in) >= ?
        """, (week_start,))
        
        week_stats = cursor.fetchone()
        
        # 獲取待審批請假
        cursor.execute("""
            SELECT COUNT(*) as pending_leaves
            FROM leaves
            WHERE status = 'pending'
        """)
        
        leave_stats = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            "today": {
                "total_checkins": today_stats[0] if today_stats else 0,
                "normal_count": today_stats[1] if today_stats else 0,
                "late_count": today_stats[2] if today_stats else 0,
                "early_leave_count": today_stats[3] if today_stats else 0,
                "absent_count": today_stats[4] if today_stats else 0
            },
            "week": {
                "total_checkins": week_stats[0] if week_stats else 0
            },
            "leaves": {
                "pending_count": leave_stats[0] if leave_stats else 0
            }
        })
        
    except Exception as e:
        logging.error(f"獲取儀表板報表失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 審核作業模組 ======
@app.route("/api/approval/leaves", methods=["GET"])
def get_pending_leaves():
    """獲取待審核的請假申請"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取待審核的請假申請，包含申請人、代理人和審核主管資訊
        cursor.execute("""
            SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id,
                   e.position as employee_position, d.name as department_name, 
                   l.leave_type, l.start_date, l.end_date, l.reason, l.created_at, 
                   l.status, l.approver_id, a.name as approver_name, a.position as approver_position,
                   l.substitute_id, s.name as substitute_name, s.position as substitute_position,
                   e.photo_url
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON l.approver_id = a.id
            LEFT JOIN employees s ON l.substitute_id = s.id
            WHERE l.status = 'pending'
            ORDER BY l.created_at DESC
        """)
        
        columns = [col[0] for col in cursor.description]
        leaves = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        # 計算請假天數
        for leave in leaves:
            if leave['start_date'] and leave['end_date']:
                start = datetime.strptime(leave['start_date'], '%Y-%m-%d')
                end = datetime.strptime(leave['end_date'], '%Y-%m-%d')
                leave['duration_days'] = (end - start).days + 1
            else:
                leave['duration_days'] = 1
        
        conn.close()
        return jsonify({
            'leaves': leaves,
            'total': len(leaves)
        })
        
    except Exception as e:
        logging.error(f"獲取待審核請假失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/approval/leaves/<int:leave_id>", methods=["POST"])
def approve_leave(leave_id):
    """審核請假申請"""
    try:
        data = request.json
        action = data.get('action')  # 'approve' 或 'reject'
        comment = data.get('comment', '')
        approver_id = data.get('approver_id', 1)  # 應該從登入狀態獲取
        
        if action not in ['approve', 'reject']:
            return jsonify({"error": "無效的審核動作"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在且為待審核狀態
        cursor.execute("""
            SELECT l.id, l.employee_id, e.name as employee_name, l.leave_type, 
                   l.start_date, l.end_date, l.status
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            WHERE l.id = ?
        """, (leave_id,))
        
        leave = cursor.fetchone()
        if not leave:
            return jsonify({"error": "請假申請不存在"}), 404
        
        if leave[6] != 'pending':  # status欄位
            return jsonify({"error": "此請假申請已被審核"}), 400
        
        # 更新請假狀態
        new_status = 'approved' if action == 'approve' else 'rejected'
        cursor.execute("""
            UPDATE leaves 
            SET status = ?, comment = ?, approver_id = ?, approved_at = ?
            WHERE id = ?
        """, (new_status, comment, approver_id, datetime.now(), leave_id))
        
        conn.commit()
        conn.close()
        
        action_text = '批准' if action == 'approve' else '拒絕'
        logging.info(f"請假申請 {leave_id} 已被{action_text}")
        
        return jsonify({
            "message": f"請假申請已{action_text}",
            "leave_id": leave_id,
            "status": new_status
        })
        
    except Exception as e:
        logging.error(f"審核請假失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500

