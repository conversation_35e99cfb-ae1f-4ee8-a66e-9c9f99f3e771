{"name": "attendance-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 7075", "build": "next build", "start": "next start --port 7075", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.0", "@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "next": "^14.2.15", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.56"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "^14.2.15", "postcss": "^8", "tailwindcss": "^3", "typescript": "^5"}}