{"summary": {"total_tests": 36, "passed": 35, "failed": 1, "skipped": 0, "partial": 0, "success_rate": 97.22}, "categories": {"其他": {"PASS": 18, "FAIL": 1, "SKIP": 0, "PARTIAL": 0}, "創建班別": {"PASS": 6, "FAIL": 0, "SKIP": 0, "PARTIAL": 0}, "無效ID測試": {"PASS": 4, "FAIL": 0, "SKIP": 0, "PARTIAL": 0}, "性能測試": {"PASS": 1, "FAIL": 0, "SKIP": 0, "PARTIAL": 0}, "清理": {"PASS": 6, "FAIL": 0, "SKIP": 0, "PARTIAL": 0}}, "detailed_results": [{"test_name": "API連接測試", "status": "PASS", "timestamp": "2025-06-02T12:54:40.736753", "details": "系統狀態: healthy", "error": null}, {"test_name": "獲取現有班別", "status": "PASS", "timestamp": "2025-06-02T12:54:40.741811", "details": "現有 12 個班別", "error": null}, {"test_name": "創建班別: 測試早班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.746119", "details": "ID: 81, 時間: 06:30-14:30", "error": null}, {"test_name": "創建班別: 測試標準班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.750130", "details": "ID: 82, 時間: 08:30-17:30", "error": null}, {"test_name": "創建班別: 測試下午班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.754066", "details": "ID: 83, 時間: 13:00-21:00", "error": null}, {"test_name": "創建班別: 測試夜間班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.758206", "details": "ID: 84, 時間: 21:30-05:30", "error": null}, {"test_name": "創建班別: 測試彈性班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.762228", "details": "ID: 85, 時間: 09:30-18:30", "error": null}, {"test_name": "創建班別: 測試無加班班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.766844", "details": "ID: 86, 時間: 08:00-17:00", "error": null}, {"test_name": "讀取班別詳情", "status": "PASS", "timestamp": "2025-06-02T12:54:40.770319", "details": "名稱: 測試早班-06021254, 代碼: TEST_MORNING_06021254", "error": null}, {"test_name": "更新班別資料", "status": "PASS", "timestamp": "2025-06-02T12:54:40.774258", "details": "更新時間: 12:54:40", "error": null}, {"test_name": "驗證更新結果", "status": "PASS", "timestamp": "2025-06-02T12:54:40.777288", "details": "更新內容已生效", "error": null}, {"test_name": "  ⏰ 準時上下班", "status": "PASS", "timestamp": "2025-06-02T12:54:40.780330", "details": "總加班: 0h, 上班前: 0h, 下班後: 0h (08:30-17:30)", "error": null}, {"test_name": "  ⏰ 早到30分鐘（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:54:40.783660", "details": "總加班: 0h, 上班前: 0h, 下班後: 0h (08:00-17:30)", "error": null}, {"test_name": "  ⏰ 早到1小時", "status": "PASS", "timestamp": "2025-06-02T12:54:40.786713", "details": "總加班: 0.5h, 上班前: 0.5h, 下班後: 0h (07:30-17:30)", "error": null}, {"test_name": "  ⏰ 晚走1小時（未達門檻）", "status": "PASS", "timestamp": "2025-06-02T12:54:40.792600", "details": "總加班: 0h, 上班前: 0h, 下班後: 0h (08:30-18:30)", "error": null}, {"test_name": "  ⏰ 晚走2小時", "status": "PASS", "timestamp": "2025-06-02T12:54:40.795827", "details": "總加班: 1.0h, 上班前: 0h, 下班後: 1.0h (08:30-19:30)", "error": null}, {"test_name": "  ⏰ 早到晚走組合", "status": "PASS", "timestamp": "2025-06-02T12:54:40.799691", "details": "總加班: 2.5h, 上班前: 1.0h, 下班後: 1.5h (07:00-20:00)", "error": null}, {"test_name": "  ⏰ 遲到早退", "status": "PASS", "timestamp": "2025-06-02T12:54:40.802615", "details": "總加班: 0h, 上班前: 0h, 下班後: 0h (09:30-16:30)", "error": null}, {"test_name": "  ⏰ 極端早到", "status": "PASS", "timestamp": "2025-06-02T12:54:40.806057", "details": "總加班: 2.0h, 上班前: 2.0h, 下班後: 0h (06:00-17:30)", "error": null}, {"test_name": "  ⏰ 極端晚走", "status": "PASS", "timestamp": "2025-06-02T12:54:40.809064", "details": "總加班: 3.5h, 上班前: 0h, 下班後: 3.5h (08:30-22:00)", "error": null}, {"test_name": "無效ID測試: 0", "status": "PASS", "timestamp": "2025-06-02T12:54:40.811812", "details": null, "error": null}, {"test_name": "無效ID測試: -1", "status": "PASS", "timestamp": "2025-06-02T12:54:40.815121", "details": null, "error": null}, {"test_name": "無效ID測試: 99999", "status": "PASS", "timestamp": "2025-06-02T12:54:40.818256", "details": null, "error": null}, {"test_name": "無效ID測試: abc", "status": "PASS", "timestamp": "2025-06-02T12:54:40.820781", "details": null, "error": null}, {"test_name": "無效時間格式測試 1", "status": "PASS", "timestamp": "2025-06-02T12:54:40.823466", "details": null, "error": null}, {"test_name": "無效時間格式測試 2", "status": "PASS", "timestamp": "2025-06-02T12:54:40.826064", "details": null, "error": null}, {"test_name": "無效時間格式測試 3", "status": "PASS", "timestamp": "2025-06-02T12:54:40.829248", "details": null, "error": null}, {"test_name": "無效時間格式測試 4", "status": "FAIL", "timestamp": "2025-06-02T12:54:40.837469", "details": null, "error": "期望400，實際201"}, {"test_name": "性能測試: 獲取班別列表", "status": "PASS", "timestamp": "2025-06-02T12:54:40.840527", "details": "響應時間: 2.94ms, 班別數量: 19", "error": null}, {"test_name": "清理: 測試早班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.844292", "details": null, "error": null}, {"test_name": "清理: 測試標準班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.849012", "details": null, "error": null}, {"test_name": "清理: 測試下午班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.852967", "details": null, "error": null}, {"test_name": "清理: 測試夜間班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.856995", "details": null, "error": null}, {"test_name": "清理: 測試彈性班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.861325", "details": null, "error": null}, {"test_name": "清理: 測試無加班班-06021254", "status": "PASS", "timestamp": "2025-06-02T12:54:40.867832", "details": null, "error": null}, {"test_name": "數據清理總結", "status": "PASS", "timestamp": "2025-06-02T12:54:40.867846", "details": "成功清理 6/6 個測試班別", "error": null}], "generated_at": "2025-06-02T12:54:40.867901", "test_environment": {"base_url": "http://localhost:7072", "python_version": "3.9+", "test_framework": "Custom"}}