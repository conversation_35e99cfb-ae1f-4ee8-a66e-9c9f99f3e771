# 開發檢查清單 (Development Checklist)

## API開發檢查清單

### ✅ 開發新API前
- [ ] 閱讀 `docs/API_STANDARDS.md` 文件
- [ ] 確認使用標準欄位名稱
- [ ] 確認回應格式符合規範
- [ ] 檢查是否與現有API欄位命名一致

### ✅ API修改檢查
- [ ] 確認前端期待的欄位名稱
- [ ] 檢查是否有向後相容性問題
- [ ] 測試API回應格式
- [ ] 更新API文件

### ✅ 前端開發檢查
- [ ] 檢查API回應欄位名稱
- [ ] 使用標準欄位名稱存取資料
- [ ] 處理API錯誤情況
- [ ] 測試資料載入功能

## 常見問題避免清單

### ❌ 避免的欄位名稱不一致問題
- `stats.pending` vs `stats.pending_count`
- `data.leaves` vs `data.records`
- `leave.emp_id` vs `leave.employee_code`

### ✅ 統一使用的欄位名稱
- `pending_count` - 待審核數量
- `records` - 記錄列表
- `employee_code` - 員工代碼
- `employee_name` - 員工姓名
- `department_name` - 部門名稱

## 測試檢查清單

### API測試
```bash
# 1. 測試審核統計API欄位
curl -s http://localhost:7072/api/approval/stats | jq '.pending_count'
# 預期：返回數字

# 2. 測試審核列表API欄位
curl -s http://localhost:7072/api/approval/leaves | jq '.records[0].employee_code'
# 預期：返回員工代碼字串

# 3. 測試員工列表API
curl -s http://localhost:7072/api/employees | jq '.employees[0].employee_code'
# 預期：返回員工代碼字串
```

### 前端測試
- [ ] 檢查統計數字是否正確顯示
- [ ] 檢查列表資料是否正確載入
- [ ] 檢查下拉選單是否有選項
- [ ] 檢查員工資訊是否正確顯示

## 部署前檢查

### 功能測試
- [ ] 請假申請表單提交成功
- [ ] 代理人下拉選單有所有員工選項
- [ ] 審核頁面統計數字正確
- [ ] 審核列表顯示正確
- [ ] 側邊選單待審核數字正確

### 效能測試
- [ ] API回應時間合理（< 2秒）
- [ ] 頁面載入速度正常
- [ ] 無明顯記憶體洩漏

### 錯誤處理測試
- [ ] API錯誤時前端有適當提示
- [ ] 網路連線中斷時的處理
- [ ] 無資料時的顯示

## 程式碼檢查

### Python/Flask API
```python
# ✅ 正確的API回應格式
return jsonify({
    "success": True,
    "records": records,        # 使用records而非leaves
    "pending_count": count,    # 使用pending_count而非pending
    "timestamp": datetime.now().isoformat()
})

# ❌ 避免的格式
return jsonify({
    "leaves": records,     # 應該用records
    "pending": count,      # 應該用pending_count
})
```

### JavaScript 前端
```javascript
// ✅ 正確的欄位存取
const pendingCount = stats.pending_count;     // 而非stats.pending
const records = data.records;                 // 而非data.leaves
const employeeCode = leave.employee_code;     // 而非leave.emp_id

// ❌ 避免的欄位存取
const pendingCount = stats.pending;           // 錯誤
const records = data.leaves;                  // 錯誤
const employeeCode = leave.emp_id;            // 錯誤
```

## 緊急修復指南

當發現API欄位不匹配時：

### 1. 快速診斷
```bash
# 檢查API實際回應
curl -s http://localhost:7072/api/approval/stats | jq .
curl -s http://localhost:7072/api/approval/leaves | jq .
```

### 2. 確認問題
- 前端期待什麼欄位？
- API實際回應什麼欄位？
- 是否符合 `docs/API_STANDARDS.md` 規範？

### 3. 修復優先順序
1. **優先修改API**使其符合標準
2. 如API無法修改，則暫時修改前端
3. 在註釋中標記臨時修改，排程後續統一

### 4. 預防措施
- 更新 `docs/API_STANDARDS.md`
- 建立API測試案例
- 通知團隊成員

## 團隊協作

### 溝通規範
- API修改前必須通知前端開發者
- 欄位名稱變更需團隊討論
- 重大API修改需寫設計文件

### 版本管理
- API變更記錄在CHANGELOG中
- 標記破壞性變更
- 提供遷移指南

### 文件維護
- API修改後立即更新文件
- 定期檢查文件與實際代碼一致性
- 新人onboarding時介紹API規範 