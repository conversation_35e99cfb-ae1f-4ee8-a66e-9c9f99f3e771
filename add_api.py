import re

# 讀取app.py文件
with open('app.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 要插入的API代碼
api_code = '''

@app.route("/api/attendance/management/update-shift", methods=["POST"])
def update_attendance_shift():
    """
    修改考勤記錄班表 API
    
    功能說明：
    - 修改指定考勤記錄的班表
    - 重新計算遲到、早退、加班時間
    - 更新考勤狀態
    
    請求參數：
    {
        "attendance_id": 123,    // 考勤記錄ID (必填)
        "shift_id": 456         // 新班表ID (必填)
    }
    
    返回格式：
    {
        "success": true,
        "message": "班表修改成功",
        "record": {
            "id": 123,
            "employee_name": "張三",
            "work_date": "2025-06-04",
            "shift_name": "標準日班",
            "late_minutes": 0,
            "early_leave_minutes": 0,
            "overtime_hours": 0,
            "status": "normal"
        }
    }
    """
    try:
        logger.info("開始處理班表修改請求")
        
        # ===== 獲取請求資料 =====
        data = request.get_json()
        if not data:
            logger.warning("請求資料為空")
            return jsonify({"success": False, "error": "請求資料不能為空"}), 400
        
        attendance_id = data.get('attendance_id')
        shift_id = data.get('shift_id')
        
        # ===== 參數驗證 =====
        if not attendance_id or not shift_id:
            logger.warning(f"缺少必要參數: attendance_id={attendance_id}, shift_id={shift_id}")
            return jsonify({"success": False, "error": "考勤記錄ID和班表ID為必填項"}), 400
        
        conn = create_connection()
        if not conn:
            logger.error("資料庫連接失敗")
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        cursor = conn.cursor()
        
        try:
            # ===== 驗證考勤記錄是否存在 =====
            cursor.execute("""
                SELECT a.id, a.employee_id, a.work_date, a.check_in, a.check_out,
                       e.name as employee_name
                FROM attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.id = ?
            """, (attendance_id,))
            
            attendance_record = cursor.fetchone()
            if not attendance_record:
                logger.warning(f"考勤記錄不存在: attendance_id={attendance_id}")
                return jsonify({"success": False, "error": "考勤記錄不存在"}), 404
            
            # ===== 驗證班表是否存在 =====
            cursor.execute("""
                SELECT id, name, start_time, end_time, break_start, break_end
                FROM shifts
                WHERE id = ?
            """, (shift_id,))
            
            shift_record = cursor.fetchone()
            if not shift_record:
                logger.warning(f"班表不存在: shift_id={shift_id}")
                return jsonify({"success": False, "error": "班表不存在"}), 404
            
            # ===== 更新或創建排班記錄 =====
            cursor.execute("""
                INSERT OR REPLACE INTO schedules (employee_id, work_date, shift_id, created_at)
                VALUES (?, ?, ?, datetime('now'))
            """, (attendance_record[1], attendance_record[2], shift_id))
            
            # ===== 重新計算考勤狀況 =====
            check_in = attendance_record[3]
            check_out = attendance_record[4]
            shift_start = shift_record[2]
            shift_end = shift_record[3]
            
            late_minutes = 0
            early_leave_minutes = 0
            overtime_minutes = 0
            status = "normal"
            
            if check_in and shift_start:
                try:
                    check_in_time = datetime.strptime(check_in.split()[1], '%H:%M:%S').time()
                    shift_start_time = datetime.strptime(shift_start, '%H:%M').time()
                    
                    if check_in_time > shift_start_time:
                        late_minutes = int((datetime.combine(datetime.today(), check_in_time) - 
                                          datetime.combine(datetime.today(), shift_start_time)).total_seconds() / 60)
                        status = "late"
                except Exception as time_error:
                    logger.warning(f"計算遲到時間失敗: {time_error}")
            
            if check_out and shift_end:
                try:
                    check_out_time = datetime.strptime(check_out.split()[1], '%H:%M:%S').time()
                    shift_end_time = datetime.strptime(shift_end, '%H:%M').time()
                    
                    if check_out_time < shift_end_time:
                        early_leave_minutes = int((datetime.combine(datetime.today(), shift_end_time) - 
                                                 datetime.combine(datetime.today(), check_out_time)).total_seconds() / 60)
                        status = "early_leave"
                    elif check_out_time > shift_end_time:
                        overtime_minutes = int((datetime.combine(datetime.today(), check_out_time) - 
                                              datetime.combine(datetime.today(), shift_end_time)).total_seconds() / 60)
                        if overtime_minutes >= 30:  # 超過30分鐘才算加班
                            status = "overtime"
                except Exception as time_error:
                    logger.warning(f"計算早退/加班時間失敗: {time_error}")
            
            # ===== 處理未打卡情況 =====
            if not check_in or not check_out:
                status = "absent"
            
            # ===== 更新考勤記錄 =====
            cursor.execute("""
                UPDATE attendance 
                SET late_minutes = ?, 
                    early_leave_minutes = ?, 
                    overtime_minutes = ?,
                    status = ?,
                    updated_at = datetime('now')
                WHERE id = ?
            """, (late_minutes, early_leave_minutes, overtime_minutes, status, attendance_id))
            
            conn.commit()
            
            # ===== 構建返回資料 =====
            updated_record = {
                "id": attendance_id,
                "employee_name": attendance_record[5],
                "work_date": attendance_record[2],
                "shift_name": shift_record[1],
                "late_minutes": late_minutes,
                "early_leave_minutes": early_leave_minutes,
                "overtime_hours": round(overtime_minutes / 60, 2),
                "status": status
            }
            
            logger.info(f"班表修改成功: attendance_id={attendance_id}, shift_id={shift_id}")
            
            return jsonify({
                "success": True,
                "message": "班表修改成功",
                "record": updated_record
            })
            
        except Exception as query_error:
            conn.rollback()
            logger.error(f"修改班表時發生錯誤: {query_error}")
            return jsonify({"success": False, "error": f"修改失敗: {str(query_error)}"}), 500
        
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"班表修改API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500

'''

# 找到插入位置（在最後一個@app.route之後）
pattern = r'(@app\.route\("/api/attendance/processing/status/<string:processing_id>", methods=\["GET"\]\))'
match = re.search(pattern, content)

if match:
    # 在找到的位置之前插入API代碼
    insert_pos = match.start()
    new_content = content[:insert_pos] + api_code + content[insert_pos:]
    
    # 寫入修改後的內容
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print('API已成功添加到app.py')
else:
    print('未找到插入位置，嘗試在文件末尾添加')
    # 如果找不到特定位置，就在文件末尾添加
    new_content = content + api_code
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    print('API已添加到文件末尾') 