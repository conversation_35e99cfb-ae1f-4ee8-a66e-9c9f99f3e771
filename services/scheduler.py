"""
排班管理服務。

此模組負責：
- 自動化排班邏輯
- 班表規則驗證
- 排班衝突檢測
- 班表最佳化
"""

import logging
from typing import Dict, List, Tuple

import sqlite3
from datetime import date, timedelta

from database import create_connection

logger = logging.getLogger(__name__)


class ShiftType:
    """班別類型定義"""

    MORNING = "morning"  # 早班 07:00-15:00
    EVENING = "evening"  # 晚班 15:00-23:00
    NIGHT = "night"  # 大夜班 23:00-07:00
    CUSTOM = "custom"  # 自訂班別


class ShiftScheduler:
    """排班調度器"""

    def __init__(self):
        self.shift_templates = {
            ShiftType.MORNING: {"start_time": "07:00", "end_time": "15:00"},
            ShiftType.EVENING: {"start_time": "15:00", "end_time": "23:00"},
            ShiftType.NIGHT: {"start_time": "23:00", "end_time": "07:00"},
        }

    def generate_roster(
        self, start_date: date, end_date: date, employee_ids: List[int]
    ) -> Dict[str, List[Dict]]:
        """
        產生排班表。

        Args:
            start_date: 開始日期
            end_date: 結束日期
            employee_ids: 員工ID列表

        Returns:
            排班表資料
        """
        roster = {"早班": [], "晚班": [], "大夜班": []}

        current_date = start_date
        employee_cycle = 0

        while current_date <= end_date:
            # 簡單的輪班邏輯
            for shift_name, shift_type in [
                ("早班", ShiftType.MORNING),
                ("晚班", ShiftType.EVENING),
                ("大夜班", ShiftType.NIGHT),
            ]:

                # 分配員工到班次
                assigned_employees = self._assign_employees_to_shift(
                    employee_ids, employee_cycle, shift_type
                )

                for employee_id in assigned_employees:
                    schedule = self._create_schedule_record(
                        employee_id, current_date, shift_type
                    )
                    roster[shift_name].append(schedule)

            current_date += timedelta(days=1)
            employee_cycle = (employee_cycle + 1) % len(employee_ids)

        return roster

    def _assign_employees_to_shift(
        self, employee_ids: List[int], cycle_offset: int, shift_type: str
    ) -> List[int]:
        """
        分配員工到特定班次。

        Args:
            employee_ids: 員工ID列表
            cycle_offset: 循環偏移量
            shift_type: 班次類型

        Returns:
            分配到該班次的員工ID列表
        """
        # 每個班次分配1/3的員工
        employees_per_shift = max(1, len(employee_ids) // 3)

        if shift_type == ShiftType.MORNING:
            start_idx = 0
        elif shift_type == ShiftType.EVENING:
            start_idx = employees_per_shift
        else:  # NIGHT
            start_idx = employees_per_shift * 2

        assigned = []
        for i in range(employees_per_shift):
            emp_idx = (start_idx + i + cycle_offset) % len(employee_ids)
            assigned.append(employee_ids[emp_idx])

        return assigned

    def _create_schedule_record(
        self, employee_id: int, shift_date: date, shift_type: str
    ) -> Dict:
        """
        建立排班記錄。

        Args:
            employee_id: 員工ID
            shift_date: 班次日期
            shift_type: 班次類型

        Returns:
            排班記錄
        """
        template = self.shift_templates.get(shift_type, {})

        return {
            "employee_id": employee_id,
            "shift_date": shift_date.isoformat(),
            "shift_type": shift_type,
            "start_time": template.get("start_time", "09:00"),
            "end_time": template.get("end_time", "17:00"),
            "is_overtime": False,
        }

    def save_schedule(self, schedule_data: List[Dict]) -> bool:
        """
        儲存排班資料到資料庫。

        Args:
            schedule_data: 排班資料列表

        Returns:
            是否儲存成功
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            for schedule in schedule_data:
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO schedules 
                    (employee_id, shift_date, shift_type, start_time, end_time, is_overtime)
                    VALUES (?, ?, ?, ?, ?, ?)
                """,
                    (
                        schedule["employee_id"],
                        schedule["shift_date"],
                        schedule["shift_type"],
                        schedule["start_time"],
                        schedule["end_time"],
                        schedule.get("is_overtime", False),
                    ),
                )

            conn.commit()
            logger.info(f"成功儲存 {len(schedule_data)} 筆排班資料")
            return True

        except sqlite3.Error as e:
            logger.error(f"儲存排班資料失敗: {e}")
            return False
        finally:
            conn.close()

    def validate_schedule(
        self, employee_id: int, shift_date: date, shift_type: str
    ) -> Tuple[bool, str]:
        """
        驗證排班是否合規。

        Args:
            employee_id: 員工ID
            shift_date: 班次日期
            shift_type: 班次類型

        Returns:
            (是否合規, 錯誤訊息)
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            # 檢查是否已有排班
            cursor.execute(
                """
                SELECT COUNT(*) FROM schedules 
                WHERE employee_id = ? AND shift_date = ?
            """,
                (employee_id, shift_date),
            )

            result = cursor.fetchone()
            if result and result[0] > 0:
                return False, "該員工在此日期已有排班"

            # 檢查連續工作天數
            cursor.execute(
                """
                SELECT COUNT(*) FROM schedules 
                WHERE employee_id = ? 
                AND shift_date BETWEEN date(?, '-6 days') AND date(?, '-1 day')
            """,
                (employee_id, shift_date, shift_date),
            )

            result = cursor.fetchone()
            consecutive_days = result[0] if result else 0
            if consecutive_days >= 6:
                return False, "連續工作天數超過限制（6天）"

            # 檢查班次間隔（如果是大夜班後的早班）
            if shift_type == ShiftType.MORNING:
                cursor.execute(
                    """
                    SELECT shift_type FROM schedules 
                    WHERE employee_id = ? AND shift_date = date(?, '-1 day')
                """,
                    (employee_id, shift_date),
                )

                prev_shift = cursor.fetchone()
                if prev_shift and prev_shift[0] == ShiftType.NIGHT:
                    return False, "大夜班後不能直接排早班（需要休息時間）"

            return True, ""

        except sqlite3.Error as e:
            logger.error(f"驗證排班失敗: {e}")
            return False, f"驗證失敗: {e}"
        finally:
            conn.close()

    def get_employee_schedule(
        self, employee_id: int, start_date: date, end_date: date
    ) -> List[Dict]:
        """
        取得員工排班資料。

        Args:
            employee_id: 員工ID
            start_date: 開始日期
            end_date: 結束日期

        Returns:
            排班資料列表
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT s.id, s.employee_id, s.shift_date, s.shift_type, 
                       s.start_time, s.end_time, s.is_overtime,
                       e.name as employee_name
                FROM schedules s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.employee_id = ? 
                AND s.shift_date BETWEEN ? AND ?
                ORDER BY s.shift_date
            """,
                (employee_id, start_date, end_date),
            )

            columns = [col[0] for col in cursor.description]
            schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return schedules

        except sqlite3.Error as e:
            logger.error(f"查詢員工排班失敗: {e}")
            return []
        finally:
            conn.close()

    def get_daily_roster(self, target_date: date) -> Dict[str, List[Dict]]:
        """
        取得特定日期的排班表。

        Args:
            target_date: 目標日期

        Returns:
            按班次分組的排班表
        """
        conn = create_connection()
        try:
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT s.*, e.name as employee_name, d.name as department_name
                FROM schedules s
                JOIN employees e ON s.employee_id = e.id
                JOIN departments d ON e.department_id = d.id
                WHERE s.shift_date = ?
                ORDER BY s.shift_type, e.name
            """,
                (target_date,),
            )

            columns = [col[0] for col in cursor.description]
            all_schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # 按班次分組
            roster = {"早班": [], "晚班": [], "大夜班": [], "自訂": []}

            shift_mapping = {
                ShiftType.MORNING: "早班",
                ShiftType.EVENING: "晚班",
                ShiftType.NIGHT: "大夜班",
                ShiftType.CUSTOM: "自訂",
            }

            for schedule in all_schedules:
                shift_name = shift_mapping.get(schedule["shift_type"], "自訂")
                roster[shift_name].append(schedule)

            return roster

        except sqlite3.Error as e:
            logger.error(f"查詢日排班表失敗: {e}")
            return {}
        finally:
            conn.close()


class ScheduleRuleEngine:
    """排班規則引擎"""

    @staticmethod
    def get_schedule_rules() -> List[Dict]:
        """取得所有排班規則"""
        conn = create_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM schedule_rules")
            columns = [col[0] for col in cursor.description]
            rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return rules
        except sqlite3.Error as e:
            logger.error(f"查詢排班規則失敗: {e}")
            return []
        finally:
            conn.close()

    @staticmethod
    def validate_against_rules(
        employee_id: int, shift_date: date
    ) -> Tuple[bool, List[str]]:
        """
        根據規則驗證排班。

        Args:
            employee_id: 員工ID
            shift_date: 排班日期

        Returns:
            (是否通過驗證, 錯誤訊息列表)
        """
        rules = ScheduleRuleEngine.get_schedule_rules()
        errors = []

        for rule in rules:
            rule_type = rule["rule_type"]
            rule_value = rule["rule_value"]

            if rule_type == "max_continuous_days":
                # 檢查最大連續工作天數
                max_days = int(rule_value)
                # TODO: 實現檢查邏輯
                logger.debug(f"檢查員工 {employee_id} 最大連續工作天數: {max_days}")

            elif rule_type == "shift_interval":
                # 檢查班次間隔
                min_hours = int(rule_value)
                # TODO: 實現檢查邏輯
                logger.debug(f"檢查班次間隔: {min_hours} 小時")

            elif rule_type == "overtime_limit":
                # 檢查加班時數限制
                max_hours = int(rule_value)
                # TODO: 實現檢查邏輯
                logger.debug(f"檢查加班時數限制: {max_hours} 小時")

        return len(errors) == 0, errors
