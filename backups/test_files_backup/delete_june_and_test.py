#!/usr/bin/env python3
"""
刪除2025年6月考勤記錄並測試增量處理功能。
"""

import sqlite3
from datetime import datetime
from services.enhanced_attendance_processor import EnhancedAttendanceProcessor

def create_connection():
    """
    創建與 SQLite 資料庫的連接。
    """
    try:
        conn = sqlite3.connect("attendance.db")
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
    except Exception as e:
        print(f"資料庫連接錯誤: {e}")
        return None

def delete_june_2025_attendance():
    """
    刪除2025年6月的考勤記錄。
    """
    conn = create_connection()
    if not conn:
        return 0
    
    try:
        cursor = conn.cursor()
        
        # 先檢查要刪除的記錄數
        cursor.execute("SELECT COUNT(*) FROM attendance WHERE work_date LIKE '2025-06%'")
        count_before = cursor.fetchone()[0]
        
        # 刪除2025年6月記錄
        cursor.execute("DELETE FROM attendance WHERE work_date LIKE '2025-06%'")
        deleted_count = cursor.rowcount
        
        conn.commit()
        
        print(f"📊 刪除前6月記錄數：{count_before}")
        print(f"✅ 成功刪除記錄數：{deleted_count}")
        
        return deleted_count
        
    except Exception as e:
        print(f"刪除6月記錄錯誤: {e}")
        return 0
    finally:
        conn.close()

def check_status_before_after():
    """
    檢查處理前後的狀態。
    """
    processor = EnhancedAttendanceProcessor()
    
    # 檢查當前狀態
    last_processed = processor.get_last_processed_date()
    dates_to_process = processor.get_dates_to_process()
    
    print(f"📅 上次處理日期：{last_processed or '無記錄'}")
    print(f"📊 需要處理天數：{len(dates_to_process)} 天")
    
    if len(dates_to_process) > 0:
        print(f"📋 需要處理的日期範圍：")
        print(f"   從：{dates_to_process[0]}")
        print(f"   到：{dates_to_process[-1]}")
        
        # 顯示前10個日期
        print(f"📋 前10個需要處理的日期：")
        for i, date in enumerate(dates_to_process[:10], 1):
            print(f"   {i:2d}. {date}")
        
        if len(dates_to_process) > 10:
            print(f"   ... 還有 {len(dates_to_process) - 10} 個日期")
    
    return {
        'last_processed_date': last_processed,
        'dates_to_process': dates_to_process,
        'total_days_to_process': len(dates_to_process)
    }

def execute_batch_processing():
    """
    執行批量考勤處理。
    """
    processor = EnhancedAttendanceProcessor()
    
    try:
        print("🚀 開始執行批量處理...")
        result = processor.process_attendance_batch()
        return result
    except Exception as e:
        print(f"批量處理錯誤: {e}")
        return None

def main():
    """
    主函數。
    """
    print("=" * 70)
    print("🧪 刪除2025年6月考勤記錄並測試增量處理功能")
    print("=" * 70)
    
    # 步驟1：刪除6月考勤記錄
    print("\n🗑️  步驟1：刪除2025年6月考勤記錄")
    print("-" * 50)
    
    deleted_count = delete_june_2025_attendance()
    if deleted_count == 0:
        print("❌ 沒有刪除任何記錄或刪除失敗")
        return
    
    # 步驟2：檢查處理前狀態
    print("\n🔍 步驟2：檢查處理前狀態")
    print("-" * 50)
    
    before_status = check_status_before_after()
    
    # 步驟3：執行批量處理
    print("\n⚙️  步驟3：執行批量考勤處理")
    print("-" * 50)
    
    if before_status['total_days_to_process'] == 0:
        print("ℹ️  沒有需要處理的日期，跳過批量處理")
    else:
        processing_result = execute_batch_processing()
        
        if processing_result:
            print(f"✅ 批量處理完成！")
            print(f"📊 處理統計：")
            print(f"   成功處理天數：{processing_result.get('successful_days', 0)}")
            print(f"   失敗天數：{processing_result.get('failed_days', 0)}")
            print(f"   總處理員工數：{processing_result.get('total_employees_processed', 0)}")
            print(f"   總處理記錄數：{processing_result.get('total_records_processed', 0)}")
            
            # 顯示每日處理詳情（前10天）
            daily_results = processing_result.get('daily_results', [])
            if daily_results:
                print(f"\n📋 每日處理詳情（前10天）：")
                for i, daily in enumerate(daily_results[:10], 1):
                    status = "✅" if daily.get('success', False) else "❌"
                    print(f"   {i:2d}. {daily.get('date')} {status} - "
                          f"員工數：{daily.get('employees_processed', 0)}, "
                          f"記錄數：{daily.get('records_processed', 0)}")
                
                if len(daily_results) > 10:
                    print(f"   ... 還有 {len(daily_results) - 10} 天的處理結果")
        else:
            print("❌ 批量處理失敗")
    
    # 步驟4：檢查處理後狀態
    print("\n🔍 步驟4：檢查處理後狀態")
    print("-" * 50)
    
    after_status = check_status_before_after()
    
    # 步驟5：驗證結果
    print("\n✅ 步驟5：驗證測試結果")
    print("-" * 50)
    
    if before_status['total_days_to_process'] > 0:
        processed_days = before_status['total_days_to_process'] - after_status['total_days_to_process']
        
        print(f"📊 處理結果統計：")
        print(f"   處理前需要處理天數：{before_status['total_days_to_process']}")
        print(f"   處理後需要處理天數：{after_status['total_days_to_process']}")
        print(f"   實際處理天數：{processed_days}")
        
        if after_status['total_days_to_process'] == 0:
            print("🎉 測試成功！增量處理功能完美工作")
            print("✅ 系統能夠正確檢測需要處理的日期範圍")
            print("✅ 系統能夠按順序處理考勤記錄")
            print("✅ 處理完成後狀態更新正確")
            print("✅ 所有未處理的打卡記錄都被正確整理成考勤記錄")
        elif processed_days > 0:
            print("⚠️  測試部分成功，處理了部分日期")
            print(f"   剩餘未處理天數：{after_status['total_days_to_process']}")
        else:
            print("❌ 測試失敗，沒有處理任何日期")
    else:
        print("ℹ️  測試完成，但沒有需要處理的日期範圍")
    
    print("\n" + "=" * 70)
    print("🏁 測試完成")
    print("=" * 70)

if __name__ == "__main__":
    main() 