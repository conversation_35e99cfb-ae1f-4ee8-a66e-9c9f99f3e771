# Makefile for Smart Attendance System
# 智慧考勤系統建置工具

.PHONY: help install install-dev setup test test-cov lint format type-check security clean run build docker-build docker-run deploy docs

# 預設目標
.DEFAULT_GOAL := help

# 變數定義
PYTHON := python3
PIP := pip
VENV := .venv
VENV_BIN := $(VENV)/bin
VENV_PYTHON := $(VENV_BIN)/python
VENV_PIP := $(VENV_BIN)/pip

# 顏色定義
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# 檢查虛擬環境是否存在
check-venv:
	@if [ ! -d "$(VENV)" ]; then \
		echo "$(YELLOW)虛擬環境不存在，正在建立...$(NC)"; \
		$(PYTHON) -m venv $(VENV); \
	fi

help: ## 顯示幫助訊息
	@echo "$(GREEN)智慧考勤系統 - 可用指令:$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)範例:$(NC)"
	@echo "  make setup     # 初始化開發環境"
	@echo "  make test      # 執行測試"
	@echo "  make run       # 啟動應用程式"

setup: check-venv ## 初始化開發環境
	@echo "$(GREEN)正在設定開發環境...$(NC)"
	$(VENV_PIP) install --upgrade pip setuptools wheel
	$(VENV_PIP) install -r requirements.txt
	@if [ -f "requirements-dev.txt" ]; then \
		$(VENV_PIP) install -r requirements-dev.txt; \
	fi
	$(VENV_PYTHON) database.py
	@echo "$(GREEN)開發環境設定完成！$(NC)"

install: check-venv ## 安裝生產環境相依套件
	@echo "$(GREEN)安裝生產環境相依套件...$(NC)"
	$(VENV_PIP) install --upgrade pip
	$(VENV_PIP) install -r requirements.txt
	@echo "$(GREEN)安裝完成！$(NC)"

install-dev: check-venv ## 安裝開發環境相依套件
	@echo "$(GREEN)安裝開發環境相依套件...$(NC)"
	$(VENV_PIP) install --upgrade pip setuptools wheel
	$(VENV_PIP) install -r requirements.txt
	@if [ -f "requirements-dev.txt" ]; then \
		$(VENV_PIP) install -r requirements-dev.txt; \
	else \
		$(VENV_PIP) install pytest pytest-cov black flake8 isort mypy pre-commit bandit; \
	fi
	$(VENV_BIN)/pre-commit install
	@echo "$(GREEN)開發環境安裝完成！$(NC)"

test: check-venv ## 執行測試
	@echo "$(GREEN)執行測試...$(NC)"
	@if [ -d "tests" ]; then \
		$(VENV_PYTHON) -m pytest tests/ -v; \
	else \
		echo "$(YELLOW)尚未建立測試目錄，跳過測試$(NC)"; \
	fi

test-cov: check-venv ## 執行測試並生成覆蓋率報告
	@echo "$(GREEN)執行測試並生成覆蓋率報告...$(NC)"
	@if [ -d "tests" ]; then \
		$(VENV_PYTHON) -m pytest tests/ --cov=. --cov-report=html --cov-report=term-missing; \
		echo "$(GREEN)覆蓋率報告已生成到 htmlcov/index.html$(NC)"; \
	else \
		echo "$(YELLOW)尚未建立測試目錄，跳過測試$(NC)"; \
	fi

lint: check-venv ## 執行程式碼檢查
	@echo "$(GREEN)執行程式碼檢查...$(NC)"
	$(VENV_PYTHON) -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	$(VENV_PYTHON) -m flake8 . --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

format: check-venv ## 格式化程式碼
	@echo "$(GREEN)格式化程式碼...$(NC)"
	$(VENV_PYTHON) -m black .
	$(VENV_PYTHON) -m isort .
	@echo "$(GREEN)程式碼格式化完成！$(NC)"

type-check: check-venv ## 執行型別檢查
	@echo "$(GREEN)執行型別檢查...$(NC)"
	$(VENV_PYTHON) -m mypy . --ignore-missing-imports

security: check-venv ## 執行安全性檢查
	@echo "$(GREEN)執行安全性檢查...$(NC)"
	$(VENV_PYTHON) -m bandit -r . -f json -o bandit-report.json || true
	$(VENV_PYTHON) -m bandit -r . --skip B101,B601

quality: lint type-check security ## 執行完整的程式碼品質檢查
	@echo "$(GREEN)程式碼品質檢查完成！$(NC)"

clean: ## 清理暫存檔和建置產物
	@echo "$(GREEN)清理暫存檔...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -f bandit-report.json
	@echo "$(GREEN)清理完成！$(NC)"

run: check-venv ## 啟動應用程式
	@echo "$(GREEN)啟動智慧考勤系統...$(NC)"
	@if [ ! -f "attendance.db" ]; then \
		echo "$(YELLOW)初始化資料庫...$(NC)"; \
		$(VENV_PYTHON) database.py; \
	fi
	$(VENV_PYTHON) app.py

run-dev: check-venv ## 以開發模式啟動應用程式
	@echo "$(GREEN)以開發模式啟動應用程式...$(NC)"
	@export FLASK_ENV=development DEBUG=True && $(VENV_PYTHON) app.py

build: clean ## 建置分發套件
	@echo "$(GREEN)建置分發套件...$(NC)"
	$(VENV_PYTHON) -m build
	@echo "$(GREEN)建置完成！套件位於 dist/ 目錄$(NC)"

# Docker 相關指令
docker-build: ## 建置 Docker 映像
	@echo "$(GREEN)建置 Docker 映像...$(NC)"
	docker build -t smart-attendance-system:latest .
	@echo "$(GREEN)Docker 映像建置完成！$(NC)"

docker-run: ## 執行 Docker 容器
	@echo "$(GREEN)啟動 Docker 容器...$(NC)"
	docker run -p 7072:7072 --name attendance-system smart-attendance-system:latest

docker-stop: ## 停止 Docker 容器
	@echo "$(GREEN)停止 Docker 容器...$(NC)"
	docker stop attendance-system || true
	docker rm attendance-system || true

# 部署相關指令
deploy-check: ## 檢查部署準備
	@echo "$(GREEN)檢查部署準備...$(NC)"
	@if [ ! -f ".env" ]; then \
		echo "$(RED)錯誤: .env 檔案不存在$(NC)"; \
		exit 1; \
	fi
	@if [ ! -f "attendance.db" ]; then \
		echo "$(YELLOW)警告: 資料庫檔案不存在，將在部署時建立$(NC)"; \
	fi
	@echo "$(GREEN)部署檢查通過！$(NC)"

deploy-prod: deploy-check ## 部署到生產環境
	@echo "$(GREEN)部署到生產環境...$(NC)"
	@echo "$(YELLOW)請確保已設定正確的環境變數$(NC)"
	gunicorn -w 4 -b 0.0.0.0:7072 app:app

# 文件相關指令
docs: check-venv ## 生成文件
	@echo "$(GREEN)生成專案文件...$(NC)"
	@if [ -f "mkdocs.yml" ]; then \
		$(VENV_BIN)/mkdocs build; \
		echo "$(GREEN)文件已生成到 site/ 目錄$(NC)"; \
	else \
		echo "$(YELLOW)尚未設定 MkDocs，跳過文件生成$(NC)"; \
	fi

docs-serve: check-venv ## 啟動文件服務
	@echo "$(GREEN)啟動文件服務...$(NC)"
	@if [ -f "mkdocs.yml" ]; then \
		$(VENV_BIN)/mkdocs serve; \
	else \
		echo "$(YELLOW)尚未設定 MkDocs$(NC)"; \
	fi

# 資料庫相關指令
db-init: check-venv ## 初始化資料庫
	@echo "$(GREEN)初始化資料庫...$(NC)"
	$(VENV_PYTHON) database.py
	@echo "$(GREEN)資料庫初始化完成！$(NC)"

db-reset: check-venv ## 重設資料庫
	@echo "$(YELLOW)警告: 這將刪除所有資料！$(NC)"
	@read -p "確定要重設資料庫嗎? [y/N] " confirm && [ "$$confirm" = "y" ]
	rm -f attendance.db
	$(VENV_PYTHON) database.py
	@echo "$(GREEN)資料庫已重設！$(NC)"

db-backup: ## 備份資料庫
	@echo "$(GREEN)備份資料庫...$(NC)"
	@if [ -f "attendance.db" ]; then \
		cp attendance.db "attendance_backup_$$(date +%Y%m%d_%H%M%S).db"; \
		echo "$(GREEN)資料庫備份完成！$(NC)"; \
	else \
		echo "$(RED)資料庫檔案不存在$(NC)"; \
	fi

# 開發工具
pre-commit: check-venv ## 執行 pre-commit 檢查
	@echo "$(GREEN)執行 pre-commit 檢查...$(NC)"
	$(VENV_BIN)/pre-commit run --all-files

install-hooks: check-venv ## 安裝 Git hooks
	@echo "$(GREEN)安裝 Git hooks...$(NC)"
	$(VENV_BIN)/pre-commit install
	@echo "$(GREEN)Git hooks 安裝完成！$(NC)"

# 效能測試
performance-test: check-venv ## 執行效能測試
	@echo "$(GREEN)執行效能測試...$(NC)"
	@echo "$(YELLOW)效能測試功能尚未實作$(NC)"

# 一鍵設定
quick-start: setup run ## 快速開始 (設定 + 啟動)

# 開發者一鍵設定
dev-setup: install-dev install-hooks ## 開發者環境設定
	@echo "$(GREEN)開發環境設定完成！$(NC)"
	@echo ""
	@echo "$(YELLOW)下一步:$(NC)"
	@echo "  1. 編輯 .env 檔案設定環境變數"
	@echo "  2. 執行 'make run' 啟動應用程式"
	@echo "  3. 瀏覽 http://localhost:7072"

# 檢查系統需求
check-requirements: ## 檢查系統需求
	@echo "$(GREEN)檢查系統需求...$(NC)"
	@python3 --version || (echo "$(RED)Python 3 未安裝$(NC)" && exit 1)
	@pip --version || (echo "$(RED)pip 未安裝$(NC)" && exit 1)
	@git --version || (echo "$(RED)Git 未安裝$(NC)" && exit 1)
	@echo "$(GREEN)系統需求檢查通過！$(NC)"