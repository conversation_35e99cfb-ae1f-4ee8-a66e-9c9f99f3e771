from flask import request, g, render_template
from config.company_configs import get_company_config, get_api_version

class SmartRoutingMiddleware:
    """
    智能路由中間件
    根據公司配置自動選擇API版本和UI模板
    """
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """
        初始化中間件
        
        參數：
        app (Flask): Flask應用實例
        """
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """
        請求前處理：設定公司上下文
        """
        # 從多個來源獲取公司ID
        company_id = self.get_company_id()
        
        # 設定全域變數
        g.company_id = company_id
        g.company_config = get_company_config(company_id)
        g.api_version = get_api_version(company_id)
        
        # 動態路由重寫
        self.rewrite_api_routes()
    
    def after_request(self, response):
        """
        請求後處理：添加公司標識頭
        
        參數：
        response (Response): Flask響應對象
        
        返回：
        Response: 修改後的響應對象
        """
        if hasattr(g, 'company_id'):
            response.headers['X-Company-ID'] = g.company_id
            response.headers['X-API-Version'] = g.api_version
        return response
    
    def get_company_id(self):
        """
        從多個來源獲取公司ID
        
        返回：
        str: 公司ID
        """
        # 優先級：Header > Subdomain > Path > Default
        
        # 1. 從Header獲取
        company_id = request.headers.get('X-Company-ID')
        if company_id:
            return company_id
        
        # 2. 從Subdomain獲取
        host = request.host
        if '.' in host and not host.startswith('www'):
            subdomain = host.split('.')[0]
            if subdomain not in ['localhost', '127', '192']:
                return subdomain
        
        # 3. 從路徑獲取
        path_parts = request.path.split('/')
        if len(path_parts) > 2 and path_parts[1] == 'company':
            return path_parts[2]
        
        # 4. 預設值
        return 'default'
    
    def rewrite_api_routes(self):
        """
        動態重寫API路由
        """
        if not request.path.startswith('/api/'):
            return
        
        # 如果已經包含版本號，不需要重寫
        if '/api/v' in request.path:
            return
        
        # 根據公司配置重寫路由
        api_version = g.api_version
        if api_version != 'v1':
            # 重寫請求路徑
            new_path = request.path.replace('/api/', f'/api/{api_version}/')
            request.environ['PATH_INFO'] = new_path

def render_company_template(template_name, **context):
    """
    根據公司配置渲染對應模板
    
    參數：
    template_name (str): 基礎模板名稱
    **context: 模板上下文變數
    
    返回：
    str: 渲染後的HTML
    """
    company_id = g.get('company_id', 'default')
    
    # 嘗試載入公司專用模板
    company_template = f"dynamic/{company_id}_{template_name}"
    
    try:
        return render_template(company_template, **context)
    except:
        # 回退到預設模板
        return render_template(template_name, **context)

def get_database_schema():
    """
    獲取當前公司的資料庫schema
    
    返回：
    str: 資料庫schema名稱
    """
    company_config = g.get('company_config', {})
    return company_config.get('database_schema', 'public')

def apply_company_filters(query, model):
    """
    為查詢添加公司過濾條件
    
    參數：
    query: SQLAlchemy查詢對象
    model: 資料模型類
    
    返回：
    query: 添加過濾條件後的查詢對象
    """
    company_id = g.get('company_id', 'default')
    
    if company_id != 'default' and hasattr(model, 'company_id'):
        query = query.filter(model.company_id == company_id)
    
    return query 