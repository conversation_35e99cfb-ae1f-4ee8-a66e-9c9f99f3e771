#!/usr/bin/env python3
"""
測試 work_date 功能的腳本

此腳本測試：
1. 新增考勤記錄時是否正確設置 work_date
2. 查詢考勤記錄時是否包含 work_date
3. 手動考勤記錄是否支援 work_date
4. 考勤處理器是否正確處理 work_date
"""

import sys
import os
import json
import requests
from datetime import datetime, date, timedelta

# 添加項目根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import create_connection
from services.attendance_processor import AttendanceProcessor

def test_database_work_date():
    """測試資料庫中的 work_date 欄位"""
    print("🔍 測試資料庫 work_date 欄位...")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    # 檢查 work_date 欄位是否存在
    cursor.execute("PRAGMA table_info(attendance)")
    columns = cursor.fetchall()
    work_date_exists = any(col[1] == 'work_date' for col in columns)
    
    if work_date_exists:
        print("✅ work_date 欄位存在於 attendance 表中")
    else:
        print("❌ work_date 欄位不存在於 attendance 表中")
        return False
    
    # 檢查現有記錄的 work_date 設置情況
    cursor.execute("SELECT COUNT(*) as total, COUNT(work_date) as with_work_date FROM attendance")
    result = cursor.fetchone()
    total_records = result[0]
    records_with_work_date = result[1]
    
    print(f"📊 總考勤記錄數: {total_records}")
    print(f"📊 已設置 work_date 的記錄數: {records_with_work_date}")
    
    if total_records > 0 and records_with_work_date == total_records:
        print("✅ 所有現有記錄都已設置 work_date")
    elif total_records > 0:
        print(f"⚠️  有 {total_records - records_with_work_date} 筆記錄未設置 work_date")
    
    # 查看最新的幾筆記錄
    cursor.execute("""
        SELECT id, employee_id, check_in, check_out, work_date, created_at 
        FROM attendance 
        ORDER BY id DESC 
        LIMIT 3
    """)
    
    recent_records = cursor.fetchall()
    print("\n📋 最新的考勤記錄:")
    for record in recent_records:
        print(f"   ID: {record[0]}, 員工: {record[1]}, 工作日期: {record[4]}, 上班: {record[2]}")
    
    conn.close()
    return True

def test_attendance_processor():
    """測試考勤處理器的 work_date 功能"""
    print("\n🔧 測試考勤處理器...")
    
    try:
        processor = AttendanceProcessor()
        
        # 測試工作日期計算
        test_datetime = datetime(2025, 6, 5, 8, 30, 0)  # 2025-06-05 08:30:00
        work_date = processor.get_work_date(test_datetime)
        
        print(f"📅 測試時間: {test_datetime}")
        print(f"📅 計算的工作日期: {work_date}")
        
        if work_date == test_datetime.date():
            print("✅ 工作日期計算正確")
        else:
            print("❌ 工作日期計算錯誤")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 考勤處理器測試失敗: {str(e)}")
        return False

def test_manual_attendance_api():
    """測試手動考勤記錄 API 的 work_date 功能"""
    print("\n🌐 測試手動考勤記錄 API...")
    
    # 測試資料
    test_data = {
        "employee_id": 1,
        "check_in": "2025-06-05 09:00:00",
        "check_out": "2025-06-05 18:00:00",
        "status": "manual",
        "note": "測試 work_date 功能",
        "work_date": "2025-06-05"
    }
    
    try:
        # 假設 API 在本地運行
        response = requests.post(
            "http://localhost:7072/api/attendance/manual",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 手動考勤記錄 API 調用成功")
            print(f"📝 返回結果: {result}")
            
            # 檢查資料庫中的記錄
            conn = create_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, work_date, note 
                FROM attendance 
                WHERE note LIKE '%測試 work_date 功能%'
                ORDER BY id DESC 
                LIMIT 1
            """)
            
            record = cursor.fetchone()
            if record and record[1] == "2025-06-05":
                print("✅ work_date 正確設置在資料庫中")
                
                # 清理測試資料
                cursor.execute("DELETE FROM attendance WHERE id = ?", (record[0],))
                conn.commit()
                print("🧹 測試資料已清理")
            else:
                print("❌ work_date 未正確設置")
            
            conn.close()
            return True
        else:
            print(f"❌ API 調用失敗: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️  無法連接到 API 服務器，跳過 API 測試")
        return True
    except Exception as e:
        print(f"❌ API 測試失敗: {str(e)}")
        return False

def test_attendance_records_api():
    """測試考勤記錄查詢 API 是否包含 work_date"""
    print("\n📊 測試考勤記錄查詢 API...")
    
    try:
        response = requests.get(
            "http://localhost:7072/api/attendance/records?limit=1",
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success") and result.get("records"):
                first_record = result["records"][0]
                
                if "work_date" in first_record:
                    print("✅ 考勤記錄查詢 API 包含 work_date 欄位")
                    print(f"📅 範例 work_date: {first_record['work_date']}")
                    return True
                else:
                    print("❌ 考勤記錄查詢 API 不包含 work_date 欄位")
                    print(f"📋 可用欄位: {list(first_record.keys())}")
                    return False
            else:
                print("⚠️  沒有考勤記錄可供測試")
                return True
        else:
            print(f"❌ API 調用失敗: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️  無法連接到 API 服務器，跳過 API 測試")
        return True
    except Exception as e:
        print(f"❌ API 測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試 work_date 功能")
    print("=" * 50)
    
    tests = [
        ("資料庫 work_date 欄位", test_database_work_date),
        ("考勤處理器", test_attendance_processor),
        ("手動考勤記錄 API", test_manual_attendance_api),
        ("考勤記錄查詢 API", test_attendance_records_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試出現異常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試都通過！work_date 功能正常運作")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查相關功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 