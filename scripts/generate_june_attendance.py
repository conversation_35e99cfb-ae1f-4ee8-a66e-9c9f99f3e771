#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六月份打卡測試資料產生器
包含各種真實情況：正常上下班、遲到、早退、加班、跨日、請假等
"""

import random
import datetime
from datetime import timedelta
import os

# 員工資料（有卡號的員工）
EMPLOYEES = [
    "00000702", "00003494", "00003715", "00000701", "00002169", 
    "00001435", "00003617", "00003701", "00003752", "00003716",
    "00003664", "00000528", "00002154", "00003740", "00000510",
    "00003750", "00000524", "00000519", "00000718", "00003601",
    "00003761", "00003593"
]

# 打卡機編號
MACHINE_IDS = ["011", "012", "013"]

# 工作日設定
WORK_DAYS = [0, 1, 2, 3, 4]  # 週一到週五

def generate_june_attendance():
    """產生六月份打卡資料"""
    records = []
    
    # 六月份日期範圍
    start_date = datetime.date(2025, 6, 1)
    end_date = datetime.date(2025, 6, 30)
    
    current_date = start_date
    while current_date <= end_date:
        weekday = current_date.weekday()
        
        # 只在工作日產生打卡記錄
        if weekday in WORK_DAYS:
            for employee in EMPLOYEES:
                # 85% 機率正常上班
                if random.random() < 0.85:
                    records.extend(generate_daily_records(employee, current_date))
                # 10% 機率請假（無打卡記錄）
                elif random.random() < 0.95:
                    pass  # 請假，無記錄
                # 5% 機率半天班
                else:
                    records.extend(generate_half_day_records(employee, current_date))
        
        current_date += timedelta(days=1)
    
    return records

def generate_daily_records(employee, date):
    """產生單日打卡記錄"""
    records = []
    machine_id = random.choice(MACHINE_IDS)
    
    # 決定工作模式
    work_type = random.choices(
        ['normal', 'late', 'overtime', 'night_shift', 'flexible'],
        weights=[60, 15, 15, 5, 5]
    )[0]
    
    if work_type == 'normal':
        # 正常班：8:00-17:00
        checkin_time = generate_time_around(8, 0, 30)  # 8:00 ± 30分鐘
        checkout_time = generate_time_around(17, 0, 45)  # 17:00 ± 45分鐘
        
        records.append(create_record(machine_id, employee, date, checkin_time))
        records.append(create_record(machine_id, employee, date, checkout_time))
        
    elif work_type == 'late':
        # 遲到：8:30-17:30
        checkin_time = generate_time_around(8, 30, 60)  # 8:30 ± 60分鐘
        checkout_time = generate_time_around(17, 30, 30)
        
        records.append(create_record(machine_id, employee, date, checkin_time))
        records.append(create_record(machine_id, employee, date, checkout_time))
        
    elif work_type == 'overtime':
        # 加班：8:00-20:00+
        checkin_time = generate_time_around(8, 0, 20)
        checkout_time = generate_time_around(20, 0, 120)  # 20:00 ± 2小時
        
        records.append(create_record(machine_id, employee, date, checkin_time))
        records.append(create_record(machine_id, employee, date, checkout_time))
        
    elif work_type == 'night_shift':
        # 夜班：22:00-06:00（跨日）
        checkin_time = generate_time_around(22, 0, 30)
        checkout_time = generate_time_around(6, 0, 30)  # 隔天早上
        
        records.append(create_record(machine_id, employee, date, checkin_time))
        # 下班時間在隔天
        next_date = date + timedelta(days=1)
        records.append(create_record(machine_id, employee, next_date, checkout_time))
        
    elif work_type == 'flexible':
        # 彈性工時：多次進出
        times = [
            generate_time_around(8, 30, 30),   # 上班
            generate_time_around(12, 0, 15),   # 午休出去
            generate_time_around(13, 0, 15),   # 午休回來
            generate_time_around(17, 30, 45)   # 下班
        ]
        
        for time in times:
            records.append(create_record(machine_id, employee, date, time))
    
    return records

def generate_half_day_records(employee, date):
    """產生半天班記錄"""
    records = []
    machine_id = random.choice(MACHINE_IDS)
    
    if random.random() < 0.5:
        # 上午班：8:00-12:00
        checkin_time = generate_time_around(8, 0, 20)
        checkout_time = generate_time_around(12, 0, 30)
    else:
        # 下午班：13:00-17:00
        checkin_time = generate_time_around(13, 0, 20)
        checkout_time = generate_time_around(17, 0, 30)
    
    records.append(create_record(machine_id, employee, date, checkin_time))
    records.append(create_record(machine_id, employee, date, checkout_time))
    
    return records

def generate_time_around(hour, minute, variance_minutes):
    """在指定時間附近產生隨機時間"""
    base_minutes = hour * 60 + minute
    variance = random.randint(-variance_minutes, variance_minutes)
    total_minutes = base_minutes + variance
    
    # 處理跨日情況
    if total_minutes < 0:
        total_minutes += 24 * 60
    elif total_minutes >= 24 * 60:
        total_minutes -= 24 * 60
    
    result_hour = total_minutes // 60
    result_minute = total_minutes % 60
    
    return f"{result_hour:02d}{result_minute:02d}"

def create_record(machine_id, employee, date, time):
    """建立打卡記錄"""
    date_str = date.strftime("%Y%m%d")
    return f"{machine_id},{employee},{date_str},{time},0"

def save_to_file(records, filename):
    """儲存記錄到檔案"""
    # 確保 download 目錄存在
    download_dir = "download"
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    filepath = os.path.join(download_dir, filename)
    
    # 按日期和時間排序
    records.sort(key=lambda x: (x.split(',')[2], x.split(',')[3]))
    
    with open(filepath, 'w', encoding='utf-8') as f:
        for record in records:
            f.write(record + '\n')
    
    return filepath

def main():
    """主程式"""
    print("🚀 開始產生六月份打卡測試資料...")
    
    # 產生打卡記錄
    records = generate_june_attendance()
    
    # 儲存到檔案
    filename = "june_2025_attendance_test.txt"
    filepath = save_to_file(records, filename)
    
    print(f"✅ 成功產生 {len(records)} 筆打卡記錄")
    print(f"📁 檔案儲存位置: {filepath}")
    print(f"👥 涵蓋員工數量: {len(EMPLOYEES)} 位")
    print(f"📅 日期範圍: 2025/06/01 - 2025/06/30")
    
    # 統計資訊
    print("\n📊 記錄統計:")
    dates = set()
    employees_with_records = set()
    
    for record in records:
        parts = record.split(',')
        employee = parts[1]
        date = parts[2]
        
        employees_with_records.add(employee)
        dates.add(date)
    
    print(f"   - 有打卡記錄的員工: {len(employees_with_records)} 位")
    print(f"   - 涵蓋工作日: {len(dates)} 天")
    print(f"   - 平均每日記錄: {len(records) / len(dates):.1f} 筆")
    
    print(f"\n🎯 檔案內容預覽（前10筆）:")
    for i, record in enumerate(records[:10]):
        print(f"   {i+1:2d}. {record}")
    
    if len(records) > 10:
        print(f"   ... 還有 {len(records) - 10} 筆記錄")

if __name__ == "__main__":
    main()