import os

from dotenv import load_dotenv

load_dotenv()


class Config:
    # 資料庫設定
    DATABASE_PATH = os.environ.get("DATABASE_PATH", "../attendance.db")

    # 應用程式設定
    DEBUG = os.environ.get("DEBUG", "False") == "True"
    SECRET_KEY = os.environ.get("SECRET_KEY", "dev_key_please_change_in_production")
    HOST = os.environ.get("HOST", "0.0.0.0")
    PORT = int(os.environ.get("PORT", 7072))

    # API 相關設定
    API_VERSION = "v1"
    AI_API_KEY = os.environ.get("OPENAI_API_KEY")

    # 檔案上傳設定
    UPLOAD_FOLDER = os.environ.get("UPLOAD_FOLDER", "uploads")
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB 上傳限制

    # 日誌設定
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    LOG_FILE = os.environ.get("LOG_FILE", "../app.log")
