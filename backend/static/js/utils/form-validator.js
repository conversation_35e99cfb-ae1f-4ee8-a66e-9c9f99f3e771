/**
 * 表單驗證工具函數庫
 * 整合所有重複的表單驗證邏輯，提供統一的驗證體驗
 * 
 * <AUTHOR> AttendanceOS
 * @version v2005.6.12
 */

class FormValidator {

    /**
     * 驗證規則配置
     */
    static RULES = {
        required: {
            message: '此欄位為必填項目',
            validate: (value) => value !== null && value !== undefined && String(value).trim() !== ''
        },
        email: {
            message: '請輸入有效的電子郵件地址',
            validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
        },
        phone: {
            message: '請輸入有效的電話號碼',
            validate: (value) => /^[\d\-\+\(\)\s]+$/.test(value) && value.replace(/\D/g, '').length >= 8
        },
        idNumber: {
            message: '請輸入有效的身分證字號',
            validate: (value) => /^[A-Z][12]\d{8}$/.test(value)
        },
        password: {
            message: '密碼長度至少6個字符',
            validate: (value) => value && value.length >= 6
        },
        strongPassword: {
            message: '密碼必須包含大小寫字母、數字，長度至少8個字符',
            validate: (value) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/.test(value)
        },
        number: {
            message: '請輸入有效的數字',
            validate: (value) => !isNaN(value) && !isNaN(parseFloat(value))
        },
        positiveNumber: {
            message: '請輸入正數',
            validate: (value) => !isNaN(value) && parseFloat(value) > 0
        },
        integer: {
            message: '請輸入整數',
            validate: (value) => Number.isInteger(Number(value))
        },
        date: {
            message: '請輸入有效的日期',
            validate: (value) => !isNaN(new Date(value).getTime())
        },
        time: {
            message: '請輸入有效的時間格式 (HH:MM)',
            validate: (value) => /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(value)
        },
        minLength: {
            message: '長度不足',
            validate: (value, min) => String(value).length >= min
        },
        maxLength: {
            message: '長度超出限制',
            validate: (value, max) => String(value).length <= max
        },
        min: {
            message: '數值過小',
            validate: (value, min) => Number(value) >= min
        },
        max: {
            message: '數值過大',
            validate: (value, max) => Number(value) <= max
        }
    };

    /**
     * 驗證單個欄位
     * @param {any} value - 欄位值
     * @param {Array|string} rules - 驗證規則
     * @param {string} fieldName - 欄位名稱
     * @returns {Object} 驗證結果
     */
    static validateField(value, rules, fieldName = '欄位') {
        const errors = [];

        // 統一處理規則格式
        const ruleList = Array.isArray(rules) ? rules : [rules];

        for (const rule of ruleList) {
            let ruleName, ruleParams;

            if (typeof rule === 'string') {
                ruleName = rule;
                ruleParams = [];
            } else if (typeof rule === 'object') {
                ruleName = rule.name || rule.rule;
                ruleParams = rule.params || [];
            } else {
                continue;
            }

            const ruleConfig = this.RULES[ruleName];
            if (!ruleConfig) {
                console.warn(`未知的驗證規則: ${ruleName}`);
                continue;
            }

            // 如果是必填規則，直接驗證
            if (ruleName === 'required') {
                if (!ruleConfig.validate(value)) {
                    errors.push(`${fieldName}${ruleConfig.message}`);
                }
                continue;
            }

            // 如果值為空且不是必填，跳過其他驗證
            if (!value && value !== 0) {
                continue;
            }

            // 執行驗證
            if (!ruleConfig.validate(value, ...ruleParams)) {
                const message = rule.message ||
                    (ruleParams.length > 0 ?
                        `${fieldName}${ruleConfig.message}（${ruleParams.join(', ')}）` :
                        `${fieldName}${ruleConfig.message}`);
                errors.push(message);
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            value: value
        };
    }

    /**
     * 驗證整個表單
     * @param {Object} formData - 表單數據
     * @param {Object} validationSchema - 驗證規則配置
     * @returns {Object} 驗證結果
     */
    static validateForm(formData, validationSchema) {
        const results = {};
        const allErrors = [];
        let isValid = true;

        for (const [fieldName, rules] of Object.entries(validationSchema)) {
            const fieldValue = formData[fieldName];
            const fieldResult = this.validateField(fieldValue, rules, fieldName);

            results[fieldName] = fieldResult;

            if (!fieldResult.isValid) {
                isValid = false;
                allErrors.push(...fieldResult.errors);
            }
        }

        return {
            isValid: isValid,
            errors: allErrors,
            fieldResults: results,
            data: formData
        };
    }

    /**
     * 即時驗證欄位（用於輸入時驗證）
     * @param {HTMLElement} element - 表單元素
     * @param {Array|string} rules - 驗證規則
     * @param {Object} options - 選項
     */
    static validateFieldRealtime(element, rules, options = {}) {
        const fieldName = options.fieldName || element.name || element.id || '欄位';
        const showSuccess = options.showSuccess !== false;
        const debounceTime = options.debounceTime || 300;

        // 清除之前的定時器
        if (element._validationTimer) {
            clearTimeout(element._validationTimer);
        }

        // 設置新的定時器
        element._validationTimer = setTimeout(() => {
            const result = this.validateField(element.value, rules, fieldName);
            this.displayFieldValidation(element, result, { showSuccess });
        }, debounceTime);
    }

    /**
     * 顯示欄位驗證結果
     * @param {HTMLElement} element - 表單元素
     * @param {Object} result - 驗證結果
     * @param {Object} options - 選項
     */
    static displayFieldValidation(element, result, options = {}) {
        const showSuccess = options.showSuccess !== false;
        const container = element.closest('.form-group') || element.parentElement;

        // 移除之前的驗證樣式和訊息
        this.clearFieldValidation(element);

        if (result.isValid) {
            if (showSuccess) {
                element.classList.add('border-green-500', 'focus:border-green-500');
                element.classList.remove('border-red-500', 'focus:border-red-500');

                // 添加成功圖標
                this.addValidationIcon(container, 'check-circle', 'text-green-500');
            }
        } else {
            element.classList.add('border-red-500', 'focus:border-red-500');
            element.classList.remove('border-green-500', 'focus:border-green-500');

            // 添加錯誤圖標
            this.addValidationIcon(container, 'x-circle', 'text-red-500');

            // 顯示錯誤訊息
            this.addErrorMessage(container, result.errors[0]);
        }
    }

    /**
     * 清除欄位驗證樣式
     * @param {HTMLElement} element - 表單元素
     */
    static clearFieldValidation(element) {
        const container = element.closest('.form-group') || element.parentElement;

        // 移除驗證樣式
        element.classList.remove(
            'border-green-500', 'focus:border-green-500',
            'border-red-500', 'focus:border-red-500'
        );

        // 移除驗證圖標
        const icon = container.querySelector('.validation-icon');
        if (icon) icon.remove();

        // 移除錯誤訊息
        const errorMsg = container.querySelector('.validation-error');
        if (errorMsg) errorMsg.remove();
    }

    /**
     * 添加驗證圖標
     * @param {HTMLElement} container - 容器元素
     * @param {string} iconName - 圖標名稱
     * @param {string} colorClass - 顏色類別
     */
    static addValidationIcon(container, iconName, colorClass) {
        const existingIcon = container.querySelector('.validation-icon');
        if (existingIcon) existingIcon.remove();

        const icon = document.createElement('div');
        icon.className = `validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 ${colorClass}`;
        icon.innerHTML = `<i data-lucide="${iconName}" class="w-4 h-4"></i>`;

        // 確保容器是相對定位
        if (getComputedStyle(container).position === 'static') {
            container.style.position = 'relative';
        }

        container.appendChild(icon);

        // 重新創建圖標
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * 添加錯誤訊息
     * @param {HTMLElement} container - 容器元素
     * @param {string} message - 錯誤訊息
     */
    static addErrorMessage(container, message) {
        const existingError = container.querySelector('.validation-error');
        if (existingError) existingError.remove();

        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error text-red-500 text-xs mt-1';
        errorDiv.textContent = message;

        container.appendChild(errorDiv);
    }

    /**
     * 設置表單即時驗證
     * @param {HTMLFormElement} form - 表單元素
     * @param {Object} validationSchema - 驗證規則配置
     * @param {Object} options - 選項
     */
    static setupFormValidation(form, validationSchema, options = {}) {
        const validateOnInput = options.validateOnInput !== false;
        const validateOnBlur = options.validateOnBlur !== false;
        const submitCallback = options.onSubmit;

        // 為每個欄位設置驗證
        for (const [fieldName, rules] of Object.entries(validationSchema)) {
            const element = form.querySelector(`[name="${fieldName}"]`) ||
                form.querySelector(`#${fieldName}`);

            if (!element) continue;

            // 輸入時驗證
            if (validateOnInput) {
                element.addEventListener('input', () => {
                    this.validateFieldRealtime(element, rules, { fieldName });
                });
            }

            // 失去焦點時驗證
            if (validateOnBlur) {
                element.addEventListener('blur', () => {
                    const result = this.validateField(element.value, rules, fieldName);
                    this.displayFieldValidation(element, result);
                });
            }
        }

        // 表單提交驗證
        form.addEventListener('submit', (e) => {
            e.preventDefault();

            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            const result = this.validateForm(data, validationSchema);

            // 顯示所有欄位的驗證結果
            for (const [fieldName, fieldResult] of Object.entries(result.fieldResults)) {
                const element = form.querySelector(`[name="${fieldName}"]`) ||
                    form.querySelector(`#${fieldName}`);
                if (element) {
                    this.displayFieldValidation(element, fieldResult);
                }
            }

            if (result.isValid) {
                if (submitCallback) {
                    submitCallback(result.data, result);
                }
            } else {
                // 顯示錯誤通知
                if (window.NotificationSystem) {
                    NotificationSystem.error(`表單驗證失敗：${result.errors[0]}`);
                }

                // 聚焦到第一個錯誤欄位
                const firstErrorField = Object.keys(result.fieldResults)
                    .find(fieldName => !result.fieldResults[fieldName].isValid);

                if (firstErrorField) {
                    const element = form.querySelector(`[name="${firstErrorField}"]`) ||
                        form.querySelector(`#${firstErrorField}`);
                    if (element) element.focus();
                }
            }
        });
    }

    /**
     * 創建自定義驗證規則
     * @param {string} name - 規則名稱
     * @param {Function} validator - 驗證函數
     * @param {string} message - 錯誤訊息
     */
    static addCustomRule(name, validator, message) {
        this.RULES[name] = {
            message: message,
            validate: validator
        };
    }

    /**
     * 驗證員工ID格式
     * @param {string} employeeId - 員工ID
     * @returns {boolean} 是否有效
     */
    static validateEmployeeId(employeeId) {
        // 員工ID格式：字母開頭，後跟數字，總長度4-8位
        return /^[A-Z][0-9]{3,7}$/.test(employeeId);
    }

    /**
     * 驗證部門代碼格式
     * @param {string} deptCode - 部門代碼
     * @returns {boolean} 是否有效
     */
    static validateDepartmentCode(deptCode) {
        // 部門代碼格式：2-4位大寫字母
        return /^[A-Z]{2,4}$/.test(deptCode);
    }

    /**
     * 驗證請假時間範圍
     * @param {string} startDate - 開始日期
     * @param {string} endDate - 結束日期
     * @param {string} startTime - 開始時間
     * @param {string} endTime - 結束時間
     * @returns {Object} 驗證結果
     */
    static validateLeaveTimeRange(startDate, endDate, startTime = null, endTime = null) {
        const errors = [];

        // 使用 LeaveCalculator 的驗證功能
        if (window.LeaveCalculator) {
            const result = LeaveCalculator.validateLeaveTime(startDate, endDate, startTime, endTime);
            return result;
        }

        // 基本驗證
        if (!startDate) errors.push('請選擇開始日期');
        if (!endDate) errors.push('請選擇結束日期');

        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);

            if (start > end) {
                errors.push('開始日期不能晚於結束日期');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 批量清除表單驗證
     * @param {HTMLFormElement} form - 表單元素
     */
    static clearFormValidation(form) {
        const elements = form.querySelectorAll('input, select, textarea');
        elements.forEach(element => {
            this.clearFieldValidation(element);
        });
    }

    /**
     * 獲取表單數據
     * @param {HTMLFormElement} form - 表單元素
     * @returns {Object} 表單數據
     */
    static getFormData(form) {
        const formData = new FormData(form);
        const data = {};

        for (const [key, value] of formData.entries()) {
            // 處理多選框
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }

        return data;
    }
}

// 註冊常用的自定義驗證規則
FormValidator.addCustomRule('employeeId', FormValidator.validateEmployeeId, '員工ID格式無效');
FormValidator.addCustomRule('deptCode', FormValidator.validateDepartmentCode, '部門代碼格式無效');

// 導出為全域變數
window.FormValidator = FormValidator;

// 提供簡化的全域函數（向後兼容）
window.validateField = function (value, rules, fieldName) {
    return FormValidator.validateField(value, rules, fieldName);
};

window.validateForm = function (formData, schema) {
    return FormValidator.validateForm(formData, schema);
};

window.setupFormValidation = function (form, schema, options) {
    return FormValidator.setupFormValidation(form, schema, options);
};