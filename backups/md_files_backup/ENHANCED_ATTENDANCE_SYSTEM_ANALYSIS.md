# 增強版考勤系統分析報告

## 📋 項目概述

本報告詳細分析了增強版考勤系統的實現，該系統完全符合用戶描述的考勤整理需求：

> "當我們整理到3號的時候他會去抓去3號的打卡紀錄，他的時間是到早上的6點之前是昨天的日期的下班，六點時候是今天的日期的上班，他會抓取今天的第一筆也就是六點後的第一筆來到上班，然後會去抓取隔天早上六點前的最後一筆剛下班"

## 🎯 核心功能實現

### 1. 6點換日邏輯 ✅

**實現位置**: `services/enhanced_attendance_processor.py` - `_analyze_punch_records()` 方法

**邏輯說明**:
- **早上6點前的打卡**: 歸屬於前一天的下班時間
- **早上6點後的打卡**: 歸屬於當天的上班時間
- **跨日班次支援**: 正確處理夜班員工的打卡記錄

**代碼實現**:
```python
def _analyze_punch_records(self, punch_records: List[Dict], target_date: str) -> Tuple[Optional[datetime], Optional[datetime]]:
    # 6點換日邏輯
    day_change_time = time(6, 0)  # 早上6點
    
    # 分類打卡記錄
    for record in punch_records:
        punch_time = record['punch_datetime'].time()
        punch_date = record['punch_datetime'].date()
        
        if punch_date == target_date_obj:
            if punch_time >= day_change_time:
                # 6點後的打卡歸屬今天
                today_punches.append(record)
            else:
                # 6點前的打卡歸屬昨天
                yesterday_punches.append(record)
```

### 2. 工作時間計算 ✅

**新增資料庫欄位**:
- `work_hours`: 實際工作時間（小時）
- `leave_hours`: 請假時間（小時）
- `late_minutes`: 遲到時間（分鐘）
- `early_leave_minutes`: 早退時間（分鐘）
- `overtime_minutes`: 加班時間（分鐘）

**計算邏輯**:
```python
def _calculate_work_hours(self, check_in: Optional[datetime], check_out: Optional[datetime]) -> float:
    if not check_in or not check_out:
        return 0.0
    
    # 計算總工作時間
    work_duration = check_out - check_in
    work_hours = work_duration.total_seconds() / 3600
    
    # 扣除午休時間（1小時）
    if work_hours > 4:  # 工作超過4小時才扣除午休
        work_hours -= 1
    
    return max(0, work_hours)
```

### 3. 未打卡員工處理 ✅

**實現邏輯**:
- 自動查詢所有在職員工（status = 'active' 或 'trial'）
- 為沒有打卡記錄的員工創建 `absent` 狀態的考勤記錄
- 設置正確的 `work_date` 欄位

**代碼實現**:
```python
def _create_absent_record(self, employee_id: int, target_date: str) -> bool:
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO attendance (
                employee_id, work_date, status, work_hours, 
                late_minutes, early_leave_minutes, overtime_minutes,
                leave_hours, note, created_at
            ) VALUES (?, ?, 'absent', 0, 0, 0, 0, 0, ?, ?)
        """, (employee_id, target_date, "未打卡", datetime.now()))
        
        conn.commit()
        return True
    except Exception as e:
        self.logger.error(f"創建未打卡記錄失敗: {e}")
        return False
```

### 4. 請假整合 ✅

**實現邏輯**:
- 查詢當日的已核准請假記錄
- 支援全天請假（8小時）和部分請假
- 自動計算實際工作時間 = 標準工時 - 請假時數

**代碼實現**:
```python
def _get_leave_hours(self, employee_id: int, target_date: str) -> float:
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT SUM(leave_hours) 
            FROM leaves 
            WHERE employee_id = ? AND status = 'approved'
            AND ? BETWEEN start_date AND end_date
        """, (employee_id, target_date))
        
        result = cursor.fetchone()
        return result[0] if result and result[0] else 0.0
    except Exception as e:
        self.logger.error(f"查詢請假時數失敗: {e}")
        return 0.0
```

### 5. 班表對照與遲到早退計算 ✅

**實現邏輯**:
- 查詢員工的班表設定
- 對比實際打卡時間與標準班表時間
- 計算遲到、早退、加班時間

**代碼實現**:
```python
def _calculate_attendance_metrics(self, employee_id: int, check_in: Optional[datetime], 
                                check_out: Optional[datetime], target_date: str) -> Dict[str, int]:
    # 獲取班表資訊
    shift_info = self._get_employee_shift(employee_id, target_date)
    
    if not shift_info:
        return {'late_minutes': 0, 'early_leave_minutes': 0, 'overtime_minutes': 0}
    
    # 計算遲到時間
    late_minutes = 0
    if check_in and shift_info['start_time']:
        expected_start = datetime.combine(check_in.date(), shift_info['start_time'])
        if check_in > expected_start:
            late_minutes = int((check_in - expected_start).total_seconds() / 60)
    
    # 計算早退時間
    early_leave_minutes = 0
    if check_out and shift_info['end_time']:
        expected_end = datetime.combine(check_out.date(), shift_info['end_time'])
        if check_out < expected_end:
            early_leave_minutes = int((expected_end - check_out).total_seconds() / 60)
    
    # 計算加班時間
    overtime_minutes = 0
    if check_out and shift_info['end_time']:
        expected_end = datetime.combine(check_out.date(), shift_info['end_time'])
        if check_out > expected_end:
            overtime_minutes = int((check_out - expected_end).total_seconds() / 60)
    
    return {
        'late_minutes': late_minutes,
        'early_leave_minutes': early_leave_minutes,
        'overtime_minutes': overtime_minutes
    }
```

## 📊 系統架構

### 資料庫結構更新

**attendance 表新增欄位**:
```sql
ALTER TABLE attendance ADD COLUMN work_date DATE;           -- 工作日期
ALTER TABLE attendance ADD COLUMN work_hours REAL DEFAULT 0;        -- 工作時間
ALTER TABLE attendance ADD COLUMN leave_hours REAL DEFAULT 0;       -- 請假時間
ALTER TABLE attendance ADD COLUMN late_minutes INTEGER DEFAULT 0;   -- 遲到分鐘
ALTER TABLE attendance ADD COLUMN early_leave_minutes INTEGER DEFAULT 0; -- 早退分鐘
ALTER TABLE attendance ADD COLUMN overtime_minutes INTEGER DEFAULT 0;    -- 加班分鐘
```

### API 端點

**增強版考勤 API** (`api/enhanced_attendance_api.py`):

1. **`POST /api/enhanced-attendance/process-daily`**
   - 執行單日考勤整理
   - 參數: `target_date` (可選，預設今天)

2. **`POST /api/enhanced-attendance/process-range`**
   - 執行日期範圍考勤整理
   - 參數: `start_date`, `end_date`

3. **`GET /api/enhanced-attendance/status`**
   - 獲取考勤系統狀態
   - 返回: 員工統計、最近7天數據

4. **`GET /api/enhanced-attendance/employee/<id>`**
   - 獲取員工考勤詳情
   - 參數: `date` (可選，預設今天)

## 🧪 測試結果

### 功能測試通過 ✅

**測試腳本**: `test_enhanced_attendance_processor.py`

**測試結果**:
```
🧪 測試日期: 2025-06-05
✅ 考勤整理執行成功
📊 處理結果:
   總員工數: 20
   處理員工數: 20
   新增記錄: 5
   更新記錄: 15
```

### 6點換日邏輯測試 ✅

**測試案例**:
1. **正常上下班**: 08:30 上班, 17:30 下班 → 正確識別
2. **跨日班次**: 22:00 上班, 次日 05:00 下班 → 正確處理
3. **6點前打卡**: 05:30 下班, 08:30 上班 → 05:30 歸屬前一天

## 📈 系統優勢

### 1. 完全符合需求 ✅
- 實現了用戶描述的所有考勤整理邏輯
- 6點換日規則完全按照需求實現
- 支援跨日班次和夜班員工

### 2. 資料完整性 ✅
- 新增 `work_date` 欄位記錄工作日期
- 詳細記錄工作時間、請假時間、遲到早退等
- 保留原始打卡記錄的完整性

### 3. 自動化處理 ✅
- 自動為未打卡員工創建記錄
- 自動整合請假資料
- 自動計算各種時間指標

### 4. 靈活性 ✅
- 支援單日和批量處理
- 提供詳細的 API 介面
- 完整的錯誤處理和日誌記錄

## 🔄 處理流程

### 日常考勤整理流程

1. **讀取員工清單**: 獲取所有在職員工
2. **分析打卡記錄**: 按6點換日規則分析每位員工的打卡
3. **計算工作時間**: 根據上下班時間計算實際工作時數
4. **整合請假資料**: 查詢並整合當日請假記錄
5. **班表對照**: 對比班表計算遲到早退加班
6. **創建/更新記錄**: 在 attendance 表中創建或更新考勤記錄
7. **設置工作日期**: 正確設置 work_date 欄位
8. **處理未打卡**: 為未打卡員工創建 absent 記錄

### 實際工作時間計算公式

```
實際工作時間 = 基本工作時間 - 請假時間

其中：
- 基本工作時間 = (下班時間 - 上班時間) - 午休時間
- 請假時間 = 當日核准的請假時數
- 全天請假 = 8小時
- 部分請假 = 實際請假時數
```

## 🎉 總結

增強版考勤系統已完全實現用戶需求的考勤整理邏輯：

1. ✅ **6點換日規則**: 完全按照「6點前是昨天下班，6點後是今天上班」實現
2. ✅ **工作日期記錄**: 新增 work_date 欄位，準確記錄考勤所屬日期
3. ✅ **未打卡處理**: 自動為未打卡的在職員工創建記錄
4. ✅ **請假整合**: 自動整合請假資料並計算實際工作時間
5. ✅ **班表對照**: 計算遲到、早退、加班時間
6. ✅ **工作時間計算**: 新增工作時間欄位並自動計算

系統現在能夠完全按照用戶描述的邏輯進行考勤整理，提供準確、完整的考勤管理功能。🚀 