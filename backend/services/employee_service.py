"""
員工業務邏輯服務
"""

from database import create_connection
from datetime import datetime
import sqlite3

class EmployeeService:
    """員工服務類"""
    
    def __init__(self):
        self.conn = None
    
    def get_connection(self):
        """獲取資料庫連接"""
        if not self.conn:
            self.conn = create_connection()
        return self.conn
    
    def get_employees(self, filters=None):
        """
        獲取員工列表
        
        參數：
        filters (dict): 篩選條件
        
        返回：
        dict: 員工列表和統計資訊
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 基本查詢
            base_query = """
                SELECT e.*, d.name as department_name, p.name as position_name
                FROM employees e
                LEFT JOIN departments d ON e.department_id = d.id
                LEFT JOIN positions p ON e.position_id = p.id
            """
            
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('department_id'):
                    where_conditions.append("e.department_id = ?")
                    params.append(filters['department_id'])
                
                if filters.get('status'):
                    where_conditions.append("e.status = ?")
                    params.append(filters['status'])
                
                if filters.get('search'):
                    where_conditions.append("(e.name LIKE ? OR e.employee_id LIKE ?)")
                    search_term = f"%{filters['search']}%"
                    params.extend([search_term, search_term])
            
            if where_conditions:
                base_query += " WHERE " + " AND ".join(where_conditions)
            
            # 計算總數
            count_query = f"SELECT COUNT(*) FROM ({base_query})"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分頁
            page = filters.get('page', 1) if filters else 1
            per_page = filters.get('per_page', 20) if filters else 20
            offset = (page - 1) * per_page
            
            base_query += " ORDER BY e.created_at DESC LIMIT ? OFFSET ?"
            params.extend([per_page, offset])
            
            cursor.execute(base_query, params)
            employees = []
            
            for row in cursor.fetchall():
                employee = {
                    'id': row[0],
                    'employee_id': row[1],
                    'name': row[2],
                    'email': row[3],
                    'phone': row[4],
                    'department_id': row[5],
                    'position_id': row[6],
                    'hire_date': row[7],
                    'status': row[8],
                    'created_at': row[9],
                    'department_name': row[10],
                    'position_name': row[11]
                }
                employees.append(employee)
            
            # 統計資訊
            stats_query = """
                SELECT status, COUNT(*) as count
                FROM employees
                GROUP BY status
            """
            cursor.execute(stats_query)
            status_breakdown = {}
            for row in cursor.fetchall():
                status_breakdown[row[0]] = row[1]
            
            # 確保所有狀態都有值
            for status in ['active', 'inactive', 'terminated']:
                if status not in status_breakdown:
                    status_breakdown[status] = 0
            
            return {
                'success': True,
                'employees': employees,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': (total_count + per_page - 1) // per_page,
                    'has_prev': page > 1,
                    'has_next': page * per_page < total_count
                },
                'statistics': {
                    'total_employees': total_count,
                    'status_breakdown': status_breakdown
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"獲取員工列表失敗: {str(e)}"
            }
    
    def get_employee(self, employee_id):
        """
        獲取單個員工詳情
        
        參數：
        employee_id (int): 員工ID
        
        返回：
        dict: 員工詳情
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT e.*, d.name as department_name, p.name as position_name
                FROM employees e
                LEFT JOIN departments d ON e.department_id = d.id
                LEFT JOIN positions p ON e.position_id = p.id
                WHERE e.id = ?
            """
            
            cursor.execute(query, (employee_id,))
            row = cursor.fetchone()
            
            if not row:
                return {
                    'success': False,
                    'error': '員工不存在'
                }
            
            employee = {
                'id': row[0],
                'employee_id': row[1],
                'name': row[2],
                'email': row[3],
                'phone': row[4],
                'department_id': row[5],
                'position_id': row[6],
                'hire_date': row[7],
                'status': row[8],
                'created_at': row[9],
                'department_name': row[10],
                'position_name': row[11]
            }
            
            return {
                'success': True,
                'data': employee
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"獲取員工詳情失敗: {str(e)}"
            }
    
    def create_employee(self, data):
        """
        創建新員工
        
        參數：
        data (dict): 員工資料
        
        返回：
        dict: 創建結果
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 驗證必要欄位
            required_fields = ['employee_id', 'name', 'email']
            for field in required_fields:
                if field not in data or not data[field]:
                    return {
                        'success': False,
                        'error': f'缺少必要欄位: {field}'
                    }
            
            # 檢查員工編號是否已存在
            cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
            if cursor.fetchone():
                return {
                    'success': False,
                    'error': '員工編號已存在'
                }
            
            # 檢查郵箱是否已存在
            cursor.execute("SELECT id FROM employees WHERE email = ?", (data['email'],))
            if cursor.fetchone():
                return {
                    'success': False,
                    'error': '郵箱已存在'
                }
            
            # 創建員工
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                INSERT INTO employees 
                (employee_id, name, email, phone, department_id, position_id, hire_date, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?)
            """, (
                data['employee_id'],
                data['name'],
                data['email'],
                data.get('phone', ''),
                data.get('department_id'),
                data.get('position_id'),
                data.get('hire_date', now.split(' ')[0]),
                now
            ))
            
            employee_id = cursor.lastrowid
            conn.commit()
            
            return {
                'success': True,
                'data': {
                    'id': employee_id,
                    'employee_id': data['employee_id'],
                    'name': data['name'],
                    'message': '員工創建成功'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"創建員工失敗: {str(e)}"
            }
    
    def get_managers(self):
        """
        獲取管理者列表
        
        返回：
        dict: 管理者列表
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 查詢管理者（假設職位名稱包含 '主管' 或 '經理' 的為管理者）
            query = """
                SELECT e.id, e.employee_id, e.name, d.name as department_name, p.name as position_name
                FROM employees e
                LEFT JOIN departments d ON e.department_id = d.id
                LEFT JOIN positions p ON e.position_id = p.id
                WHERE e.status = 'active' 
                AND (p.name LIKE '%主管%' OR p.name LIKE '%經理%' OR p.name LIKE '%總監%' OR p.name LIKE '%長%')
                ORDER BY e.name
            """
            
            cursor.execute(query)
            managers = []
            
            for row in cursor.fetchall():
                manager = {
                    'id': row[0],
                    'employee_id': row[1],
                    'name': row[2],
                    'department_name': row[3],
                    'position_name': row[4]
                }
                managers.append(manager)
            
            return {
                'success': True,
                'data': managers
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"獲取管理者列表失敗: {str(e)}"
            }

# 創建全局實例
employee_service = EmployeeService() 