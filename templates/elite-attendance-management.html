<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤作業管理 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- 設計系統CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/design-system.css', v=timestamp) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/colors.css', v=timestamp) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/typography.css', v=timestamp) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/buttons.css', v=timestamp) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/forms.css', v=timestamp) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css', v=timestamp) }}">
    <style>
        /* 強制隱藏任何包含 "true" 或 "false" 的獨立文字元素 */
        
        body>*:last-child {
            display: none !important;
        }
        
        body>*:nth-last-child(2) {
            display: none !important;
        }
        /* 更精確的隱藏規則 */
        
        *[data-test="true"],
        *[data-test="false"],
        *:contains("true"):not(table):not(tbody):not(tr):not(td):not(th):not(button):not(select):not(option):not(input),
        *:contains("false"):not(table):not(tbody):not(tr):not(td):not(th):not(button):not(select):not(option):not(input) {
            display: none !important;
        }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>



    <style>
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e5e7eb;
        }
        
        .calendar-cell {
            background: white;
            min-height: 120px;
            padding: 8px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .calendar-cell:hover {
            background: #f8fafc;
        }
        
        .calendar-cell.today {
            background: #eff6ff;
            border: 2px solid #3b82f6;
        }
        
        .shift-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin: 1px 0;
            display: block;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .shift-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .morning-shift {
            background: #dbeafe;
            color: #1e40af;
            border-left: 3px solid #3b82f6;
        }
        
        .evening-shift {
            background: #fef3c7;
            color: #92400e;
            border-left: 3px solid #f59e0b;
        }
        
        .night-shift {
            background: #f3e8ff;
            color: #7c2d12;
            border-left: 3px solid #8b5cf6;
        }
        
        .standard-shift {
            background: #ecfdf5;
            color: #065f46;
            border-left: 3px solid #10b981;
        }
        
        .flexible-shift {
            background: #fdf2f8;
            color: #9d174d;
            border-left: 3px solid #ec4899;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        /* 固定表頭樣式 */
        
        .table-wrapper {
            position: relative;
            border-radius: 16px;
            border: none;
            overflow: hidden;
            background: white;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        }
        
        .table-container {
            position: relative;
            overflow: visible;
        }
        
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 20;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            backdrop-filter: blur(12px);
            border-bottom: none;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
        }
        
        .sticky-header th {
            background: transparent;
            font-weight: 700;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: 15px;
            padding: 20px 16px;
            border-bottom: none;
            white-space: nowrap;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        /* 確保表格在頁面滾動時表頭能固定 */
        
        body {
            overflow-x: hidden;
        }
        
        .main-content {
            position: relative;
            z-index: 1;
            animation: fadeInUp 0.6s ease-out;
        }
        /* 頁面載入動畫 */
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* 卡片懸停效果增強 */
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        /* 表格行樣式優化 */
        
        .table-row {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            background: rgba(255, 255, 255, 0.8);
        }
        
        .table-row:hover {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9));
            transform: translateX(3px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.08);
        }
        
        .table-row:last-child {
            border-bottom: none;
        }
        /* 異常記錄樣式 - 只顯示紅色底線 */
        
        .abnormal-record {
            border-bottom: 1px solid #ef4444 !important;
            position: relative;
        }
        
        .abnormal-record:hover {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
        }
        /* 班表按鈕樣式 */
        
        .shift-button {
            background: linear-gradient(135deg, #7c6df2, #6d4de6);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
            text-align: center;
        }
        
        .shift-button:hover {
            background: linear-gradient(135deg, #6d4de6, #5b3dd9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 109, 242, 0.3);
        }
        
        .shift-button:active {
            transform: translateY(0);
        }
        /* 班表選項樣式 */
        
        .shift-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .shift-option:hover {
            border-color: #7c6df2;
            background: #f8fafc;
            transform: translateX(4px);
        }
        
        .shift-option.selected {
            border-color: #7c6df2;
            background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-sans min-h-screen">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20 main-content">
        <!-- 頁面標題 -->
        <div class="mb-8 relative">
            <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-8 text-white shadow-2xl">
                <div class="relative z-10">
                    <h1 class="text-4xl font-bold mb-3 text-white">考勤作業管理</h1>
                    <p class="text-indigo-100 text-lg font-medium">管理員工每日考勤狀況，包含上下班時間、加班、遲到早退、請假等統計</p>
                </div>
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl"></div>
            </div>
        </div>

        <!-- 查詢條件 -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-6 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">員工</label>
                    <select id="employeeSelect" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有員工</option>
                        <!-- 動態載入員工選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">部門</label>
                    <select id="departmentSelect" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有部門</option>
                        <!-- 動態載入部門選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">開始日期</label>
                    <input type="date" id="startDate" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                    <input type="date" id="endDate" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">考勤狀態</label>
                    <select id="statusSelect" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有狀態</option>
                        <option value="normal">正常</option>
                        <option value="late">遲到</option>
                        <option value="early_leave">早退</option>
                        <option value="overtime">加班</option>
                        <option value="leave">請假</option>
                        <option value="absent">曠職</option>
                        <option value="abnormal">異常</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button id="searchBtn" class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="search" class="w-4 h-4"></i>
                        <span>查詢</span>
                    </button>
                </div>
            </div>

            <!-- 快速選擇 -->
            <div class="mt-4 flex flex-wrap gap-2">
                <button class="quick-date-btn text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                    data-days="0">今天</button>
                <button class="quick-date-btn text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                    data-days="7">本週</button>
                <button class="quick-date-btn text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                    data-days="30">本月</button>
                <button class="quick-date-btn text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                    data-days="1">昨天</button>
                <button class="quick-date-btn text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                    data-days="14">上週</button>
            </div>
        </div>

        <!-- 統計摘要 -->
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-success-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-5 h-5 text-success-600"></i>
                    </div>
                    <div>
                        <p id="normalCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">正常</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-warning-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-5 h-5 text-warning-600"></i>
                    </div>
                    <div>
                        <p id="lateCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">遲到</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-error-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock-alert" class="w-5 h-5 text-error-600"></i>
                    </div>
                    <div>
                        <p id="earlyLeaveCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">早退</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-brand-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="plus-circle" class="w-5 h-5 text-brand-600"></i>
                    </div>
                    <div>
                        <p id="overtimeCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">加班</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar-x" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div>
                        <p id="leaveCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">請假</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="x-circle" class="w-5 h-5 text-red-600"></i>
                    </div>
                    <div>
                        <p id="absentCount" class="text-xl font-bold text-gray-900">0</p>
                        <p class="text-xs text-gray-500">曠職</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具欄 -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">考勤作業列表</h2>
                    <span class="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm" id="recordCount">0 筆記錄</span>
                </div>

                <div class="flex items-center space-x-3">
                    <button id="refreshBtn" class="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-sm hover:shadow-md transform hover:scale-105">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>重新整理</span>
                    </button>
                    <button id="generateBtn" class="bg-gradient-to-r from-emerald-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="calculator" class="w-4 h-4"></i>
                        <span>重新計算</span>
                    </button>
                    <button id="exportBtn" class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>匯出Excel</span>
                    </button>
                    <button id="exportPdfBtn" class="bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-red-600 hover:to-pink-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="file-text" class="w-4 h-4"></i>
                        <span>產生PDF檔</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 考勤記錄列表 -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/30 overflow-hidden">
            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="flex items-center justify-center py-12">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-500"></div>
                    <span class="text-gray-600">載入中...</span>
                </div>
            </div>

            <!-- 資料表格 -->
            <div id="dataTable" class="hidden">
                <div class="table-wrapper">
                    <div class="table-container">
                        <table class="w-full">
                            <thead class="sticky-header">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">日期</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">員工</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">班別</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">實際到班</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">實際下班</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">遲到</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">早退</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">加班</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">請假</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">請假小時</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-700">狀態</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-700">操作</th>
                                </tr>
                            </thead>
                            <tbody id="tableBody" class="divide-y divide-gray-100">
                                <!-- 動態生成表格內容 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 無資料提示 -->
            <div id="noDataMessage" class="hidden text-center py-12">
                <div class="text-gray-400 mb-2">
                    <i data-lucide="clipboard-list" class="w-12 h-12 mx-auto mb-3"></i>
                </div>
                <p class="text-gray-600">暫無考勤記錄</p>
                <p class="text-sm text-gray-400 mt-1">調整查詢條件或點擊「重新計算」生成考勤資料</p>
            </div>
        </div>
    </div>

    <!-- 考勤詳情模態框 -->
    <div id="attendanceDetailModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-brand-500 to-brand-600 px-8 py-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold">考勤詳情</h3>
                            <p class="text-brand-100 text-sm">詳細考勤資訊與統計</p>
                        </div>
                        <button type="button" id="closeDetailBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-xl transition-all duration-200">
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>

                <!-- 模態框內容 -->
                <div class="p-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div id="attendanceDetailContent">
                        <!-- 動態載入考勤詳情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 考勤編輯模態框 -->
    <div id="attendanceEditModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-2">
            <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-yellow-500 to-orange-500 px-4 py-3 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-bold">編輯考勤記錄</h3>
                            <p class="text-yellow-100 text-xs">修改上下班時間、班表與請假資訊</p>
                        </div>
                        <button type="button" id="closeEditBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-1.5 rounded-lg transition-all duration-200">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <!-- 模態框內容 -->
                <div class="p-4 overflow-y-auto max-h-[calc(95vh-120px)]">
                    <div id="attendanceEditContent">
                        <!-- 動態載入編輯表單 -->
                    </div>
                </div>

                <!-- 模態框底部按鈕 -->
                <div class="bg-gray-50 px-4 py-2 border-t">
                    <div class="flex justify-end space-x-2">
                        <button type="button" id="cancelEditBtn" class="px-4 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="button" id="saveEditBtn" class="px-4 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200">
                            儲存變更
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 換班模態框 (優化為橫式布局，一頁顯示) -->
    <div id="shiftChangeModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-2xl w-[90vw] max-w-5xl h-[80vh] overflow-hidden">
            <!-- 模態框頭部 -->
            <div class="bg-gradient-to-r from-purple-500 to-indigo-500 px-4 py-3 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-bold">更換班表</h3>
                        <p class="text-purple-100 text-xs">選擇新的班表類型</p>
                    </div>
                    <button type="button" id="closeShiftChangeBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-1.5 rounded-lg transition-all duration-200">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                </div>
            </div>

            <!-- 模態框內容 -->
            <div class="p-4 h-[calc(80vh-120px)] overflow-hidden">
                <div id="shiftChangeContent" class="h-full">
                    <!-- 動態載入換班表單 -->
                    <div class="text-center py-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">班別選擇</h2>
                        <p class="text-gray-600 mb-6">請選擇要更換的班別：</p>
                        <div class="space-y-3">
                            <div class="text-lg font-medium text-blue-600">• 標準日班</div>
                            <div class="text-lg font-medium text-green-600">• 早班</div>
                            <div class="text-lg font-medium text-orange-600">• 晚班</div>
                            <div class="text-lg font-medium text-purple-600">• 夜班</div>
                            <div class="text-lg font-medium text-red-600">• 彈性班</div>
                            <div class="text-lg font-medium text-yellow-600">• 半日班</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模態框底部按鈕 -->
            <div class="bg-gray-50 px-4 py-2 border-t">
                <div class="flex justify-end space-x-2">
                    <button type="button" id="cancelShiftChangeBtn" class="px-4 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                    <button type="button" id="saveShiftChangeBtn" class="px-4 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-200">
                            確認更換
                        </button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 全域變數
        let currentPage = 1;
        let pageSize = 50;
        let totalRecords = 0;
        let currentRecords = [];
        let selectedShiftId = null;
        let selectedShiftName = null;
        let currentShiftChangeRecordId = null;

        // 安全的錯誤訊息格式化函數
        function formatErrorMessage(error, defaultMessage) {
            if (typeof error === 'string' && error.trim() !== '') {
                return error;
            }
            return defaultMessage || '發生未知錯誤';
        }

        // 全域通知函數
        function showNotification(message, type = 'info') {
            // 確保消息不為空或false
            const safeMessage = message || '發生未知錯誤';

            if (window.NotificationSystem) {
                return NotificationSystem.showSlideNotification(safeMessage, type);
            } else {
                // 降級處理：使用瀏覽器原生alert
                console.log(`[${type.toUpperCase()}] ${safeMessage}`);
                if (type === 'error') {
                    alert(`錯誤: ${safeMessage}`);
                } else if (type === 'success') {
                    console.log(`成功: ${safeMessage}`);
                } else {
                    console.log(`通知: ${safeMessage}`);
                }
            }
        }

        // 全域載入指示器函數
        function showLoading(show = true) {
            const loadingElement = document.getElementById('loadingIndicator');
            if (loadingElement) {
                if (show) {
                    loadingElement.classList.remove('hidden');
                } else {
                    loadingElement.classList.add('hidden');
                }
            } else {
                console.log(show ? '載入中...' : '載入完成');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            loadEmployees();
            loadDepartments();
            bindEvents();
            setDefaultDates();
            searchRecords(); // 預設載入今天的資料

            // 頁面初始化完成
            console.log('✅ 考勤作業管理頁面初始化完成');
        });



        // 設定預設日期
        function setDefaultDates() {
            const today = new Date();

            // 設定開始日期和結束日期都為當天
            const todayStr = today.toISOString().split('T')[0];

            document.getElementById('startDate').value = todayStr;
            document.getElementById('endDate').value = todayStr;
        }

        // 綁定事件
        function bindEvents() {
            document.getElementById('searchBtn').addEventListener('click', searchRecords);
            document.getElementById('refreshBtn').addEventListener('click', searchRecords);
            document.getElementById('generateBtn').addEventListener('click', generateAttendanceRecords);
            document.getElementById('exportBtn').addEventListener('click', exportRecords);
            document.getElementById('exportPdfBtn').addEventListener('click', exportPdfRecords);
            document.getElementById('closeDetailBtn').addEventListener('click', hideDetailModal);

            // 換班模態框事件綁定
            document.getElementById('closeShiftChangeBtn').addEventListener('click', hideShiftChangeModal);
            document.getElementById('cancelShiftChangeBtn').addEventListener('click', hideShiftChangeModal);
            document.getElementById('saveShiftChangeBtn').addEventListener('click', saveShiftChange);

            // 考勤編輯模態框事件綁定
            document.getElementById('closeEditBtn').addEventListener('click', hideEditModal);
            document.getElementById('cancelEditBtn').addEventListener('click', hideEditModal);
            document.getElementById('saveEditBtn').addEventListener('click', saveAttendanceEdit);

            // 快速日期選擇
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const days = parseInt(this.dataset.days);
                    setQuickDate(days);
                });
            });
        }

        // 設定快速日期
        function setQuickDate(days) {
            const today = new Date();
            const endDate = new Date(today);
            const startDate = new Date(today);

            if (days === 0) {
                // 今天
                startDate.setDate(today.getDate());
            } else if (days === 7) {
                // 本週（週一到今天）
                const dayOfWeek = today.getDay();
                const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
                startDate.setDate(diff);
            } else if (days === 30) {
                // 本月（當月第一天到最後一天）
                startDate.setDate(1);
                endDate.setDate(1);
                endDate.setMonth(endDate.getMonth() + 1);
                endDate.setDate(0); // 設定為上個月的最後一天，即當月最後一天
            } else if (days === 1) {
                // 昨天
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
            } else if (days === 14) {
                // 上週
                const dayOfWeek = today.getDay();
                const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
                startDate.setDate(diff - 7);
                endDate.setDate(diff - 1);
            }

            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

            // 自動查詢
            searchRecords();
        }

        // 載入員工列表
        function loadEmployees() {
            fetch('/api/employees')
                .then(response => response.json())
                .then(data => {
                    const employees = data.employees || data;
                    const select = document.getElementById('employeeSelect');
                    employees.forEach(emp => {
                        const option = document.createElement('option');
                        option.value = emp.id;
                        option.textContent = `${emp.name} (${emp.employee_id})`;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('載入員工失敗:', error));
        }

        // 載入部門列表
        function loadDepartments() {
            fetch('/api/departments')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('departmentSelect');
                    // 修正：API返回的是包含departments陣列的物件
                    const departments = data.departments || data;
                    departments.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.name;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('載入部門失敗:', error));
        }

        // 查詢考勤記錄
        function searchRecords() {
            showLoading(true);

            const employeeId = document.getElementById('employeeSelect').value;
            const departmentId = document.getElementById('departmentSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const status = document.getElementById('statusSelect').value;

            let url = `/api/attendance/records?page=${currentPage}&per_page=${pageSize}`;
            if (employeeId) url += `&employee_id=${employeeId}`;
            if (departmentId) url += `&department_id=${departmentId}`;
            if (startDate) url += `&start_date=${startDate}`;
            if (endDate) url += `&end_date=${endDate}`;
            if (status) url += `&status=${status}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    currentRecords = data.records || [];
                    totalRecords = data.pagination ? data.pagination.total : (data.total || 0);
                    updateStatistics(data.statistics || {});
                    renderTable();
                })
                .catch(error => {
                    console.error('查詢失敗:', error);
                    showNotification('查詢失敗', 'error');
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        // 生成考勤記錄
        async function generateAttendanceRecords() {
            if (!confirm('確定要重新計算考勤記錄嗎？這將根據打卡記錄和班表重新統計所有考勤資料。')) {
                return;
            }

            showNotification('開始重新計算考勤記錄...', 'info');

            try {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                const response = await fetch('/api/attendance/management/generate-complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        start_date: startDate,
                        end_date: endDate
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showNotification(`成功生成 ${data.generated_count} 筆考勤記錄`, 'success');
                    searchRecords(); // 重新載入資料
                } else {
                    const errorMessage = (typeof data.error === 'string' && data.error.trim() !== '') ? data.error : '生成失敗';
                    showNotification(errorMessage, 'error');
                }

            } catch (error) {
                console.error('生成失敗:', error);
                showNotification('生成失敗', 'error');
            }
        }

        // 更新統計資訊
        function updateStatistics(stats) {
            document.getElementById('normalCount').textContent = stats.normal || 0;
            document.getElementById('lateCount').textContent = stats.late || 0;
            document.getElementById('earlyLeaveCount').textContent = stats.early_leave || 0;
            document.getElementById('overtimeCount').textContent = stats.overtime || 0;
            document.getElementById('leaveCount').textContent = stats.leave || 0;
            document.getElementById('absentCount').textContent = stats.absent || 0;
        }

        // 渲染表格
        function renderTable() {
            const tableBody = document.getElementById('tableBody');
            const dataTable = document.getElementById('dataTable');
            const noDataMessage = document.getElementById('noDataMessage');
            const recordCount = document.getElementById('recordCount');

            recordCount.textContent = `${totalRecords} 筆記錄`;

            if (currentRecords.length === 0) {
                dataTable.classList.add('hidden');
                noDataMessage.classList.remove('hidden');
                return;
            }

            noDataMessage.classList.add('hidden');
            dataTable.classList.remove('hidden');

            // 生成表格內容
            tableBody.innerHTML = currentRecords.map(record => {
                        // 處理日期顯示 - 優先使用work_date，其次check_in，最後check_out
                        const displayDate = record.work_date || (record.check_in ? record.check_in.split(' ')[0] : (record.check_out ? record.check_out.split(' ')[0] : ''));

                        // 處理班表時間顯示
                        const shiftTime = record.shift_start_time && record.shift_end_time ?
                            `${record.shift_start_time}-${record.shift_end_time}` : '';

                        // 處理加班時數 - API返回overtime_hours，轉換為分鐘顯示
                        const overtimeMinutes = record.overtime_hours ? record.overtime_hours * 60 : 0;

                        // 判斷是否為異常記錄
                        const isAbnormal = record.late_minutes > 0 ||
                            record.early_leave_minutes > 0 ||
                            !record.check_in ||
                            !record.check_out ||
                            overtimeMinutes > 0;

                        const rowClass = isAbnormal ? 'table-row abnormal-record cursor-pointer' : 'table-row hover:bg-gray-50 cursor-pointer';

                        return `
                    <tr class="${rowClass}" onclick="showAttendanceDetail(${record.id})">
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <div class="font-medium">${formatDate(displayDate)}</div>
                            <div class="text-xs text-gray-500">${getWeekDay(displayDate)}</div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <div class="font-medium">${record.employee_name}</div>
                            <div class="text-xs text-gray-500">${record.employee_code}</div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <div class="flex flex-col items-center space-y-1">
                                ${getShiftButtonHtml(record)}
                                <div class="text-xs text-gray-500">
                                    ${shiftTime ? `規定時間: ${shiftTime}` : ''}
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <span class="${record.check_in ? '' : 'text-gray-400'}">${getCheckInDisplay(record)}</span>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <span class="${record.check_out ? '' : 'text-gray-400'}">${getCheckOutDisplay(record)}</span>
                        </td>
                        <td class="px-4 py-3 text-sm">
                            ${record.late_minutes > 0 ? 
                                `<span class="text-warning-600 font-medium">${record.late_minutes}分</span>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-4 py-3 text-sm">
                            ${record.early_leave_minutes > 0 ? 
                                `<span class="text-error-600 font-medium">${record.early_leave_minutes}分</span>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-4 py-3 text-sm">
                            ${overtimeMinutes > 0 ? 
                                `<span class="text-brand-600 font-medium">${Math.round(overtimeMinutes/60*10)/10}小時</span>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-4 py-3 text-sm">
                            ${record.leave_type ? 
                                `<div class="text-purple-600 font-medium">${record.leave_type}</div>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-4 py-3 text-sm">
                            ${record.leave_hours > 0 ? 
                                `<span class="text-purple-600 font-medium">${record.leave_hours}小時</span>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-4 py-3 text-sm">
                            <span class="inline-flex items-center text-sm font-medium ${getStatusClass(record.status)}">
                                ${getStatusText(record.status)}
                            </span>
                        </td>
                        <td class="px-4 py-3 text-right">
                            <div class="flex items-center justify-end space-x-2">
                                <button onclick="event.stopPropagation(); editAttendanceRecord(${record.id})"
                                        style="display: flex !important; align-items: center !important; justify-content: center !important; width: 100%; padding: 8px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #f59e0b, #ea580c); cursor: pointer; text-align: center !important; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
                                        onmouseover="this.style.background='linear-gradient(135deg, #d97706, #dc2626)'; this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.background='linear-gradient(135deg, #f59e0b, #ea580c)'; this.style.transform='scale(1)'">
                                    編輯
                                </button>
                                <button onclick="event.stopPropagation(); showAttendanceDetail(${record.id})"
                                        style="display: flex !important; align-items: center !important; justify-content: center !important; width: 100%; padding: 8px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #3b82f6, #6366f1); cursor: pointer; text-align: center !important; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
                                        onmouseover="this.style.background='linear-gradient(135deg, #2563eb, #4f46e5)'; this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.background='linear-gradient(135deg, #3b82f6, #6366f1)'; this.style.transform='scale(1)'">
                                    詳情
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 顯示考勤詳情
        async function showAttendanceDetail(recordId) {
            try {
                const response = await fetch(`/api/attendance/records/${recordId}`);
                const data = await response.json();

                if (response.ok) {
                    const record = data.record;
                    const content = document.getElementById('attendanceDetailContent');
                    
                    // 處理日期顯示 - 優先使用work_date，其次check_in，最後check_out
                    const displayDate = record.work_date || (record.check_in ? record.check_in.split(' ')[0] : (record.check_out ? record.check_out.split(' ')[0] : ''));
                    
                    content.innerHTML = `
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- 基本資訊 -->
                            <div class="space-y-6">
                                <h4 class="text-lg font-semibold text-gray-900 border-b pb-2">基本資訊</h4>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">日期</label>
                                        <p class="text-gray-900 font-medium">${formatDate(displayDate)}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">星期</label>
                                        <p class="text-gray-900">${getWeekDay(displayDate)}</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">員工</label>
                                        <p class="text-gray-900">${record.employee_name} (${record.employee_code})</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">部門</label>
                                        <p class="text-gray-900">${record.department_name || '-'}</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">考勤狀態</label>
                                    <span class="inline-flex items-center text-sm font-medium ${getStatusClass(record.status)}">
                                        ${getStatusText(record.status)}
                                    </span>
                                </div>
                            </div>
                            
                            <!-- 時間統計 -->
                            <div class="space-y-6">
                                <h4 class="text-lg font-semibold text-gray-900 border-b pb-2">時間統計</h4>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">班表時間</label>
                                        <p class="text-gray-900">${record.shift_name || '-'}</p>
                                        <p class="text-xs text-gray-500">${record.shift_start_time && record.shift_end_time ? `${record.shift_start_time}-${record.shift_end_time}` : ''}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">工作時長</label>
                                        <p class="text-brand-600 font-medium">${record.work_hours ? `${record.work_hours}小時` : '-'}</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">上班打卡</label>
                                        <p class="text-gray-900 ${record.check_in ? '' : 'text-gray-400'}">${getCheckInDisplayFull(record)}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">下班打卡</label>
                                        <p class="text-gray-900 ${record.check_out ? '' : 'text-gray-400'}">${getCheckOutDisplayFull(record)}</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">遲到</label>
                                        <p class="text-warning-600 font-medium">${record.late_minutes > 0 ? `${record.late_minutes}分鐘` : '-'}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">早退</label>
                                        <p class="text-error-600 font-medium">${record.early_leave_minutes > 0 ? `${record.early_leave_minutes}分鐘` : '-'}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 mb-1">加班</label>
                                        <p class="text-brand-600 font-medium">${record.overtime_hours > 0 ? `${record.overtime_hours}小時` : '-'}</p>
                                    </div>
                                </div>
                                
                                ${record.note ? `
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">備註</label>
                                    <p class="text-gray-900">${record.note}</p>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                    
                    document.getElementById('attendanceDetailModal').classList.remove('hidden');
                } else {
                    const errorMessage = (typeof data.error === 'string' && data.error.trim() !== '') ? data.error : '載入詳情失敗';
                    showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('載入詳情失敗:', error);
                showNotification('載入詳情失敗', 'error');
            }
        }

        // 隱藏詳情模態框
        function hideDetailModal() {
            document.getElementById('attendanceDetailModal').classList.add('hidden');
        }

        // 匯出記錄
        async function exportRecords() {
            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const status = document.getElementById('statusSelect').value;

                let url = '/api/attendance/records/export?';
                const params = new URLSearchParams();
                if (employeeId) params.append('employee_id', employeeId);
                if (departmentId) params.append('department_id', departmentId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (status) params.append('status', status);

                const response = await fetch(url + params.toString());
                const blob = await response.blob();

                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `attendance_summary_${startDate}_${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                showNotification('匯出成功', 'success');
            } catch (error) {
                console.error('匯出失敗:', error);
                showNotification('匯出失敗', 'error');
            }
        }

        // 產生PDF檔案
        async function exportPdfRecords() {
            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const status = document.getElementById('statusSelect').value;

                // 顯示生成中的提示
                if (employeeId) {
                    showNotification('正在產生單一員工PDF檔案...', 'info');
                } else {
                    showNotification('正在產生所有員工PDF檔案...', 'info');
                }

                let url = '/api/attendance/records/export-pdf?';
                const params = new URLSearchParams();
                if (employeeId) params.append('employee_id', employeeId);
                if (departmentId) params.append('department_id', departmentId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (status) params.append('status', status);

                const response = await fetch(url + params.toString());
                
                if (!response.ok) {
                    throw new Error('PDF生成失敗');
                }

                const blob = await response.blob();

                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                
                // 根據是否選擇員工來決定檔案命名
                let fileName;
                if (employeeId) {
                    // 單一員工PDF
                    const employeeSelect = document.getElementById('employeeSelect');
                    const employeeName = employeeSelect.options[employeeSelect.selectedIndex].text.split(' ')[0];
                    fileName = `考勤報表_${employeeName}_${startDate}_${endDate}.pdf`;
                } else {
                    // 所有員工PDF
                    fileName = `考勤報表_所有員工_${startDate}_${endDate}.pdf`;
                }
                
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                showNotification('PDF檔案產生成功', 'success');
            } catch (error) {
                console.error('PDF產生失敗:', error);
                showNotification('PDF產生失敗', 'error');
            }
        }

        // 工具函數
        function formatDate(dateString) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的日期字符串:', dateString);
                return '-';
            }
            
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function formatTime(timestamp) {
            if (!timestamp) return '-';
            
            const date = new Date(timestamp);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的時間戳:', timestamp);
                return '-';
            }
            
            return date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function getWeekDay(dateString) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的日期字符串:', dateString);
                return '-';
            }
            
            const days = ['日', '一', '二', '三', '四', '五', '六'];
            return '週' + days[date.getDay()];
        }

        function getStatusText(status) {
            const statuses = {
                'normal': '正常',
                'late': '遲到',
                'early_leave': '早退',
                'overtime': '加班',
                'leave': '請假',
                'absent': '曠職',
                'holiday': '休假',
                'weekend': '週末',
                'incomplete': '不完整'
            };
            return statuses[status] || status;
        }

        function getStatusClass(status) {
            const classes = {
                'normal': 'text-green-600 border-b-2 border-green-400 pb-1',
                'late': 'text-yellow-600 border-b-2 border-yellow-400 pb-1',
                'early_leave': 'text-orange-600 border-b-2 border-orange-400 pb-1',
                'overtime': 'text-blue-600 border-b-2 border-blue-400 pb-1',
                'leave': 'text-purple-600 border-b-2 border-purple-400 pb-1',
                'absent': 'text-red-600 border-b-2 border-red-400 pb-1',
                'holiday': 'text-gray-600 border-b-2 border-gray-400 pb-1',
                'weekend': 'text-gray-600 border-b-2 border-gray-400 pb-1',
                'incomplete': 'text-orange-600 border-b-2 border-orange-400 pb-1'
            };
            return classes[status] || 'text-gray-600 border-b-2 border-gray-400 pb-1';
        }

        // 獲取上班時間顯示（列表用）
        function getCheckInDisplay(record) {
            if (record.check_in) {
                return record.check_in.split(' ')[1];
            }
            // 如果是週末或假日且沒有打卡記錄，顯示"假日"
            if (record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday') {
                return '假日';
            }
            return '未打卡';
        }

        // 獲取下班時間顯示（列表用）
        function getCheckOutDisplay(record) {
            if (record.check_out) {
                return record.check_out.split(' ')[1];
            }
            // 如果是週末或假日且沒有打卡記錄，顯示"假日"
            if (record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday') {
                return '假日';
            }
            return '未打卡';
        }

        // 獲取上班時間顯示（詳情用）
        function getCheckInDisplayFull(record) {
            if (record.check_in) {
                return record.check_in;
            }
            // 如果是週末或假日且沒有打卡記錄，顯示"假日"
            if (record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday') {
                return '假日';
            }
            return '未打卡';
        }

        // 獲取下班時間顯示（詳情用）
        function getCheckOutDisplayFull(record) {
            if (record.check_out) {
                return record.check_out;
            }
            // 如果是週末或假日且沒有打卡記錄，顯示"假日"
            if (record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday') {
                return '假日';
            }
            return '未打卡';
        }

        function getClockTypeText(type) {
            const types = {
                'clock_in': '上班打卡',
                'clock_out': '下班打卡',
                'break_start': '休息開始',
                'break_end': '休息結束',
                'overtime_start': '加班開始',
                'overtime_end': '加班結束'
            };
            return types[type] || type;
        }

        function showLoading(show) {
            const loading = document.getElementById('loadingIndicator');
            const table = document.getElementById('dataTable');
            const noData = document.getElementById('noDataMessage');
            
            if (show) {
                loading.classList.remove('hidden');
                table.classList.add('hidden');
                noData.classList.add('hidden');
            } else {
                loading.classList.add('hidden');
            }
        }

        // 重複的showNotification函數已移除，使用上方的統一版本





        // 計算工作時長
        function calculateWorkDuration(startTime, endTime) {
            if (!startTime || !endTime) return '未設定';
            
            try {
                const start = new Date(`2000-01-01 ${startTime}`);
                const end = new Date(`2000-01-01 ${endTime}`);
                
                let diffMs = end - start;
                
                // 處理跨日情況
                if (diffMs < 0) {
                    diffMs += 24 * 60 * 60 * 1000;
                }
                
                const hours = Math.floor(diffMs / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                
                if (minutes === 0) {
                    return `${hours}小時`;
                } else {
                    return `${hours}小時${minutes}分鐘`;
                }
            } catch (error) {
                return '計算錯誤';
            }
        }

        // 獲取班表類型名稱
        function getShiftTypeName(type) {
            const types = {
                'fixed': '固定班',
                'flexible': '彈性班',
                'rotating': '輪班',
                'split': '分段班',
                'night': '夜班',
                'weekend': '假日班'
            };
            return types[type] || type;
        }

        // 獲取班表按鈕HTML
        function getShiftButtonHtml(record) {
            // 檢查是否為假日
            const isHoliday = record.date_type === 'weekend' || record.status === 'weekend' || record.status === 'holiday';
            
            if (isHoliday) {
                // 假日顯示綠色按鈕，統一顯示「休假」
                return `
                    <button onclick="event.stopPropagation(); editShiftRecord(${record.id})"
                            style="display: flex; align-items: center; justify-content: center; gap: 6px; width: 100%; padding: 8px 12px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #10b981, #059669); cursor: pointer; text-align: center;">
                        <i data-lucide="calendar-x" style="width: 16px; height: 16px;"></i>
                        休假
                    </button>
                `;
            } else {
                // 工作日顯示紫色按鈕
                return `
                    <button onclick="event.stopPropagation(); editShiftRecord(${record.id})"
                            style="display: flex; align-items: center; justify-content: center; gap: 6px; width: 100%; padding: 8px 12px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; color: white; background: linear-gradient(135deg, #8b5cf6, #7c3aed); cursor: pointer; text-align: center;">
                        <i data-lucide="clock" style="width: 16px; height: 16px;"></i>
                        ${record.shift_name || '未設定'}
                    </button>
                `;
            }
        }



        // 班表選擇相關變數
        // let currentAttendanceId = null;
        // let selectedShiftId = null;

        // // 顯示班表選擇模態框 (新版 - 複製`editAttendanceRecord`模式)
        // async function showShiftModal(recordId) {
        //     console.log('正在執行最新版本的 showShiftModal, recordId:', recordId);
        // }

        // // 隱藏班表選擇模態框
        // function hideShiftModal() {
        //     const modal = document.getElementById('shiftModal');
        //     if (modal) {
        //         modal.classList.add('hidden');
        //         // 清除強制設置的樣式
        //         modal.style.display = '';
        //         modal.style.visibility = '';
        //         modal.style.opacity = '';
        //         console.log('模態框已隱藏');
        //     }
        //     currentAttendanceId = null;
        //     selectedShiftId = null;
        // }

        // 載入班表選項 (新版)
        function loadShiftOptions(currentShiftId, shifts) {
            console.log('loadShiftOptions (新版) 開始載入, currentShiftId:', currentShiftId);
            // selectedShiftId = currentShiftId;
            
            try {
                const container = document.getElementById('shiftOptions');
                if (!container) {
                    throw new Error('找不到 shiftOptions 容器');
                }
                
                container.innerHTML = '';
                
                if (!Array.isArray(shifts) || shifts.length === 0) {
                    container.innerHTML = '<div class="text-center text-gray-500 py-4">沒有可用的班表</div>';
                    return;
                }
                
                shifts.forEach(shift => {
                    const isSelected = shift.id == currentShiftId;
                    const option = document.createElement('div');
                    option.className = `shift-option p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${isSelected ? 'selected bg-purple-50 border-purple-300' : ''}`;
                    option.dataset.shiftId = shift.id;
                    
                    option.innerHTML = `
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-gray-900">${shift.name || '未命名班表'}</div>
                                <div class="text-sm text-gray-500">${shift.start_time || '00:00'} - ${shift.end_time || '00:00'}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-purple-600">${calculateWorkDuration(shift.start_time, shift.end_time)}</div>
                                <div class="text-xs text-gray-400">${shift.code || ''}</div>
                            </div>
                        </div>
                    `;
                    
                    option.addEventListener('click', function() {
                        container.querySelectorAll('.shift-option').forEach(opt => {
                            opt.classList.remove('selected', 'bg-purple-50', 'border-purple-300');
                        });
                        
                        this.classList.add('selected', 'bg-purple-50', 'border-purple-300');
                        // selectedShiftId = shift.id;
                        console.log('選中班表ID:', shift.id);
                    });
                    
                    container.appendChild(option);
                });
                
                console.log(`成功載入 ${shifts.length} 個班表選項`);
                
            } catch (error) {
                console.error('載入班表選項失敗:', error);
                showNotification('載入班表選項失敗: ' + error.message, 'error');
                
                const container = document.getElementById('shiftOptions');
                if (container) {
                    container.innerHTML = `<div class="text-center text-red-500 py-4">載入失敗: ${error.message}</div>`;
                }
            }
        }



        // ===== 考勤編輯相關函數 =====
        let currentEditRecordId = null;

        // 編輯考勤記錄
        async function editAttendanceRecord(recordId) {
            currentEditRecordId = recordId;
            
            try {
                showNotification('正在載入編輯資料...', 'info');
                
                const response = await fetch(`/api/attendance/edit/${recordId}`);
                const data = await response.json();
                
                if (response.ok) {
                    showEditModal(data);
                } else {
                    showNotification(data.error || '載入編輯資料失敗', 'error');
                }
                
            } catch (error) {
                console.error('載入編輯資料失敗:', error);
                showNotification('載入編輯資料失敗', 'error');
            }
        }

        // 顯示編輯模態框
        function showEditModal(data) {
            const record = data.record;
            const availableShifts = data.available_shifts;
            const leaveTypes = data.leave_types;
            const leaveRecords = data.leave_records;
            
            // 構建編輯表單
            const editContent = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-3">
                    <!-- 基本資訊 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="user" class="w-4 h-4 mr-1 text-brand-600"></i>
                            基本資訊
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs font-medium text-gray-700">員工姓名</label>
                                <div class="mt-0.5 text-xs text-gray-900">${record.employee_name} (${record.employee_code})</div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700">工作日期</label>
                                <div class="mt-0.5 text-xs text-gray-900">${record.work_date}</div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700">部門</label>
                                <div class="mt-0.5 text-xs text-gray-900">${record.department_name || '未設定'}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 班表設定 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 mr-1 text-brand-600"></i>
                            班表設定
                        </h4>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">選擇班表</label>
                            <select id="editShiftSelect" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                                <option value="">請選擇班表</option>
                                ${availableShifts.map(shift => `
                                    <option value="${shift.id}" ${shift.id == record.shift_id ? 'selected' : ''}>
                                        ${shift.name} (${shift.start_time} - ${shift.end_time})
                                    </option>
                                `).join('')}
                            </select>
                            ${record.shift_name ? `
                                <div class="mt-1 text-xs text-gray-600">
                                    目前：${record.shift_name} 
                                    <span class="text-brand-600">${record.shift_start_time} - ${record.shift_end_time}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- 當前統計 -->
                    <div class="bg-blue-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-4 h-4 mr-1 text-blue-600"></i>
                            當前統計
                        </h4>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900">${(record.work_hours || 0).toFixed(1)}</div>
                                <div class="text-xs text-gray-600">工時</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-red-600">${record.late_minutes || 0}</div>
                                <div class="text-xs text-gray-600">遲到分鐘</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-orange-600">${record.early_leave_minutes || 0}</div>
                                <div class="text-xs text-gray-600">早退分鐘</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">${(record.overtime_hours || 0).toFixed(1)}</div>
                                <div class="text-xs text-gray-600">加班時數</div>
                            </div>
                        </div>
                    </div>

                    <!-- 上下班時間 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="calendar-clock" class="w-4 h-4 mr-1 text-brand-600"></i>
                            上下班時間
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">上班時間</label>
                                <input type="datetime-local" id="editCheckIn" 
                                       value="${record.check_in ? record.check_in.replace(' ', 'T') : ''}"
                                       class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">下班時間</label>
                                <input type="datetime-local" id="editCheckOut" 
                                       value="${record.check_out ? record.check_out.replace(' ', 'T') : ''}"
                                       class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                            </div>
                        </div>
                    </div>

                    <!-- 請假資訊 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="calendar-x" class="w-4 h-4 mr-1 text-brand-600"></i>
                            請假資訊
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">請假類型</label>
                                <select id="editLeaveType" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                                    <option value="">無請假</option>
                                    ${leaveTypes.map(type => `
                                        <option value="${type.code || type}">${type.name || type}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">請假時數</label>
                                <input type="number" id="editLeaveHours" 
                                       value="${record.leave_hours || 0}" 
                                       min="0" max="24" step="0.5"
                                       class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500">
                            </div>
                        </div>
                    </div>

                    <!-- 備註與請假原因 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                            <i data-lucide="file-text" class="w-4 h-4 mr-1 text-brand-600"></i>
                            備註資訊
                        </h4>
                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">請假原因</label>
                                <textarea id="editLeaveReason" rows="2" 
                                          class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                                          placeholder="請輸入請假原因"></textarea>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-700 mb-1">備註</label>
                                <textarea id="editNote" rows="2" 
                                          class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                                          placeholder="請輸入備註資訊">${record.note || ''}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('attendanceEditContent').innerHTML = editContent;
            
            // 如果有現有請假記錄，填入資料
            if (leaveRecords.length > 0) {
                const leave = leaveRecords[0];
                document.getElementById('editLeaveType').value = leave.leave_type || '';
                document.getElementById('editLeaveReason').value = leave.reason || '';
            }
            
            // 重新創建圖標
            lucide.createIcons();
            
            // 顯示模態框
            document.getElementById('attendanceEditModal').classList.remove('hidden');
        }

        // 隱藏編輯模態框
        function hideEditModal() {
            document.getElementById('attendanceEditModal').classList.add('hidden');
            currentEditRecordId = null;
        }

        // ===== 換班功能 =====
        
        // 換班按鈕點擊處理
        function editShiftRecord(recordId) {
            console.log('🔵 換班按鈕點擊，記錄ID:', recordId);
            
            // 載入考勤記錄資料
            fetch(`/api/attendance/records/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showShiftChangeModal(data.record);
                    } else {
                        showNotification('載入考勤記錄失敗', 'error');
                    }
                })
                .catch(error => {
                    console.error('載入考勤記錄失敗:', error);
                    showNotification('載入考勤記錄失敗', 'error');
                });
        }

        // 顯示換班模態框
        function showShiftChangeModal(data) {
            console.log('🔵 showShiftChangeModal 被調用，資料:', data);
            
            // 設置當前記錄ID
            currentShiftChangeRecordId = data.id;
            
            // 動態載入內容
            const contentDiv = document.getElementById('shiftChangeContent');
            console.log('🔵 找到內容容器:', contentDiv);
            
            // 生成換班選擇表單 - 專業緊湊布局
            contentDiv.innerHTML = `
                <div class="h-full flex flex-col">
                    <!-- 員工資訊條 -->
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-5 gap-4 text-center">
                            <div>
                                <div class="text-blue-100 text-xs">員工姓名</div>
                                <div class="font-bold">${data.employee_name || '未知'}</div>
                            </div>
                            <div>
                                <div class="text-blue-100 text-xs">工作日期</div>
                                <div class="font-bold">${data.work_date || '未知'}</div>
                            </div>
                            <div>
                                <div class="text-blue-100 text-xs">星期</div>
                                <div class="font-bold">${getWeekDay(data.work_date)}</div>
                            </div>
                            <div>
                                <div class="text-blue-100 text-xs">所屬部門</div>
                                <div class="font-bold">${data.department_name || '未知'}</div>
                            </div>
                            <div>
                                <div class="text-blue-100 text-xs">目前班表</div>
                                <div class="font-bold bg-white bg-opacity-20 px-2 py-1 rounded">${data.shift_name || '未知'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 班表選擇區域 -->
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                            <i data-lucide="clock" class="w-5 h-5 mr-2"></i>選擇新班表
                        </h4>
                        <div id="shiftOptionsContainer" class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                            <!-- 動態載入班表選項 -->
                            <div class="col-span-full text-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
                                <p class="text-gray-500 mt-2">載入班表選項中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 顯示模態框 - 使用強制樣式覆蓋CSS衝突
            const modal = document.getElementById('shiftChangeModal');
            console.log('🔵 找到模態框元素:', modal);
            modal.classList.remove('hidden');
            
            // 🚀 強制設置樣式，覆蓋任何CSS衝突
            modal.style.setProperty('display', 'flex', 'important');
            modal.style.setProperty('visibility', 'visible', 'important');
            modal.style.setProperty('opacity', '1', 'important');
            modal.style.setProperty('z-index', '9999', 'important');
            modal.style.setProperty('position', 'fixed', 'important');
            console.log('🔵 強制樣式已設置，模態框應該已顯示！');
            
            // 載入班表選項（不自動選中假日班表）
            loadShiftOptions(false);
            
            // 重新創建圖標
            setTimeout(() => {
                lucide.createIcons();
            }, 100);
        }

        // 隱藏換班模態框
        function hideShiftChangeModal() {
            const modal = document.getElementById('shiftChangeModal');
            modal.classList.add('hidden');
            
            // 重置強制樣式
            modal.style.removeProperty('display');
            modal.style.removeProperty('visibility');
            modal.style.removeProperty('opacity');
            modal.style.removeProperty('z-index');
            modal.style.removeProperty('position');
            
            // 重置選擇狀態
            selectedShiftId = null;
            currentShiftChangeRecordId = null;
            
            console.log('🔵 換班模態框已隱藏並重置');
        }

        // 載入班表選項
        async function loadShiftOptions(autoSelectHoliday = false) {
            try {
                const response = await fetch('/api/shifts');
                const data = await response.json();
                
                if (response.ok) {
                    const shifts = data.shifts || data;
                    const container = document.getElementById('shiftOptionsContainer');
                    
                    if (shifts.length === 0) {
                        container.innerHTML = `
                            <div class="text-center py-8">
                                <i data-lucide="alert-circle" class="w-12 h-12 text-gray-400 mx-auto mb-2"></i>
                                <p class="text-gray-500">沒有可用的班表選項</p>
                            </div>
                        `;
                        return;
                    }
                    
                    // 分離假日班表和一般班表，假日班表排在最後
                    const regularShifts = shifts.filter(shift => !(shift.code === 'HOLIDAY' || shift.name === '假日'));
                    const holidayShifts = shifts.filter(shift => shift.code === 'HOLIDAY' || shift.name === '假日');
                    const sortedShifts = [...regularShifts, ...holidayShifts];
                    
                    // 獲取當前班表ID（從全域變數或資料中取得）
                    const currentShiftId = window.currentShiftChangeRecordId ? 
                        document.querySelector(`[data-record-id="${window.currentShiftChangeRecordId}"]`)?.dataset?.shiftId : 
                        null;
                    
                    // 生成班表選項
                    let optionsHtml = '';
                    
                    console.log('🔍 當前記錄的班表ID:', data.shift_id, '班表名稱:', data.shift_name);
                    
                    sortedShifts.forEach((shift, index) => {
                        // 假日班表使用特殊的綠色樣式
                        const isHolidayShift = shift.code === 'HOLIDAY' || shift.name === '假日';
                        const isCurrentShift = shift.id == data.shift_id; // 檢查是否為當前班表
                        
                        console.log(`🔍 班表比對: ${shift.name} (ID: ${shift.id}) vs 當前 (ID: ${data.shift_id}) = ${isCurrentShift}`);
                        
                        let color, icon;
                        
                        if (isHolidayShift) {
                            color = 'green';
                            icon = 'calendar-x';
                        } else {
                            const shiftColors = ['blue', 'purple', 'orange', 'red', 'yellow', 'indigo', 'pink'];
                            color = shiftColors[index % shiftColors.length];
                            icon = 'clock';
                        }
                        
                        // 當前班表使用特殊樣式突出顯示
                        let currentShiftStyle = 'border-gray-200';
                        let currentShiftBadge = '';
                        
                        if (isCurrentShift) {
                            currentShiftStyle = 'border-blue-500 bg-blue-50 ring-4 ring-blue-200 shadow-xl';
                            currentShiftBadge = '<span class="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold">目前班表</span>';
                        }
                        
                        optionsHtml += `
                            <div class="shift-option relative bg-white border-2 ${currentShiftStyle} rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-300" 
                                 data-shift-id="${shift.id}" 
                                 data-shift-name="${shift.name}"
                                 data-is-holiday="${isHolidayShift}"
                                 data-is-current="${isCurrentShift}"
                                 onclick="selectShift(${shift.id}, '${shift.name}')">
                                ${currentShiftBadge}
                                <div class="text-center">
                                    <div class="w-12 h-12 mx-auto mb-3 bg-${color}-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="${icon}" class="w-6 h-6 text-${color}-600"></i>
                                    </div>
                                    <div class="font-bold text-${color}-600 text-sm mb-1">
                                        ${shift.name}
                                    </div>
                                    <div class="text-xs text-gray-500 mb-2">${shift.start_time} - ${shift.end_time}</div>
                                    ${isHolidayShift ? '<div class="inline-block text-xs bg-green-500 text-white px-2 py-1 rounded-full">假日班表</div>' : ''}
                                    <div class="shift-check absolute top-2 right-2 hidden">
                                        <div class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    container.innerHTML = optionsHtml;
                    
                    // 重新創建圖標
                    setTimeout(() => {
                        lucide.createIcons();
                    }, 100);
                    
                } else {
                    throw new Error(data.error || '載入班表失敗');
                }
                
            } catch (error) {
                console.error('載入班表選項失敗:', error);
                const container = document.getElementById('shiftOptionsContainer');
                container.innerHTML = `
                    <div class="text-center py-8">
                        <i data-lucide="alert-triangle" class="w-12 h-12 text-red-400 mx-auto mb-2"></i>
                        <p class="text-red-500">載入班表選項失敗</p>
                        <button onclick="loadShiftOptions(false)" class="mt-2 text-sm text-blue-600 hover:text-blue-800">重新載入</button>
                    </div>
                `;
                setTimeout(() => {
                    lucide.createIcons();
                }, 100);
            }
        }
        
        // 班表選擇功能
        function selectShift(shiftId, shiftName) {
            selectedShiftId = shiftId;
            selectedShiftName = shiftName;
            
            // 移除所有選中狀態
            document.querySelectorAll('.shift-option').forEach(option => {
                option.classList.remove('bg-purple-100', 'border-purple-500', 'ring-2', 'ring-purple-200');
                option.classList.add('border-gray-200');
                option.querySelector('.shift-check').classList.add('hidden');
            });
            
            // 添加選中狀態
            const selectedOption = document.querySelector(`[data-shift-id="${shiftId}"]`);
            if (selectedOption) {
                selectedOption.classList.remove('border-gray-200');
                selectedOption.classList.add('bg-purple-100', 'border-purple-500', 'ring-2', 'ring-purple-200');
                selectedOption.querySelector('.shift-check').classList.remove('hidden');
            }
            
            console.log('🔵 選中班表:', { id: shiftId, name: shiftName });
        }
        
        // 儲存換班 - 包含重新計算考勤指標
        async function saveShiftChange() {
            if (!currentShiftChangeRecordId) {
                showNotification('無效的記錄ID', 'error');
                return;
            }
            
            if (!selectedShiftId) {
                showNotification('請選擇要更換的班表', 'error');
                return;
            }
            
            try {
                showNotification(`正在更新班表為「${selectedShiftName || '新班表'}」...`, 'info');
                
                const response = await fetch(`/api/attendance/update-shift/${currentShiftChangeRecordId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        shift_id: selectedShiftId,
                        recalculate: true  // 要求重新計算考勤指標
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showNotification(`班表已成功更新為「${selectedShiftName}」，考勤指標已重新計算`, 'success');
                    hideShiftChangeModal();
                    
                    // 重新載入考勤記錄，顯示更新後的數據
                    setTimeout(() => {
                        searchRecords(); // 重新查詢而不是整頁重新載入
                    }, 1500);
                } else {
                    showNotification(result.error || '班表更新失敗', 'error');
                }
                
            } catch (error) {
                console.error('班表更新失敗:', error);
                showNotification('網路錯誤，班表更新失敗', 'error');
            }
        }



        // 儲存考勤編輯
        async function saveAttendanceEdit() {
            if (!currentEditRecordId) {
                showNotification('無效的記錄ID', 'error');
                return;
            }
            
            try {
                // 收集表單資料
                const editData = {
                    check_in: document.getElementById('editCheckIn').value ? 
                              document.getElementById('editCheckIn').value.replace('T', ' ') + ':00' : null,
                    check_out: document.getElementById('editCheckOut').value ? 
                               document.getElementById('editCheckOut').value.replace('T', ' ') + ':00' : null,
                    shift_id: document.getElementById('editShiftSelect').value || null,
                    leave_type: document.getElementById('editLeaveType').value || null,
                    leave_hours: parseFloat(document.getElementById('editLeaveHours').value) || 0,
                    leave_reason: document.getElementById('editLeaveReason').value || '',
                    note: document.getElementById('editNote').value || ''
                };
                
                showNotification('正在儲存變更...', 'info');
                
                const response = await fetch(`/api/attendance/edit/${currentEditRecordId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(editData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showNotification('考勤記錄更新成功', 'success');
                    hideEditModal();
                    searchRecords(); // 重新載入資料
                } else {
                    showNotification(data.error || '更新失敗', 'error');
                }
                
            } catch (error) {
                console.error('儲存考勤編輯失敗:', error);
                showNotification('儲存失敗', 'error');
            }
        }
    </script>
</body>

</html>