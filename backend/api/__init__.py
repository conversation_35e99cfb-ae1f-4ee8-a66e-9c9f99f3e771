"""
API模組初始化文件

此模組包含所有API路由，按功能分類：
- auth_api: 認證相關API
- attendance_api: 考勤相關API  
- employee_api: 員工管理API
- shift_api: 班表管理API
- leave_api: 請假管理API
- report_api: 報表分析API
- system_api: 系統功能API
"""

from flask import Blueprint

def register_api_blueprints(app):
    """
    註冊所有API藍圖到Flask應用程式
    
    參數:
        app: Flask應用程式實例
    """
    from .auth_api import auth_bp
    from .attendance_api import attendance_bp
    from .employee_api import employee_bp
    from .shift_api import shift_bp
    from .leave_api import leave_bp
    from .report_api import report_bp
    from .system_api import system_bp
    
    # 註冊所有藍圖
    app.register_blueprint(auth_bp)
    app.register_blueprint(attendance_bp)
    app.register_blueprint(employee_bp)
    app.register_blueprint(shift_bp)
    app.register_blueprint(leave_bp)
    app.register_blueprint(report_bp)
    app.register_blueprint(system_bp) 