<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>換班模態框測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">換班模態框測試</h1>

        <!-- 測試按鈕 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">測試按鈕</h2>
            <button id="testShiftBtn" class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                標準日班 (測試)
            </button>
        </div>

        <!-- 日誌區域 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">調試日誌</h2>
            <div id="debugLog" class="bg-gray-50 p-4 rounded text-sm font-mono h-64 overflow-y-auto"></div>
        </div>
    </div>

    <!-- 換班模態框 -->
    <div id="shiftModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-hidden">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-purple-500 to-indigo-500 px-4 py-3 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-bold">選擇班表</h3>
                            <p class="text-purple-100 text-xs">
                                員工：<span id="modalEmployeeName" class="font-medium">測試員工</span> | 日期：
                                <span id="modalWorkDate" class="font-medium">2025-06-07</span>
                            </p>
                        </div>
                        <button type="button" id="closeShiftModalBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-1.5 rounded-lg transition-all duration-200">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <!-- 模態框主體 -->
                <div class="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div id="shiftOptions" class="space-y-2">
                        <!-- 測試班表選項 -->
                        <div class="shift-option p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-gray-900">標準日班</div>
                                    <div class="text-sm text-gray-500">08:00 - 17:00</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-purple-600">8小時</div>
                                </div>
                            </div>
                        </div>
                        <div class="shift-option p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-gray-900">早班</div>
                                    <div class="text-sm text-gray-500">06:00 - 15:00</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-purple-600">8小時</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模態框底部 -->
                <div class="bg-gray-50 px-4 py-2 border-t">
                    <div class="flex justify-end space-x-2">
                        <button type="button" id="cancelShiftBtn" class="px-4 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="button" id="confirmShiftBtn" class="px-4 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-200">
                            確認修改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 調試日誌函數
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 顯示班表選擇模態框
        function showShiftModal(attendanceId, employeeName, workDate, currentShiftId) {
            log(`showShiftModal 被調用: attendanceId=${attendanceId}, employeeName=${employeeName}, workDate=${workDate}, currentShiftId=${currentShiftId}`);

            try {
                // 更新模態框資訊
                document.getElementById('modalEmployeeName').textContent = employeeName;
                document.getElementById('modalWorkDate').textContent = workDate;
                log('模態框資訊更新成功');

                // 顯示模態框
                const modal = document.getElementById('shiftModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    log('模態框顯示成功');
                } else {
                    log('錯誤: 找不到 shiftModal 元素');
                }
            } catch (error) {
                log(`錯誤: ${error.message}`);
            }
        }

        // 隱藏班表選擇模態框
        function hideShiftModal() {
            log('hideShiftModal 被調用');
            try {
                const modal = document.getElementById('shiftModal');
                if (modal) {
                    modal.classList.add('hidden');
                    log('模態框隱藏成功');
                } else {
                    log('錯誤: 找不到 shiftModal 元素');
                }
            } catch (error) {
                log(`錯誤: ${error.message}`);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成');

            // 創建圖標
            lucide.createIcons();
            log('Lucide 圖標創建完成');

            // 綁定測試按鈕事件
            const testBtn = document.getElementById('testShiftBtn');
            if (testBtn) {
                testBtn.addEventListener('click', function() {
                    log('測試按鈕被點擊');
                    showShiftModal(123, '測試員工', '2025-06-07', 1);
                });
                log('測試按鈕事件綁定成功');
            } else {
                log('錯誤: 找不到測試按鈕');
            }

            // 綁定關閉按鈕事件
            const closeBtn = document.getElementById('closeShiftModalBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideShiftModal);
                log('關閉按鈕事件綁定成功');
            } else {
                log('錯誤: 找不到關閉按鈕');
            }

            // 綁定取消按鈕事件
            const cancelBtn = document.getElementById('cancelShiftBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', hideShiftModal);
                log('取消按鈕事件綁定成功');
            } else {
                log('錯誤: 找不到取消按鈕');
            }

            log('初始化完成');
        });
    </script>
</body>

</html>