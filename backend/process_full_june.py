#!/usr/bin/env python3
"""
完整處理6月份的所有考勤記錄。
"""

import sqlite3
from datetime import datetime, date
from services.enhanced_attendance_processor import EnhancedAttendanceProcessor

def create_connection():
    """
    創建與 SQLite 資料庫的連接。
    """
    try:
        conn = sqlite3.connect("../attendance.db")
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
    except Exception as e:
        print(f"資料庫連接錯誤: {e}")
        return None

def check_current_status():
    """
    檢查當前考勤處理狀態。
    """
    processor = EnhancedAttendanceProcessor()
    
    # 獲取上次處理日期
    last_processed = processor.get_last_processed_date()
    
    # 獲取需要處理的日期（處理到6月30日）
    dates_to_process = processor.get_dates_to_process(end_date=date(2025, 6, 30))
    
    return {
        'last_processed_date': last_processed,
        'dates_to_process': dates_to_process,
        'total_days_to_process': len(dates_to_process)
    }

def check_punch_records_status():
    """
    檢查打卡記錄狀態。
    """
    conn = create_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        
        # 檢查未處理的6月打卡記錄
        cursor.execute("""
            SELECT COUNT(*) as total_unprocessed,
                   MIN(punch_date) as earliest_date,
                   MAX(punch_date) as latest_date
            FROM punch_records 
            WHERE processed = 0 AND punch_date LIKE '2025-06%'
        """)
        
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            return {
                'total_unprocessed': result[0],
                'earliest_date': result[1],
                'latest_date': result[2]
            }
        else:
            return {'total_unprocessed': 0}
            
    except Exception as e:
        print(f"檢查打卡記錄錯誤: {e}")
        return None
    finally:
        conn.close()

def execute_full_june_processing():
    """
    執行完整的6月份考勤處理。
    """
    processor = EnhancedAttendanceProcessor()
    
    try:
        print("🚀 開始執行6月份完整批量處理...")
        # 處理到6月30日
        result = processor.process_attendance_batch(end_date=date(2025, 6, 30))
        return result
    except Exception as e:
        print(f"批量處理錯誤: {e}")
        return None

def check_attendance_summary():
    """
    檢查考勤記錄摘要。
    """
    conn = create_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        
        # 檢查6月考勤記錄統計
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   MIN(work_date) as earliest_date,
                   MAX(work_date) as latest_date
            FROM attendance 
            WHERE work_date LIKE '2025-06%'
        """)
        
        result = cursor.fetchone()
        
        return {
            'total_records': result[0] if result else 0,
            'earliest_date': result[1] if result else None,
            'latest_date': result[2] if result else None
        }
        
    except Exception as e:
        print(f"檢查考勤摘要錯誤: {e}")
        return None
    finally:
        conn.close()

def main():
    """
    主函數：執行完整的6月份考勤處理。
    """
    print("=" * 70)
    print("🗓️  完整處理6月份考勤記錄")
    print("=" * 70)
    
    # 步驟1：檢查處理前狀態
    print("\n🔍 步驟1：檢查處理前狀態")
    print("-" * 50)
    
    before_status = check_current_status()
    print(f"📅 上次處理日期：{before_status['last_processed_date'] or '無記錄'}")
    print(f"📊 需要處理天數：{before_status['total_days_to_process']} 天")
    
    if before_status['total_days_to_process'] > 0:
        print(f"📋 需要處理的日期範圍：")
        print(f"   從：{before_status['dates_to_process'][0]}")
        print(f"   到：{before_status['dates_to_process'][-1]}")
    
    # 檢查打卡記錄狀態
    punch_status = check_punch_records_status()
    if punch_status and punch_status['total_unprocessed'] > 0:
        print(f"\n📊 未處理的6月打卡記錄：{punch_status['total_unprocessed']} 筆")
        print(f"📅 打卡記錄日期範圍：{punch_status['earliest_date']} ~ {punch_status['latest_date']}")
    
    # 檢查處理前的6月考勤記錄
    before_attendance = check_attendance_summary()
    if before_attendance:
        print(f"\n📊 處理前6月考勤記錄：{before_attendance['total_records']} 筆")
        if before_attendance['total_records'] > 0:
            print(f"📅 考勤記錄日期範圍：{before_attendance['earliest_date']} ~ {before_attendance['latest_date']}")
    
    # 步驟2：執行完整批量處理
    print("\n⚙️  步驟2：執行6月份完整批量處理")
    print("-" * 50)
    
    if before_status['total_days_to_process'] == 0:
        print("ℹ️  沒有需要處理的日期，6月份已經完全處理完成")
    else:
        processing_result = execute_full_june_processing()
        
        if processing_result:
            print(f"✅ 6月份批量處理完成！")
            print(f"📊 處理統計：")
            print(f"   成功處理天數：{processing_result.get('successful_days', 0)}")
            print(f"   失敗天數：{processing_result.get('failed_days', 0)}")
            print(f"   總處理員工數：{processing_result.get('total_employees_processed', 0)}")
            print(f"   總處理記錄數：{processing_result.get('total_records_processed', 0)}")
            
            # 顯示每日處理詳情（前15天）
            daily_results = processing_result.get('daily_results', [])
            if daily_results:
                print(f"\n📋 每日處理詳情（前15天）：")
                for i, daily in enumerate(daily_results[:15], 1):
                    status = "✅" if daily.get('success', False) else "❌"
                    print(f"   {i:2d}. {daily.get('date')} {status} - "
                          f"員工數：{daily.get('employees_processed', 0)}, "
                          f"記錄數：{daily.get('records_processed', 0)}")
                
                if len(daily_results) > 15:
                    print(f"   ... 還有 {len(daily_results) - 15} 天的處理結果")
        else:
            print("❌ 批量處理失敗")
            return
    
    # 步驟3：檢查處理後狀態
    print("\n🔍 步驟3：檢查處理後狀態")
    print("-" * 50)
    
    after_status = check_current_status()
    print(f"📅 處理後上次處理日期：{after_status['last_processed_date'] or '無記錄'}")
    print(f"📊 處理後需要處理天數：{after_status['total_days_to_process']} 天")
    
    # 檢查處理後的打卡記錄狀態
    after_punch_status = check_punch_records_status()
    if after_punch_status:
        print(f"📊 處理後未處理的6月打卡記錄：{after_punch_status['total_unprocessed']} 筆")
    
    # 檢查處理後的6月考勤記錄
    after_attendance = check_attendance_summary()
    if after_attendance:
        print(f"📊 處理後6月考勤記錄：{after_attendance['total_records']} 筆")
        if after_attendance['total_records'] > 0:
            print(f"📅 考勤記錄日期範圍：{after_attendance['earliest_date']} ~ {after_attendance['latest_date']}")
    
    # 步驟4：驗證處理結果
    print("\n✅ 步驟4：驗證處理結果")
    print("-" * 50)
    
    if before_status['total_days_to_process'] > 0:
        processed_days = before_status['total_days_to_process'] - after_status['total_days_to_process']
        
        print(f"📊 處理結果統計：")
        print(f"   處理前需要處理天數：{before_status['total_days_to_process']}")
        print(f"   處理後需要處理天數：{after_status['total_days_to_process']}")
        print(f"   實際處理天數：{processed_days}")
        
        if before_attendance and after_attendance:
            added_records = after_attendance['total_records'] - before_attendance['total_records']
            print(f"   新增考勤記錄數：{added_records}")
        
        if after_status['total_days_to_process'] == 0:
            print("🎉 6月份考勤處理完全成功！")
            print("✅ 所有6月份的打卡記錄都已整理成考勤記錄")
            print("✅ 增量處理功能運作正常")
            print("✅ 系統已準備好處理下個月的資料")
        elif processed_days > 0:
            print("⚠️  6月份部分處理成功")
            print(f"   剩餘未處理天數：{after_status['total_days_to_process']}")
        else:
            print("❌ 處理失敗，沒有處理任何日期")
    else:
        print("ℹ️  6月份已經完全處理完成，無需額外處理")
    
    print("\n" + "=" * 70)
    print("🏁 6月份考勤處理完成")
    print("=" * 70)

if __name__ == "__main__":
    main() 