"""
考勤業務邏輯服務
"""

from database import create_connection
from datetime import datetime, timedelta
import sqlite3

class AttendanceService:
    """考勤服務類"""
    
    def __init__(self):
        self.conn = None
    
    def get_connection(self):
        """獲取資料庫連接"""
        if not self.conn:
            self.conn = create_connection()
        return self.conn
    
    def get_attendance_records(self, filters=None):
        """
        獲取考勤記錄
        
        參數：
        filters (dict): 篩選條件
        
        返回：
        dict: 考勤記錄和統計資訊
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 基本查詢
            query = """
                SELECT ar.*, e.name as employee_name, d.name as department_name
                FROM attendance ar
                LEFT JOIN employees e ON ar.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                WHERE 1=1
            """
            
            where_conditions = []
            params = []
            
            if filters:
                if filters.get('employee_id'):
                    where_conditions.append("ar.employee_id = ?")
                    params.append(filters['employee_id'])
                
                if filters.get('department_id'):
                    where_conditions.append("e.department_id = ?")
                    params.append(filters['department_id'])
                
                if filters.get('start_date'):
                    where_conditions.append("DATE(ar.check_in) >= ?")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    where_conditions.append("DATE(ar.check_in) <= ?")
                    params.append(filters['end_date'])
                
                if filters.get('status'):
                    where_conditions.append("ar.status = ?")
                    params.append(filters['status'])
            
            if where_conditions:
                query += " AND " + " AND ".join(where_conditions)
            
            # 計算總數
            count_query = f"SELECT COUNT(*) FROM ({query})"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分頁
            page = filters.get('page', 1) if filters else 1
            per_page = filters.get('per_page', 20) if filters else 20
            offset = (page - 1) * per_page
            
            query += " ORDER BY ar.created_at DESC LIMIT ? OFFSET ?"
            params.extend([per_page, offset])
            
            cursor.execute(query, params)
            records = []
            
            for row in cursor.fetchall():
                record = {
                    'id': row[0],
                    'employee_id': row[1],
                    'check_in': row[2],
                    'check_out': row[3],
                    'status': row[4],
                    'device_id': row[5],
                    'note': row[6],
                    'created_at': row[7],
                    'employee_name': row[8],
                    'employee_code': row[9],
                    'department_name': row[10],
                    'check_in_formatted': row[2],
                    'check_out_formatted': row[3],
                    'created_at_formatted': row[7]
                }
                records.append(record)
            
            # 統計資訊
            stats_query = """
                SELECT status, COUNT(*) as count
                FROM attendance
                GROUP BY status
            """
            cursor.execute(stats_query)
            status_breakdown = {}
            for row in cursor.fetchall():
                status_breakdown[row[0]] = row[1]
            
            # 確保所有狀態都有值
            for status in ['normal', 'late', 'early_leave', 'absent', 'manual']:
                if status not in status_breakdown:
                    status_breakdown[status] = 0
            
            # 計算其他統計
            cursor.execute("SELECT COUNT(DISTINCT employee_id) FROM attendance")
            total_employees = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT DATE(check_in)) FROM attendance")
            total_days = cursor.fetchone()[0]
            
            avg_records_per_day = total_count / total_days if total_days > 0 else 0
            
            return {
                'success': True,
                'records': records,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': (total_count + per_page - 1) // per_page,
                    'has_prev': page > 1,
                    'has_next': page * per_page < total_count,
                    'limit': per_page
                },
                'statistics': {
                    'total_records': total_count,
                    'total_employees': total_employees,
                    'total_days': total_days,
                    'avg_records_per_day': round(avg_records_per_day, 1),
                    'status_breakdown': status_breakdown
                },
                'query_info': {
                    'filters_applied': len([f for f in (filters or {}).values() if f]),
                    'query_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"獲取考勤記錄失敗: {str(e)}"
            }
    
    def get_record_detail(self, record_id):
        """
        獲取單個記錄詳情
        
        參數：
        record_id (int): 記錄ID
        
        返回：
        dict: 記錄詳情
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT ar.*, e.name as employee_name, e.employee_id as employee_code,
                       d.name as department_name
                FROM attendance ar
                LEFT JOIN employees e ON ar.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                WHERE ar.id = ?
            """
            
            cursor.execute(query, (record_id,))
            row = cursor.fetchone()
            
            if not row:
                return {
                    'success': False,
                    'error': '記錄不存在'
                }
            
            record = {
                'id': row[0],
                'employee_id': row[1],
                'check_in': row[2],
                'check_out': row[3],
                'status': row[4],
                'device_id': row[5],
                'note': row[6],
                'created_at': row[7],
                'employee_name': row[8],
                'employee_code': row[9],
                'department_name': row[10]
            }
            
            return {
                'success': True,
                'data': record
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"獲取記錄詳情失敗: {str(e)}"
            }
    
    def clock_in(self, data):
        """
        上班打卡
        
        參數：
        data (dict): 打卡資料
        
        返回：
        dict: 打卡結果
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            employee_id = data.get('employee_id')
            if not employee_id:
                return {
                    'success': False,
                    'error': '缺少員工ID'
                }
            
            # 檢查員工是否存在
            cursor.execute("SELECT id FROM employees WHERE id = ?", (employee_id,))
            if not cursor.fetchone():
                return {
                    'success': False,
                    'error': '員工不存在'
                }
            
            # 創建打卡記錄
            now = datetime.now()
            cursor.execute("""
                INSERT INTO attendance 
                (employee_id, check_in, status, note, created_at)
                VALUES (?, ?, 'normal', ?, ?)
            """, (
                employee_id,
                now.strftime('%Y-%m-%d %H:%M:%S.%f'),
                data.get('notes', ''),
                now.strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            record_id = cursor.lastrowid
            conn.commit()
            
            return {
                'success': True,
                'data': {
                    'id': record_id,
                    'employee_id': employee_id,
                    'check_in': now.strftime('%Y-%m-%d %H:%M:%S.%f'),
                    'message': '上班打卡成功'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"上班打卡失敗: {str(e)}"
            }
    
    def clock_out(self, data):
        """
        下班打卡
        
        參數：
        data (dict): 打卡資料
        
        返回：
        dict: 打卡結果
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            employee_id = data.get('employee_id')
            if not employee_id:
                return {
                    'success': False,
                    'error': '缺少員工ID'
                }
            
            # 查找今天的打卡記錄
            today = datetime.now().date()
            cursor.execute("""
                SELECT id FROM attendance 
                WHERE employee_id = ? AND DATE(check_in) = ? AND check_out IS NULL
                ORDER BY created_at DESC LIMIT 1
            """, (employee_id, today))
            
            record = cursor.fetchone()
            if not record:
                return {
                    'success': False,
                    'error': '找不到今天的上班記錄'
                }
            
            # 更新下班時間
            now = datetime.now()
            cursor.execute("""
                UPDATE attendance 
                SET check_out = ?
                WHERE id = ?
            """, (now.strftime('%Y-%m-%d %H:%M:%S.%f'), record[0]))
            
            conn.commit()
            
            return {
                'success': True,
                'data': {
                    'id': record[0],
                    'employee_id': employee_id,
                    'check_out': now.strftime('%Y-%m-%d %H:%M:%S.%f'),
                    'message': '下班打卡成功'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"下班打卡失敗: {str(e)}"
            }

# 創建全局實例
attendance_service = AttendanceService() 