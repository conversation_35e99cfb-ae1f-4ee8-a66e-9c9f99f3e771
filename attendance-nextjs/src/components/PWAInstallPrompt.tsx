"use client"

import { useState, useEffect } from 'react'
import { Download, X, Smartphone, Monitor } from 'lucide-react'

interface BeforeInstallPromptEvent extends Event {
    readonly platforms: string[]
    readonly userChoice: Promise<{
        outcome: 'accepted' | 'dismissed'
        platform: string
    }>
    prompt(): Promise<void>
}

export default function PWAInstallPrompt() {
    const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
    const [showPrompt, setShowPrompt] = useState(false)
    const [isIOS, setIsIOS] = useState(false)
    const [isStandalone, setIsStandalone] = useState(false)

    useEffect(() => {
        // 檢測是否為iOS設備
        const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
        setIsIOS(iOS)

        // 檢測是否已經是獨立模式（已安裝）
        const standalone = window.matchMedia('(display-mode: standalone)').matches
        setIsStandalone(standalone)

        // 監聽beforeinstallprompt事件
        const handleBeforeInstallPrompt = (e: Event) => {
            e.preventDefault()
            setDeferredPrompt(e as BeforeInstallPromptEvent)

            // 延遲顯示提示，讓用戶先體驗應用
            setTimeout(() => {
                if (!standalone) {
                    setShowPrompt(true)
                }
            }, 3000)
        }

        // 監聽應用安裝事件
        const handleAppInstalled = () => {
            setShowPrompt(false)
            setDeferredPrompt(null)
            console.log('PWA 已安裝')
        }

        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
        window.addEventListener('appinstalled', handleAppInstalled)

        return () => {
            window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
            window.removeEventListener('appinstalled', handleAppInstalled)
        }
    }, [])

    const handleInstallClick = async () => {
        if (!deferredPrompt) return

        try {
            await deferredPrompt.prompt()
            const { outcome } = await deferredPrompt.userChoice

            if (outcome === 'accepted') {
                console.log('用戶接受安裝')
            } else {
                console.log('用戶拒絕安裝')
            }

            setDeferredPrompt(null)
            setShowPrompt(false)
        } catch (error) {
            console.error('安裝過程中發生錯誤:', error)
        }
    }

    const handleDismiss = () => {
        setShowPrompt(false)
        // 24小時後再次顯示
        if (typeof window !== 'undefined') {
            localStorage.setItem('pwa-prompt-dismissed', Date.now().toString())
        }
    }

    // 如果已經是獨立模式，不顯示提示
    if (isStandalone) return null

    // 檢查是否在24小時內被拒絕過（僅在客戶端執行）
    if (typeof window !== 'undefined') {
        const lastDismissed = localStorage.getItem('pwa-prompt-dismissed')
        if (lastDismissed && Date.now() - parseInt(lastDismissed) < 24 * 60 * 60 * 1000) {
            return null
        }
    }

    // iOS設備顯示手動安裝指引
    if (isIOS && showPrompt) {
        return (
            <div className="fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto">
                <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Smartphone className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                        <h3 className="font-bold text-gray-900 mb-1">安裝考勤系統</h3>
                        <p className="text-sm text-gray-600 mb-3">
                            點擊 Safari 底部的分享按鈕 📤，然後選擇「加入主畫面」
                        </p>
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={handleDismiss}
                                className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                稍後再說
                            </button>
                        </div>
                    </div>
                    <button
                        onClick={handleDismiss}
                        className="w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
                    >
                        <X className="w-4 h-4" />
                    </button>
                </div>
            </div>
        )
    }

    // Android/Desktop 顯示安裝按鈕
    if (deferredPrompt && showPrompt) {
        return (
            <div className="fixed bottom-4 left-4 right-4 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-w-sm mx-auto">
                <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Monitor className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                        <h3 className="font-bold text-gray-900 mb-1">安裝考勤系統</h3>
                        <p className="text-sm text-gray-600 mb-3">
                            安裝到您的設備，享受更好的使用體驗
                        </p>
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={handleInstallClick}
                                className="flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
                            >
                                <Download className="w-4 h-4" />
                                <span>安裝</span>
                            </button>
                            <button
                                onClick={handleDismiss}
                                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                稍後再說
                            </button>
                        </div>
                    </div>
                    <button
                        onClick={handleDismiss}
                        className="w-6 h-6 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
                    >
                        <X className="w-4 h-4" />
                    </button>
                </div>
            </div>
        )
    }

    return null
} 