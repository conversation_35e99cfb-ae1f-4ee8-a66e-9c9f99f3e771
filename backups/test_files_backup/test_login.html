<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登錄測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <h2>員工登錄測試</h2>

    <form id="loginForm">
        <div class="form-group">
            <label for="employeeId">員工編號:</label>
            <input type="text" id="employeeId" value="E001" required>
        </div>

        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" value="password123" required>
        </div>

        <button type="submit" id="loginBtn">登錄</button>
    </form>

    <div id="message" class="message hidden"></div>

    <div style="margin-top: 20px;">
        <h3>測試結果:</h3>
        <div id="testResults"></div>
    </div>

    <script>
        const form = document.getElementById('loginForm');
        const employeeIdInput = document.getElementById('employeeId');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const messageDiv = document.getElementById('message');
        const testResults = document.getElementById('testResults');

        function showMessage(text, type = 'error') {
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.classList.remove('hidden');
        }

        function hideMessage() {
            messageDiv.classList.add('hidden');
        }

        function addTestResult(text) {
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + text;
            testResults.appendChild(p);
        }

        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const employeeId = employeeIdInput.value.trim();
            const password = passwordInput.value.trim();

            if (!employeeId || !password) {
                showMessage('請填寫完整資訊', 'error');
                return;
            }

            loginBtn.disabled = true;
            loginBtn.textContent = '登錄中...';
            hideMessage();

            addTestResult('開始登錄測試...');
            addTestResult(`員工編號: ${employeeId}`);
            addTestResult(`密碼: ${password.replace(/./g, '*')}`);

            try {
                addTestResult('發送登錄請求...');

                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: employeeId,
                        password: password,
                        remember_me: false
                    })
                });

                addTestResult(`響應狀態: ${response.status} ${response.statusText}`);

                const result = await response.json();
                addTestResult(`響應內容: ${JSON.stringify(result, null, 2)}`);

                if (response.ok) {
                    showMessage('登錄成功！', 'success');
                    addTestResult('✅ 登錄成功');

                    // 測試跳轉
                    setTimeout(() => {
                        addTestResult('準備跳轉到 /user...');
                        window.location.href = '/user';
                    }, 2000);
                } else {
                    showMessage(result.error || '登錄失敗', 'error');
                    addTestResult('❌ 登錄失敗: ' + (result.error || '未知錯誤'));
                }
            } catch (error) {
                addTestResult('❌ 網路錯誤: ' + error.message);
                showMessage('網路連線錯誤: ' + error.message, 'error');
                console.error('登錄錯誤:', error);
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登錄';
            }
        });

        // 頁面載入時的測試
        addTestResult('頁面載入完成');
        addTestResult('當前URL: ' + window.location.href);

        // 測試API連通性
        fetch('/api/auth/verify')
            .then(response => {
                addTestResult(`認證API狀態: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addTestResult(`認證API響應: ${JSON.stringify(data)}`);
            })
            .catch(error => {
                addTestResult(`認證API錯誤: ${error.message}`);
            });
    </script>
</body>

</html>