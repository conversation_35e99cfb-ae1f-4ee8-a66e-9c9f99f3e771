#!/usr/bin/env python3
"""
動態基本資料管理功能測試腳本

此腳本測試所有動態基本資料管理 API 的功能：
- 打卡狀態類型
- 員工狀態類型  
- 假別類型
"""

import requests
import json
import sys

BASE_URL = "http://localhost:7072"

def test_api_endpoint(endpoint, description):
    """測試單一 API 端點"""
    try:
        print(f"\n🔍 測試 {description}...")
        response = requests.get(f"{BASE_URL}{endpoint}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {description} 測試成功")
            
            # 顯示基本統計
            if 'items' in data:
                print(f"   📊 項目數量: {data.get('total', len(data['items']))}")
                print(f"   📋 表格名稱: {data.get('table_name', 'N/A')}")
                print(f"   🏷️  顯示名稱: {data.get('display_name', 'N/A')}")
                
                # 顯示前3個項目
                items = data['items'][:3]
                for item in items:
                    if 'code' in item and 'name' in item:
                        print(f"   - {item['code']}: {item['name']}")
                    elif 'status_code' in item and 'status_name' in item:
                        print(f"   - {item['status_code']}: {item['status_name']}")
                    else:
                        print(f"   - {item.get('name', str(item))}")
                        
                if len(data['items']) > 3:
                    print(f"   ... 還有 {len(data['items']) - 3} 個項目")
            else:
                print(f"   📊 項目數量: {data.get('total', 'N/A')}")
                
            return True
        else:
            print(f"❌ {description} 測試失敗: HTTP {response.status_code}")
            print(f"   錯誤信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ {description} 測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試動態基本資料管理功能")
    print("=" * 50)
    
    # 測試項目列表
    test_cases = [
        ("/api/masterdata/clock_status_types", "打卡狀態類型 API"),
        ("/api/masterdata/employee-status", "員工狀態類型 API"),
        ("/api/masterdata/leave_types", "假別類型 API"),
        ("/api/clock-status-types", "舊版打卡狀態 API（相容性測試）"),
    ]
    
    # 執行測試
    passed = 0
    total = len(test_cases)
    
    for endpoint, description in test_cases:
        if test_api_endpoint(endpoint, description):
            passed += 1
    
    # 測試結果摘要
    print("\n" + "=" * 50)
    print("📊 測試結果摘要")
    print(f"✅ 通過: {passed}/{total}")
    print(f"❌ 失敗: {total - passed}/{total}")
    print(f"📈 成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有動態基本資料管理功能測試通過！")
        print("💡 系統已成功實現：")
        print("   - 打卡狀態動態載入")
        print("   - 員工狀態動態管理")
        print("   - 假別類型動態配置")
        print("   - 前端自動更新機制")
        print("   - 容錯處理機制")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 個測試失敗，請檢查系統配置")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 測試過程中發生未預期錯誤: {str(e)}")
        sys.exit(1)