<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>AttendanceOS Mobile</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#7c6df2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="AttendanceOS">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        /* 移動端優化樣式 */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            touch-action: manipulation;
        }
        
        /* 安全區域適配 */
        .safe-area-top {
            padding-top: env(safe-area-inset-top);
        }
        
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* 觸摸反饋 */
        .touch-feedback {
            transition: all 0.15s ease;
            -webkit-tap-highlight-color: transparent;
        }
        
        .touch-feedback:active {
            transform: scale(0.98);
            opacity: 0.8;
        }
        
        /* 卡片陰影 */
        .card-shadow {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        
        /* 漸層背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 底部導航固定 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        /* 內容區域底部間距 */
        .content-with-nav {
            padding-bottom: 80px;
        }
        
        /* 滑動手勢 */
        .swipe-container {
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .swipe-container::-webkit-scrollbar {
            display: none;
        }
        
        /* 動畫 */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-slide-up {
            animation: slideUp 0.3s ease-out;
        }
        
        /* 脈衝動畫 */
        .pulse-ring {
            animation: pulse-ring 1.5s ease-out infinite;
        }
        
        @keyframes pulse-ring {
            0% {
                transform: scale(0.33);
            }
            40%, 50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(1.33);
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 頂部狀態欄 -->
    <div class="safe-area-top bg-white border-b border-gray-100">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-brand-500 to-brand-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="zap" class="w-4 h-4 text-white"></i>
                </div>
                <div>
                    <h1 class="text-lg font-bold text-gray-900">AttendanceOS</h1>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button class="p-2 rounded-lg hover:bg-gray-100 touch-feedback">
                    <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                </button>
                <button class="p-2 rounded-lg hover:bg-gray-100 touch-feedback">
                    <i data-lucide="user" class="w-5 h-5 text-gray-600"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 主內容區 -->
    <div class="content-with-nav">
        <!-- 歡迎區域 -->
        <div class="gradient-bg px-4 py-6 text-white">
            <div class="animate-slide-up">
                <h2 class="text-xl font-bold mb-1">早安，張經理</h2>
                <p class="text-white/80 text-sm">今天是 2024年5月28日 星期二</p>
                <div class="mt-4 flex items-center space-x-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold">09:00</p>
                        <p class="text-white/80 text-xs">當前時間</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold">8.5h</p>
                        <p class="text-white/80 text-xs">今日工時</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速打卡區域 -->
        <div class="px-4 -mt-6 relative z-10">
            <div class="bg-white rounded-2xl p-6 card-shadow animate-slide-up">
                <div class="text-center">
                    <div class="relative inline-block mb-4">
                        <button class="w-24 h-24 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center text-white shadow-lg touch-feedback relative">
                            <i data-lucide="clock" class="w-8 h-8"></i>
                            <!-- 脈衝環 -->
                            <div class="absolute inset-0 rounded-full bg-brand-500 opacity-75 pulse-ring"></div>
                        </button>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">快速打卡</h3>
                    <p class="text-sm text-gray-500 mb-4">點擊上方按鈕進行打卡</p>
                    <div class="flex space-x-3">
                        <button class="flex-1 bg-success-500 text-white py-3 px-4 rounded-xl font-medium touch-feedback">
                            上班打卡
                        </button>
                        <button class="flex-1 bg-gray-500 text-white py-3 px-4 rounded-xl font-medium touch-feedback">
                            下班打卡
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 今日統計 -->
        <div class="px-4 mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">今日概況</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white rounded-xl p-4 card-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-success-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="users-check" class="w-5 h-5 text-success-600"></i>
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">156</p>
                            <p class="text-xs text-gray-500">已出勤</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 card-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-warning-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="clock-alert" class="w-5 h-5 text-warning-600"></i>
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">8</p>
                            <p class="text-xs text-gray-500">遲到</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 card-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-brand-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="file-text" class="w-5 h-5 text-brand-600"></i>
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">12</p>
                            <p class="text-xs text-gray-500">請假申請</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 card-shadow">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="trending-up" class="w-5 h-5 text-gray-600"></i>
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">97%</p>
                            <p class="text-xs text-gray-500">出勤率</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活動 -->
        <div class="px-4 mt-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">最近活動</h3>
                <button class="text-sm text-brand-600 font-medium">查看全部</button>
            </div>
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-4 space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-success-50 rounded-full flex items-center justify-center">
                            <i data-lucide="check" class="w-4 h-4 text-success-600"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">張小明 正常上班</p>
                            <p class="text-xs text-gray-500">09:00 · 技術部</p>
                        </div>
                        <span class="text-xs text-gray-400">剛剛</span>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-warning-50 rounded-full flex items-center justify-center">
                            <i data-lucide="clock" class="w-4 h-4 text-warning-600"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">李小華 遲到15分鐘</p>
                            <p class="text-xs text-gray-500">09:15 · 行銷部</p>
                        </div>
                        <span class="text-xs text-gray-400">5分鐘前</span>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-brand-50 rounded-full flex items-center justify-center">
                            <i data-lucide="file-text" class="w-4 h-4 text-brand-600"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">王大明 申請病假</p>
                            <p class="text-xs text-gray-500">待審批 · 財務部</p>
                        </div>
                        <span class="text-xs text-gray-400">10分鐘前</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速功能 -->
        <div class="px-4 mt-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">快速功能</h3>
            <div class="swipe-container">
                <div class="flex space-x-4 pb-2">
                    <button class="flex-shrink-0 w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-600 rounded-xl flex flex-col items-center justify-center text-white touch-feedback">
                        <i data-lucide="calendar-plus" class="w-6 h-6 mb-1"></i>
                        <span class="text-xs">請假</span>
                    </button>
                    
                    <button class="flex-shrink-0 w-20 h-20 bg-gradient-to-br from-success-500 to-success-600 rounded-xl flex flex-col items-center justify-center text-white touch-feedback">
                        <i data-lucide="calendar" class="w-6 h-6 mb-1"></i>
                        <span class="text-xs">排班</span>
                    </button>
                    
                    <button class="flex-shrink-0 w-20 h-20 bg-gradient-to-br from-warning-500 to-warning-600 rounded-xl flex flex-col items-center justify-center text-white touch-feedback">
                        <i data-lucide="bar-chart-3" class="w-6 h-6 mb-1"></i>
                        <span class="text-xs">報表</span>
                    </button>
                    
                    <button class="flex-shrink-0 w-20 h-20 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex flex-col items-center justify-center text-white touch-feedback">
                        <i data-lucide="settings" class="w-6 h-6 mb-1"></i>
                        <span class="text-xs">設定</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部導航 -->
    <div class="bottom-nav safe-area-bottom bg-white border-t border-gray-100">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-4 text-brand-600 touch-feedback">
                <i data-lucide="home" class="w-5 h-5 mb-1"></i>
                <span class="text-xs font-medium">首頁</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-400 touch-feedback">
                <i data-lucide="clock" class="w-5 h-5 mb-1"></i>
                <span class="text-xs">考勤</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-400 touch-feedback">
                <i data-lucide="calendar" class="w-5 h-5 mb-1"></i>
                <span class="text-xs">排班</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-400 touch-feedback">
                <i data-lucide="user" class="w-5 h-5 mb-1"></i>
                <span class="text-xs">我的</span>
            </button>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // 初始化 Lucide 圖標
        lucide.createIcons();
        
        // 實時時間更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-TW', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            const timeElement = document.querySelector('.gradient-bg .text-2xl');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        updateTime();
        setInterval(updateTime, 1000);
        
        // 觸摸反饋
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('.touch-feedback')) {
                e.target.closest('.touch-feedback').style.transform = 'scale(0.98)';
            }
        });
        
        document.addEventListener('touchend', function(e) {
            if (e.target.closest('.touch-feedback')) {
                setTimeout(() => {
                    e.target.closest('.touch-feedback').style.transform = 'scale(1)';
                }, 150);
            }
        });
        
        // 頁面載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.animate-slide-up');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // PWA 安裝提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            // 顯示安裝提示
        });
        
        // 防止雙擊縮放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>