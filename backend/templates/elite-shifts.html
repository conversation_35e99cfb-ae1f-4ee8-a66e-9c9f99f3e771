<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理 - Han AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 載入設計系統CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>

    <style>
        /* 自訂捲軸 */
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 班別卡片樣式 */
        
        .shift-card {
            transition: all 0.3s ease;
        }
        
        .shift-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- 🔙 返回導航 - 固定位置返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
            <i data-lucide="arrow-left" class="w-4 h-4"></i>
            <span class="font-medium">返回管理後台</span>
        </a>
    </div>

    <!-- 📦 主容器 - 內容區域容器 -->
    <div class="pt-16 p-6">
        <!-- 🎨 頁面標題 - 統一的標題設計 -->
        <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
            <div class="flex items-center justify-between">
                <!-- 📍 左側標題區 -->
                <div class="relative z-10">
                    <h1 class="text-3xl font-bold mb-2 text-white">排班管理中心</h1>
                    <p class="text-indigo-100 text-base font-medium">靈活的班別設定，智能的加班計算，高效的排班管理</p>
                </div>

                <!-- 📍 右側資訊區 -->
                <div class="flex items-center space-x-3 text-right">
                    <div>
                        <p class="text-sm font-medium text-white">管理員模式</p>
                        <p class="text-xs text-indigo-100">排班管理</p>
                    </div>
                    <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                        <i data-lucide="calendar-clock" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 📊 工具欄 - 列表標題和統計 -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
            <div class="flex items-center justify-between">
                <!-- 📍 左側標題和統計 -->
                <div class="flex items-center space-x-4">
                    <h2 class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">班別設定列表</h2>
                    <span id="shiftsCount" class="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                        載入中...
                    </span>
                </div>

                <!-- 📍 右側操作按鈕 -->
                <div class="flex items-center space-x-3">
                    <!-- 🔄 重新整理按鈕 -->
                    <button onclick="loadShifts()" class="flex items-center space-x-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span class="font-medium">重新整理</span>
                    </button>

                    <!-- 🆕 新增班別按鈕 -->
                    <button onclick="openAddModal()" class="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span class="font-medium">新增班別</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 📋 數據列表容器 -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            <!-- 🔄 載入狀態 -->
            <div id="loadingState" class="flex items-center justify-center py-16">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                        <div class="relative">
                            <div class="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                    <p class="text-gray-500">正在載入班別資料</p>
                </div>
            </div>

            <!-- 💻 桌面版表格 -->
            <div id="desktopTable" class="hidden lg:block">
                <!-- 🏷️ 表格標題行 -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                    <div class="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                        <div class="col-span-2 flex items-center space-x-2">
                            <i data-lucide="tag" class="w-4 h-4 text-indigo-600"></i>
                            <span>班別名稱</span>
                        </div>
                        <div class="col-span-1 flex items-center space-x-2">
                            <i data-lucide="code" class="w-4 h-4 text-purple-600"></i>
                            <span>代碼</span>
                        </div>
                        <div class="col-span-2 flex items-center space-x-2">
                            <i data-lucide="clock" class="w-4 h-4 text-blue-600"></i>
                            <span>工作時間</span>
                        </div>
                        <div class="col-span-1 flex items-center space-x-2">
                            <i data-lucide="timer" class="w-4 h-4 text-green-600"></i>
                            <span>工時</span>
                        </div>
                        <div class="col-span-2 flex items-center space-x-2">
                            <i data-lucide="coffee" class="w-4 h-4 text-orange-600"></i>
                            <span>休息時間</span>
                        </div>
                        <div class="col-span-2 flex items-center space-x-2">
                            <i data-lucide="zap" class="w-4 h-4 text-yellow-600"></i>
                            <span>加班設定</span>
                        </div>
                        <div class="col-span-2 flex items-center space-x-2">
                            <i data-lucide="settings" class="w-4 h-4 text-gray-600"></i>
                            <span>操作</span>
                        </div>
                    </div>
                </div>

                <!-- 📊 表格內容區 -->
                <div id="shiftsTableBody" class="divide-y divide-gray-100">
                    <!-- 班別記錄將在這裡動態生成 -->
                </div>
            </div>

            <!-- 📱 手機版卡片 -->
            <div id="mobileCards" class="lg:hidden space-y-4 p-4">
                <!-- 手機版卡片將在這裡動態生成 -->
            </div>

            <!-- 🚫 空狀態 -->
            <div id="emptyState" class="hidden text-center py-16">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
                    <i data-lucide="calendar-clock" class="w-8 h-8 text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">尚未設定任何班別</h3>
                <p class="text-gray-500 mb-4">開始建立您的第一個班別設定</p>
                <button onclick="openAddModal()" class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg">
                                    新增班別
                                </button>
            </div>
        </div>
    </div>

    <!-- 🔧 新增/編輯班別模態框 -->
    <div id="shiftModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <!-- 🎨 模態框標題 -->
                <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-6 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                <i data-lucide="calendar-clock" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <h3 id="modalTitle" class="text-xl font-bold text-white">新增班別</h3>
                                <p class="text-indigo-100 text-sm">設定班別的基本資訊和工作時間</p>
                            </div>
                        </div>
                        <button onclick="closeModal()" class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <!-- 📝 表單內容 -->
                <form id="shiftForm" class="p-6">
                    <input type="hidden" id="shiftId" name="id">

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 左側：基本資訊 -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-700 border-b border-gray-200 pb-2 flex items-center space-x-2">
                                <i data-lucide="info" class="w-4 h-4 text-indigo-600"></i>
                                <span>基本資訊</span>
                            </h4>

                            <!-- 班別名稱 -->
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="tag" class="w-4 h-4 text-blue-600"></i>
                                    <label class="block text-sm font-semibold text-gray-700">班別名稱 <span class="text-red-500">*</span></label>
                                </div>
                                <input type="text" name="name" required class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm" placeholder="例如：標準日班">
                            </div>

                            <!-- 班別代碼 -->
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="code" class="w-4 h-4 text-purple-600"></i>
                                    <label class="block text-sm font-semibold text-gray-700">班別代碼 <span class="text-red-500">*</span></label>
                                </div>
                                <input type="text" name="code" required class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm" placeholder="例如：STANDARD_DAY">
                            </div>

                            <!-- 顏色代碼 -->
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="palette" class="w-4 h-4 text-pink-600"></i>
                                    <label class="block text-sm font-semibold text-gray-700">顏色代碼</label>
                                </div>
                                <input type="color" name="color_code" value="#3B82F6" class="w-full h-12 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <!-- 描述 -->
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="file-text" class="w-4 h-4 text-gray-600"></i>
                                    <label class="block text-sm font-semibold text-gray-700">描述</label>
                                </div>
                                <textarea name="description" rows="3" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm" placeholder="班別描述..."></textarea>
                            </div>
                        </div>

                        <!-- 右側：時間設定 -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-700 border-b border-gray-200 pb-2 flex items-center space-x-2">
                                <i data-lucide="clock" class="w-4 h-4 text-indigo-600"></i>
                                <span>時間設定</span>
                            </h4>

                            <!-- 工作時間 -->
                            <div class="grid grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="sunrise" class="w-4 h-4 text-orange-600"></i>
                                        <label class="block text-sm font-semibold text-gray-700">上班時間 <span class="text-red-500">*</span></label>
                                    </div>
                                    <input type="time" name="start_time" required class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="sunset" class="w-4 h-4 text-orange-600"></i>
                                        <label class="block text-sm font-semibold text-gray-700">下班時間 <span class="text-red-500">*</span></label>
                                    </div>
                                    <input type="time" name="end_time" required class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                </div>
                            </div>

                            <!-- 當天起算時間 -->
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="calendar" class="w-4 h-4 text-green-600"></i>
                                    <label class="block text-sm font-semibold text-gray-700">當天起算時間 <span class="text-red-500">*</span></label>
                                </div>
                                <input type="time" name="day_start_time" required value="06:00" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                <p class="text-xs text-gray-500">此時間之前的打卡將歸屬前一天</p>
                            </div>

                            <!-- 休息時間 -->
                            <div class="grid grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="coffee" class="w-4 h-4 text-brown-600"></i>
                                        <label class="block text-sm font-semibold text-gray-700">休息開始時間</label>
                                    </div>
                                    <input type="time" name="break_start_time" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="timer" class="w-4 h-4 text-brown-600"></i>
                                        <label class="block text-sm font-semibold text-gray-700">休息時長（分鐘）</label>
                                    </div>
                                    <input type="number" name="break_duration_minutes" value="60" min="0" max="480" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                </div>
                            </div>

                            <!-- 加班設定 -->
                            <div class="space-y-3">
                                <h5 class="font-medium text-gray-700 flex items-center space-x-2">
                                    <i data-lucide="zap" class="w-4 h-4 text-yellow-600"></i>
                                    <span>加班設定</span>
                                </h5>

                                <div class="grid grid-cols-2 gap-4">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">上班前加班門檻（分鐘）</label>
                                        <input type="number" name="pre_overtime_threshold_minutes" value="0" min="0" max="240" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">下班後加班門檻（分鐘）</label>
                                        <input type="number" name="post_overtime_threshold_minutes" value="0" min="0" max="240" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm">
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" name="enable_pre_overtime" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                <span class="text-sm font-medium text-gray-700">啟用上班前加班</span>
                                            </label>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" name="enable_post_overtime" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                <span class="text-sm font-medium text-gray-700">啟用下班後加班</span>
                                            </label>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" name="auto_calculate_overtime" checked class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                <span class="text-sm font-medium text-gray-700">自動計算加班</span>
                                            </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 🎛️ 操作按鈕區 -->
                    <div class="flex flex-wrap items-center justify-between gap-4 pt-6 border-t border-gray-100 mt-6">
                        <!-- 📍 左側取消按鈕 -->
                        <button type="button" onclick="closeModal()" class="flex items-center space-x-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-2 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                            <i data-lucide="x" class="w-4 h-4"></i>
                            <span class="font-medium">取消</span>
                        </button>

                        <!-- 📍 右側提交按鈕 -->
                        <button type="submit" class="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                            <i data-lucide="save" class="w-4 h-4"></i>
                            <span class="font-medium submit-text">儲存班別</span>
                            <span class="font-medium loading-text hidden">儲存中...</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 初始化圖標
        lucide.createIcons();

        // 全域變數
        let shifts = [];
        let currentShift = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工具函數庫
            try {
                if (typeof UtilsLoader !== 'undefined') {
                    UtilsLoader.initPageUtils('elite-shifts');
                    console.log('工具函數庫初始化成功');
                } else {
                    console.warn('工具函數庫未載入，使用降級處理');
                }
            } catch (error) {
                console.error('工具函數庫初始化失敗:', error);
            }

            loadShifts();
            setupFormHandlers();
        });

        // 載入班別資料
        async function loadShifts() {
            try {
                showLoading();
                const response = await fetch('/api/shifts');
                const data = await response.json();

                // 正確取出shifts陣列
                shifts = data.shifts || [];
                renderShifts();
                updateShiftsCount();
                hideLoading();
            } catch (error) {
                console.error('載入班別資料失敗:', error);
                showNotification('載入班別資料失敗', 'error');
                hideLoading();
            }
        }

        // 更新班別統計
        function updateShiftsCount() {
            const countElement = document.getElementById('shiftsCount');
            if (countElement) {
                countElement.textContent = `${shifts.length} 個班別`;
            }
        }

        // 渲染班別列表
        function renderShifts() {
            const desktopTable = document.getElementById('shiftsTableBody');
            const mobileCards = document.getElementById('mobileCards');
            const emptyState = document.getElementById('emptyState');

            // 清空現有內容
            desktopTable.innerHTML = '';
            mobileCards.innerHTML = '';

            if (shifts.length === 0) {
                document.getElementById('desktopTable').classList.add('hidden');
                document.getElementById('mobileCards').classList.add('hidden');
                emptyState.classList.remove('hidden');
                return;
            }

            document.getElementById('desktopTable').classList.remove('hidden');
            document.getElementById('mobileCards').classList.remove('hidden');
            emptyState.classList.add('hidden');

            shifts.forEach(shift => {
                // 桌面版表格行
                const row = createDesktopRow(shift);
                desktopTable.appendChild(row);

                // 手機版卡片
                const card = createMobileCard(shift);
                mobileCards.appendChild(card);
            });

            lucide.createIcons();
        }

        // 創建桌面版表格行
        function createDesktopRow(shift) {
            const row = document.createElement('div');
            row.className = 'px-6 py-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200';

            const workHours = calculateWorkHours(shift.start_time, shift.end_time, shift.break_duration_minutes);
            const overtimeText = getOvertimeText(shift);

            row.innerHTML = `
                <div class="grid grid-cols-12 gap-4 items-center text-sm">
                    <div class="col-span-2 flex items-center space-x-3">
                        <div class="w-4 h-4 rounded-full" style="background-color: ${shift.color_code}"></div>
                        <span class="font-medium text-gray-900">${shift.name}</span>
                    </div>
                    <div class="col-span-1">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            ${shift.code}
                        </span>
                    </div>
                    <div class="col-span-2 text-gray-600">
                        ${shift.start_time} - ${shift.end_time}
                    </div>
                    <div class="col-span-1 text-gray-600">
                        ${workHours} 小時
                    </div>
                    <div class="col-span-2 text-gray-600">
                        ${shift.break_start_time ? `${shift.break_start_time} (${shift.break_duration_minutes}分鐘)` : '無設定'}
                    </div>
                    <div class="col-span-2 text-gray-600">
                        ${overtimeText}
                    </div>
                    <div class="col-span-2 flex items-center space-x-2">
                        <button onclick="editShift(${shift.id})" class="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm font-medium">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                            <span>編輯</span>
                        </button>
                        <button onclick="deleteShift(${shift.id})" class="flex items-center space-x-1 text-red-600 hover:text-red-700 text-sm font-medium">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                            <span>刪除</span>
                        </button>
                    </div>
                </div>
            `;

            return row;
        }

        // 創建手機版卡片
        function createMobileCard(shift) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-xl p-4 shadow-sm border border-gray-200';

            const workHours = calculateWorkHours(shift.start_time, shift.end_time, shift.break_duration_minutes);
            const overtimeText = getOvertimeText(shift);

            card.innerHTML = `
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-4 h-4 rounded-full" style="background-color: ${shift.color_code}"></div>
                        <h3 class="font-semibold text-gray-900">${shift.name}</h3>
                    </div>
                    <span class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">${shift.code}</span>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600 mb-4">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="clock" class="w-4 h-4"></i>
                        <span>${shift.start_time} - ${shift.end_time} (${workHours}小時)</span>
                    </div>
                    ${shift.break_start_time ? `
                        <div class="flex items-center space-x-2">
                            <i data-lucide="coffee" class="w-4 h-4"></i>
                            <span>休息：${shift.break_start_time} (${shift.break_duration_minutes}分鐘)</span>
                        </div>
                    ` : ''}
                    <div class="flex items-center space-x-2">
                        <i data-lucide="zap" class="w-4 h-4"></i>
                        <span>${overtimeText}</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-500">${shift.description || '無描述'}</span>
                    <div class="flex space-x-2">
                        <button onclick="editShift(${shift.id})" class="text-blue-600 hover:text-blue-700 text-sm">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button onclick="deleteShift(${shift.id})" class="text-red-600 hover:text-red-700 text-sm">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            `;

            return card;
        }

        // 計算工作時數
        function calculateWorkHours(startTime, endTime, breakMinutes = 0) {
            const start = new Date(`2000-01-01 ${startTime}`);
            const end = new Date(`2000-01-01 ${endTime}`);
            
            // 處理跨日情況
            if (end < start) {
                end.setDate(end.getDate() + 1);
            }
            
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);
            const workHours = diffHours - (breakMinutes / 60);
            
            return workHours.toFixed(1);
        }

        // 獲取加班設定文字
        function getOvertimeText(shift) {
            if (!shift.auto_calculate_overtime) {
                return '不計算加班';
            }
            
            const parts = [];
            if (shift.enable_pre_overtime) {
                parts.push(`上班前${shift.pre_overtime_threshold_minutes}分鐘`);
            }
            if (shift.enable_post_overtime) {
                parts.push(`下班後${shift.post_overtime_threshold_minutes}分鐘`);
            }
            
            if (parts.length === 0) {
                return '無加班設定';
            }
            
            return `加班門檻：${parts.join('、')}`;
        }

        // 設定表單處理器
        function setupFormHandlers() {
            const form = document.getElementById('shiftForm');
            form.addEventListener('submit', handleShiftSubmit);
        }

        // 處理班別表單提交
        async function handleShiftSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 處理複選框
            data.enable_pre_overtime = formData.has('enable_pre_overtime');
            data.enable_post_overtime = formData.has('enable_post_overtime');
            data.auto_calculate_overtime = formData.has('auto_calculate_overtime');
            
            // 轉換數值類型
            data.break_duration_minutes = parseInt(data.break_duration_minutes) || 0;
            data.pre_overtime_threshold_minutes = parseInt(data.pre_overtime_threshold_minutes) || 0;
            data.post_overtime_threshold_minutes = parseInt(data.post_overtime_threshold_minutes) || 0;

            try {
                const submitBtn = e.target.querySelector('button[type="submit"]');
                const submitText = submitBtn.querySelector('.submit-text');
                const loadingText = submitBtn.querySelector('.loading-text');
                
                submitText.classList.add('hidden');
                loadingText.classList.remove('hidden');
                submitBtn.disabled = true;

                const isEdit = data.id && data.id !== '';
                const url = isEdit ? `/api/shifts/${data.id}` : '/api/shifts';
                const method = isEdit ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    showNotification(isEdit ? '班別更新成功！' : '班別新增成功！', 'success');
                    closeModal();
                    loadShifts();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '操作失敗');
                }
            } catch (error) {
                console.error('班別操作失敗:', error);
                showNotification(error.message || '班別操作失敗', 'error');
            } finally {
                const submitBtn = e.target.querySelector('button[type="submit"]');
                const submitText = submitBtn.querySelector('.submit-text');
                const loadingText = submitBtn.querySelector('.loading-text');
                
                submitText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                submitBtn.disabled = false;
            }
        }

        // 開啟新增模態框
        function openAddModal() {
            resetShiftForm();
            document.getElementById('modalTitle').textContent = '新增班別';
            document.getElementById('shiftModal').classList.remove('hidden');
        }

        // 編輯班別
        function editShift(shiftId) {
            const shift = shifts.find(s => s.id === shiftId);
            if (!shift) return;

            currentShift = shift;
            const form = document.getElementById('shiftForm');
            
            // 填充表單
            form.querySelector('[name="id"]').value = shift.id || '';
            form.querySelector('[name="name"]').value = shift.name || '';
            form.querySelector('[name="code"]').value = shift.code || '';
            form.querySelector('[name="color_code"]').value = shift.color_code || '#3B82F6';
            form.querySelector('[name="start_time"]').value = shift.start_time || '';
            form.querySelector('[name="end_time"]').value = shift.end_time || '';
            form.querySelector('[name="day_start_time"]').value = shift.day_start_time || '06:00';
            form.querySelector('[name="break_start_time"]').value = shift.break_start_time || '';
            form.querySelector('[name="break_duration_minutes"]').value = shift.break_duration_minutes || 60;
            form.querySelector('[name="pre_overtime_threshold_minutes"]').value = shift.pre_overtime_threshold_minutes || 0;
            form.querySelector('[name="post_overtime_threshold_minutes"]').value = shift.post_overtime_threshold_minutes || 0;
            form.querySelector('[name="description"]').value = shift.description || '';
            
            // 設定複選框
            form.querySelector('[name="enable_pre_overtime"]').checked = shift.enable_pre_overtime || false;
            form.querySelector('[name="enable_post_overtime"]').checked = shift.enable_post_overtime || false;
            form.querySelector('[name="auto_calculate_overtime"]').checked = shift.auto_calculate_overtime !== false;

            document.getElementById('modalTitle').textContent = '編輯班別';
            document.getElementById('shiftModal').classList.remove('hidden');
        }

        // 刪除班別
        async function deleteShift(shiftId) {
            if (!confirm('確定要刪除這個班別嗎？此操作無法復原。')) {
                return;
            }

            try {
                const response = await fetch(`/api/shifts/${shiftId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showNotification('班別刪除成功！', 'success');
                    loadShifts();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '刪除失敗');
                }
            } catch (error) {
                console.error('刪除班別失敗:', error);
                showNotification(error.message || '刪除班別失敗', 'error');
            }
        }

        // 重置班別表單
        function resetShiftForm() {
            const form = document.getElementById('shiftForm');
            form.reset();
            form.querySelector('[name="color_code"]').value = '#3B82F6';
            form.querySelector('[name="day_start_time"]').value = '06:00';
            form.querySelector('[name="break_duration_minutes"]').value = 60;
            form.querySelector('[name="auto_calculate_overtime"]').checked = true;
            currentShift = null;
        }

        // 關閉模態框
        function closeModal() {
            document.getElementById('shiftModal').classList.add('hidden');
        }

        // 顯示載入狀態
        function showLoading() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('desktopTable').classList.add('hidden');
            document.getElementById('mobileCards').classList.add('hidden');
            document.getElementById('emptyState').classList.add('hidden');
        }

        // 隱藏載入狀態
        function hideLoading() {
            document.getElementById('loadingState').classList.add('hidden');
        }

        // 顯示通知
        function showNotification(message, type = 'info') {
            // 嘗試使用工具函數庫的通知系統
            if (typeof window.NotificationSystem !== 'undefined') {
                window.NotificationSystem.show(message, type);
            } else {
                // 降級處理：使用簡單的alert
                if (type === 'error') {
                    alert('錯誤：' + message);
                } else {
                    alert(message);
                }
            }
        }

        // 點擊模態框外部關閉
        document.getElementById('shiftModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
        }
        });
    </script>
</body>

</html>

</html>