# API 標準化規範文件

## 目的
本文件定義考勤管理系統的API標準格式，確保前後端介面一致性，避免欄位名稱不匹配問題。

## 通用API回應格式

### 成功回應
```json
{
    "success": true,
    "data": { ... },
    "message": "操作成功",
    "timestamp": "2025-06-06T13:46:00Z"
}
```

### 錯誤回應
```json
{
    "success": false,
    "error": "錯誤訊息",
    "error_code": "ERROR_CODE",
    "timestamp": "2025-06-06T13:46:00Z"
}
```

## 標準欄位名稱對照表

### 員工相關欄位
| 前端期待 | API回應 | 說明 |
|---------|---------|------|
| `employee_id` | `employee_id` | 員工ID（統一使用） |
| `employee_code` | `employee_code` | 員工代碼（統一使用） |
| `employee_name` | `employee_name` | 員工姓名 |
| `department_name` | `department_name` | 部門名稱 |

### 請假審核相關欄位
| 前端期待 | API回應 | 說明 |
|---------|---------|------|
| `pending_count` | `pending_count` | 待審核數量（統一使用） |
| `records` | `records` | 記錄列表（統一使用） |
| `leaves` | `records` | ❌ 已棄用，改用records |
| `pending` | `pending_count` | ❌ 已棄用，改用pending_count |

### 請假申請相關欄位
| 前端期待 | API回應 | 說明 |
|---------|---------|------|
| `leave_type` | `leave_type` | 請假類型代碼 |
| `substitute_id` | `substitute_id` | 代理人ID |
| `substitute_name` | `substitute_name` | 代理人姓名 |
| `approver_id` | `approver_id` | 審核主管ID |
| `approver_name` | `approver_name` | 審核主管姓名 |

## 具體API標準

### 1. 審核統計API `/api/approval/stats`

**標準回應格式：**
```json
{
    "pending_count": 8,          // 待審核數量（前端統一使用）
    "monthly_approved": 0,       // 本月已審核數量
    "approval_rate": 0,          // 審核通過率
    "monthly_stats": {           // 月度統計
        "approved": 0,
        "rejected": 0,
        "total": 0
    },
    "recent_approvals": [...],   // 最近審核記錄
    "generated_at": "2025-06-06T13:46:00Z"
}
```

### 2. 審核列表API `/api/approval/leaves`

**標準回應格式：**
```json
{
    "success": true,
    "records": [                 // 記錄列表（前端統一使用）
        {
            "id": 46,
            "employee_id": 1,
            "employee_code": "E001",     // 員工代碼（前端統一使用）
            "employee_name": "黎麗玲",
            "department_name": "技術部",
            "leave_type": "official",
            "start_date": "2025-06-18",
            "end_date": "2025-06-19",
            "reason": "公務出差",
            "status": "pending",
            "substitute_name": "張三",   // 代理人姓名
            "approver_name": "李四",     // 審核主管姓名
            "created_at": "2025-06-06T13:36:47.926122"
        }
    ],
    "pagination": {
        "total": 8,
        "page": 1,
        "limit": 20,
        "total_pages": 1,
        "has_next": false,
        "has_prev": false
    }
}
```

### 3. 員工列表API `/api/employees`

**標準回應格式：**
```json
{
    "success": true,
    "employees": [               // 員工列表
        {
            "id": 1,
            "employee_code": "E001", // 員工代碼（統一使用）
            "name": "黎麗玲",
            "department_name": "技術部",
            "position": "軟體工程師",
            "email": "<EMAIL>"
        }
    ]
}
```

## 開發規範

### 1. 新API開發時
- 必須參考此文件定義欄位名稱
- 統一使用 `employee_code` 而非 `emp_id`
- 統一使用 `pending_count` 而非 `pending`
- 統一使用 `records` 而非 `leaves`

### 2. 前端開發時
- 必須按照此文件期待的欄位名稱編寫代碼
- 如發現API欄位不匹配，請先檢查此文件
- 優先修改API使其符合標準，而非修改前端適應API

### 3. API測試
建議測試指令：
```bash
# 測試審核統計API
curl -s http://localhost:7072/api/approval/stats | jq '.pending_count'

# 測試審核列表API
curl -s http://localhost:7072/api/approval/leaves | jq '.records | length'

# 測試員工列表API
curl -s http://localhost:7072/api/employees | jq '.employees | length'
```

## 修改歷史

### 2025-06-06
- 建立初始API標準規範
- 統一審核相關API欄位名稱
- 統一員工相關API欄位名稱
- 定義標準回應格式

## 注意事項

1. **向後相容性**：修改API時需考慮現有前端代碼
2. **漸進式遷移**：如需修改現有欄位，可提供過渡期同時支援新舊欄位
3. **文件更新**：API修改後必須同步更新此文件
4. **團隊溝通**：API標準修改需與團隊成員充分溝通

## 開發工具建議

### VSCode設定
可在VSCode中建立程式碼片段，自動生成符合標準的API回應格式：

```json
{
    "API Success Response": {
        "prefix": "api-success",
        "body": [
            "return jsonify({",
            "    \"success\": True,",
            "    \"${1:data_key}\": ${2:data_value},",
            "    \"message\": \"${3:操作成功}\",",
            "    \"timestamp\": datetime.now().isoformat()",
            "})"
        ]
    }
}
```

### API測試腳本
建議建立API測試腳本，定期檢查API回應格式是否符合標準。 