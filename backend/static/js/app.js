/**
 * 智慧考勤系統 - 主應用程式
 * 現代化 JavaScript ES6+ 實現
 */

class AttendanceApp {
    constructor() {
        this.currentUser = null;
        this.currentView = 'login';
        this.apiBase = '/api';
        this.notifications = [];
        
        this.init();
    }

    async init() {
        console.log('🚀 初始化智慧考勤系統...');
        
        // 初始化主題
        this.initTheme();
        
        // 檢查登入狀態
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            await this.showDashboard();
        } else {
            this.showLogin();
        }

        // 綁定全局事件
        this.bindGlobalEvents();
        
        // 綁定快捷鍵
        this.bindKeyboardShortcuts();
        
        // 啟動定時器
        this.startTimers();
    }

    bindGlobalEvents() {
        // ESC 鍵關閉模態框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // 點擊模態框外部關閉
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
    }

    startTimers() {
        // 每分鐘更新時間顯示
        setInterval(() => {
            this.updateTimeDisplay();
        }, 60000);

        // 每5分鐘同步數據
        setInterval(() => {
            if (this.currentUser) {
                this.syncData();
            }
        }, 300000);
    }

    updateTimeDisplay() {
        const timeElements = document.querySelectorAll('.current-time');
        const now = new Date();
        const timeString = now.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        timeElements.forEach(element => {
            element.textContent = timeString;
        });
    }

    async syncData() {
        try {
            if (this.currentView === 'dashboard') {
                await this.loadDashboardStats();
            }
        } catch (error) {
            console.warn('數據同步失敗:', error);
        }
    }

    // ============ 頁面管理 ============
    showLogin() {
        this.currentView = 'login';
        const app = document.getElementById('app');
        app.innerHTML = this.getLoginHTML();
        this.bindLoginEvents();
    }

    async showDashboard() {
        this.currentView = 'dashboard';
        const app = document.getElementById('app');
        app.innerHTML = this.getDashboardHTML();
        await this.loadDashboardStats();
        this.bindDashboardEvents();
        this.updateTimeDisplay();
    }

    // ============ 登入功能 ============
    getLoginHTML() {
        return `
            <div class="login-container">
                <div class="login-card">
                    <div class="login-header">
                        <div class="logo">
                            <div class="logo-icon">🏢</div>
                            <h1 class="logo-text gradient-text">智慧考勤系統</h1>
                        </div>
                        <p class="login-subtitle">歡迎回來，請登入您的帳號</p>
                    </div>
                    
                    <form class="login-form" id="loginForm">
                        <div class="form-group">
                            <label class="form-label" for="username">帳號</label>
                            <input type="text" id="username" class="form-input" placeholder="請輸入帳號" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="password">密碼</label>
                            <input type="password" id="password" class="form-input" placeholder="請輸入密碼" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkbox-mark"></span>
                                記住我
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg login-btn">
                            <span class="btn-text">登入</span>
                            <div class="spinner" style="display: none;"></div>
                        </button>
                    </form>
                    
                    <div class="login-footer">
                        <p class="demo-info">
                            <strong>示範帳號：</strong><br>
                            帳號：admin<br>
                            密碼：admin123
                        </p>
                    </div>
                </div>
                
                <div class="login-background">
                    <div class="bg-shape shape-1"></div>
                    <div class="bg-shape shape-2"></div>
                    <div class="bg-shape shape-3"></div>
                </div>
            </div>
        `;
    }

    bindLoginEvents() {
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        
        // 輸入框焦點效果
        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('focus', (e) => {
                e.target.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', (e) => {
                if (!e.target.value) {
                    e.target.parentElement.classList.remove('focused');
                }
            });
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        const btn = document.querySelector('.login-btn');
        const btnText = btn.querySelector('.btn-text');
        const spinner = btn.querySelector('.spinner');
        
        // 顯示載入狀態
        btn.disabled = true;
        btnText.style.display = 'none';
        spinner.style.display = 'block';
        
        try {
            const response = await fetch(`${this.apiBase}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                this.currentUser = data;
                
                if (rememberMe) {
                    localStorage.setItem('currentUser', JSON.stringify(data));
                }
                
                this.showNotification('登入成功！歡迎使用智慧考勤系統', 'success');
                
                setTimeout(() => {
                    this.showDashboard();
                }, 1000);
            } else {
                throw new Error(data.error || '登入失敗');
            }
        } catch (error) {
            this.showNotification(error.message || '登入失敗，請檢查網路連線', 'error');
        } finally {
            // 恢復按鈕狀態
            btn.disabled = false;
            btnText.style.display = 'block';
            spinner.style.display = 'none';
        }
    }

    // ============ 儀表板功能 ============
    getDashboardHTML() {
        return `
            <div class="dashboard">
                <!-- 頂部導航 -->
                <nav class="navbar">
                    <div class="navbar-brand">
                        <div class="logo-icon">🏢</div>
                        <span class="brand-text">智慧考勤系統</span>
                    </div>
                    
                    <div class="navbar-content">
                        <div class="time-display">
                            <span class="current-time">${new Date().toLocaleString('zh-TW')}</span>
                        </div>
                        
                        <div class="user-menu">
                            <button class="user-btn" onclick="app.toggleUserMenu()">
                                <div class="user-avatar">👤</div>
                                <span class="user-name">${this.currentUser.name}</span>
                                <span class="user-role">${this.currentUser.role}</span>
                            </button>
                            
                            <div class="user-menu-dropdown" id="userDropdown">
                                <a href="#" onclick="app.showProfile()">個人資料</a>
                                <a href="#" onclick="app.showSettings()">系統設定</a>
                                <a href="#" onclick="app.toggleTheme()">🌙 切換主題</a>
                                <div class="dropdown-divider"></div>
                                <a href="#" onclick="app.logout()">登出</a>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- 側邊欄 -->
                <aside class="sidebar">
                    <div class="sidebar-menu">
                        <div class="menu-item active" onclick="app.showDashboardView()">
                            <span class="menu-icon">📊</span>
                            <span class="menu-text">儀表板</span>
                        </div>
                        
                        <div class="menu-item" onclick="app.showAttendanceView()">
                            <span class="menu-icon">⏰</span>
                            <span class="menu-text">考勤打卡</span>
                        </div>
                        
                        <div class="menu-item" onclick="app.showEmployeeView()">
                            <span class="menu-icon">👥</span>
                            <span class="menu-text">員工管理</span>
                        </div>
                        
                        <div class="menu-item" onclick="app.showScheduleView()">
                            <span class="menu-icon">📅</span>
                            <span class="menu-text">排班管理</span>
                        </div>
                        
                        <div class="menu-item" onclick="app.showReportsView()">
                            <span class="menu-icon">📈</span>
                            <span class="menu-text">統計報表</span>
                        </div>
                        
                        <div class="menu-item" onclick="app.showLeaveView()">
                            <span class="menu-icon">🏖️</span>
                            <span class="menu-text">請假管理</span>
                        </div>
                    </div>
                </aside>
                
                <!-- 主要內容 -->
                <main class="main-content">
                    <div id="dashboardContent">
                        <!-- 統計卡片 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-card-header">
                                    <span class="stat-card-title">今日出勤</span>
                                    <div class="stat-card-icon">👤</div>
                                </div>
                                <div class="stat-card-value" id="todayAttendance">-</div>
                                <div class="stat-card-change positive">
                                    <span>↗</span> 較昨日 +5%
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-card-header">
                                    <span class="stat-card-title">遲到人數</span>
                                    <div class="stat-card-icon">⏰</div>
                                </div>
                                <div class="stat-card-value" id="lateCount">-</div>
                                <div class="stat-card-change negative">
                                    <span>↘</span> 較昨日 -2%
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-card-header">
                                    <span class="stat-card-title">請假申請</span>
                                    <div class="stat-card-icon">📝</div>
                                </div>
                                <div class="stat-card-value" id="pendingLeaves">-</div>
                                <div class="stat-card-change">
                                    <span>待審批</span>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-card-header">
                                    <span class="stat-card-title">在職員工</span>
                                    <div class="stat-card-icon">🏢</div>
                                </div>
                                <div class="stat-card-value" id="totalEmployees">-</div>
                                <div class="stat-card-change positive">
                                    <span>↗</span> 本月 +3
                                </div>
                            </div>
                        </div>
                        
                        <!-- 圖表和列表 -->
                        <div class="dashboard-grid">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>最近考勤記錄</h3>
                                    <button class="btn btn-outline btn-sm" onclick="app.showAttendanceView()">查看全部</button>
                                </div>
                                <div class="card-body">
                                    <div class="table-container">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>員工</th>
                                                    <th>時間</th>
                                                    <th>狀態</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recentAttendance">
                                                <tr>
                                                    <td colspan="3" class="text-center">載入中...</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>部門統計</h3>
                                </div>
                                <div class="card-body">
                                    <div id="departmentStats">載入中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        `;
    }

    bindDashboardEvents() {
        // 實現側邊欄菜單切換
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', () => {
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });
    }

    async loadDashboardStats() {
        try {
            const response = await fetch(`${this.apiBase}/reports/dashboard`);
            const data = await response.json();
            
            if (response.ok) {
                this.updateDashboardStats(data);
            } else {
                throw new Error(data.error || '載入統計資料失敗');
            }
        } catch (error) {
            console.error('載入儀表板數據失敗:', error);
            this.showNotification('載入數據失敗', 'error');
        }
    }

    updateDashboardStats(data) {
        // 更新統計卡片
        if (data.attendance) {
            document.getElementById('todayAttendance').textContent = data.attendance.total || 0;
            document.getElementById('lateCount').textContent = data.attendance.late || 0;
        }
        
        document.getElementById('pendingLeaves').textContent = data.pending_leaves || 0;
        
        // 更新最近考勤記錄
        if (data.recent_records) {
            this.updateRecentAttendance(data.recent_records);
        }
        
        // 更新部門統計
        if (data.departments) {
            this.updateDepartmentStats(data.departments);
        }
    }

    updateRecentAttendance(records) {
        const tbody = document.getElementById('recentAttendance');
        
        if (records.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="text-center">暫無記錄</td></tr>';
            return;
        }
        
        tbody.innerHTML = records.map(record => `
            <tr>
                <td>${record.name || record.employee_id}</td>
                <td>${new Date(record.check_in).toLocaleString('zh-TW')}</td>
                <td>
                    <span class="badge badge-${this.getStatusBadgeClass(record.status)}">
                        ${this.getStatusText(record.status)}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    updateDepartmentStats(departments) {
        const container = document.getElementById('departmentStats');
        
        if (departments.length === 0) {
            container.innerHTML = '<p class="text-center">暫無部門數據</p>';
            return;
        }
        
        container.innerHTML = departments.map(dept => `
            <div class="dept-stat-item">
                <div class="dept-name">${dept.name}</div>
                <div class="dept-count">${dept.count} 人</div>
            </div>
        `).join('');
    }

    getStatusBadgeClass(status) {
        const statusMap = {
            'normal': 'success',
            'late': 'warning',
            'early_leave': 'warning',
            'absent': 'error',
            'manual': 'info'
        };
        return statusMap[status] || 'info';
    }

    getStatusText(status) {
        const statusMap = {
            'normal': '正常',
            'late': '遲到',
            'early_leave': '早退',
            'absent': '缺勤',
            'manual': '手動'
        };
        return statusMap[status] || status;
    }

    // ============ 考勤打卡功能 ============
    showAttendanceView() {
        this.currentView = 'attendance';
        this.setActiveMenuItem('showAttendanceView');
        const content = document.getElementById('dashboardContent');
        content.innerHTML = `
            <div class="attendance-view">
                <div class="view-header">
                    <h2>考勤打卡</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="app.showClockInModal()">立即打卡</button>
                        <button class="btn btn-outline" onclick="app.showManualAttendanceModal()">手動補登</button>
                    </div>
                </div>
                
                <div class="clock-section">
                    <div class="clock-card">
                        <div class="clock-display">
                            <div class="current-time-large" id="clockDisplay">${new Date().toLocaleString('zh-TW')}</div>
                            <div class="clock-date">${new Date().toLocaleDateString('zh-TW', { weekday: 'long' })}</div>
                        </div>
                        
                        <div class="clock-actions">
                            <button class="btn btn-success btn-xl" onclick="app.clockIn()">
                                <span>上班打卡</span>
                                <small>Check In</small>
                            </button>
                            <button class="btn btn-warning btn-xl" onclick="app.clockOut()">
                                <span>下班打卡</span>
                                <small>Check Out</small>
                            </button>
                        </div>
                    </div>
                    
                    <div class="attendance-status">
                        <div class="status-item">
                            <span class="status-label">今日狀態</span>
                            <span class="badge badge-success" id="todayStatus">未打卡</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">上班時間</span>
                            <span id="checkInTime">--:--</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">下班時間</span>
                            <span id="checkOutTime">--:--</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">工作時長</span>
                            <span id="workHours">0 小時</span>
                        </div>
                    </div>
                </div>
                
                <div class="attendance-history">
                    <div class="card">
                        <div class="card-header">
                            <h3>最近打卡記錄</h3>
                            <button class="btn btn-outline btn-sm" onclick="app.refreshAttendanceHistory()">刷新</button>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>上班時間</th>
                                            <th>下班時間</th>
                                            <th>工作時長</th>
                                            <th>狀態</th>
                                        </tr>
                                    </thead>
                                    <tbody id="attendanceHistory">
                                        <tr><td colspan="5" class="text-center">載入中...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.loadTodayAttendance();
        this.loadAttendanceHistory();
        this.startClockUpdate();
    }

    startClockUpdate() {
        setInterval(() => {
            const clockDisplay = document.getElementById('clockDisplay');
            if (clockDisplay) {
                clockDisplay.textContent = new Date().toLocaleString('zh-TW');
            }
        }, 1000);
    }

    async clockIn() {
        await this.performClockAction('in');
    }

    async clockOut() {
        await this.performClockAction('out');
    }

    async performClockAction(type) {
        try {
            const endpoint = type === 'in' ? '/attendance/clock-in' : '/attendance/clock-out';
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_id: this.currentUser.id || this.currentUser.user_id,
                    note: `${type === 'in' ? '上班' : '下班'}打卡`
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.showNotification(`${type === 'in' ? '上班' : '下班'}打卡成功！`, 'success');
                this.loadTodayAttendance();
                this.loadAttendanceHistory();
            } else {
                throw new Error(data.error || '打卡失敗');
            }
        } catch (error) {
            this.showNotification(error.message, 'error');
        }
    }

    async loadTodayAttendance() {
        // 實現載入今日考勤狀態
        try {
            if (!this.currentUser?.id) return;
            
            const response = await fetch(`${this.apiBase}/attendance/today/${this.currentUser.id}`);
            const data = await response.json();
            
            if (response.ok) {
                this.updateAttendanceStatus(data);
            } else {
                console.error('載入今日考勤失敗:', data.error);
            }
        } catch (error) {
            console.error('載入今日考勤失敗:', error);
        }
    }

    async loadAttendanceHistory() {
        // 實現載入考勤歷史
        try {
            if (!this.currentUser?.id) return;
            
            const response = await fetch(`${this.apiBase}/attendance/history/${this.currentUser.id}?days=30`);
            const data = await response.json();
            
            if (response.ok) {
                this.renderAttendanceHistory(data.records);
            } else {
                console.error('載入考勤歷史失敗:', data.error);
                this.showNotification('載入考勤歷史失敗', 'error');
            }
        } catch (error) {
            console.error('載入考勤歷史失敗:', error);
            this.showNotification('載入考勤歷史失敗', 'error');
        }
    }

    updateAttendanceStatus(data) {
        // 更新考勤狀態顯示
        const statusElement = document.querySelector('.attendance-status');
        if (statusElement) {
            const hasCheckedIn = data.has_checked_in;
            const hasCheckedOut = data.has_checked_out;
            
            let statusText = '尚未打卡';
            let statusClass = 'status-pending';
            
            if (hasCheckedIn && hasCheckedOut) {
                statusText = '已完成打卡';
                statusClass = 'status-completed';
            } else if (hasCheckedIn) {
                statusText = '已上班打卡';
                statusClass = 'status-working';
            }
            
            statusElement.textContent = statusText;
            statusElement.className = `attendance-status ${statusClass}`;
        }
    }

    renderAttendanceHistory(records) {
        const tbody = document.getElementById('attendanceHistory');
        if (!tbody) return;
        
        if (!records || records.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">暫無考勤記錄</td></tr>';
            return;
        }
        
        // 按日期分組考勤記錄
        const groupedRecords = this.groupRecordsByDate(records);
        
        tbody.innerHTML = records.map(record => {
            const date = record.check_in ? new Date(record.check_in).toLocaleDateString('zh-TW') : 
                        record.check_out ? new Date(record.check_out).toLocaleDateString('zh-TW') : '--';
            const inTime = record.check_in ? new Date(record.check_in).toLocaleTimeString('zh-TW', {hour: '2-digit', minute: '2-digit'}) : '--';
            const outTime = record.check_out ? new Date(record.check_out).toLocaleTimeString('zh-TW', {hour: '2-digit', minute: '2-digit'}) : '--';
            
            let workHours = '--';
            let status = '異常';
            let statusClass = 'badge-error';
            
            if (record.check_in && record.check_out) {
                const diff = new Date(record.check_out) - new Date(record.check_in);
                workHours = Math.round(diff / (1000 * 60 * 60) * 10) / 10 + ' 小時';
                status = '正常';
                statusClass = 'badge-success';
            } else if (record.check_in) {
                status = '未下班';
                statusClass = 'badge-warning';
            }
            
            return `
                <tr>
                    <td>${date}</td>
                    <td>${inTime}</td>
                    <td>${outTime}</td>
                    <td>${workHours}</td>
                    <td><span class="badge ${statusClass}">${status}</span></td>
                </tr>
            `;
        }).join('');
    }

    groupRecordsByDate(records) {
        return records.reduce((groups, record) => {
            const date = new Date(record.timestamp).toLocaleDateString('zh-TW');
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(record);
            return groups;
        }, {});
    }

    // ============ 通用功能 ============
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 顯示動畫
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // 自動移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }

    showModal(title, content, actions = []) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="modal-close" onclick="app.closeModal()">×</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${actions.map(action => `
                        <button class="btn ${action.class || 'btn-outline'}" onclick="${action.onclick}">${action.text}</button>
                    `).join('')}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
    }

    closeModal() {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => {
            modal.classList.remove('active');
            setTimeout(() => {
                modal.remove();
            }, 300);
        });
    }

    toggleUserMenu() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');
    }

    logout() {
        localStorage.removeItem('currentUser');
        this.currentUser = null;
        this.showNotification('已安全登出', 'info');
        setTimeout(() => {
            this.showLogin();
        }, 1000);
    }

    // ============ 佔位功能（待實現） ============
    showDashboardView() {
        this.showDashboard();
    }

    async showEmployeeView() {
        if (!this.employeeManager) {
            this.employeeManager = new EmployeeManagement(this);
            await this.employeeManager.init();
        }
        
        const content = document.getElementById('dashboardContent');
        content.innerHTML = this.employeeManager.getEmployeeManagementHTML();
        await this.employeeManager.loadEmployees();
    }

    async showScheduleView() {
        this.currentView = 'schedule';
        this.setActiveMenuItem('showScheduleView');
        const content = `
            <div class="schedule-management">
                <div class="schedule-actions" style="margin-bottom: 2rem;">
                    <button class="btn btn-primary" onclick="app.loadMySchedule()">我的排班</button>
                    <button class="btn btn-secondary" onclick="app.showScheduleCalendar()">排班日曆</button>
                </div>
                <div id="scheduleContent">
                    <p>選擇上方功能查看排班資訊</p>
                </div>
            </div>
        `;
        this.showModal('排班管理', content, [
            { text: '關閉', class: 'btn-outline', onclick: 'app.closeModal()' }
        ]);
    }

    async loadMySchedule() {
        try {
            const response = await fetch(`${this.apiBase}/schedules/employee/${this.currentUser.id}`);
            const data = await response.json();

            if (response.ok) {
                this.renderSchedule(data.schedules);
            } else {
                this.showNotification('載入排班失敗', 'error');
            }
        } catch (error) {
            console.error('載入排班錯誤:', error);
            this.showNotification('載入排班失敗', 'error');
        }
    }

    renderSchedule(schedules) {
        const container = document.getElementById('scheduleContent');
        if (!container) return;

        if (!schedules || schedules.length === 0) {
            container.innerHTML = '<p class="text-center">暫無排班記錄</p>';
            return;
        }

        const html = `
            <table class="table">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>開始時間</th>
                        <th>結束時間</th>
                        <th>班次類型</th>
                        <th>狀態</th>
                    </tr>
                </thead>
                <tbody>
                    ${schedules.map(schedule => `
                        <tr>
                            <td>${schedule.date}</td>
                            <td>${schedule.start_time}</td>
                            <td>${schedule.end_time}</td>
                            <td>${schedule.shift_type || '一般班'}</td>
                            <td><span class="badge badge-success">已排班</span></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = html;
    }

    showScheduleCalendar() {
        const container = document.getElementById('scheduleContent');
        if (!container) return;

        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();
        
        // 簡化的日曆視圖
        const html = `
            <div class="schedule-calendar">
                <h4>${currentYear}年 ${currentMonth + 1}月 排班日曆</h4>
                <p class="text-center">完整的日曆排班功能將在下一版本推出</p>
                <div class="calendar-placeholder" style="
                    background: #f8f9fa; 
                    border: 2px dashed #dee2e6; 
                    padding: 3rem; 
                    text-align: center; 
                    border-radius: 12px;
                    margin: 2rem 0;
                ">
                    📅 互動式排班日曆<br>
                    <small>可拖拽編輯排班、批次操作等功能開發中</small>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    async showReportsView() {
        this.currentView = 'reports';
        this.setActiveMenuItem('showReportsView');
        try {
            const response = await fetch(`${this.apiBase}/reports/dashboard`);
            const data = await response.json();
            
            if (response.ok) {
                this.renderDashboardStats(data);
                this.showNotification('統計報表已更新', 'success');
            } else {
                this.showNotification('統計報表載入失敗', 'error');
            }
        } catch (error) {
            console.error('統計報表錯誤:', error);
            this.showNotification('統計報表載入失敗', 'error');
        }
    }

    showLeaveView() {
        this.currentView = 'leave';
        this.setActiveMenuItem('showLeaveView');
        const content = `
            <div class="leave-management">
                <div class="leave-actions" style="margin-bottom: 2rem;">
                    <button class="btn btn-primary" onclick="app.showLeaveApplicationForm()">申請請假</button>
                    <button class="btn btn-secondary" onclick="app.loadLeaveHistory()">請假記錄</button>
                </div>
                <div id="leaveContent">
                    <p>選擇上方功能開始使用請假管理系統</p>
                </div>
            </div>
        `;
        this.showModal('請假管理', content, [
            { text: '關閉', class: 'btn-outline', onclick: 'app.closeModal()' }
        ]);
    }

    showLeaveApplicationForm() {
        const content = `
            <form id="leaveForm" class="leave-form">
                <div class="form-group">
                    <label>請假類型</label>
                    <select id="leaveType" class="form-control" required>
                        <option value="">請選擇</option>
                        <option value="annual">年假</option>
                        <option value="sick">病假</option>
                        <option value="personal">事假</option>
                        <option value="maternity">產假</option>
                        <option value="paternity">陪產假</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>開始日期</label>
                    <input type="date" id="startDate" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>結束日期</label>
                    <input type="date" id="endDate" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>請假原因</label>
                    <textarea id="leaveReason" class="form-control" rows="3" placeholder="請詳細說明請假原因..." required></textarea>
                </div>
            </form>
        `;
        
        this.showModal('申請請假', content, [
            { text: '取消', class: 'btn-outline', onclick: 'app.closeModal()' },
            { text: '提交申請', class: 'btn-primary', onclick: 'app.submitLeaveApplication()' }
        ]);
    }

    async submitLeaveApplication() {
        const form = document.getElementById('leaveForm');
        const leaveType = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reason = document.getElementById('leaveReason').value;

        if (!leaveType || !startDate || !endDate || !reason) {
            this.showNotification('請填寫所有必要欄位', 'error');
            return;
        }

        try {
            const response = await fetch(`${this.apiBase}/leaves`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    employee_id: this.currentUser.id,
                    leave_type: leaveType,
                    start_date: startDate,
                    end_date: endDate,
                    reason: reason
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.closeModal();
                this.showNotification('請假申請提交成功', 'success');
            } else {
                this.showNotification(data.error || '申請失敗', 'error');
            }
        } catch (error) {
            console.error('請假申請錯誤:', error);
            this.showNotification('申請失敗', 'error');
        }
    }

    async loadLeaveHistory() {
        try {
            const response = await fetch(`${this.apiBase}/leaves?employee_id=${this.currentUser.id}`);
            const leaves = await response.json();

            if (response.ok) {
                this.renderLeaveHistory(leaves);
            } else {
                this.showNotification('載入請假記錄失敗', 'error');
            }
        } catch (error) {
            console.error('載入請假記錄錯誤:', error);
            this.showNotification('載入請假記錄失敗', 'error');
        }
    }

    renderLeaveHistory(leaves) {
        const container = document.getElementById('leaveContent');
        if (!container) return;

        if (!leaves || leaves.length === 0) {
            container.innerHTML = '<p class="text-center">暫無請假記錄</p>';
            return;
        }

        const html = `
            <table class="table">
                <thead>
                    <tr>
                        <th>類型</th>
                        <th>開始日期</th>
                        <th>結束日期</th>
                        <th>狀態</th>
                        <th>申請時間</th>
                    </tr>
                </thead>
                <tbody>
                    ${leaves.map(leave => {
                        const statusMap = {
                            'pending': { class: 'badge-warning', text: '待審核' },
                            'approved': { class: 'badge-success', text: '已核准' },
                            'rejected': { class: 'badge-error', text: '已拒絕' }
                        };
                        const status = statusMap[leave.status] || { class: 'badge-secondary', text: '未知' };
                        
                        const typeMap = {
                            'annual': '年假',
                            'sick': '病假',
                            'personal': '事假',
                            'maternity': '產假',
                            'paternity': '陪產假'
                        };
                        
                        return `
                            <tr>
                                <td>${typeMap[leave.leave_type] || leave.leave_type}</td>
                                <td>${leave.start_date}</td>
                                <td>${leave.end_date}</td>
                                <td><span class="badge ${status.class}">${status.text}</span></td>
                                <td>${new Date(leave.created_at).toLocaleDateString('zh-TW')}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = html;
    }

    showProfile() {
        if (!this.currentUser) {
            this.showNotification('用戶未登入', 'error');
            return;
        }
        
        const content = `
            <div class="profile-form">
                <div class="form-group">
                    <label>姓名</label>
                    <input type="text" value="${this.currentUser.name || ''}" readonly class="form-control">
                </div>
                <div class="form-group">
                    <label>部門</label>
                    <input type="text" value="${this.currentUser.department || ''}" readonly class="form-control">
                </div>
                <div class="form-group">
                    <label>職位</label>
                    <input type="text" value="${this.currentUser.position || ''}" readonly class="form-control">
                </div>
            </div>
        `;
        
        this.showModal('個人資料', content, [
            { text: '關閉', class: 'btn-outline', onclick: 'app.closeModal()' }
        ]);
    }

    showSettings() {
        const content = `
            <div class="settings-info">
                <h4>⚙️ 系統設定</h4>
                <div class="setting-item">
                    <label>主題設定</label>
                    <select class="form-control">
                        <option>明亮模式</option>
                        <option>暗黑模式</option>
                        <option>自動設定</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>語言設定</label>
                    <select class="form-control">
                        <option>繁體中文</option>
                        <option>English</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>通知設定</label>
                    <input type="checkbox" checked> 啟用打卡提醒
                </div>
            </div>
        `;
        this.showModal('系統設定', content, [
            { text: '取消', class: 'btn-outline', onclick: 'app.closeModal()' },
            { text: '儲存', class: 'btn-primary', onclick: 'app.closeModal(); app.showNotification("設定已儲存", "success");' }
        ]);
    }

    showClockInModal() {
        this.clockIn();
    }

    showManualAttendanceModal() {
        const today = new Date().toISOString().split('T')[0];
        const content = `
            <div class="manual-attendance-form">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="manualDate" value="${today}" class="form-control">
                </div>
                <div class="form-group">
                    <label>上班時間</label>
                    <input type="time" id="manualCheckIn" class="form-control">
                </div>
                <div class="form-group">
                    <label>下班時間</label>
                    <input type="time" id="manualCheckOut" class="form-control">
                </div>
                <div class="form-group">
                    <label>備註</label>
                    <textarea id="manualNote" class="form-control" rows="3" placeholder="請說明補登原因..."></textarea>
                </div>
            </div>
        `;
        
        this.showModal('手動補登考勤', content, [
            { text: '取消', class: 'btn-outline', onclick: 'app.closeModal()' },
            { text: '提交', class: 'btn-primary', onclick: 'app.submitManualAttendance()' }
        ]);
    }

    refreshAttendanceHistory() {
        this.loadAttendanceHistory();
        this.showNotification('數據已刷新', 'success');
    }

    renderDashboardStats(data) {
        // 更新儀表板統計數據
        const elements = {
            totalEmployees: document.querySelector('.stat-employees .stat-number'),
            totalDepartments: document.querySelector('.stat-departments .stat-number'),
            checkedInToday: document.querySelector('.stat-attendance .stat-number'),
            attendanceRate: document.querySelector('.stat-rate .stat-number')
        };

        Object.entries(elements).forEach(([key, element]) => {
            if (element) {
                const value = data[key === 'checkedInToday' ? 'checked_in_today' : 
                              key === 'totalEmployees' ? 'total_employees' :
                              key === 'totalDepartments' ? 'total_departments' :
                              'attendance_rate'];
                element.textContent = key === 'attendanceRate' ? `${value}%` : value;
            }
        });
    }

    async submitManualAttendance() {
        const date = document.getElementById('manualDate')?.value;
        const checkIn = document.getElementById('manualCheckIn')?.value;
        const checkOut = document.getElementById('manualCheckOut')?.value;
        const note = document.getElementById('manualNote')?.value;

        if (!date || !checkIn) {
            this.showNotification('請填寫日期和上班時間', 'error');
            return;
        }

        try {
            // 提交上班打卡
            if (checkIn) {
                const checkInResponse = await fetch(`${this.apiBase}/attendance`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        employee_id: this.currentUser.id,
                        type: 'in',
                        timestamp: `${date} ${checkIn}:00`,
                        note: note
                    })
                });
            }

            // 提交下班打卡
            if (checkOut) {
                const checkOutResponse = await fetch(`${this.apiBase}/attendance`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        employee_id: this.currentUser.id,
                        type: 'out',
                        timestamp: `${date} ${checkOut}:00`,
                        note: note
                    })
                });
            }

            this.closeModal();
            this.showNotification('手動補登成功', 'success');
            this.loadAttendanceHistory();
        } catch (error) {
            console.error('手動補登錯誤:', error);
            this.showNotification('手動補登失敗', 'error');
        }
    }

    // ============ 缺失的選單和視圖切換功能 ============
    toggleUserMenu() {
        const userMenu = document.getElementById('userDropdown');
        if (userMenu) {
            userMenu.style.display = userMenu.style.display === 'block' ? 'none' : 'block';
        }
        
        // 點擊外部關閉選單
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                userMenu.style.display = 'none';
            }
        }, { once: true });
    }

    showDashboardView() {
        this.currentView = 'dashboard';
        this.setActiveMenuItem('showDashboardView');
        this.showDashboard();
    }

    showEmployeeView() {
        this.currentView = 'employees';
        this.setActiveMenuItem('showEmployeeView');
        const content = document.getElementById('mainContent');
        if (content) {
            content.innerHTML = this.employeeManager.getEmployeeManagementHTML();
            this.employeeManager.loadEmployees();
        }
    }

    showClockInModal() {
        this.clockIn();
    }

    logout() {
        localStorage.removeItem('currentUser');
        this.currentUser = null;
        this.currentView = 'login';
        this.showLogin();
        this.showNotification('已安全登出', 'success');
    }

    // ============ 缺失的同步和更新功能 ============
    async syncData() {
        try {
            // 同步各種數據
            await Promise.all([
                this.loadDashboardStats(),
                this.loadTodayAttendance(),
                this.loadAttendanceHistory()
            ]);
            this.showNotification('數據同步完成', 'success');
        } catch (error) {
            console.error('數據同步錯誤:', error);
            this.showNotification('數據同步失敗', 'error');
        }
    }

    // ============ 視圖管理功能 ============
    hideAllViews() {
        const views = ['login-container', 'dashboard-container'];
        views.forEach(viewId => {
            const view = document.getElementById(viewId);
            if (view) {
                view.style.display = 'none';
            }
        });
    }

    showView(viewId) {
        this.hideAllViews();
        const view = document.getElementById(viewId);
        if (view) {
            view.style.display = 'block';
        }
    }

    // ============ 選單項目高亮管理 ============
    setActiveMenuItem(activeItem) {
        // 移除所有選單項目的active class
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => item.classList.remove('active'));
        
        // 添加active class到當前項目
        const currentItem = document.querySelector(`.menu-item[onclick*="${activeItem}"]`);
        if (currentItem) {
            currentItem.classList.add('active');
        }
    }

    // ============ 快捷鍵支援 ============
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.showDashboardView();
                        break;
                    case '2':
                        e.preventDefault();
                        this.showAttendanceView();
                        break;
                    case '3':
                        e.preventDefault();
                        this.showEmployeeView();
                        break;
                    case 'k':
                        e.preventDefault();
                        this.clockIn();
                        break;
                }
            }
        });
    }

    // ============ 響應式選單切換 ============
    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('mobile-open');
        }
    }

    // ============ 主題切換功能 ============
    toggleTheme() {
        const body = document.body;
        const isDark = body.classList.contains('dark-theme');
        
        if (isDark) {
            body.classList.remove('dark-theme');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            localStorage.setItem('theme', 'dark');
        }
        
        this.showNotification(`已切換到${isDark ? '淺色' : '深色'}主題`, 'success');
    }

    // ============ 初始化主題 ============
    initTheme() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }
}

// 初始化應用程式
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AttendanceApp();
});

// 全局錯誤處理
window.addEventListener('error', (e) => {
    console.error('全局錯誤:', e.error);
    if (app) {
        app.showNotification('系統發生錯誤，請刷新頁面', 'error');
    }
});

// 網路狀態檢測
window.addEventListener('online', () => {
    if (app) {
        app.showNotification('網路連線已恢復', 'success');
    }
});

window.addEventListener('offline', () => {
    if (app) {
        app.showNotification('網路連線中斷', 'warning');
    }
});