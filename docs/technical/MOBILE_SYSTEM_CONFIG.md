# Han AttendanceOS 手機版系統配置指南

## 📋 系統概述

Han AttendanceOS 手機版是一個基於 Next.js + Flask 的考勤管理系統，專為移動設備優化。

## 🗄️ 資料庫結構配置

### ⚠️ 重要：正確的欄位名稱

```sql
-- employees 表結構（重要欄位）
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    employee_id TEXT UNIQUE NOT NULL,
    department_id INTEGER NOT NULL,
    position TEXT NOT NULL,           -- ✅ 職位存在這裡，不是roles表
    email TEXT UNIQUE,
    phone TEXT,
    password TEXT,
    photo_url TEXT,                   -- ✅ 頭像URL欄位名稱
    role_id INTEGER,                  -- 參考permissions表，不是roles表
    -- 其他欄位...
);
```

### ❌ 常見錯誤避免

1. **錯誤**：使用 `avatar_url` 欄位
   **正確**：使用 `photo_url` 欄位

2. **錯誤**：查詢 `roles` 表獲取職位
   **正確**：直接使用 `employees.position` 欄位

3. **錯誤**：假設 `roles` 表存在
   **正確**：職位資訊直接存在 `employees` 表中

## 🔧 API 配置

### 服務器端口配置

```bash
# Flask 後端服務器
http://localhost:7072

# Next.js 前端服務器  
http://localhost:7075
```

### Profile API 正確實現

```python
# ✅ 正確的 SQL 查詢
cursor.execute("""
    SELECT e.employee_id, e.name, e.email, e.phone, 
           d.name as department, e.position,
           e.photo_url
    FROM employees e
    LEFT JOIN departments d ON e.department_id = d.id
    WHERE e.employee_id = ?
""", (employee_id,))

# ✅ 正確的回應格式
profile_data = {
    'employee_id': employee['employee_id'],
    'name': employee['name'],
    'email': employee['email'] or '',
    'phone': employee['phone'] or '',
    'department': employee['department'] or '',
    'position': employee['position'] or '',
    'avatar': employee['photo_url'] or ''
}
```

### API 測試驗證

```bash
# 測試 Profile API
curl -X GET "http://localhost:7072/api/profile/E001" -H "Content-Type: application/json"

# 預期成功回應
{
  "profile": {
    "avatar": "",
    "department": "技術部",
    "email": "<EMAIL>",
    "employee_id": "E001",
    "name": "黎麗玲",
    "phone": "00-63075176",
    "position": "專員"
  },
  "success": true
}
```

## 📱 前端配置

### 手機版頁面優化設定

```typescript
// ✅ 主頁面容器設定（已優化留白）
<div className="px-3 pt-2 pb-16 space-y-3">

// ✅ 打卡區域設定
<div className="bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 rounded-2xl p-5 shadow-xl">

// ✅ 功能按鈕設定
<div className="bg-white rounded-2xl p-4 border border-gray-100 shadow-xl">
```

### API 基礎URL 配置

```typescript
// ✅ 正確的 API URL 配置
const getApiBaseUrl = () => {
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:7072'
        } else {
            return `http://${hostname}:7072`
        }
    }
    return 'http://localhost:7072'
}
```

### 錯誤處理和預設值

```typescript
// ✅ 正確的錯誤處理
try {
    const response = await fetch(`${apiBaseUrl}/api/profile/${user.employee_id}`)
    const data = await response.json()

    if (data.success && data.profile) {
        setProfile(data.profile)
    } else {
        // 設定預設值
        setProfile({
            employee_id: user.employee_id,
            name: user.name || '',
            email: '',
            phone: '',
            department: user.department_name || '',
            position: user.position || '',
            avatar: ''
        })
    }
} catch (error) {
    // 同樣設定預設值
    setProfile({ /* 預設值 */ })
}
```

## 🚀 啟動順序

### 1. 啟動 Flask 服務器

```bash
cd /Users/<USER>/2024newdev/attend_next
python app.py
```

### 2. 啟動 Next.js 服務器

```bash
cd /Users/<USER>/2024newdev/attend_next/attendance-nextjs
npm run dev
```

### 3. 驗證服務器狀態

```bash
# 檢查 Flask 服務器
lsof -ti:7072

# 檢查 Next.js 服務器
lsof -ti:7075
```

## 🔍 故障排除

### 常見問題和解決方案

1. **API 返回 404**
   - 檢查 Flask 服務器是否運行
   - 確認 Blueprint 已正確註冊

2. **資料庫錯誤**
   - 確認使用正確的欄位名稱（photo_url 不是 avatar_url）
   - 不要查詢不存在的 roles 表

3. **前端載入失敗**
   - 檢查 API 基礎URL 配置
   - 確認錯誤處理和預設值設定

4. **端口被佔用**
   ```bash
   # 終止佔用端口的程序
   lsof -ti:7075 | xargs kill -9
   lsof -ti:7072 | xargs kill -9
   ```

## 📝 重要提醒

### ⚠️ 絕對不要做的事情

1. ❌ 不要使用 `avatar_url` 欄位名稱
2. ❌ 不要查詢 `roles` 表
3. ❌ 不要假設資料庫結構與預期一致
4. ❌ 不要忘記設定錯誤處理和預設值

### ✅ 必須要做的事情

1. ✅ 使用 `photo_url` 欄位名稱
2. ✅ 直接使用 `employees.position` 獲取職位
3. ✅ 先測試 API 再修改前端
4. ✅ 添加完整的錯誤處理機制

## 📊 系統狀態檢查清單

- [ ] Flask 服務器運行在 7072 端口
- [ ] Next.js 服務器運行在 7075 端口
- [ ] Profile API 測試成功
- [ ] 手機版主頁面正常載入
- [ ] 設定頁面正常載入個人資料
- [ ] 所有 API 使用正確的欄位名稱

---

**最後更新：** 2025-06-13  
**版本：** v1.0  
**維護者：** Han AttendanceOS 開發團隊 