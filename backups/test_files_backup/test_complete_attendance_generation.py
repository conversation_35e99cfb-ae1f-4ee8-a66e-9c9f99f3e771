#!/usr/bin/env python3
"""
完整考勤記錄生成功能測試腳本

測試功能：
1. 完整考勤記錄生成 API
2. 每日考勤完整性檢查 API
3. 驗證未打卡員工記錄創建
4. 驗證請假記錄整合
"""

import requests
import json
from datetime import datetime, timedelta
import sqlite3
from database import create_connection

# API 基礎 URL
BASE_URL = "http://localhost:7072"

def test_daily_completion_check():
    """
    測試每日考勤完整性檢查功能
    """
    print("=" * 60)
    print("測試 1: 每日考勤完整性檢查")
    print("=" * 60)
    
    # 測試今天的完整性檢查
    today = datetime.now().strftime('%Y-%m-%d')
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/attendance/management/daily-completion-check",
            params={
                'target_date': today
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 完整性檢查成功")
            print(f"📊 檢查結果摘要:")
            print(f"   - 總在職員工: {data['completion_summary']['total_active_employees']}")
            print(f"   - 有考勤記錄: {data['completion_summary']['employees_with_attendance']}")
            print(f"   - 有打卡記錄: {data['completion_summary']['employees_with_punches']}")
            print(f"   - 有請假記錄: {data['completion_summary']['employees_with_leaves']}")
            print(f"   - 缺少考勤記錄: {data['completion_summary']['missing_attendance_count']}")
            
            print(f"\n📈 覆蓋率:")
            print(f"   - 考勤記錄覆蓋率: {data['coverage_rates']['attendance_coverage']}%")
            print(f"   - 打卡記錄覆蓋率: {data['coverage_rates']['punch_coverage']}%")
            print(f"   - 請假記錄覆蓋率: {data['coverage_rates']['leave_coverage']}%")
            
            print(f"\n💡 系統建議:")
            for recommendation in data['recommendations']:
                print(f"   - {recommendation}")
            
            # 顯示缺少考勤記錄的員工
            if data['missing_data_analysis']['missing_attendance_employees']:
                print(f"\n⚠️  缺少考勤記錄的員工:")
                for emp in data['missing_data_analysis']['missing_attendance_employees']:
                    print(f"   - {emp['employee_name']} ({emp['employee_code']}) - {emp['department_name']}")
                    print(f"     有打卡: {'是' if emp['has_punch'] else '否'}, 有請假: {'是' if emp['has_leave'] else '否'}")
            
            return data
        else:
            print(f"❌ 完整性檢查失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 完整性檢查異常: {str(e)}")
        return None


def test_complete_attendance_generation():
    """
    測試完整考勤記錄生成功能
    """
    print("\n" + "=" * 60)
    print("測試 2: 完整考勤記錄生成")
    print("=" * 60)
    
    # 測試今天的考勤記錄生成
    today = datetime.now().strftime('%Y-%m-%d')
    
    try:
        # 準備請求資料
        request_data = {
            "target_date": today,
            "department_ids": [],  # 空陣列表示處理所有部門
            "force_regenerate": False  # 不強制重新生成
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/generate-complete",
            json=request_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 完整考勤記錄生成成功")
            print(f"📊 處理摘要:")
            print(f"   - 總在職員工: {data['processing_summary']['total_active_employees']}")
            print(f"   - 有打卡記錄的員工: {data['processing_summary']['employees_with_punches']}")
            print(f"   - 有請假記錄的員工: {data['processing_summary']['employees_with_leaves']}")
            print(f"   - 未打卡員工: {data['processing_summary']['no_punch_employees']}")
            print(f"   - 請假整合員工: {data['processing_summary']['leave_integrated_employees']}")
            
            print(f"\n📈 生成結果:")
            print(f"   - 新增記錄: {data['generation_results']['generated_count']}")
            print(f"   - 更新記錄: {data['generation_results']['updated_count']}")
            print(f"   - 總處理記錄: {data['generation_results']['total_processed']}")
            print(f"   - 重新計算記錄: {data['generation_results']['recalculated_count']}")
            
            print(f"\n📋 資料來源:")
            print(f"   - 打卡記錄總數: {data['data_sources']['total_punch_records']}")
            print(f"   - 請假記錄總數: {data['data_sources']['total_leave_records']}")
            print(f"   - 處理部門: {data['data_sources']['departments_processed']}")
            
            return data
        else:
            print(f"❌ 完整考勤記錄生成失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 完整考勤記錄生成異常: {str(e)}")
        return None


def test_force_regeneration():
    """
    測試強制重新生成功能
    """
    print("\n" + "=" * 60)
    print("測試 3: 強制重新生成考勤記錄")
    print("=" * 60)
    
    # 測試昨天的強制重新生成
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    try:
        # 準備請求資料
        request_data = {
            "target_date": yesterday,
            "department_ids": [],  # 空陣列表示處理所有部門
            "force_regenerate": True  # 強制重新生成
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/generate-complete",
            json=request_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 強制重新生成成功 - 日期: {yesterday}")
            print(f"📊 處理結果:")
            print(f"   - 總處理員工: {data['processing_summary']['total_active_employees']}")
            print(f"   - 新增記錄: {data['generation_results']['generated_count']}")
            print(f"   - 更新記錄: {data['generation_results']['updated_count']}")
            
            return data
        else:
            print(f"❌ 強制重新生成失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 強制重新生成異常: {str(e)}")
        return None


def verify_database_records():
    """
    驗證資料庫中的考勤記錄
    """
    print("\n" + "=" * 60)
    print("測試 4: 驗證資料庫記錄")
    print("=" * 60)
    
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 查詢今天的考勤記錄
        cursor.execute("""
            SELECT ar.id, ar.employee_id, ar.status, ar.clock_in_time, ar.clock_out_time,
                   ar.leave_hours, ar.note, e.name as employee_name, e.employee_id as employee_code
            FROM attendance_records ar
            LEFT JOIN employees e ON ar.employee_id = e.id
            WHERE DATE(COALESCE(ar.clock_in_time, ar.clock_out_time, ar.created_at)) = ?
            ORDER BY ar.employee_id
        """, (today,))
        
        records = cursor.fetchall()
        
        print(f"📋 今天的考勤記錄 ({len(records)} 筆):")
        
        status_counts = {}
        for record in records:
            status = record['status']
            status_counts[status] = status_counts.get(status, 0) + 1
            
            print(f"   - {record['employee_name']} ({record['employee_code']})")
            print(f"     狀態: {status}")
            if record['clock_in_time']:
                print(f"     上班: {record['clock_in_time']}")
            if record['clock_out_time']:
                print(f"     下班: {record['clock_out_time']}")
            if record['leave_hours'] and record['leave_hours'] > 0:
                print(f"     請假時數: {record['leave_hours']} 小時")
            if record['note']:
                print(f"     備註: {record['note']}")
            print()
        
        print(f"📊 狀態統計:")
        for status, count in status_counts.items():
            print(f"   - {status}: {count} 人")
        
        # 查詢沒有考勤記錄的在職員工
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id
            FROM employees e
            WHERE (e.status IS NULL OR e.status = 'active')
            AND e.id NOT IN (
                SELECT DISTINCT ar.employee_id 
                FROM attendance_records ar 
                WHERE DATE(COALESCE(ar.clock_in_time, ar.clock_out_time, ar.created_at)) = ?
            )
        """, (today,))
        
        missing_employees = cursor.fetchall()
        
        if missing_employees:
            print(f"\n⚠️  仍缺少考勤記錄的員工 ({len(missing_employees)} 人):")
            for emp in missing_employees:
                print(f"   - {emp['name']} ({emp['employee_id']})")
        else:
            print(f"\n✅ 所有在職員工都有考勤記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證資料庫記錄異常: {str(e)}")


def create_test_data():
    """
    創建測試資料（打卡記錄和請假記錄）
    """
    print("\n" + "=" * 60)
    print("測試 5: 創建測試資料")
    print("=" * 60)
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 檢查資料庫結構
        conn = create_connection()
        cursor = conn.cursor()
        
        # 注意：punch_records表已刪除，不需要檢查和創建
        print("✅ 資料庫結構檢查完成（punch_records表已刪除）")
        
        # 檢查是否有 leave_requests 表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='leave_requests'
        """)
        
        if not cursor.fetchone():
            # 創建 leave_requests 表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS leave_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    leave_type INTEGER,
                    start_time DATETIME NOT NULL,
                    end_time DATETIME NOT NULL,
                    hours REAL,
                    status TEXT DEFAULT 'pending',
                    reason TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            """)
            print("✅ 創建 leave_requests 表")
        
        # 檢查是否有 leave_types 表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='leave_types'
        """)
        
        if not cursor.fetchone():
            # 創建 leave_types 表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS leave_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    max_days_per_year INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 插入基本請假類型
            leave_types = [
                ('特休', '特別休假', 14),
                ('病假', '病假', 30),
                ('事假', '事假', 14),
                ('公假', '公假', None)
            ]
            
            cursor.executemany("""
                INSERT INTO leave_types (name, description, max_days_per_year)
                VALUES (?, ?, ?)
            """, leave_types)
            
            print("✅ 創建 leave_types 表並插入基本資料")
        
        # 獲取前3名員工來創建測試資料
        cursor.execute("SELECT id, name FROM employees LIMIT 3")
        employees = cursor.fetchall()
        
        if employees:
            # 為第一個員工創建完整的打卡記錄
            emp1_id = employees[0][0]
            cursor.execute("""
                INSERT OR IGNORE INTO attendance (employee_id, check_in, check_out, status, work_date, note)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (emp1_id, f"{today} 09:00:00", f"{today} 18:00:00", 'normal', today, '測試正常打卡'))
            
            cursor.execute("""
                INSERT OR IGNORE INTO attendance (employee_id, check_in, check_out, status, work_date, note)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (emp1_id, f"{today} 09:30:00", f"{today} 18:00:00", 'late', today, '測試遲到'))
            
            print(f"✅ 為員工 {employees[0][1]} 創建完整打卡記錄")
            
            # 為第二個員工創建只有上班的打卡記錄
            if len(employees) > 1:
                emp2_id = employees[1][0]
                cursor.execute("""
                    INSERT OR IGNORE INTO attendance (employee_id, check_in, status, work_date, note)
                    VALUES (?, ?, ?, ?, ?)
                """, (emp2_id, f"{today} 09:00:00", 'incomplete', today, '測試不完整打卡'))
                
                print(f"✅ 為員工 {employees[1][1]} 創建不完整打卡記錄（只有上班）")
            
            # 為第三個員工創建請假記錄（沒有打卡）
            if len(employees) > 2:
                emp3_id = employees[2][0]
                cursor.execute("""
                    INSERT OR IGNORE INTO leave_requests 
                    (employee_id, leave_type, start_time, end_time, hours, status, reason)
                    VALUES (?, 1, ?, ?, 8, 'approved', '個人事務')
                """, (emp3_id, f"{today} 08:00:00", f"{today} 17:00:00"))
                
                print(f"✅ 為員工 {employees[2][1]} 創建全天請假記錄")
        
        conn.commit()
        conn.close()
        
        print("✅ 測試資料創建完成")
        
    except Exception as e:
        print(f"❌ 創建測試資料異常: {str(e)}")


def main():
    """
    主測試函數
    """
    print("🚀 開始完整考勤記錄生成功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 創建測試資料
    create_test_data()
    
    # 2. 執行完整性檢查
    completion_data = test_daily_completion_check()
    
    # 3. 執行完整考勤記錄生成
    generation_data = test_complete_attendance_generation()
    
    # 4. 再次執行完整性檢查，驗證改善情況
    if completion_data and generation_data:
        print("\n" + "=" * 60)
        print("測試 6: 生成後的完整性檢查")
        print("=" * 60)
        
        after_completion_data = test_daily_completion_check()
        
        if after_completion_data:
            before_missing = completion_data['completion_summary']['missing_attendance_count']
            after_missing = after_completion_data['completion_summary']['missing_attendance_count']
            
            print(f"\n📈 改善效果:")
            print(f"   - 生成前缺少考勤記錄: {before_missing} 人")
            print(f"   - 生成後缺少考勤記錄: {after_missing} 人")
            print(f"   - 改善人數: {before_missing - after_missing} 人")
    
    # 5. 測試強制重新生成
    test_force_regeneration()
    
    # 6. 驗證資料庫記錄
    verify_database_records()
    
    print("\n" + "=" * 60)
    print("🎉 完整考勤記錄生成功能測試完成")
    print("=" * 60)


if __name__ == "__main__":
    main() 