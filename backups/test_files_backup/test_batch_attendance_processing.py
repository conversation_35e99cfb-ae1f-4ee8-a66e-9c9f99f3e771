#!/usr/bin/env python3
"""
批量考勤處理測試腳本。

測試功能：
1. 自動檢測上次整理日期
2. 獲取需要處理的日期列表
3. 批量處理考勤整理
4. 檢查處理狀態
"""

import sys
import logging
from datetime import datetime, timedelta
from services.enhanced_attendance_processor import enhanced_attendance_processor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_last_processed_date_detection():
    """測試上次整理日期檢測功能"""
    
    print("🧪 測試上次整理日期檢測")
    print("-" * 40)
    
    try:
        # 檢測上次整理日期
        last_date = enhanced_attendance_processor.get_last_processed_date()
        
        if last_date:
            print(f"✅ 檢測到上次整理日期: {last_date}")
        else:
            print("📭 未找到之前的整理記錄")
        
        return last_date
        
    except Exception as e:
        print(f"❌ 檢測上次整理日期時發生錯誤: {e}")
        return None


def test_dates_to_process():
    """測試需要處理的日期列表獲取"""
    
    print("\n🧪 測試需要處理的日期列表")
    print("-" * 40)
    
    try:
        # 獲取需要處理的日期列表
        dates_to_process = enhanced_attendance_processor.get_dates_to_process()
        
        if dates_to_process:
            print(f"📅 需要處理的日期:")
            for i, date in enumerate(dates_to_process, 1):
                print(f"   {i}. {date}")
            print(f"📊 總共需要處理 {len(dates_to_process)} 天")
        else:
            print("✅ 考勤記錄已是最新，無需處理")
        
        return dates_to_process
        
    except Exception as e:
        print(f"❌ 獲取處理日期列表時發生錯誤: {e}")
        return []


def test_batch_processing():
    """測試批量處理功能"""
    
    print("\n🧪 測試批量考勤處理")
    print("-" * 40)
    
    try:
        # 執行批量處理
        result = enhanced_attendance_processor.process_attendance_batch()
        
        if result['success']:
            print("✅ 批量考勤處理執行成功")
            print(f"📊 處理結果:")
            print(f"   總天數: {result['total_days']}")
            print(f"   成功天數: {result['processed_days']}")
            print(f"   失敗天數: {result['failed_days']}")
            print(f"   總處理員工: {result['total_processed']}")
            print(f"   新增記錄: {result['total_created']}")
            print(f"   更新記錄: {result['total_updated']}")
            
            if result.get('date_range'):
                date_range = result['date_range']
                if date_range['start_date'] and date_range['end_date']:
                    print(f"   處理範圍: {date_range['start_date']} 到 {date_range['end_date']}")
            
            # 顯示每日處理詳情
            if result.get('daily_results'):
                print(f"\n📋 每日處理詳情:")
                for daily in result['daily_results']:
                    status = "✅" if daily['success'] else "❌"
                    print(f"   {status} {daily['date']}: 處理 {daily['processed_count']}, 新增 {daily['created_count']}, 更新 {daily['updated_count']}")
                    if not daily['success'] and daily.get('error'):
                        print(f"      錯誤: {daily['error']}")
        else:
            print("❌ 批量考勤處理執行失敗")
            print(f"   錯誤: {result.get('error', '未知錯誤')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 批量處理測試時發生錯誤: {e}")
        return False


def test_specific_date_range():
    """測試指定日期範圍的處理"""
    
    print("\n🧪 測試指定日期範圍處理")
    print("-" * 40)
    
    try:
        # 測試處理最近3天
        end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        print(f"📅 測試處理範圍: {start_date} 到 {end_date}")
        
        # 手動處理指定範圍
        current_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        processed_days = 0
        total_processed = 0
        
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            
            try:
                result = enhanced_attendance_processor.process_daily_attendance(date_str)
                
                if result['success']:
                    processed_days += 1
                    total_processed += result.get('processed_count', 0)
                    print(f"   ✅ {date_str}: 處理 {result.get('processed_count', 0)} 位員工")
                else:
                    print(f"   ❌ {date_str}: 處理失敗 - {result.get('error')}")
                
            except Exception as e:
                print(f"   ❌ {date_str}: 發生錯誤 - {e}")
            
            current_date += timedelta(days=1)
        
        print(f"📊 指定範圍處理完成: 成功 {processed_days} 天，總處理 {total_processed} 位員工")
        return True
        
    except Exception as e:
        print(f"❌ 指定日期範圍處理測試時發生錯誤: {e}")
        return False


def test_processing_status_check():
    """測試處理狀態檢查"""
    
    print("\n🧪 測試處理狀態檢查")
    print("-" * 40)
    
    try:
        # 檢查處理前狀態
        last_date_before = enhanced_attendance_processor.get_last_processed_date()
        dates_before = enhanced_attendance_processor.get_dates_to_process()
        
        print(f"📅 處理前狀態:")
        print(f"   上次整理日期: {last_date_before or '無'}")
        print(f"   待處理天數: {len(dates_before)}")
        
        # 執行一次批量處理
        if dates_before:
            print(f"\n🚀 執行批量處理...")
            result = enhanced_attendance_processor.process_attendance_batch()
            
            if result['success']:
                print(f"✅ 批量處理完成")
                
                # 檢查處理後狀態
                last_date_after = enhanced_attendance_processor.get_last_processed_date()
                dates_after = enhanced_attendance_processor.get_dates_to_process()
                
                print(f"\n📅 處理後狀態:")
                print(f"   上次整理日期: {last_date_after or '無'}")
                print(f"   待處理天數: {len(dates_after)}")
                
                # 比較前後狀態
                if len(dates_after) < len(dates_before):
                    print(f"✅ 狀態改善: 待處理天數從 {len(dates_before)} 減少到 {len(dates_after)}")
                elif len(dates_after) == 0:
                    print(f"🎉 完美狀態: 所有考勤記錄已是最新")
                else:
                    print(f"⚠️  狀態未變: 待處理天數仍為 {len(dates_after)}")
            else:
                print(f"❌ 批量處理失敗: {result.get('error')}")
        else:
            print(f"✅ 考勤記錄已是最新，無需處理")
        
        return True
        
    except Exception as e:
        print(f"❌ 處理狀態檢查測試時發生錯誤: {e}")
        return False


def main():
    """主測試函數"""
    
    print("🔧 批量考勤處理功能測試")
    print("=" * 50)
    
    test_results = []
    
    # 1. 測試上次整理日期檢測
    last_date = test_last_processed_date_detection()
    test_results.append(last_date is not None or True)  # 無記錄也算正常
    
    # 2. 測試需要處理的日期列表
    dates_to_process = test_dates_to_process()
    test_results.append(True)  # 能獲取列表就算成功
    
    # 3. 測試批量處理功能
    batch_success = test_batch_processing()
    test_results.append(batch_success)
    
    # 4. 測試指定日期範圍處理
    range_success = test_specific_date_range()
    test_results.append(range_success)
    
    # 5. 測試處理狀態檢查
    status_success = test_processing_status_check()
    test_results.append(status_success)
    
    # 統計結果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"🎯 測試結果統計")
    print(f"   通過測試: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！批量考勤處理功能正常")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查相關功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 