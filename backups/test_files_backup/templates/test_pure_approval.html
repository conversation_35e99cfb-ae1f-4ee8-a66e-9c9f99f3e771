<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>純JavaScript審核頁面測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-50">
    <div class="max-w-7xl mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">純JavaScript審核頁面測試</h1>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">執行日誌</h2>
            <div id="logContainer" class="bg-gray-100 p-4 rounded max-h-64 overflow-y-auto">
                <div id="logs"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">審核列表</h2>
            <div id="approvalList">
                <p class="text-gray-500">載入中...</p>
            </div>
        </div>
    </div>

    <script>
        // 日誌函數
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);

            const logsDiv = document.getElementById('logs');
            const p = document.createElement('p');
            p.textContent = logEntry;
            p.className = 'text-sm mb-1';
            logsDiv.appendChild(p);

            // 自動滾動到底部
            const container = document.getElementById('logContainer');
            container.scrollTop = container.scrollHeight;
        }

        // 簡化的顯示列表函數
        function displayApprovalList(leaves) {
            log(`開始渲染 ${leaves.length} 筆審核記錄`);

            const container = document.getElementById('approvalList');

            if (leaves.length === 0) {
                container.innerHTML = '<p class="text-gray-500">沒有待審核記錄</p>';
                log('沒有待審核記錄');
                return;
            }

            try {
                const html = leaves.map((leave, index) => {
                    log(`渲染記錄 ${index + 1}: ${leave.employee_name}`);
                    return `
                        <div class="border border-gray-200 rounded-lg p-4 mb-3 hover:bg-gray-50">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label class="text-sm font-medium text-gray-600">申請人</label>
                                    <p class="text-gray-900">${leave.employee_name || '未知'}</p>
                                    <p class="text-sm text-gray-500">${leave.employee_code || 'N/A'}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">假別</label>
                                    <p class="text-gray-900">${leave.leave_type_name || leave.leave_type || '未知'}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">請假期間</label>
                                    <p class="text-gray-900">${leave.start_date} ~ ${leave.end_date}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">操作</label>
                                    <div class="flex gap-2">
                                        <button onclick="approveLeave(${leave.id}, 'approve')" 
                                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                            核准
                                        </button>
                                        <button onclick="approveLeave(${leave.id}, 'reject')" 
                                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">
                                            拒絕
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <label class="text-sm font-medium text-gray-600">請假原因</label>
                                <p class="text-gray-900">${leave.reason || '無原因說明'}</p>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = html;
                log('✅ 審核列表渲染完成');

            } catch (error) {
                log(`❌ 渲染失敗: ${error.message}`);
                container.innerHTML = '<p class="text-red-500">渲染失敗</p>';
            }
        }

        // 載入審核列表
        async function loadApprovalList() {
            try {
                log('🔄 開始載入審核列表...');

                const response = await fetch('/api/approval/leaves');
                log(`📡 API回應狀態: ${response.status}`);

                if (!response.ok) {
                    throw new Error(`API請求失敗: ${response.status}`);
                }

                const data = await response.json();
                log(`📊 API回應數據結構: ${Object.keys(data).join(', ')}`);

                const leaves = data.records || [];
                log(`✅ 成功載入 ${leaves.length} 筆審核記錄`);

                displayApprovalList(leaves);

            } catch (error) {
                log(`❌ 載入失敗: ${error.message}`);
                const container = document.getElementById('approvalList');
                container.innerHTML = `<p class="text-red-500">載入失敗: ${error.message}</p>`;
            }
        }

        // 審核請假
        async function approveLeave(leaveId, action) {
            try {
                log(`🔄 開始審核請假 ID: ${leaveId}, 動作: ${action}`);

                const response = await fetch(`/api/approval/leaves/${leaveId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: action,
                        comment: `${action === 'approve' ? '核准' : '拒絕'}請假申請`,
                        approver_id: 1 // 測試用的審核人ID
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    const actionText = action === 'approve' ? '核准' : '拒絕';
                    log(`✅ 請假申請已${actionText}`);

                    // 重新載入列表
                    await loadApprovalList();
                } else {
                    log(`❌ 審核失敗: ${result.error || result.message}`);
                }

            } catch (error) {
                log(`❌ 審核請假失敗: ${error.message}`);
            }
        }

        // 頁面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 頁面初始化開始');

            try {
                log('📡 開始載入審核列表...');
                loadApprovalList();

                log('✅ 頁面初始化完成');

            } catch (error) {
                log(`❌ 初始化失敗: ${error.message}`);
            }
        });
    </script>
</body>

</html>