<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統監控 - 智慧考勤系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
         :root {
            --primary-color: #007AFF;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --danger-color: #FF3B30;
            --secondary-color: #8E8E93;
            --background-color: #F2F2F7;
            --card-background: #FFFFFF;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --border-color: #C6C6C8;
        }
        
        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-primary);
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #5856D6);
            backdrop-filter: blur(20px);
            border: none;
            box-shadow: 0 2px 20px rgba(0, 122, 255, 0.1);
        }
        
        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }
        
        .status-card {
            background: var(--card-background);
            border-radius: 16px;
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-healthy {
            background-color: var(--success-color);
        }
        
        .status-warning {
            background-color: var(--warning-color);
        }
        
        .status-error {
            background-color: var(--danger-color);
        }
        
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            background: var(--card-background);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, var(--primary-color), #5856D6);
            border: none;
            border-radius: 12px;
            color: white;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 122, 255, 0.3);
            color: white;
        }
        
        .log-container {
            background: var(--card-background);
            border-radius: 16px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }
        
        .log-entry {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.85rem;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .log-timestamp {
            color: var(--text-secondary);
            margin-right: 12px;
        }
        
        .alert-custom {
            border: none;
            border-radius: 12px;
            border-left: 4px solid;
        }
        
        .alert-success {
            border-left-color: var(--success-color);
            background-color: rgba(52, 199, 89, 0.1);
        }
        
        .alert-warning {
            border-left-color: var(--warning-color);
            background-color: rgba(255, 149, 0, 0.1);
        }
        
        .alert-danger {
            border-left-color: var(--danger-color);
            background-color: rgba(255, 59, 48, 0.1);
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #f0f0f0;
        }
        
        .progress-bar-custom {
            border-radius: 4px;
            transition: width 0.6s ease;
        }
    </style>
</head>

<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i> 系統監控中心
            </a>
            <div class="d-flex">
                <button class="btn refresh-btn" onclick="refreshAllData()">
                    <i class="fas fa-sync-alt me-2"></i>
                    刷新數據
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 系統狀態總覽 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="status-card card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title mb-1">
                                    <span class="status-indicator" id="overallStatusIndicator"></span> 系統整體狀態
                                </h5>
                                <p class="text-muted mb-0" id="lastUpdateTime">最後更新: --</p>
                            </div>
                            <div class="text-end">
                                <div class="metric-value" id="overallStatus">檢查中...</div>
                                <div class="metric-label">系統狀態</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 關鍵指標 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="status-card card">
                    <div class="card-body text-center">
                        <div class="metric-value" id="cpuUsage">--</div>
                        <div class="metric-label">CPU 使用率</div>
                        <div class="progress progress-custom mt-2">
                            <div class="progress-bar progress-bar-custom bg-primary" id="cpuProgress"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="status-card card">
                    <div class="card-body text-center">
                        <div class="metric-value" id="memoryUsage">--</div>
                        <div class="metric-label">記憶體使用率</div>
                        <div class="progress progress-custom mt-2">
                            <div class="progress-bar progress-bar-custom bg-success" id="memoryProgress"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="status-card card">
                    <div class="card-body text-center">
                        <div class="metric-value" id="dbResponseTime">--</div>
                        <div class="metric-label">資料庫響應時間 (ms)</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="status-card card">
                    <div class="card-body text-center">
                        <div class="metric-value" id="uptime">--</div>
                        <div class="metric-label">系統運行時間 (小時)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 圖表區域 -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="chart-container">
                    <h6 class="mb-3">系統資源使用趨勢</h6>
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="chart-container">
                    <h6 class="mb-3">API 響應時間</h6>
                    <canvas id="responseTimeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 服務狀態 -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="status-card card">
                    <div class="card-header">
                        <h6 class="mb-0">服務健康狀態</h6>
                    </div>
                    <div class="card-body">
                        <div id="serviceStatus">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>資料庫服務</span>
                                <span class="badge bg-secondary" id="dbStatus">檢查中</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>API 服務</span>
                                <span class="badge bg-secondary" id="apiStatus">檢查中</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>系統資源</span>
                                <span class="badge bg-secondary" id="systemStatus">檢查中</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="status-card card">
                    <div class="card-header">
                        <h6 class="mb-0">考勤系統統計</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-value text-primary" id="todayAttendance">--</div>
                                <div class="metric-label">今日出勤</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-warning" id="pendingLeaves">--</div>
                                <div class="metric-label">待審批請假</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系統日誌 -->
        <div class="row">
            <div class="col-12">
                <div class="status-card card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">系統日誌</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="clearLogs()">
                            <i class="fas fa-trash me-1"></i>清除
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="systemLogs">
                            <div class="log-entry text-muted text-center py-4">
                                正在載入系統日誌...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全域變數
        let resourceChart, responseTimeChart;
        let resourceData = [];
        let responseTimeData = [];
        let logEntries = [];

        // 初始化圖表
        function initCharts() {
            // 系統資源圖表
            const resourceCtx = document.getElementById('resourceChart').getContext('2d');
            resourceChart = new Chart(resourceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU (%)',
                        data: [],
                        borderColor: '#007AFF',
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        tension: 0.4
                    }, {
                        label: '記憶體 (%)',
                        data: [],
                        borderColor: '#34C759',
                        backgroundColor: 'rgba(52, 199, 89, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // API響應時間圖表
            const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
            responseTimeChart = new Chart(responseCtx, {
                type: 'bar',
                data: {
                    labels: ['資料庫', 'API', '系統檢查', '指標'],
                    datasets: [{
                        label: '響應時間 (ms)',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(52, 199, 89, 0.8)',
                            'rgba(255, 149, 0, 0.8)',
                            'rgba(88, 86, 214, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 獲取系統健康狀態
        async function fetchHealthData() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                updateHealthDisplay(data);
                addLogEntry('info', '系統健康檢查完成');
                return data;
            } catch (error) {
                console.error('獲取健康數據失敗:', error);
                addLogEntry('error', `健康檢查失敗: ${error.message}`);
                return null;
            }
        }

        // 獲取系統指標
        async function fetchMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                updateMetricsDisplay(data);
                updateCharts(data);
                return data;
            } catch (error) {
                console.error('獲取指標數據失敗:', error);
                addLogEntry('error', `指標獲取失敗: ${error.message}`);
                return null;
            }
        }

        // 更新健康狀態顯示
        function updateHealthDisplay(data) {
            const overallStatus = document.getElementById('overallStatus');
            const statusIndicator = document.getElementById('overallStatusIndicator');
            const lastUpdate = document.getElementById('lastUpdateTime');

            // 更新整體狀態
            const status = data.overall_status;
            overallStatus.textContent = getStatusText(status);

            // 更新狀態指示器
            statusIndicator.className = 'status-indicator';
            if (status === 'healthy') {
                statusIndicator.classList.add('status-healthy');
            } else if (status === 'warning') {
                statusIndicator.classList.add('status-warning');
            } else {
                statusIndicator.classList.add('status-error');
            }

            // 更新最後更新時間
            lastUpdate.textContent = `最後更新: ${new Date().toLocaleTimeString()}`;

            // 更新服務狀態
            updateServiceStatus(data);
        }

        // 更新指標顯示
        function updateMetricsDisplay(data) {
            document.getElementById('cpuUsage').textContent = `${data.cpu_usage}%`;
            document.getElementById('memoryUsage').textContent = `${data.memory_usage}%`;
            document.getElementById('dbResponseTime').textContent = data.database_response_time;
            document.getElementById('uptime').textContent = data.uptime_hours.toFixed(1);
            document.getElementById('todayAttendance').textContent = data.attendance_today;
            document.getElementById('pendingLeaves').textContent = data.pending_leaves;

            // 更新進度條
            document.getElementById('cpuProgress').style.width = `${data.cpu_usage}%`;
            document.getElementById('memoryProgress').style.width = `${data.memory_usage}%`;

            // 設定進度條顏色
            const cpuProgress = document.getElementById('cpuProgress');
            const memoryProgress = document.getElementById('memoryProgress');

            cpuProgress.className = 'progress-bar progress-bar-custom';
            memoryProgress.className = 'progress-bar progress-bar-custom';

            if (data.cpu_usage > 80) {
                cpuProgress.classList.add('bg-danger');
            } else if (data.cpu_usage > 60) {
                cpuProgress.classList.add('bg-warning');
            } else {
                cpuProgress.classList.add('bg-primary');
            }

            if (data.memory_usage > 80) {
                memoryProgress.classList.add('bg-danger');
            } else if (data.memory_usage > 60) {
                memoryProgress.classList.add('bg-warning');
            } else {
                memoryProgress.classList.add('bg-success');
            }
        }

        // 更新服務狀態
        function updateServiceStatus(data) {
            const dbStatus = document.getElementById('dbStatus');
            const apiStatus = document.getElementById('apiStatus');
            const systemStatus = document.getElementById('systemStatus');

            // 資料庫狀態
            updateStatusBadge(dbStatus, data.database.status);

            // API狀態（基於整體狀態）
            updateStatusBadge(apiStatus, data.overall_status);

            // 系統狀態
            updateStatusBadge(systemStatus, data.system_resources.status);
        }

        // 更新狀態徽章
        function updateStatusBadge(element, status) {
            element.className = 'badge';
            element.textContent = getStatusText(status);

            if (status === 'healthy') {
                element.classList.add('bg-success');
            } else if (status === 'warning') {
                element.classList.add('bg-warning');
            } else {
                element.classList.add('bg-danger');
            }
        }

        // 獲取狀態文字
        function getStatusText(status) {
            switch (status) {
                case 'healthy':
                    return '正常';
                case 'warning':
                    return '警告';
                case 'error':
                case 'unhealthy':
                    return '異常';
                default:
                    return '未知';
            }
        }

        // 更新圖表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 更新資源圖表
            resourceData.push({
                time: now,
                cpu: data.cpu_usage,
                memory: data.memory_usage
            });

            // 保持最近20個數據點
            if (resourceData.length > 20) {
                resourceData.shift();
            }

            resourceChart.data.labels = resourceData.map(d => d.time);
            resourceChart.data.datasets[0].data = resourceData.map(d => d.cpu);
            resourceChart.data.datasets[1].data = resourceData.map(d => d.memory);
            resourceChart.update();

            // 更新響應時間圖表
            responseTimeChart.data.datasets[0].data = [
                data.database_response_time,
                50, // API平均響應時間（模擬）
                100, // 系統檢查時間（模擬）
                30 // 指標獲取時間（模擬）
            ];
            responseTimeChart.update();
        }

        // 添加日誌條目
        function addLogEntry(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = {
                timestamp,
                level,
                message
            };

            logEntries.unshift(entry);

            // 保持最近50條日誌
            if (logEntries.length > 50) {
                logEntries.pop();
            }

            updateLogDisplay();
        }

        // 更新日誌顯示
        function updateLogDisplay() {
            const logsContainer = document.getElementById('systemLogs');

            if (logEntries.length === 0) {
                logsContainer.innerHTML = '<div class="log-entry text-muted text-center py-4">暫無日誌記錄</div>';
                return;
            }

            const logsHtml = logEntries.map(entry => {
                const levelClass = entry.level === 'error' ? 'text-danger' :
                    entry.level === 'warning' ? 'text-warning' : 'text-muted';

                return `
                    <div class="log-entry">
                        <span class="log-timestamp">${entry.timestamp}</span>
                        <span class="${levelClass}">[${entry.level.toUpperCase()}]</span>
                        <span>${entry.message}</span>
                    </div>
                `;
            }).join('');

            logsContainer.innerHTML = logsHtml;
        }

        // 清除日誌
        function clearLogs() {
            logEntries = [];
            updateLogDisplay();
            addLogEntry('info', '日誌已清除');
        }

        // 刷新所有數據
        async function refreshAllData() {
            addLogEntry('info', '開始刷新系統數據...');

            const healthData = await fetchHealthData();
            const metricsData = await fetchMetrics();

            if (healthData && metricsData) {
                addLogEntry('info', '數據刷新完成');
            } else {
                addLogEntry('error', '數據刷新失敗');
            }
        }

        // 自動刷新
        function startAutoRefresh() {
            setInterval(refreshAllData, 30000); // 每30秒刷新一次
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshAllData();
            startAutoRefresh();

            addLogEntry('info', '系統監控儀表板已啟動');
        });
    </script>
</body>

</html>