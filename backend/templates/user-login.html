<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>員工登錄 - AttendanceOS</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            100: '#e0e7ff',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                        }
                    },
                    fontFamily: {
                        'inter': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input {
            transition: all 0.3s ease;
        }
        
        .input-group input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
        }
        
        .login-button {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            transition: all 0.3s ease;
        }
        
        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
        }
        
        .login-button:active {
            transform: translateY(0);
        }
        
        .error-message {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%,
            100% {
                transform: translateX(0);
            }
            25% {
                transform: translateX(-5px);
            }
            75% {
                transform: translateX(5px);
            }
        }
        
        .loading-spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body class="login-container">
    <!-- 浮動裝飾元素 -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- 主登錄容器 -->
    <div class="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div class="w-full max-w-md">
            <!-- 登錄卡片 -->
            <div class="glass-effect rounded-3xl p-8 shadow-2xl">
                <!-- Logo 和標題 -->
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                        <i data-lucide="building-2" class="w-10 h-10 text-white"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2">Han AttendanceOS</h1>
                    <p class="text-white/80 text-lg">遠漢科技考勤系統</p>
                </div>

                <!-- 登錄表單 -->
                <form id="loginForm" class="space-y-6">
                    <!-- 員工編號輸入 -->
                    <div class="input-group">
                        <label for="employeeId" class="block text-white/90 text-sm font-medium mb-2">
                            <i data-lucide="user" class="w-4 h-4 inline mr-2"></i>
                            員工編號
                        </label>
                        <input type="text" id="employeeId" name="employeeId" required placeholder="請輸入您的員工編號" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 backdrop-blur-sm"
                            autocomplete="username">
                    </div>

                    <!-- 密碼輸入 -->
                    <div class="input-group">
                        <label for="password" class="block text-white/90 text-sm font-medium mb-2">
                            <i data-lucide="lock" class="w-4 h-4 inline mr-2"></i>
                            密碼
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required placeholder="請輸入您的密碼" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 backdrop-blur-sm pr-12"
                                autocomplete="current-password">
                            <button type="button" id="togglePassword" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white/80 transition-colors">
                                <i data-lucide="eye" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 記住我選項 -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center text-white/80 text-sm">
                            <input 
                                type="checkbox" 
                                id="rememberMe" 
                                name="rememberMe"
                                class="w-4 h-4 text-brand-600 bg-white/10 border-white/20 rounded focus:ring-brand-500 focus:ring-2"
                            >
                            <span class="ml-2">記住我</span>
                        </label>
                        <a href="#" class="text-white/80 hover:text-white text-sm transition-colors">
                            忘記密碼？
                        </a>
                    </div>

                    <!-- 錯誤訊息 -->
                    <div id="errorMessage" class="hidden error-message bg-red-500/20 border border-red-500/30 rounded-xl p-3 text-red-100 text-sm backdrop-blur-sm">
                        <i data-lucide="alert-circle" class="w-4 h-4 inline mr-2"></i>
                        <span id="errorText"></span>
                    </div>

                    <!-- 登錄按鈕 -->
                    <button type="submit" id="loginButton" class="login-button w-full py-3 px-6 text-white font-semibold rounded-xl focus:outline-none focus:ring-2 focus:ring-white/30 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loginButtonText" class="flex items-center justify-center">
                            <i data-lucide="log-in" class="w-5 h-5 mr-2"></i>
                            登錄
                        </span>
                        <div id="loginButtonLoading" class="hidden flex items-center justify-center">
                            <div class="loading-spinner mr-2"></div>
                            登錄中...
                        </div>
                    </button>
                </form>

                <!-- 底部資訊 -->
                <div class="mt-8 text-center">
                    <p class="text-white/60 text-sm">
                        © 2025 Han AttendanceOS. ver 2005.6.12
                    </p>
                    <div class="flex justify-center space-x-4 mt-4">
                        <a href="#" class="text-white/60 hover:text-white/80 transition-colors">
                            <i data-lucide="help-circle" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-white/60 hover:text-white/80 transition-colors">
                            <i data-lucide="settings" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-white/60 hover:text-white/80 transition-colors">
                            <i data-lucide="info" class="w-5 h-5"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 功能預覽卡片 -->
            <div class="mt-8 grid grid-cols-3 gap-4">
                <div class="glass-effect rounded-2xl p-4 text-center">
                    <i data-lucide="clock-4" class="w-8 h-8 text-white/80 mx-auto mb-2"></i>
                    <p class="text-white/70 text-xs">加班申請</p>
                </div>
                <div class="glass-effect rounded-2xl p-4 text-center">
                    <i data-lucide="calendar-days" class="w-8 h-8 text-white/80 mx-auto mb-2"></i>
                    <p class="text-white/70 text-xs">請假申請</p>
                </div>
                <div class="glass-effect rounded-2xl p-4 text-center">
                    <i data-lucide="fingerprint" class="w-8 h-8 text-white/80 mx-auto mb-2"></i>
                    <p class="text-white/70 text-xs">線上打卡</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 圖標
        lucide.createIcons();

        // DOM 元素
        const loginForm = document.getElementById('loginForm');
        const employeeIdInput = document.getElementById('employeeId');
        const passwordInput = document.getElementById('password');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const rememberMeCheckbox = document.getElementById('rememberMe');
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        const loginButton = document.getElementById('loginButton');
        const loginButtonText = document.getElementById('loginButtonText');
        const loginButtonLoading = document.getElementById('loginButtonLoading');

        // 密碼顯示/隱藏切換
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const icon = this.querySelector('i');
            if (icon) {
                icon.setAttribute('data-lucide', type === 'password' ? 'eye' : 'eye-off');
                lucide.createIcons();
            }
        });

        // 表單提交處理
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const employeeId = employeeIdInput.value.trim();
            const password = passwordInput.value.trim();
            const rememberMe = rememberMeCheckbox.checked;

            // 基本驗證
            if (!employeeId || !password) {
                showError('請填寫完整的登錄資訊');
                return;
            }

            // 顯示載入狀態
            setLoading(true);
            hideError();

            try {
                console.log('開始登錄請求...');
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: employeeId,
                        password: password,
                        remember_me: rememberMe
                    })
                });

                console.log('收到響應，狀態碼:', response.status);
                console.log('響應頭:', response.headers);

                // 檢查響應是否為JSON格式
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    console.error('響應不是JSON格式:', contentType);
                    throw new Error('伺服器響應格式錯誤');
                }

                const result = await response.json();
                console.log('解析的響應數據:', result);

                if (response.ok) {
                    // 登錄成功
                    console.log('登錄成功，準備跳轉...');
                    showSuccess('登錄成功！正在跳轉...');

                    // 延遲跳轉，讓用戶看到成功訊息
                    setTimeout(() => {
                        console.log('執行頁面跳轉...');
                        window.location.href = '/user';
                    }, 1000);
                } else {
                    // 登錄失敗
                    console.log('登錄失敗:', result);
                    showError(result.error || '登錄失敗，請檢查您的員工編號和密碼');
                }
            } catch (error) {
                console.error('登錄錯誤詳情:', error);
                console.error('錯誤堆疊:', error.stack);

                // 更詳細的錯誤處理
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    showError('網路連線失敗，請檢查網路連接');
                } else if (error.name === 'SyntaxError') {
                    showError('伺服器響應格式錯誤，請聯繫系統管理員');
                } else {
                    showError(`登錄過程發生錯誤: ${error.message}`);
                }
            } finally {
                setLoading(false);
            }
        });

        // 顯示錯誤訊息
        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            errorMessage.classList.add('error-message');

            // 移除動畫類別，以便下次可以重新觸發
            setTimeout(() => {
                errorMessage.classList.remove('error-message');
            }, 500);
        }

        // 隱藏錯誤訊息
        function hideError() {
            errorMessage.classList.add('hidden');
        }

        // 顯示成功訊息
        function showSuccess(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            errorMessage.classList.remove('bg-red-500/20', 'border-red-500/30', 'text-red-100');
            errorMessage.classList.add('bg-green-500/20', 'border-green-500/30', 'text-green-100');

            const icon = errorMessage.querySelector('i');
            if (icon) {
                icon.setAttribute('data-lucide', 'check-circle');
                lucide.createIcons();
            }
        }

        // 設置載入狀態
        function setLoading(loading) {
            loginButton.disabled = loading;

            if (loading) {
                loginButtonText.classList.add('hidden');
                loginButtonLoading.classList.remove('hidden');
            } else {
                loginButtonText.classList.remove('hidden');
                loginButtonLoading.classList.add('hidden');
            }
        }

        // 輸入框焦點效果
        [employeeIdInput, passwordInput].forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // 檢查是否已經登錄
        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/auth/verify');
                if (response.ok) {
                    // 已經登錄，直接跳轉
                    window.location.href = '/user';
                }
            } catch (error) {
                // 未登錄或驗證失敗，繼續顯示登錄頁面
                console.log('未登錄，顯示登錄頁面');
            }
        }

        // 頁面載入時檢查登錄狀態
        checkAuthStatus();

        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            // Enter 鍵提交表單
            if (e.key === 'Enter' && !loginButton.disabled) {
                loginForm.dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>

</html>