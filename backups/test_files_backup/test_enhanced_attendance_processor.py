#!/usr/bin/env python3
"""
增強版考勤處理器測試腳本。

測試功能：
1. 6點換日邏輯
2. 打卡記錄分析
3. 請假整合
4. 工作時間計算
5. 遲到早退加班計算
"""

import sys
import logging
from datetime import datetime, timedelta
from services.enhanced_attendance_processor import enhanced_attendance_processor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_enhanced_attendance_processor():
    """測試增強版考勤處理器"""
    
    print("🚀 開始測試增強版考勤處理器")
    print("=" * 50)
    
    # 測試今天的考勤整理
    today = datetime.now().strftime('%Y-%m-%d')
    
    print(f"🧪 測試日期: {today}")
    print("-" * 30)
    
    try:
        # 執行考勤整理
        result = enhanced_attendance_processor.process_daily_attendance(today)
        
        if result['success']:
            print("✅ 考勤整理執行成功")
            print(f"📊 處理結果:")
            print(f"   總員工數: {result['total_employees']}")
            print(f"   處理員工數: {result['processed_count']}")
            print(f"   新增記錄: {result['created_count']}")
            print(f"   更新記錄: {result['updated_count']}")
        else:
            print("❌ 考勤整理執行失敗")
            print(f"   錯誤: {result.get('error', '未知錯誤')}")
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        return False
    
    print("\n" + "=" * 50)
    
    # 檢查資料庫中的結果
    print("🔍 檢查資料庫結果")
    print("-" * 30)
    
    try:
        from database import create_connection
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 查詢今天的考勤記錄
        cursor.execute("""
            SELECT 
                e.name as employee_name,
                a.work_date,
                a.check_in,
                a.check_out,
                a.status,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.note
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE a.work_date = ?
            ORDER BY e.name
            LIMIT 10
        """, (today,))
        
        records = cursor.fetchall()
        
        if records:
            print(f"📋 找到 {len(records)} 筆今天的考勤記錄:")
            print()
            
            for record in records:
                name, work_date, check_in, check_out, status, work_hours, leave_hours, late_min, early_min, overtime_min, note = record
                
                print(f"👤 員工: {name}")
                print(f"   工作日期: {work_date}")
                print(f"   上班時間: {check_in or '未打卡'}")
                print(f"   下班時間: {check_out or '未打卡'}")
                print(f"   狀態: {status}")
                print(f"   工作時間: {work_hours or 0:.1f} 小時")
                
                if leave_hours and leave_hours > 0:
                    print(f"   請假時間: {leave_hours:.1f} 小時")
                if late_min and late_min > 0:
                    print(f"   遲到: {late_min} 分鐘")
                if early_min and early_min > 0:
                    print(f"   早退: {early_min} 分鐘")
                if overtime_min and overtime_min > 0:
                    print(f"   加班: {overtime_min} 分鐘")
                if note:
                    print(f"   備註: {note}")
                print()
        else:
            print("📭 沒有找到今天的考勤記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查資料庫時發生錯誤: {e}")
        return False
    
    print("🎉 測試完成")
    return True


def test_punch_record_analysis():
    """測試打卡記錄分析邏輯"""
    
    print("\n🧪 測試打卡記錄分析邏輯")
    print("-" * 30)
    
    # 模擬打卡記錄
    target_date = "2025-01-27"
    
    # 測試案例：正常上下班
    punch_records = [
        {'punch_datetime': datetime(2025, 1, 27, 8, 30), 'status_code': '0', 'device_id': 'D001'},
        {'punch_datetime': datetime(2025, 1, 27, 17, 30), 'status_code': '1', 'device_id': 'D001'},
    ]
    
    processor = enhanced_attendance_processor
    check_in, check_out = processor._analyze_punch_records(punch_records, target_date)
    
    print(f"📊 測試案例 1: 正常上下班")
    print(f"   打卡記錄: 08:30 上班, 17:30 下班")
    print(f"   分析結果: 上班 {check_in.strftime('%H:%M') if check_in else '無'}, 下班 {check_out.strftime('%H:%M') if check_out else '無'}")
    
    # 測試案例：跨日班次
    punch_records = [
        {'punch_datetime': datetime(2025, 1, 27, 22, 0), 'status_code': '0', 'device_id': 'D001'},
        {'punch_datetime': datetime(2025, 1, 28, 5, 0), 'status_code': '1', 'device_id': 'D001'},
    ]
    
    check_in, check_out = processor._analyze_punch_records(punch_records, target_date)
    
    print(f"\n📊 測試案例 2: 跨日班次")
    print(f"   打卡記錄: 22:00 上班, 次日 05:00 下班")
    print(f"   分析結果: 上班 {check_in.strftime('%H:%M') if check_in else '無'}, 下班 {check_out.strftime('%H:%M') if check_out else '無'}")
    
    # 測試案例：6點前打卡（應該歸屬前一天）
    punch_records = [
        {'punch_datetime': datetime(2025, 1, 27, 5, 30), 'status_code': '1', 'device_id': 'D001'},
        {'punch_datetime': datetime(2025, 1, 27, 8, 30), 'status_code': '0', 'device_id': 'D001'},
    ]
    
    check_in, check_out = processor._analyze_punch_records(punch_records, target_date)
    
    print(f"\n📊 測試案例 3: 6點前打卡")
    print(f"   打卡記錄: 05:30 下班, 08:30 上班")
    print(f"   分析結果: 上班 {check_in.strftime('%H:%M') if check_in else '無'}, 下班 {check_out.strftime('%H:%M') if check_out else '無'}")
    print(f"   說明: 05:30 的打卡應該歸屬前一天，不計入今天")


if __name__ == "__main__":
    print("🔧 增強版考勤處理器測試")
    print("=" * 50)
    
    # 執行主要測試
    success = test_enhanced_attendance_processor()
    
    # 執行打卡記錄分析測試
    test_punch_record_analysis()
    
    if success:
        print("\n✅ 所有測試通過")
        sys.exit(0)
    else:
        print("\n❌ 測試失敗")
        sys.exit(1) 