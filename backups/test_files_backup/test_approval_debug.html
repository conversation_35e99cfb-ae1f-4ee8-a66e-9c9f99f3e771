<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>審核頁面調試</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">審核頁面調試</h1>

        <div class="bg-white rounded-lg p-6 shadow-lg mb-6">
            <h2 class="text-lg font-semibold mb-4">工具函數庫載入狀態</h2>
            <div id="utilsStatus" class="space-y-2">
                <p>檢查中...</p>
            </div>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-lg mb-6">
            <h2 class="text-lg font-semibold mb-4">API測試</h2>
            <button id="testApiBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                測試API
            </button>
            <div id="apiResult" class="mt-4 p-4 bg-gray-100 rounded">
                <p>點擊按鈕測試API</p>
            </div>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-lg">
            <h2 class="text-lg font-semibold mb-4">通知系統測試</h2>
            <button id="testNotificationBtn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                測試通知
            </button>
        </div>
    </div>

    <script>
        console.log('🚀 開始調試腳本');

        // 檢查工具函數庫載入狀態
        function checkUtilsStatus() {
            const statusDiv = document.getElementById('utilsStatus');
            const checks = [{
                name: 'UtilsLoader',
                obj: window.UtilsLoader
            }, {
                name: 'NotificationSystem',
                obj: window.NotificationSystem
            }, {
                name: 'LeaveCalculator',
                obj: window.LeaveCalculator
            }, {
                name: 'lucide',
                obj: window.lucide
            }];

            const results = checks.map(check => {
                const status = check.obj ? '✅ 已載入' : '❌ 未載入';
                return `<p><strong>${check.name}:</strong> ${status}</p>`;
            });

            statusDiv.innerHTML = results.join('');
        }

        // 測試API
        async function testApi() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>🔄 測試中...</p>';

            try {
                const response = await fetch('/api/approval/leaves');
                const data = await response.json();

                resultDiv.innerHTML = `
                    <p><strong>狀態:</strong> ${response.status}</p>
                    <p><strong>記錄數:</strong> ${data.records ? data.records.length : 0}</p>
                    <pre class="mt-2 text-xs overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 錯誤: ${error.message}</p>`;
            }
        }

        // 測試通知系統
        function testNotification() {
            if (window.NotificationSystem) {
                NotificationSystem.success('通知系統正常工作！');
            } else {
                alert('NotificationSystem 未載入');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📋 DOM載入完成');

            // 檢查初始狀態
            checkUtilsStatus();

            // 嘗試載入工具函數庫
            if (window.UtilsLoader) {
                try {
                    console.log('🔧 開始載入工具函數庫...');
                    await UtilsLoader.initPageUtils('elite-approval');
                    console.log('✅ 工具函數庫載入完成');

                    // 重新檢查狀態
                    setTimeout(checkUtilsStatus, 500);
                } catch (error) {
                    console.error('❌ 工具函數庫載入失敗:', error);
                }
            }

            // 綁定事件
            document.getElementById('testApiBtn').addEventListener('click', testApi);
            document.getElementById('testNotificationBtn').addEventListener('click', testNotification);

            // 創建圖標
            if (window.lucide) {
                lucide.createIcons();
            }

            console.log('🎯 調試頁面初始化完成');
        });
    </script>
</body>

</html>