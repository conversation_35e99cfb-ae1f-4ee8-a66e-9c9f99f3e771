<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統功能總覽 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            100: '#e0e8ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">系統功能總覽</h1>
            <p class="text-lg text-gray-600">AttendanceOS Elite 完整功能列表</p>
        </div>

        <!-- 功能分類 -->
        <div class="max-w-7xl mx-auto">
            <!-- 排班管理功能 -->
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i data-lucide="calendar-clock" class="w-6 h-6 mr-2 text-brand-500"></i> 排班管理系統
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 排班管理 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">排班管理</h3>
                                <p class="text-sm text-gray-500">動態日曆排班</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">提供42格日曆網格，支援月份切換，員工和部門篩選</p>
                        <a href="/elite/schedule" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            開始使用 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 班別管理 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="clock" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">班別管理</h3>
                                <p class="text-sm text-gray-500">班別設定與維護</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">管理標準日班、早班、晚班、夜班、彈性班等各種班別類型</p>
                        <a href="/elite/shifts" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            開始使用 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 排班設定 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="settings" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">排班設定</h3>
                                <p class="text-sm text-gray-500">進階排班規則</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">設定排班衝突檢測、員工班別限制、加班規則等</p>
                        <a href="/elite/masterdata" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            開始使用 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 考勤管理功能 -->
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i data-lucide="user-check" class="w-6 h-6 mr-2 text-brand-500"></i> 考勤管理系統
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 打卡系統 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="fingerprint" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">智能打卡</h3>
                                <p class="text-sm text-gray-500">多元打卡方式</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">支援照片打卡、地理位置驗證、指紋識別等多種打卡方式</p>
                        <a href="/elite/attendance" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            開始使用 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 加班計算 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="calculator" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">靈活加班計算</h3>
                                <p class="text-sm text-gray-500">智能加班計算</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">支援半小時、1小時、1分鐘為單位，自動捨去規則</p>
                        <a href="/elite/settings" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            設定規則 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 考勤報告 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">考勤報告</h3>
                                <p class="text-sm text-gray-500">詳細統計分析</p>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">每日考勤摘要、月度統計、加班時間、遲到早退分析</p>
                        <a href="/elite/analytics" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            查看報告 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 管理工具 -->
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i data-lucide="tool" class="w-6 h-6 mr-2 text-brand-500"></i> 管理工具
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 基本資料 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="database" class="w-6 h-6 text-gray-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">基本資料</h3>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">學歷、職位、假別、薪資等級、班別設定</p>
                        <a href="/elite/masterdata" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            管理 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 員工管理 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">員工管理</h3>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">員工檔案、部門分配、權限設定</p>
                        <a href="/elite/employees" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            管理 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 審核作業 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="file-check" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">審核作業</h3>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">請假申請、加班申請、異常考勤審核</p>
                        <a href="/elite/approval" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            查看 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>

                    <!-- 系統設定 -->
                    <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="settings" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">系統設定</h3>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">加班設定、通知設定、系統參數</p>
                        <a href="/elite/settings" class="inline-flex items-center text-brand-600 text-sm font-medium hover:text-brand-700">
                            設定 <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- API 功能 -->
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <i data-lucide="code" class="w-6 h-6 mr-2 text-brand-500"></i> API 接口功能
                </h2>

                <div class="bg-white rounded-xl p-8 shadow-lg">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- 排班 API -->
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i data-lucide="calendar-clock" class="w-5 h-5 mr-2 text-green-600"></i> 排班管理 API
                            </h3>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/schedules/calendar - 獲取日曆排班</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>POST /api/schedules - 創建排班記錄</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>PUT /api/schedules/&lt;id&gt; - 更新排班</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>DELETE /api/schedules/&lt;id&gt; - 刪除排班</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/schedules/statistics - 排班統計</li>
                            </ul>
                        </div>

                        <!-- 班別 API -->
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i data-lucide="clock" class="w-5 h-5 mr-2 text-blue-600"></i> 班別管理 API
                            </h3>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/shifts - 獲取所有班別</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>POST /api/shifts - 創建新班別</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>PUT /api/shifts/&lt;id&gt; - 更新班別</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>DELETE /api/shifts/&lt;id&gt; - 刪除班別</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/shifts/calculate-overtime-advanced - 進階加班計算</li>
                            </ul>
                        </div>

                        <!-- 考勤 API -->
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i data-lucide="user-check" class="w-5 h-5 mr-2 text-purple-600"></i> 考勤管理 API
                            </h3>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/attendance/daily-report - 每日考勤報告</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/attendance/monthly-summary - 月度考勤摘要</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/attendance/overtime-settings - 加班設定</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>POST /api/attendance/overtime-settings - 更新加班設定</li>
                            </ul>
                        </div>

                        <!-- 基礎 API -->
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i data-lucide="database" class="w-5 h-5 mr-2 text-orange-600"></i> 基礎資料 API
                            </h3>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/employees - 員工列表</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/departments - 部門列表</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/masterdata/&lt;table&gt; - 基礎資料</li>
                                <li class="flex items-center"><i data-lucide="check-circle" class="w-4 h-4 mr-2 text-green-500"></i>GET /api/health - 系統健康檢查</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統狀態 -->
            <div class="text-center p-8 bg-gradient-to-r from-brand-500 to-purple-600 rounded-xl text-white">
                <h2 class="text-2xl font-bold mb-4">系統運行狀態</h2>
                <div class="flex items-center justify-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span>數據庫已連接</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span>API 服務正常</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span>排班系統已就緒</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 初始化 Lucide 圖標 -->
    <script>
        lucide.createIcons();
    </script>
</body>

</html>