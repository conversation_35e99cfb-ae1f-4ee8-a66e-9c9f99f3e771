#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AttendanceOS Elite 新功能演示腳本

此腳本演示新增的三個考勤功能：
1. 打卡紀錄查詢系統
2. 考勤作業管理系統  
3. 考勤整理處理系統

使用方法：
python demo_new_features.py

確保應用程式正在運行於 http://localhost:7072
"""

import requests
import json
import time
from datetime import datetime, timedelta

# API基礎URL
BASE_URL = "http://localhost:7072"

def print_header(title):
    """打印標題"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_success(message):
    """打印成功訊息"""
    print(f"✅ {message}")

def print_error(message):
    """打印錯誤訊息"""
    print(f"❌ {message}")

def print_info(message):
    """打印資訊"""
    print(f"ℹ️  {message}")

def demo_attendance_records():
    """演示打卡記錄查詢功能"""
    print_header("打卡紀錄查詢系統演示")
    
    try:
        # 1. 查詢所有打卡記錄
        print_info("查詢所有打卡記錄...")
        response = requests.get(f"{BASE_URL}/api/attendance/records")
        if response.status_code == 200:
            data = response.json()
            print_success(f"查詢成功！總共 {data['total']} 筆記錄")
            print(f"   📊 統計資訊: {data['stats']}")
        else:
            print_error(f"查詢失敗: {response.status_code}")
        
        # 2. 分頁查詢
        print_info("分頁查詢（第1頁，每頁5筆）...")
        response = requests.get(f"{BASE_URL}/api/attendance/records?page=1&limit=5")
        if response.status_code == 200:
            data = response.json()
            print_success(f"分頁查詢成功！當前頁 {len(data['records'])} 筆記錄")
        
        # 3. 日期範圍篩選
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        print_info(f"日期範圍篩選（{start_date} 到 {end_date}）...")
        response = requests.get(f"{BASE_URL}/api/attendance/records?start_date={start_date}&end_date={end_date}")
        if response.status_code == 200:
            data = response.json()
            print_success(f"日期篩選成功！找到 {len(data['records'])} 筆記錄")
        
        # 4. Excel匯出
        print_info("測試Excel匯出功能...")
        response = requests.get(f"{BASE_URL}/api/attendance/records/export")
        if response.status_code == 200:
            print_success(f"Excel匯出成功！檔案大小: {len(response.content)} 字節")
        
    except Exception as e:
        print_error(f"演示過程中發生錯誤: {e}")

def demo_attendance_management():
    """演示考勤作業管理功能"""
    print_header("考勤作業管理系統演示")
    
    try:
        # 1. 查詢考勤作業
        print_info("查詢考勤作業記錄...")
        response = requests.get(f"{BASE_URL}/api/attendance/management")
        if response.status_code == 200:
            data = response.json()
            print_success(f"查詢成功！找到 {len(data['records'])} 筆作業記錄")
            print(f"   📊 統計資訊: {data['stats']}")
        else:
            print_error(f"查詢失敗: {response.status_code}")
        
        # 2. 生成考勤記錄
        print_info("測試考勤記錄生成...")
        generate_data = {
            "date_range": {
                "start_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "end_date": datetime.now().strftime('%Y-%m-%d')
            },
            "employee_ids": [],  # 空陣列表示所有員工
            "force_regenerate": False
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/generate",
            json=generate_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print_success("考勤記錄生成成功！")
            print(f"   📈 處理摘要: {data['summary']}")
        else:
            print_error(f"生成失敗: {response.status_code}")
        
    except Exception as e:
        print_error(f"演示過程中發生錯誤: {e}")

def demo_attendance_processing():
    """演示考勤整理處理功能"""
    print_header("考勤整理處理系統演示")
    
    try:
        # 1. 查詢考勤整理狀態
        print_info("查詢考勤整理狀態...")
        response = requests.get(f"{BASE_URL}/api/attendance/processing")
        if response.status_code == 200:
            data = response.json()
            print_success("狀態查詢成功！")
            print(f"   📊 系統狀態: {data['status']}")
            if data.get('suggestions'):
                print(f"   💡 建議: {data['suggestions']}")
        else:
            print_error(f"狀態查詢失敗: {response.status_code}")
        
        # 2. 執行考勤整理
        print_info("執行考勤整理處理...")
        processing_data = {
            "date_range": {
                "start_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                "end_date": datetime.now().strftime('%Y-%m-%d')
            },
            "employee_scope": {
                "type": "all"
            },
            "processing_options": {
                "calculate_late_early": True,
                "calculate_overtime": True,
                "integrate_leaves": True,
                "overwrite_existing": False
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/processing/execute",
            json=processing_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print_success("考勤整理執行成功！")
            print(f"   🆔 處理ID: {data['processing_id']}")
            print(f"   📊 總記錄數: {data['total_records']}")
            print(f"   ⏱️  預估完成時間: {data['estimated_completion']}")
            
            # 3. 查詢處理狀態
            processing_id = data['processing_id']
            print_info(f"查詢處理狀態 ({processing_id})...")
            response = requests.get(f"{BASE_URL}/api/attendance/processing/status/{processing_id}")
            if response.status_code == 200:
                status_data = response.json()
                print_success(f"處理狀態: {status_data['status']}")
                print(f"   📈 進度: {status_data['progress']}%")
                print(f"   📝 訊息: {status_data['message']}")
        else:
            print_error(f"執行失敗: {response.status_code}")
        
    except Exception as e:
        print_error(f"演示過程中發生錯誤: {e}")

def demo_system_integration():
    """演示系統整合功能"""
    print_header("系統整合功能演示")
    
    try:
        print_info("檢查系統資料一致性...")
        
        # 檢查打卡記錄數量
        response1 = requests.get(f"{BASE_URL}/api/attendance/records?limit=1")
        if response1.status_code == 200:
            records_count = response1.json()['total']
            print_success(f"打卡記錄總數: {records_count}")
        
        # 檢查考勤作業數量
        response2 = requests.get(f"{BASE_URL}/api/attendance/management?limit=1")
        if response2.status_code == 200:
            management_count = response2.json()['total']
            print_success(f"考勤作業總數: {management_count}")
        
        # 資料一致性檢查
        if records_count == management_count:
            print_success("✨ 資料一致性檢查通過！")
        else:
            print_info(f"資料差異: 打卡記錄 {records_count} vs 考勤作業 {management_count}")
        
    except Exception as e:
        print_error(f"系統整合檢查失敗: {e}")

def main():
    """主函數"""
    print_header("AttendanceOS Elite 新功能演示")
    print("🚀 開始演示新增的考勤管理功能...")
    print(f"🌐 API基礎URL: {BASE_URL}")
    
    # 檢查應用程式是否運行
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print_success("應用程式運行正常")
        else:
            print_error("應用程式狀態異常")
            return
    except requests.exceptions.RequestException:
        print_error("無法連接到應用程式，請確保應用程式正在運行於 http://localhost:7072")
        return
    
    # 執行各功能演示
    demo_attendance_records()
    time.sleep(1)
    
    demo_attendance_management()
    time.sleep(1)
    
    demo_attendance_processing()
    time.sleep(1)
    
    demo_system_integration()
    
    print_header("演示完成")
    print("🎉 所有新功能演示完成！")
    print("📖 詳細功能說明請參閱: PROJECT_COMPLETE_REPORT.md")
    print("🌐 訪問系統: http://localhost:7072/elite")

if __name__ == "__main__":
    main() 