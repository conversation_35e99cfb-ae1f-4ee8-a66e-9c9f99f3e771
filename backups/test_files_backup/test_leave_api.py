#!/usr/bin/env python3
"""
測試請假API的腳本
"""

import sqlite3
from database import create_connection

def test_leave_query():
    """測試請假查詢"""
    conn = create_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    query = """
        SELECT 
            a.id,
            a.employee_id,
            a.work_date,
            a.leave_hours,
            e.name as employee_name,
            l.leave_type,
            l.start_date as leave_start_date,
            l.end_date as leave_end_date,
            l.reason as leave_reason
        FROM attendance a
        LEFT JOIN employees e ON a.employee_id = e.id
        LEFT JOIN leaves l ON l.employee_id = a.employee_id 
            AND a.work_date BETWEEN l.start_date AND l.end_date
            AND l.status = 'approved'
        WHERE a.id = 927
    """
    
    cursor.execute(query)
    record = cursor.fetchone()
    
    if record:
        print("查詢結果:")
        for key in record.keys():
            print(f"  {key}: {record[key]}")
    else:
        print("沒有找到記錄")
    
    conn.close()

if __name__ == "__main__":
    test_leave_query() 