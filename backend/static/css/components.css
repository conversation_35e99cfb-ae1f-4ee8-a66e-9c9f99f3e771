/* 
智慧考勤系統 - UI 組件庫
現代化組件樣式
*/

/* ============ 按鈕組件 ============ */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-500) var(--ease-out);
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:disabled::before {
  display: none;
}

/* 按鈕變體 */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.8);
  color: var(--primary-600);
  border: 2px solid var(--primary-200);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--neutral-600);
}

.btn-ghost:hover {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  color: white;
}

.btn-error {
  background: linear-gradient(135deg, var(--error-500) 0%, var(--error-600) 100%);
  color: white;
}

/* 按鈕大小 */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--text-xl);
}

/* ============ 卡片組件 ============ */
.card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(0, 0, 0, 0.02);
}

/* ============ 表單組件 ============ */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: var(--space-2);
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all var(--duration-200) var(--ease-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.form-input::placeholder {
  color: var(--neutral-400);
}

.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* ============ 統計卡片 ============ */
.stat-card {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.stat-card-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  background: var(--gradient-primary);
  color: white;
}

.stat-card-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
}

.stat-card-change {
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.stat-card-change.positive {
  color: var(--success-600);
}

.stat-card-change.negative {
  color: var(--error-600);
}

/* ============ 表格組件 ============ */
.table-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--neutral-100);
  font-size: var(--text-sm);
}

.table tbody tr:hover {
  background: var(--primary-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ============ 狀態標籤 ============ */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: var(--success-100);
  color: var(--success-700);
}

.badge-warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.badge-error {
  background: var(--error-100);
  color: var(--error-700);
}

.badge-info {
  background: var(--info-100);
  color: var(--info-700);
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

/* ============ 模態框組件 ============ */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: all var(--duration-300) var(--ease-bounce);
}

.modal-overlay.active .modal {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--neutral-800);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-2xl);
  color: var(--neutral-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.modal-close:hover {
  background: var(--neutral-100);
  color: var(--neutral-600);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--neutral-100);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* ============ 載入動畫 ============ */
.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--neutral-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin var(--duration-1000) linear infinite;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* ============ 通知組件 ============ */
.notification {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-4);
  border-left: 4px solid var(--primary-500);
  z-index: 1100;
  transform: translateX(100%);
  transition: all var(--duration-300) var(--ease-out);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left-color: var(--success-500);
}

.notification.warning {
  border-left-color: var(--warning-500);
}

.notification.error {
  border-left-color: var(--error-500);
}

/* ============ 響應式斷點 ============ */
@media (max-width: 768px) {
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .card-body,
  .card-header,
  .card-footer {
    padding: var(--space-4);
  }
  
  .stat-card {
    padding: var(--space-4);
  }
  
  .table th,
  .table td {
    padding: var(--space-2) var(--space-3);
  }
  
  .modal {
    width: 95%;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--space-4);
  }
}