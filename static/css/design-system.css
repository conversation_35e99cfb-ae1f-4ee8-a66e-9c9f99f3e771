/**
 * Han AttendanceOS v2005.6.12 - 設計系統
 * 遠漢科技考勤系統統一UI設計規範
 * 
 * 設計理念：
 * - 簡潔至上：遵循Apple設計語言的簡潔原則
 * - 人性化交互：注重用戶體驗和操作流暢性
 * - 視覺層次：清晰的信息架構和視覺引導
 * - 品牌一致性：統一的色彩、字體和組件風格
 */


/* 導入設計系統組件 */

@import url('./colors.css');
@import url('./typography.css');
@import url('./buttons.css');
@import url('./forms.css');
@import url('./animations.css');

/* 全局CSS變數 */

:root {
    /* 間距系統 */
    --spacing-xs: 0.25rem;
    /* 4px */
    --spacing-sm: 0.5rem;
    /* 8px */
    --spacing-md: 1rem;
    /* 16px */
    --spacing-lg: 1.5rem;
    /* 24px */
    --spacing-xl: 2rem;
    /* 32px */
    --spacing-2xl: 3rem;
    /* 48px */
    --spacing-3xl: 4rem;
    /* 64px */
    /* 圓角系統 */
    --radius-sm: 0.25rem;
    /* 4px */
    --radius-md: 0.5rem;
    /* 8px */
    --radius-lg: 0.75rem;
    /* 12px */
    --radius-xl: 1rem;
    /* 16px */
    --radius-2xl: 1.5rem;
    /* 24px */
    --radius-full: 9999px;
    /* 完全圓角 */
    /* 陰影系統 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    /* Z-index 層級 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    /* 斷點系統 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    /* 過渡效果 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    /* 毛玻璃效果 */
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(20px);
}


/* 全局重置和基礎樣式 */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    color: var(--color-gray-900);
    background-color: var(--color-gray-50);
    min-height: 100vh;
}


/* 滾動條樣式 */

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--color-gray-300);
    border-radius: var(--radius-full);
    transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-400);
}


/* 選擇文字樣式 */

::selection {
    background-color: var(--color-primary-100);
    color: var(--color-primary-900);
}


/* 焦點樣式 */

:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}


/* 無障礙支援 */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}


/* 高對比度模式支援 */

@media (prefers-contrast: high) {
     :root {
        --color-gray-50: #ffffff;
        --color-gray-900: #000000;
    }
}


/* 暗色模式支援 */

@media (prefers-color-scheme: dark) {
     :root {
        --glass-bg: rgba(0, 0, 0, 0.8);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
    body {
        background-color: var(--color-gray-900);
        color: var(--color-gray-100);
    }
}


/* 工具類別 */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.glass {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}


/* 響應式工具類別 */

.hidden {
    display: none !important;
}

@media (max-width: 639px) {
    .sm\:hidden {
        display: none !important;
    }
}

@media (max-width: 767px) {
    .md\:hidden {
        display: none !important;
    }
}

@media (max-width: 1023px) {
    .lg\:hidden {
        display: none !important;
    }
}


/* 列印樣式 */

@media print {
    .no-print {
        display: none !important;
    }
    body {
        background: white !important;
        color: black !important;
    }
    .glass {
        background: white !important;
        border: 1px solid #ccc !important;
        backdrop-filter: none !important;
    }
}