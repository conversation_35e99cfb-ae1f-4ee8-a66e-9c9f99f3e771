#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新計算所有考勤記錄的請假小時
特別處理同一天多種請假類型的情況
"""

import sqlite3
import logging
from datetime import datetime

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_connection():
    """創建資料庫連接"""
    return sqlite3.connect('attendance.db')

def calculate_daily_leave_hours_detailed(employee_id, work_date, cursor):
    """
    計算指定員工在指定日期的詳細請假資訊
    返回總請假小時和所有請假類型的詳細資訊
    """
    # 查詢該日期所有已核准的請假記錄
    query = """
        SELECT leave_type, start_date, end_date, leave_hours, reason, created_at
        FROM leaves 
        WHERE employee_id = ? 
        AND status = 'approved'
        AND ? BETWEEN start_date AND end_date
        ORDER BY created_at ASC
    """
    
    cursor.execute(query, (employee_id, work_date))
    leave_records = cursor.fetchall()
    
    if not leave_records:
        return 0.0, None, []
    
    # 計算總請假小時（最多8小時）
    total_leave_hours = 0.0
    leave_details = []
    
    for record in leave_records:
        leave_type = record[0]
        leave_hours = float(record[3]) if record[3] else 0.0
        reason = record[4]
        
        leave_details.append({
            'leave_type': leave_type,
            'leave_hours': leave_hours,
            'reason': reason
        })
        
        total_leave_hours += leave_hours
    
    # 限制總請假小時不超過8小時
    total_leave_hours = min(8.0, total_leave_hours)
    
    # 選擇主要請假類型（優先級最高的）
    leave_priority = {
        '喪假': 1, '婚假': 2, '產假': 3, '陪產假': 4, 
        '年假': 5, '特休': 6, '病假': 7, '事假': 8
    }
    
    primary_leave_type = None
    best_priority = 999
    
    for detail in leave_details:
        priority = leave_priority.get(detail['leave_type'], 99)
        if priority < best_priority:
            best_priority = priority
            primary_leave_type = detail['leave_type']
    
    return total_leave_hours, primary_leave_type, leave_details

def recalculate_all_leave_hours():
    """重新計算所有考勤記錄的請假小時"""
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢所有考勤記錄
        query = """
            SELECT a.id, a.employee_id, a.work_date, a.leave_hours, a.date_type, e.name as employee_name
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            WHERE a.date_type = 'workday'
            ORDER BY a.employee_id, a.work_date
        """
        
        cursor.execute(query)
        attendance_records = cursor.fetchall()
        
        logger.info(f"找到 {len(attendance_records)} 筆工作日考勤記錄")
        
        updated_count = 0
        leave_records_found = 0
        special_cases = []
        
        for record in attendance_records:
            attendance_id = record['id']
            employee_id = record['employee_id']
            work_date = record['work_date']
            current_leave_hours = record['leave_hours']
            employee_name = record['employee_name']
            
            # 重新計算該日期的請假小時
            correct_leave_hours, primary_leave_type, leave_details = calculate_daily_leave_hours_detailed(
                employee_id, work_date, cursor
            )
            
            # 如果有請假記錄
            if correct_leave_hours > 0:
                leave_records_found += 1
                
                # 檢查是否為特殊情況（多種請假類型）
                if len(leave_details) > 1:
                    special_cases.append({
                        'employee_name': employee_name,
                        'work_date': work_date,
                        'leave_details': leave_details,
                        'total_hours': correct_leave_hours,
                        'primary_type': primary_leave_type
                    })
                    
                    logger.info(f"🔍 特殊情況: {employee_name} ({work_date}) - {len(leave_details)}種請假類型:")
                    for detail in leave_details:
                        logger.info(f"   - {detail['leave_type']}: {detail['leave_hours']}小時 ({detail['reason']})")
                    logger.info(f"   總計: {correct_leave_hours}小時, 主要類型: {primary_leave_type}")
                
                # 如果計算結果與現有數據不同，則更新
                if abs(correct_leave_hours - current_leave_hours) > 0.01:
                    logger.info(f"更新: {employee_name} ({work_date}) - {current_leave_hours}小時 → {correct_leave_hours}小時 ({primary_leave_type})")
                    
                    # 更新資料庫
                    update_query = """
                        UPDATE attendance 
                        SET leave_hours = ?, status = 'leave'
                        WHERE id = ?
                    """
                    cursor.execute(update_query, (correct_leave_hours, attendance_id))
                    updated_count += 1
                else:
                    # 確保狀態正確
                    cursor.execute("UPDATE attendance SET status = 'leave' WHERE id = ?", (attendance_id,))
            else:
                # 沒有請假記錄，確保leave_hours為0
                if current_leave_hours > 0:
                    logger.info(f"清除: {employee_name} ({work_date}) - 清除無效的請假小時 {current_leave_hours}")
                    cursor.execute("UPDATE attendance SET leave_hours = 0.0 WHERE id = ?", (attendance_id,))
                    updated_count += 1
        
        # 提交變更
        conn.commit()
        
        logger.info(f"\n✅ 重新計算完成！")
        logger.info(f"📊 統計結果:")
        logger.info(f"   總考勤記錄: {len(attendance_records)}")
        logger.info(f"   有請假記錄: {leave_records_found}")
        logger.info(f"   更新記錄數: {updated_count}")
        logger.info(f"   特殊情況數: {len(special_cases)}")
        
        # 輸出特殊情況摘要
        if special_cases:
            logger.info(f"\n🎯 特殊情況摘要 (同一天多種請假類型):")
            for case in special_cases:
                types_str = " + ".join([f"{d['leave_type']}({d['leave_hours']}h)" for d in case['leave_details']])
                logger.info(f"   {case['employee_name']} - {case['work_date']}: {types_str} = {case['total_hours']}小時")
        
        conn.close()
        
        return {
            'success': True,
            'updated_count': updated_count,
            'leave_records_found': leave_records_found,
            'special_cases': special_cases
        }
        
    except Exception as e:
        logger.error(f"重新計算過程發生錯誤: {e}")
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    logger.info("🚀 開始重新計算所有考勤記錄的請假小時...")
    result = recalculate_all_leave_hours()
    
    if result['success']:
        logger.info("🎉 重新計算完成！")
        if result.get('special_cases'):
            logger.info(f"發現 {len(result['special_cases'])} 個特殊情況（同一天多種請假類型）")
    else:
        logger.error(f"❌ 重新計算失敗: {result['error']}") 