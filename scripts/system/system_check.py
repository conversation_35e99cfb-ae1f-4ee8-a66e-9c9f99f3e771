#!/usr/bin/env python3
"""
系統狀態檢查腳本

此腳本用於檢查 AttendanceOS Elite 系統的所有功能是否正常運行。
包括：
- 資料庫連接測試
- API端點測試
- 前端頁面測試
- 數據完整性檢查
"""

import requests
import sqlite3
import sys
import json
from datetime import datetime
from database import create_connection

# 系統配置
BASE_URL = "http://localhost:7072"
API_BASE = f"{BASE_URL}/api"

class SystemChecker:
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.results = []
    
    def test(self, name, func):
        """執行測試並記錄結果"""
        try:
            result = func()
            if result:
                print(f"✅ {name}")
                self.passed += 1
                self.results.append({"test": name, "status": "PASS", "message": "OK"})
            else:
                print(f"❌ {name}")
                self.failed += 1
                self.results.append({"test": name, "status": "FAIL", "message": "Test returned False"})
        except Exception as e:
            print(f"❌ {name} - 錯誤: {str(e)}")
            self.failed += 1
            self.results.append({"test": name, "status": "ERROR", "message": str(e)})
    
    def check_database_connection(self):
        """檢查資料庫連接"""
        conn = create_connection()
        if conn:
            conn.close()
            return True
        return False
    
    def check_database_tables(self):
        """檢查資料庫表結構"""
        conn = create_connection()
        cursor = conn.cursor()
        
        required_tables = ['employees', 'departments', 'attendance', 'schedules', 'leaves', 'permissions']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        for table in required_tables:
            if table not in existing_tables:
                return False
        return True
    
    def check_test_data(self):
        """檢查測試資料"""
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查員工數量
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        
        # 檢查考勤記錄數量
        cursor.execute("SELECT COUNT(*) FROM attendance")
        attendance_count = cursor.fetchone()[0]
        
        # 檢查部門數量
        cursor.execute("SELECT COUNT(*) FROM departments")
        department_count = cursor.fetchone()[0]
        
        conn.close()
        
        return employee_count >= 10 and attendance_count >= 100 and department_count >= 4
    
    def check_api_endpoint(self, endpoint, method="GET", data=None):
        """檢查API端點"""
        try:
            url = f"{API_BASE}{endpoint}"
            if method == "GET":
                response = requests.get(url, timeout=5)
            elif method == "POST":
                response = requests.post(url, json=data, timeout=5)
            
            return response.status_code == 200
        except:
            return False
    
    def check_frontend_page(self, path):
        """檢查前端頁面"""
        try:
            url = f"{BASE_URL}{path}"
            response = requests.get(url, timeout=5)
            return response.status_code == 200 and "html" in response.headers.get('content-type', '')
        except:
            return False
    
    def run_all_tests(self):
        """執行所有測試"""
        print("🚀 開始系統狀態檢查...")
        print("=" * 50)
        
        # 資料庫測試
        print("\n📊 資料庫測試")
        print("-" * 20)
        self.test("資料庫連接", self.check_database_connection)
        self.test("資料庫表結構", self.check_database_tables)
        self.test("測試資料完整性", self.check_test_data)
        
        # API端點測試
        print("\n🔌 API端點測試")
        print("-" * 20)
        
        # 基本API
        self.test("儀表板統計API", lambda: self.check_api_endpoint("/reports/dashboard"))
        self.test("員工列表API", lambda: self.check_api_endpoint("/employees"))
        self.test("部門列表API", lambda: self.check_api_endpoint("/departments"))
        self.test("請假記錄API", lambda: self.check_api_endpoint("/leaves"))
        
        # 分析API
        self.test("出勤趨勢分析API", lambda: self.check_api_endpoint("/analytics/attendance-trends"))
        self.test("部門統計分析API", lambda: self.check_api_endpoint("/analytics/department-stats"))
        self.test("時間分布分析API", lambda: self.check_api_endpoint("/analytics/time-distribution"))
        self.test("請假統計分析API", lambda: self.check_api_endpoint("/analytics/leave-stats"))
        self.test("效率指標分析API", lambda: self.check_api_endpoint("/analytics/efficiency"))
        
        # 前端頁面測試
        print("\n🌐 前端頁面測試")
        print("-" * 20)
        self.test("Elite版儀表板", lambda: self.check_frontend_page("/elite"))
        self.test("Elite版考勤頁面", lambda: self.check_frontend_page("/elite/attendance"))
        self.test("Elite版員工管理", lambda: self.check_frontend_page("/elite/employees"))
        self.test("Elite版請假管理", lambda: self.check_frontend_page("/elite/leaves"))
        self.test("Elite版排班系統", lambda: self.check_frontend_page("/elite/schedule"))
        self.test("Elite版數據分析", lambda: self.check_frontend_page("/elite/analytics"))
        self.test("Elite版系統設定", lambda: self.check_frontend_page("/elite/settings"))
        
        # 其他版本頁面
        self.test("移動端版本", lambda: self.check_frontend_page("/mobile"))
        self.test("專業版本", lambda: self.check_frontend_page("/professional"))
        self.test("現代版本", lambda: self.check_frontend_page("/modern"))
        
        # 功能測試
        print("\n⚙️ 功能測試")
        print("-" * 20)
        self.test("今日考勤查詢", lambda: self.check_api_endpoint("/attendance/today/1"))
        
        # 輸出結果
        print("\n" + "=" * 50)
        print("📋 測試結果摘要")
        print("=" * 50)
        print(f"✅ 通過: {self.passed}")
        print(f"❌ 失敗: {self.failed}")
        print(f"📊 總計: {self.passed + self.failed}")
        print(f"🎯 成功率: {(self.passed / (self.passed + self.failed) * 100):.1f}%")
        
        if self.failed == 0:
            print("\n🎉 所有測試通過！系統運行正常。")
            return True
        else:
            print(f"\n⚠️  有 {self.failed} 個測試失敗，請檢查系統狀態。")
            return False
    
    def generate_report(self):
        """生成詳細報告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": self.passed + self.failed,
                "passed": self.passed,
                "failed": self.failed,
                "success_rate": round(self.passed / (self.passed + self.failed) * 100, 1) if (self.passed + self.failed) > 0 else 0
            },
            "results": self.results
        }
        
        with open("system_check_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存至: system_check_report.json")

def main():
    """主函數"""
    print("AttendanceOS Elite - 系統狀態檢查工具")
    print("版本: 1.0.0")
    print(f"檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    checker = SystemChecker()
    success = checker.run_all_tests()
    checker.generate_report()
    
    # 返回適當的退出碼
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 