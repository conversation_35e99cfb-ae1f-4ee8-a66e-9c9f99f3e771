"""
員工管理相關API模組

包含所有員工管理相關的API端點：
- 員工CRUD操作
- 部門管理
- 主管和代理人管理
- 權限管理
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
import sqlite3
import logging

# 創建藍圖
employee_bp = Blueprint('employee', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數
from database import create_connection

@employee_bp.route("/api/employees", methods=["GET"])
def get_employees():
    """
    獲取員工列表，支援多條件查詢
    
    查詢參數：
    - department_id: 部門ID篩選
    - employee_id: 員工編號搜尋（支援模糊搜尋）
    - name: 員工姓名搜尋（支援模糊搜尋）
    - position: 職位搜尋（支援模糊搜尋）
    
    返回：
    - 員工列表，包含部門名稱、角色名稱、主管姓名等完整資訊
    """
    try:
        # 獲取查詢參數
        department_id = request.args.get('department_id')
        employee_id = request.args.get('employee_id')
        name = request.args.get('name')
        position = request.args.get('position')
        
        # 構建基本查詢
        query = """
            SELECT e.id, e.name, e.employee_id, e.department_id, e.position, 
                   e.email, e.phone, e.role_id, e.manager_id, e.hire_date, 
                   e.status, e.salary_level, e.id_number, e.address, 
                   e.emergency_contact, e.emergency_phone, e.photo_url, e.shift_type,
                   e.allow_online_punch, e.education_level_id,
                   d.name as department_name,
                   r.role_name as role_name,
                   m.name as manager_name,
                   s.name as shift_name,
                   el.name as education_level_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions r ON e.role_id = r.id
            LEFT JOIN employees m ON e.manager_id = m.id
            LEFT JOIN shifts s ON e.shift_type = s.id
            LEFT JOIN education_levels el ON e.education_level_id = el.id
            WHERE 1=1
        """
        
        params = []
        
        # 添加查詢條件
        if department_id:
            query += " AND e.department_id = ?"
            params.append(department_id)
            
        if employee_id:
            query += " AND e.employee_id LIKE ?"
            params.append(f"%{employee_id}%")
            
        if name:
            query += " AND e.name LIKE ?"
            params.append(f"%{name}%")
            
        if position:
            query += " AND e.position LIKE ?"
            params.append(f"%{position}%")
        
        query += " ORDER BY e.id"
        
        conn = create_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        employees = []
        for row in cursor.fetchall():
            employee = {
                'id': row[0],
                'name': row[1],
                'employee_id': row[2],
                'department_id': row[3],
                'position': row[4],
                'email': row[5],
                'phone': row[6],
                'role_id': row[7],
                'manager_id': row[8],
                'hire_date': row[9],
                'status': row[10],
                'salary_level': row[11],
                'id_number': row[12],
                'address': row[13],
                'emergency_contact': row[14],
                'emergency_phone': row[15],
                'photo_url': row[16],
                'shift_type': row[17],
                'allow_online_punch': row[18],
                'education_level_id': row[19],
                'department_name': row[20],
                'role_name': row[21],
                'manager_name': row[22],
                'shift_name': row[23],
                'education_level_name': row[24]
            }
            
            # 獲取員工技能
            cursor.execute("""
                SELECT s.id, s.name, s.category, es.proficiency_level, es.years_experience, es.certification
                FROM employee_skills es
                JOIN skills s ON es.skill_id = s.id
                WHERE es.employee_id = ?
                ORDER BY s.category, s.name
            """, (employee['id'],))
            
            skills = []
            for skill_row in cursor.fetchall():
                skill = {
                    'id': skill_row[0],
                    'name': skill_row[1],
                    'category': skill_row[2],
                    'proficiency_level': skill_row[3],
                    'years_experience': skill_row[4],
                    'certification': skill_row[5]
                }
                skills.append(skill)
            
            employee['skills'] = skills
            employees.append(employee)
        
        conn.close()
        return jsonify({'employees': employees})
        
    except Exception as e:
        logger.error(f"獲取員工列表失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/employees", methods=["POST"])
def create_employee():
    """
    新增員工
    
    請求體：
    - name: 員工姓名（必填）
    - employee_id: 員工編號（必填）
    - department_id: 部門ID（必填）
    - position: 職位（必填）
    - email: 電子郵件（必填）
    - phone: 電話號碼
    - role_id: 角色ID
    - manager_id: 主管ID
    - password: 密碼
    - hire_date: 到職日期
    - status: 員工狀態
    - salary_level: 薪資等級
    - id_number: 身分證號
    - address: 地址
    - emergency_contact: 緊急聯絡人
    - emergency_phone: 緊急聯絡電話
    - photo_url: 照片連結
    
    返回：
    - 新增的員工資訊
    """
    conn = None
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['name', 'employee_id', 'department_id', 'position', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填欄位: {field}'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 驗證外鍵約束
        if data.get('department_id'):
            cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的部門不存在'}), 400
        
        if data.get('role_id'):
            cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的角色不存在'}), 400
        
        if data.get('manager_id'):
            cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的主管不存在'}), 400
        
        # 檢查員工編號是否已存在
        cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
        if cursor.fetchone():
            return jsonify({'error': '員工編號已存在'}), 400
        
        # 插入新員工
        cursor.execute("""
            INSERT INTO employees (name, employee_id, department_id, position, email, phone, 
                                 role_id, manager_id, password, hire_date, status, salary_level, 
                                 id_number, address, emergency_contact, emergency_phone, photo_url, 
                                 shift_type, allow_online_punch, education_level_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['name'],
            data['employee_id'],
            data['department_id'],
            data['position'],
            data['email'],
            data.get('phone', ''),
            data.get('role_id', 3),  # 預設為一般員工
            data.get('manager_id'),
            data.get('password', 'default123'),
            data.get('hire_date', datetime.now().strftime('%Y-%m-%d')),
            data.get('status', 'active'),
            data.get('salary_level', 'junior'),
            data.get('id_number', ''),
            data.get('address', ''),
            data.get('emergency_contact', ''),
            data.get('emergency_phone', ''),
            data.get('photo_url', ''),
            data.get('shift_type', 1),  # 預設為第一個班表
            data.get('allow_online_punch', 1),  # 預設允許線上打卡
            data.get('education_level_id')  # 學歷等級
        ))
        
        employee_id = cursor.lastrowid
        
        # 處理技能資料
        if "skills" in data and isinstance(data["skills"], list):
            for skill in data["skills"]:
                if isinstance(skill, dict) and "id" in skill:
                    cursor.execute("""
                        INSERT INTO employee_skills 
                        (employee_id, skill_id, proficiency_level, years_experience, certification)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        employee_id,
                        skill["id"],
                        skill.get("proficiency_level", "intermediate"),
                        skill.get("years_experience", 1),
                        skill.get("certification")
                    ))
        
        conn.commit()
        
        logger.info(f"員工新增成功: {data['name']} (ID: {employee_id})")
        return jsonify({
            'message': '員工新增成功',
            'employee_id': employee_id
        }), 201
        
    except sqlite3.IntegrityError as e:
        error_msg = str(e)
        if "FOREIGN KEY constraint failed" in error_msg:
            return jsonify({'error': '資料關聯錯誤，請檢查部門、角色或主管設定'}), 400
        else:
            return jsonify({'error': f'資料庫錯誤: {error_msg}'}), 400
    except Exception as e:
        logger.error(f"新增員工失敗: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@employee_bp.route("/api/departments", methods=["GET"])
def get_departments():
    """
    獲取部門列表
    
    返回：
    - 部門列表，包含部門統計資訊
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取部門列表及員工統計
        cursor.execute("""
            SELECT d.id, d.name, d.description, d.manager_id, d.created_at,
                   COUNT(e.id) as employee_count,
                   m.name as manager_name
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
            LEFT JOIN employees m ON d.manager_id = m.id
            GROUP BY d.id, d.name, d.description, d.manager_id, d.created_at, m.name
            ORDER BY d.id
        """)
        
        departments = []
        for row in cursor.fetchall():
            department = {
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'manager_id': row[3],
                'created_at': row[4],
                'employee_count': row[5],
                'manager_name': row[6]
            }
            departments.append(department)
        
        conn.close()
        return jsonify({'departments': departments})
        
    except Exception as e:
        logger.error(f"獲取部門列表失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/departments", methods=["POST"])
def create_department():
    """
    新增部門
    
    請求體：
    - name: 部門名稱（必填）
    - description: 部門描述
    - manager_id: 部門主管ID
    
    返回：
    - 新增的部門資訊
    """
    conn = None
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        if not data.get('name'):
            return jsonify({'error': '缺少必填欄位: name'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 驗證主管是否存在
        if data.get('manager_id'):
            cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的主管不存在'}), 400
        
        # 檢查部門名稱是否已存在
        cursor.execute("SELECT id FROM departments WHERE name = ?", (data['name'],))
        if cursor.fetchone():
            return jsonify({'error': '部門名稱已存在'}), 400
        
        # 插入新部門
        cursor.execute("""
            INSERT INTO departments (name, description, manager_id, created_at)
            VALUES (?, ?, ?, datetime('now'))
        """, (
            data['name'],
            data.get('description', ''),
            data.get('manager_id')
        ))
        
        department_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"部門新增成功: {data['name']} (ID: {department_id})")
        return jsonify({
            'message': '部門新增成功',
            'department_id': department_id
        }), 201
        
    except Exception as e:
        logger.error(f"新增部門失敗: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@employee_bp.route("/api/employees/<int:employee_id>", methods=["GET", "PUT", "DELETE"])
def manage_employee(employee_id):
    """
    管理單一員工資料
    
    支援獲取、更新和刪除員工資料。
    
    參數：
    employee_id (int): 員工ID
    
    GET: 獲取員工詳細資料
    PUT: 更新員工資料
    DELETE: 刪除員工
    
    返回：
    - 員工資料或操作結果
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 取得特定員工資料
            cursor.execute(
                """
                SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, 
                e.position, e.email, e.phone, e.password, e.hire_date, e.status,
                e.salary_level, e.id_number, e.address, e.emergency_contact, 
                e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type, e.allow_online_punch
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                LEFT JOIN permissions p ON e.role_id = p.id
                LEFT JOIN employees m ON e.manager_id = m.id
                WHERE e.id = ?
            """,
                (employee_id,),
            )

            row = cursor.fetchone()
            if not row:
                return jsonify({"error": "找不到員工資料"}), 404

            columns = [col[0] for col in cursor.description]
            employee = dict(zip(columns, row))
            
            # 確保所有欄位都存在，即使是NULL值
            expected_fields = [
                'id', 'name', 'employee_id', 'department_id', 'department_name',
                'position', 'email', 'phone', 'password', 'hire_date', 'status',
                'salary_level', 'id_number', 'address', 'emergency_contact',
                'emergency_phone', 'role_id', 'role_name', 'manager_id', 'manager_name', 'shift_type', 'allow_online_punch'
            ]
            
            for field in expected_fields:
                if field not in employee:
                    employee[field] = None
            
            return jsonify(employee)

        elif request.method == "PUT":
            # 更新員工資料
            data = request.json
            
            # 驗證外鍵約束
            if "department_id" in data and data["department_id"]:
                cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的部門不存在"}), 400
            
            if "role_id" in data and data["role_id"]:
                cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的角色不存在"}), 400
            
            if "manager_id" in data and data["manager_id"]:
                cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的主管不存在"}), 400
                
                # 防止員工設定自己為主管
                if str(data["manager_id"]) == str(employee_id):
                    return jsonify({"error": "員工不能設定自己為主管"}), 400
            
            set_clause = []
            params = []

            # 處理所有可更新的欄位
            updatable_fields = [
                "name", "employee_id", "department_id", "position", "email", "phone",
                "hire_date", "status", "salary_level", "id_number", "address", 
                "emergency_contact", "emergency_phone", "role_id", "manager_id", "photo_url", "shift_type", "allow_online_punch", "education_level_id"
            ]

            for field in updatable_fields:
                if field in data:
                    # 處理空值情況
                    value = data[field]
                    if value == "" or value is None:
                        if field in ["manager_id", "role_id", "education_level_id"]:
                            value = None
                        elif field in ["department_id"]:
                            continue  # 部門ID不能為空，跳過
                    set_clause.append(f"{field} = ?")
                    params.append(value)

            # 特殊處理密碼欄位（只有提供且非空時才更新）
            if "password" in data and data["password"] and data["password"].strip():
                set_clause.append("password = ?")
                params.append(data["password"])

            if not set_clause:
                return jsonify({"error": "未提供更新資料"}), 400

            params.append(employee_id)
            
            try:
                cursor.execute(
                    f"""
                    UPDATE employees
                    SET {', '.join(set_clause)}
                    WHERE id = ?
                """,
                    params,
                )

                if cursor.rowcount == 0:
                    return jsonify({"error": "員工不存在或無變更"}), 404

                # 處理技能資料更新
                if "skills" in data and isinstance(data["skills"], list):
                    # 先刪除現有技能
                    cursor.execute("DELETE FROM employee_skills WHERE employee_id = ?", (employee_id,))
                    
                    # 新增技能
                    for skill in data["skills"]:
                        if isinstance(skill, dict) and "id" in skill:
                            cursor.execute("""
                                INSERT INTO employee_skills 
                                (employee_id, skill_id, proficiency_level, years_experience, certification)
                                VALUES (?, ?, ?, ?, ?)
                            """, (
                                employee_id,
                                skill["id"],
                                skill.get("proficiency_level", "intermediate"),
                                skill.get("years_experience", 1),
                                skill.get("certification")
                            ))

                conn.commit()
                logger.info(f"成功更新員工 ID {employee_id} 的資料")
                return jsonify({"message": "員工資料更新成功"})
                
            except sqlite3.IntegrityError as e:
                error_msg = str(e)
                if "FOREIGN KEY constraint failed" in error_msg:
                    return jsonify({"error": "資料關聯錯誤，請檢查部門、角色或主管設定"}), 400
                elif "UNIQUE constraint failed" in error_msg:
                    return jsonify({"error": "員工編號已存在，請使用其他編號"}), 400
                else:
                    return jsonify({"error": f"資料完整性錯誤: {error_msg}"}), 400

        elif request.method == "DELETE":
            # 刪除員工資料
            cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))

            if cursor.rowcount == 0:
                return jsonify({"error": "找不到員工資料"}), 404

            conn.commit()
            logger.info(f"成功刪除員工 ID {employee_id}")
            return jsonify({"message": "員工刪除成功"})
            
    except sqlite3.Error as e:
        logger.error(f"管理員工資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@employee_bp.route("/api/employees/managers", methods=["GET"])
def get_managers():
    """
    獲取可以作為審核人員的主管列表
    
    返回有主管權限的員工列表，包括系統管理員、部門主管等。
    
    返回：
    - 主管列表和統計資訊
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取有主管權限的員工（role_id <= 2 表示系統管理員或部門主管）
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name,
                   p.role_name, e.department_id
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions p ON e.role_id = p.id
            WHERE e.role_id <= 2 OR e.id IN (
                SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL
            )
            ORDER BY e.department_id, e.name
        """)
        
        columns = [col[0] for col in cursor.description]
        managers = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'managers': managers,
            'total': len(managers)
        })
        
    except sqlite3.Error as e:
        logger.error(f"獲取主管列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@employee_bp.route("/api/employees/substitutes/<int:employee_id>", methods=["GET"])
def get_substitutes(employee_id):
    """
    獲取可以作為代理人的員工列表
    
    參數：
    employee_id (int): 員工ID
    
    返回同部門或有權限的員工作為代理人選項。
    
    返回：
    - 代理人列表
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 先獲取當前員工的部門資訊
        cursor.execute("""
            SELECT department_id FROM employees WHERE id = ?
        """, (employee_id,))
        
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "找不到員工資料"}), 404
            
        current_dept_id = result[0]
        
        # 獲取同部門或有權限的員工作為代理人
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name,
                   p.role_name, e.department_id
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions p ON e.role_id = p.id
            WHERE (e.department_id = ? OR e.role_id <= 2) 
            AND e.id != ? 
            AND e.status = 'active'
            ORDER BY 
                CASE WHEN e.department_id = ? THEN 0 ELSE 1 END,
                e.role_id ASC,
                e.name
        """, (current_dept_id, employee_id, current_dept_id))
        
        columns = [col[0] for col in cursor.description]
        substitutes = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'substitutes': substitutes,
            'total': len(substitutes),
            'current_department_id': current_dept_id
        })
        
    except sqlite3.Error as e:
        logger.error(f"獲取代理人列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@employee_bp.route("/api/departments/<int:dept_id>", methods=["GET", "PUT", "DELETE"])
def manage_single_department(dept_id):
    """
    管理單一部門資料
    
    支援獲取、更新和刪除部門資料。
    
    參數：
    dept_id (int): 部門ID
    
    GET: 獲取部門詳細資料
    PUT: 更新部門資料
    DELETE: 刪除部門
    
    返回：
    - 部門資料或操作結果
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取部門詳細資料
            cursor.execute("""
                SELECT d.*, 
                       COUNT(e.id) as employee_count,
                       m.name as manager_name
                FROM departments d
                LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
                LEFT JOIN employees m ON d.manager_id = m.id
                WHERE d.id = ?
                GROUP BY d.id
            """, (dept_id,))
            
            row = cursor.fetchone()
            if not row:
                return jsonify({"error": "找不到部門資料"}), 404
            
            columns = [col[0] for col in cursor.description]
            department = dict(zip(columns, row))
            
            return jsonify(department)
            
        elif request.method == "PUT":
            # 更新部門資料
            data = request.json
            
            # 驗證主管是否存在
            if "manager_id" in data and data["manager_id"]:
                cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的主管不存在"}), 400
            
            set_clause = []
            params = []
            
            updatable_fields = ["name", "description", "manager_id", "budget", "location"]
            
            for field in updatable_fields:
                if field in data:
                    set_clause.append(f"{field} = ?")
                    params.append(data[field])
            
            if not set_clause:
                return jsonify({"error": "未提供更新資料"}), 400
            
            params.append(dept_id)
            
            cursor.execute(
                f"""
                UPDATE departments
                SET {', '.join(set_clause)}
                WHERE id = ?
            """,
                params,
            )
            
            if cursor.rowcount == 0:
                return jsonify({"error": "部門不存在或無變更"}), 404
            
            conn.commit()
            logger.info(f"成功更新部門 ID {dept_id} 的資料")
            return jsonify({"message": "部門資料更新成功"})
            
        elif request.method == "DELETE":
            # 檢查是否有員工屬於此部門
            cursor.execute("SELECT COUNT(*) FROM employees WHERE department_id = ?", (dept_id,))
            employee_count = cursor.fetchone()[0]
            
            if employee_count > 0:
                return jsonify({"error": f"無法刪除部門，仍有 {employee_count} 名員工屬於此部門"}), 400
            
            # 刪除部門
            cursor.execute("DELETE FROM departments WHERE id = ?", (dept_id,))
            
            if cursor.rowcount == 0:
                return jsonify({"error": "找不到部門資料"}), 404
            
            conn.commit()
            logger.info(f"成功刪除部門 ID {dept_id}")
            return jsonify({"message": "部門刪除成功"})
            
    except sqlite3.Error as e:
        logger.error(f"管理部門資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@employee_bp.route("/api/departments/permissions", methods=["GET", "POST"])
def manage_department_permissions():
    """
    管理部門權限設定
    
    GET: 獲取部門權限設定
    POST: 更新部門權限設定
    
    返回：
    - 部門權限設定資料
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取部門權限設定
            cursor.execute("""
                SELECT d.id, d.name, d.manager_id, m.name as manager_name,
                       COUNT(e.id) as employee_count,
                       GROUP_CONCAT(DISTINCT p.role_name) as available_roles
                FROM departments d
                LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
                LEFT JOIN employees m ON d.manager_id = m.id
                LEFT JOIN employees ep ON d.id = ep.department_id
                LEFT JOIN permissions p ON ep.role_id = p.id
                GROUP BY d.id, d.name, d.manager_id, m.name
                ORDER BY d.name
            """)
            
            columns = [col[0] for col in cursor.description]
            departments = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            return jsonify({
                'departments': departments,
                'total': len(departments)
            })
            
        else:  # POST
            # 更新部門權限設定
            data = request.json
            
            if not data or 'department_id' not in data:
                return jsonify({"error": "缺少部門ID"}), 400
            
            department_id = data['department_id']
            
            # 更新部門主管
            if 'manager_id' in data:
                cursor.execute("""
                    UPDATE departments SET manager_id = ? WHERE id = ?
                """, (data['manager_id'], department_id))
            
            # 更新部門員工的預設權限
            if 'default_role_id' in data:
                cursor.execute("""
                    UPDATE employees 
                    SET role_id = ? 
                    WHERE department_id = ? AND role_id IS NULL
                """, (data['default_role_id'], department_id))
            
            conn.commit()
            logger.info(f"成功更新部門 {department_id} 的權限設定")
            return jsonify({"message": "部門權限設定更新成功"})
            
    except sqlite3.Error as e:
        logger.error(f"管理部門權限錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@employee_bp.route("/api/departments/stats", methods=["GET"])
def get_departments_stats():
    """
    獲取部門統計資訊
    
    返回各部門的員工數量、出勤率等統計資料。
    
    返回：
    - 部門統計資訊
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取部門基本統計
        cursor.execute("""
            SELECT d.id, d.name, d.manager_id, m.name as manager_name,
                   COUNT(DISTINCT e.id) as total_employees,
                   COUNT(DISTINCT CASE WHEN e.status = 'active' THEN e.id END) as active_employees,
                   COUNT(DISTINCT CASE WHEN e.status = 'inactive' THEN e.id END) as inactive_employees
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            LEFT JOIN employees m ON d.manager_id = m.id
            GROUP BY d.id, d.name, d.manager_id, m.name
            ORDER BY d.name
        """)
        
        departments_stats = []
        for row in cursor.fetchall():
            dept_id, dept_name, manager_id, manager_name, total_emp, active_emp, inactive_emp = row
            
            # 計算本月出勤率
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT a.employee_id) as attended_employees,
                    COUNT(DISTINCT e.id) as total_active_employees
                FROM employees e
                LEFT JOIN attendance a ON e.id = a.employee_id 
                    AND strftime('%Y-%m', a.check_in) = strftime('%Y-%m', 'now')
                WHERE e.department_id = ? AND e.status = 'active'
            """, (dept_id,))
            
            attendance_result = cursor.fetchone()
            attended_emp = attendance_result[0] if attendance_result[0] else 0
            attendance_rate = (attended_emp / active_emp * 100) if active_emp > 0 else 0
            
            departments_stats.append({
                'id': dept_id,
                'name': dept_name,
                'manager_id': manager_id,
                'manager_name': manager_name,
                'total_employees': total_emp,
                'active_employees': active_emp,
                'inactive_employees': inactive_emp,
                'attendance_rate': round(attendance_rate, 2),
                'attended_employees_this_month': attended_emp
            })
        
        return jsonify({
            'departments': departments_stats,
            'total_departments': len(departments_stats),
            'summary': {
                'total_employees': sum(d['total_employees'] for d in departments_stats),
                'total_active': sum(d['active_employees'] for d in departments_stats),
                'total_inactive': sum(d['inactive_employees'] for d in departments_stats),
                'average_attendance_rate': round(
                    sum(d['attendance_rate'] for d in departments_stats) / len(departments_stats)
                    if departments_stats else 0, 2
                )
            }
        })
        
    except sqlite3.Error as e:
        logger.error(f"獲取部門統計錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()

# ==================== 升遷紀錄相關API ====================

@employee_bp.route("/api/employees/<int:employee_id>/promotions", methods=["GET"])
def get_employee_promotions(employee_id):
    """
    獲取員工升遷紀錄
    
    參數：
    - employee_id: 員工ID
    
    返回：
    - 員工升遷紀錄列表
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT p.id, p.promotion_date, p.from_position, p.to_position, 
                   p.reason, p.notes, pt.name as type_name, pt.description as type_description
            FROM employee_promotions p
            JOIN promotion_types pt ON p.type_id = pt.id
            WHERE p.employee_id = ?
            ORDER BY p.promotion_date DESC
        """, (employee_id,))
        
        promotions = []
        for row in cursor.fetchall():
            promotion = {
                'id': row[0],
                'promotion_date': row[1],
                'from_position': row[2],
                'to_position': row[3],
                'reason': row[4],
                'notes': row[5],
                'type_name': row[6],
                'type_description': row[7]
            }
            promotions.append(promotion)
        
        conn.close()
        return jsonify({'promotions': promotions})
        
    except Exception as e:
        logger.error(f"獲取員工升遷紀錄失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/employees/<int:employee_id>/promotions", methods=["POST"])
def add_employee_promotion(employee_id):
    """
    新增員工升遷紀錄
    
    參數：
    - employee_id: 員工ID
    
    請求體：
    - type_id: 升遷類型ID
    - promotion_date: 升遷日期
    - from_position: 原職位
    - to_position: 新職位
    - reason: 升遷原因
    - notes: 備註
    
    返回：
    - 新增的升遷紀錄
    """
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['type_id', 'promotion_date', 'from_position', 'to_position', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填欄位: {field}'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO employee_promotions 
            (employee_id, type_id, promotion_date, from_position, to_position, reason, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            employee_id,
            data['type_id'],
            data['promotion_date'],
            data['from_position'],
            data['to_position'],
            data['reason'],
            data.get('notes', '')
        ))
        
        promotion_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"成功新增員工 {employee_id} 的升遷紀錄")
        return jsonify({'message': '升遷紀錄新增成功', 'promotion_id': promotion_id})
        
    except Exception as e:
        logger.error(f"新增員工升遷紀錄失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/promotion-types", methods=["GET"])
def get_promotion_types():
    """
    獲取升遷類型列表
    
    返回：
    - 升遷類型列表
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, description FROM promotion_types ORDER BY id")
        
        types = []
        for row in cursor.fetchall():
            type_data = {
                'id': row[0],
                'name': row[1],
                'description': row[2]
            }
            types.append(type_data)
        
        conn.close()
        return jsonify({'promotion_types': types})
        
    except Exception as e:
        logger.error(f"獲取升遷類型失敗: {e}")
        return jsonify({'error': str(e)}), 500


# ==================== 獎懲紀錄相關API ====================

@employee_bp.route("/api/employees/<int:employee_id>/rewards", methods=["GET"])
def get_employee_rewards(employee_id):
    """
    獲取員工獎懲紀錄
    
    參數：
    - employee_id: 員工ID
    
    返回：
    - 員工獎懲紀錄列表
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT r.id, r.reward_date, r.reason, r.amount, r.approver, r.notes,
                   rt.name as type_name, rt.type as category, rt.description as type_description
            FROM employee_rewards r
            JOIN reward_types rt ON r.type_id = rt.id
            WHERE r.employee_id = ?
            ORDER BY r.reward_date DESC
        """, (employee_id,))
        
        rewards = []
        for row in cursor.fetchall():
            reward = {
                'id': row[0],
                'reward_date': row[1],
                'reason': row[2],
                'amount': row[3],
                'approver': row[4],
                'notes': row[5],
                'type_name': row[6],
                'category': row[7],
                'type_description': row[8]
            }
            rewards.append(reward)
        
        conn.close()
        return jsonify({'rewards': rewards})
        
    except Exception as e:
        logger.error(f"獲取員工獎懲紀錄失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/employees/<int:employee_id>/rewards", methods=["POST"])
def add_employee_reward(employee_id):
    """
    新增員工獎懲紀錄
    
    參數：
    - employee_id: 員工ID
    
    請求體：
    - type_id: 獎懲類型ID
    - reward_date: 獎懲日期
    - reason: 事由
    - amount: 金額（選填）
    - approver: 核准人
    - notes: 備註
    
    返回：
    - 新增的獎懲紀錄
    """
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['type_id', 'reward_date', 'reason', 'approver']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填欄位: {field}'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO employee_rewards 
            (employee_id, type_id, reward_date, reason, amount, approver, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            employee_id,
            data['type_id'],
            data['reward_date'],
            data['reason'],
            data.get('amount'),
            data['approver'],
            data.get('notes', '')
        ))
        
        reward_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"成功新增員工 {employee_id} 的獎懲紀錄")
        return jsonify({'message': '獎懲紀錄新增成功', 'reward_id': reward_id})
        
    except Exception as e:
        logger.error(f"新增員工獎懲紀錄失敗: {e}")
        return jsonify({'error': str(e)}), 500


@employee_bp.route("/api/reward-types", methods=["GET"])
def get_reward_types():
    """
    獲取獎懲類型列表
    
    返回：
    - 獎懲類型列表
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, type, description FROM reward_types ORDER BY type, id")
        
        types = []
        for row in cursor.fetchall():
            type_data = {
                'id': row[0],
                'name': row[1],
                'type': row[2],
                'description': row[3]
            }
            types.append(type_data)
        
        conn.close()
        return jsonify({'reward_types': types})
        
    except Exception as e:
        logger.error(f"獲取獎懲類型失敗: {e}")
        return jsonify({'error': str(e)}), 500 