# Han AttendanceOS API 文檔 v2025.6.12

## 📋 文檔說明

此文檔記錄了 Han AttendanceOS 系統的完整API端點，包含所有模組的API接口、參數說明、返回格式和使用範例。

**⚠️ 重要提醒：**
- 所有API端點都需要適當的認證和權限
- 請求和響應格式統一使用JSON
- 所有時間格式使用ISO 8601標準
- 分頁查詢統一使用page和limit參數

**🚨 版本更新：**
- **v2025.6.12**: 新增工作回報API模組、完善刪除功能、優化UI設計系統
- **v2005.6.12**: 新增考勤編輯API、加班申請API、換班功能API
- **v2005.6.8**: 修復CSS按鈕居中問題相關的前端API調用
- **v2005.6.5**: 完成API模組化遷移，新增120+ API端點

---

## 🗂️ API模組總覽

| 模組名稱 | API數量 | 主要功能 | 狀態 |
|---------|---------|---------|------|
| **attendance_api.py** | 30+ | 考勤管理、打卡記錄、數據匯入匯出 | ✅ 完成 |
| **employee_api.py** | 20+ | 員工管理、部門管理、組織架構、升遷獎懲 | ✅ 完成 |
| **shift_api.py** | 12+ | 班表管理、排班設定、加班計算 | ✅ 完成 |
| **leave_api.py** | 9+ | 請假申請、審核流程、假別管理 | ✅ 完成 |
| **report_api.py** | 18+ | 報表分析、數據統計、趨勢分析 | ✅ 完成 |
| **system_api.py** | 15+ | 系統設定、健康監控、基本資料 | ✅ 完成 |
| **auth_api.py** | 10+ | 認證權限、用戶管理、審核流程 | ✅ 完成 |
| **attendance_edit_api.py** | 8+ | 考勤編輯、時間修正、狀態調整 | ✅ 完成 |
| **overtime_api.py** | 8+ | 加班申請、審核管理、統計分析 | ✅ 完成 |
| **work_report_api.py** | 10+ | 工作回報、照片上傳、回報管理 | ✅ 新增 |

**總計**: 140+ API端點

---

## 📊 詳細API清單

### 1. 考勤管理API (attendance_api.py)

**核心功能**: 打卡記錄、考勤分析、數據匯入匯出、換班管理

#### 基本考勤記錄
| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/attendance/records` | GET | 查詢打卡記錄（支援分頁和篩選） | |
| `/api/attendance/records/<id>` | GET | 獲取單個打卡記錄詳情 | |
| `/api/attendance/records/export` | GET | 匯出考勤記錄（Excel/PDF） | |
| `/api/attendance/records/export-pdf` | GET | 匯出PDF考勤記錄 | |
| `/api/attendance/import` | POST | 匯入考勤資料CSV檔案 | |
| `/api/attendance/import-text` | POST | 匯入文字格式考勤資料 | |
| `/api/attendance/manual` | POST | 手動新增考勤記錄 | |

#### 即時考勤功能
| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/attendance/today/<employee_id>` | GET | 獲取員工今日考勤記錄 | |
| `/api/attendance/clock-in` | POST | 員工上班打卡 | |
| `/api/attendance/clock-out` | POST | 員工下班打卡 | |
| `/api/attendance/recent` | GET | 獲取最近打卡記錄 | |
| `/api/attendance/daily-summary/<employee_id>` | GET | 獲取員工每日考勤摘要 | |

#### 考勤管理功能
| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/attendance/management` | GET | 考勤作業管理列表 | |
| `/api/attendance/management/generate` | POST | 生成考勤記錄 | |
| `/api/attendance/management/update-shift` | POST | 更新考勤班別（換班功能） | ✅ |
| `/api/attendance/management/daily-completion-check` | GET | 每日考勤完成度檢查 | ✅ |
| `/api/attendance/management/generate-complete` | POST | 生成完整月度考勤 | ✅ |
| `/api/attendance/management/incremental-process` | POST | 增量考勤處理 | ✅ |

#### 考勤分析與報表
| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/attendance/analysis` | GET | 出勤分析報表 | |
| `/api/attendance/trends` | GET | 考勤趨勢分析 | |
| `/api/attendance/daily-report` | GET | 每日考勤報表 | |
| `/api/attendance/monthly-summary` | GET | 月度考勤摘要 | |
| `/api/attendance/work-date` | GET | 獲取工作日期資訊 | |

#### 系統設定與監控
| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/attendance/settings` | GET/POST | 管理考勤設定 | |
| `/api/attendance/processing` | GET | 獲取考勤處理狀態 | |
| `/api/attendance/processing/execute` | POST | 執行考勤處理 | |
| `/api/attendance/processing/status/<id>` | GET | 獲取處理狀態 | |
| `/api/attendance/raw-records` | GET | 獲取原始打卡記錄 | |
| `/api/attendance/overtime-settings` | GET/POST | 加班設定管理 | |

### 2. 工作回報API (work_report_api.py) 🆕

**核心功能**: 工作回報管理、照片上傳、回報統計、主管回饋

#### 基本工作回報管理
| API端點 | 方法 | 功能描述 | 參數說明 |
|---------|------|----------|----------|
| `/api/work-reports` | GET | 獲取工作回報列表 | `employee_id`, `start_date`, `end_date`, `category`, `page`, `limit` |
| `/api/work-reports` | POST | 提交新的工作回報 | `employee_id`, `category`, `content`, `photos`, `location` |
| `/api/work-reports/<id>` | GET | 獲取單個工作回報詳情 | - |
| `/api/work-reports/<id>` | PUT | 更新工作回報內容 | `content`, `photos`, `supervisor_feedback` |
| `/api/work-reports/<id>` | DELETE | 刪除工作回報記錄 | - |

#### 照片管理功能
| API端點 | 方法 | 功能描述 | 參數說明 |
|---------|------|----------|----------|
| `/api/work-reports/upload-photos` | POST | 上傳工作回報照片 | `files[]` (multipart/form-data) |
| `/uploads/work-reports/<filename>` | GET | 獲取工作回報照片 | 支援CORS跨域訪問 |

#### 統計與分析功能
| API端點 | 方法 | 功能描述 | 參數說明 |
|---------|------|----------|----------|
| `/api/work-reports/stats` | GET | 獲取工作回報統計 | 總數、未讀數、已回覆數、今日數 |
| `/api/work-reports/categories` | GET | 獲取回報類型統計 | 各類型回報數量分布 |
| `/api/work-reports/trends` | GET | 獲取回報趨勢分析 | `period` (daily/weekly/monthly) |

#### 主管管理功能
| API端點 | 方法 | 功能描述 | 參數說明 |
|---------|------|----------|----------|
| `/api/work-reports/<id>/feedback` | POST | 主管回饋工作回報 | `supervisor_feedback`, `is_read` |
| `/api/work-reports/<id>/mark-read` | POST | 標記回報為已讀 | - |
| `/api/work-reports/unread` | GET | 獲取未讀回報列表 | `supervisor_id` |

**回報類型說明：**
- `issue`: 問題回報
- `suggestion`: 建議回饋  
- `progress`: 進度更新
- `feedback`: 一般回饋
- `other`: 其他類型

**照片上傳規範：**
- 支援格式：jpg, jpeg, png, gif
- 檔案大小限制：10MB
- 檔案命名：`work_report_YYYYMMDD_HHMMSS_mmm.ext`
- 存儲位置：`uploads/work-reports/`

### 3. 考勤編輯API (attendance_edit_api.py)

**核心功能**: 考勤記錄編輯、時間修正、狀態調整

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/attendance/edit/<id>` | GET | 獲取考勤記錄編輯資訊 |
| `/api/attendance/edit/<id>` | PUT | 更新考勤記錄 |
| `/api/attendance/edit/validate` | POST | 驗證考勤時間合理性 |
| `/api/attendance/edit/calculate` | POST | 重新計算考勤指標 |
| `/api/attendance/edit/history/<id>` | GET | 獲取考勤編輯歷史 |
| `/api/attendance/edit/batch` | POST | 批次編輯考勤記錄 |
| `/api/attendance/edit/approve/<id>` | POST | 審核考勤編輯申請 |
| `/api/attendance/edit/reject/<id>` | POST | 拒絕考勤編輯申請 |

### 4. 加班申請API (overtime_api.py)

**核心功能**: 加班申請、審核管理、統計分析

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/overtime/requests` | GET/POST | 加班申請列表和新增 |
| `/api/overtime/requests/<id>` | GET/PUT/DELETE | 管理加班申請 |
| `/api/overtime/requests/<id>/approve` | POST | 審核加班申請（核准） |
| `/api/overtime/requests/<id>/reject` | POST | 審核加班申請（拒絕） |
| `/api/overtime/types` | GET | 獲取加班類型列表 |
| `/api/overtime/statistics` | GET | 獲取加班統計資料 |
| `/api/overtime/calendar` | GET | 加班日曆檢視 |
| `/api/overtime/balance/<employee_id>` | GET | 獲取員工加班時數餘額 |

### 5. 員工管理API (employee_api.py)

**核心功能**: 員工資料管理、部門管理、組織架構、升遷獎懲管理

#### 基本員工管理
| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/employees` | GET/POST | 員工列表查詢與新增 |
| `/api/employees/<id>` | GET/PUT/DELETE | 管理單一員工資料 |
| `/api/employees/managers` | GET | 獲取主管列表 |
| `/api/employees/substitutes/<id>` | GET | 獲取代理人列表 |

#### 部門管理
| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/departments` | GET/POST | 部門列表查詢與新增 |
| `/api/departments/<id>` | GET/PUT/DELETE | 管理單一部門 |
| `/api/departments/permissions` | GET/POST | 管理部門權限 |
| `/api/departments/stats` | GET | 獲取部門統計資訊 |

#### 升遷管理
| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/employees/<id>/promotions` | GET/POST | 獲取/新增員工升遷紀錄 |
| `/api/employees/<id>/promotions/<promotion_id>` | GET/PUT/DELETE | 管理單一升遷紀錄 |
| `/api/promotion-types` | GET | 獲取升遷類型列表 |
| `/api/promotions/statistics` | GET | 獲取升遷統計資料 |

#### 獎懲管理
| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/employees/<id>/rewards` | GET/POST | 獲取/新增員工獎懲紀錄 |
| `/api/employees/<id>/rewards/<reward_id>` | GET/PUT/DELETE | 管理單一獎懲紀錄 |
| `/api/reward-types` | GET | 獲取獎懲類型列表 |
| `/api/rewards/statistics` | GET | 獲取獎懲統計資料 |

### 6. 班表管理API (shift_api.py)

**核心功能**: 班別設定、排班管理、加班計算

| API端點 | 方法 | 功能描述 | 新增/更新 |
|---------|------|----------|----------|
| `/api/shifts` | GET/POST | 班別列表查詢與新增 | |
| `/api/shifts/<id>` | GET/PUT/DELETE | 管理單一班別 | |
| `/api/shifts/calculate-overtime` | POST | 計算加班時數 | |
| `/api/shifts/calculate-overtime-advanced` | POST | 進階加班時數計算 | |
| `/api/shifts/holiday` | GET | 獲取假日班表資訊 | ✅ |
| `/api/schedules` | POST | 新增排班記錄 | |
| `/api/schedules/<id>` | PUT/DELETE | 更新和刪除排班 | |
| `/api/schedules/batch` | POST | 批次設定班表 | |
| `/api/schedules/rules` | GET/POST | 管理排班規則 | |
| `/api/schedules/employee/<id>` | GET | 獲取員工排班 | |
| `/api/schedules/calendar` | GET | 獲取日曆排班 | |
| `/api/schedules/statistics` | GET | 排班統計資料 | |

### 7. 請假管理API (leave_api.py)

**核心功能**: 請假申請、審核流程、假別管理

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/leave-types` | GET/POST | 請假類型管理 |
| `/api/leave-types/<id>` | GET/PUT/DELETE | 管理單一請假類型 |
| `/api/leave-requests` | GET/POST | 請假申請列表和新增 |
| `/api/leave-requests/<id>` | GET/PUT/DELETE | 管理請假申請 |
| `/api/leave-requests/<id>/approve` | POST | 審核請假申請（核准） |
| `/api/leave-requests/<id>/reject` | POST | 審核請假申請（拒絕） |
| `/api/leave-requests/statistics` | GET | 獲取請假統計資料 |
| `/api/leave-requests/calendar` | GET | 請假日曆檢視 |
| `/api/leave-requests/balance/<employee_id>` | GET | 獲取員工假期餘額 |

### 8. 報表分析API (report_api.py)

**核心功能**: 數據統計、趨勢分析、報表匯出

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/dashboard/stats` | GET | 獲取儀表板統計數據 |
| `/api/reports/attendance` | GET | 考勤報表分析 |
| `/api/reports/leave` | GET | 請假報表分析 |
| `/api/reports/overtime` | GET | 加班報表分析 |
| `/api/reports/employee-performance` | GET | 員工績效報表 |
| `/api/reports/department-summary` | GET | 部門摘要報表 |
| `/api/reports/trends` | GET | 趨勢分析報表 |
| `/api/reports/export` | GET | 匯出報表（Excel/PDF） |

### 9. 系統功能API (system_api.py)

**核心功能**: 系統設定、健康監控、基本資料管理

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/system/health` | GET | 系統健康檢查 |
| `/api/system/settings` | GET/POST | 系統設定管理 |
| `/api/system/backup` | POST | 資料庫備份 |
| `/api/system/logs` | GET | 系統日誌查詢 |
| `/api/system/notifications` | GET/POST | 系統通知管理 |

### 10. 認證權限API (auth_api.py)

**核心功能**: 用戶認證、權限管理、審核流程

| API端點 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/auth/login` | POST | 用戶登錄 |
| `/api/auth/logout` | POST | 用戶登出 |
| `/api/auth/verify` | GET | 驗證登錄狀態 |
| `/api/auth/permissions` | GET | 獲取用戶權限 |
| `/api/approval/leaves/<id>` | POST | 審核請假申請 |
| `/api/approval/overtime/<id>` | POST | 審核加班申請 |

---

## 🔧 API使用範例

### 工作回報API使用範例

#### 1. 提交工作回報
```javascript
// 先上傳照片
const formData = new FormData();
formData.append('files', photoFile);

const uploadResponse = await fetch('/api/work-reports/upload-photos', {
    method: 'POST',
    body: formData
});
const uploadResult = await uploadResponse.json();

// 提交工作回報
const reportData = {
    employee_id: 'E001',
    category: 'issue',
    content: '發現設備異常，需要維修',
    photos: uploadResult.filenames,
    location: '工廠A區'
};

const response = await fetch('/api/work-reports', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(reportData)
});
```

#### 2. 獲取工作回報列表
```javascript
const response = await fetch('/api/work-reports?employee_id=E001&category=issue&limit=10');
const reports = await response.json();
```

#### 3. 刪除工作回報
```javascript
const response = await fetch('/api/work-reports/17', {
    method: 'DELETE'
});
```

### 考勤管理API使用範例

#### 1. 換班功能
```javascript
const shiftData = {
    attendance_id: 123,
    new_shift_id: 456
};

const response = await fetch('/api/attendance/management/update-shift', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(shiftData)
});
```

#### 2. 獲取今日考勤
```javascript
const response = await fetch('/api/attendance/today/E001');
const todayAttendance = await response.json();
```

---

## 📝 API響應格式

### 成功響應格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具體數據內容
    },
    "timestamp": "2025-06-12T15:30:00Z"
}
```

### 錯誤響應格式
```json
{
    "success": false,
    "error": "錯誤類型",
    "message": "詳細錯誤說明",
    "timestamp": "2025-06-12T15:30:00Z"
}
```

### 分頁響應格式
```json
{
    "success": true,
    "data": {
        "items": [...],
        "pagination": {
            "page": 1,
            "limit": 10,
            "total": 100,
            "pages": 10
        }
    }
}
```

---

## 🚀 最新功能更新

### v2025.6.12 更新內容

#### 🆕 新增功能
1. **工作回報系統完整實現**
   - 照片上傳和管理功能
   - 工作回報CRUD操作
   - 統計分析功能
   - 主管回饋系統

2. **UI設計系統優化**
   - 優雅的單線條圖示設計
   - 精緻的陰影效果和互動動畫
   - 統一的Apple/Google設計語言

3. **儀表板詳細資訊顯示**
   - 請假記錄：假別、期間、原因、狀態
   - 加班申請：日期、時間、原因、狀態
   - 考勤記錄：簡化為上下班打卡顯示

#### 🔧 功能改進
1. **刪除功能完整實現**
   - 新增DELETE API端點
   - 專業的確認模態框
   - 完整的錯誤處理

2. **照片存儲系統重構**
   - 改為檔案系統存儲
   - 支援CORS跨域訪問
   - 優化檔案命名規範

3. **PWA功能增強**
   - 完整的圖標集合
   - Service Worker支援
   - 離線功能實現

---

## 📞 技術支援

如有API使用問題，請參考：
- 系統日誌：`/api/system/logs`
- 健康檢查：`/api/system/health`
- 開發文檔：`docs/technical/`

**最後更新**: 2025年6月12日
**文檔版本**: v2025.6.12
**系統版本**: Han AttendanceOS v2025.6.12 