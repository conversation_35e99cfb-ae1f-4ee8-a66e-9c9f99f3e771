<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本資料API測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <h1>基本資料API測試</h1>

    <div class="test-section">
        <h2>API測試</h2>
        <button onclick="testAPI('leave_types', '假別類型')">測試假別類型</button>
        <button onclick="testAPI('education_levels', '學歷等級')">測試學歷等級</button>
        <button onclick="testAPI('positions', '職位')">測試職位</button>
        <button onclick="testAPI('departments', '部門')">測試部門</button>
        <button onclick="testAllAPIs()">測試所有API</button>
    </div>

    <div id="results"></div>

    <script>
        async function testAPI(tableName, displayName) {
            const resultsDiv = document.getElementById('results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-section loading';
            testDiv.innerHTML = `<h3>測試 ${displayName} (${tableName})</h3><p>載入中...</p>`;
            resultsDiv.appendChild(testDiv);

            try {
                const response = await fetch(`/api/masterdata/${tableName}`);
                const data = await response.json();

                if (response.ok) {
                    const records = data.records || data.items || [];
                    testDiv.className = 'test-section success';
                    testDiv.innerHTML = `
                        <h3>✅ ${displayName} (${tableName}) - 成功</h3>
                        <p><strong>總記錄數:</strong> ${data.total || records.length}</p>
                        <p><strong>返回記錄數:</strong> ${records.length}</p>
                        <p><strong>資料格式:</strong> ${data.records ? 'data.records' : data.items ? 'data.items' : '未知'}</p>
                        <details>
                            <summary>查看原始資料</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    testDiv.className = 'test-section error';
                    testDiv.innerHTML = `
                        <h3>❌ ${displayName} (${tableName}) - 失敗</h3>
                        <p><strong>錯誤:</strong> ${data.error || '未知錯誤'}</p>
                        <p><strong>狀態碼:</strong> ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                testDiv.className = 'test-section error';
                testDiv.innerHTML = `
                    <h3>❌ ${displayName} (${tableName}) - 網路錯誤</h3>
                    <p><strong>錯誤:</strong> ${error.message}</p>
                `;
            }
        }

        async function testAllAPIs() {
            const apis = [
                ['education_levels', '學歷等級'],
                ['positions', '職位'],
                ['leave_types', '假別類型'],
                ['salary_grades', '薪資等級'],
                ['work_locations', '工作地點'],
                ['skills', '技能'],
                ['clock_status_types', '打卡狀態類型'],
                ['departments', '部門'],
                ['shifts', '班別']
            ];

            document.getElementById('results').innerHTML = '';

            for (const [tableName, displayName] of apis) {
                await testAPI(tableName, displayName);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延遲500ms
            }
        }

        // 頁面載入時自動測試假別類型
        document.addEventListener('DOMContentLoaded', function() {
            testAPI('leave_types', '假別類型');
        });
    </script>
</body>

</html>