/* 
智慧考勤系統 - Pinterest風格現代設計
完全重新設計，採用Pinterest的簡潔美學
*/

/* ============ 重置和基礎樣式 ============ */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: #f7f7f7;
  color: #333;
  line-height: 1.6;
  overflow-x: hidden;
}

/* ============ Pinterest風格登入頁面 ============ */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  max-width: 420px;
  width: 90%;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}

.logo {
  text-align: center;
  margin-bottom: 2rem;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 24px;
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-text {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.logo-subtitle {
  color: #718096;
  font-size: 0.9rem;
}

/* ============ Pinterest風格表單 ============ */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-control {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f9fafb;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-primary {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* ============ Pinterest風格儀表板 ============ */
.dashboard-container {
  min-height: 100vh;
  background: #f7f7f7;
}

.dashboard-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #e2e8f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* ============ 用戶選單下拉 ============ */
.user-menu {
  position: relative;
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  padding: 1rem 0;
  display: none;
  z-index: 1000;
  border: 1px solid #e2e8f0;
}

.user-menu-dropdown a {
  display: block;
  padding: 0.75rem 1.5rem;
  color: #4a5568;
  text-decoration: none;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.user-menu-dropdown a:last-child {
  border-bottom: none;
}

.user-menu-dropdown a:hover {
  background: #f8fafc;
  color: #667eea;
  transform: translateX(5px);
}

/* ============ Pinterest風格側邊欄 ============ */
.sidebar {
  position: fixed;
  left: 0;
  top: 80px;
  width: 280px;
  height: calc(100vh - 80px);
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.08);
  padding: 2rem 0;
  overflow-y: auto;
  z-index: 50;
}

.sidebar-nav {
  list-style: none;
}

.nav-item {
  margin: 0.5rem 1.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  color: #4a5568;
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.nav-link:hover, .nav-link.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.menu-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  margin: 0.5rem 1rem;
}

.menu-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.menu-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  transform: translateX(3px);
}

.nav-icon {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* ============ Pinterest風格主內容區 ============ */
.main-content {
  margin-left: 280px;
  padding: 2rem;
  max-width: calc(100vw - 280px);
}

/* ============ Pinterest風格卡片 ============ */
.card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f1f5f9;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.card-subtitle {
  color: #718096;
  font-size: 0.9rem;
}

/* ============ Pinterest風格統計卡片 ============ */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 1rem;
}

.stat-icon.employees { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.attendance { background: linear-gradient(135deg, #48bb78, #38a169); }
.stat-icon.departments { background: linear-gradient(135deg, #ed8936, #dd6b20); }
.stat-icon.reports { background: linear-gradient(135deg, #9f7aea, #805ad5); }

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #718096;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ============ Pinterest風格考勤打卡區 ============ */
.attendance-clock {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  padding: 3rem;
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.attendance-clock::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.current-time {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.current-date {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.clock-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.clock-btn {
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.clock-btn:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* ============ Pinterest風格表格 ============ */
.table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: #2d3748;
}

.table tbody tr:hover {
  background: #f8fafc;
}

/* ============ Pinterest風格徽章 ============ */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  color: white;
}

.badge-error {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
}

/* ============ Pinterest風格按鈕 ============ */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #718096, #4a5568);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
}

.btn-outline {
  background: transparent;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ============ Pinterest風格模態框 ============ */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.modal-body {
  padding: 1.5rem 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* ============ 響應式設計 ============ */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    max-width: 100vw;
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .clock-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .current-time {
    font-size: 2rem;
  }
}

/* ============ 動畫和過渡效果 ============ */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from { transform: translateX(-30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* ============ 載入動畫 ============ */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============ 深色主題 ============ */
.dark-theme {
  background: #1a202c;
  color: #e2e8f0;
}

.dark-theme .dashboard-header {
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.dark-theme .sidebar {
  background: #2d3748;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .card {
  background: #2d3748;
  border: 1px solid #4a5568;
}

.dark-theme .stat-card {
  background: #2d3748;
  border: 1px solid #4a5568;
}

.dark-theme .table-container {
  background: #2d3748;
}

.dark-theme .table th {
  background: #374151;
  color: #e2e8f0;
  border-bottom: 1px solid #4a5568;
}

.dark-theme .table td {
  border-bottom: 1px solid #374151;
  color: #e2e8f0;
}

.dark-theme .form-control {
  background: #374151;
  border: 2px solid #4a5568;
  color: #e2e8f0;
}

.dark-theme .form-control:focus {
  background: #4a5568;
  border-color: #667eea;
}

.dark-theme .modal {
  background: #2d3748;
  color: #e2e8f0;
}

.dark-theme .user-menu-dropdown {
  background: #2d3748;
  border: 1px solid #4a5568;
}

.dark-theme .user-menu-dropdown a {
  color: #e2e8f0;
  border-bottom: 1px solid #4a5568;
}

.dark-theme .user-menu-dropdown a:hover {
  background: #374151;
}