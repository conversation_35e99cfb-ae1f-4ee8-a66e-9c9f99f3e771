"""
考勤處理服務模組。

此模組負責：
- 跨日考勤邏輯處理
- 打卡記錄的智能分析
- 上下班時間的自動判斷
- 加班時數計算
"""

import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from database import create_connection


class AttendanceProcessor:
    """
    考勤處理器類別。
    
    負責處理複雜的考勤邏輯，包括跨日考勤、打卡記錄分析等。
    """
    
    def __init__(self):
        """
        初始化考勤處理器。
        """
        self.logger = logging.getLogger(__name__)
        self.day_change_time = None
        self.cross_day_enabled = True
        self.first_punch_as_checkin = True
        self.last_punch_as_checkout = True
        self._load_settings()
    
    def _load_settings(self):
        """
        載入系統設定。
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 載入跨日考勤相關設定
            cursor.execute("""
                SELECT rule_type, rule_value 
                FROM schedule_rules 
                WHERE rule_type IN (
                    'day_change_time', 
                    'cross_day_attendance', 
                    'first_punch_as_checkin', 
                    'last_punch_as_checkout'
                )
            """)
            
            settings = dict(cursor.fetchall())
            
            # 設定換日時間
            day_change_str = settings.get('day_change_time', '06:00')
            self.day_change_time = datetime.strptime(day_change_str, '%H:%M').time()
            
            # 設定其他選項
            self.cross_day_enabled = settings.get('cross_day_attendance', '1') == '1'
            self.first_punch_as_checkin = settings.get('first_punch_as_checkin', '1') == '1'
            self.last_punch_as_checkout = settings.get('last_punch_as_checkout', '1') == '1'
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"載入考勤設定失敗: {e}")
            # 使用預設值
            self.day_change_time = time(6, 0)  # 早上6點
            self.cross_day_enabled = True
            self.first_punch_as_checkin = True
            self.last_punch_as_checkout = True
    
    def get_work_date(self, punch_datetime: datetime) -> datetime:
        """
        根據打卡時間和換日設定，確定工作日期。
        
        參數：
        punch_datetime (datetime): 打卡時間
        
        返回：
        datetime: 對應的工作日期
        """
        if not self.cross_day_enabled:
            return punch_datetime.date()
        
        punch_time = punch_datetime.time()
        
        # 如果打卡時間早於換日時間，歸屬於前一日
        if punch_time < self.day_change_time:
            work_date = punch_datetime.date() - timedelta(days=1)
        else:
            work_date = punch_datetime.date()
        
        return work_date
    
    def process_punch_record(self, employee_id: int, punch_datetime: datetime, 
                           device_id: str = None, note: str = None) -> Dict[str, Any]:
        """
        處理打卡記錄。
        
        參數：
        employee_id (int): 員工ID
        punch_datetime (datetime): 打卡時間
        device_id (str): 設備ID
        note (str): 備註
        
        返回：
        dict: 處理結果
        """
        try:
            # 確定工作日期
            work_date = self.get_work_date(punch_datetime)
            
            # 獲取當日現有打卡記錄
            existing_records = self._get_daily_punch_records(employee_id, work_date)
            
            # 判斷打卡類型（上班/下班）
            punch_type = self._determine_punch_type(existing_records, punch_datetime)
            
            # 更新或創建考勤記錄
            result = self._update_attendance_record(
                employee_id, work_date, punch_datetime, punch_type, device_id, note
            )
            
            # 計算工作時數和加班
            if punch_type == 'check_out':
                overtime_result = self._calculate_work_hours(employee_id, work_date)
                result.update(overtime_result)
            
            return {
                'success': True,
                'work_date': work_date.isoformat(),
                'punch_type': punch_type,
                'punch_time': punch_datetime.isoformat(),
                **result
            }
            
        except Exception as e:
            self.logger.error(f"處理打卡記錄失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_daily_punch_records(self, employee_id: int, work_date: datetime) -> List[Dict]:
        """
        獲取指定日期的所有打卡記錄。
        
        參數：
        employee_id (int): 員工ID
        work_date (datetime): 工作日期
        
        返回：
        list: 打卡記錄列表
        """
        conn = create_connection()
        cursor = conn.cursor()
        
        # 計算查詢時間範圍
        if self.cross_day_enabled:
            # 從前一日的換日時間開始，到當日的換日時間結束
            start_datetime = datetime.combine(work_date, self.day_change_time)
            end_datetime = datetime.combine(work_date + timedelta(days=1), self.day_change_time)
        else:
            # 只查詢當日的記錄
            start_datetime = datetime.combine(work_date, time.min)
            end_datetime = datetime.combine(work_date, time.max)
        
        cursor.execute("""
            SELECT check_in, check_out, status, note
            FROM attendance 
            WHERE employee_id = ? 
            AND ((check_in BETWEEN ? AND ?) OR (check_out BETWEEN ? AND ?))
            ORDER BY check_in
        """, (employee_id, start_datetime, end_datetime, start_datetime, end_datetime))
        
        records = []
        for row in cursor.fetchall():
            records.append({
                'check_in': row[0],
                'check_out': row[1],
                'status': row[2],
                'note': row[3]
            })
        
        conn.close()
        return records
    
    def _determine_punch_type(self, existing_records: List[Dict], 
                            punch_datetime: datetime) -> str:
        """
        判斷打卡類型（上班或下班）。
        
        參數：
        existing_records (list): 現有打卡記錄
        punch_datetime (datetime): 當前打卡時間
        
        返回：
        str: 'check_in' 或 'check_out'
        """
        if not existing_records:
            # 沒有記錄，第一筆為上班
            return 'check_in'
        
        # 找到最後一筆記錄
        last_record = existing_records[-1]
        
        if self.first_punch_as_checkin and self.last_punch_as_checkout:
            # 如果最後一筆記錄沒有下班時間，這次打卡為下班
            if last_record['check_out'] is None:
                return 'check_out'
            else:
                # 最後一筆已經有下班時間，這次為新的上班
                return 'check_in'
        
        # 其他邏輯可以根據需要擴展
        return 'check_in'
    
    def _update_attendance_record(self, employee_id: int, work_date: datetime, 
                                punch_datetime: datetime, punch_type: str,
                                device_id: str = None, note: str = None) -> Dict[str, Any]:
        """
        更新考勤記錄。
        
        參數：
        employee_id (int): 員工ID
        work_date (datetime): 工作日期
        punch_datetime (datetime): 打卡時間
        punch_type (str): 打卡類型
        device_id (str): 設備ID
        note (str): 備註
        
        返回：
        dict: 更新結果
        """
        conn = create_connection()
        cursor = conn.cursor()
        
        try:
            if punch_type == 'check_in':
                # 上班打卡
                cursor.execute("""
                    INSERT INTO attendance (employee_id, check_in, status, device_id, note, work_date)
                    VALUES (?, ?, 'normal', ?, ?, ?)
                """, (employee_id, punch_datetime, device_id, note, work_date))
                
                attendance_id = cursor.lastrowid
                
            else:  # check_out
                # 下班打卡 - 更新最後一筆沒有下班時間的記錄
                cursor.execute("""
                    UPDATE attendance 
                    SET check_out = ?, device_id = COALESCE(?, device_id), 
                        note = CASE WHEN ? IS NOT NULL THEN COALESCE(note, '') || ' | ' || ? ELSE note END,
                        work_date = COALESCE(work_date, ?)
                    WHERE employee_id = ? 
                    AND check_out IS NULL 
                    AND (work_date = ? OR work_date IS NULL OR DATE(check_in) = ?)
                    ORDER BY check_in DESC 
                    LIMIT 1
                """, (punch_datetime, device_id, note, note, work_date, employee_id, work_date, work_date))
                
                if cursor.rowcount == 0:
                    # 沒有找到對應的上班記錄，創建新記錄
                    cursor.execute("""
                        INSERT INTO attendance (employee_id, check_out, status, device_id, note, work_date)
                        VALUES (?, ?, 'manual', ?, ?, ?)
                    """, (employee_id, punch_datetime, device_id, f"補登下班時間: {note or ''}", work_date))
                    
                    attendance_id = cursor.lastrowid
                else:
                    # 獲取更新的記錄ID
                    cursor.execute("""
                        SELECT id FROM attendance 
                        WHERE employee_id = ? AND check_out = ?
                        ORDER BY id DESC LIMIT 1
                    """, (employee_id, punch_datetime))
                    
                    result = cursor.fetchone()
                    attendance_id = result[0] if result else None
            
            conn.commit()
            
            return {
                'attendance_id': attendance_id,
                'message': f"{'上班' if punch_type == 'check_in' else '下班'}打卡成功"
            }
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def _calculate_work_hours(self, employee_id: int, work_date: datetime) -> Dict[str, Any]:
        """
        計算工作時數和加班時數。
        
        參數：
        employee_id (int): 員工ID
        work_date (datetime): 工作日期
        
        返回：
        dict: 計算結果
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 獲取當日考勤記錄
            cursor.execute("""
                SELECT check_in, check_out 
                FROM attendance 
                WHERE employee_id = ? AND DATE(check_in) = ?
                AND check_in IS NOT NULL AND check_out IS NOT NULL
                ORDER BY check_in
            """, (employee_id, work_date))
            
            records = cursor.fetchall()
            
            if not records:
                return {'work_hours': 0, 'overtime_hours': 0}
            
            # 計算總工作時間
            total_work_minutes = 0
            for check_in_str, check_out_str in records:
                check_in = datetime.fromisoformat(check_in_str)
                check_out = datetime.fromisoformat(check_out_str)
                
                work_duration = check_out - check_in
                total_work_minutes += work_duration.total_seconds() / 60
            
            # 獲取員工的班別資訊（如果有排班）
            cursor.execute("""
                SELECT s.start_time, s.end_time, s.break_duration_minutes,
                       s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes,
                       s.enable_pre_overtime, s.enable_post_overtime
                FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                WHERE sc.employee_id = ? AND sc.shift_date = ?
            """, (employee_id, work_date))
            
            shift_info = cursor.fetchone()
            
            # 如果沒有班別信息，使用預設值
            if not shift_info:
                conn.close()
                return {'work_hours': round(total_work_minutes / 60, 2), 'overtime_hours': 0}
            
            # 這裡可以根據班別信息計算更精確的加班時數
            # 目前簡化處理
            work_hours = total_work_minutes / 60
            overtime_hours = max(0, work_hours - 8)  # 假設標準工作時間為8小時
            
            conn.close()
            return {
                'work_hours': round(work_hours, 2),
                'overtime_hours': round(overtime_hours, 2)
            }
            
        except Exception as e:
            self.logger.error(f"計算工作時數失敗: {e}")
            if 'conn' in locals():
                conn.close()
            return {'work_hours': 0, 'overtime_hours': 0}

    def get_daily_summary(self, employee_id: int, work_date: datetime) -> Dict[str, Any]:
        """
        獲取員工指定日期的考勤摘要。
        
        參數：
        employee_id (int): 員工ID
        work_date (datetime): 工作日期
        
        返回：
        dict: 考勤摘要
        """
        records = self._get_daily_punch_records(employee_id, work_date)
        work_hours_info = self._calculate_work_hours(employee_id, work_date)
        
        return {
            'work_date': work_date.isoformat(),
            'employee_id': employee_id,
            'punch_records': records,
            **work_hours_info
        }

# 全域考勤處理器實例
attendance_processor = AttendanceProcessor() 