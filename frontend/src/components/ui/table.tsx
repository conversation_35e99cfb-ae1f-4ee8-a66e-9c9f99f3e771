import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronUp, ChevronDown } from "lucide-react"
import type { TableColumn, ActionButton } from "@/types"

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn[]
  actions?: ActionButton[]
  loading?: boolean
  sortable?: boolean
  onSort?: (key: string, direction: 'asc' | 'desc') => void
  className?: string
  emptyMessage?: string
}

/**
 * 表格組件 - 遷移自 Han AttendanceOS 設計系統
 * 
 * 功能特色：
 * - 響應式設計
 * - 排序功能
 * - 操作按鈕支援
 * - 載入狀態
 * - 空狀態處理
 * - 手機端優化
 */
export function Table<T extends Record<string, any>>({
  data,
  columns,
  actions = [],
  loading = false,
  sortable = false,
  onSort,
  className,
  emptyMessage = "暫無資料"
}: TableProps<T>) {
  const [sortConfig, setSortConfig] = React.useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)

  const handleSort = (key: string) => {
    if (!sortable || !onSort) return

    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig?.key === key && sortConfig.direction === 'asc') {
      direction = 'desc'
    }

    setSortConfig({ key, direction })
    onSort(key, direction)
  }

  const getSortIcon = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) {
      return null
    }
    return sortConfig.direction === 'asc' ? (
      <ChevronUp className="w-4 h-4" />
    ) : (
      <ChevronDown className="w-4 h-4" />
    )
  }

  if (loading) {
    return (
      <div className={cn("bg-white rounded-2xl shadow-soft overflow-hidden", className)}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className={cn("bg-white rounded-2xl shadow-soft overflow-hidden", className)}>
        <div className="text-center py-12">
          <p className="text-neutral-600">{emptyMessage}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("bg-white rounded-2xl shadow-soft overflow-hidden", className)}>
      {/* 桌面版表格 */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-neutral-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider",
                    column.sortable && sortable && "cursor-pointer hover:bg-neutral-100 transition-colors",
                    column.width && `w-${column.width}`,
                    column.align === 'center' && "text-center",
                    column.align === 'right' && "text-right"
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && sortable && getSortIcon(column.key)}
                  </div>
                </th>
              ))}
              {actions.length > 0 && (
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-200">
            {data.map((row, index) => (
              <tr key={index} className="hover:bg-neutral-50 transition-colors">
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className={cn(
                      "px-6 py-4 whitespace-nowrap text-sm text-neutral-900",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right"
                    )}
                  >
                    {column.render ? column.render(row[column.key], row) : row[column.key] || '-'}
                  </td>
                ))}
                {actions.length > 0 && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex items-center space-x-2">
                      {actions.map((action) => (
                        <button
                          key={action.key}
                          onClick={() => action.onClick(row)}
                          disabled={action.disabled?.(row)}
                          className={cn(
                            "inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium transition-colors",
                            "hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed",
                            {
                              'bg-primary-100 text-primary-700 hover:bg-primary-200': action.variant === 'primary',
                              'bg-secondary-100 text-secondary-700 hover:bg-secondary-200': action.variant === 'secondary',
                              'bg-success-100 text-success-700 hover:bg-success-200': action.variant === 'success',
                              'bg-warning-100 text-warning-700 hover:bg-warning-200': action.variant === 'warning',
                              'bg-error-100 text-error-700 hover:bg-error-200': action.variant === 'error',
                              'bg-neutral-100 text-neutral-700 hover:bg-neutral-200': !action.variant
                            }
                          )}
                        >
                          {action.icon && <span className="mr-1">{action.icon}</span>}
                          {action.label}
                        </button>
                      ))}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 手機版卡片 */}
      <div className="md:hidden space-y-4 p-4">
        {data.map((row, index) => (
          <div key={index} className="bg-neutral-50 rounded-xl p-4 space-y-3">
            {columns.map((column) => (
              <div key={column.key} className="flex justify-between">
                <span className="text-sm font-medium text-neutral-600">
                  {column.title}:
                </span>
                <span className="text-sm text-neutral-900">
                  {column.render ? column.render(row[column.key], row) : row[column.key] || '-'}
                </span>
              </div>
            ))}
            
            {actions.length > 0 && (
              <div className="pt-3 border-t border-neutral-200">
                <div className="flex items-center space-x-2 flex-wrap">
                  {actions.map((action) => (
                    <button
                      key={action.key}
                      onClick={() => action.onClick(row)}
                      disabled={action.disabled?.(row)}
                      className={cn(
                        "inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium transition-colors",
                        "hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed",
                        {
                          'bg-primary-100 text-primary-700 hover:bg-primary-200': action.variant === 'primary',
                          'bg-secondary-100 text-secondary-700 hover:bg-secondary-200': action.variant === 'secondary',
                          'bg-success-100 text-success-700 hover:bg-success-200': action.variant === 'success',
                          'bg-warning-100 text-warning-700 hover:bg-warning-200': action.variant === 'warning',
                          'bg-error-100 text-error-700 hover:bg-error-200': action.variant === 'error',
                          'bg-neutral-100 text-neutral-700 hover:bg-neutral-200': !action.variant
                        }
                      )}
                    >
                      {action.icon && <span className="mr-1">{action.icon}</span>}
                      {action.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}