"""
考勤相關API模組

包含所有考勤相關的API端點：
- 打卡相關API
- 考勤記錄查詢
- 考勤管理功能
- 考勤處理和生成

重要概念說明：
1. attendance = 考勤記錄表（實際資料表）: 
   - 包含基本考勤欄位：employee_id, check_in, check_out, status, device_id, note, created_at
   - 存放所有員工的考勤記錄
   - 這是系統的主要考勤資料表

注意：系統使用單一的attendance表存放所有考勤記錄。
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, time, timedelta
import logging
import sqlite3

# 創建藍圖
attendance_bp = Blueprint('attendance', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數和考勤處理器
from database import create_connection
from services.attendance_processor import attendance_processor

@attendance_bp.route("/api/attendance/clock-in", methods=["POST"])
def clock_in():
    """
    智能打卡API
    
    支援跨日考勤邏輯，自動判斷上班/下班，處理換日時間。
    
    請求體：
    - employee_id: 員工ID（必填）
    - device_id: 設備ID
    - note: 備註
    
    返回：
    - 打卡結果和相關資訊
    """
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少員工ID"}), 400
    
    try:
        employee_id = data["employee_id"]
        punch_datetime = datetime.now()
        device_id = data.get("device_id")
        note = data.get("note")
        
        # 使用考勤處理器處理打卡
        result = attendance_processor.process_punch_record(
            employee_id=employee_id,
            punch_datetime=punch_datetime,
            device_id=device_id,
            note=note
        )
        
        if result['success']:
            # 獲取員工資訊
            conn = create_connection()
            conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
            employee = cursor.fetchone()
            conn.close()
            
            employee_name = employee['name'] if employee else "未知員工"
            
            logger.info(f"員工 {employee_name} (ID: {employee_id}) 打卡成功 - {result['punch_type']}")
            
            return jsonify({
                "message": result.get('message', '打卡成功'),
                "employee_name": employee_name,
                "punch_type": result['punch_type'],
                "work_date": result['work_date'],
                "punch_time": result['punch_time'],
                "attendance_id": result.get('attendance_id'),
                **{k: v for k, v in result.items() if k not in ['success', 'message', 'punch_type', 'work_date', 'punch_time', 'attendance_id']}
            })
        else:
            return jsonify({"error": result.get('error', '打卡失敗')}), 500
            
    except Exception as e:
        logger.error(f"打卡API錯誤: {str(e)}")
        return jsonify({"error": "系統錯誤，請稍後再試"}), 500


@attendance_bp.route("/api/attendance/clock-out", methods=["POST"])
def clock_out():
    """
    下班打卡API（已整合到智能打卡中）
    
    此API保留向後相容性，實際使用智能打卡邏輯。
    """
    # 直接調用智能打卡API
    return clock_in()


@attendance_bp.route("/api/attendance/recent", methods=["GET"])
def get_recent_attendance():
    """
    獲取最近的考勤記錄
    
    查詢參數：
    - limit: 記錄數量限制（預設5）
    
    返回：
    - 最近的考勤記錄列表
    """
    try:
        limit = request.args.get('limit', 5, type=int)
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status,
                   d.name as department_name
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            ORDER BY a.check_in DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            records.append({
                "id": row['id'],
                "name": row['name'],
                "employee_id": row['employee_id'],
                "check_in": row['check_in'],
                "check_out": row['check_out'],
                "status": row['status'],
                "department_name": row['department_name']
            })
        
        conn.close()
        return jsonify({"recent_attendance": records})
        
    except Exception as e:
        logger.error(f"獲取最近考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@attendance_bp.route("/api/attendance/daily-summary/<int:employee_id>", methods=["GET"])
def get_daily_attendance_summary(employee_id):
    """
    獲取員工指定日期的考勤摘要
    
    支援跨日考勤邏輯，根據換日時間計算工作日期。
    
    參數：
    employee_id (int): 員工ID
    
    查詢參數：
    - date: 查詢日期（格式：YYYY-MM-DD，預設為今天）
    
    返回：
    - 員工考勤摘要資訊
    """
    try:
        # 獲取查詢日期，預設為今天
        date_str = request.args.get('date')
        if date_str:
            work_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            # 根據當前時間和換日設定確定工作日期
            work_date = attendance_processor.get_work_date(datetime.now())
        
        # 獲取考勤摘要
        summary = attendance_processor.get_daily_summary(employee_id, work_date)
        
        return jsonify(summary)
        
    except Exception as e:
        logger.error(f"獲取考勤摘要失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@attendance_bp.route("/api/attendance/work-date", methods=["GET"])
def get_work_date():
    """
    獲取當前工作日期
    
    根據換日時間設定計算當前的工作日期。
    
    返回：
    - 當前工作日期和相關設定
    """
    try:
        # 獲取查詢時間，預設為當前時間
        query_time_str = request.args.get('time')
        if query_time_str:
            query_time = datetime.fromisoformat(query_time_str)
        else:
            query_time = datetime.now()
        
        # 使用考勤處理器計算工作日期
        work_date = attendance_processor.get_work_date(query_time)
        
        return jsonify({
            "work_date": work_date.isoformat(),
            "query_time": query_time.isoformat(),
            "day_change_time": attendance_processor.day_change_time.strftime('%H:%M'),
            "cross_day_enabled": attendance_processor.cross_day_enabled
        })
        
    except Exception as e:
        logger.error(f"獲取工作日期失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@attendance_bp.route("/api/attendance/management/update-shift", methods=["POST"])
def update_attendance_shift():
    """
    更新考勤記錄的班表並重新計算
    
    請求體：
    {
        "attendance_id": 考勤記錄ID,
        "shift_id": 新的班別ID
    }
    
    返回：
    - 更新結果和重新計算的考勤記錄
    """
    try:
        data = request.get_json()
        attendance_id = data.get('attendance_id')
        shift_id = data.get('shift_id')
        
        if not attendance_id or not shift_id:
            return jsonify({"error": "請提供考勤記錄ID和班別ID"}), 400
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        # 獲取考勤記錄資訊
        cursor.execute("""
            SELECT ar.*, e.name as employee_name, e.employee_id
            FROM attendance ar
            JOIN employees e ON ar.employee_id = e.id
            WHERE ar.id = ?
            """, (attendance_id,))
        attendance_row = cursor.fetchone()
        
        if not attendance_row:
            return jsonify({"error": "考勤記錄不存在"}), 404
        
        # 轉換為字典
        attendance = dict(attendance_row)
        
        # 如果work_date為空，從check_in或check_out中提取日期
        work_date = attendance['work_date']
        if not work_date:
            if attendance['check_in']:
                work_date = attendance['check_in'].split()[0]  # 取日期部分
            elif attendance['check_out']:
                work_date = attendance['check_out'].split()[0]
            elif attendance['clock_in_time']:
                work_date = attendance['clock_in_time'].split()[0]
            elif attendance['clock_out_time']:
                work_date = attendance['clock_out_time'].split()[0]
            
            if work_date:
                # 更新attendance記錄的work_date
                cursor.execute("UPDATE attendance SET work_date = ? WHERE id = ?", 
                             (work_date, attendance_id))
                attendance['work_date'] = work_date
        
        # 檢查班別是否存在
        cursor.execute("SELECT id, name, start_time, end_time FROM shifts WHERE id = ?", (shift_id,))
        shift_row = cursor.fetchone()
        if not shift_row:
            return jsonify({"error": "班別不存在"}), 404
        
        # 轉換為字典
        shift = dict(shift_row)
        
        # 更新或創建排班記錄
        cursor.execute("""
            INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_id, status, updated_at)
            VALUES (?, ?, ?, 'scheduled', CURRENT_TIMESTAMP)
        """, (attendance['employee_id'], attendance['work_date'], shift_id))
        
        # 重新計算考勤記錄
        recalculated = recalculate_single_attendance(attendance, shift, cursor)
        
        conn.commit()
        conn.close()
        
        logging.info(f"成功更新考勤記錄 {attendance_id} 的班表為 {shift['name']} (ID: {shift_id})")
        
        return jsonify({
            "success": True,
            "message": f"成功將 {attendance['employee_name']} 在 {attendance['work_date']} 的班表修改為 {shift['name']}",
            "employee_name": attendance['employee_name'],
            "employee_id": attendance['employee_id'],
            "work_date": attendance['work_date'],
            "shift_name": shift['name'],
            "recalculated_data": recalculated
        })
        
    except Exception as e:
        logging.error(f"更新考勤班表失敗: {str(e)}")
        return jsonify({"error": f"更新考勤班表失敗: {str(e)}"}), 500


def recalculate_single_attendance(attendance, shift, cursor):
    """
    重新計算單一考勤記錄
    
    參數：
    - attendance: 考勤記錄
    - shift: 班別資訊
    - cursor: 資料庫游標
    
    返回：
    - 重新計算後的考勤數據
    """
    try:
        from datetime import datetime, time
        
        # 解析班別時間
        shift_start = datetime.strptime(shift['start_time'], "%H:%M").time()
        shift_end = datetime.strptime(shift['end_time'], "%H:%M").time()
        
        # 解析實際打卡時間 - 優先使用修改後的時間
        actual_check_in = None
        actual_check_out = None
        
        # 檢查上班時間（優先使用clock_in_time，然後check_in）
        if attendance['clock_in_time']:
            try:
                actual_check_in = datetime.fromisoformat(attendance['clock_in_time'].replace('Z', '+00:00')).time()
            except:
                actual_check_in = datetime.strptime(attendance['clock_in_time'], '%Y-%m-%d %H:%M:%S').time()
        elif attendance['check_in']:
            try:
                actual_check_in = datetime.fromisoformat(attendance['check_in'].replace('Z', '+00:00')).time()
            except:
                actual_check_in = datetime.strptime(attendance['check_in'], '%Y-%m-%d %H:%M:%S').time()
            
        # 檢查下班時間（優先使用clock_out_time，然後check_out）
        if attendance['clock_out_time']:
            try:
                actual_check_out = datetime.fromisoformat(attendance['clock_out_time'].replace('Z', '+00:00')).time()
            except:
                actual_check_out = datetime.strptime(attendance['clock_out_time'], '%Y-%m-%d %H:%M:%S').time()
        elif attendance['check_out']:
            try:
                actual_check_out = datetime.fromisoformat(attendance['check_out'].replace('Z', '+00:00')).time()
            except:
                actual_check_out = datetime.strptime(attendance['check_out'], '%Y-%m-%d %H:%M:%S').time()
        
        # 計算遲到、早退、加班時間
        late_minutes = 0
        early_leave_minutes = 0
        overtime_minutes = 0
        work_hours = 0
        status = "normal"  # 預設為正常
        
        # 判斷考勤狀態
        if actual_check_in and actual_check_out:
            # 有完整的上下班時間 - 計算各種狀況
            
            # 計算遲到時間
            if actual_check_in > shift_start:
                late_delta = datetime.combine(datetime.today(), actual_check_in) - datetime.combine(datetime.today(), shift_start)
                late_minutes = int(late_delta.total_seconds() / 60)
                status = "late"
            
            # 計算早退時間
            if actual_check_out < shift_end:
                early_delta = datetime.combine(datetime.today(), shift_end) - datetime.combine(datetime.today(), actual_check_out)
                early_leave_minutes = int(early_delta.total_seconds() / 60)
                if status == "normal":
                    status = "early_leave"
                elif status == "late":
                    status = "late_and_early_leave"  # 既遲到又早退
            
            # 計算工作時數
            work_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), actual_check_in)
            work_hours = work_delta.total_seconds() / 3600
            
            # 計算加班時間（下班後）
            if actual_check_out > shift_end:
                overtime_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), shift_end)
                overtime_minutes = int(overtime_delta.total_seconds() / 60)
                # 如果有加班但沒有遲到或早退，狀態可能是加班
                if status == "normal":
                    status = "overtime"
            
            # 如果沒有遲到、早退或加班，狀態就是正常
            if late_minutes == 0 and early_leave_minutes == 0:
                status = "normal"
                
        elif actual_check_in and not actual_check_out:
            # 只有上班時間，沒有下班時間
            status = "incomplete"
            
            # 計算遲到時間
            if actual_check_in > shift_start:
                late_delta = datetime.combine(datetime.today(), actual_check_in) - datetime.combine(datetime.today(), shift_start)
                late_minutes = int(late_delta.total_seconds() / 60)
                
        elif not actual_check_in and actual_check_out:
            # 只有下班時間，沒有上班時間
            status = "incomplete"
            
            # 計算早退時間
            if actual_check_out < shift_end:
                early_delta = datetime.combine(datetime.today(), shift_end) - datetime.combine(datetime.today(), actual_check_out)
                early_leave_minutes = int(early_delta.total_seconds() / 60)
                
        else:
            # 沒有任何打卡時間
            status = "absent"
        
        # 更新考勤記錄
        cursor.execute("""
            UPDATE attendance 
            SET shift_id = ?, 
                status = ?, 
                late_minutes = ?, 
                early_leave_minutes = ?, 
                overtime_minutes = ?,
                work_hours = ?,
                overtime_hours = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
        """, (shift['id'], status, late_minutes, early_leave_minutes, 
              overtime_minutes, work_hours, overtime_minutes / 60.0, attendance['id']))
        
        logging.info(f"重新計算考勤記錄 - ID: {attendance['id']}, 原狀態: {attendance.get('status', 'unknown')}, 新狀態: {status}, 遲到: {late_minutes}分鐘, 早退: {early_leave_minutes}分鐘, 加班: {overtime_minutes}分鐘, 工時: {round(work_hours, 2)}小時")
        
        return {
            "status": status,
            "late_minutes": late_minutes,
            "early_leave_minutes": early_leave_minutes,
            "overtime_minutes": overtime_minutes,
            "work_hours": round(work_hours, 2),
            "shift_start_time": shift['start_time'],
            "shift_end_time": shift['end_time'],
            "actual_check_in": attendance['clock_in_time'] or attendance['check_in'],
            "actual_check_out": attendance['clock_out_time'] or attendance['check_out']
        }
        
    except Exception as e:
        logging.error(f"重新計算考勤記錄失敗: {str(e)}")
        return None


@attendance_bp.route("/api/attendance/import", methods=["POST"])
def import_attendance():
    """
    匯入考勤資料（CSV 檔案）
    
    功能說明：
    - 支援CSV檔案格式的考勤資料匯入
    - 自動解析檔案內容並寫入資料庫
    
    請求：
    - file: CSV檔案（必填）
    
    返回：
    - 匯入結果統計
    """
    if "file" not in request.files:
        return jsonify({"error": "未找到檔案"}), 400
    
    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "未選擇檔案"}), 400

    try:
        import io
        import csv
        
        # 讀取 CSV 檔案
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_data = csv.DictReader(stream)
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        imported_count = 0
        for row in csv_data:
            cursor.execute(
                """
                INSERT INTO attendance (employee_id, check_in, status)
                VALUES (?, ?, ?)
            """,
                (row["employee_id"], row["clock_time"], row["status"]),
            )
            imported_count += 1
        
        conn.commit()
        conn.close()
        logger.info(f"成功匯入 {imported_count} 筆考勤記錄")
        
        return jsonify({"message": f"成功匯入 {imported_count} 筆記錄"})
    except Exception as e:
        logger.error(f"匯入考勤資料失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@attendance_bp.route("/api/attendance/manual", methods=["POST"])
def manual_attendance():
    """
    手動新增考勤記錄
    
    功能說明：
    - 允許管理員手動新增考勤記錄
    - 支援完整的考勤資訊設定
    
    請求體：
    - employee_id: 員工ID（必填）
    - check_in: 上班時間
    - check_out: 下班時間
    - status: 考勤狀態（預設：manual）
    - note: 備註
    
    返回：
    - 新增結果和記錄ID
    """
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少必要參數"}), 400
    
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        cursor.execute(
            """
            INSERT INTO attendance (employee_id, check_in, check_out, status, note, work_date)
            VALUES (?, ?, ?, ?, ?, ?)
        """,
            (
                data["employee_id"],
                data.get("check_in"),
                data.get("check_out"),
                data.get("status", "manual"),
                data.get("note", ""),
                data.get("work_date", datetime.now().strftime('%Y-%m-%d'))
            )
        )
        
        conn.commit()
        attendance_id = cursor.lastrowid
        
        logger.info(f"手動新增考勤記錄: 員工ID {data['employee_id']}, 記錄ID {attendance_id}")
        
        return jsonify({
            "message": "考勤記錄新增成功",
            "attendance_id": attendance_id
        })
        
    except sqlite3.Error as e:
        logger.error(f"手動新增考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@attendance_bp.route("/api/attendance/today/<int:employee_id>", methods=["GET"])
def get_today_attendance(employee_id):
    """
    獲取員工今日考勤記錄
    
    功能說明：
    - 查詢指定員工的今日考勤狀況
    - 包含打卡狀態和時間資訊
    
    參數：
    employee_id (int): 員工ID
    
    返回：
    - 員工今日考勤詳細資訊
    """
    try:
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 獲取員工資訊
        cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({"error": "員工不存在"}), 404
        
        # 獲取今日考勤記錄
        cursor.execute(
            """
            SELECT id, check_in, check_out, status, note
            FROM attendance
            WHERE employee_id = ? AND DATE(check_in) = ?
            ORDER BY check_in DESC
            LIMIT 1
        """,
            (employee_id, today)
        )
        
        attendance = cursor.fetchone()
        
        result = {
            "employee_id": employee['employee_id'],
            "employee_name": employee['name'],
            "date": today,
            "has_attendance": attendance is not None
        }
        
        if attendance:
            result.update({
                "attendance_id": attendance['id'],
                "check_in": attendance['check_in'],
                "check_out": attendance['check_out'],
                "status": attendance['status'],
                "note": attendance['note'],
                "has_checked_in": attendance['check_in'] is not None,
                "has_checked_out": attendance['check_out'] is not None
            })
        else:
            result.update({
                "attendance_id": None,
                "check_in": None,
                "check_out": None,
                "status": None,
                "note": None,
                "has_checked_in": False,
                "has_checked_out": False
            })
        
        return jsonify(result)
        
    except sqlite3.Error as e:
        logger.error(f"獲取今日考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@attendance_bp.route("/api/attendance/settings", methods=["GET", "POST"])
def manage_attendance_settings():
    """
    管理考勤設定
    
    功能說明：
    - 獲取和更新考勤相關設定
    - 包括換日時間、跨日考勤開關等設定
    
    GET: 獲取當前設定
    POST: 更新設定
    
    返回：
    - 考勤設定資訊
    """
    if request.method == "GET":
        try:
            conn = create_connection()
            conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
            cursor = conn.cursor()
            
            # 獲取考勤相關設定
            cursor.execute("""
                SELECT rule_type, rule_value, description 
                FROM schedule_rules 
                WHERE rule_type IN (
                    'day_change_time', 
                    'cross_day_attendance', 
                    'first_punch_as_checkin', 
                    'last_punch_as_checkout',
                    'late_tolerance_minutes',
                    'early_leave_tolerance_minutes'
                )
                ORDER BY rule_type
            """)
            
            settings = {}
            for row in cursor.fetchall():
                settings[row['rule_type']] = {
                    'value': row['rule_value'],
                    'description': row['description']
                }
            
            conn.close()
            return jsonify({"settings": settings})
            
        except Exception as e:
            logger.error(f"獲取考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500
    
    else:  # POST
        try:
            data = request.json
            if not data:
                return jsonify({"error": "缺少設定資料"}), 400
            
            conn = create_connection()
            conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
            cursor = conn.cursor()
            
            # 更新設定
            for rule_type, rule_value in data.items():
                cursor.execute("""
                    UPDATE schedule_rules 
                    SET rule_value = ? 
                    WHERE rule_type = ?
                """, (str(rule_value), rule_type))
                
                if cursor.rowcount == 0:
                    # 如果設定不存在，則插入新設定
                    cursor.execute("""
                        INSERT INTO schedule_rules (rule_type, rule_value, description)
                        VALUES (?, ?, ?)
                    """, (rule_type, str(rule_value), f"考勤設定: {rule_type}"))
            
            conn.commit()
            conn.close()
            
            # 重新載入考勤處理器設定
            attendance_processor._load_settings()
            
            logger.info(f"考勤設定更新成功: {data}")
            return jsonify({"message": "考勤設定更新成功"})
            
        except Exception as e:
            logger.error(f"更新考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500


@attendance_bp.route("/api/attendance/analysis", methods=["GET"])
def analyze_attendance():
    """
    出勤分析報表
    
    功能說明：
    - 提供考勤資料的統計分析
    - 支援多種篩選條件
    
    查詢參數：
    - start_date: 開始日期
    - end_date: 結束日期
    - department: 部門ID
    
    返回：
    - 考勤分析統計資料
    """
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    department = request.args.get("department")

    conn = create_connection()
    conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
    try:
        cursor = conn.cursor()
        
        # 基本查詢條件
        query_conditions = []
        params = []
        
        if start_date and end_date:
            query_conditions.append("DATE(a.check_in) BETWEEN ? AND ?")
            params.extend([start_date, end_date])
        
        if department:
            query_conditions.append("e.department_id = ?")
            params.append(department)
        
        where_clause = " AND ".join(query_conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 出勤統計查詢
        query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            {where_clause}
        """
        
        cursor.execute(query, params)
        stats = cursor.fetchone()
        
        # 部門統計
        dept_query = f"""
            SELECT 
                d.name as department_name,
                COUNT(*) as attendance_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN departments d ON e.department_id = d.id
            {where_clause}
            GROUP BY d.id, d.name
            ORDER BY attendance_count DESC
        """
        
        cursor.execute(dept_query, params)
        dept_stats = cursor.fetchall()
        
        result = {
            "summary": {
                "total_records": stats['total_records'],
                "normal_count": stats['normal_count'],
                "late_count": stats['late_count'],
                "early_leave_count": stats['early_leave_count'],
                "absent_count": stats['absent_count']
            },
            "department_stats": [
                {
                    "department_name": row['department_name'],
                    "attendance_count": row['attendance_count'],
                    "late_count": row['late_count']
                }
                for row in dept_stats
            ]
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"出勤分析失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@attendance_bp.route("/api/attendance/records", methods=["GET"])
def get_attendance_records():
    """
    查詢考勤記錄 API
    
    功能說明：
    - 支援分頁查詢考勤記錄（包含上下班時間、遲到早退、加班等計算欄位）
    - 支援多種篩選條件（員工、部門、日期範圍、狀態）
    - 返回統計資訊和詳細記錄列表
    
    查詢參數：
    - page: 頁碼 (預設: 1, 最小值: 1)
    - limit: 每頁筆數 (預設: 20, 範圍: 1-100)
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - status: 考勤狀態 (可選)
    
    返回：
    - 分頁的考勤記錄列表和統計資訊
    """
    try:
        logger.info("開始處理考勤記錄查詢請求")
        
        # 參數獲取與驗證
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        
        # 參數驗證
        if page < 1:
            logger.warning(f"無效的頁碼參數: {page}")
            return jsonify({"error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            logger.warning(f"無效的每頁筆數參數: {limit}")
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 日期格式驗證
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的開始日期格式: {start_date}")
                return jsonify({"error": "開始日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的結束日期格式: {end_date}")
                return jsonify({"error": "結束日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        # 狀態值驗證
        valid_statuses = ['normal', 'late', 'early_leave', 'absent', 'manual', 'overtime', 'incomplete']
        if status and status not in valid_statuses:
            logger.warning(f"無效的狀態參數: {status}")
            return jsonify({"error": f"狀態值必須是以下之一: {', '.join(valid_statuses)}"}), 400
        
        logger.info(f"查詢參數驗證通過 - page: {page}, limit: {limit}, employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, status: {status}")
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = []
        query_params = []
        
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        if start_date:
            where_conditions.append("DATE(COALESCE(a.check_in, a.check_out)) >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(COALESCE(a.check_in, a.check_out)) <= ?")
            query_params.append(end_date)
        
        if status:
            where_conditions.append("a.status = ?")
            query_params.append(status)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 查詢總記錄數 - 使用attendance表（考勤記錄）
        count_query = f"""
            SELECT COUNT(*) as total
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        logger.debug(f"執行總數查詢 SQL: {count_query}")
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()['total']
        logger.info(f"查詢到總記錄數: {total_records}")
        
        # 查詢詳細記錄 - 使用attendance表（考勤記錄）
        records_query = f"""
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.device_id,
                a.note,
                a.work_date,
                a.created_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                s.shift_date as schedule_date,
                COALESCE(sh.name, default_sh.name) as shift_name,
                COALESCE(sh.start_time, default_sh.start_time) as shift_start_time,
                COALESCE(sh.end_time, default_sh.end_time) as shift_end_time,
                COALESCE(sh.break_duration_minutes, default_sh.break_duration_minutes) as shift_break_duration,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                    THEN ROUND((strftime('%s', a.check_out) - strftime('%s', a.check_in)) / 3600.0, 2)
                    ELSE 0
                END as calculated_work_hours
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN schedules s ON a.employee_id = s.employee_id 
                AND DATE(COALESCE(a.check_in, a.check_out, a.work_date)) = DATE(s.shift_date)
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            LEFT JOIN shifts default_sh ON e.shift_type = default_sh.id
            {where_clause}
            ORDER BY a.created_at DESC, a.id DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            # 格式化時間欄位
            if record.get('check_in'):
                record['check_in_formatted'] = record['check_in']
            if record.get('check_out'):
                record['check_out_formatted'] = record['check_out']
            if record.get('created_at'):
                record['created_at_formatted'] = record['created_at']
            # 確保 work_date 欄位存在
            if not record.get('work_date') and (record.get('check_in') or record.get('check_out')):
                # 如果 work_date 為空，從 check_in 或 check_out 中提取日期
                date_source = record.get('check_in') or record.get('check_out')
                if date_source:
                    record['work_date'] = date_source.split()[0] if ' ' in date_source else date_source[:10]
            
            records.append(record)
        
        # 計算統計資訊 - 使用attendance表（考勤記錄）
        stats_query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT a.employee_id) as total_employees,
                COUNT(DISTINCT DATE(COALESCE(a.check_in, a.check_out))) as total_days,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count,
                COUNT(CASE WHEN a.status = 'incomplete' THEN 1 END) as incomplete_count
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        cursor.execute(stats_query, query_params)
        stats_row = cursor.fetchone()
        
        statistics = {
            "total_records": stats_row['total_records'],
            "total_employees": stats_row['total_employees'],
            "total_days": stats_row['total_days'],
            "avg_records_per_day": round(stats_row['total_records'] / max(stats_row['total_days'], 1), 1),
            "status_breakdown": {
                "normal": stats_row['normal_count'],
                "late": stats_row['late_count'],
                "early_leave": stats_row['early_leave_count'],
                "absent": stats_row['absent_count'],
                "manual": stats_row['manual_count'],
                "incomplete": stats_row['incomplete_count']
            }
        }
        
        conn.close()
        
        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit
        
        # 構建響應資料
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "statistics": statistics,
            "query_info": {
                "filters_applied": len(where_conditions),
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        logger.info(f"考勤記錄查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        error_msg = f"查詢考勤記錄時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/attendance/records/<int:record_id>", methods=["GET"])
def get_attendance_record_detail(record_id):
    """
    獲取單個考勤記錄詳情 API
    
    功能說明：
    - 根據記錄ID獲取完整的考勤記錄詳情
    - 包含員工資訊、部門資訊、班表資訊和所有相關欄位
    
    參數：
    record_id (int): 考勤記錄ID
    
    返回：
    - 完整的考勤記錄詳情
    """
    try:
        logger.info(f"開始查詢考勤記錄詳情 - record_id: {record_id}")
        
        # 參數驗證
        if record_id <= 0:
            logger.warning(f"無效的記錄ID: {record_id}")
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row  # 設定Row factory以返回字典式結果
        cursor = conn.cursor()
        
        # 查詢記錄詳情
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.work_date,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.note,
                a.created_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN schedules sc ON sc.employee_id = e.id AND sc.shift_date = a.work_date
            LEFT JOIN shifts s ON sc.shift_id = s.id
            WHERE a.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            logger.warning(f"找不到記錄ID為 {record_id} 的考勤記錄")
            return jsonify({
                "success": False,
                "error": "找不到指定的考勤記錄"
            }), 404
        
        # 轉換為字典格式
        record_dict = dict(record)
        
        # 計算加班時數（從分鐘轉換為小時）
        if record_dict['overtime_minutes']:
            record_dict['overtime_hours'] = round(record_dict['overtime_minutes'] / 60, 2)
        else:
            record_dict['overtime_hours'] = 0
        
        logger.info(f"成功查詢考勤記錄詳情 - record_id: {record_id}, employee: {record_dict['employee_name']}")
        
        return jsonify({
            "success": True,
            "record": record_dict
        })
        
    except Exception as e:
        logger.error(f"查詢考勤記錄詳情時發生錯誤: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"查詢考勤記錄詳情失敗: {str(e)}"
        }), 500
    
    finally:
        if conn:
            conn.close()


        
        # 移除重複的函數定義
        
        # ===== 步驟3: 處理打卡記錄，生成基礎考勤記錄 =====
        # 修正：從attendance表查詢打卡記錄，而不是punch_records表
        punch_records_query = f"""
            SELECT employee_id, punch_time, device_id, note, employee_name FROM (
                SELECT a.employee_id, a.check_in as punch_time, '' as device_id, a.note,
                       e.name as employee_name
                FROM attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE DATE(a.check_in) = ? AND a.check_in IS NOT NULL
                {department_filter.replace('e.department_id', 'e.department_id')}
                
                UNION ALL
                
                SELECT a.employee_id, a.check_out as punch_time, '' as device_id, a.note,
                       e.name as employee_name
                FROM attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE DATE(a.check_out) = ? AND a.check_out IS NOT NULL
                {department_filter.replace('e.department_id', 'e.department_id')}
            ) ORDER BY employee_id, punch_time
        """
        
        cursor.execute(punch_records_query, [target_date] + dept_params + [target_date] + dept_params)
        punch_records = cursor.fetchall()
        
        # 按員工分組打卡記錄
        employee_punches = {}
        for punch in punch_records:
            emp_id = punch['employee_id']
            if emp_id not in employee_punches:
                employee_punches[emp_id] = []
            employee_punches[emp_id].append(punch)
        
        logger.info(f"找到 {len(employee_punches)} 名員工有打卡記錄，共 {len(punch_records)} 筆打卡")
        
        # ===== 步驟4: 查詢請假記錄 =====
        # 簡化請假記錄查詢，只查詢當天有效的請假記錄
        leave_records_query = f"""
            SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason,
                   lr.start_date, lr.end_date, lr.leave_hours
            FROM leaves lr
            WHERE lr.status = 'approved'
            AND ? BETWEEN lr.start_date AND lr.end_date
            {department_filter.replace('e.department_id', '(SELECT department_id FROM employees WHERE id = lr.employee_id)')}
        """
        
        cursor.execute(leave_records_query, [target_date] + dept_params)
        leave_records_raw = cursor.fetchall()
        
        # 處理請假記錄 - 簡化邏輯，每天固定8小時
        leave_records = []
        for leave_record in leave_records_raw:
            employee_id, leave_type_name, reason, start_date, end_date, total_leave_hours = leave_record
            
            # 計算請假天數
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            total_days = (end_date_obj - start_date_obj).days + 1
            
            # 每天的請假時數計算
            if total_leave_hours and total_leave_hours > 0:
                # 如果有指定總時數，平均分配
                daily_leave_hours = total_leave_hours / total_days
            else:
                # 沒有指定時數，預設全天請假8小時
                daily_leave_hours = 8.0
            
            # 如果是跨多天請假但每天時數小於8小時，調整為8小時
            if total_days > 1 and daily_leave_hours < 8.0:
                daily_leave_hours = 8.0
            
            # 構建日期範圍說明
            if total_days > 1:
                date_range_note = f"{start_date} 至 {end_date}"
            else:
                date_range_note = start_date
            
            leave_records.append((
                employee_id, 
                daily_leave_hours, 
                leave_type_name, 
                reason,
                date_range_note
            ))
        
        # ===== 步驟5: 為每個在職員工生成考勤記錄 =====
        generated_count = 0
        updated_count = 0
        no_punch_count = 0
        leave_integrated_count = 0
        
        for employee in active_employees:
            emp_id = employee['id']
            emp_name = employee['name']
            emp_code = employee['employee_id']
            
            # 檢查是否已存在考勤記錄
            cursor.execute("""
                SELECT id FROM attendance 
                WHERE employee_id = ? AND DATE(COALESCE(clock_in_time, clock_out_time, created_at)) = ?
            """, (emp_id, target_date))
            
            existing_record = cursor.fetchone()
            
            # 獲取該員工的打卡記錄
            punches = employee_punches.get(emp_id, [])
            leaves = leave_records
            
            # 分析打卡記錄，確定上下班時間
            check_in_time = None
            check_out_time = None
            punch_note = ""
            
            if punches:
                # 第一筆打卡作為上班時間
                check_in_time = punches[0]['punch_time']
                punch_note = punches[0]['note'] or ""
                
                # 最後一筆打卡作為下班時間（如果有多筆）
                if len(punches) > 1:
                    check_out_time = punches[-1]['punch_time']
                    if punches[-1]['note']:
                        punch_note += f"; {punches[-1]['note']}"
            
            # 分析請假記錄
            leave_hours = 0
            leave_note = ""
            attendance_status = "normal"
            
            # 獲取該員工的請假記錄
            employee_leaves = [l for l in leave_records if l[0] == emp_id]
            
            if employee_leaves:
                total_leave_hours = 0
                leave_types = []
                
                for leave in employee_leaves:
                    # 使用新的請假記錄格式：(employee_id, daily_leave_hours, leave_type_name, reason, date_range)
                    daily_leave_hours = float(leave[1]) if leave[1] else 0
                    
                    # 添加調試日誌
                    if emp_id == 11:  # 只為員工11添加調試日誌
                        logger.info(f"DEBUG - 員工11請假記錄處理: daily_leave_hours={daily_leave_hours}")
                    
                    total_leave_hours += daily_leave_hours
                    leave_type_name = leave[2] or "未指定類型"
                    leave_types.append(leave_type_name)
                
                leave_hours = total_leave_hours
                leave_note = f"請假: {', '.join(set(leave_types))} ({leave_hours:.1f}小時)"
                
                # 如果是跨多天請假，加入日期範圍資訊
                if any("至" in leave[4] for leave in employee_leaves):
                    date_ranges = list(set([leave[4] for leave in employee_leaves if "至" in leave[4]]))
                    if date_ranges:
                        leave_note += f"; 請假期間: {', '.join(date_ranges)}"
                
                # 添加調試日誌
                if emp_id == 11:  # 只為員工11添加調試日誌
                    logger.info(f"DEBUG - 員工11最終請假時數: leave_hours={leave_hours}, leave_note={leave_note}")
                
                # 判斷請假狀態
                if leave_hours >= 8:  # 全天請假
                    attendance_status = "leave_full_day"
                elif leave_hours > 0:  # 部分請假
                    attendance_status = "leave_partial"
            
            # 確定最終考勤狀態
            if not punches and not employee_leaves:
                # 沒有打卡也沒有請假
                attendance_status = "absent"
                no_punch_count += 1
            elif not punches and employee_leaves:
                # 沒有打卡但有請假
                if leave_hours >= 8:
                    attendance_status = "leave_full_day"
                else:
                    attendance_status = "leave_partial_absent"  # 部分請假但未打卡
            elif punches and employee_leaves:
                # 有打卡也有請假
                attendance_status = "leave_partial"
                leave_integrated_count += 1
            elif punches and not employee_leaves:
                # 有打卡沒有請假
                if check_in_time and check_out_time:
                    attendance_status = "normal"
                elif check_in_time or check_out_time:
                    attendance_status = "incomplete"
                else:
                    attendance_status = "absent"
            
            # 合併備註
            combined_note = ""
            if punch_note:
                combined_note += punch_note
            if leave_note:
                if combined_note:
                    combined_note += "; "
                combined_note += leave_note
            
            # 創建或更新考勤記錄
            if existing_record:
                # 更新現有記錄
                cursor.execute("""
                    UPDATE attendance 
                    SET clock_in_time = ?, clock_out_time = ?, 
                        check_in = ?, check_out = ?,
                        status = ?, note = ?, 
                        leave_hours = ?, work_date = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    check_in_time, check_out_time,
                    check_in_time, check_out_time,
                    attendance_status, combined_note,
                    leave_hours, target_date,
                    existing_record['id']
                ))
                updated_count += 1
                logger.debug(f"更新員工 {emp_name} ({emp_code}) 的考勤記錄")
            else:
                # 創建新記錄
                cursor.execute("""
                    INSERT INTO attendance 
                    (employee_id, clock_in_time, clock_out_time, 
                     check_in, check_out, status, note, 
                     leave_hours, work_date, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    emp_id, check_in_time, check_out_time,
                    check_in_time, check_out_time,
                    attendance_status, combined_note,
                    leave_hours, target_date
                ))
                generated_count += 1
                logger.debug(f"創建員工 {emp_name} ({emp_code}) 的考勤記錄 - 狀態: {attendance_status}")
        
        # ===== 步驟6: 重新計算考勤統計 =====
        # 這裡可以調用現有的重新計算邏輯
        recalculation_query = f"""
            UPDATE attendance 
            SET work_hours = CASE 
                WHEN clock_in_time IS NOT NULL AND clock_out_time IS NOT NULL 
                THEN ROUND((strftime('%s', clock_out_time) - strftime('%s', clock_in_time)) / 3600.0, 2) - COALESCE(leave_hours, 0)
                ELSE 0 
            END,
            updated_at = CURRENT_TIMESTAMP
            WHERE DATE(COALESCE(clock_in_time, clock_out_time, created_at)) = ?
            {' AND employee_id IN (SELECT id FROM employees WHERE 1=1' + department_filter + ')' if department_ids else ''}
        """
        
        cursor.execute(recalculation_query, [target_date] + dept_params)
        recalculated_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        # ===== 構建響應資料 =====
        response_data = {
            "success": True,
            "message": f"完整考勤記錄生成完成 - {target_date}",
            "target_date": target_date,
            "processing_summary": {
                "total_active_employees": len(active_employees),
                "employees_with_punches": len(employee_punches),
                "employees_with_leaves": len(leave_records),
                "no_punch_employees": no_punch_count,
                "leave_integrated_employees": leave_integrated_count
            },
            "generation_results": {
                "generated_count": generated_count,
                "updated_count": updated_count,
                "total_processed": generated_count + updated_count,
                "recalculated_count": recalculated_count
            },
            "data_sources": {
                "total_punch_records": len(punch_records),
                "total_leave_records": len(leave_records),
                "departments_processed": len(department_ids) if department_ids else "全部"
            },
            "processing_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        logger.info(f"完整考勤記錄生成成功完成 - 目標日期: {target_date}, 處理員工: {len(active_employees)}, 新增: {generated_count}, 更新: {updated_count}")
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"生成完整考勤記錄時發生錯誤: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"生成完整考勤記錄失敗: {str(e)}",
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/attendance/management/daily-completion-check", methods=["GET"])
def daily_attendance_completion_check():
    """
    每日考勤完整性檢查 API
    
    功能說明：
    - 檢查指定日期的考勤記錄完整性
    - 識別缺少考勤記錄的在職員工
    - 分析打卡記錄和請假記錄的覆蓋情況
    - 提供考勤數據品質報告
    
    查詢參數：
    - target_date: 目標日期 (可選, 格式: YYYY-MM-DD, 預設為今天)
    - department_id: 部門ID (可選, 檢查特定部門)
    
    返回：
    - 考勤完整性檢查報告
    """
    try:
        logger.info("開始處理每日考勤完整性檢查請求")
        
        # 參數獲取
        target_date = request.args.get('target_date')
        department_id = request.args.get('department_id', type=int)
        
        # 預設為今天
        if not target_date:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        # 日期格式驗證
        try:
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
        except ValueError:
            logger.warning(f"無效的日期格式: {target_date}")
            return jsonify({"success": False, "error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        logger.info(f"考勤完整性檢查參數 - target_date: {target_date}, department_id: {department_id}")
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 構建部門篩選條件
        dept_filter = ""
        dept_params = []
        if department_id:
            dept_filter = " AND e.department_id = ?"
            dept_params = [department_id]
        
        # ===== 1. 獲取所有在職員工 =====
        active_employees_query = f"""
            SELECT e.id, e.name, e.employee_id, e.department_id,
                   d.name as department_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE (e.status IS NULL OR e.status = 'active')
            {dept_filter}
            ORDER BY e.department_id, e.id
        """
        
        cursor.execute(active_employees_query, dept_params)
        active_employees = cursor.fetchall()
        active_employee_ids = [emp['id'] for emp in active_employees]
        
        # ===== 2. 檢查考勤記錄覆蓋情況 =====
        attendance_coverage_query = f"""
            SELECT ar.employee_id, ar.status, ar.check_in, ar.check_out,
                   ar.note
            FROM attendance ar
            WHERE ar.employee_id IN ({','.join(['?' for _ in active_employee_ids])})
            AND DATE(COALESCE(ar.check_in, ar.check_out, ar.created_at)) = ?
        """
        
        cursor.execute(attendance_coverage_query, active_employee_ids + [target_date])
        attendance_records = cursor.fetchall()
        
        # 有考勤記錄的員工ID
        employees_with_attendance = {record['employee_id'] for record in attendance_records}
        
        # ===== 3. 檢查打卡記錄覆蓋情況 =====
        # 修正：從attendance表查詢打卡記錄，而不是punch_records表
        punch_coverage_query = f"""
            SELECT a.employee_id, COUNT(*) as punch_count,
                   MIN(a.check_in) as first_punch,
                   MAX(a.check_out) as last_punch
            FROM attendance a
            WHERE a.employee_id IN ({','.join(['?' for _ in active_employee_ids])})
            AND DATE(a.check_in) = ? OR DATE(a.check_out) = ?
            GROUP BY a.employee_id
        """
        
        cursor.execute(punch_coverage_query, active_employee_ids + [target_date, target_date])
        punch_records = cursor.fetchall()
        
        # 有打卡記錄的員工ID
        employees_with_punches = {record['employee_id'] for record in punch_records}
        
        # ===== 4. 檢查請假記錄覆蓋情況 =====
        leave_coverage_query = f"""
            SELECT lr.employee_id, lr.leave_type, lr.hours,
                   lt.name as leave_type_name
            FROM leaves lr
            LEFT JOIN leave_types lt ON lr.leave_type = lt.id
            WHERE lr.employee_id IN ({','.join(['?' for _ in active_employee_ids])})
            AND lr.status = 'approved'
            AND DATE(lr.start_time) <= ? AND DATE(lr.end_time) >= ?
        """
        
        cursor.execute(leave_coverage_query, active_employee_ids + [target_date, target_date])
        leave_records = cursor.fetchall()
        
        # 有請假記錄的員工ID
        employees_with_leaves = {record['employee_id'] for record in leave_records}
        
        conn.close()
        
        # ===== 5. 分析完整性狀況 =====
        
        # 缺少考勤記錄的員工
        missing_attendance = []
        for emp in active_employees:
            if emp['id'] not in employees_with_attendance:
                missing_attendance.append({
                    "employee_id": emp['id'],
                    "employee_name": emp['name'],
                    "employee_code": emp['employee_id'],
                    "department_name": emp['department_name'],
                    "has_punch": emp['id'] in employees_with_punches,
                    "has_leave": emp['id'] in employees_with_leaves
                })
        
        # 只有打卡沒有考勤記錄的員工
        punch_only_employees = []
        for emp in active_employees:
            if (emp['id'] in employees_with_punches and 
                emp['id'] not in employees_with_attendance):
                punch_only_employees.append({
                    "employee_id": emp['id'],
                    "employee_name": emp['name'],
                    "employee_code": emp['employee_id'],
                    "department_name": emp['department_name']
                })
        
        # 只有請假沒有考勤記錄的員工
        leave_only_employees = []
        for emp in active_employees:
            if (emp['id'] in employees_with_leaves and 
                emp['id'] not in employees_with_attendance):
                leave_only_employees.append({
                    "employee_id": emp['id'],
                    "employee_name": emp['name'],
                    "employee_code": emp['employee_id'],
                    "department_name": emp['department_name']
                })
        
        # 完全沒有任何記錄的員工
        no_record_employees = []
        for emp in active_employees:
            if (emp['id'] not in employees_with_attendance and
                emp['id'] not in employees_with_punches and
                emp['id'] not in employees_with_leaves):
                no_record_employees.append({
                    "employee_id": emp['id'],
                    "employee_name": emp['name'],
                    "employee_code": emp['employee_id'],
                    "department_name": emp['department_name']
                })
        
        # ===== 6. 計算完整性指標 =====
        total_employees = len(active_employees)
        attendance_coverage_rate = len(employees_with_attendance) / total_employees * 100 if total_employees > 0 else 0
        punch_coverage_rate = len(employees_with_punches) / total_employees * 100 if total_employees > 0 else 0
        leave_coverage_rate = len(employees_with_leaves) / total_employees * 100 if total_employees > 0 else 0
        
        # ===== 構建響應資料 =====
        response_data = {
            "success": True,
            "target_date": target_date,
            "department_filter": department_id,
            "completion_summary": {
                "total_active_employees": total_employees,
                "employees_with_attendance": len(employees_with_attendance),
                "employees_with_punches": len(employees_with_punches),
                "employees_with_leaves": len(employees_with_leaves),
                "missing_attendance_count": len(missing_attendance)
            },
            "coverage_rates": {
                "attendance_coverage": round(attendance_coverage_rate, 2),
                "punch_coverage": round(punch_coverage_rate, 2),
                "leave_coverage": round(leave_coverage_rate, 2)
            },
            "missing_data_analysis": {
                "missing_attendance_employees": missing_attendance,
                "punch_only_employees": punch_only_employees,
                "leave_only_employees": leave_only_employees,
                "no_record_employees": no_record_employees
            },
            "recommendations": [],
            "check_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # ===== 7. 生成建議 =====
        recommendations = []
        
        if len(missing_attendance) > 0:
            recommendations.append(f"發現 {len(missing_attendance)} 名員工缺少考勤記錄，建議執行完整考勤記錄生成")
        
        if len(punch_only_employees) > 0:
            recommendations.append(f"發現 {len(punch_only_employees)} 名員工有打卡記錄但缺少考勤記錄")
        
        if len(leave_only_employees) > 0:
            recommendations.append(f"發現 {len(leave_only_employees)} 名員工有請假記錄但缺少考勤記錄")
        
        if len(no_record_employees) > 0:
            recommendations.append(f"發現 {len(no_record_employees)} 名員工完全沒有任何記錄，可能為缺勤")
        
        if attendance_coverage_rate < 100:
            recommendations.append(f"考勤記錄覆蓋率為 {attendance_coverage_rate:.1f}%，建議提升至 100%")
        
        if not recommendations:
            recommendations.append("考勤記錄完整性良好，無需特別處理")
        
        response_data["recommendations"] = recommendations
        
        logger.info(f"每日考勤完整性檢查完成 - 日期: {target_date}, 總員工: {total_employees}, 缺少考勤記錄: {len(missing_attendance)}")
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"每日考勤完整性檢查時發生錯誤: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"每日考勤完整性檢查失敗: {str(e)}",
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/attendance/management/incremental-process", methods=["POST"])
def incremental_attendance_process():
    """
    增量考勤整理處理 - 使用增強版考勤處理器
    
    功能：
    - 自動檢測上次整理日期（通過查詢 attendance 表中 work_date 最大值）
    - 從上次整理日期的下一天開始批量處理考勤記錄
    - 按日期順序逐天處理，確保資料完整性
    
    請求參數：
    - end_date (可選): 結束日期 (YYYY-MM-DD)，預設為昨天
    
    返回：
    - 處理結果統計
    - 各日期的處理詳情
    """
    try:
        from services.enhanced_attendance_processor import EnhancedAttendanceProcessor
        
        data = request.get_json() or {}
        end_date = data.get('end_date')  # 可選的結束日期
        
        logger.info("🚀 開始增量考勤整理處理 - 使用增強版處理器")
        
        # 使用增強版考勤處理器
        processor = EnhancedAttendanceProcessor()
        
        # 執行批量處理
        result = processor.process_attendance_batch(end_date=end_date)
        
        if result['success']:
            # 轉換結果格式以符合前端期望
            processing_summary = {
                "start_date": result['date_range']['start_date'],
                "end_date": result['date_range']['end_date'],
                "total_days": result.get('total_days', 0),
                "successful_days": result.get('successful_days', 0),
                "failed_days": result.get('failed_days', 0),
                "total_employees_processed": result.get('total_employees_processed', 0),
                "total_records_processed": result.get('total_records_processed', 0)
            }
            
            # 轉換每日結果格式
            daily_results = []
            for daily in result.get('daily_results', []):
                daily_results.append({
                    "date": daily.get('date'),
                    "success": daily.get('success', False),
                    "employees_processed": daily.get('employees_processed', 0),
                    "records_processed": daily.get('records_processed', 0),
                    "error": daily.get('error') if not daily.get('success', False) else None
                })
            
            logger.info(f"✅ 增量考勤整理完成 - 處理 {result.get('total_days', 0)} 天，成功 {result.get('successful_days', 0)} 天")
            
            return jsonify({
                "success": True,
                "message": result.get('message', '增量考勤整理完成'),
                "processing_summary": processing_summary,
                "daily_results": daily_results
            })
        else:
            logger.error(f"❌ 增量考勤整理失敗: {result.get('error', '未知錯誤')}")
            return jsonify({
                "success": False,
                "error": result.get('error', '增量考勤整理失敗'),
                "processing_summary": {
                    "total_days": 0,
                    "successful_days": 0,
                    "failed_days": 0
                }
            }), 500
        
    except Exception as e:
        logger.error(f"增量考勤整理處理錯誤: {str(e)}")
        return jsonify({"error": f"增量考勤整理處理失敗: {str(e)}"}), 500


def generate_complete_attendance_for_date(target_date, department_ids=None, force_regenerate=False):
    """
    為指定日期生成完整考勤記錄的內部函數
    
    參數：
    - target_date: 目標日期 (YYYY-MM-DD)
    - department_ids: 部門ID列表
    - force_regenerate: 是否強制重新生成
    
    返回：
    - 處理結果字典
    """
    if department_ids is None:
        department_ids = []
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 獲取在職員工
        if department_ids:
            placeholders = ','.join(['?' for _ in department_ids])
            cursor.execute(f"""
                SELECT e.id, e.name, e.employee_id, d.name as department_name
                FROM employees e
                LEFT JOIN departments d ON e.department_id = d.id
                WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
                AND e.department_id IN ({placeholders})
                ORDER BY e.employee_id
            """, department_ids)
        else:
            cursor.execute("""
                SELECT e.id, e.name, e.employee_id, d.name as department_name
                FROM employees e
                LEFT JOIN departments d ON e.department_id = d.id
                WHERE (e.status IS NULL OR e.status IN ('active', 'trial'))
                ORDER BY e.employee_id
            """)
        
        active_employees = cursor.fetchall()
        
        if not active_employees:
            return {
                "processing_summary": {"total_active_employees": 0},
                "generation_results": {"generated_count": 0, "updated_count": 0, "total_processed": 0}
            }
        
        # 如果強制重新生成，先刪除現有記錄
        if force_regenerate:
            if department_ids:
                placeholders = ','.join(['?' for _ in department_ids])
                cursor.execute(f"""
                    DELETE FROM attendance 
                    WHERE work_date = ?
                    AND employee_id IN (
                        SELECT id FROM employees 
                        WHERE department_id IN ({placeholders})
                    )
                """, [target_date] + department_ids)
            else:
                cursor.execute("""
                    DELETE FROM attendance 
                    WHERE work_date = ?
                """, (target_date,))
        
        # 獲取打卡記錄 - 修正：從punch_records表查詢
        cursor.execute("""
            SELECT 
                e.id as employee_id, 
                p.punch_datetime as punch_time, 
                p.note
            FROM punch_records p
            JOIN employees e ON p.employee_id = e.employee_id
            WHERE DATE(p.punch_datetime) = ?
            ORDER BY e.id, p.punch_datetime
        """, (target_date,))
        
        punch_records = cursor.fetchall()
        
        # 獲取請假記錄 - 簡化版本，只查詢當天有效的請假記錄
        cursor.execute("""
            SELECT lr.employee_id, lr.leave_type as leave_type_name, lr.reason,
                   lr.start_date, lr.end_date, lr.leave_hours
            FROM leaves lr
            WHERE lr.status = 'approved'
            AND ? BETWEEN lr.start_date AND lr.end_date
        """, (target_date,))
        
        leave_records_raw = cursor.fetchall()
        
        # 處理請假記錄 - 簡化邏輯，每天固定8小時
        leave_records = []
        for leave_record in leave_records_raw:
            employee_id, leave_type_name, reason, start_date, end_date, total_leave_hours = leave_record
            
            # 計算請假天數
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            total_days = (end_date_obj - start_date_obj).days + 1
            
            # 每天的請假時數計算
            if total_leave_hours and total_leave_hours > 0:
                # 如果有指定總時數，平均分配
                daily_leave_hours = total_leave_hours / total_days
            else:
                # 沒有指定時數，預設全天請假8小時
                daily_leave_hours = 8.0
            
            # 如果是跨多天請假但每天時數小於8小時，調整為8小時
            if total_days > 1 and daily_leave_hours < 8.0:
                daily_leave_hours = 8.0
            
            # 構建日期範圍說明
            if total_days > 1:
                date_range_note = f"{start_date} 至 {end_date}"
            else:
                date_range_note = start_date
            
            leave_records.append((
                employee_id, 
                daily_leave_hours, 
                leave_type_name, 
                reason,
                date_range_note
            ))
        
        # 處理每個員工的考勤記錄
        generated_count = 0
        updated_count = 0
        
        for employee in active_employees:
            employee_id = employee[0]
            
            # 獲取該員工的打卡記錄
            employee_punches = [p for p in punch_records if p[0] == employee_id]
            
            # 獲取該員工的請假記錄
            employee_leaves = [l for l in leave_records if l[0] == employee_id]
            
            # 檢查是否已有考勤記錄
            cursor.execute("""
                SELECT id FROM attendance
                WHERE employee_id = ? AND work_date = ?
            """, (employee_id, target_date))
            
            existing_record = cursor.fetchone()
            
            # 生成或更新考勤記錄
            if existing_record:
                # 更新現有記錄
                update_attendance_record(cursor, employee_id, target_date, employee_punches, employee_leaves)
                updated_count += 1
            else:
                # 創建新記錄
                create_attendance_record(cursor, employee_id, target_date, employee_punches, employee_leaves)
                generated_count += 1
        
        conn.commit()
        
        return {
            "processing_summary": {
                "total_active_employees": len(active_employees),
                "employees_with_punches": len(set(p[0] for p in punch_records)),
                "employees_with_leaves": len(set(l[0] for l in leave_records))
            },
            "generation_results": {
                "generated_count": generated_count,
                "updated_count": updated_count,
                "total_processed": generated_count + updated_count
            }
        }
        
    finally:
        conn.close()


def create_attendance_record(cursor, employee_id, target_date, punches, leaves):
    """
    創建考勤記錄（包含班表計算邏輯）
    
    參數：
    - cursor: 資料庫游標
    - employee_id: 員工ID
    - target_date: 目標日期（工作日期）
    - punches: 打卡記錄列表 [(employee_id, punch_time, note), ...]
    - leaves: 請假記錄列表 [(employee_id, daily_leave_hours, leave_type_name, reason, date_range), ...]
    """
    # 分析打卡記錄
    clock_in_time = None
    clock_out_time = None
    
    if punches:
        # 第一筆作為上班時間，最後一筆作為下班時間
        clock_in_time = punches[0][1]
        if len(punches) > 1:
            clock_out_time = punches[-1][1]
    
    # 計算請假時數 - 只計算該員工當天的請假記錄
    employee_leaves = [l for l in leaves if l[0] == employee_id]
    leave_hours = 0
    leave_types = []
    leave_reasons = []
    date_ranges = []
    
    for leave in employee_leaves:
        # leave格式：(employee_id, daily_leave_hours, leave_type_name, reason, date_range)
        daily_hours = leave[1] if leave[1] else 8.0  # 預設8小時
        leave_hours += daily_hours
        
        if leave[2]:  # leave_type_name
            leave_types.append(leave[2])
        if leave[3]:  # reason
            leave_reasons.append(leave[3])
        if leave[4]:  # date_range
            date_ranges.append(leave[4])
    
    # ===== 新增：查詢員工班表資訊 =====
    shift_info = None
    late_minutes = 0
    early_leave_minutes = 0
    overtime_minutes = 0
    work_hours = 0
    shift_id = None
    
    # 1. 先查詢當天是否有排班記錄
    cursor.execute("""
        SELECT s.id, s.name, s.start_time, s.end_time, s.break_duration_minutes
        FROM schedules sc
        JOIN shifts s ON sc.shift_id = s.id
        WHERE sc.employee_id = ? AND sc.shift_date = ?
    """, (employee_id, target_date))
    
    shift_result = cursor.fetchone()
    
    # 2. 如果沒有排班記錄，查詢員工預設班表
    if not shift_result:
        cursor.execute("""
            SELECT s.id, s.name, s.start_time, s.end_time, s.break_duration_minutes
            FROM employees e
            JOIN shifts s ON e.shift_type = s.id
            WHERE e.id = ?
        """, (employee_id,))
        shift_result = cursor.fetchone()
    
    # 3. 如果有班表資訊，進行遲到早退加班計算
    if shift_result and clock_in_time:
        shift_id, shift_name, shift_start, shift_end, break_minutes = shift_result
        
        try:
            # 解析時間
            if isinstance(clock_in_time, str):
                if ' ' in clock_in_time:
                    actual_check_in = datetime.strptime(clock_in_time.split()[1], '%H:%M:%S').time()
                else:
                    actual_check_in = datetime.strptime(clock_in_time, '%H:%M:%S').time()
            else:
                actual_check_in = clock_in_time.time()
            
            if clock_out_time:
                if isinstance(clock_out_time, str):
                    if ' ' in clock_out_time:
                        actual_check_out = datetime.strptime(clock_out_time.split()[1], '%H:%M:%S').time()
                    else:
                        actual_check_out = datetime.strptime(clock_out_time, '%H:%M:%S').time()
                else:
                    actual_check_out = clock_out_time.time()
            else:
                actual_check_out = None
            
            shift_start_time = datetime.strptime(shift_start, '%H:%M').time()
            shift_end_time = datetime.strptime(shift_end, '%H:%M').time()
            
            # 計算遲到時間
            if actual_check_in > shift_start_time:
                late_delta = datetime.combine(datetime.today(), actual_check_in) - datetime.combine(datetime.today(), shift_start_time)
                late_minutes = int(late_delta.total_seconds() / 60)
            
            # 計算早退和加班時間
            if actual_check_out:
                # 計算早退時間
                if actual_check_out < shift_end_time:
                    early_delta = datetime.combine(datetime.today(), shift_end_time) - datetime.combine(datetime.today(), actual_check_out)
                    early_leave_minutes = int(early_delta.total_seconds() / 60)
                
                # 計算加班時間（下班後）
                if actual_check_out > shift_end_time:
                    overtime_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), shift_end_time)
                    overtime_minutes = int(overtime_delta.total_seconds() / 60)
                
                # 計算工作時數
                work_delta = datetime.combine(datetime.today(), actual_check_out) - datetime.combine(datetime.today(), actual_check_in)
                work_hours = round(work_delta.total_seconds() / 3600, 2)
            
            logging.info(f"班表計算結果 - 員工ID: {employee_id}, 班表: {shift_name}, 遲到: {late_minutes}分鐘, 早退: {early_leave_minutes}分鐘, 加班: {overtime_minutes}分鐘, 工時: {work_hours}小時")
            
        except Exception as e:
            logging.error(f"計算考勤時間失敗 - 員工ID: {employee_id}, 錯誤: {str(e)}")
    
    # ===== 判斷考勤狀態（根據班表計算結果） =====
    if leave_hours >= 8:
        status = 'leave_full_day'
    elif leave_hours > 0 and not punches:
        status = 'leave_partial_absent'
    elif leave_hours > 0 and punches:
        status = 'leave_partial'
    elif not punches:
        status = 'absent'
    elif clock_in_time and not clock_out_time:
        status = 'incomplete'
    elif late_minutes > 0 and early_leave_minutes > 0:
        status = 'late_and_early_leave'
    elif late_minutes > 0:
        status = 'late'
    elif early_leave_minutes > 0:
        status = 'early_leave'
    elif overtime_minutes > 0:
        status = 'overtime'
    else:
        status = 'normal'
    
    # 生成備註
    note_parts = []
    if punches:
        note_parts.append(f"打卡記錄: {len(punches)} 次")
    if late_minutes > 0:
        note_parts.append(f"遲到: {late_minutes} 分鐘")
    if early_leave_minutes > 0:
        note_parts.append(f"早退: {early_leave_minutes} 分鐘")
    if overtime_minutes > 0:
        note_parts.append(f"加班: {overtime_minutes} 分鐘")
    if work_hours > 0:
        note_parts.append(f"工時: {work_hours} 小時")
    if employee_leaves:
        # 請假資訊
        unique_types = list(set(leave_types))
        if unique_types:
            note_parts.append(f"請假: {', '.join(unique_types)} ({leave_hours:.1f}小時)")
        
        # 如果是跨多天請假，加入日期範圍資訊
        unique_ranges = list(set([r for r in date_ranges if "至" in r]))
        if unique_ranges:
            note_parts.append(f"請假期間: {', '.join(unique_ranges)}")
    
    note = "; ".join(note_parts) if note_parts else None
    
    # 插入考勤記錄 - 包含班表計算結果
    cursor.execute("""
        INSERT INTO attendance (
            employee_id, check_in, check_out, status, note, work_date
        ) VALUES (?, ?, ?, ?, ?, ?)
    """, (
        employee_id, clock_in_time, clock_out_time, status, note, target_date
    ))


def update_attendance_record(cursor, employee_id, target_date, punches, leaves):
    """
    更新考勤記錄（包含班表計算邏輯）
    
    參數：
    - cursor: 資料庫游標
    - employee_id: 員工ID
    - target_date: 目標日期（工作日期）
    - punches: 打卡記錄列表 [(employee_id, punch_time, note), ...]
    - leaves: 請假記錄列表 [(employee_id, daily_leave_hours, leave_type_name, reason, date_range), ...]
    """
    # 分析打卡記錄
    clock_in_time = None
    clock_out_time = None
    
    if punches:
        clock_in_time = punches[0][1]
        if len(punches) > 1:
            clock_out_time = punches[-1][1]
    
    # 計算請假時數 - 只計算該員工當天的請假記錄
    employee_leaves = [l for l in leaves if l[0] == employee_id]
    leave_hours = 0
    leave_types = []
    leave_reasons = []
    date_ranges = []
    
    for leave in employee_leaves:
        # leave格式：(employee_id, daily_leave_hours, leave_type_name, reason, date_range)
        daily_hours = leave[1] if leave[1] else 8.0  # 預設8小時
        leave_hours += daily_hours
        
        if leave[2]:  # leave_type_name
            leave_types.append(leave[2])
        if leave[3]:  # reason
            leave_reasons.append(leave[3])
        if leave[4]:  # date_range
            date_ranges.append(leave[4])
    
    # 判斷考勤狀態
    if leave_hours >= 8:
        status = 'leave_full_day'
    elif leave_hours > 0 and not punches:
        status = 'leave_partial_absent'
    elif leave_hours > 0 and punches:
        status = 'leave_partial'
    elif not punches:
        status = 'absent'
    elif clock_in_time and not clock_out_time:
        status = 'incomplete'
    elif late_minutes > 0 and early_leave_minutes > 0:
        status = 'late_and_early_leave'
    elif late_minutes > 0:
        status = 'late'
    elif early_leave_minutes > 0:
        status = 'early_leave'
    elif overtime_minutes > 0:
        status = 'overtime'
    else:
        status = 'normal'
    
    # 生成備註
    note_parts = []
    if punches:
        note_parts.append(f"打卡記錄: {len(punches)} 次")
    if late_minutes > 0:
        note_parts.append(f"遲到: {late_minutes} 分鐘")
    if early_leave_minutes > 0:
        note_parts.append(f"早退: {early_leave_minutes} 分鐘")
    if overtime_minutes > 0:
        note_parts.append(f"加班: {overtime_minutes} 分鐘")
    if work_hours > 0:
        note_parts.append(f"工時: {work_hours} 小時")
    if employee_leaves:
        # 請假資訊
        unique_types = list(set(leave_types))
        if unique_types:
            note_parts.append(f"請假: {', '.join(unique_types)} ({leave_hours:.1f}小時)")
        
        # 如果是跨多天請假，加入日期範圍資訊
        unique_ranges = list(set([r for r in date_ranges if "至" in r]))
        if unique_ranges:
            note_parts.append(f"請假期間: {', '.join(unique_ranges)}")
    
    note = "; ".join(note_parts) if note_parts else None
    
    # 更新考勤記錄 - 確保work_date設置為target_date
    cursor.execute("""
        UPDATE attendance 
        SET check_in = ?, check_out = ?, status = ?, note = ?, work_date = ?
        WHERE employee_id = ? AND (work_date = ? OR work_date IS NULL)
    """, (
        clock_in_time, clock_out_time, status, note, target_date,
        employee_id, target_date
    ))


@attendance_bp.route("/api/attendance/management/last-process-date", methods=["GET"])
def get_last_process_date():
    """
    獲取上次考勤整理日期 - 使用增強版考勤處理器
    
    功能：
    - 自動檢測上次整理日期（通過查詢 attendance 表中 work_date 最大值）
    - 計算需要處理的日期範圍
    - 提供處理狀態和建議
    
    返回：
    - last_process_date: 上次整理日期
    - next_process_date: 下次建議整理日期
    - days_since_last: 距離上次整理的天數
    - dates_to_process: 需要處理的日期列表
    """
    try:
        from services.enhanced_attendance_processor import EnhancedAttendanceProcessor
        
        # 使用增強版考勤處理器
        processor = EnhancedAttendanceProcessor()
        
        # 獲取上次處理日期
        last_date = processor.get_last_processed_date()
        
        # 獲取需要處理的日期列表
        dates_to_process = processor.get_dates_to_process()
        
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        if last_date:
            # 將字符串轉換為 date 對象
            last_date_obj = datetime.strptime(last_date, '%Y-%m-%d').date()
            days_since = (yesterday - last_date_obj).days
            next_date = last_date_obj + timedelta(days=1)
            
            return jsonify({
                "success": True,
                "last_process_date": last_date,
                "next_process_date": next_date.strftime('%Y-%m-%d') if next_date <= yesterday else None,
                "days_since_last": days_since,
                "has_pending_days": len(dates_to_process) > 0,
                "dates_to_process": dates_to_process,
                "total_pending_days": len(dates_to_process),
                "message": f"上次整理到 {last_date}，{'已是最新' if len(dates_to_process) == 0 else f'還有 {len(dates_to_process)} 天需要處理'}"
            })
        else:
            return jsonify({
                "success": True,
                "last_process_date": None,
                "next_process_date": dates_to_process[0] if dates_to_process else None,
                "days_since_last": None,
                "has_pending_days": len(dates_to_process) > 0,
                "dates_to_process": dates_to_process,
                "total_pending_days": len(dates_to_process),
                "message": f"尚未執行過考勤整理，{'建議從 30 天前開始處理' if dates_to_process else '沒有需要處理的日期'}"
            })
            
    except Exception as e:
        logger.error(f"獲取上次整理日期錯誤: {str(e)}")
        return jsonify({"error": f"獲取上次整理日期失敗: {str(e)}"}), 500


@attendance_bp.route("/api/punch/records", methods=["GET"])
def get_punch_records():
    """
    查詢打卡原始記錄 API
    
    功能說明：
    - 支援分頁查詢打卡原始記錄（punch_records表）
    - 支援多種篩選條件（員工、部門、日期範圍、狀態代碼）
    - 返回統計資訊和詳細記錄列表
    
    查詢參數：
    - page: 頁碼 (預設: 1, 最小值: 1)
    - limit: 每頁筆數 (預設: 20, 範圍: 1-100)
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - status_code: 打卡狀態代碼 (可選, 0=上班, 1=下班)
    
    返回：
    - 分頁的打卡原始記錄列表和統計資訊
    """
    try:
        logger.info("開始處理打卡原始記錄查詢請求")
        
        # ===== 參數解析與驗證 =====
        page = max(1, request.args.get('page', 1, type=int))
        limit = max(1, min(100, request.args.get('limit', 20, type=int)))
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status_code = request.args.get('status_code')
        
        logger.info(f"查詢參數驗證通過 - page: {page}, limit: {limit}, employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, status_code: {status_code}")
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        if employee_id:
            where_conditions.append("p.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        if start_date:
            where_conditions.append("DATE(p.punch_date) >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(p.punch_date) <= ?")
            query_params.append(end_date)
        
        if status_code:
            where_conditions.append("p.status_code = ?")
            query_params.append(status_code)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        # ===== 查詢總記錄數 =====
        count_query = f"""
            SELECT COUNT(*) as total
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        logger.debug(f"執行總數查詢 SQL: {count_query}")
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()['total']
        logger.info(f"查詢到總記錄數: {total_records}")
        
        # ===== 查詢詳細記錄 =====
        records_query = f"""
            SELECT 
                p.id,
                p.employee_id,
                p.device_id,
                p.punch_date,
                p.punch_time,
                p.punch_datetime,
                p.status_code,
                p.raw_data,
                p.processed,
                p.attendance_id,
                p.imported_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
            ORDER BY p.punch_datetime DESC, p.id DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            # 格式化時間欄位
            if record.get('punch_datetime'):
                record['punch_datetime_formatted'] = record['punch_datetime']
            if record.get('imported_at'):
                record['imported_at_formatted'] = record['imported_at']
            
            records.append(record)
        
        # ===== 計算統計資訊 =====
        stats_query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT p.employee_id) as total_employees,
                COUNT(DISTINCT DATE(p.punch_date)) as total_days,
                COUNT(CASE WHEN p.status_code = '0' THEN 1 END) as checkin_count,
                COUNT(CASE WHEN p.status_code = '1' THEN 1 END) as checkout_count,
                COUNT(CASE WHEN p.processed = 1 THEN 1 END) as processed_count,
                COUNT(CASE WHEN p.processed = 0 THEN 1 END) as unprocessed_count
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            {where_clause}
        """
        
        cursor.execute(stats_query, query_params)
        stats_row = cursor.fetchone()
        
        statistics = {
            "total_records": stats_row['total_records'],
            "total_employees": stats_row['total_employees'],
            "total_days": stats_row['total_days'],
            "avg_records_per_day": round(stats_row['total_records'] / max(stats_row['total_days'], 1), 1),
            "status_breakdown": {
                "checkin": stats_row['checkin_count'],
                "checkout": stats_row['checkout_count'],
                "processed": stats_row['processed_count'],
                "unprocessed": stats_row['unprocessed_count']
            }
        }
        
        conn.close()
        
        # 計算總頁數
        total_pages = (total_records + limit - 1) // limit
        
        # 構建響應資料
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "statistics": statistics,
            "query_info": {
                "filters_applied": len(where_conditions),
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        logger.info(f"打卡原始記錄查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        error_msg = f"查詢打卡原始記錄時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/punch/records/<int:record_id>", methods=["GET"])
def get_punch_record_detail(record_id):
    """
    獲取單個打卡原始記錄詳情 API
    
    功能說明：
    - 根據記錄ID獲取完整的打卡原始記錄詳情
    - 包含員工資訊、部門資訊和所有相關欄位
    - 如果有關聯的考勤記錄也一併返回
    
    參數：
    record_id (int): 打卡記錄ID
    
    返回：
    - 完整的打卡原始記錄詳情
    """
    try:
        logger.info(f"開始查詢打卡原始記錄詳情 - record_id: {record_id}")
        
        # 參數驗證
        if record_id <= 0:
            logger.warning(f"無效的記錄ID: {record_id}")
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢記錄詳情
        query = """
            SELECT 
                p.id,
                p.employee_id,
                p.device_id,
                p.punch_date,
                p.punch_time,
                p.punch_datetime,
                p.status_code,
                p.raw_data,
                p.processed,
                p.attendance_id,
                p.imported_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id
            FROM punch_records p
            LEFT JOIN employees e ON p.employee_id = e.employee_id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE p.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record_row = cursor.fetchone()
        
        if not record_row:
            logger.warning(f"找不到ID為 {record_id} 的打卡原始記錄")
            return jsonify({
                "success": False,
                "error": "找不到指定的打卡記錄"
            }), 404
        
        # 轉換為字典格式
        record = dict(record_row)
        
        # 如果有關聯的考勤記錄，也查詢出來
        if record['attendance_id']:
            attendance_query = """
                SELECT id, check_in, check_out, status, note
                FROM attendance
                WHERE id = ?
            """
            cursor.execute(attendance_query, (record['attendance_id'],))
            attendance_row = cursor.fetchone()
            if attendance_row:
                record['attendance_record'] = dict(attendance_row)
        
        conn.close()
        
        logger.info(f"打卡原始記錄詳情查詢成功 - record_id: {record_id}")
        return jsonify({
            "success": True,
            "record": record
        })
        
    except Exception as e:
        error_msg = f"查詢打卡原始記錄詳情時發生錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/attendance/management/generate-complete", methods=["POST"])
def generate_complete_attendance():
    """生成完整考勤記錄 API - 從punch_records表讀取打卡原始資料生成attendance表記錄"""
    try:
        # 從URL參數獲取日期，而不是從JSON body
        target_date = request.args.get('date')
        force_regenerate = request.args.get('force_regenerate', 'false').lower() == 'true'
        
        if not target_date:
            return jsonify({"success": False, "error": "缺少必要參數 date"}), 400
        
        logger.info(f"開始生成完整考勤記錄 - 日期: {target_date}, 強制重新生成: {force_regenerate}")
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 如果強制重新生成，先刪除現有記錄
        if force_regenerate:
            cursor.execute("DELETE FROM attendance WHERE DATE(COALESCE(check_in, check_out, created_at)) = ?", (target_date,))
            deleted_count = cursor.rowcount
            logger.info(f"強制重新生成：刪除了 {deleted_count} 筆現有考勤記錄")
        
        # 從punch_records表查詢打卡記錄
        cursor.execute("""
            SELECT e.id as employee_id, p.punch_datetime, p.status_code, p.device_id, p.raw_data,
                   e.name as employee_name, e.employee_id as employee_code
            FROM punch_records p
            JOIN employees e ON p.employee_id = e.employee_id
            WHERE p.punch_date = ?
            ORDER BY e.id, p.punch_datetime
        """, (target_date,))
        
        punch_records = cursor.fetchall()
        
        # 按員工分組處理打卡記錄
        employee_punches = {}
        for record in punch_records:
            emp_id = record[0]
            if emp_id not in employee_punches:
                employee_punches[emp_id] = {"punches": []}
            employee_punches[emp_id]["punches"].append({"datetime": record[1], "status_code": record[2]})
        
        generated_count = 0
        updated_count = 0
        
        # 為每個員工生成考勤記錄
        for emp_id, data in employee_punches.items():
            punches = data["punches"]
            
            # 確保employee_id不是None
            if emp_id is None:
                logger.error(f"employee_id為None，跳過此記錄")
                continue
            
            # 找出上班和下班打卡
            check_in_time = None
            check_out_time = None
            
            for punch in punches:
                if punch["status_code"] == "0":  # 上班
                    if not check_in_time or punch["datetime"] < check_in_time:
                        check_in_time = punch["datetime"]
                elif punch["status_code"] == "1":  # 下班
                    if not check_out_time or punch["datetime"] > check_out_time:
                        check_out_time = punch["datetime"]
            
            # 判斷考勤狀態 - 確保status不是None
            status = "normal"
            if check_in_time and not check_out_time:
                status = "incomplete"
            elif not check_in_time and not check_out_time:
                status = "absent"
            
            # 檢查是否已有考勤記錄
            cursor.execute("SELECT id FROM attendance WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out, created_at)) = ?", (emp_id, target_date))
            existing_record = cursor.fetchone()
            note = f"從打卡記錄生成 - 共{len(punches)}筆打卡"
            
            logger.info(f"處理員工ID: {emp_id}, 上班時間: {check_in_time}, 下班時間: {check_out_time}, 狀態: {status}")
            
            if existing_record:
                cursor.execute("UPDATE attendance SET check_in = ?, check_out = ?, status = ?, note = ? WHERE id = ?", (check_in_time, check_out_time, status, note, existing_record[0]))
                updated_count += 1
            else:
                cursor.execute("INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at) VALUES (?, ?, ?, ?, ?, ?)", (emp_id, check_in_time, check_out_time, status, note, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                generated_count += 1
        
        conn.commit()
        conn.close()
        
        result = {
            "success": True,
            "message": f"完整考勤記錄生成完成 - {target_date}",
            "target_date": target_date,
            "processing_summary": {"total_active_employees": len(employee_punches), "employees_with_punches": len(employee_punches), "total_punch_records": len(punch_records)},
            "generation_results": {"generated_count": generated_count, "updated_count": updated_count, "total_processed": generated_count + updated_count}
        }
        
        logger.info(f"完整考勤記錄生成API調用成功 - 日期: {target_date}, 新增: {generated_count}, 更新: {updated_count}")
        return jsonify(result), 200
        
    except Exception as e:
        error_msg = f"生成完整考勤記錄API調用失敗: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({"success": False, "error": error_msg}), 500


@attendance_bp.route("/api/attendance/trends", methods=["GET"])
def get_attendance_trends():
    """
    獲取考勤趨勢分析
    
    查詢參數：
    - days: 分析天數（預設7天）
    - employee_id: 特定員工ID（可選）
    - department_id: 特定部門ID（可選）
    
    返回：
    - 考勤趨勢統計資料
    """
    try:
        days = request.args.get('days', 7, type=int)
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        
        logger.info(f"開始獲取考勤趨勢分析 - days: {days}, employee_id: {employee_id}, department_id: {department_id}")
        
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 計算日期範圍
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 基本查詢條件
        where_conditions = ["DATE(a.check_in) BETWEEN ? AND ?"]
        params = [start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')]
        
        # 添加篩選條件
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查詢每日考勤統計
        daily_stats_query = f"""
            SELECT 
                DATE(a.check_in) as date,
                COUNT(*) as total_records,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN a.status = 'incomplete' THEN 1 END) as incomplete_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE {where_clause}
            GROUP BY DATE(a.check_in)
            ORDER BY DATE(a.check_in)
        """
        
        cursor.execute(daily_stats_query, params)
        daily_stats = [dict(row) for row in cursor.fetchall()]
        
        # 查詢總體統計
        total_stats_query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN a.status = 'incomplete' THEN 1 END) as incomplete_count,
                AVG(CASE WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                    THEN (julianday(a.check_out) - julianday(a.check_in)) * 24 END) as avg_work_hours
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE {where_clause}
        """
        
        cursor.execute(total_stats_query, params)
        total_stats = dict(cursor.fetchone())
        
        conn.close()
        
        # 計算趨勢百分比
        if len(daily_stats) >= 2:
            recent_normal_rate = (daily_stats[-1]['normal_count'] / max(daily_stats[-1]['total_records'], 1)) * 100
            previous_normal_rate = (daily_stats[-2]['normal_count'] / max(daily_stats[-2]['total_records'], 1)) * 100
            trend_percentage = recent_normal_rate - previous_normal_rate
        else:
            trend_percentage = 0
        
        result = {
            "success": True,
            "period": {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "days": days
            },
            "daily_stats": daily_stats,
            "total_stats": total_stats,
            "trend_analysis": {
                "normal_attendance_trend": trend_percentage,
                "trend_direction": "up" if trend_percentage > 0 else "down" if trend_percentage < 0 else "stable"
            }
        }
        
        logger.info(f"考勤趨勢分析查詢成功 - 返回 {len(daily_stats)} 天的統計資料")
        return jsonify(result)
        
    except Exception as e:
        error_msg = f"獲取考勤趨勢分析失敗: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


@attendance_bp.route("/api/attendance/management", methods=["GET"])
def get_attendance_management():
    """
    考勤管理查詢API
    
    查詢參數：
    - page: 頁碼（預設1）
    - limit: 每頁記錄數（預設20）
    - start_date: 開始日期（格式：YYYY-MM-DD）
    - end_date: 結束日期（格式：YYYY-MM-DD）
    - employee_id: 員工ID篩選
    - department_id: 部門ID篩選
    - status: 考勤狀態篩選
    
    返回：
    - 考勤管理記錄列表和分頁資訊
    """
    try:
        # 獲取查詢參數
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        status = request.args.get('status')
        
        logger.info(f"開始處理考勤管理查詢請求 - page: {page}, limit: {limit}, start_date: {start_date}, end_date: {end_date}")
        
        # 參數驗證
        if page < 1:
            page = 1
        if limit < 1 or limit > 100:
            limit = 20
        
        # 計算偏移量
        offset = (page - 1) * limit
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if start_date:
            where_conditions.append("DATE(a.check_in) >= ?")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(a.check_in) <= ?")
            params.append(end_date)
        
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        if status:
            where_conditions.append("a.status = ?")
            params.append(status)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查詢總記錄數
        count_query = f"""
            SELECT COUNT(*) as total
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE {where_clause}
        """
        
        cursor.execute(count_query, params)
        total_records = cursor.fetchone()['total']
        
        # 查詢記錄
        records_query = f"""
            SELECT 
                a.id,
                a.employee_id,
                e.name as employee_name,
                e.employee_id as employee_code,
                d.name as department_name,
                a.check_in,
                a.check_out,
                a.status,
                a.device_id,
                a.note,
                a.work_date,
                a.created_at,
                CASE 
                    WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                    THEN ROUND((julianday(a.check_out) - julianday(a.check_in)) * 24, 2)
                    ELSE NULL 
                END as work_hours
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE {where_clause}
            ORDER BY a.check_in DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, params + [limit, offset])
        records = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        # 計算分頁資訊
        total_pages = (total_records + limit - 1) // limit
        
        result = {
            "success": True,
            "records": records,
            "pagination": {
                "current_page": page,
                "total_pages": total_pages,
                "total_records": total_records,
                "page_size": limit,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "employee_id": employee_id,
                "department_id": department_id,
                "status": status
            }
        }
        
        logger.info(f"考勤管理查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(result)
        
    except Exception as e:
        error_msg = f"考勤管理查詢失敗: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


