#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六月份打卡原始記錄產生器

功能：
- 為所有在職員工產生6月1日到6月30日的打卡原始記錄
- 包含各種真實情況：正常上下班、遲到、早退、加班、跨日、請假等
- 確保設備編號與員工正確對應
- 只在工作日產生打卡記錄（週一到週五）
- 自動匯入到 punch_records 表

作者：AI Assistant
日期：2025-06-05
版本：v1.0.0
"""

import sys
import os
import random
import sqlite3
from datetime import datetime, date, time, timedelta
from typing import List, Dict, Tuple

# 添加專案根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import create_connection, import_punch_data

# 打卡機設備配置
DEVICE_CONFIG = {
    "MAIN_ENTRANCE": "DEV001",    # 主要入口打卡機
    "OFFICE_FLOOR_2": "DEV002",   # 二樓辦公室打卡機
    "OFFICE_FLOOR_3": "DEV003",   # 三樓辦公室打卡機
    "WAREHOUSE": "DEV004",        # 倉庫打卡機
    "FACTORY": "DEV005"           # 工廠打卡機
}

# 員工與設備對應關係（根據部門分配）
EMPLOYEE_DEVICE_MAPPING = {
    # 管理部門 - 主要入口
    "E001": "DEV001", "E002": "DEV001", "E003": "DEV001",
    # 技術部門 - 二樓辦公室
    "E005": "DEV002", "E007": "DEV002", "E009": "DEV002",
    # 行政部門 - 三樓辦公室
    "E010": "DEV003", "E011": "DEV003", "E012": "DEV003",
    # 業務部門 - 主要入口
    "E014": "DEV001", "E015": "DEV001", "E016": "DEV001",
    # 生產部門 - 工廠/倉庫
    "E017": "DEV004", "E019": "DEV005", "E020": "DEV004"
}

# 工作日設定（週一到週五）
WORK_DAYS = [0, 1, 2, 3, 4]

# 班別時間設定
SHIFT_SCHEDULES = {
    "normal": {"start": "08:00", "end": "17:00"},      # 正常班
    "early": {"start": "07:00", "end": "16:00"},       # 早班
    "late": {"start": "09:00", "end": "18:00"},        # 晚班
    "night": {"start": "22:00", "end": "06:00"}        # 夜班（跨日）
}

# 打卡狀態碼
STATUS_CODES = {
    "check_in": "0",      # 上班
    "check_out": "1",     # 下班
    "break_out": "2",     # 外出
    "break_in": "3",      # 外出返回
    "overtime_start": "4", # 加班開始
    "overtime_end": "5"    # 加班結束
}


def get_active_employees() -> List[Dict]:
    """
    獲取所有在職員工資料
    
    返回：
    List[Dict]: 員工資料列表
    """
    conn = create_connection()
    if not conn:
        print("❌ 無法連接到資料庫")
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, employee_id, department_id
            FROM employees 
            WHERE status = 'active' OR status IS NULL
            ORDER BY employee_id
        """)
        
        employees = []
        for row in cursor.fetchall():
            employees.append({
                'id': row[0],
                'name': row[1],
                'employee_id': row[2],
                'department_id': row[3]
            })
        
        print(f"✅ 獲取到 {len(employees)} 名在職員工")
        return employees
        
    except Exception as e:
        print(f"❌ 獲取員工資料失敗: {e}")
        return []
    finally:
        conn.close()


def get_work_dates_in_june() -> List[date]:
    """
    獲取6月份的所有工作日（週一到週五）
    
    返回：
    List[date]: 工作日期列表
    """
    work_dates = []
    current_date = date(2025, 6, 1)
    end_date = date(2025, 6, 30)
    
    while current_date <= end_date:
        # 只在工作日（週一到週五）產生記錄
        if current_date.weekday() in WORK_DAYS:
            work_dates.append(current_date)
        current_date += timedelta(days=1)
    
    print(f"✅ 6月份共有 {len(work_dates)} 個工作日")
    return work_dates


def determine_work_pattern(employee_id: str, work_date: date) -> str:
    """
    決定員工的工作模式
    
    參數：
    employee_id (str): 員工編號
    work_date (date): 工作日期
    
    返回：
    str: 工作模式
    """
    # 根據員工編號和日期設定不同的工作模式機率
    random.seed(f"{employee_id}_{work_date}")
    
    # 不同工作模式的機率分配
    patterns = [
        ("normal", 60),        # 正常上下班 60%
        ("late", 15),          # 遲到 15%
        ("early_leave", 8),    # 早退 8%
        ("overtime", 10),      # 加班 10%
        ("flexible", 5),       # 彈性工時（多次進出）5%
        ("half_day", 2)        # 半天班 2%
    ]
    
    # 特殊日期調整（週一遲到機率較高，週五早退機率較高）
    if work_date.weekday() == 0:  # 週一
        patterns = [("normal", 50), ("late", 25), ("early_leave", 5), ("overtime", 15), ("flexible", 5)]
    elif work_date.weekday() == 4:  # 週五
        patterns = [("normal", 55), ("late", 10), ("early_leave", 15), ("overtime", 15), ("flexible", 5)]
    
    # 隨機選擇工作模式
    total_weight = sum(weight for _, weight in patterns)
    rand_num = random.randint(1, total_weight)
    
    cumulative_weight = 0
    for pattern, weight in patterns:
        cumulative_weight += weight
        if rand_num <= cumulative_weight:
            return pattern
    
    return "normal"  # 預設為正常模式


def generate_time_variation(base_hour: int, base_minute: int, variation_minutes: int) -> time:
    """
    產生時間變化
    
    參數：
    base_hour (int): 基準小時
    base_minute (int): 基準分鐘
    variation_minutes (int): 變化範圍（分鐘）
    
    返回：
    time: 調整後的時間
    """
    # 隨機變化範圍：-variation_minutes 到 +variation_minutes
    variation = random.randint(-variation_minutes, variation_minutes)
    
    # 計算調整後的時間
    total_minutes = base_hour * 60 + base_minute + variation
    
    # 確保時間在合理範圍內
    total_minutes = max(0, min(total_minutes, 24 * 60 - 1))
    
    hour = total_minutes // 60
    minute = total_minutes % 60
    
    return time(hour, minute)


def generate_daily_punch_records(employee: Dict, work_date: date) -> List[Dict]:
    """
    為單個員工產生單日的打卡記錄
    
    參數：
    employee (Dict): 員工資料
    work_date (date): 工作日期
    
    返回：
    List[Dict]: 打卡記錄列表
    """
    employee_id = employee['employee_id']
    employee_name = employee['name']
    
    # 獲取員工對應的設備ID
    device_id = EMPLOYEE_DEVICE_MAPPING.get(employee_id, "DEV001")  # 預設使用主要入口
    
    # 決定工作模式
    work_pattern = determine_work_pattern(employee_id, work_date)
    
    # 設定隨機種子以確保一致性
    random.seed(f"{employee_id}_{work_date}_{work_pattern}")
    
    punch_records = []
    
    if work_pattern == "normal":
        # 正常上下班：8:00-17:00 (±30分鐘)
        check_in_time = generate_time_variation(8, 0, 30)
        check_out_time = generate_time_variation(17, 0, 30)
        
        punch_records.extend([
            create_punch_record(device_id, employee_id, work_date, check_in_time, STATUS_CODES["check_in"], f"正常上班 - {employee_name}"),
            create_punch_record(device_id, employee_id, work_date, check_out_time, STATUS_CODES["check_out"], f"正常下班 - {employee_name}")
        ])
    
    elif work_pattern == "late":
        # 遲到：8:30-17:30 (±60分鐘)
        check_in_time = generate_time_variation(8, 45, 60)  # 遲到45分鐘±60分鐘
        check_out_time = generate_time_variation(17, 30, 30)
        
        punch_records.extend([
            create_punch_record(device_id, employee_id, work_date, check_in_time, STATUS_CODES["check_in"], f"遲到上班 - {employee_name}"),
            create_punch_record(device_id, employee_id, work_date, check_out_time, STATUS_CODES["check_out"], f"正常下班 - {employee_name}")
        ])
    
    elif work_pattern == "early_leave":
        # 早退：8:00-16:30
        check_in_time = generate_time_variation(8, 0, 20)
        check_out_time = generate_time_variation(16, 30, 30)  # 早退30分鐘
        
        punch_records.extend([
            create_punch_record(device_id, employee_id, work_date, check_in_time, STATUS_CODES["check_in"], f"正常上班 - {employee_name}"),
            create_punch_record(device_id, employee_id, work_date, check_out_time, STATUS_CODES["check_out"], f"早退下班 - {employee_name}")
        ])
    
    elif work_pattern == "overtime":
        # 加班：8:00-20:00+
        check_in_time = generate_time_variation(8, 0, 20)
        overtime_duration = random.randint(60, 180)  # 加班1-3小時
        check_out_time = generate_time_variation(17 + overtime_duration // 60, overtime_duration % 60, 30)
        
        punch_records.extend([
            create_punch_record(device_id, employee_id, work_date, check_in_time, STATUS_CODES["check_in"], f"正常上班 - {employee_name}"),
            create_punch_record(device_id, employee_id, work_date, check_out_time, STATUS_CODES["check_out"], f"加班下班 - {employee_name}")
        ])
    
    elif work_pattern == "flexible":
        # 彈性工時：多次進出
        times = [
            generate_time_variation(8, 30, 30),   # 上班
            generate_time_variation(12, 0, 15),   # 午休外出
            generate_time_variation(13, 0, 15),   # 午休返回
            generate_time_variation(17, 30, 45)   # 下班
        ]
        
        status_sequence = [STATUS_CODES["check_in"], STATUS_CODES["break_out"], STATUS_CODES["break_in"], STATUS_CODES["check_out"]]
        notes = [f"彈性上班 - {employee_name}", f"午休外出 - {employee_name}", f"午休返回 - {employee_name}", f"彈性下班 - {employee_name}"]
        
        for i, punch_time in enumerate(times):
            punch_records.append(create_punch_record(device_id, employee_id, work_date, punch_time, status_sequence[i], notes[i]))
    
    elif work_pattern == "half_day":
        # 半天班
        if random.random() < 0.5:
            # 上午班：8:00-12:00
            check_in_time = generate_time_variation(8, 0, 20)
            check_out_time = generate_time_variation(12, 0, 30)
            note_suffix = "上午班"
        else:
            # 下午班：13:00-17:00
            check_in_time = generate_time_variation(13, 0, 20)
            check_out_time = generate_time_variation(17, 0, 30)
            note_suffix = "下午班"
        
        punch_records.extend([
            create_punch_record(device_id, employee_id, work_date, check_in_time, STATUS_CODES["check_in"], f"{note_suffix}上班 - {employee_name}"),
            create_punch_record(device_id, employee_id, work_date, check_out_time, STATUS_CODES["check_out"], f"{note_suffix}下班 - {employee_name}")
        ])
    
    return punch_records


def create_punch_record(device_id: str, employee_id: str, punch_date: date, punch_time: time, status_code: str, note: str) -> Dict:
    """
    創建打卡記錄
    
    參數：
    device_id (str): 設備ID
    employee_id (str): 員工編號
    punch_date (date): 打卡日期
    punch_time (time): 打卡時間
    status_code (str): 狀態碼
    note (str): 備註
    
    返回：
    Dict: 打卡記錄
    """
    return {
        'device_id': device_id,
        'employee_id': employee_id,
        'punch_date': punch_date.strftime('%Y-%m-%d'),
        'punch_time': punch_time.strftime('%H:%M:%S'),
        'status_code': status_code,
        'raw_data': f"{device_id},{employee_id},{punch_date.strftime('%Y%m%d')},{punch_time.strftime('%H:%M:%S')},{status_code}",
        'note': note
    }


def generate_june_punch_records() -> bool:
    """
    產生6月份所有員工的打卡記錄
    
    返回：
    bool: 是否成功
    """
    print("🚀 開始產生6月份打卡原始記錄...")
    print("=" * 60)
    
    # 獲取在職員工
    employees = get_active_employees()
    if not employees:
        print("❌ 沒有找到在職員工，程式結束")
        return False
    
    # 獲取工作日期
    work_dates = get_work_dates_in_june()
    if not work_dates:
        print("❌ 沒有找到工作日期，程式結束")
        return False
    
    # 產生打卡記錄
    all_punch_records = []
    total_records = 0
    
    print(f"📊 開始為 {len(employees)} 名員工產生 {len(work_dates)} 個工作日的打卡記錄...")
    print()
    
    for employee in employees:
        employee_records = 0
        print(f"👤 處理員工：{employee['name']} ({employee['employee_id']})")
        
        for work_date in work_dates:
            # 85% 機率正常上班，15% 機率請假（無打卡記錄）
            random.seed(f"{employee['employee_id']}_{work_date}_attendance")
            if random.random() < 0.85:
                daily_records = generate_daily_punch_records(employee, work_date)
                all_punch_records.extend(daily_records)
                employee_records += len(daily_records)
                total_records += len(daily_records)
        
        print(f"   ✅ 產生 {employee_records} 筆打卡記錄")
    
    print()
    print(f"📈 總共產生 {total_records} 筆打卡記錄")
    
    # 匯入到資料庫
    print("💾 開始匯入打卡記錄到資料庫...")
    success_count, errors = import_punch_data(all_punch_records)
    
    if errors:
        print("⚠️  匯入過程中發生錯誤：")
        for error in errors[:5]:  # 只顯示前5個錯誤
            print(f"   - {error}")
        if len(errors) > 5:
            print(f"   ... 還有 {len(errors) - 5} 個錯誤")
    
    print(f"✅ 成功匯入 {success_count} 筆打卡記錄")
    print(f"❌ 失敗 {len(errors)} 筆記錄")
    
    # 顯示統計資訊
    print()
    print("📊 產生統計：")
    print(f"   - 處理員工數：{len(employees)}")
    print(f"   - 工作日數：{len(work_dates)}")
    print(f"   - 總記錄數：{total_records}")
    print(f"   - 成功匯入：{success_count}")
    print(f"   - 匯入成功率：{success_count/total_records*100:.1f}%")
    
    return success_count > 0


def verify_generated_records():
    """
    驗證產生的打卡記錄
    """
    print()
    print("🔍 驗證產生的打卡記錄...")
    
    conn = create_connection()
    if not conn:
        print("❌ 無法連接到資料庫進行驗證")
        return
    
    try:
        cursor = conn.cursor()
        
        # 統計總記錄數
        cursor.execute("SELECT COUNT(*) FROM punch_records WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30'")
        total_records = cursor.fetchone()[0]
        
        # 統計每個員工的記錄數
        cursor.execute("""
            SELECT employee_id, COUNT(*) as record_count
            FROM punch_records 
            WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30'
            GROUP BY employee_id
            ORDER BY employee_id
        """)
        employee_stats = cursor.fetchall()
        
        # 統計每個設備的記錄數
        cursor.execute("""
            SELECT device_id, COUNT(*) as record_count
            FROM punch_records 
            WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30'
            GROUP BY device_id
            ORDER BY device_id
        """)
        device_stats = cursor.fetchall()
        
        print(f"✅ 6月份總打卡記錄數：{total_records}")
        print()
        print("👥 各員工記錄統計：")
        for employee_id, count in employee_stats:
            print(f"   {employee_id}: {count} 筆")
        
        print()
        print("🖥️  各設備記錄統計：")
        for device_id, count in device_stats:
            print(f"   {device_id}: {count} 筆")
        
        # 檢查是否有異常記錄
        cursor.execute("""
            SELECT punch_date, COUNT(*) as daily_count
            FROM punch_records 
            WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30'
            GROUP BY punch_date
            ORDER BY punch_date
        """)
        daily_stats = cursor.fetchall()
        
        print()
        print("📅 每日記錄統計（前10天）：")
        for i, (punch_date, count) in enumerate(daily_stats[:10]):
            print(f"   {punch_date}: {count} 筆")
        if len(daily_stats) > 10:
            print(f"   ... 還有 {len(daily_stats) - 10} 天的記錄")
        
    except Exception as e:
        print(f"❌ 驗證過程發生錯誤: {e}")
    finally:
        conn.close()


def main():
    """
    主程式
    """
    print("🎯 六月份打卡原始記錄產生器")
    print("=" * 60)
    print("功能：為所有在職員工產生6月1日到6月30日的打卡原始記錄")
    print("包含：正常上下班、遲到、早退、加班、彈性工時、半天班等情況")
    print("=" * 60)
    print()
    
    # 確認是否要執行
    confirm = input("⚠️  此操作將產生大量打卡記錄，是否繼續？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return
    
    # 產生打卡記錄
    success = generate_june_punch_records()
    
    if success:
        # 驗證結果
        verify_generated_records()
        print()
        print("🎉 六月份打卡原始記錄產生完成！")
        print("💡 提示：您可以使用以下SQL查詢來檢視記錄：")
        print("   SELECT * FROM punch_records WHERE punch_date BETWEEN '2025-06-01' AND '2025-06-30' LIMIT 10;")
    else:
        print("❌ 打卡記錄產生失敗")


if __name__ == "__main__":
    main() 