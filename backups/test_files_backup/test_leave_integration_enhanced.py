#!/usr/bin/env python3
"""
測試改進後的請假整合功能

此腳本測試以下功能：
1. 跨多天請假的正確處理
2. 每日請假時數的正確計算
3. 全天請假（8小時）的自動處理
4. 請假資料在考勤記錄中的正確顯示
"""

import sqlite3
import requests
import json
from datetime import datetime, timedelta
from database import create_connection

def setup_test_data():
    """
    設置測試資料
    """
    print("=== 設置測試資料 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 清理現有測試資料
        cursor.execute("DELETE FROM leave_requests WHERE employee_id IN (1, 2, 3)")
        cursor.execute("DELETE FROM attendance_records WHERE employee_id IN (1, 2, 3)")
        cursor.execute("DELETE FROM punch_records WHERE employee_id IN (1, 2, 3)")
        
        # 確保有測試員工
        cursor.execute("SELECT COUNT(*) FROM employees WHERE id IN (1, 2, 3)")
        if cursor.fetchone()[0] < 3:
            print("警告：測試員工不足，請確保資料庫中有ID為1、2、3的員工")
            return False
        
        # 確保有請假類型
        cursor.execute("SELECT COUNT(*) FROM leave_types")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO leave_types (id, name, code, max_days_per_year, is_paid, requires_approval)
                VALUES 
                (1, '年假', 'annual', 14, 1, 1),
                (2, '病假', 'sick', 30, 1, 0),
                (3, '事假', 'personal', 14, 0, 1)
            """)
        
        # 創建測試請假記錄
        today = datetime.now().date()
        test_date_1 = today + timedelta(days=1)  # 明天
        test_date_2 = today + timedelta(days=2)  # 後天
        test_date_3 = today + timedelta(days=3)  # 大後天
        
        test_leaves = [
            # 員工1：請假兩天（跨多天請假）
            {
                'employee_id': 1,
                'leave_type': '年假',
                'start_date': test_date_1.strftime('%Y-%m-%d'),
                'end_date': test_date_2.strftime('%Y-%m-%d'),
                'leave_hours': 16.0,  # 兩天共16小時
                'reason': '家庭事務',
                'status': 'approved'
            },
            # 員工2：請假一天（全天請假）
            {
                'employee_id': 2,
                'leave_type': '病假',
                'start_date': test_date_1.strftime('%Y-%m-%d'),
                'end_date': test_date_1.strftime('%Y-%m-%d'),
                'leave_hours': 8.0,  # 一天8小時
                'reason': '身體不適',
                'status': 'approved'
            },
            # 員工3：請假半天（部分請假）
            {
                'employee_id': 3,
                'leave_type': '事假',
                'start_date': test_date_1.strftime('%Y-%m-%d'),
                'end_date': test_date_1.strftime('%Y-%m-%d'),
                'leave_hours': 4.0,  # 半天4小時
                'reason': '個人事務',
                'status': 'approved'
            }
        ]
        
        for leave in test_leaves:
            cursor.execute("""
                INSERT INTO leave_requests (
                    employee_id, leave_type, start_date, end_date, 
                    leave_hours, reason, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                leave['employee_id'], leave['leave_type'], 
                leave['start_date'], leave['end_date'],
                leave['leave_hours'], leave['reason'], leave['status']
            ))
        
        # 為員工3添加一些打卡記錄（測試部分請假+打卡的情況）
        cursor.execute("""
            INSERT INTO punch_records (employee_id, punch_time, device_id, note)
            VALUES (3, ?, 'TEST_DEVICE', '測試打卡')
        """, (f"{test_date_1} 13:00:00",))
        
        cursor.execute("""
            INSERT INTO punch_records (employee_id, punch_time, device_id, note)
            VALUES (3, ?, 'TEST_DEVICE', '測試打卡')
        """, (f"{test_date_1} 18:00:00",))
        
        conn.commit()
        print("✓ 測試資料設置完成")
        print(f"  - 員工1：{test_date_1} 至 {test_date_2} 請假兩天（16小時）")
        print(f"  - 員工2：{test_date_1} 請假一天（8小時）")
        print(f"  - 員工3：{test_date_1} 請假半天（4小時）+ 下午打卡")
        
        return True
        
    except Exception as e:
        print(f"✗ 設置測試資料失敗: {e}")
        return False
    finally:
        conn.close()

def test_attendance_generation():
    """
    測試考勤生成功能
    """
    print("\n=== 測試考勤生成功能 ===")
    
    today = datetime.now().date()
    test_dates = [
        (today + timedelta(days=1)).strftime('%Y-%m-%d'),  # 明天
        (today + timedelta(days=2)).strftime('%Y-%m-%d'),  # 後天
    ]
    
    for test_date in test_dates:
        print(f"\n--- 測試日期：{test_date} ---")
        
        try:
            # 調用考勤生成API
            response = requests.post('http://localhost:5000/api/attendance/management/generate-complete', 
                                   json={
                                       'target_date': test_date,
                                       'force_regenerate': True
                                   })
            
            if response.status_code == 200:
                result = response.json()
                print("✓ 考勤生成成功")
                print(f"  - 處理員工數：{result.get('processing_summary', {}).get('total_active_employees', 0)}")
                print(f"  - 生成記錄數：{result.get('generation_results', {}).get('generated_count', 0)}")
                print(f"  - 更新記錄數：{result.get('generation_results', {}).get('updated_count', 0)}")
            else:
                print(f"✗ 考勤生成失敗: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"✗ 調用API失敗: {e}")

def verify_attendance_records():
    """
    驗證生成的考勤記錄
    """
    print("\n=== 驗證考勤記錄 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        today = datetime.now().date()
        test_dates = [
            (today + timedelta(days=1)).strftime('%Y-%m-%d'),  # 明天
            (today + timedelta(days=2)).strftime('%Y-%m-%d'),  # 後天
        ]
        
        for test_date in test_dates:
            print(f"\n--- 驗證日期：{test_date} ---")
            
            cursor.execute("""
                SELECT ar.employee_id, e.name, ar.status, ar.leave_hours, ar.note,
                       ar.clock_in_time, ar.clock_out_time
                FROM attendance_records ar
                JOIN employees e ON ar.employee_id = e.id
                WHERE ar.work_date = ? AND ar.employee_id IN (1, 2, 3)
                ORDER BY ar.employee_id
            """, (test_date,))
            
            records = cursor.fetchall()
            
            if not records:
                print("  ⚠️  沒有找到考勤記錄")
                continue
            
            for record in records:
                emp_id, emp_name, status, leave_hours, note, clock_in, clock_out = record
                print(f"  員工{emp_id}（{emp_name}）:")
                print(f"    - 狀態: {status}")
                print(f"    - 請假時數: {leave_hours or 0}")
                print(f"    - 上班時間: {clock_in or '無'}")
                print(f"    - 下班時間: {clock_out or '無'}")
                print(f"    - 備註: {note or '無'}")
                
                # 驗證邏輯
                if test_date == (today + timedelta(days=1)).strftime('%Y-%m-%d'):
                    # 明天的驗證
                    if emp_id == 1:
                        expected_hours = 8.0  # 16小時分兩天，每天8小時
                        if abs(leave_hours - expected_hours) < 0.1:
                            print("    ✓ 員工1請假時數正確（跨多天請假第一天）")
                        else:
                            print(f"    ✗ 員工1請假時數錯誤，期望{expected_hours}，實際{leave_hours}")
                    
                    elif emp_id == 2:
                        expected_hours = 8.0  # 全天請假
                        if abs(leave_hours - expected_hours) < 0.1 and status == 'leave_full_day':
                            print("    ✓ 員工2全天請假正確")
                        else:
                            print(f"    ✗ 員工2請假處理錯誤，期望8小時全天請假，實際{leave_hours}小時，狀態{status}")
                    
                    elif emp_id == 3:
                        expected_hours = 4.0  # 半天請假
                        if abs(leave_hours - expected_hours) < 0.1 and status == 'leave_partial' and clock_in and clock_out:
                            print("    ✓ 員工3部分請假+打卡正確")
                        else:
                            print(f"    ✗ 員工3處理錯誤，期望4小時部分請假+打卡，實際{leave_hours}小時，狀態{status}")
                
                elif test_date == (today + timedelta(days=2)).strftime('%Y-%m-%d'):
                    # 後天的驗證（只有員工1有請假）
                    if emp_id == 1:
                        expected_hours = 8.0  # 16小時分兩天，每天8小時
                        if abs(leave_hours - expected_hours) < 0.1:
                            print("    ✓ 員工1請假時數正確（跨多天請假第二天）")
                        else:
                            print(f"    ✗ 員工1請假時數錯誤，期望{expected_hours}，實際{leave_hours}")
                
                print()
        
    except Exception as e:
        print(f"✗ 驗證考勤記錄失敗: {e}")
    finally:
        conn.close()

def test_leave_api():
    """
    測試請假API的相容性
    """
    print("\n=== 測試請假API相容性 ===")
    
    try:
        # 測試獲取請假記錄
        response = requests.get('http://localhost:5000/api/leave-requests')
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 請假記錄API正常")
            print(f"  - 找到 {len(result.get('leave_requests', []))} 筆請假記錄")
        else:
            print(f"✗ 請假記錄API失敗: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 測試請假API失敗: {e}")

def cleanup_test_data():
    """
    清理測試資料
    """
    print("\n=== 清理測試資料 ===")
    
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("DELETE FROM leave_requests WHERE employee_id IN (1, 2, 3)")
        cursor.execute("DELETE FROM attendance_records WHERE employee_id IN (1, 2, 3)")
        cursor.execute("DELETE FROM punch_records WHERE employee_id IN (1, 2, 3)")
        
        conn.commit()
        print("✓ 測試資料清理完成")
        
    except Exception as e:
        print(f"✗ 清理測試資料失敗: {e}")
    finally:
        conn.close()

def main():
    """
    主測試流程
    """
    print("🚀 開始測試改進後的請假整合功能")
    print("=" * 50)
    
    # 設置測試資料
    if not setup_test_data():
        print("❌ 測試資料設置失敗，終止測試")
        return
    
    # 測試考勤生成
    test_attendance_generation()
    
    # 驗證考勤記錄
    verify_attendance_records()
    
    # 測試請假API
    test_leave_api()
    
    # 詢問是否清理測試資料
    print("\n" + "=" * 50)
    cleanup = input("是否清理測試資料？(y/N): ").lower().strip()
    if cleanup == 'y':
        cleanup_test_data()
    else:
        print("保留測試資料，可手動查看資料庫內容")
    
    print("\n🎉 測試完成！")

if __name__ == "__main__":
    main() 