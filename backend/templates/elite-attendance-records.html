<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打卡紀錄查詢 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-6 pt-20">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">打卡紀錄查詢</h1>
            <p class="text-gray-600">查詢員工在指定時間範圍內的所有打卡紀錄</p>
        </div>

        <!-- 查詢條件 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">員工</label>
                    <select id="employeeSelect" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有員工</option>
                        <!-- 動態載入員工選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">部門</label>
                    <select id="departmentSelect" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有部門</option>
                        <!-- 動態載入部門選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">開始日期</label>
                    <input type="date" id="startDate" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                    <input type="date" id="endDate" class="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div class="flex items-end">
                    <button id="searchBtn" class="w-full bg-brand-500 text-white px-4 py-2 rounded-lg hover:bg-brand-600 transition-colors flex items-center justify-center space-x-2">
                        <i data-lucide="search" class="w-4 h-4"></i>
                        <span>查詢</span>
                    </button>
                </div>
            </div>

            <!-- 快速選擇 -->
            <div class="mt-4 flex flex-wrap gap-2">
                <button class="quick-date-btn text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors" data-days="0">今天</button>
                <button class="quick-date-btn text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors" data-days="1">昨天</button>
                <button class="quick-date-btn text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors" data-days="7">最近7天</button>
                <button class="quick-date-btn text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors" data-days="30">最近30天</button>
            </div>
        </div>

        <!-- 統計摘要 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="fingerprint" class="w-6 h-6 text-brand-600"></i>
                    </div>
                    <div>
                        <p id="totalRecords" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">打卡記錄</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-success-600"></i>
                    </div>
                    <div>
                        <p id="totalEmployees" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">員工人數</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar-days" class="w-6 h-6 text-warning-600"></i>
                    </div>
                    <div>
                        <p id="totalDays" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">查詢天數</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-error-600"></i>
                    </div>
                    <div>
                        <p id="avgRecordsPerDay" class="text-2xl font-bold text-gray-900">0</p>
                        <p class="text-sm text-gray-500">日均打卡</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具欄 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-xl font-semibold text-gray-900">打卡紀錄列表</h2>
                    <span class="bg-brand-100 text-brand-600 text-sm px-3 py-1 rounded-full" id="recordCount">0 筆紀錄</span>
                </div>

                <div class="flex items-center space-x-3">
                    <button id="refreshBtn" class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>重新整理</span>
                    </button>
                    <button id="exportBtn" class="bg-success-500 text-white px-4 py-2 rounded-lg hover:bg-success-600 transition-colors flex items-center space-x-2">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>匯出Excel</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 紀錄列表 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="flex items-center justify-center py-12">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-500"></div>
                    <span class="text-gray-600">載入中...</span>
                </div>
            </div>

            <!-- 資料表格 -->
            <div id="dataTable" class="hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    日期時間
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    員工
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    部門
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    考勤狀態
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    工作時長
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody" class="divide-y divide-gray-100">
                            <!-- 動態生成表格內容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 無資料提示 -->
            <div id="noDataMessage" class="hidden text-center py-12">
                <div class="text-gray-400 mb-2">
                    <i data-lucide="file-search" class="w-12 h-12 mx-auto mb-3"></i>
                </div>
                <p class="text-gray-600">暫無打卡紀錄</p>
                <p class="text-sm text-gray-400 mt-1">調整查詢條件或選擇其他時間範圍</p>
            </div>
        </div>

        <!-- 分頁 -->
        <div id="pagination" class="flex items-center justify-between mt-6 hidden">
            <div class="text-sm text-gray-500">
                顯示第 <span id="pageInfo">1-20</span> 項，共 <span id="totalItems">0</span> 項
            </div>
            <div class="flex items-center space-x-2">
                <button id="prevPage" class="px-3 py-1 border border-gray-200 rounded text-sm hover:bg-gray-50 disabled:opacity-50" disabled>上一頁</button>
                <div id="pageNumbers" class="flex items-center space-x-1">
                    <!-- 動態生成頁碼 -->
                </div>
                <button id="nextPage" class="px-3 py-1 border border-gray-200 rounded text-sm hover:bg-gray-50 disabled:opacity-50" disabled>下一頁</button>
            </div>
        </div>
    </div>

    <!-- 打卡詳情模態框 -->
    <div id="recordDetailModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-lg w-full">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-brand-500 to-brand-600 px-8 py-6 text-white rounded-t-3xl">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold">打卡詳情</h3>
                            <p class="text-brand-100 text-sm">詳細打卡資訊</p>
                        </div>
                        <button type="button" id="closeDetailBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-xl transition-all duration-200">
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>

                <!-- 模態框內容 -->
                <div class="p-8">
                    <div id="recordDetailContent">
                        <!-- 動態載入打卡詳情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentPage = 1;
        let pageSize = 20;
        let totalRecords = 0;
        let currentRecords = [];

        // 全域變數儲存打卡狀態對應表
        let clockStatusMap = {};

        // 載入打卡狀態類型
        async function loadClockStatusTypes() {
            try {
                const response = await fetch('/api/masterdata/clock_status_types');
                const data = await response.json();

                if (data.items) {
                    // 建立狀態代碼到狀態名稱的對應表
                    clockStatusMap = {};
                    data.items.forEach(item => {
                        clockStatusMap[item.status_code] = item.status_name;
                    });
                    console.log('打卡狀態類型載入成功:', clockStatusMap);
                } else {
                    console.warn('打卡狀態類型載入失敗，使用預設對應表');
                    useDefaultClockStatusMap();
                }
            } catch (error) {
                console.error('載入打卡狀態類型失敗:', error);
                useDefaultClockStatusMap();
            }
        }

        // 使用預設的打卡狀態對應表（容錯處理）
        function useDefaultClockStatusMap() {
            clockStatusMap = {
                '0': '上班',
                '1': '上班',
                '2': '下班',
                '3': '休息開始',
                '4': '休息結束',
                'I': '上班',
                'O': '下班',
                'B': '休息開始',
                'E': '休息結束',
                'OT': '加班'
            };
        }

        // 獲取打卡狀態文字（動態版本）
        function getClockStatusText(statusCode) {
            // 使用動態載入的對應表
            if (clockStatusMap[statusCode]) {
                return clockStatusMap[statusCode];
            }

            // 如果找不到對應，返回原始代碼
            console.warn('未知的打卡狀態代碼:', statusCode);
            return statusCode;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            initializeForm();
            loadEmployees();
            loadDepartments();
            loadClockStatusTypes();
            bindEvents();
            setDefaultDates();
            // 初始化時顯示無資料狀態
            showInitialState();
        });

        // 顯示初始狀態（無資料）
        function showInitialState() {
            const loading = document.getElementById('loadingIndicator');
            const table = document.getElementById('dataTable');
            const noData = document.getElementById('noDataMessage');

            loading.classList.add('hidden');
            table.classList.add('hidden');
            noData.classList.remove('hidden');

            // 重置統計資訊
            updateStatistics({});
            document.getElementById('recordCount').textContent = '0 筆紀錄';
        }

        // 設定預設日期（今天）
        function setDefaultDates() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = today;
            document.getElementById('endDate').value = today;
        }

        // 綁定事件
        function bindEvents() {
            document.getElementById('searchBtn').addEventListener('click', searchRecords);
            document.getElementById('refreshBtn').addEventListener('click', searchRecords);
            document.getElementById('exportBtn').addEventListener('click', exportRecords);
            document.getElementById('closeDetailBtn').addEventListener('click', hideDetailModal);

            // 快速日期選擇
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const days = parseInt(this.dataset.days);
                    setQuickDate(days);
                });
            });

            // Enter鍵查詢
            document.getElementById('startDate').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') searchRecords();
            });
            document.getElementById('endDate').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') searchRecords();
            });
        }

        // 設定快速日期
        function setQuickDate(days) {
            const today = new Date();
            const endDate = new Date(today);
            const startDate = new Date(today);

            if (days === 0) {
                // 今天
                startDate.setDate(today.getDate());
            } else if (days === 1) {
                // 昨天
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
            } else {
                // 最近N天
                startDate.setDate(today.getDate() - days + 1);
            }

            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

            // 自動查詢
            searchRecords();
        }

        // 初始化表單
        function initializeForm() {
            // 可以在這裡設定表單的初始狀態
        }

        // 載入員工列表
        async function loadEmployees() {
            try {
                const response = await fetch('/api/employees');
                const data = await response.json();
                const employees = data.employees || data;

                const select = document.getElementById('employeeSelect');
                employees.forEach(emp => {
                    const option = document.createElement('option');
                    option.value = emp.id;
                    option.textContent = `${emp.name} (${emp.employee_id})`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入員工失敗:', error);
            }
        }

        // 載入部門列表
        async function loadDepartments() {
            try {
                const response = await fetch('/api/departments');
                const data = await response.json();

                const select = document.getElementById('departmentSelect');
                data.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入部門失敗:', error);
            }
        }

        // 查詢打卡紀錄
        async function searchRecords() {
            showLoading(true);

            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                let url = `/api/attendance/records?page=${currentPage}&limit=${pageSize}`;
                if (employeeId) url += `&employee_id=${employeeId}`;
                if (departmentId) url += `&department_id=${departmentId}`;
                if (startDate) url += `&start_date=${startDate}`;
                if (endDate) url += `&end_date=${endDate}`;

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    currentRecords = data.records || [];
                    totalRecords = data.total || 0;
                    updateStatistics(data.statistics || {});
                    renderTable();
                    updatePagination();
                } else {
                    showNotification(data.error || '查詢失敗', 'error');
                }

            } catch (error) {
                console.error('查詢失敗:', error);
                showNotification('查詢失敗', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 更新統計資訊
        function updateStatistics(stats) {
            document.getElementById('totalRecords').textContent = stats.total_records || 0;
            document.getElementById('totalEmployees').textContent = stats.total_employees || 0;
            document.getElementById('totalDays').textContent = stats.total_days || 0;
            document.getElementById('avgRecordsPerDay').textContent = (stats.avg_records_per_day || 0).toFixed(1);
        }

        // 渲染表格
        function renderTable() {
            const tableBody = document.getElementById('tableBody');
            const dataTable = document.getElementById('dataTable');
            const noDataMessage = document.getElementById('noDataMessage');
            const recordCount = document.getElementById('recordCount');

            recordCount.textContent = `${totalRecords} 筆紀錄`;

            if (currentRecords.length === 0) {
                dataTable.classList.add('hidden');
                noDataMessage.classList.remove('hidden');
                return;
            }

            noDataMessage.classList.add('hidden');
            dataTable.classList.remove('hidden');

            // 生成表格內容
            tableBody.innerHTML = currentRecords.map(record => {
                        // 處理日期顯示 - 優先使用check_in，如果沒有則使用check_out
                        const displayDateTime = record.check_in || record.check_out;

                        return `
                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="showRecordDetail(${record.id})">
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="font-medium">${formatDateTime(displayDateTime)}</div>
                            <div class="text-xs text-gray-500">${formatTime(displayDateTime)}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="font-medium">${record.employee_name}</div>
                            <div class="text-xs text-gray-500">${record.employee_code}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">${record.department_name || '-'}</td>
                        <td class="px-6 py-4 text-sm">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(record.status)}">
                                ${getStatusText(record.status)}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            ${record.calculated_work_hours ? 
                                `<span class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">${record.calculated_work_hours.toFixed(1)} 小時</span>` : 
                                '<span class="text-gray-400">-</span>'
                            }
                        </td>
                        <td class="px-6 py-4 text-right">
                            <button onclick="event.stopPropagation(); showRecordDetail(${record.id})" class="text-brand-600 hover:text-brand-700 text-sm font-medium">
                                查看
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 顯示打卡詳情
        async function showRecordDetail(recordId) {
            try {
                const response = await fetch(`/api/attendance/records/${recordId}`);
                const data = await response.json();

                if (response.ok) {
                    const record = data.record;
                    const content = document.getElementById('recordDetailContent');

                    content.innerHTML = `
                        <div class="space-y-6">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">上班時間</label>
                                    <p class="text-gray-900 font-medium">${record.check_in ? formatDateTime(record.check_in) + ' ' + formatTime(record.check_in) : '未上班'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">下班時間</label>
                                    <p class="text-gray-900 font-medium">${record.check_out ? formatDateTime(record.check_out) + ' ' + formatTime(record.check_out) : '未下班'}</p>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">考勤狀態</label>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(record.status)}">
                                        ${getStatusText(record.status)}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">工作時長</label>
                                    <p class="text-gray-900">${record.calculated_work_hours ? record.calculated_work_hours.toFixed(1) + ' 小時' : '-'}</p>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">員工</label>
                                    <p class="text-gray-900">${record.employee_name} (${record.employee_code})</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">部門</label>
                                    <p class="text-gray-900">${record.department_name || '-'}</p>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">打卡方式</label>
                                    <p class="text-gray-900">${getMethodText('web')}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">設備ID</label>
                                    <p class="text-gray-900">${record.device_id || '-'}</p>
                                </div>
                            </div>
                            
                            ${record.note ? `
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">備註</label>
                                <p class="text-gray-900">${record.note}</p>
                            </div>
                            ` : ''}
                        </div>
                    `;
                    
                    document.getElementById('recordDetailModal').classList.remove('hidden');
                } else {
                    showNotification(data.error || '載入詳情失敗', 'error');
                }
            } catch (error) {
                console.error('載入詳情失敗:', error);
                showNotification('載入詳情失敗', 'error');
            }
        }

        // 隱藏詳情模態框
        function hideDetailModal() {
            document.getElementById('recordDetailModal').classList.add('hidden');
        }

        // 匯出紀錄
        async function exportRecords() {
            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                let url = '/api/attendance/records/export?';
                const params = new URLSearchParams();
                if (employeeId) params.append('employee_id', employeeId);
                if (departmentId) params.append('department_id', departmentId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);

                const response = await fetch(url + params.toString());
                const blob = await response.blob();

                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `attendance_records_${startDate}_${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                showNotification('匯出成功', 'success');
            } catch (error) {
                console.error('匯出失敗:', error);
                showNotification('匯出失敗', 'error');
            }
        }

        // 更新分頁
        function updatePagination() {
            // TODO: 實作分頁功能
        }

        // 工具函數
        function formatDateTime(timestamp) {
            if (!timestamp) return '-';
            
            const date = new Date(timestamp);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的時間戳:', timestamp);
                return '-';
            }
            
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function formatTime(timestamp) {
            if (!timestamp) return '-';
            
            const date = new Date(timestamp);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的時間戳:', timestamp);
                return '-';
            }
            
            return date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function getClockTypeText(type) {
            const types = {
                'clock_in': '上班打卡',
                'clock_out': '下班打卡',
                'break_start': '休息開始',
                'break_end': '休息結束',
                'overtime_start': '加班開始',
                'overtime_end': '加班結束'
            };
            return types[type] || type;
        }

        function getClockTypeClass(type) {
            const classes = {
                'clock_in': 'bg-success-100 text-success-800',
                'clock_out': 'bg-error-100 text-error-800',
                'break_start': 'bg-warning-100 text-warning-800',
                'break_end': 'bg-warning-100 text-warning-800',
                'overtime_start': 'bg-brand-100 text-brand-800',
                'overtime_end': 'bg-brand-100 text-brand-800'
            };
            return classes[type] || 'bg-gray-100 text-gray-800';
        }

        function getMethodText(method) {
            const methods = {
                'web': '網頁打卡',
                'mobile': '手機打卡',
                'fingerprint': '指紋打卡',
                'card': '刷卡打卡',
                'face': '人臉識別',
                'manual': '手動補登'
            };
            return methods[method] || method;
        }

        function getStatusText(status) {
            const statuses = {
                'normal': '正常',
                'late': '遲到',
                'early_leave': '早退',
                'overtime': '加班',
                'absent': '缺勤',
                'manual': '手動補登',
                'incomplete': '不完整'
            };
            return statuses[status] || status;
        }

        function getStatusClass(status) {
            const classes = {
                'normal': 'bg-green-100 text-green-800',
                'late': 'bg-red-100 text-red-800',
                'early_leave': 'bg-orange-100 text-orange-800',
                'overtime': 'bg-blue-100 text-blue-800',
                'absent': 'bg-gray-100 text-gray-800',
                'manual': 'bg-purple-100 text-purple-800',
                'incomplete': 'bg-orange-100 text-orange-800'
            };
            return classes[status] || 'bg-gray-100 text-gray-800';
        }

        function showLoading(show) {
            const loading = document.getElementById('loadingIndicator');
            const table = document.getElementById('dataTable');
            const noData = document.getElementById('noDataMessage');
            
            if (show) {
                loading.classList.remove('hidden');
                table.classList.add('hidden');
                noData.classList.add('hidden');
            } else {
                loading.classList.add('hidden');
            }
        }

        // 向後兼容的通知函數（使用新的工具函數庫）
        function showNotification(message, type = 'info') {
            if (window.NotificationSystem) {
                switch (type) {
                    case 'success':
                        NotificationSystem.success(message);
                        break;
                    case 'error':
                        NotificationSystem.error(message);
                        break;
                    case 'warning':
                        NotificationSystem.warning(message);
                        break;
                    default:
                        NotificationSystem.info(message);
                }
            } else {
                console.warn('通知系統未載入，使用控制台輸出:', message);
            }
        }
    </script>
</body>

</html>