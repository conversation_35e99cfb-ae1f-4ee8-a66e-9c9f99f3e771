"""
Flask 應用程式主模組。

此模組負責：
- 設定 Flask 應用程式
- 定義所有 API 路由
- 處理請求和響應
"""

import csv
import io
import json
import logging
import os
import time

import sqlite3
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, send_file, send_from_directory, render_template, redirect, url_for
from flask_cors import CORS

from config import Config
from database import create_connection, init_db
from services.health_monitor import health_monitor
from services.attendance_processor import attendance_processor

# 配置日誌
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.FileHandler(Config.LOG_FILE), logging.StreamHandler()],
)

# 創建logger實例
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置應用程式
app.config["SECRET_KEY"] = Config.SECRET_KEY
app.config["MAX_CONTENT_LENGTH"] = Config.MAX_CONTENT_LENGTH
app.config["UPLOAD_FOLDER"] = Config.UPLOAD_FOLDER

# 確保上傳目錄存在
os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)


@app.route("/")
def index():
    """主頁面 - 重定向到Elite版"""
    return redirect(url_for('elite_dashboard'))


@app.route("/modern")
def modern_index():
    """現代化主頁面 - 全新設計的智慧考勤系統"""
    return render_template("modern-index.html")


@app.route("/professional")
def professional_dashboard():
    """專業版儀表板 - 參考醫療儀表板設計風格"""
    return render_template("professional-dashboard.html")


@app.route("/elite")
def elite_dashboard():
    """Elite版儀表板 - 國際級企業設計標準"""
    return render_template("elite-dashboard.html")


@app.route("/elite/attendance")
def elite_attendance():
    """Elite版考勤打卡頁面"""
    return render_template("elite-attendance.html")


@app.route("/elite/schedule")
def elite_schedule():
    """Elite版排班系統頁面"""
    return render_template("elite-schedule.html")


@app.route("/elite/leaves")
def elite_leaves():
    """Elite版請假管理頁面"""
    return render_template("elite-leaves.html")


@app.route("/elite/employees")
def elite_employees():
    """Elite版員工管理頁面"""
    return render_template("elite-employees.html")


@app.route("/elite/analytics")
def elite_analytics():
    """Elite版數據分析頁面"""
    return render_template("elite-analytics.html")


@app.route("/elite/settings")
def elite_settings():
    """Elite版系統設定頁面"""
    return render_template("elite-settings.html")


@app.route("/elite/approval")
def elite_approval():
    """Elite版審核作業頁面"""
    return render_template("elite-approval.html")


@app.route("/elite/masterdata")
def elite_masterdata():
    """Elite版基本資料管理頁面"""
    return render_template("elite-masterdata.html")


@app.route("/mobile")
def mobile_dashboard():
    """移動端優化版儀表板 - 原生App體驗"""
    return render_template("mobile-dashboard.html")


@app.route("/leaves")
def leaves():
    """請假管理頁面"""
    return render_template("leaves.html")


@app.route("/monitor")
def system_monitor():
    """系統監控頁面"""
    return render_template("system-monitor.html")


@app.route("/elite/shifts")
def elite_shifts():
    """Elite 版本班別管理頁面"""
    return render_template("elite-shifts.html")


@app.route("/elite/features")
def elite_features():
    """Elite 版本功能總覽頁面"""
    return render_template("elite-features.html")

@app.route("/elite/attendance-records")
def elite_attendance_records():
    """打卡紀錄查詢頁面"""
    return render_template("elite-attendance-records.html")

@app.route("/elite/attendance-management")
def elite_attendance_management():
    """考勤作業管理頁面"""
    return render_template("elite-attendance-management.html")

@app.route("/elite/attendance-processing")
def elite_attendance_processing():
    """考勤整理頁面"""
    return render_template("elite-attendance-processing.html")

@app.route("/elite/import-attendance")
def elite_import_attendance():
    """匯入文字檔頁面"""
    return render_template("elite-import-attendance.html")

@app.route("/test/approval-display")
def test_approval_display():
    """審核資料顯示測試頁面"""
    return send_from_directory('.', 'test_approval_display.html')


# ====== 登入與認證模組 ======
@app.route("/api/login", methods=["POST"])
def login():
    """使用者登入"""
    data = request.json
    if not data or "username" not in data or "password" not in data:
        return jsonify({"error": "缺少必要參數"}), 400

    # 在實際應用中，需要實現密碼雜湊和驗證
    # 這裡只是簡單示範
    if data["username"] == "admin" and data["password"] == "admin123":
        return jsonify(
            {"success": True, "user_id": 1, "name": "系統管理員", "role": "admin"}
        )

    return jsonify({"error": "帳號或密碼錯誤"}), 401


@app.route("/api/dashboard/stats", methods=["GET"])
def dashboard_stats():
    """獲取儀表板統計數據"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        # 獲取今日考勤統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute(
            """
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent,
                SUM(CASE WHEN status = 'manual' THEN 1 ELSE 0 END) as manual
            FROM attendance
            WHERE DATE(check_in) = ?
        """,
            (today,),
        )

        attendance_stats = dict(
            zip(
                ["total", "normal", "late", "early_leave", "absent", "manual"],
                cursor.fetchone(),
            )
        )

        # 獲取待審批請假數量
        cursor.execute(
            """
            SELECT COUNT(*) FROM leaves WHERE status = 'pending'
        """
        )
        result = cursor.fetchone()
        pending_leaves = result[0] if result else 0

        # 獲取部門統計
        cursor.execute(
            """
            SELECT d.name, COUNT(e.id)
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            GROUP BY d.id
        """
        )
        departments = [{"name": row[0], "count": row[1]} for row in cursor.fetchall()]

        # 獲取最近的考勤記錄
        cursor.execute(
            """
            SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            ORDER BY a.check_in DESC
            LIMIT 5
        """
        )
        recent_records = [
            {
                "id": row[0],
                "name": row[1],
                "employee_id": row[2],
                "check_in": row[3],
                "check_out": row[4],
                "status": row[5],
            }
            for row in cursor.fetchall()
        ]

        return jsonify(
            {
                "attendance": attendance_stats,
                "pending_leaves": pending_leaves,
                "departments": departments,
                "recent_records": recent_records,
            }
        )
    except sqlite3.Error as e:
        logging.error(f"儀表板統計數據查詢錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/dashboard/reports", methods=["GET"])
def dashboard_reports():
    """獲取儀表板報表數據"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取今日統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkins,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM attendance
            WHERE DATE(check_in) = ?
        """, (today,))
        
        today_stats = cursor.fetchone()
        
        # 獲取本週統計
        week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT COUNT(*) as week_total
            FROM attendance
            WHERE DATE(check_in) >= ?
        """, (week_start,))
        
        week_stats = cursor.fetchone()
        
        # 獲取待審批請假
        cursor.execute("""
            SELECT COUNT(*) as pending_leaves
            FROM leaves
            WHERE status = 'pending'
        """)
        
        leave_stats = cursor.fetchone()
        
        # 獲取部門考勤統計
        cursor.execute("""
            SELECT d.name, 
                   COUNT(a.id) as total_attendance,
                   SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            LEFT JOIN attendance a ON e.id = a.employee_id AND DATE(a.check_in) = ?
            GROUP BY d.id, d.name
            ORDER BY d.name
        """, (today,))
        
        department_stats = []
        for row in cursor.fetchall():
            department_stats.append({
                "department": row[0],
                "total_attendance": row[1] or 0,
                "normal_count": row[2] or 0,
                "attendance_rate": round((row[2] or 0) / max(row[1] or 1, 1) * 100, 1)
            })
        
        # 獲取最近7天的考勤趨勢
        cursor.execute("""
            SELECT DATE(check_in) as date,
                   COUNT(*) as total,
                   SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                   SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
            FROM attendance
            WHERE DATE(check_in) >= DATE('now', '-7 days')
            GROUP BY DATE(check_in)
            ORDER BY DATE(check_in)
        """)
        
        trend_data = []
        for row in cursor.fetchall():
            trend_data.append({
                "date": row[0],
                "total": row[1],
                "normal": row[2],
                "late": row[3],
                "on_time_rate": round((row[2] / max(row[1], 1)) * 100, 1)
            })
        
        conn.close()
        
        return jsonify({
            "today": {
                "total_checkins": today_stats[0] if today_stats else 0,
                "normal_count": today_stats[1] if today_stats else 0,
                "late_count": today_stats[2] if today_stats else 0,
                "early_leave_count": today_stats[3] if today_stats else 0,
                "absent_count": today_stats[4] if today_stats else 0,
                "on_time_rate": round((today_stats[1] / max(today_stats[0], 1)) * 100, 1) if today_stats and today_stats[0] > 0 else 0
            },
            "week": {
                "total_checkins": week_stats[0] if week_stats else 0
            },
            "leaves": {
                "pending_count": leave_stats[0] if leave_stats else 0
            },
            "departments": department_stats,
            "trends": trend_data,
            "summary": {
                "total_employees": len(department_stats),
                "active_departments": len([d for d in department_stats if d["total_attendance"] > 0]),
                "average_attendance_rate": round(sum(d["attendance_rate"] for d in department_stats) / max(len(department_stats), 1), 1)
            }
        })
        
    except Exception as e:
        logging.error(f"獲取儀表板報表失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 資料採集模組 ======
@app.route("/api/attendance/import", methods=["POST"])
def import_attendance():
    """匯入考勤資料（CSV 檔案）"""
    if "file" not in request.files:
        return jsonify({"error": "未找到檔案"}), 400
    
    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "未選擇檔案"}), 400

    try:
        # 讀取 CSV 檔案
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_data = csv.DictReader(stream)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        imported_count = 0
        for row in csv_data:
            cursor.execute(
                """
                INSERT INTO attendance (employee_id, check_in, status)
                VALUES (?, ?, ?)
            """,
                (row["employee_id"], row["clock_time"], row["status"]),
            )
            imported_count += 1
        
        conn.commit()
        conn.close()
        logging.info(f"成功匯入 {imported_count} 筆考勤記錄")
        
        return jsonify({"message": f"成功匯入 {imported_count} 筆記錄"})
    except Exception as e:
        logging.error(f"匯入考勤資料失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/manual", methods=["POST"])
def manual_attendance():
    """手動新增考勤記錄"""
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少必要參數"}), 400
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            """
            INSERT INTO attendance (employee_id, check_in, check_out, status, note)
            VALUES (?, ?, ?, ?, ?)
        """,
            (
                data["employee_id"],
                data.get("check_in"),
                data.get("check_out"),
                data.get("status", "manual"),
                data.get("note", "")
            )
        )
        
        conn.commit()
        attendance_id = cursor.lastrowid
        
        logging.info(f"手動新增考勤記錄: 員工ID {data['employee_id']}, 記錄ID {attendance_id}")
        
        return jsonify({
            "message": "考勤記錄新增成功",
            "attendance_id": attendance_id
        })
        
    except sqlite3.Error as e:
        logging.error(f"手動新增考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/today/<int:employee_id>", methods=["GET"])
def get_today_attendance(employee_id):
    """獲取員工今日考勤記錄"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 獲取員工資訊
        cursor.execute("SELECT name, employee_id FROM employees WHERE id = ?", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            return jsonify({"error": "員工不存在"}), 404
        
        # 獲取今日考勤記錄
        cursor.execute(
            """
            SELECT id, check_in, check_out, status, note
            FROM attendance
            WHERE employee_id = ? AND DATE(check_in) = ?
            ORDER BY check_in DESC
            LIMIT 1
        """,
            (employee_id, today)
        )
        
        attendance = cursor.fetchone()
        
        result = {
            "employee_id": employee[1],
            "employee_name": employee[0],
            "date": today,
            "has_attendance": attendance is not None
        }
        
        if attendance:
            result.update({
                "attendance_id": attendance[0],
                "check_in": attendance[1],
                "check_out": attendance[2],
                "status": attendance[3],
                "note": attendance[4],
                "has_checked_in": attendance[1] is not None,
                "has_checked_out": attendance[2] is not None
            })
        else:
            result.update({
                "attendance_id": None,
                "check_in": None,
                "check_out": None,
                "status": None,
                "note": None,
                "has_checked_in": False,
                "has_checked_out": False
            })
        
        return jsonify(result)
        
    except sqlite3.Error as e:
        logging.error(f"獲取今日考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/clock-in", methods=["POST"])
def clock_in():
    """
    智能打卡API。
    
    支援跨日考勤邏輯，自動判斷上班/下班，處理換日時間。
    """
    data = request.get_json()
    
    if not data or not data.get("employee_id"):
        return jsonify({"error": "缺少員工ID"}), 400
    
    try:
        employee_id = data["employee_id"]
        punch_datetime = datetime.now()
        device_id = data.get("device_id")
        note = data.get("note")
        
        # 使用考勤處理器處理打卡
        result = attendance_processor.process_punch_record(
            employee_id=employee_id,
            punch_datetime=punch_datetime,
            device_id=device_id,
            note=note
        )
        
        if result['success']:
            # 獲取員工資訊
            conn = create_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM employees WHERE id = ?", (employee_id,))
            employee = cursor.fetchone()
            conn.close()
            
            employee_name = employee[0] if employee else "未知員工"
            
            logging.info(f"員工 {employee_name} (ID: {employee_id}) 打卡成功 - {result['punch_type']}")
            
            return jsonify({
                "message": result.get('message', '打卡成功'),
                "employee_name": employee_name,
                "punch_type": result['punch_type'],
                "work_date": result['work_date'],
                "punch_time": result['punch_time'],
                "attendance_id": result.get('attendance_id'),
                **{k: v for k, v in result.items() if k not in ['success', 'message', 'punch_type', 'work_date', 'punch_time', 'attendance_id']}
            })
        else:
            return jsonify({"error": result.get('error', '打卡失敗')}), 500
            
    except Exception as e:
        logging.error(f"打卡API錯誤: {str(e)}")
        return jsonify({"error": "系統錯誤，請稍後再試"}), 500


@app.route("/api/attendance/clock-out", methods=["POST"])
def clock_out():
    """
    下班打卡API（已整合到智能打卡中）。
    
    此API保留向後相容性，實際使用智能打卡邏輯。
    """
    # 直接調用智能打卡API
    return clock_in()


@app.route("/api/attendance/recent", methods=["GET"])
def get_recent_attendance():
    """獲取最近的考勤記錄"""
    try:
        limit = request.args.get('limit', 5, type=int)
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, e.name, e.employee_id, a.check_in, a.check_out, a.status,
                   d.name as department_name
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            ORDER BY a.check_in DESC
            LIMIT ?
        """, (limit,))
        
        records = []
        for row in cursor.fetchall():
            records.append({
                "id": row[0],
                "name": row[1],
                "employee_id": row[2],
                "check_in": row[3],
                "check_out": row[4],
                "status": row[5],
                "department_name": row[6]
            })
        
        conn.close()
        return jsonify({"recent_attendance": records})
        
    except Exception as e:
        logging.error(f"獲取最近考勤記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/daily-summary/<int:employee_id>", methods=["GET"])
def get_daily_attendance_summary(employee_id):
    """
    獲取員工指定日期的考勤摘要。
    
    支援跨日考勤邏輯，根據換日時間計算工作日期。
    """
    try:
        # 獲取查詢日期，預設為今天
        date_str = request.args.get('date')
        if date_str:
            work_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            # 根據當前時間和換日設定確定工作日期
            work_date = attendance_processor.get_work_date(datetime.now())
        
        # 獲取考勤摘要
        summary = attendance_processor.get_daily_summary(employee_id, work_date)
        
        return jsonify(summary)
        
    except Exception as e:
        logging.error(f"獲取考勤摘要失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/settings", methods=["GET", "POST"])
def manage_attendance_settings():
    """
    管理考勤設定。
    
    包括換日時間、跨日考勤開關等設定。
    """
    if request.method == "GET":
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 獲取考勤相關設定
            cursor.execute("""
                SELECT rule_type, rule_value, description 
                FROM schedule_rules 
                WHERE rule_type IN (
                    'day_change_time', 
                    'cross_day_attendance', 
                    'first_punch_as_checkin', 
                    'last_punch_as_checkout',
                    'late_tolerance_minutes',
                    'early_leave_tolerance_minutes'
                )
                ORDER BY rule_type
            """)
            
            settings = {}
            for row in cursor.fetchall():
                settings[row[0]] = {
                    'value': row[1],
                    'description': row[2]
                }
            
            conn.close()
            return jsonify({"settings": settings})
            
        except Exception as e:
            logging.error(f"獲取考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500
    
    else:  # POST
        try:
            data = request.json
            if not data:
                return jsonify({"error": "缺少設定資料"}), 400
            
            conn = create_connection()
            cursor = conn.cursor()
            
            # 更新設定
            for rule_type, rule_value in data.items():
                cursor.execute("""
                    UPDATE schedule_rules 
                    SET rule_value = ? 
                    WHERE rule_type = ?
                """, (str(rule_value), rule_type))
                
                if cursor.rowcount == 0:
                    # 如果設定不存在，則插入新設定
                    cursor.execute("""
                        INSERT INTO schedule_rules (rule_type, rule_value, description)
                        VALUES (?, ?, ?)
                    """, (rule_type, str(rule_value), f"考勤設定: {rule_type}"))
            
            conn.commit()
            conn.close()
            
            # 重新載入考勤處理器設定
            attendance_processor._load_settings()
            
            logging.info(f"考勤設定更新成功: {data}")
            return jsonify({"message": "考勤設定更新成功"})
            
        except Exception as e:
            logging.error(f"更新考勤設定失敗: {str(e)}")
            return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/work-date", methods=["GET"])
def get_work_date():
    """
    根據當前時間和換日設定，獲取對應的工作日期。
    """
    try:
        # 獲取查詢時間，預設為當前時間
        time_str = request.args.get('time')
        if time_str:
            query_time = datetime.fromisoformat(time_str)
        else:
            query_time = datetime.now()
        
        work_date = attendance_processor.get_work_date(query_time)
        
        return jsonify({
            "query_time": query_time.isoformat(),
            "work_date": work_date.isoformat(),
            "day_change_time": attendance_processor.day_change_time.strftime('%H:%M'),
            "cross_day_enabled": attendance_processor.cross_day_enabled
        })
        
    except Exception as e:
        logging.error(f"獲取工作日期失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 排班管理模組 ======
@app.route("/api/schedules/batch", methods=["POST"])
def batch_schedule():
    """批次設定班表"""
    data = request.json
    if not isinstance(data, list):
        return jsonify({"error": "資料格式錯誤"}), 400

    conn = create_connection()
    try:
        cursor = conn.cursor()
        for schedule in data:
            cursor.execute(
                """
                INSERT INTO schedules (employee_id, shift_date, shift_type, start_time, end_time)
                VALUES (?, ?, ?, ?, ?)
            """,
                (
                    schedule["employee_id"],
                    schedule["shift_date"],
                    schedule["shift_type"],
                    schedule["start_time"],
                    schedule["end_time"],
                ),
            )
        
        conn.commit()
        logging.info(f"成功批次設定 {len(data)} 筆班表資料")
        return jsonify({"message": "班表設定成功"})
    except sqlite3.Error as e:
        logging.error(f"批次設定班表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/schedules/rules", methods=["GET", "POST"])
def manage_schedule_rules():
    """管理排班規則"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("SELECT * FROM schedule_rules")
            columns = [col[0] for col in cursor.description]
            rules = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(rules)
        else:
            data = request.json
            cursor.execute(
                """
                INSERT INTO schedule_rules (rule_type, rule_value, description)
                VALUES (?, ?, ?)
            """,
                (data["rule_type"], data["rule_value"], data["description"]),
            )
            
            conn.commit()
            logging.info(f"成功設定排班規則 {data['rule_type']}")
            return jsonify({"message": "規則設定成功"})
    except sqlite3.Error as e:
        logging.error(f"管理排班規則錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 出勤分析模組 ======
@app.route("/api/attendance/analysis", methods=["GET"])
def analyze_attendance():
    """出勤分析報表"""
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    department = request.args.get("department")

    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 基本查詢條件
        query_conditions = []
        params = []
        
        if start_date and end_date:
            query_conditions.append("DATE(a.check_in) BETWEEN ? AND ?")
            params.extend([start_date, end_date])
        
        if department:
            query_conditions.append("e.department_id = ?")
            params.append(department)
        
        where_clause = " AND ".join(query_conditions)
        where_clause = f"WHERE {where_clause}" if where_clause else ""
        
        # 查詢出勤統計
        cursor.execute(
            f"""
            SELECT 
                d.name as department,
                COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN departments d ON e.department_id = d.id
            {where_clause}
            GROUP BY d.id
        """,
            params,
        )

        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # 如果是按日期範圍查詢，添加每日趨勢數據
        if start_date and end_date:
            cursor.execute(
                f"""
                SELECT 
                    DATE(a.check_in) as date,
                    COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            {where_clause}
                GROUP BY DATE(a.check_in)
                ORDER BY DATE(a.check_in)
            """,
                params,
            )

            columns = [col[0] for col in cursor.description]
            daily_trends = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return jsonify({"summary": results, "daily_trends": daily_trends})

        return jsonify(results)
    except sqlite3.Error as e:
        logging.error(f"出勤分析錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/reports/export", methods=["GET"])
def export_report():
    """匯出報表"""
    report_type = request.args.get("type", "attendance")
    format_type = request.args.get("format", "csv")
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    department = request.args.get("department")
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 構建基本查詢條件
        query_conditions = []
        params = []

        if start_date and end_date:
            query_conditions.append("DATE(a.check_in) BETWEEN ? AND ?")
            params.extend([start_date, end_date])

        if department:
            query_conditions.append("e.department_id = ?")
            params.append(department)

        where_clause = " AND ".join(query_conditions)
        where_clause = f"WHERE {where_clause}" if where_clause else ""

        if report_type == "attendance":
            cursor.execute(
                f"""
                SELECT e.name, d.name as department, a.check_in, a.check_out, a.status, a.note
                FROM attendance a
                JOIN employees e ON a.employee_id = e.id
                JOIN departments d ON e.department_id = d.id
                {where_clause}
                ORDER BY a.check_in DESC
            """,
                params,
            )
            
            if format_type == "csv":
                output = io.StringIO()
                writer = csv.writer(output)
                writer.writerow(
                    ["姓名", "部門", "簽到時間", "簽退時間", "狀態", "備註"]
                )
                writer.writerows(cursor.fetchall())
                
                return send_file(
                    io.BytesIO(output.getvalue().encode("utf-8-sig")),
                    mimetype="text/csv",
                    as_attachment=True,
                    download_name=f'attendance_report_{datetime.now().strftime("%Y%m%d")}.csv',
                )
            
        return jsonify({"error": "不支援的報表類型或格式"}), 400
    except sqlite3.Error as e:
        logging.error(f"匯出報表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 員工管理模組 ======
@app.route("/api/employees", methods=["GET"])
def get_employees():
    """
    獲取員工列表，支援多條件查詢
    
    查詢參數：
    - department_id: 部門ID篩選
    - employee_id: 員工編號搜尋（支援模糊搜尋）
    - name: 員工姓名搜尋（支援模糊搜尋）
    - position: 職位搜尋（支援模糊搜尋）
    
    返回：
    - 員工列表，包含部門名稱、角色名稱、主管姓名等完整資訊
    """
    try:
        # 獲取查詢參數
        department_id = request.args.get('department_id')
        employee_id = request.args.get('employee_id')
        name = request.args.get('name')
        position = request.args.get('position')
        
        # 構建基本查詢
        query = """
            SELECT e.id, e.name, e.employee_id, e.department_id, e.position, 
                   e.email, e.phone, e.role_id, e.manager_id, e.hire_date, 
                   e.status, e.salary_level, e.id_number, e.address, 
                   e.emergency_contact, e.emergency_phone, e.photo_url, e.shift_type,
                   d.name as department_name,
                   r.role_name as role_name,
                   m.name as manager_name,
                   s.name as shift_name
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions r ON e.role_id = r.id
            LEFT JOIN employees m ON e.manager_id = m.id
            LEFT JOIN shifts s ON e.shift_type = s.id
            WHERE 1=1
        """
        
        params = []
        
        # 添加查詢條件
        if department_id:
            query += " AND e.department_id = ?"
            params.append(department_id)
            
        if employee_id:
            query += " AND e.employee_id LIKE ?"
            params.append(f"%{employee_id}%")
            
        if name:
            query += " AND e.name LIKE ?"
            params.append(f"%{name}%")
            
        if position:
            query += " AND e.position LIKE ?"
            params.append(f"%{position}%")
        
        query += " ORDER BY e.id"
        
        conn = create_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        employees = []
        for row in cursor.fetchall():
            employee = {
                'id': row[0],
                'name': row[1],
                'employee_id': row[2],
                'department_id': row[3],
                'position': row[4],
                'email': row[5],
                'phone': row[6],
                'role_id': row[7],
                'manager_id': row[8],
                'hire_date': row[9],
                'status': row[10],
                'salary_level': row[11],
                'id_number': row[12],
                'address': row[13],
                'emergency_contact': row[14],
                'emergency_phone': row[15],
                'photo_url': row[16],
                'shift_type': row[17],
                'department_name': row[18],
                'role_name': row[19],
                'manager_name': row[20],
                'shift_name': row[21]
            }
            employees.append(employee)
        
        conn.close()
        return jsonify({'employees': employees})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route("/api/employees", methods=["POST"])
def create_employee():
    """
    新增員工
    
    請求體：
    - name: 員工姓名（必填）
    - employee_id: 員工編號（必填）
    - department_id: 部門ID（必填）
    - position: 職位（必填）
    - email: 電子郵件（必填）
    - phone: 電話號碼
    - role_id: 角色ID
    - manager_id: 主管ID
    - password: 密碼
    - hire_date: 到職日期
    - status: 員工狀態
    - salary_level: 薪資等級
    - id_number: 身分證號
    - address: 地址
    - emergency_contact: 緊急聯絡人
    - emergency_phone: 緊急聯絡電話
    - photo_url: 照片連結
    
    返回：
    - 新增的員工資訊
    """
    conn = None
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['name', 'employee_id', 'department_id', 'position', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填欄位: {field}'}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 驗證外鍵約束
        if data.get('department_id'):
            cursor.execute("SELECT id FROM departments WHERE id = ?", (data['department_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的部門不存在'}), 400
        
        if data.get('role_id'):
            cursor.execute("SELECT id FROM permissions WHERE id = ?", (data['role_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的角色不存在'}), 400
        
        if data.get('manager_id'):
            cursor.execute("SELECT id FROM employees WHERE id = ?", (data['manager_id'],))
            if not cursor.fetchone():
                return jsonify({'error': '指定的主管不存在'}), 400
        
        # 檢查員工編號是否已存在
        cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (data['employee_id'],))
        if cursor.fetchone():
            return jsonify({'error': '員工編號已存在'}), 400
        
        # 插入新員工
        cursor.execute("""
            INSERT INTO employees (name, employee_id, department_id, position, email, phone, 
                                 role_id, manager_id, password, hire_date, status, salary_level, 
                                 id_number, address, emergency_contact, emergency_phone, photo_url, shift_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['name'],
            data['employee_id'],
            data['department_id'],
            data['position'],
            data['email'],
            data.get('phone', ''),
            data.get('role_id', 3),  # 預設為一般員工
            data.get('manager_id'),
            data.get('password', 'default123'),
            data.get('hire_date', datetime.now().strftime('%Y-%m-%d')),
            data.get('status', 'active'),
            data.get('salary_level', 'junior'),
            data.get('id_number', ''),
            data.get('address', ''),
            data.get('emergency_contact', ''),
            data.get('emergency_phone', ''),
            data.get('photo_url', ''),
            data.get('shift_type', 1)  # 預設為第一個班表
        ))
        
        employee_id = cursor.lastrowid
        conn.commit()
        
        return jsonify({
            'message': '員工新增成功',
            'employee_id': employee_id
        }), 201
        
    except sqlite3.IntegrityError as e:
        error_msg = str(e)
        if "FOREIGN KEY constraint failed" in error_msg:
            return jsonify({'error': '資料關聯錯誤，請檢查部門、角色或主管設定'}), 400
        elif "UNIQUE constraint failed" in error_msg:
            return jsonify({'error': '員工編號已存在，請使用其他編號'}), 400
        else:
            return jsonify({'error': f'資料完整性錯誤: {error_msg}'}), 400
    except Exception as e:
        logging.error(f"新增員工錯誤: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route("/api/employees/<int:employee_id>", methods=["GET", "PUT", "DELETE"])
def manage_employee(employee_id):
    """管理單一員工資料"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 取得特定員工資料
            cursor.execute(
                """
                SELECT e.id, e.name, e.employee_id, e.department_id, d.name as department_name, 
                e.position, e.email, e.phone, e.password, e.hire_date, e.status,
                e.salary_level, e.id_number, e.address, e.emergency_contact, 
                e.emergency_phone, e.role_id, p.role_name, e.manager_id, m.name as manager_name, e.shift_type
                FROM employees e
                JOIN departments d ON e.department_id = d.id
                LEFT JOIN permissions p ON e.role_id = p.id
                LEFT JOIN employees m ON e.manager_id = m.id
                WHERE e.id = ?
            """,
                (employee_id,),
            )

            row = cursor.fetchone()
            if not row:
                return jsonify({"error": "找不到員工資料"}), 404

            columns = [col[0] for col in cursor.description]
            employee = dict(zip(columns, row))
            
            # 確保所有欄位都存在，即使是NULL值
            expected_fields = [
                'id', 'name', 'employee_id', 'department_id', 'department_name',
                'position', 'email', 'phone', 'password', 'hire_date', 'status',
                'salary_level', 'id_number', 'address', 'emergency_contact',
                'emergency_phone', 'role_id', 'role_name', 'manager_id', 'manager_name'
            ]
            
            for field in expected_fields:
                if field not in employee:
                    employee[field] = None
            
            return jsonify(employee)

        elif request.method == "PUT":
            # 更新員工資料
            data = request.json
            
            # 驗證外鍵約束
            if "department_id" in data and data["department_id"]:
                cursor.execute("SELECT id FROM departments WHERE id = ?", (data["department_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的部門不存在"}), 400
            
            if "role_id" in data and data["role_id"]:
                cursor.execute("SELECT id FROM permissions WHERE id = ?", (data["role_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的角色不存在"}), 400
            
            if "manager_id" in data and data["manager_id"]:
                cursor.execute("SELECT id FROM employees WHERE id = ?", (data["manager_id"],))
                if not cursor.fetchone():
                    return jsonify({"error": "指定的主管不存在"}), 400
                
                # 防止員工設定自己為主管
                if str(data["manager_id"]) == str(employee_id):
                    return jsonify({"error": "員工不能設定自己為主管"}), 400
            
            set_clause = []
            params = []

            # 處理所有可更新的欄位
            updatable_fields = [
                "name", "employee_id", "department_id", "position", "email", "phone",
                "hire_date", "status", "salary_level", "id_number", "address", 
                "emergency_contact", "emergency_phone", "role_id", "manager_id", "photo_url", "shift_type"
            ]

            for field in updatable_fields:
                if field in data:
                    # 處理空值情況
                    value = data[field]
                    if value == "" or value is None:
                        if field in ["manager_id", "role_id"]:
                            value = None
                        elif field in ["department_id"]:
                            continue  # 部門ID不能為空，跳過
                    set_clause.append(f"{field} = ?")
                    params.append(value)

            # 特殊處理密碼欄位（只有提供且非空時才更新）
            if "password" in data and data["password"] and data["password"].strip():
                set_clause.append("password = ?")
                params.append(data["password"])

            if not set_clause:
                return jsonify({"error": "未提供更新資料"}), 400

            params.append(employee_id)
            
            try:
                cursor.execute(
                    f"""
                    UPDATE employees
                    SET {', '.join(set_clause)}
                    WHERE id = ?
                """,
                    params,
                )

                if cursor.rowcount == 0:
                    return jsonify({"error": "員工不存在或無變更"}), 404

                conn.commit()
                logging.info(f"成功更新員工 ID {employee_id} 的資料")
                return jsonify({"message": "員工資料更新成功"})
                
            except sqlite3.IntegrityError as e:
                error_msg = str(e)
                if "FOREIGN KEY constraint failed" in error_msg:
                    return jsonify({"error": "資料關聯錯誤，請檢查部門、角色或主管設定"}), 400
                elif "UNIQUE constraint failed" in error_msg:
                    return jsonify({"error": "員工編號已存在，請使用其他編號"}), 400
                else:
                    return jsonify({"error": f"資料完整性錯誤: {error_msg}"}), 400

        elif request.method == "DELETE":
            # 刪除員工資料
            cursor.execute("DELETE FROM employees WHERE id = ?", (employee_id,))

            if cursor.rowcount == 0:
                return jsonify({"error": "找不到員工資料"}), 404

            conn.commit()
            logging.info(f"成功刪除員工 ID {employee_id}")
            return jsonify({"message": "員工刪除成功"})
    except sqlite3.Error as e:
        logging.error(f"管理員工資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/employees/managers", methods=["GET"])
def get_managers():
    """獲取可以作為審核人員的主管列表"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取有主管權限的員工（role_id <= 2 表示系統管理員或部門主管）
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name,
                   p.role_name, e.department_id
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            LEFT JOIN permissions p ON e.role_id = p.id
            WHERE e.role_id <= 2 OR e.id IN (
                SELECT DISTINCT manager_id FROM employees WHERE manager_id IS NOT NULL
            )
            ORDER BY e.department_id, e.name
        """)
        
        columns = [col[0] for col in cursor.description]
        managers = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'managers': managers,
            'total': len(managers)
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取主管列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/employees/substitutes/<int:employee_id>", methods=["GET"])
def get_substitutes(employee_id):
    """獲取可以作為代理人的員工列表"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 先獲取當前員工的部門資訊
        cursor.execute("""
            SELECT department_id FROM employees WHERE id = ?
        """, (employee_id,))
        
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "找不到員工資料"}), 404
            
        current_dept_id = result[0]
        
        # 獲取同部門或有權限的員工作為代理人
        cursor.execute("""
            SELECT e.id, e.name, e.employee_id, e.position, d.name as department_name
            FROM employees e
            JOIN departments d ON e.department_id = d.id
            WHERE e.id != ? AND (
                e.department_id = ? OR 
                e.role_id <= 2
            )
            ORDER BY 
                CASE WHEN e.department_id = ? THEN 0 ELSE 1 END,
                e.name
        """, (employee_id, current_dept_id, current_dept_id))
        
        columns = [col[0] for col in cursor.description]
        substitutes = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            'substitutes': substitutes,
            'total': len(substitutes)
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取代理人列表錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 部門管理 ======
@app.route("/api/departments", methods=["GET", "POST"])
def manage_departments():
    """管理部門資料"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得所有部門資料
            cursor.execute(
                """
                SELECT d.id, d.name, d.manager_id, e.name as manager_name, d.description
                FROM departments d
                LEFT JOIN employees e ON d.manager_id = e.id
            """
            )

            columns = [col[0] for col in cursor.description]
            departments = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(departments)
        else:
            # 新增部門資料
            data = request.json
            required_fields = ["name"]

            if not all(field in data for field in required_fields):
                return jsonify({"error": "缺少必要參數"}), 400

            cursor.execute(
                """
                INSERT INTO departments (name, manager_id, description)
                VALUES (?, ?, ?)
            """,
                (data["name"], data.get("manager_id"), data.get("description")),
            )

            conn.commit()
            logging.info(f"成功新增部門 {data['name']}")
            return jsonify({"message": "部門新增成功", "id": cursor.lastrowid})
    except sqlite3.Error as e:
        logging.error(f"管理部門資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/<int:dept_id>", methods=["GET", "PUT", "DELETE"])
def manage_single_department(dept_id):
    """管理單一部門"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("SELECT * FROM departments WHERE id = ?", (dept_id,))
            dept = cursor.fetchone()
            if not dept:
                return jsonify({"error": "部門不存在"}), 404
            
            columns = [col[0] for col in cursor.description]
            return jsonify(dict(zip(columns, dept)))
        
        elif request.method == "PUT":
            data = request.json
            
            # 檢查是否為審核操作
            if "status" in data and data["status"] in ["approved", "rejected"]:
                # 審核請假
                status = data.get("status")
                comment = data.get("comment", "")
                
                cursor.execute("""
                    UPDATE leaves 
                    SET status = ?, comment = ?, approved_at = ?
                    WHERE id = ?
                """, (status, comment, datetime.now(), dept_id))
                
                if cursor.rowcount == 0:
                    return jsonify({"error": "請假記錄不存在"}), 404
                
                conn.commit()
                logging.info(f"請假 {dept_id} 審核為 {status}")
                return jsonify({"message": f"請假{status}成功"})
                
            else:
                # 更新請假申請
                cursor.execute("SELECT status FROM leaves WHERE id = ?", (dept_id,))
                result = cursor.fetchone()
                
                if not result:
                    return jsonify({"error": "請假記錄不存在"}), 404
                
                if result[0] != 'pending':
                    return jsonify({"error": "只能編輯待審批的請假申請"}), 400
                
                cursor.execute("""
                    UPDATE leaves 
                    SET leave_type = ?, start_date = ?, end_date = ?, reason = ?
                    WHERE id = ?
                """, (
                    data.get("leave_type"),
                    data.get("start_date"),
                    data.get("end_date"),
                    data.get("reason"),
                    dept_id
                ))
                
                if cursor.rowcount == 0:
                    return jsonify({"error": "更新失敗"}), 404
                
                conn.commit()
                logging.info(f"請假記錄 {dept_id} 已更新")
                return jsonify({"message": "請假申請更新成功"})
        
        elif request.method == "DELETE":
            cursor.execute("DELETE FROM leaves WHERE id = ?", (dept_id,))
            
            if cursor.rowcount == 0:
                return jsonify({"error": "刪除失敗"}), 404
            
            conn.commit()
            logging.info(f"請假記錄 {dept_id} 已被撤回")
            return jsonify({"message": "請假申請已撤回"})
        
    except sqlite3.Error as e:
        logging.error(f"管理請假記錄錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 排班管理模組 ======
@app.route("/api/schedules/employee/<int:employee_id>", methods=["GET"])
def get_employee_schedule(employee_id):
    """獲取員工排班"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取日期範圍
        start_date = request.args.get('start_date', datetime.now().strftime("%Y-%m-%d"))
        end_date = request.args.get('end_date', (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"))
        
        cursor.execute("""
            SELECT * FROM schedules 
            WHERE employee_id = ? AND shift_date BETWEEN ? AND ?
            ORDER BY shift_date, start_time
        """, (employee_id, start_date, end_date))
        
        columns = [col[0] for col in cursor.description]
        schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        return jsonify({
            "employee_id": employee_id,
            "start_date": start_date,
            "end_date": end_date,
            "schedules": schedules
        })
        
    except sqlite3.Error as e:
        logging.error(f"查詢員工排班錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 數據分析模組 ======

@app.route("/api/analytics/attendance-trends", methods=["GET"])
def get_attendance_trends():
    """獲取出勤趨勢分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        days = int(request.args.get('days', 7))
        
        # 獲取最近N天的出勤統計
        cursor.execute("""
            SELECT 
                strftime('%w', check_in) as day_of_week,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent,
                COUNT(*) as total
            FROM attendance 
            WHERE DATE(check_in) >= DATE('now', '-{} days')
            GROUP BY strftime('%w', check_in)
            ORDER BY day_of_week
        """.format(days))
        
        results = cursor.fetchall()
        
        # 初始化週數據
        week_data = {
            'labels': ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
            'normal': [0] * 7,
            'late': [0] * 7,
            'absent': [0] * 7
        }
        
        # 填充實際數據
        for row in results:
            day_index = int(row[0])
            week_data['normal'][day_index] = row[1]
            week_data['late'][day_index] = row[2]
            week_data['absent'][day_index] = row[3]
        
        # 計算摘要統計
        total_attendance = sum(week_data['normal'])
        total_late = sum(week_data['late'])
        total_absent = sum(week_data['absent'])
        total_records = total_attendance + total_late + total_absent
        
        summary = {
            'totalAttendance': total_attendance,
            'onTimeRate': round((total_attendance / max(total_records, 1)) * 100, 1),
            'avgWorkHours': 8.2,  # 可以從實際工時計算
            'absentRate': round((total_absent / max(total_records, 1)) * 100, 1)
        }
        
        return jsonify({
            'labels': week_data['labels'],
            'normal': week_data['normal'],
            'late': week_data['late'],
            'absent': week_data['absent'],
            'summary': summary
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取出勤趨勢錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/attendance/trends", methods=["GET"])
def get_attendance_trends_simple():
    """獲取考勤趨勢分析（簡化版）"""
    return get_attendance_trends()


@app.route("/api/analytics/department-stats", methods=["GET"])
def get_department_stats():
    """獲取部門統計分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取各部門出勤統計
        cursor.execute("""
            SELECT 
                d.name as department,
                COUNT(a.id) as total_records,
                SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            LEFT JOIN attendance a ON e.id = a.employee_id
            WHERE a.check_in >= DATE('now', '-30 days')
            GROUP BY d.id, d.name
            ORDER BY d.name
        """)
        
        results = cursor.fetchall()
        
        labels = []
        attendance_rates = []
        
        for row in results:
            department = row[0]
            total_records = row[1] or 0
            normal_count = row[2] or 0
            
            attendance_rate = round((normal_count / max(total_records, 1)) * 100, 1)
            
            labels.append(department)
            attendance_rates.append(attendance_rate)
        
        return jsonify({
            'labels': labels,
            'attendanceRates': attendance_rates
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取部門統計錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/time-distribution", methods=["GET"])
def get_time_distribution():
    """獲取打卡時間分布分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取打卡時間分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN strftime('%H:%M', check_in) BETWEEN '08:00' AND '08:30' THEN '8:00-8:30'
                    WHEN strftime('%H:%M', check_in) BETWEEN '08:30' AND '09:00' THEN '8:30-9:00'
                    WHEN strftime('%H:%M', check_in) BETWEEN '09:00' AND '09:30' THEN '9:00-9:30'
                    ELSE '9:30+'
                END as time_range,
                COUNT(*) as count
            FROM attendance 
            WHERE check_in >= DATE('now', '-30 days')
            GROUP BY time_range
            ORDER BY time_range
        """)
        
        results = cursor.fetchall()
        
        # 初始化時間分布數據
        time_data = {
            '8:00-8:30': 0,
            '8:30-9:00': 0,
            '9:00-9:30': 0,
            '9:30+': 0
        }
        
        # 填充實際數據
        for row in results:
            time_range = row[0]
            count = row[1]
            time_data[time_range] = count
        
        return jsonify({
            'labels': list(time_data.keys()),
            'data': list(time_data.values())
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取時間分布錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/leave-stats", methods=["GET"])
def get_leave_stats():
    """獲取請假統計分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取請假類型統計
        cursor.execute("""
            SELECT 
                leave_type,
                COUNT(*) as count
            FROM leaves 
            WHERE created_at >= DATE('now', '-30 days')
            GROUP BY leave_type
        """)
        
        results = cursor.fetchall()
        
        # 計算總數
        total_leaves = sum(row[1] for row in results)
        
        # 生成統計數據
        leave_stats = []
        for row in results:
            leave_type = row[0]
            count = row[1]
            percentage = round((count / max(total_leaves, 1)) * 100, 1)
            
            leave_stats.append({
                'type': leave_type,
                'count': count,
                'percentage': percentage
            })
        
        return jsonify(leave_stats)
        
    except sqlite3.Error as e:
        logging.error(f"獲取請假統計錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/efficiency", methods=["GET"])
def get_efficiency_metrics():
    """獲取效率指標分析"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 計算準時率
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as on_time
            FROM attendance 
            WHERE check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_records = result[0] or 0
        on_time_records = result[1] or 0
        punctuality_rate = round((on_time_records / max(total_records, 1)) * 100, 1)
        
        # 計算加班頻率（假設超過18:00為加班）
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkouts,
                SUM(CASE WHEN strftime('%H', check_out) >= '18' THEN 1 ELSE 0 END) as overtime_count
            FROM attendance 
            WHERE check_out IS NOT NULL 
            AND check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_checkouts = result[0] or 0
        overtime_count = result[1] or 0
        overtime_frequency = round((overtime_count / max(total_checkouts, 1)) * 100, 1)
        
        # 計算滿勤率（無缺勤記錄的員工比例）
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT e.id) as total_employees,
                COUNT(DISTINCT e.id) - COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN e.id END) as full_attendance_employees
            FROM employees e
            LEFT JOIN attendance a ON e.id = a.employee_id 
            AND a.check_in >= DATE('now', '-30 days')
        """)
        
        result = cursor.fetchone()
        total_employees = result[0] or 0
        full_attendance_employees = result[1] or 0
        full_attendance_rate = round((full_attendance_employees / max(total_employees, 1)) * 100, 1)
        
        # 綜合工作效率（基於準時率和滿勤率的加權平均）
        work_efficiency = round((punctuality_rate * 0.6 + full_attendance_rate * 0.4), 1)
        
        return jsonify({
            'workEfficiency': work_efficiency,
            'punctualityRate': punctuality_rate,
            'overtimeFrequency': overtime_frequency,
            'fullAttendanceRate': full_attendance_rate
        })
        
    except sqlite3.Error as e:
        logging.error(f"獲取效率指標錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/analytics/export", methods=["GET"])
def export_analytics_report():
    """匯出分析報告"""
    report_type = request.args.get('type', 'summary')
    
    # 這裡可以實現不同類型的報告匯出
    # 目前返回一個簡單的JSON響應
    return jsonify({
        "message": f"正在準備{report_type}報告...",
        "download_url": f"/downloads/analytics_{report_type}_{datetime.now().strftime('%Y%m%d')}.pdf"
    })


# ====== 系統設定管理模組 ======
@app.route("/api/settings", methods=["GET", "POST"])
def manage_system_settings():
    """系統設定管理"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取系統設定
            cursor.execute("SELECT * FROM system_settings ORDER BY category, setting_key")
            columns = [col[0] for col in cursor.description]
            settings = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # 按類別組織設定
            organized_settings = {}
            for setting in settings:
                category = setting['category']
                if category not in organized_settings:
                    organized_settings[category] = {}
                organized_settings[category][setting['setting_key']] = setting['setting_value']
            
            return jsonify(organized_settings)
            
        else:
            # 更新系統設定
            data = request.json
            
            for category, settings in data.items():
                for key, value in settings.items():
                    cursor.execute("""
                        INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                        VALUES (?, ?, ?, ?)
                    """, (category, key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("系統設定已更新")
            return jsonify({"message": "設定保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"系統設定管理錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/settings/attendance-rules", methods=["GET", "POST"])
def manage_attendance_rules():
    """考勤規則設定"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT setting_key, setting_value FROM system_settings 
                WHERE category = 'attendance_rules'
            """)
            rules = dict(cursor.fetchall())
            
            # 設定預設值
            default_rules = {
                'late_tolerance_minutes': '10',
                'early_leave_tolerance_minutes': '10',
                'absent_mark_hours': '2',
                'overtime_minimum_hours': '1',
                'auto_mark_absent': 'true',
                'require_location': 'false',
                'allow_mobile_checkin': 'true'
            }
            
            # 合併預設值和實際設定
            for key, default_value in default_rules.items():
                if key not in rules:
                    rules[key] = default_value
            
            return jsonify(rules)
            
        else:
            # 更新考勤規則
            data = request.json
            
            for key, value in data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, ?)
                """, ('attendance_rules', key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("考勤規則已更新")
            return jsonify({"message": "考勤規則保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"考勤規則設定錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/settings/notifications", methods=["GET", "POST"])
def manage_notification_settings():
    """通知設定管理"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT setting_key, setting_value FROM system_settings 
                WHERE category = 'notifications'
            """)
            settings = dict(cursor.fetchall())
            
            # 設定預設值
            default_settings = {
                'email_enabled': 'true',
                'sms_enabled': 'false',
                'push_enabled': 'true',
                'late_notification': 'true',
                'absent_notification': 'true',
                'leave_approval_notification': 'true',
                'system_maintenance_notification': 'true'
            }
            
            for key, default_value in default_settings.items():
                if key not in settings:
                    settings[key] = default_value
            
            return jsonify(settings)
            
        else:
            # 更新通知設定
            data = request.json
            
            for key, value in data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, ?)
                """, ('notifications', key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("通知設定已更新")
            return jsonify({"message": "通知設定保存成功"})
            
    except sqlite3.Error as e:
        logging.error(f"通知設定錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/permissions/roles", methods=["GET", "POST"])
def manage_permissions():
    """管理權限角色"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得所有權限角色
            cursor.execute(
                """
                SELECT p.id, p.role_name, p.permission_level, p.description,
                       COUNT(e.id) as user_count
                FROM permissions p
                LEFT JOIN employees e ON p.id = e.role_id
                GROUP BY p.id, p.role_name, p.permission_level, p.description
                ORDER BY p.permission_level DESC
            """
            )

            columns = [col[0] for col in cursor.description]
            roles = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify({"roles": roles})
        else:
            # 新增權限角色
            data = request.json
            required_fields = ["role_name", "permission_level"]

            if not all(field in data for field in required_fields):
                return jsonify({"error": "缺少必要參數"}), 400

            cursor.execute(
                """
                INSERT INTO permissions (role_name, permission_level, description)
                VALUES (?, ?, ?)
            """,
                (
                    data["role_name"],
                    data["permission_level"],
                    data.get("description", ""),
                ),
            )

            conn.commit()
            logging.info(f"成功新增權限角色 {data['role_name']}")
            return jsonify({"message": "權限角色新增成功", "id": cursor.lastrowid})
    except sqlite3.Error as e:
        logging.error(f"管理權限角色錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/permissions", methods=["GET", "POST"])
def manage_department_permissions():
    """管理部門權限"""
    conn = create_connection()
    try:
        cursor = conn.cursor()

        if request.method == "GET":
            # 取得部門權限設定
            cursor.execute(
                """
                SELECT d.id, d.name, d.manager_id, e.name as manager_name
                FROM departments d
                LEFT JOIN employees e ON d.manager_id = e.id
            """
            )

            columns = [col[0] for col in cursor.description]
            departments = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return jsonify(departments)
        else:
            # 更新部門權限設定
            data = request.json
            dept_id = data.get("department_id")
            manager_id = data.get("manager_id")

            if not dept_id:
                return jsonify({"error": "缺少部門ID"}), 400

            cursor.execute(
                """
                UPDATE departments SET manager_id = ? WHERE id = ?
            """,
                (manager_id, dept_id),
            )

            conn.commit()
            logging.info(f"成功更新部門 {dept_id} 的權限設定")
            return jsonify({"message": "部門權限設定成功"})
    except sqlite3.Error as e:
        logging.error(f"管理部門權限錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/departments/stats", methods=["GET"])
def get_departments_stats():
    """獲取部門統計資料"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取部門員工統計
        cursor.execute("""
            SELECT d.id, d.name, 
                   COUNT(e.id) as total_employees,
                   SUM(CASE WHEN e.status = 'active' THEN 1 ELSE 0 END) as active_employees,
                   SUM(CASE WHEN e.status = 'trial' THEN 1 ELSE 0 END) as trial_employees
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id
            GROUP BY d.id, d.name
            ORDER BY d.name
        """)
        
        departments = []
        for row in cursor.fetchall():
            departments.append({
                "id": row[0],
                "name": row[1],
                "total_employees": row[2],
                "active_employees": row[3],
                "trial_employees": row[4]
            })
        
        conn.close()
        return jsonify({"departments": departments})
        
    except Exception as e:
        logging.error(f"獲取部門統計失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/reports/dashboard", methods=["GET"])
def get_dashboard_reports():
    """獲取儀表板報表數據"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取今日統計
        today = datetime.now().strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_checkins,
                SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal_count,
                SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
                SUM(CASE WHEN status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_count,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM attendance
            WHERE DATE(check_in) = ?
        """, (today,))
        
        today_stats = cursor.fetchone()
        
        # 獲取本週統計
        week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT COUNT(*) as week_total
            FROM attendance
            WHERE DATE(check_in) >= ?
        """, (week_start,))
        
        week_stats = cursor.fetchone()
        
        # 獲取待審批請假
        cursor.execute("""
            SELECT COUNT(*) as pending_leaves
            FROM leaves
            WHERE status = 'pending'
        """)
        
        leave_stats = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            "today": {
                "total_checkins": today_stats[0] if today_stats else 0,
                "normal_count": today_stats[1] if today_stats else 0,
                "late_count": today_stats[2] if today_stats else 0,
                "early_leave_count": today_stats[3] if today_stats else 0,
                "absent_count": today_stats[4] if today_stats else 0
            },
            "week": {
                "total_checkins": week_stats[0] if week_stats else 0
            },
            "leaves": {
                "pending_count": leave_stats[0] if leave_stats else 0
            }
        })
        
    except Exception as e:
        logging.error(f"獲取儀表板報表失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 審核作業模組 ======
@app.route("/api/approval/leaves", methods=["GET"])
def get_pending_leaves():
    """獲取待審核的請假申請"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取待審核的請假申請，包含申請人、代理人和審核主管資訊
        cursor.execute("""
            SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id,
                   e.position as employee_position, d.name as department_name, 
                   l.leave_type, l.start_date, l.end_date, l.reason, l.created_at, 
                   l.status, l.approver_id, a.name as approver_name, a.position as approver_position,
                   l.substitute_id, s.name as substitute_name, s.position as substitute_position,
                   e.photo_url
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON l.approver_id = a.id
            LEFT JOIN employees s ON l.substitute_id = s.id
            WHERE l.status = 'pending'
            ORDER BY l.created_at DESC
        """)
        
        columns = [col[0] for col in cursor.description]
        leaves = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        # 計算請假天數
        for leave in leaves:
            if leave['start_date'] and leave['end_date']:
                start = datetime.strptime(leave['start_date'], '%Y-%m-%d')
                end = datetime.strptime(leave['end_date'], '%Y-%m-%d')
                leave['duration_days'] = (end - start).days + 1
            else:
                leave['duration_days'] = 1
        
        conn.close()
        return jsonify({
            'leaves': leaves,
            'total': len(leaves)
        })
        
    except Exception as e:
        logging.error(f"獲取待審核請假失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/approval/leaves/<int:leave_id>", methods=["POST"])
def approve_leave(leave_id):
    """審核請假申請"""
    try:
        data = request.json
        action = data.get('action')  # 'approve' 或 'reject'
        comment = data.get('comment', '')
        approver_id = data.get('approver_id', 1)  # 應該從登入狀態獲取
        
        if action not in ['approve', 'reject']:
            return jsonify({"error": "無效的審核動作"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查請假申請是否存在且為待審核狀態
        cursor.execute("""
            SELECT l.id, l.employee_id, e.name as employee_name, l.leave_type, 
                   l.start_date, l.end_date, l.status
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            WHERE l.id = ?
        """, (leave_id,))
        
        leave = cursor.fetchone()
        if not leave:
            return jsonify({"error": "請假申請不存在"}), 404
        
        if leave[6] != 'pending':  # status欄位
            return jsonify({"error": "此請假申請已被審核"}), 400
        
        # 更新請假狀態
        new_status = 'approved' if action == 'approve' else 'rejected'
        cursor.execute("""
            UPDATE leaves 
            SET status = ?, comment = ?, approver_id = ?, approved_at = ?
            WHERE id = ?
        """, (new_status, comment, approver_id, datetime.now(), leave_id))
        
        conn.commit()
        conn.close()
        
        action_text = '批准' if action == 'approve' else '拒絕'
        logging.info(f"請假申請 {leave_id} 已被{action_text}")
        
        return jsonify({
            "message": f"請假申請已{action_text}",
            "leave_id": leave_id,
            "status": new_status
        })
        
    except Exception as e:
        logging.error(f"審核請假失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/approval/stats", methods=["GET"])
def get_approval_stats():
    """獲取審核統計資料"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取各種狀態的請假統計
        cursor.execute("""
            SELECT 
                status,
                COUNT(*) as count,
                leave_type
            FROM leaves
            WHERE created_at >= DATE('now', '-30 days')
            GROUP BY status, leave_type
        """)
        
        results = cursor.fetchall()
        
        # 組織統計資料
        stats = {
            'pending': 0,
            'approved': 0,
            'rejected': 0,
            'by_type': {}
        }
        
        for row in results:
            status, count, leave_type = row
            if status in stats:
                stats[status] += count
            
            if leave_type not in stats['by_type']:
                stats['by_type'][leave_type] = {'pending': 0, 'approved': 0, 'rejected': 0}
            stats['by_type'][leave_type][status] = count
        
        # 獲取今日待審核數量
        cursor.execute("""
            SELECT COUNT(*) FROM leaves 
            WHERE status = 'pending' AND DATE(created_at) = DATE('now')
        """)
        today_pending = cursor.fetchone()[0]
        
        stats['today_pending'] = today_pending
        
        conn.close()
        return jsonify(stats)
        
    except Exception as e:
        logging.error(f"獲取審核統計失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ====== 基本資料管理模組 ======
@app.route("/api/masterdata/<string:table_name>", methods=["GET", "POST"])
def manage_masterdata(table_name):
    """
    基本資料管理通用API
    
    支援的資料表：
    - education_levels: 學歷
    - positions: 職位
    - leave_types: 假別
    - salary_grades: 薪資等級
    - work_locations: 工作地點
    - skills: 技能
    """
    # 定義允許的資料表和對應的中文名稱
    allowed_tables = {
        'education_levels': '學歷',
        'positions': '職位',
        'leave_types': '假別',
        'salary_grades': '薪資等級',
        'work_locations': '工作地點',
        'skills': '技能',
        'clock_status_types': '打卡狀態設定',
        'employee-status': '員工狀態設定'
    }
    
    if table_name not in allowed_tables:
        return jsonify({"error": "不支援的資料表"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 特殊處理員工狀態（從 system_settings 表查詢）
            if table_name == 'employee-status':
                cursor.execute("""
                    SELECT setting_key as code, setting_value as name, description
                    FROM system_settings 
                    WHERE category = 'employee_status'
                    ORDER BY 
                        CASE setting_key 
                            WHEN 'active' THEN 1
                            WHEN 'trial' THEN 2
                            WHEN 'inactive' THEN 3
                            WHEN 'leave' THEN 4
                            ELSE 5
                        END
                """)
                
                columns = [col[0] for col in cursor.description]
                items = [dict(zip(columns, row)) for row in cursor.fetchall()]
                
                # 如果沒有設定，返回預設狀態
                if not items:
                    items = [
                        {'code': 'active', 'name': '在職', 'description': '正常在職狀態'},
                        {'code': 'trial', 'name': '試用期', 'description': '試用期員工'},
                        {'code': 'inactive', 'name': '停職', 'description': '暫時停職狀態'},
                        {'code': 'leave', 'name': '離職', 'description': '已離職員工'}
                    ]
                
                return jsonify({
                    'items': items,
                    'total': len(items),
                    'table_name': 'employee_status',
                    'display_name': '員工狀態設定'
                })
            
            # 一般資料表處理
            order_by_clause = "name"  # 預設按名稱排序
            if table_name in ['education_levels', 'positions', 'salary_grades']:
                order_by_clause = "level_order"
            elif table_name == 'clock_status_types':
                order_by_clause = "sort_order, status_code"
            
            cursor.execute(f"""
                SELECT * FROM {table_name} 
                WHERE is_active = 1 
                ORDER BY {order_by_clause}
            """)
            
            columns = [col[0] for col in cursor.description]
            items = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            return jsonify({
                'items': items,
                'total': len(items),
                'table_name': table_name,
                'display_name': allowed_tables[table_name]
            })
            
        else:  # POST
            # 新增資料
            data = request.json
            
            # 根據不同資料表處理不同欄位
            if table_name == 'education_levels':
                required_fields = ['name', 'level_order']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO education_levels (name, level_order, description)
                    VALUES (?, ?, ?)
                """, (data['name'], data['level_order'], data.get('description', '')))
                
            elif table_name == 'positions':
                required_fields = ['name', 'level_order']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO positions (name, department_id, level_order, salary_range_min, 
                                         salary_range_max, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (data['name'], data.get('department_id'), data['level_order'],
                      data.get('salary_range_min'), data.get('salary_range_max'), 
                      data.get('description', '')))
                
            elif table_name == 'leave_types':
                required_fields = ['name', 'code']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO leave_types (name, code, max_days_per_year, is_paid, 
                                           requires_approval, advance_notice_days, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (data['name'], data['code'], data.get('max_days_per_year'),
                      data.get('is_paid', 1), data.get('requires_approval', 1),
                      data.get('advance_notice_days', 1), data.get('description', '')))
                
            elif table_name == 'salary_grades':
                required_fields = ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO salary_grades (name, grade_code, level_order, min_salary, max_salary, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (data['name'], data['grade_code'], data['level_order'], data['min_salary'],
                      data['max_salary'], data.get('description', '')))
                
            elif table_name == 'work_locations':
                required_fields = ['name']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO work_locations (name, address, city, country, timezone, 
                                              is_remote, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (data['name'], data.get('address', ''), data.get('city', ''),
                      data.get('country', '台灣'), data.get('timezone', 'Asia/Taipei'),
                      data.get('is_remote', 0), data.get('description', '')))
                
            elif table_name == 'skills':
                required_fields = ['name']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO skills (name, category, description)
                    VALUES (?, ?, ?)
                """, (data['name'], data.get('category', ''), data.get('description', '')))
                
            elif table_name == 'clock_status_types':
                required_fields = ['status_code', 'status_name']
                # 檢查必填欄位
                for field in required_fields:
                    if not data.get(field):
                        return jsonify({"error": f"缺少必填欄位: {field}"}), 400
                
                cursor.execute("""
                    INSERT INTO clock_status_types (status_code, status_name, description, sort_order, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, (data['status_code'], data['status_name'], data.get('description', ''), 
                      data.get('sort_order', 0), data.get('is_active', 1)))
            
            # 檢查必填欄位
            for field in required_fields:
                if not data.get(field):
                    return jsonify({"error": f"缺少必填欄位: {field}"}), 400
            
            conn.commit()
            item_id = cursor.lastrowid
            
            # 日誌記錄
            log_name = data.get('name') or data.get('status_name') or str(item_id)
            logging.info(f"成功新增{allowed_tables[table_name]}資料: {log_name}")
            return jsonify({
                "message": f"{allowed_tables[table_name]}新增成功",
                "id": item_id
            }), 201
            
    except sqlite3.IntegrityError as e:
        error_msg = str(e)
        if "UNIQUE constraint failed" in error_msg:
            return jsonify({"error": "資料已存在，請檢查名稱或代碼是否重複"}), 400
        else:
            return jsonify({"error": f"資料完整性錯誤: {error_msg}"}), 400
    except Exception as e:
        logging.error(f"管理{allowed_tables[table_name]}資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@app.route("/api/masterdata/<string:table_name>/<int:item_id>", methods=["GET", "PUT", "DELETE"])
def manage_single_masterdata(table_name, item_id):
    """管理單一基本資料項目"""
    allowed_tables = {
        'education_levels': '學歷',
        'positions': '職位',
        'leave_types': '假別',
        'salary_grades': '薪資等級',
        'work_locations': '工作地點',
        'skills': '技能',
        'clock_status_types': '打卡狀態設定'
    }
    
    if table_name not in allowed_tables:
        return jsonify({"error": "不支援的資料表"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取單一項目
            cursor.execute(f"SELECT * FROM {table_name} WHERE id = ?", (item_id,))
            row = cursor.fetchone()
            
            if not row:
                return jsonify({"error": "資料不存在"}), 404
            
            columns = [col[0] for col in cursor.description]
            item = dict(zip(columns, row))
            
            return jsonify(item)
            
        elif request.method == "PUT":
            # 更新項目
            data = request.json
            
            # 根據不同資料表處理不同欄位
            if table_name == 'education_levels':
                cursor.execute("""
                    UPDATE education_levels 
                    SET name = ?, level_order = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data['level_order'], data.get('description', ''), item_id))
                
            elif table_name == 'positions':
                cursor.execute("""
                    UPDATE positions 
                    SET name = ?, department_id = ?, level_order = ?, salary_range_min = ?,
                        salary_range_max = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data.get('department_id'), data['level_order'],
                      data.get('salary_range_min'), data.get('salary_range_max'),
                      data.get('description', ''), item_id))
                
            elif table_name == 'leave_types':
                cursor.execute("""
                    UPDATE leave_types 
                    SET name = ?, code = ?, max_days_per_year = ?, is_paid = ?,
                        requires_approval = ?, advance_notice_days = ?, description = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data['code'], data.get('max_days_per_year'),
                      data.get('is_paid', 1), data.get('requires_approval', 1),
                      data.get('advance_notice_days', 1), data.get('description', ''), item_id))
                
            elif table_name == 'salary_grades':
                cursor.execute("""
                    UPDATE salary_grades 
                    SET name = ?, grade_code = ?, level_order = ?, min_salary = ?, max_salary = ?,
                        description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data['grade_code'], data['level_order'], data['min_salary'],
                      data['max_salary'], data.get('description', ''), item_id))
                
            elif table_name == 'work_locations':
                cursor.execute("""
                    UPDATE work_locations 
                    SET name = ?, address = ?, city = ?, country = ?, timezone = ?,
                        is_remote = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data.get('address', ''), data.get('city', ''),
                      data.get('country', '台灣'), data.get('timezone', 'Asia/Taipei'),
                      data.get('is_remote', 0), data.get('description', ''), item_id))
                
            elif table_name == 'skills':
                cursor.execute("""
                    UPDATE skills 
                    SET name = ?, category = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['name'], data.get('category', ''), data.get('description', ''), item_id))
                
            elif table_name == 'clock_status_types':
                cursor.execute("""
                    UPDATE clock_status_types 
                    SET status_code = ?, status_name = ?, description = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (data['status_code'], data['status_name'], data.get('description', ''), 
                      data.get('sort_order', 0), data.get('is_active', 1), item_id))
            
            if cursor.rowcount == 0:
                return jsonify({"error": "資料不存在或無變更"}), 404
            
            conn.commit()
            logging.info(f"成功更新{allowed_tables[table_name]}資料 ID {item_id}")
            return jsonify({"message": f"{allowed_tables[table_name]}更新成功"})
            
        elif request.method == "DELETE":
            # 軟刪除（設為非活躍狀態）
            cursor.execute(f"""
                UPDATE {table_name} 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (item_id,))
            
            if cursor.rowcount == 0:
                return jsonify({"error": "資料不存在"}), 404
            
            conn.commit()
            logging.info(f"成功刪除{allowed_tables[table_name]}資料 ID {item_id}")
            return jsonify({"message": f"{allowed_tables[table_name]}刪除成功"})
            
    except sqlite3.IntegrityError as e:
        error_msg = str(e)
        if "UNIQUE constraint failed" in error_msg:
            return jsonify({"error": "資料已存在，請檢查名稱或代碼是否重複"}), 400
        else:
            return jsonify({"error": f"資料完整性錯誤: {error_msg}"}), 400
    except Exception as e:
        logging.error(f"管理{allowed_tables[table_name]}資料錯誤: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# ====== 打卡狀態類型API ======
@app.route("/api/clock-status-types", methods=["GET"])
def get_clock_status_types():
    """
    獲取打卡狀態類型列表
    
    返回所有啟用的打卡狀態類型，用於打卡機狀態代碼對應
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT status_code, status_name, description, sort_order
            FROM clock_status_types 
            WHERE is_active = 1 
            ORDER BY sort_order, status_code
        """)
        
        status_types = []
        for row in cursor.fetchall():
            status_types.append({
                'code': row[0],
                'name': row[1],
                'description': row[2],
                'sort_order': row[3]
            })
        
        conn.close()
        
        return jsonify({
            'status_types': status_types,
            'total': len(status_types)
        })
        
    except Exception as e:
        logging.error(f"獲取打卡狀態類型失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500



# ====== 健康檢查和系統監控模組 ======
@app.route("/api/health", methods=["GET"])
def health_check():
    """系統健康檢查"""
    try:
        # 檢查資料庫連接
        conn = create_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        conn.close()
        
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": "connected",
            "version": "1.0.0"
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@app.route("/api/health/database", methods=["GET"])
def database_health():
    """資料庫健康檢查"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查主要資料表
        tables = ['employees', 'attendance', 'schedules', 'leaves']
        table_status = {}
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            table_status[table] = count
        
        conn.close()
        
        return jsonify({
            "status": "healthy",
            "tables": table_status,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route("/api/health/system", methods=["GET"])
def system_health():
    """系統資源健康檢查"""
    import psutil
    import os
    
    try:
        # 獲取系統資源使用情況
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return jsonify({
            "status": "healthy",
            "cpu_usage": cpu_percent,
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route("/api/metrics", methods=["GET"])
def get_metrics():
    """獲取系統指標"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取各種統計數據
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 今日考勤統計
        cursor.execute("""
            SELECT COUNT(*) FROM attendance 
            WHERE DATE(check_in) = ?
        """, (today,))
        today_attendance = cursor.fetchone()[0]
        
        # 員工總數
        cursor.execute("SELECT COUNT(*) FROM employees WHERE status = 'active'")
        total_employees = cursor.fetchone()[0]
        
        # 本月請假統計
        this_month = datetime.now().strftime("%Y-%m")
        cursor.execute("""
            SELECT COUNT(*) FROM leaves 
            WHERE strftime('%Y-%m', start_date) = ?
        """, (this_month,))
        monthly_leaves = cursor.fetchone()[0]
        
        # 待審核項目
        cursor.execute("""
            SELECT COUNT(*) FROM leaves 
            WHERE status = 'pending'
        """)
        pending_approvals = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            "today_attendance": today_attendance,
            "total_employees": total_employees,
            "monthly_leaves": monthly_leaves,
            "pending_approvals": pending_approvals,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# ====== 請假管理模組 ======
@app.route("/api/leaves", methods=["GET"])
def get_leaves():
    """
    獲取請假記錄
    
    查詢參數：
    - employee_id: 員工ID (可選)
    - status: 狀態篩選 (可選)
    - start_date: 開始日期 (可選)
    - end_date: 結束日期 (可選)
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 建構查詢條件
        where_conditions = []
        params = []
        
        employee_id = request.args.get('employee_id')
        if employee_id:
            where_conditions.append("l.employee_id = ?")
            params.append(employee_id)
        
        status = request.args.get('status')
        if status:
            where_conditions.append("l.status = ?")
            params.append(status)
        
        start_date = request.args.get('start_date')
        if start_date:
            where_conditions.append("l.start_date >= ?")
            params.append(start_date)
        
        end_date = request.args.get('end_date')
        if end_date:
            where_conditions.append("l.end_date <= ?")
            params.append(end_date)
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 查詢請假記錄
        query = f"""
            SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id,
                   d.name as department_name, l.leave_type, l.start_date, l.end_date,
                   l.reason, l.status, l.created_at, l.approved_at, l.comment,
                   l.approver_id, a.name as approver_name, l.substitute_id, s.name as substitute_name,
                   e.photo_url
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON l.approver_id = a.id
            LEFT JOIN employees s ON l.substitute_id = s.id
            {where_clause}
            ORDER BY l.created_at DESC
        """
        
        cursor.execute(query, params)
        columns = [col[0] for col in cursor.description]
        leaves = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        # 計算請假天數
        for leave in leaves:
            if leave['start_date'] and leave['end_date']:
                start = datetime.strptime(leave['start_date'], '%Y-%m-%d')
                end = datetime.strptime(leave['end_date'], '%Y-%m-%d')
                leave['duration_days'] = (end - start).days + 1
            else:
                leave['duration_days'] = 1
        
        conn.close()
        return jsonify(leaves)
        
    except Exception as e:
        logging.error(f"獲取請假記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/leaves", methods=["POST"])
def create_leave():
    """申請請假"""
    try:
        data = request.json
        
        # 驗證必要欄位
        required_fields = ['employee_id', 'leave_type', 'start_date', 'end_date', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"缺少必要欄位: {field}"}), 400
        
        # 驗證日期格式和邏輯
        try:
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d')
            
            if start_date > end_date:
                return jsonify({"error": "結束日期不能早於開始日期"}), 400
            
            # 檢查是否申請過去的日期
            today = datetime.now().date()
            if start_date.date() < today:
                return jsonify({"error": "不能申請過去的日期"}), 400
                
        except ValueError:
            return jsonify({"error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查員工是否存在
        cursor.execute("SELECT id, name FROM employees WHERE id = ?", (data['employee_id'],))
        employee = cursor.fetchone()
        if not employee:
            return jsonify({"error": "員工不存在"}), 404
        
        # 檢查代理人是否存在（如果提供）
        substitute_id = data.get('substitute_id')
        if substitute_id:
            cursor.execute("SELECT id, name FROM employees WHERE id = ?", (substitute_id,))
            substitute = cursor.fetchone()
            if not substitute:
                return jsonify({"error": "代理人不存在"}), 404
        
        # 檢查審核主管是否存在（如果提供）
        approver_id = data.get('approver_id')
        if approver_id:
            cursor.execute("SELECT id, name FROM employees WHERE id = ?", (approver_id,))
            approver = cursor.fetchone()
            if not approver:
                return jsonify({"error": "審核主管不存在"}), 404
        
        # 插入請假記錄
        cursor.execute("""
            INSERT INTO leaves (
                employee_id, leave_type, start_date, end_date, reason,
                substitute_id, approver_id, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)
        """, (
            data['employee_id'], data['leave_type'], data['start_date'], data['end_date'],
            data['reason'], substitute_id, approver_id, datetime.now()
        ))
        
        leave_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logging.info(f"員工 {employee[1]} (ID: {data['employee_id']}) 申請請假成功，請假ID: {leave_id}")
        
        return jsonify({
            "message": "請假申請提交成功",
            "leave_id": leave_id,
            "status": "pending"
        }), 201
        
    except Exception as e:
        logging.error(f"申請請假失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/leaves/<int:leave_id>", methods=["GET"])
def get_leave(leave_id):
    """獲取單一請假記錄"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT l.id, l.employee_id, e.name as employee_name, e.employee_id as emp_id,
                   d.name as department_name, l.leave_type, l.start_date, l.end_date,
                   l.reason, l.status, l.created_at, l.approved_at, l.comment,
                   l.approver_id, a.name as approver_name, l.substitute_id, s.name as substitute_name,
                   e.photo_url
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN employees a ON l.approver_id = a.id
            LEFT JOIN employees s ON l.substitute_id = s.id
            WHERE l.id = ?
        """, (leave_id,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "請假記錄不存在"}), 404
        
        columns = [col[0] for col in cursor.description]
        leave = dict(zip(columns, row))
        
        # 計算請假天數
        if leave['start_date'] and leave['end_date']:
            start = datetime.strptime(leave['start_date'], '%Y-%m-%d')
            end = datetime.strptime(leave['end_date'], '%Y-%m-%d')
            leave['duration_days'] = (end - start).days + 1
        else:
            leave['duration_days'] = 1
        
        conn.close()
        return jsonify(leave)
        
    except Exception as e:
        logging.error(f"獲取請假記錄失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/leaves/<int:leave_id>", methods=["PUT"])
def update_leave(leave_id):
    """更新請假申請（僅限待審核狀態）"""
    try:
        data = request.json
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查請假記錄是否存在且為待審核狀態
        cursor.execute("SELECT status, employee_id FROM leaves WHERE id = ?", (leave_id,))
        leave = cursor.fetchone()
        if not leave:
            return jsonify({"error": "請假記錄不存在"}), 404
        
        if leave[0] != 'pending':
            return jsonify({"error": "只能修改待審核的請假申請"}), 400
        
        # 驗證日期格式和邏輯（如果提供）
        if 'start_date' in data and 'end_date' in data:
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d')
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d')
                
                if start_date > end_date:
                    return jsonify({"error": "結束日期不能早於開始日期"}), 400
                
                # 檢查是否申請過去的日期
                today = datetime.now().date()
                if start_date.date() < today:
                    return jsonify({"error": "不能申請過去的日期"}), 400
                    
            except ValueError:
                return jsonify({"error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        # 建構更新語句
        update_fields = []
        params = []
        
        allowed_fields = ['leave_type', 'start_date', 'end_date', 'reason', 'substitute_id', 'approver_id']
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                params.append(data[field])
        
        if not update_fields:
            return jsonify({"error": "沒有提供要更新的欄位"}), 400
        
        params.append(leave_id)
        
        cursor.execute(f"""
            UPDATE leaves 
            SET {', '.join(update_fields)}
            WHERE id = ?
        """, params)
        
        conn.commit()
        conn.close()
        
        logging.info(f"請假申請 {leave_id} 更新成功")
        
        return jsonify({
            "message": "請假申請更新成功",
            "leave_id": leave_id
        })
        
    except Exception as e:
        logging.error(f"更新請假申請失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/leaves/<int:leave_id>", methods=["DELETE"])
def cancel_leave(leave_id):
    """撤回請假申請"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查請假記錄是否存在且狀態為待審批
        cursor.execute(
            """
            SELECT status FROM leaves WHERE id = ?
        """,
            (leave_id,),
        )
        
        result = cursor.fetchone()
        if not result:
            return jsonify({"error": "請假記錄不存在"}), 404
            
        if result[0] != 'pending':
            return jsonify({"error": "只能撤回待審批的請假申請"}), 400
        
        # 刪除請假記錄
        cursor.execute(
            """
            DELETE FROM leaves WHERE id = ?
        """,
            (leave_id,),
        )
        
        conn.commit()
        
        return jsonify({"message": "請假申請已撤回"})
        
    except Exception as e:
        logging.error(f"撤回請假失敗: {e}")
        return jsonify({"error": "撤回請假失敗"}), 500
    finally:
        conn.close()


# ====== 班別管理模組 ======
@app.route("/api/shifts", methods=["GET"])
def get_shifts():
    """獲取班別列表"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, name, code, start_time, end_time, break_start_time, 
                   break_duration_minutes, pre_overtime_threshold_minutes, 
                   post_overtime_threshold_minutes, enable_pre_overtime, 
                   enable_post_overtime, auto_calculate_overtime, color_code, 
                   description, is_active, created_at, updated_at
            FROM shifts 
            WHERE is_active = 1 
            ORDER BY name
        """
        )
        
        shifts = []
        for row in cursor.fetchall():
            shift = {
                "id": row[0],
                "name": row[1],
                "code": row[2],
                "start_time": row[3],
                "end_time": row[4],
                "break_start_time": row[5],
                "break_duration_minutes": row[6],
                "pre_overtime_threshold_minutes": row[7],
                "post_overtime_threshold_minutes": row[8],
                "enable_pre_overtime": bool(row[9]),
                "enable_post_overtime": bool(row[10]),
                "auto_calculate_overtime": bool(row[11]),
                "color_code": row[12],
                "description": row[13],
                "is_active": bool(row[14]),
                "created_at": row[15],
                "updated_at": row[16]
            }
            shifts.append(shift)
        
        return jsonify({"shifts": shifts})
        
    except Exception as e:
        logging.error(f"獲取班別列表失敗: {e}")
        return jsonify({"error": "獲取班別列表失敗"}), 500
    finally:
        conn.close()


@app.route("/api/shifts", methods=["POST"])
def create_shift():
    """新增班別"""
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["name", "code", "start_time", "end_time"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    def validate_time_format(time_str):
        """驗證時間格式是否為 HH:MM"""
        from datetime import datetime
        try:
            datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False
    
    # 驗證時間格式
    if not validate_time_format(data["start_time"]):
        return jsonify({"error": "上班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if not validate_time_format(data["end_time"]):
        return jsonify({"error": "下班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if data.get("break_start_time") and not validate_time_format(data["break_start_time"]):
        return jsonify({"error": "休息開始時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別代碼是否已存在
        cursor.execute("SELECT id FROM shifts WHERE code = ?", (data["code"],))
        if cursor.fetchone():
            return jsonify({"error": "班別代碼已存在"}), 400
        
        # 插入新班別
        cursor.execute(
            """
            INSERT INTO shifts (
                name, code, start_time, end_time, break_start_time, 
                break_duration_minutes, pre_overtime_threshold_minutes, 
                post_overtime_threshold_minutes, enable_pre_overtime, 
                enable_post_overtime, auto_calculate_overtime, color_code, 
                description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                data["name"],
                data["code"],
                data["start_time"],
                data["end_time"],
                data.get("break_start_time"),
                data.get("break_duration_minutes", 60),
                data.get("pre_overtime_threshold_minutes", 0),
                data.get("post_overtime_threshold_minutes", 0),
                data.get("enable_pre_overtime", False),
                data.get("enable_post_overtime", False),
                data.get("auto_calculate_overtime", True),
                data.get("color_code", "#3B82F6"),
                data.get("description", "")
            ),
        )
        
        shift_id = cursor.lastrowid
        conn.commit()
        
        return jsonify({"message": "班別新增成功", "id": shift_id}), 201
        
    except Exception as e:
        logging.error(f"新增班別失敗: {e}")
        return jsonify({"error": "新增班別失敗"}), 500
    finally:
        conn.close()


@app.route("/api/shifts/<int:shift_id>", methods=["GET"])
def get_shift(shift_id):
    """獲取單一班別詳情"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, name, code, start_time, end_time, break_start_time, 
                   break_duration_minutes, pre_overtime_threshold_minutes, 
                   post_overtime_threshold_minutes, enable_pre_overtime, 
                   enable_post_overtime, auto_calculate_overtime, color_code, 
                   description, is_active, created_at, updated_at
            FROM shifts 
            WHERE id = ?
        """,
            (shift_id,),
        )
        
        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "班別不存在"}), 404
        
        shift = {
            "id": row[0],
            "name": row[1],
            "code": row[2],
            "start_time": row[3],
            "end_time": row[4],
            "break_start_time": row[5],
            "break_duration_minutes": row[6],
            "pre_overtime_threshold_minutes": row[7],
            "post_overtime_threshold_minutes": row[8],
            "enable_pre_overtime": bool(row[9]),
            "enable_post_overtime": bool(row[10]),
            "auto_calculate_overtime": bool(row[11]),
            "color_code": row[12],
            "description": row[13],
            "is_active": bool(row[14]),
            "created_at": row[15],
            "updated_at": row[16]
        }
        
        
        return jsonify(shift)
        
    except Exception as e:
        logging.error(f"獲取班別詳情失敗: {e}")
        return jsonify({"error": "獲取班別詳情失敗"}), 500
    finally:
        conn.close()


@app.route("/api/shifts/<int:shift_id>", methods=["PUT"])
def update_shift(shift_id):
    """更新班別"""
    data = request.json
    
    # 驗證必要欄位
    required_fields = ["name", "code", "start_time", "end_time"]
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    def validate_time_format(time_str):
        """驗證時間格式是否為 HH:MM"""
        from datetime import datetime
        try:
            datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False
    
    # 驗證時間格式
    if not validate_time_format(data["start_time"]):
        return jsonify({"error": "上班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if not validate_time_format(data["end_time"]):
        return jsonify({"error": "下班時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    if data.get("break_start_time") and not validate_time_format(data["break_start_time"]):
        return jsonify({"error": "休息開始時間格式錯誤，請使用 HH:MM 格式"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別是否存在
        cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
        if not cursor.fetchone():
            return jsonify({"error": "班別不存在"}), 404
        
        # 檢查班別代碼是否與其他班別衝突
        cursor.execute(
            "SELECT id FROM shifts WHERE code = ? AND id != ?", 
            (data["code"], shift_id)
        )
        if cursor.fetchone():
            return jsonify({"error": "班別代碼已存在"}), 400
        
        # 更新班別
        cursor.execute(
            """
            UPDATE shifts SET 
                name = ?, code = ?, start_time = ?, end_time = ?, 
                break_start_time = ?, break_duration_minutes = ?, 
                pre_overtime_threshold_minutes = ?, post_overtime_threshold_minutes = ?, 
                enable_pre_overtime = ?, enable_post_overtime = ?, 
                auto_calculate_overtime = ?, color_code = ?, description = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """,
            (
                data["name"],
                data["code"],
                data["start_time"],
                data["end_time"],
                data.get("break_start_time"),
                data.get("break_duration_minutes", 60),
                data.get("pre_overtime_threshold_minutes", 0),
                data.get("post_overtime_threshold_minutes", 0),
                data.get("enable_pre_overtime", False),
                data.get("enable_post_overtime", False),
                data.get("auto_calculate_overtime", True),
                data.get("color_code", "#3B82F6"),
                data.get("description", ""),
                shift_id
            ),
        )
        
        conn.commit()
        
        return jsonify({"message": "班別更新成功"})
        
    except Exception as e:
        logging.error(f"更新班別失敗: {e}")
        return jsonify({"error": "更新班別失敗"}), 500
    finally:
        conn.close()


@app.route("/api/shifts/<int:shift_id>", methods=["DELETE"])
def delete_shift(shift_id):
    """刪除班別（軟刪除）"""
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 檢查班別是否存在且為活躍狀態
        cursor.execute("SELECT id, is_active FROM shifts WHERE id = ?", (shift_id,))
        shift_record = cursor.fetchone()
        if not shift_record:
            return jsonify({"error": "班別不存在"}), 404
        
        if not shift_record[1]:  # is_active = 0
            return jsonify({"error": "班別已被刪除"}), 400
        
        # 軟刪除班別
        cursor.execute(
            """
            UPDATE shifts SET 
                is_active = 0, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """,
            (shift_id,),
        )
        
        conn.commit()
        
        logging.info(f"班別 {shift_id} 已成功刪除（軟刪除）")
        return jsonify({"message": "班別刪除成功"})
        
    except Exception as e:
        logging.error(f"刪除班別失敗: {e}")
        return jsonify({"error": f"刪除班別失敗: {str(e)}"}), 500
    finally:
        conn.close()


@app.route("/api/shifts/calculate-overtime", methods=["POST"])
def calculate_overtime():
    """計算加班時數"""
    data = request.json
    
    required_fields = ["shift_id", "actual_start_time", "actual_end_time"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    def validate_time_format(time_str):
        """驗證時間格式是否為 HH:MM"""
        try:
            datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False
    
    if not validate_time_format(data["actual_start_time"]):
        return jsonify({"error": "實際上班時間格式錯誤"}), 400
    
    if not validate_time_format(data["actual_end_time"]):
        return jsonify({"error": "實際下班時間格式錯誤"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取班別資訊
        cursor.execute(
            """
            SELECT start_time, end_time, break_duration_minutes,
                   pre_overtime_threshold_minutes, post_overtime_threshold_minutes,
                   enable_pre_overtime, enable_post_overtime, auto_calculate_overtime
            FROM shifts WHERE id = ? AND is_active = 1
        """,
            (data["shift_id"],),
        )
        
        shift = cursor.fetchone()
        if not shift:
            return jsonify({"error": "班別不存在或已停用"}), 404
        
        if not shift[7]:  # auto_calculate_overtime
            return jsonify({
                "pre_overtime_hours": 0,
                "post_overtime_hours": 0,
                "total_overtime_hours": 0,
                "message": "此班別未啟用自動計算加班"
            })
        
        # 計算加班時數的邏輯
        from datetime import timedelta
        
        # 解析時間
        shift_start = datetime.strptime(shift[0], "%H:%M").time()
        shift_end = datetime.strptime(shift[1], "%H:%M").time()
        actual_start = datetime.strptime(data["actual_start_time"], "%H:%M").time()
        actual_end = datetime.strptime(data["actual_end_time"], "%H:%M").time()
        
        pre_overtime_hours = 0
        post_overtime_hours = 0
        
        # 計算上班前加班
        if shift[5] and shift[3] > 0:  # enable_pre_overtime and threshold > 0
            # 計算門檻時間：上班時間 - 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_start) - 
                            timedelta(minutes=shift[3])).time()
            
            # 如果實際到達時間早於門檻時間，計算加班
            if actual_start < threshold_time:
                # 加班時間 = 門檻時間 - 實際到達時間
                pre_overtime_minutes = (datetime.combine(datetime.today(), threshold_time) - 
                                      datetime.combine(datetime.today(), actual_start)).total_seconds() / 60
                pre_overtime_hours = pre_overtime_minutes / 60
        
        # 計算下班後加班
        if shift[6] and shift[4] > 0:  # enable_post_overtime and threshold > 0
            # 計算門檻時間：下班時間 + 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_end) + 
                            timedelta(minutes=shift[4])).time()
            
            # 如果實際離開時間晚於門檻時間，計算加班
            if actual_end > threshold_time:
                # 加班時間 = 實際離開時間 - 門檻時間
                post_overtime_minutes = (datetime.combine(datetime.today(), actual_end) - 
                                       datetime.combine(datetime.today(), threshold_time)).total_seconds() / 60
                post_overtime_hours = post_overtime_minutes / 60
        
        total_overtime_hours = pre_overtime_hours + post_overtime_hours
        
        return jsonify({
            "pre_overtime_hours": round(pre_overtime_hours, 2),
            "post_overtime_hours": round(post_overtime_hours, 2),
            "total_overtime_hours": round(total_overtime_hours, 2),
            "break_duration_minutes": shift[2],
            "calculation_details": {
                "shift_start": shift[0],
                "shift_end": shift[1],
                "actual_start": data["actual_start_time"],
                "actual_end": data["actual_end_time"],
                "pre_threshold_minutes": shift[3],
                "post_threshold_minutes": shift[4],
                "enable_pre_overtime": bool(shift[5]),
                "enable_post_overtime": bool(shift[6])
            }
        })
        
    except Exception as e:
        logging.error(f"計算加班時數失敗: {e}")
        return jsonify({"error": f"計算加班時數失敗: {str(e)}"}), 500
    finally:
        conn.close()


# 在calculate_overtime函數之後添加新的API

@app.route("/api/attendance/overtime-settings", methods=["GET", "POST"])
def manage_overtime_settings():
    """
    管理加班時間計算設定
    
    支援的設定：
    - overtime_calculation_unit: 加班計算單位 (minutes/half_hour/hour)
    - overtime_rounding_rule: 捨去規則 (down/up/nearest)
    - late_tolerance_minutes: 遲到容忍分鐘數
    - early_leave_tolerance_minutes: 早退容忍分鐘數
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取加班計算設定
            cursor.execute("""
                SELECT setting_key, setting_value FROM system_settings 
                WHERE category = 'overtime_calculation'
            """)
            settings = dict(cursor.fetchall())
            
            # 設定預設值
            default_settings = {
                'overtime_calculation_unit': 'minutes',  # minutes/half_hour/hour
                'overtime_rounding_rule': 'down',        # down/up/nearest
                'late_tolerance_minutes': '10',
                'early_leave_tolerance_minutes': '10',
                'minimum_overtime_minutes': '30'
            }
            
            # 合併預設值和實際設定
            for key, default_value in default_settings.items():
                if key not in settings:
                    settings[key] = default_value
            
            return jsonify(settings)
            
        else:  # POST
            # 更新加班計算設定
            data = request.json
            
            for key, value in data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO system_settings (category, setting_key, setting_value, updated_at)
                    VALUES (?, ?, ?, ?)
                """, ('overtime_calculation', key, str(value), datetime.now()))
            
            conn.commit()
            logging.info("加班計算設定已更新")
            return jsonify({"message": "加班計算設定保存成功"})
            
    except Exception as e:
        logging.error(f"管理加班計算設定失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


def calculate_overtime_with_rules(overtime_minutes, calculation_unit='minutes', rounding_rule='down'):
    """
    根據設定的規則計算加班時數
    
    參數：
    - overtime_minutes: 原始加班分鐘數
    - calculation_unit: 計算單位 (minutes/half_hour/hour)
    - rounding_rule: 捨去規則 (down/up/nearest)
    
    返回：
    - 處理後的加班小時數
    """
    if overtime_minutes <= 0:
        return 0
    
    if calculation_unit == 'hour':
        # 以小時為單位
        hours = overtime_minutes / 60
        if rounding_rule == 'down':
            return int(hours)
        elif rounding_rule == 'up':
            import math
            return math.ceil(hours)
        else:  # nearest
            return round(hours)
    
    elif calculation_unit == 'half_hour':
        # 以半小時為單位
        half_hours = overtime_minutes / 30
        if rounding_rule == 'down':
            return int(half_hours) * 0.5
        elif rounding_rule == 'up':
            import math
            return math.ceil(half_hours) * 0.5
        else:  # nearest
            return round(half_hours) * 0.5
    
    else:  # minutes
        # 以分鐘為單位（原始精度）
        return overtime_minutes / 60


@app.route("/api/shifts/calculate-overtime-advanced", methods=["POST"])
def calculate_overtime_advanced():
    """
    進階加班計算，支援設定的計算單位和捨去規則
    """
    data = request.json
    
    required_fields = ["shift_id", "actual_start_time", "actual_end_time"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"缺少必要欄位: {field}"}), 400
    
    # 驗證時間格式
    def validate_time_format(time_str):
        """驗證時間格式是否為 HH:MM"""
        try:
            datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False
    
    if not validate_time_format(data["actual_start_time"]):
        return jsonify({"error": "實際上班時間格式錯誤"}), 400
    
    if not validate_time_format(data["actual_end_time"]):
        return jsonify({"error": "實際下班時間格式錯誤"}), 400
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取班別資訊
        cursor.execute("""
            SELECT start_time, end_time, break_duration_minutes,
                   pre_overtime_threshold_minutes, post_overtime_threshold_minutes,
                   enable_pre_overtime, enable_post_overtime, auto_calculate_overtime
            FROM shifts WHERE id = ? AND is_active = 1
        """, (data["shift_id"],))
        
        shift = cursor.fetchone()
        if not shift:
            return jsonify({"error": "班別不存在或已停用"}), 404
        
        if not shift[7]:  # auto_calculate_overtime
            return jsonify({
                "pre_overtime_hours": 0,
                "post_overtime_hours": 0,
                "total_overtime_hours": 0,
                "message": "此班別未啟用自動計算加班"
            })
        
        # 獲取加班計算設定
        cursor.execute("""
            SELECT setting_key, setting_value FROM system_settings 
            WHERE category = 'overtime_calculation'
        """)
        overtime_settings = dict(cursor.fetchall())
        
        calculation_unit = overtime_settings.get('overtime_calculation_unit', 'minutes')
        rounding_rule = overtime_settings.get('overtime_rounding_rule', 'down')
        minimum_overtime_minutes = int(overtime_settings.get('minimum_overtime_minutes', '30'))
        
        # 計算加班時數的邏輯
        from datetime import timedelta
        
        # 解析時間
        shift_start = datetime.strptime(shift[0], "%H:%M").time()
        shift_end = datetime.strptime(shift[1], "%H:%M").time()
        actual_start = datetime.strptime(data["actual_start_time"], "%H:%M").time()
        actual_end = datetime.strptime(data["actual_end_time"], "%H:%M").time()
        
        pre_overtime_minutes = 0
        post_overtime_minutes = 0
        
        # 計算上班前加班
        if shift[5] and shift[3] > 0:  # enable_pre_overtime and threshold > 0
            # 計算門檻時間：上班時間 - 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_start) - 
                            timedelta(minutes=shift[3])).time()
            
            # 如果實際到達時間早於門檻時間，計算加班
            if actual_start < threshold_time:
                # 加班時間 = 門檻時間 - 實際到達時間
                pre_overtime_minutes = (datetime.combine(datetime.today(), threshold_time) - 
                                      datetime.combine(datetime.today(), actual_start)).total_seconds() / 60
        
        # 計算下班後加班
        if shift[6] and shift[4] > 0:  # enable_post_overtime and threshold > 0
            # 計算門檻時間：下班時間 + 門檻分鐘數
            threshold_time = (datetime.combine(datetime.today(), shift_end) + 
                            timedelta(minutes=shift[4])).time()
            
            # 如果實際離開時間晚於門檻時間，計算加班
            if actual_end > threshold_time:
                # 加班時間 = 實際離開時間 - 門檻時間
                post_overtime_minutes = (datetime.combine(datetime.today(), actual_end) - 
                                       datetime.combine(datetime.today(), threshold_time)).total_seconds() / 60
        
        # 應用最小加班時間限制
        if pre_overtime_minutes < minimum_overtime_minutes:
            pre_overtime_minutes = 0
        if post_overtime_minutes < minimum_overtime_minutes:
            post_overtime_minutes = 0
        
        # 應用計算單位和捨去規則
        pre_overtime_hours = calculate_overtime_with_rules(
            pre_overtime_minutes, calculation_unit, rounding_rule
        )
        post_overtime_hours = calculate_overtime_with_rules(
            post_overtime_minutes, calculation_unit, rounding_rule
        )
        
        total_overtime_hours = pre_overtime_hours + post_overtime_hours
        
        return jsonify({
            "pre_overtime_hours": round(pre_overtime_hours, 2),
            "post_overtime_hours": round(post_overtime_hours, 2),
            "total_overtime_hours": round(total_overtime_hours, 2),
            "raw_pre_overtime_minutes": pre_overtime_minutes,
            "raw_post_overtime_minutes": post_overtime_minutes,
            "calculation_settings": {
                "calculation_unit": calculation_unit,
                "rounding_rule": rounding_rule,
                "minimum_overtime_minutes": minimum_overtime_minutes
            },
            "calculation_details": {
                "shift_start": shift[0],
                "shift_end": shift[1],
                "actual_start": data["actual_start_time"],
                "actual_end": data["actual_end_time"],
                "pre_threshold_minutes": shift[3],
                "post_threshold_minutes": shift[4]
            }
        })
        
    except Exception as e:
        logging.error(f"進階加班計算失敗: {e}")
        return jsonify({"error": f"進階加班計算失敗: {str(e)}"}), 500
    finally:
        conn.close()


@app.route("/api/attendance/daily-report", methods=["GET"])
def get_daily_attendance_report():
    """
    獲取每日考勤摘要報告
    
    查詢參數：
    - employee_id: 員工ID (可選，不提供則查詢所有員工)
    - date: 查詢日期 YYYY-MM-DD (可選，預設為今天)
    - department_id: 部門ID (可選)
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取查詢參數
        employee_id = request.args.get('employee_id')
        query_date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        department_id = request.args.get('department_id')
        
        # 獲取加班計算設定
        cursor.execute("""
            SELECT setting_key, setting_value FROM system_settings 
            WHERE category = 'overtime_calculation'
        """)
        overtime_settings = dict(cursor.fetchall())
        
        calculation_unit = overtime_settings.get('overtime_calculation_unit', 'minutes')
        rounding_rule = overtime_settings.get('overtime_rounding_rule', 'down')
        late_tolerance_minutes = int(overtime_settings.get('late_tolerance_minutes', '10'))
        early_leave_tolerance_minutes = int(overtime_settings.get('early_leave_tolerance_minutes', '10'))
        
        # 建構查詢條件
        where_conditions = ["DATE(a.check_in) = ?"]
        params = [query_date]
        
        if employee_id:
            where_conditions.append("e.id = ?")
            params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查詢考勤記錄和員工資訊
        query = f"""
            SELECT e.id, e.name, e.employee_id, d.name as department_name,
                   a.check_in, a.check_out, a.status, a.note,
                   s.start_time, s.end_time, s.name as shift_name,
                   s.pre_overtime_threshold_minutes, s.post_overtime_threshold_minutes,
                   s.enable_pre_overtime, s.enable_post_overtime
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN attendance a ON e.id = a.employee_id AND {where_conditions[0]}
            LEFT JOIN schedules sc ON e.id = sc.employee_id AND sc.shift_date = ?
            LEFT JOIN shifts s ON sc.shift_id = s.id
            WHERE e.status = 'active'
        """
        
        if employee_id:
            query += " AND e.id = ?"
        if department_id:
            query += " AND e.department_id = ?"
        
        query += " ORDER BY d.name, e.name"
        
        # 調整參數
        params_with_date = [query_date, query_date]
        if employee_id:
            params_with_date.append(employee_id)
        if department_id:
            params_with_date.append(department_id)
        
        cursor.execute(query, params_with_date)
        
        # 查詢請假記錄
        leave_query = f"""
            SELECT l.employee_id, l.leave_type, l.start_date, l.end_date, l.reason
            FROM leaves l
            JOIN employees e ON l.employee_id = e.id
            WHERE l.status = 'approved' 
            AND ? BETWEEN l.start_date AND l.end_date
        """
        
        leave_params = [query_date]
        if employee_id:
            leave_query += " AND e.id = ?"
            leave_params.append(employee_id)
        if department_id:
            leave_query += " AND e.department_id = ?"
            leave_params.append(department_id)
        
        cursor.execute(leave_query, leave_params)
        leave_records = {}
        for row in cursor.fetchall():
            emp_id = row[0]
            if emp_id not in leave_records:
                leave_records[emp_id] = []
            leave_records[emp_id].append({
                'leave_type': row[1],
                'start_date': row[2],
                'end_date': row[3],
                'reason': row[4]
            })
        
        # 重新執行考勤查詢
        cursor.execute(query, params_with_date)
        
        daily_reports = []
        for row in cursor.fetchall():
            emp_id = row[0]
            employee_name = row[1]
            employee_code = row[2]
            department_name = row[3]
            check_in = row[4]
            check_out = row[5]
            status = row[6]
            note = row[7]
            shift_start = row[8]
            shift_end = row[9]
            shift_name = row[10]
            pre_threshold = row[11] or 0
            post_threshold = row[12] or 0
            enable_pre_overtime = row[13]
            enable_post_overtime = row[14]
            
            # 初始化計算結果
            late_minutes = 0
            early_leave_minutes = 0
            pre_overtime_hours = 0
            post_overtime_hours = 0
            total_work_hours = 0
            
            # 如果有考勤記錄和班別資訊，進行詳細計算
            if check_in and shift_start and shift_end:
                try:
                    # 解析時間
                    check_in_time = datetime.strptime(check_in.split()[1], "%H:%M:%S").time()
                    shift_start_time = datetime.strptime(shift_start, "%H:%M").time()
                    shift_end_time = datetime.strptime(shift_end, "%H:%M").time()
                    
                    # 計算遲到時間
                    if check_in_time > shift_start_time:
                        late_delta = datetime.combine(datetime.today(), check_in_time) - \
                                   datetime.combine(datetime.today(), shift_start_time)
                        late_minutes = late_delta.total_seconds() / 60
                        if late_minutes <= late_tolerance_minutes:
                            late_minutes = 0  # 在容忍範圍內不算遲到
                    
                    # 計算早退時間和加班時間
                    if check_out:
                        check_out_time = datetime.strptime(check_out.split()[1], "%H:%M:%S").time()
                        
                        # 計算早退時間
                        if check_out_time < shift_end_time:
                            early_delta = datetime.combine(datetime.today(), shift_end_time) - \
                                        datetime.combine(datetime.today(), check_out_time)
                            early_leave_minutes = early_delta.total_seconds() / 60
                            if early_leave_minutes <= early_leave_tolerance_minutes:
                                early_leave_minutes = 0  # 在容忍範圍內不算早退
                        
                        # 計算加班時間
                        if enable_pre_overtime and pre_threshold > 0:
                            threshold_time = (datetime.combine(datetime.today(), shift_start_time) - 
                                            timedelta(minutes=pre_threshold)).time()
                            if check_in_time < threshold_time:
                                pre_overtime_minutes = (datetime.combine(datetime.today(), threshold_time) - 
                                                      datetime.combine(datetime.today(), check_in_time)).total_seconds() / 60
                                pre_overtime_hours = calculate_overtime_with_rules(
                                    pre_overtime_minutes, calculation_unit, rounding_rule
                                )
                        
                        if enable_post_overtime and post_threshold > 0:
                            threshold_time = (datetime.combine(datetime.today(), shift_end_time) + 
                                            timedelta(minutes=post_threshold)).time()
                            if check_out_time > threshold_time:
                                post_overtime_minutes = (datetime.combine(datetime.today(), check_out_time) - 
                                                       datetime.combine(datetime.today(), threshold_time)).total_seconds() / 60
                                post_overtime_hours = calculate_overtime_with_rules(
                                    post_overtime_minutes, calculation_unit, rounding_rule
                                )
                        
                        # 計算總工作時數
                        total_work_delta = datetime.combine(datetime.today(), check_out_time) - \
                                         datetime.combine(datetime.today(), check_in_time)
                        total_work_hours = total_work_delta.total_seconds() / 3600
                
                except Exception as calc_error:
                    logging.warning(f"計算員工 {employee_name} 考勤數據時發生錯誤: {calc_error}")
            
            # 獲取請假資訊
            employee_leaves = leave_records.get(emp_id, [])
            
            # 建構每日報告
            daily_report = {
                'employee_id': emp_id,
                'employee_name': employee_name,
                'employee_code': employee_code,
                'department_name': department_name,
                'date': query_date,
                'shift_name': shift_name,
                'scheduled_start': shift_start,
                'scheduled_end': shift_end,
                'actual_check_in': check_in,
                'actual_check_out': check_out,
                'attendance_status': status,
                'note': note,
                'late_minutes': round(late_minutes, 0),
                'early_leave_minutes': round(early_leave_minutes, 0),
                'pre_overtime_hours': round(pre_overtime_hours, 2),
                'post_overtime_hours': round(post_overtime_hours, 2),
                'total_overtime_hours': round(pre_overtime_hours + post_overtime_hours, 2),
                'total_work_hours': round(total_work_hours, 2),
                'leaves': employee_leaves,
                'has_attendance': check_in is not None,
                'has_leave': len(employee_leaves) > 0
            }
            
            daily_reports.append(daily_report)
        
        conn.close()
        
        # 統計摘要
        total_employees = len(daily_reports)
        attended_employees = len([r for r in daily_reports if r['has_attendance']])
        late_employees = len([r for r in daily_reports if r['late_minutes'] > 0])
        early_leave_employees = len([r for r in daily_reports if r['early_leave_minutes'] > 0])
        overtime_employees = len([r for r in daily_reports if r['total_overtime_hours'] > 0])
        leave_employees = len([r for r in daily_reports if r['has_leave']])
        
        return jsonify({
            'date': query_date,
            'summary': {
                'total_employees': total_employees,
                'attended_employees': attended_employees,
                'late_employees': late_employees,
                'early_leave_employees': early_leave_employees,
                'overtime_employees': overtime_employees,
                'leave_employees': leave_employees,
                'attendance_rate': round((attended_employees / max(total_employees, 1)) * 100, 1)
            },
            'calculation_settings': {
                'calculation_unit': calculation_unit,
                'rounding_rule': rounding_rule,
                'late_tolerance_minutes': late_tolerance_minutes,
                'early_leave_tolerance_minutes': early_leave_tolerance_minutes
            },
            'daily_reports': daily_reports
        })
        
    except Exception as e:
        logging.error(f"獲取每日考勤報告失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/attendance/monthly-summary", methods=["GET"])
def get_monthly_attendance_summary():
    """
    獲取月度考勤摘要
    
    查詢參數：
    - employee_id: 員工ID (可選)
    - year_month: 查詢年月 YYYY-MM (可選，預設為本月)
    - department_id: 部門ID (可選)
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取查詢參數
        employee_id = request.args.get('employee_id')
        year_month = request.args.get('year_month', datetime.now().strftime('%Y-%m'))
        department_id = request.args.get('department_id')
        
        # 計算月份的開始和結束日期
        year, month = map(int, year_month.split('-'))
        start_date = f"{year}-{month:02d}-01"
        
        # 計算下個月第一天
        if month == 12:
            next_year, next_month = year + 1, 1
        else:
            next_year, next_month = year, month + 1
        end_date = f"{next_year}-{next_month:02d}-01"
        
        # 建構查詢條件
        where_conditions = ["DATE(a.check_in) >= ? AND DATE(a.check_in) < ?"]
        params = [start_date, end_date]
        
        if employee_id:
            where_conditions.append("e.id = ?")
            params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            params.append(department_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查詢月度統計
        query = f"""
            SELECT e.id, e.name, e.employee_id, d.name as department_name,
                   COUNT(a.id) as total_days,
                   SUM(CASE WHEN a.status = 'normal' THEN 1 ELSE 0 END) as normal_days,
                   SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_days,
                   SUM(CASE WHEN a.status = 'early_leave' THEN 1 ELSE 0 END) as early_leave_days,
                   SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_days
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN attendance a ON e.id = a.employee_id AND {where_conditions[0]}
            WHERE e.status = 'active'
        """
        
        if employee_id:
            query += " AND e.id = ?"
        if department_id:
            query += " AND e.department_id = ?"
        
        query += " GROUP BY e.id ORDER BY d.name, e.name"
        
        cursor.execute(query, params)
        
        monthly_summaries = []
        for row in cursor.fetchall():
            emp_id = row[0]
            
            # 查詢該員工的請假統計
            cursor.execute("""
                SELECT leave_type, COUNT(*) as leave_count,
                       SUM(julianday(end_date) - julianday(start_date) + 1) as total_leave_days
                FROM leaves l
                WHERE l.employee_id = ? AND l.status = 'approved'
                AND ((l.start_date <= ? AND l.end_date >= ?) OR 
                     (l.start_date >= ? AND l.start_date < ?))
                GROUP BY leave_type
            """, (emp_id, end_date, start_date, start_date, end_date))
            
            leave_summary = {}
            total_leave_days = 0
            for leave_row in cursor.fetchall():
                leave_summary[leave_row[0]] = {
                    'count': leave_row[1],
                    'days': leave_row[2] or 0
                }
                total_leave_days += leave_row[2] or 0
            
            monthly_summary = {
                'employee_id': emp_id,
                'employee_name': row[1],
                'employee_code': row[2],
                'department_name': row[3],
                'year_month': year_month,
                'attendance_stats': {
                    'total_attendance_days': row[4] or 0,
                    'normal_days': row[5] or 0,
                    'late_days': row[6] or 0,
                    'early_leave_days': row[7] or 0,
                    'absent_days': row[8] or 0
                },
                'leave_stats': {
                    'total_leave_days': total_leave_days,
                    'by_type': leave_summary
                },
                'attendance_rate': round(((row[5] or 0) / max(row[4] or 1, 1)) * 100, 1)
            }
            
            monthly_summaries.append(monthly_summary)
        
        conn.close()
        
        return jsonify({
            'year_month': year_month,
            'period': f"{start_date} 至 {end_date}",
            'monthly_summaries': monthly_summaries
        })
        
    except Exception as e:
        logging.error(f"獲取月度考勤摘要失敗: {str(e)}")
        return jsonify({"error": str(e)}), 500


# 在其他HTML頁面路由之後添加新路由

@app.route("/attendance-enhanced-demo")
def attendance_enhanced_demo():
    """考勤系統增強功能演示頁面"""
    return render_template("attendance_enhanced_demo.html")


# ============================================================================
# 🔹 排班系統 API 路由
# ============================================================================

@app.route("/api/schedules", methods=["POST"])
def create_schedule():
    """
    創建新的排班記錄
    
    請求格式：
    {
        "employee_id": 1,
        "shift_date": "2025-06-02",
        "shift_id": 1,
        "note": "備註",
        "status": "scheduled"
    }
    """
    try:
        data = request.json
        
        # 驗證必要欄位
        required_fields = ['employee_id', 'shift_date', 'shift_id']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"缺少必要欄位: {field}"}), 400
        
        # 檢查員工是否存在
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id FROM employees WHERE id = ? AND status = 'active'", 
                      (data['employee_id'],))
        if not cursor.fetchone():
            return jsonify({"error": "員工不存在或已停用"}), 404
        
        # 檢查班別是否存在
        cursor.execute("SELECT id FROM shifts WHERE id = ?", (data['shift_id'],))
        if not cursor.fetchone():
            return jsonify({"error": "班別不存在"}), 404
        
        # 檢查是否已有相同日期的排班
        cursor.execute("""
            SELECT id FROM schedules 
            WHERE employee_id = ? AND shift_date = ? AND status != 'cancelled'
        """, (data['employee_id'], data['shift_date']))
        
        if cursor.fetchone():
            return jsonify({"error": "該員工在此日期已有排班"}), 409
        
        # 插入新排班記錄
        cursor.execute("""
            INSERT INTO schedules (employee_id, shift_date, shift_id, note, status, created_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """, (
            data['employee_id'],
            data['shift_date'], 
            data['shift_id'],
            data.get('note'),
            data.get('status', 'scheduled')
        ))
        
        schedule_id = cursor.lastrowid
        conn.commit()
        
        # 獲取完整的排班資訊
        cursor.execute("""
            SELECT s.*, e.name as employee_name, sh.name as shift_name
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            WHERE s.id = ?
        """, (schedule_id,))
        
        columns = [description[0] for description in cursor.description]
        schedule = dict(zip(columns, cursor.fetchone()))
        
        conn.close()
        
        return jsonify({
            "message": "排班創建成功",
            "schedule": schedule
        }), 201
        
    except Exception as e:
        logger.error(f"創建排班失敗: {e}")
        return jsonify({"error": f"創建排班失敗: {str(e)}"}), 500

@app.route("/api/schedules/<int:schedule_id>", methods=["PUT"])
def update_schedule(schedule_id):
    """更新排班記錄"""
    try:
        data = request.json
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查排班記錄是否存在
        cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
        if not cursor.fetchone():
            return jsonify({"error": "排班記錄不存在"}), 404
        
        # 構建更新語句
        update_fields = []
        update_values = []
        
        allowed_fields = ['employee_id', 'shift_date', 'shift_id', 'note', 'status']
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                update_values.append(data[field])
        
        if not update_fields:
            return jsonify({"error": "沒有可更新的欄位"}), 400
        
        update_values.append(schedule_id)
        
        cursor.execute(f"""
            UPDATE schedules 
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, update_values)
        
        conn.commit()
        
        # 獲取更新後的記錄
        cursor.execute("""
            SELECT s.*, e.name as employee_name, sh.name as shift_name
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            WHERE s.id = ?
        """, (schedule_id,))
        
        columns = [description[0] for description in cursor.description]
        schedule = dict(zip(columns, cursor.fetchone()))
        
        conn.close()
        
        return jsonify({
            "message": "排班更新成功",
            "schedule": schedule
        })
        
    except Exception as e:
        logger.error(f"更新排班失敗: {e}")
        return jsonify({"error": f"更新排班失敗: {str(e)}"}), 500

@app.route("/api/schedules/<int:schedule_id>", methods=["DELETE"])
def delete_schedule(schedule_id):
    """刪除排班記錄"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查排班記錄是否存在
        cursor.execute("SELECT * FROM schedules WHERE id = ?", (schedule_id,))
        if not cursor.fetchone():
            return jsonify({"error": "排班記錄不存在"}), 404
        
        # 軟刪除（更新狀態為cancelled）
        cursor.execute("""
            UPDATE schedules 
            SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (schedule_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({"message": "排班已取消"})
        
    except Exception as e:
        logger.error(f"刪除排班失敗: {e}")
        return jsonify({"error": f"刪除排班失敗: {str(e)}"}), 500

@app.route("/api/schedules/calendar", methods=["GET"])
def get_calendar_schedules():
    """
    獲取日曆排班數據
    
    查詢參數：
    - year: 年份 (必填)
    - month: 月份 (必填)
    - employee_id: 員工ID (可選)
    - department_id: 部門ID (可選)
    """
    try:
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        
        if not year or not month:
            return jsonify({"error": "請提供年份和月份"}), 400
        
        # 計算月份的開始和結束日期
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = ["s.shift_date >= ? AND s.shift_date < ?", "s.status != 'cancelled'"]
        query_params = [start_date, end_date]
        
        if employee_id:
            where_conditions.append("s.employee_id = ?")
            query_params.append(employee_id)
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
        
        # 執行查詢
        query = f"""
            SELECT s.*, e.name as employee_name, e.employee_id as employee_code,
                   sh.name as shift_name, sh.start_time, sh.end_time, sh.code as shift_code,
                   d.name as department_name
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE {' AND '.join(where_conditions)}
            ORDER BY s.shift_date, s.employee_id
        """
        
        cursor.execute(query, query_params)
        columns = [description[0] for description in cursor.description]
        schedules = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            "year": year,
            "month": month,
            "schedules": schedules,
            "total": len(schedules)
        })
        
    except Exception as e:
        logger.error(f"獲取日曆排班失敗: {e}")
        return jsonify({"error": f"獲取日曆排班失敗: {str(e)}"}), 500

@app.route("/api/schedules/statistics", methods=["GET"])
def get_schedule_statistics():
    """獲取排班統計資訊"""
    try:
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        
        if not year or not month:
            return jsonify({"error": "請提供年份和月份"}), 400
        
        # 計算月份範圍
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 統計排班數量
        cursor.execute("""
            SELECT COUNT(*) as total_schedules,
                   COUNT(DISTINCT employee_id) as total_employees
            FROM schedules 
            WHERE shift_date >= ? AND shift_date < ? AND status != 'cancelled'
        """, (start_date, end_date))
        
        basic_stats = dict(zip([d[0] for d in cursor.description], cursor.fetchone()))
        
        # 統計各班別數量
        cursor.execute("""
            SELECT sh.name, COUNT(*) as count
            FROM schedules s
            LEFT JOIN shifts sh ON s.shift_id = sh.id
            WHERE s.shift_date >= ? AND s.shift_date < ? AND s.status != 'cancelled'
            GROUP BY s.shift_id, sh.name
            ORDER BY count DESC
        """, (start_date, end_date))
        
        shift_stats = [dict(zip([d[0] for d in cursor.description], row)) for row in cursor.fetchall()]
        
        # 統計各部門數量
        cursor.execute("""
            SELECT d.name, COUNT(*) as count
            FROM schedules s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            WHERE s.shift_date >= ? AND s.shift_date < ? AND s.status != 'cancelled'
            GROUP BY e.department_id, d.name
            ORDER BY count DESC
        """, (start_date, end_date))
        
        department_stats = [dict(zip([d[0] for d in cursor.description], row)) for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            "period": f"{year}年{month}月",
            "basic_statistics": basic_stats,
            "shift_statistics": shift_stats,
            "department_statistics": department_stats
        })
        
    except Exception as e:
        logger.error(f"獲取排班統計失敗: {e}")
        return jsonify({"error": f"獲取排班統計失敗: {str(e)}"}), 500


# ====== 打卡紀錄查詢 API ======

@app.route("/api/attendance/records", methods=["GET"])
def get_attendance_records():
    """
    查詢打卡紀錄 API
    
    功能說明：
    - 支援分頁查詢打卡記錄
    - 支援多種篩選條件（員工、部門、日期範圍、狀態）
    - 返回統計資訊和詳細記錄列表
    - 適應現有資料庫結構（attendance 表包含 check_in, check_out 欄位）
    
    查詢參數：
    - page: 頁碼 (預設: 1, 最小值: 1)
    - limit: 每頁筆數 (預設: 20, 範圍: 1-100)
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - status: 考勤狀態 (可選, 值: normal/late/early_leave/absent/manual)
    
    返回格式：
    {
        "records": [...],           // 打卡記錄陣列
        "total": 100,              // 總記錄數
        "page": 1,                 // 當前頁碼
        "limit": 20,               // 每頁筆數
        "total_pages": 5,          // 總頁數
        "statistics": {...}        // 統計資訊
    }
    
    錯誤處理：
    - 參數驗證失敗：返回 400 錯誤
    - 資料庫連接失敗：返回 500 錯誤
    - SQL 查詢錯誤：記錄詳細錯誤並返回 500
    """
    try:
        logger.info("開始處理打卡紀錄查詢請求")
        
        # ===== 參數獲取與驗證 =====
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        
        # 參數驗證
        if page < 1:
            logger.warning(f"無效的頁碼參數: {page}")
            return jsonify({"error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            logger.warning(f"無效的每頁筆數參數: {limit}")
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 日期格式驗證
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的開始日期格式: {start_date}")
                return jsonify({"error": "開始日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的結束日期格式: {end_date}")
                return jsonify({"error": "結束日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        # 狀態值驗證
        valid_statuses = ['normal', 'late', 'early_leave', 'absent', 'manual', 'overtime', 'incomplete']
        if status and status not in valid_statuses:
            logger.warning(f"無效的狀態參數: {status}")
            return jsonify({"error": f"狀態值必須是以下之一: {', '.join(valid_statuses)}"}), 400
        
        logger.info(f"查詢參數驗證通過 - page: {page}, limit: {limit}, employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, status: {status}")
        
        # ===== 計算偏移量 =====
        offset = (page - 1) * limit
        logger.debug(f"計算偏移量: offset = {offset}")
        
        # ===== 資料庫連接 =====
        try:
            conn = create_connection()
            if not conn:
                logger.error("資料庫連接失敗")
                return jsonify({"error": "資料庫連接失敗"}), 500
            
            cursor = conn.cursor()
            logger.debug("資料庫連接成功")
        except Exception as db_error:
            logger.error(f"資料庫連接異常: {db_error}")
            return jsonify({"error": f"資料庫連接異常: {str(db_error)}"}), 500
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        # 員工ID篩選
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            query_params.append(employee_id)
            logger.debug(f"添加員工ID篩選條件: {employee_id}")
        
        # 部門ID篩選
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
            logger.debug(f"添加部門ID篩選條件: {department_id}")
        
        # 開始日期篩選（檢查 check_in 或 check_out 任一欄位）
        if start_date:
            where_conditions.append("(DATE(a.check_in) >= ? OR DATE(a.check_out) >= ?)")
            query_params.extend([start_date, start_date])
            logger.debug(f"添加開始日期篩選條件: {start_date}")
        
        # 結束日期篩選（檢查 check_in 或 check_out 任一欄位）
        if end_date:
            where_conditions.append("(DATE(a.check_in) <= ? OR DATE(a.check_out) <= ?)")
            query_params.extend([end_date, end_date])
            logger.debug(f"添加結束日期篩選條件: {end_date}")
        
        # 狀態篩選
        if status:
            where_conditions.append("a.status = ?")
            query_params.append(status)
            logger.debug(f"添加狀態篩選條件: {status}")
        
        # 組合 WHERE 子句
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        logger.debug(f"構建的 WHERE 子句: {where_clause}")
        logger.debug(f"查詢參數: {query_params}")
        
        # ===== 查詢總記錄數 =====
        try:
            count_query = f"""
                SELECT COUNT(*) as total
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                {where_clause}
            """
            
            logger.debug(f"執行總數查詢 SQL: {count_query}")
            cursor.execute(count_query, query_params)
            total_records = cursor.fetchone()[0]
            logger.info(f"查詢到總記錄數: {total_records}")
            
        except Exception as count_error:
            logger.error(f"查詢總記錄數失敗: {count_error}")
            conn.close()
            return jsonify({"error": f"查詢總記錄數失敗: {str(count_error)}"}), 500
        
        # ===== 查詢詳細記錄 =====
        try:
            # 注意：這裡使用現有資料庫結構的欄位名稱
            records_query = f"""
                SELECT 
                    a.id,
                    a.employee_id,
                    a.check_in,
                    a.check_out,
                    a.status,
                    a.device_id,
                    a.note,
                    a.created_at,
                    a.clock_status_code,
                    e.name as employee_name,
                    e.employee_id as employee_code,
                    d.name as department_name
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                {where_clause}
                ORDER BY a.created_at DESC, a.id DESC
                LIMIT ? OFFSET ?
            """
            
            logger.debug(f"執行記錄查詢 SQL: {records_query}")
            cursor.execute(records_query, query_params + [limit, offset])
            
            # 獲取欄位名稱
            columns = [description[0] for description in cursor.description]
            logger.debug(f"查詢結果欄位: {columns}")
            
            # 轉換為字典格式
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                # 格式化時間欄位以便前端顯示
                if record.get('check_in'):
                    record['check_in_formatted'] = record['check_in']
                if record.get('check_out'):
                    record['check_out_formatted'] = record['check_out']
                if record.get('created_at'):
                    record['created_at_formatted'] = record['created_at']
                
                # 查詢該考勤記錄的原始打卡記錄
                raw_records_query = """
                    SELECT timestamp, status_code, machine_code
                    FROM clock_raw_records 
                    WHERE attendance_id = ?
                    ORDER BY timestamp
                """
                cursor.execute(raw_records_query, (record['id'],))
                raw_records = cursor.fetchall()
                
                # 將原始記錄添加到記錄中
                record['raw_records'] = []
                for raw_row in raw_records:
                    record['raw_records'].append({
                        'timestamp': raw_row[0],
                        'status_code': raw_row[1],
                        'machine_code': raw_row[2]
                    })
                
                records.append(record)
            
            logger.info(f"查詢到 {len(records)} 筆記錄")
            
        except Exception as records_error:
            logger.error(f"查詢詳細記錄失敗: {records_error}")
            conn.close()
            return jsonify({"error": f"查詢詳細記錄失敗: {str(records_error)}"}), 500
        
        # ===== 計算統計資訊 =====
        try:
            stats_query = f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT a.employee_id) as total_employees,
                    COUNT(DISTINCT DATE(COALESCE(a.check_in, a.check_out))) as total_days,
                    COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                    COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                    COUNT(CASE WHEN a.status = 'manual' THEN 1 END) as manual_count,
                    COUNT(CASE WHEN a.status = 'incomplete' THEN 1 END) as incomplete_count
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                {where_clause}
            """
            
            logger.debug(f"執行統計查詢 SQL: {stats_query}")
            cursor.execute(stats_query, query_params)
            stats_row = cursor.fetchone()
            
            statistics = {
                "total_records": stats_row[0],
                "total_employees": stats_row[1],
                "total_days": stats_row[2],
                "avg_records_per_day": round(stats_row[0] / max(stats_row[2], 1), 1),
                "status_breakdown": {
                    "normal": stats_row[3],
                    "late": stats_row[4],
                    "early_leave": stats_row[5],
                    "absent": stats_row[6],
                    "manual": stats_row[7],
                    "incomplete": stats_row[8]
                }
            }
            
            logger.info(f"統計資訊計算完成: {statistics}")
            
        except Exception as stats_error:
            logger.error(f"計算統計資訊失敗: {stats_error}")
            # 統計失敗不影響主要功能，使用預設值
            statistics = {
                "total_records": total_records,
                "total_employees": 0,
                "total_days": 0,
                "avg_records_per_day": 0,
                "status_breakdown": {
                    "normal": 0,
                    "late": 0,
                    "early_leave": 0,
                    "absent": 0,
                    "manual": 0,
                    "incomplete": 0
                }
            }
            logger.warning("使用預設統計資訊")
        
        # ===== 關閉資料庫連接 =====
        conn.close()
        logger.debug("資料庫連接已關閉")
        
        # ===== 計算總頁數 =====
        total_pages = (total_records + limit - 1) // limit
        logger.debug(f"計算總頁數: {total_pages}")
        
        # ===== 構建響應資料 =====
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "statistics": statistics,
            "query_info": {
                "filters_applied": len(where_conditions),
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        logger.info(f"打卡紀錄查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        # ===== 全域錯誤處理 =====
        error_msg = f"查詢打卡紀錄時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)  # exc_info=True 會記錄完整的錯誤堆疊
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route("/api/attendance/records/<int:record_id>", methods=["GET"])
def get_attendance_record_detail(record_id):
    """
    獲取單個打卡紀錄詳情 API
    
    功能說明：
    - 根據記錄ID獲取完整的打卡記錄詳情
    - 包含員工資訊、部門資訊和所有相關欄位
    - 適應現有資料庫結構
    
    路徑參數：
    - record_id: 打卡記錄ID (必須, 正整數)
    
    返回格式：
    {
        "success": true,
        "record": {
            "id": 1,
            "employee_id": 123,
            "check_in": "2025-06-02 08:30:00",
            "check_out": "2025-06-02 17:30:00",
            "status": "normal",
            "device_id": "DEVICE_1",
            "note": "正常打卡",
            "created_at": "2025-06-02 08:30:05",
            "employee_name": "張三",
            "employee_code": "EMP001",
            "department_name": "資訊部"
        }
    }
    
    錯誤處理：
    - 記錄不存在：返回 404 錯誤
    - 無效的記錄ID：返回 400 錯誤
    - 資料庫錯誤：返回 500 錯誤
    """
    try:
        logger.info(f"開始查詢打卡紀錄詳情 - record_id: {record_id}")
        
        # ===== 參數驗證 =====
        if record_id <= 0:
            logger.warning(f"無效的記錄ID: {record_id}")
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # ===== 資料庫連接 =====
        try:
            conn = create_connection()
            if not conn:
                logger.error("資料庫連接失敗")
                return jsonify({
                    "success": False,
                    "error": "資料庫連接失敗"
                }), 500
            
            cursor = conn.cursor()
            logger.debug("資料庫連接成功")
        except Exception as db_error:
            logger.error(f"資料庫連接異常: {db_error}")
            return jsonify({
                "success": False,
                "error": f"資料庫連接異常: {str(db_error)}"
            }), 500
        
        # ===== 查詢記錄詳情 =====
        try:
            query = """
                SELECT 
                    a.id,
                    a.employee_id,
                    a.check_in,
                    a.check_out,
                    a.status,
                    a.device_id,
                    a.note,
                    a.created_at,
                    e.name as employee_name,
                    e.employee_id as employee_code,
                    e.phone as employee_phone,
                    e.email as employee_email,
                    d.name as department_name,
                    d.id as department_id
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                WHERE a.id = ?
            """
            
            logger.debug(f"執行查詢 SQL: {query}")
            cursor.execute(query, (record_id,))
            
            row = cursor.fetchone()
            if not row:
                logger.warning(f"找不到記錄ID為 {record_id} 的打卡紀錄")
                conn.close()
                return jsonify({
                    "success": False,
                    "error": f"找不到記錄ID為 {record_id} 的打卡紀錄"
                }), 404
            
            # ===== 轉換為字典格式 =====
            columns = [description[0] for description in cursor.description]
            record = dict(zip(columns, row))
            
            # 格式化時間欄位
            if record.get('check_in'):
                record['check_in_formatted'] = record['check_in']
            if record.get('check_out'):
                record['check_out_formatted'] = record['check_out']
            if record.get('created_at'):
                record['created_at_formatted'] = record['created_at']
            
            # 計算工作時長（如果有上下班時間）
            if record.get('check_in') and record.get('check_out'):
                try:
                    check_in_time = datetime.strptime(record['check_in'], '%Y-%m-%d %H:%M:%S.%f') if '.' in record['check_in'] else datetime.strptime(record['check_in'], '%Y-%m-%d %H:%M:%S')
                    check_out_time = datetime.strptime(record['check_out'], '%Y-%m-%d %H:%M:%S.%f') if '.' in record['check_out'] else datetime.strptime(record['check_out'], '%Y-%m-%d %H:%M:%S')
                    work_duration = check_out_time - check_in_time
                    record['work_hours'] = round(work_duration.total_seconds() / 3600, 2)
                    record['work_duration_formatted'] = str(work_duration).split('.')[0]  # 移除微秒
                except Exception as time_error:
                    logger.warning(f"計算工作時長失敗: {time_error}")
                    record['work_hours'] = None
                    record['work_duration_formatted'] = None
            
            logger.info(f"成功查詢到打卡紀錄詳情 - ID: {record_id}, 員工: {record.get('employee_name')}")
            
        except Exception as query_error:
            logger.error(f"查詢打卡紀錄詳情失敗: {query_error}")
            conn.close()
            return jsonify({
                "success": False,
                "error": f"查詢打卡紀錄詳情失敗: {str(query_error)}"
            }), 500
        
        # ===== 關閉資料庫連接 =====
        conn.close()
        logger.debug("資料庫連接已關閉")
        
        # ===== 返回結果 =====
        response_data = {
            "success": True,
            "record": record,
            "query_info": {
                "record_id": record_id,
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        # ===== 全域錯誤處理 =====
        error_msg = f"獲取打卡紀錄詳情時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route("/api/attendance/records/export", methods=["GET"])
def export_attendance_records():
    """
    匯出打卡紀錄為 Excel 檔案 API
    
    功能說明：
    - 根據篩選條件匯出打卡記錄到 Excel 檔案
    - 支援與查詢 API 相同的篩選條件
    - 自動格式化 Excel 表格（標題、邊框、欄位寬度）
    - 中文化打卡狀態和設備類型
    
    查詢參數：
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - status: 考勤狀態 (可選, 值: normal/late/early_leave/absent/manual)
    
    返回：
    - 成功：Excel 檔案下載
    - 失敗：JSON 錯誤訊息
    
    檔案命名格式：
    attendance_records_YYYYMMDD_HHMMSS.xlsx
    
    錯誤處理：
    - 參數驗證失敗：返回 400 錯誤
    - 無資料可匯出：返回 404 錯誤
    - Excel 生成失敗：返回 500 錯誤
    """
    try:
        logger.info("開始處理打卡紀錄匯出請求")
        
        # ===== 導入必要模組 =====
        try:
            import io
            import xlsxwriter
            from datetime import datetime
            logger.debug("Excel 相關模組導入成功")
        except ImportError as import_error:
            logger.error(f"導入 Excel 模組失敗: {import_error}")
            return jsonify({
                "success": False,
                "error": f"系統缺少必要的 Excel 處理模組: {str(import_error)}"
            }), 500
        
        # ===== 獲取查詢參數 =====
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        
        logger.info(f"匯出參數 - employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, status: {status}")
        
        # ===== 參數驗證 =====
        # 日期格式驗證
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的開始日期格式: {start_date}")
                return jsonify({
                    "success": False,
                    "error": "開始日期格式錯誤，請使用 YYYY-MM-DD 格式"
                }), 400
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的結束日期格式: {end_date}")
                return jsonify({
                    "success": False,
                    "error": "結束日期格式錯誤，請使用 YYYY-MM-DD 格式"
                }), 400
        
        # 狀態值驗證
        valid_statuses = ['normal', 'late', 'early_leave', 'absent', 'manual', 'overtime', 'incomplete']
        if status and status not in valid_statuses:
            logger.warning(f"無效的狀態參數: {status}")
            return jsonify({
                "success": False,
                "error": f"狀態值必須是以下之一: {', '.join(valid_statuses)}"
            }), 400
        
        # ===== 資料庫連接 =====
        try:
            conn = create_connection()
            if not conn:
                logger.error("資料庫連接失敗")
                return jsonify({
                    "success": False,
                    "error": "資料庫連接失敗"
                }), 500
            
            cursor = conn.cursor()
            logger.debug("資料庫連接成功")
        except Exception as db_error:
            logger.error(f"資料庫連接異常: {db_error}")
            return jsonify({
                "success": False,
                "error": f"資料庫連接異常: {str(db_error)}"
            }), 500
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            query_params.append(employee_id)
            logger.debug(f"添加員工ID篩選條件: {employee_id}")
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
            logger.debug(f"添加部門ID篩選條件: {department_id}")
        
        if start_date:
            where_conditions.append("(DATE(a.check_in) >= ? OR DATE(a.check_out) >= ?)")
            query_params.extend([start_date, start_date])
            logger.debug(f"添加開始日期篩選條件: {start_date}")
        
        if end_date:
            where_conditions.append("(DATE(a.check_in) <= ? OR DATE(a.check_out) <= ?)")
            query_params.extend([end_date, end_date])
            logger.debug(f"添加結束日期篩選條件: {end_date}")
        
        if status:
            where_conditions.append("a.status = ?")
            query_params.append(status)
            logger.debug(f"添加狀態篩選條件: {status}")
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        logger.debug(f"構建的 WHERE 子句: {where_clause}")
        
        # ===== 查詢記錄 =====
        try:
            query = f"""
                SELECT 
                    a.check_in,
                    a.check_out,
                    e.name as employee_name,
                    e.employee_id as employee_code,
                    d.name as department_name,
                    a.status,
                    a.device_id,
                    a.note,
                    a.created_at
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                {where_clause}
                ORDER BY a.created_at DESC
            """
            
            logger.debug(f"執行匯出查詢 SQL: {query}")
            cursor.execute(query, query_params)
            records = cursor.fetchall()
            
            if not records:
                logger.warning("沒有找到符合條件的記錄")
                conn.close()
                return jsonify({
                    "success": False,
                    "error": "沒有找到符合條件的記錄可供匯出"
                }), 404
            
            logger.info(f"查詢到 {len(records)} 筆記錄準備匯出")
            
        except Exception as query_error:
            logger.error(f"查詢匯出記錄失敗: {query_error}")
            conn.close()
            return jsonify({
                "success": False,
                "error": f"查詢匯出記錄失敗: {str(query_error)}"
            }), 500
        
        # ===== 創建 Excel 檔案 =====
        try:
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            worksheet = workbook.add_worksheet('打卡紀錄')
            
            logger.debug("Excel 工作簿創建成功")
            
            # ===== 設定 Excel 格式 =====
            # 標題格式
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4F46E5',
                'font_color': 'white',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # 一般儲存格格式
            cell_format = workbook.add_format({
                'border': 1,
                'align': 'left',
                'valign': 'vcenter'
            })
            
            # 日期時間格式
            date_format = workbook.add_format({
                'border': 1,
                'num_format': 'yyyy-mm-dd hh:mm:ss',
                'align': 'center'
            })
            
            # 狀態格式（根據狀態設定不同顏色）
            status_formats = {
                'normal': workbook.add_format({'border': 1, 'bg_color': '#D1FAE5', 'align': 'center'}),
                'late': workbook.add_format({'border': 1, 'bg_color': '#FEF3C7', 'align': 'center'}),
                'early_leave': workbook.add_format({'border': 1, 'bg_color': '#FECACA', 'align': 'center'}),
                'absent': workbook.add_format({'border': 1, 'bg_color': '#F3F4F6', 'align': 'center'}),
                'manual': workbook.add_format({'border': 1, 'bg_color': '#E0E7FF', 'align': 'center'}),
                'overtime': workbook.add_format({'border': 1, 'bg_color': '#FFD700', 'align': 'center'})
            }
            
            logger.debug("Excel 格式設定完成")
            
        except Exception as excel_error:
            logger.error(f"創建 Excel 檔案失敗: {excel_error}")
            conn.close()
            return jsonify({
                "success": False,
                "error": f"創建 Excel 檔案失敗: {str(excel_error)}"
            }), 500
        
        # ===== 寫入 Excel 內容 =====
        try:
            # 寫入標題行
            headers = ['上班打卡', '下班打卡', '員工姓名', '員工編號', '部門', '考勤狀態', '打卡設備', '備註', '記錄時間']
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
            
            logger.debug("Excel 標題行寫入完成")
            
            # 狀態和設備類型中文對照
            status_map = {
                'normal': '正常',
                'late': '遲到',
                'early_leave': '早退',
                'absent': '缺勤',
                'manual': '手動補登',
                'overtime': '加班'
            }
            
            # 寫入資料行
            for row, record in enumerate(records, 1):
                # 上班打卡時間
                if record[0]:  # check_in
                    worksheet.write(row, 0, record[0], date_format)
                else:
                    worksheet.write(row, 0, '', cell_format)
                
                # 下班打卡時間
                if record[1]:  # check_out
                    worksheet.write(row, 1, record[1], date_format)
                else:
                    worksheet.write(row, 1, '', cell_format)
                
                # 員工姓名
                worksheet.write(row, 2, record[2] or '', cell_format)
                
                # 員工編號
                worksheet.write(row, 3, record[3] or '', cell_format)
                
                # 部門
                worksheet.write(row, 4, record[4] or '', cell_format)
                
                # 考勤狀態（使用對應的格式和中文）
                status_value = record[5]
                status_text = status_map.get(status_value, status_value)
                status_format = status_formats.get(status_value, cell_format)
                worksheet.write(row, 5, status_text, status_format)
                
                # 打卡設備
                worksheet.write(row, 6, record[6] or '', cell_format)
                
                # 備註
                worksheet.write(row, 7, record[7] or '', cell_format)
                
                # 記錄時間
                if record[8]:  # created_at
                    worksheet.write(row, 8, record[8], date_format)
                else:
                    worksheet.write(row, 8, '', cell_format)
            
            logger.info(f"Excel 資料寫入完成 - 共 {len(records)} 筆記錄")
            
        except Exception as write_error:
            logger.error(f"寫入 Excel 內容失敗: {write_error}")
            workbook.close()
            conn.close()
            return jsonify({
                "success": False,
                "error": f"寫入 Excel 內容失敗: {str(write_error)}"
            }), 500
        
        # ===== 調整欄位寬度 =====
        try:
            worksheet.set_column('A:A', 20)  # 上班打卡
            worksheet.set_column('B:B', 20)  # 下班打卡
            worksheet.set_column('C:C', 15)  # 員工姓名
            worksheet.set_column('D:D', 12)  # 員工編號
            worksheet.set_column('E:E', 15)  # 部門
            worksheet.set_column('F:F', 12)  # 考勤狀態
            worksheet.set_column('G:G', 15)  # 打卡設備
            worksheet.set_column('H:H', 25)  # 備註
            worksheet.set_column('I:I', 20)  # 記錄時間
            
            logger.debug("Excel 欄位寬度調整完成")
            
        except Exception as format_error:
            logger.warning(f"調整 Excel 欄位寬度失敗: {format_error}")
            # 這不是致命錯誤，繼續處理
        
        # ===== 完成 Excel 檔案 =====
        try:
            workbook.close()
            output.seek(0)
            logger.debug("Excel 檔案生成完成")
            
        except Exception as close_error:
            logger.error(f"完成 Excel 檔案失敗: {close_error}")
            conn.close()
            return jsonify({
                "success": False,
                "error": f"完成 Excel 檔案失敗: {str(close_error)}"
            }), 500
        
        # ===== 關閉資料庫連接 =====
        conn.close()
        logger.debug("資料庫連接已關閉")
        
        # ===== 生成檔案名稱 =====
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"attendance_records_{timestamp}.xlsx"
        logger.info(f"準備下載 Excel 檔案: {filename}")
        
        # ===== 返回檔案 =====
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        # ===== 全域錯誤處理 =====
        error_msg = f"匯出打卡紀錄時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


# ====== 考勤作業管理 API ======

@app.route("/api/attendance/management", methods=["GET"])
def get_attendance_management():
    """
    考勤作業管理查詢 API
    
    功能說明：
    - 查詢日常考勤作業摘要
    - 顯示每人每日的考勤狀況（上下班時間、加班、遲到早退、請假等）
    - 需要員工有指派的班別排程才能正確計算考勤狀況
    - 支援多種篩選條件和分頁查詢
    
    查詢參數：
    - page: 頁碼 (預設: 1, 最小值: 1)
    - limit: 每頁筆數 (預設: 20, 範圍: 1-100)
    - employee_id: 員工ID (可選, 整數)
    - department_id: 部門ID (可選, 整數)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - attendance_status: 考勤狀況 (可選, 值: normal/late/early_leave/absent/overtime)
    
    返回格式：
    {
        "success": true,
        "records": [...],           // 考勤作業記錄陣列
        "pagination": {...},        // 分頁資訊
        "statistics": {...},        // 統計資訊
        "summary": {...}           // 摘要資訊
    }
    
    錯誤處理：
    - 參數驗證失敗：返回 400 錯誤
    - 資料庫連接失敗：返回 500 錯誤
    - SQL 查詢錯誤：記錄詳細錯誤並返回 500
    """
    try:
        logger.info("開始處理考勤作業管理查詢請求")
        
        # ===== 參數獲取與驗證 =====
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        attendance_status = request.args.get('attendance_status')
        
        # 參數驗證
        if page < 1:
            logger.warning(f"無效的頁碼參數: {page}")
            return jsonify({"success": False, "error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            logger.warning(f"無效的每頁筆數參數: {limit}")
            return jsonify({"success": False, "error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 日期格式驗證
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的開始日期格式: {start_date}")
                return jsonify({"success": False, "error": "開始日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                logger.warning(f"無效的結束日期格式: {end_date}")
                return jsonify({"success": False, "error": "結束日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        # 考勤狀況驗證
        valid_statuses = ['normal', 'late', 'early_leave', 'absent', 'overtime']
        if attendance_status and attendance_status not in valid_statuses:
            logger.warning(f"無效的考勤狀況參數: {attendance_status}")
            return jsonify({"success": False, "error": f"考勤狀況必須是以下之一: {', '.join(valid_statuses)}"}), 400
        
        logger.info(f"考勤作業查詢參數驗證通過 - page: {page}, limit: {limit}, employee_id: {employee_id}, department_id: {department_id}, start_date: {start_date}, end_date: {end_date}, attendance_status: {attendance_status}")
        
        # ===== 計算偏移量 =====
        offset = (page - 1) * limit
        logger.debug(f"計算偏移量: offset = {offset}")
        
        # ===== 資料庫連接 =====
        try:
            conn = create_connection()
            if not conn:
                logger.error("資料庫連接失敗")
                return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
            
            cursor = conn.cursor()
            logger.debug("資料庫連接成功")
        except Exception as db_error:
            logger.error(f"資料庫連接異常: {db_error}")
            return jsonify({"success": False, "error": f"資料庫連接異常: {str(db_error)}"}), 500
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        if employee_id:
            where_conditions.append("a.employee_id = ?")
            query_params.append(employee_id)
            logger.debug(f"添加員工ID篩選條件: {employee_id}")
        
        if department_id:
            where_conditions.append("e.department_id = ?")
            query_params.append(department_id)
            logger.debug(f"添加部門ID篩選條件: {department_id}")
        
        if start_date:
            where_conditions.append("DATE(COALESCE(a.check_in, a.check_out)) >= ?")
            query_params.append(start_date)
            logger.debug(f"添加開始日期篩選條件: {start_date}")
        
        if end_date:
            where_conditions.append("DATE(COALESCE(a.check_in, a.check_out)) <= ?")
            query_params.append(end_date)
            logger.debug(f"添加結束日期篩選條件: {end_date}")
        
        if attendance_status:
            # 根據不同的考勤狀況構建條件
            if attendance_status == 'normal':
                where_conditions.append("a.status = 'normal'")
            elif attendance_status == 'late':
                where_conditions.append("a.status = 'late'")
            elif attendance_status == 'early_leave':
                where_conditions.append("a.status = 'early_leave'")
            elif attendance_status == 'absent':
                where_conditions.append("a.status = 'absent'")
            elif attendance_status == 'overtime':
                where_conditions.append("a.status LIKE '%overtime%'")
            logger.debug(f"添加考勤狀況篩選條件: {attendance_status}")
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        logger.debug(f"構建的 WHERE 子句: {where_clause}")
        
        # ===== 查詢總記錄數 =====
        try:
            count_query = f"""
                SELECT COUNT(*) as total
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                -- 左聯結排班記錄（可能沒有）
                LEFT JOIN schedules s ON a.employee_id = s.employee_id 
                    AND DATE(COALESCE(a.check_in, a.check_out)) = DATE(s.shift_date)
                LEFT JOIN shifts sh ON s.shift_id = sh.id
                -- 左聯結員工預設班表（作為備用）
                LEFT JOIN shifts default_sh ON e.shift_type = default_sh.id
                {where_clause}
            """
            
            logger.debug(f"執行總數查詢 SQL: {count_query}")
            cursor.execute(count_query, query_params)
            total_records = cursor.fetchone()[0]
            logger.info(f"查詢到總記錄數: {total_records}")
            
        except Exception as count_error:
            logger.error(f"查詢總記錄數失敗: {count_error}")
            conn.close()
            return jsonify({"success": False, "error": f"查詢總記錄數失敗: {str(count_error)}"}), 500
        
        # ===== 查詢詳細記錄 =====
        try:
            records_query = f"""
                SELECT 
                    a.id,
                    a.employee_id,
                    a.check_in,
                    a.check_out,
                    a.status,
                    a.note,
                    a.created_at,
                    e.name as employee_name,
                    e.employee_id as employee_code,
                    d.name as department_name,
                    s.shift_date as schedule_date,
                    -- 優先使用排班的班表，如果沒有排班則使用員工預設班表
                    COALESCE(sh.name, default_sh.name) as shift_name,
                    COALESCE(sh.start_time, default_sh.start_time) as shift_start_time,
                    COALESCE(sh.end_time, default_sh.end_time) as shift_end_time,
                    COALESCE(sh.break_duration_minutes, default_sh.break_duration_minutes) as shift_break_duration,
                    -- 計算遲到分鐘數（使用有效的班表時間）
                    CASE 
                        WHEN a.check_in IS NOT NULL AND COALESCE(sh.start_time, default_sh.start_time) IS NOT NULL 
                        THEN MAX(0, (strftime('%s', a.check_in) - strftime('%s', DATE(a.check_in) || ' ' || COALESCE(sh.start_time, default_sh.start_time))) / 60)
                        ELSE 0 
                    END as late_minutes,
                    -- 計算早退分鐘數（使用有效的班表時間）
                    CASE 
                        WHEN a.check_out IS NOT NULL AND COALESCE(sh.end_time, default_sh.end_time) IS NOT NULL 
                        THEN MAX(0, (strftime('%s', DATE(a.check_out) || ' ' || COALESCE(sh.end_time, default_sh.end_time)) - strftime('%s', a.check_out)) / 60)
                        ELSE 0 
                    END as early_leave_minutes,
                    -- 計算工作時長（小時）
                    CASE 
                        WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                        THEN ROUND((strftime('%s', a.check_out) - strftime('%s', a.check_in)) / 3600.0, 2)
                        ELSE 0 
                    END as work_hours,
                    -- 計算加班時長（小時）（使用有效的班表時間）
                    CASE 
                        WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                             AND COALESCE(sh.start_time, default_sh.start_time) IS NOT NULL 
                             AND COALESCE(sh.end_time, default_sh.end_time) IS NOT NULL
                        THEN MAX(0, ROUND(((strftime('%s', a.check_out) - strftime('%s', a.check_in)) - 
                                          (strftime('%s', DATE(a.check_in) || ' ' || COALESCE(sh.end_time, default_sh.end_time)) - 
                                           strftime('%s', DATE(a.check_in) || ' ' || COALESCE(sh.start_time, default_sh.start_time))) - 
                                          COALESCE(COALESCE(sh.break_duration_minutes, default_sh.break_duration_minutes) * 60, 0)) / 3600.0, 2))
                        ELSE 0 
                    END as overtime_hours
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                -- 左聯結排班記錄（可能沒有）
                LEFT JOIN schedules s ON a.employee_id = s.employee_id 
                    AND DATE(COALESCE(a.check_in, a.check_out)) = DATE(s.shift_date)
                LEFT JOIN shifts sh ON s.shift_id = sh.id
                -- 左聯結員工預設班表（作為備用）
                LEFT JOIN shifts default_sh ON e.shift_type = default_sh.id
                {where_clause}
                ORDER BY a.created_at DESC, a.id DESC
                LIMIT ? OFFSET ?
            """
            
            logger.debug(f"執行記錄查詢 SQL: {records_query}")
            cursor.execute(records_query, query_params + [limit, offset])
            
            columns = [description[0] for description in cursor.description]
            logger.debug(f"查詢結果欄位: {columns}")
            
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                
                # 格式化時間欄位
                if record.get('check_in'):
                    record['check_in_formatted'] = record['check_in']
                if record.get('check_out'):
                    record['check_out_formatted'] = record['check_out']
                if record.get('created_at'):
                    record['created_at_formatted'] = record['created_at']
                
                # 格式化班別時間
                if record.get('shift_start_time'):
                    record['shift_start_time_formatted'] = record['shift_start_time']
                if record.get('shift_end_time'):
                    record['shift_end_time_formatted'] = record['shift_end_time']
                
                # 計算考勤狀況摘要
                attendance_summary = []
                if record.get('late_minutes', 0) > 0:
                    attendance_summary.append(f"遲到 {int(record['late_minutes'])} 分鐘")
                if record.get('early_leave_minutes', 0) > 0:
                    attendance_summary.append(f"早退 {int(record['early_leave_minutes'])} 分鐘")
                if record.get('overtime_hours', 0) > 0:
                    attendance_summary.append(f"加班 {record['overtime_hours']} 小時")
                
                record['attendance_summary'] = '; '.join(attendance_summary) if attendance_summary else '正常'
                
                records.append(record)
            
            logger.info(f"查詢到 {len(records)} 筆考勤作業記錄")
            
        except Exception as records_error:
            logger.error(f"查詢考勤作業記錄失敗: {records_error}")
            conn.close()
            return jsonify({"success": False, "error": f"查詢考勤作業記錄失敗: {str(records_error)}"}), 500
        
        # ===== 計算統計資訊 =====
        try:
            stats_query = f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT a.employee_id) as total_employees,
                    COUNT(DISTINCT DATE(COALESCE(a.check_in, a.check_out))) as total_days,
                    COUNT(CASE WHEN a.status = 'normal' THEN 1 END) as normal_count,
                    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                    COUNT(CASE WHEN a.status = 'early_leave' THEN 1 END) as early_leave_count,
                    COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
                    COUNT(CASE WHEN a.status LIKE '%overtime%' THEN 1 END) as overtime_count,
                    AVG(CASE 
                        WHEN a.check_in IS NOT NULL AND a.check_out IS NOT NULL 
                        THEN (strftime('%s', a.check_out) - strftime('%s', a.check_in)) / 3600.0
                        ELSE NULL 
                    END) as avg_work_hours
                FROM attendance a
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN departments d ON e.department_id = d.id
                -- 左聯結排班記錄（可能沒有）
                LEFT JOIN schedules s ON a.employee_id = s.employee_id 
                    AND DATE(COALESCE(a.check_in, a.check_out)) = DATE(s.shift_date)
                LEFT JOIN shifts sh ON s.shift_id = sh.id
                -- 左聯結員工預設班表（作為備用）
                LEFT JOIN shifts default_sh ON e.shift_type = default_sh.id
                {where_clause}
            """
            
            logger.debug(f"執行統計查詢 SQL: {stats_query}")
            cursor.execute(stats_query, query_params)
            stats_row = cursor.fetchone()
            
            statistics = {
                "total_records": stats_row[0],
                "total_employees": stats_row[1],
                "total_days": stats_row[2],
                "avg_records_per_day": round(stats_row[0] / max(stats_row[2], 1), 1),
                "avg_work_hours": round(stats_row[8] or 0, 2),
                "status_breakdown": {
                    "normal": stats_row[3],
                    "late": stats_row[4],
                    "early_leave": stats_row[5],
                    "absent": stats_row[6],
                    "overtime": stats_row[7]
                }
            }
            
            logger.info(f"考勤作業統計資訊計算完成: {statistics}")
            
        except Exception as stats_error:
            logger.error(f"計算考勤作業統計資訊失敗: {stats_error}")
            # 統計失敗不影響主要功能，使用預設值
            statistics = {
                "total_records": total_records,
                "total_employees": 0,
                "total_days": 0,
                "avg_records_per_day": 0,
                "avg_work_hours": 0,
                "status_breakdown": {
                    "normal": 0,
                    "late": 0,
                    "early_leave": 0,
                    "absent": 0,
                    "overtime": 0
                }
            }
            logger.warning("使用預設考勤作業統計資訊")
        
        # ===== 關閉資料庫連接 =====
        conn.close()
        logger.debug("資料庫連接已關閉")
        
        # ===== 計算總頁數 =====
        total_pages = (total_records + limit - 1) // limit
        logger.debug(f"計算總頁數: {total_pages}")
        
        # ===== 構建響應資料 =====
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "statistics": statistics,
            "summary": {
                "query_filters": len(where_conditions),
                "has_shift_assignments": sum(1 for r in records if r.get('shift_name')),
                "missing_shift_assignments": sum(1 for r in records if not r.get('shift_name')),
                "query_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        logger.info(f"考勤作業管理查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        # ===== 全域錯誤處理 =====
        error_msg = f"查詢考勤作業管理時發生未預期的錯誤: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return jsonify({
            "success": False,
            "error": error_msg,
            "error_type": type(e).__name__,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500


# ===== 考勤記錄生成/重新計算 API =====
@app.route("/api/attendance/management/generate", methods=["POST"])
def generate_attendance_records():
    """
    考勤記錄生成/重新計算 API
    
    功能說明：
    - 批量生成或重新計算考勤記錄
    - 根據排班資料和打卡記錄計算考勤狀況
    - 支援指定日期範圍和員工範圍
    
    請求參數：
    {
        "start_date": "2025-06-01",     // 開始日期 (必填)
        "end_date": "2025-06-02",       // 結束日期 (必填)
        "employee_ids": [1, 2, 3],      // 員工ID陣列 (可選，空則處理所有員工)
        "overwrite": true,              // 是否覆蓋現有記錄 (預設: false)
        "calculate_overtime": true,     // 是否計算加班 (預設: true)
        "apply_rules": true            // 是否套用考勤規則 (預設: true)
    }
    
    返回格式：
    {
        "success": true,
        "message": "考勤記錄生成完成",
        "summary": {
            "processed_employees": 10,
            "processed_days": 5,
            "generated_records": 50,
            "updated_records": 5,
            "errors": 0
        }
    }
    """
    try:
        logger.info("開始處理考勤記錄生成請求")
        
        # ===== 獲取請求資料 =====
        data = request.get_json()
        if not data:
            logger.warning("請求資料為空")
            return jsonify({"success": False, "error": "請求資料不能為空"}), 400
        
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        employee_ids = data.get('employee_ids', [])
        overwrite = data.get('overwrite', False)
        calculate_overtime = data.get('calculate_overtime', True)
        apply_rules = data.get('apply_rules', True)
        
        # ===== 參數驗證 =====
        if not start_date or not end_date:
            logger.warning("缺少必要的日期參數")
            return jsonify({"success": False, "error": "開始日期和結束日期為必填項"}), 400
        
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            logger.warning(f"日期格式錯誤: start_date={start_date}, end_date={end_date}")
            return jsonify({"success": False, "error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if start_dt > end_dt:
            logger.warning("開始日期不能晚於結束日期")
            return jsonify({"success": False, "error": "開始日期不能晚於結束日期"}), 400
        
        logger.info(f"考勤記錄生成參數: start_date={start_date}, end_date={end_date}, employee_ids={employee_ids}, overwrite={overwrite}")
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        if not conn:
            logger.error("資料庫連接失敗")
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        cursor = conn.cursor()
        
        # ===== 統計變數 =====
        processed_employees = 0
        processed_days = 0
        generated_records = 0
        updated_records = 0
        errors = 0
        
        try:
            # ===== 獲取要處理的員工列表 =====
            if employee_ids:
                employee_filter = f"WHERE id IN ({','.join(['?' for _ in employee_ids])})"
                cursor.execute(f"SELECT id, name, employee_id FROM employees {employee_filter}", employee_ids)
            else:
                cursor.execute("SELECT id, name, employee_id FROM employees WHERE status = 'active'")
            
            employees = cursor.fetchall()
            logger.info(f"找到 {len(employees)} 名員工需要處理")
            
            # ===== 遍歷每個員工 =====
            for emp_id, emp_name, emp_code in employees:
                processed_employees += 1
                logger.debug(f"處理員工: {emp_name} ({emp_code})")
                
                # ===== 遍歷日期範圍 =====
                current_date = start_dt
                while current_date <= end_dt:
                    processed_days += 1
                    date_str = current_date.strftime('%Y-%m-%d')
                    
                    try:
                        # ===== 檢查是否已有考勤記錄 =====
                        cursor.execute("""
                            SELECT id FROM attendance 
                            WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out)) = ?
                        """, (emp_id, date_str))
                        
                        existing_record = cursor.fetchone()
                        
                        if existing_record and not overwrite:
                            logger.debug(f"員工 {emp_name} 在 {date_str} 已有考勤記錄，跳過")
                            current_date += timedelta(days=1)
                            continue
                        
                        # ===== 獲取排班資訊 =====
                        cursor.execute("""
                            SELECT s.id, s.shift_id, sh.name, sh.start_time, sh.end_time, sh.break_duration_minutes
                            FROM schedules s
                            JOIN shifts sh ON s.shift_id = sh.id
                            WHERE s.employee_id = ? AND s.shift_date = ?
                        """, (emp_id, date_str))
                        
                        schedule = cursor.fetchone()
                        
                        if not schedule:
                            logger.debug(f"員工 {emp_name} 在 {date_str} 沒有排班，跳過")
                            current_date += timedelta(days=1)
                            continue
                        
                        schedule_id, shift_id, shift_name, shift_start, shift_end, break_duration = schedule
                        
                        # ===== 模擬生成考勤記錄 =====
                        # 這裡可以根據實際需求實現更複雜的邏輯
                        # 目前簡化為基本的記錄生成
                        
                        if existing_record:
                            # 更新現有記錄
                            cursor.execute("""
                                UPDATE attendance 
                                SET status = 'normal', note = '系統重新計算', updated_at = ?
                                WHERE id = ?
                            """, (datetime.now(), existing_record[0]))
                            updated_records += 1
                            logger.debug(f"更新員工 {emp_name} 在 {date_str} 的考勤記錄")
                        else:
                            # 生成新記錄
                            check_in_time = f"{date_str} {shift_start}"
                            check_out_time = f"{date_str} {shift_end}"
                            
                            cursor.execute("""
                                INSERT INTO attendance (employee_id, check_in, check_out, status, note, created_at)
                                VALUES (?, ?, ?, 'normal', '系統生成', ?)
                            """, (emp_id, check_in_time, check_out_time, datetime.now()))
                            generated_records += 1
                            logger.debug(f"生成員工 {emp_name} 在 {date_str} 的考勤記錄")
                        
                    except Exception as day_error:
                        logger.error(f"處理員工 {emp_name} 在 {date_str} 時發生錯誤: {day_error}")
                        errors += 1
                    
                    current_date += timedelta(days=1)
            
            # ===== 提交事務 =====
            conn.commit()
            logger.info("考勤記錄生成事務提交成功")
            
        except Exception as process_error:
            logger.error(f"考勤記錄生成過程中發生錯誤: {process_error}")
            conn.rollback()
            conn.close()
            return jsonify({"success": False, "error": f"處理失敗: {str(process_error)}"}), 500
        
        finally:
            conn.close()
        
        # ===== 構建響應 =====
        summary = {
            "processed_employees": processed_employees,
            "processed_days": processed_days,
            "generated_records": generated_records,
            "updated_records": updated_records,
            "errors": errors
        }
        
        logger.info(f"考勤記錄生成完成: {summary}")
        
        return jsonify({
            "success": True,
            "message": "考勤記錄生成完成",
            "summary": summary
        })
        
    except Exception as e:
        logger.error(f"考勤記錄生成API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500


@app.route("/api/attendance/processing", methods=["GET"])
def get_attendance_processing_status():
    """
    獲取考勤整理狀態 API
    
    功能說明：
    - 獲取待處理的考勤資料統計
    - 顯示系統處理狀態
    - 提供處理建議和預估時間
    
    返回格式：
    {
        "success": true,
        "status": {
            "pending_days": 15,
            "pending_employees": 25,
            "estimated_time": "約 5 分鐘",
            "anomaly_records": 3,
            "last_processed": "2025-06-01 14:30:00",
            "processing_status": "idle"
        },
        "suggestions": [
            "建議在非高峰時段進行處理",
            "發現 3 筆異常記錄需要人工確認"
        ]
    }
    """
    try:
        logger.info("獲取考勤整理狀態")
        
        conn = create_connection()
        if not conn:
            logger.error("資料庫連接失敗")
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        cursor = conn.cursor()
        
        try:
            # ===== 計算待處理天數 =====
            cursor.execute("""
                SELECT COUNT(DISTINCT DATE(COALESCE(check_in, check_out))) as pending_days
                FROM attendance 
                WHERE status IS NULL OR status = 'pending'
            """)
            pending_days = cursor.fetchone()[0] or 0
            
            # ===== 計算待處理員工數 =====
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as pending_employees
                FROM attendance 
                WHERE status IS NULL OR status = 'pending'
            """)
            pending_employees = cursor.fetchone()[0] or 0
            
            # ===== 計算異常記錄數 =====
            cursor.execute("""
                SELECT COUNT(*) as anomaly_records
                FROM attendance 
                WHERE (check_in IS NULL AND check_out IS NULL) 
                   OR (check_in IS NOT NULL AND check_out IS NOT NULL AND check_out < check_in)
            """)
            anomaly_records = cursor.fetchone()[0] or 0
            
            # ===== 獲取最後處理時間 =====
            cursor.execute("""
                SELECT MAX(created_at) as last_processed
                FROM attendance 
                WHERE status IN ('normal', 'late', 'early_leave', 'overtime')
            """)
            last_processed_result = cursor.fetchone()[0]
            last_processed = last_processed_result if last_processed_result else "尚未處理"
            
            # ===== 預估處理時間 =====
            total_records = pending_days * pending_employees
            if total_records == 0:
                estimated_time = "無需處理"
            elif total_records < 100:
                estimated_time = "約 1 分鐘"
            elif total_records < 500:
                estimated_time = "約 3 分鐘"
            elif total_records < 1000:
                estimated_time = "約 5 分鐘"
            else:
                estimated_time = "約 10 分鐘"
            
            # ===== 生成建議 =====
            suggestions = []
            if total_records > 1000:
                suggestions.append("建議在非高峰時段進行處理")
            if anomaly_records > 0:
                suggestions.append(f"發現 {anomaly_records} 筆異常記錄需要人工確認")
            if pending_days > 30:
                suggestions.append("待處理天數較多，建議分批處理")
            
            # ===== 構建響應資料 =====
            status = {
                "pending_days": pending_days,
                "pending_employees": pending_employees,
                "estimated_time": estimated_time,
                "anomaly_records": anomaly_records,
                "last_processed": last_processed,
                "processing_status": "idle"  # 可以擴展為實際的處理狀態
            }
            
            logger.info(f"考勤整理狀態: {status}")
            
            return jsonify({
                "success": True,
                "status": status,
                "suggestions": suggestions
            })
            
        except Exception as query_error:
            logger.error(f"查詢考勤整理狀態時發生錯誤: {query_error}")
            return jsonify({"success": False, "error": f"查詢失敗: {str(query_error)}"}), 500
        
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"考勤整理狀態API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500


@app.route("/api/attendance/processing/execute", methods=["POST"])
def execute_attendance_processing():
    """
    執行考勤整理處理 API
    
    功能說明：
    - 啟動考勤資料整理處理
    - 計算遲到、早退、加班時間
    - 整合請假資料
    - 更新考勤狀態
    
    請求參數：
    {
        "date_range": {
            "start_date": "2025-06-01",
            "end_date": "2025-06-02"
        },
        "employee_scope": {
            "type": "all",  // all, department, specific
            "department_ids": [1, 2],
            "employee_ids": [1, 2, 3]
        },
        "processing_options": {
            "calculate_late_early": true,
            "calculate_overtime": true,
            "integrate_leaves": true,
            "overwrite_existing": false
        }
    }
    
    返回格式：
    {
        "success": true,
        "message": "考勤整理處理已啟動",
        "processing_id": "proc_20250602_143000",
        "total_records": 150,
        "estimated_completion": "2025-06-02 14:35:00"
    }
    """
    try:
        logger.info("開始執行考勤整理處理")
        
        # ===== 獲取請求資料 =====
        data = request.get_json()
        if not data:
            logger.warning("請求資料為空")
            return jsonify({"success": False, "error": "請求資料不能為空"}), 400
        
        date_range = data.get('date_range', {})
        employee_scope = data.get('employee_scope', {})
        processing_options = data.get('processing_options', {})
        
        start_date = date_range.get('start_date')
        end_date = date_range.get('end_date')
        
        # ===== 參數驗證 =====
        if not start_date or not end_date:
            logger.warning("缺少必要的日期參數")
            return jsonify({"success": False, "error": "開始日期和結束日期為必填項"}), 400
        
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            logger.warning(f"日期格式錯誤: start_date={start_date}, end_date={end_date}")
            return jsonify({"success": False, "error": "日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if start_dt > end_dt:
            logger.warning("開始日期不能晚於結束日期")
            return jsonify({"success": False, "error": "開始日期不能晚於結束日期"}), 400
        
        # ===== 處理選項 =====
        calculate_late_early = processing_options.get('calculate_late_early', True)
        calculate_overtime = processing_options.get('calculate_overtime', True)
        integrate_leaves = processing_options.get('integrate_leaves', True)
        overwrite_existing = processing_options.get('overwrite_existing', False)
        
        logger.info(f"考勤整理處理參數: date_range={date_range}, employee_scope={employee_scope}, options={processing_options}")
        
        # ===== 生成處理ID =====
        processing_id = f"proc_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        if not conn:
            logger.error("資料庫連接失敗")
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        cursor = conn.cursor()
        
        try:
            # ===== 計算要處理的記錄數 =====
            query_conditions = ["DATE(COALESCE(check_in, check_out)) BETWEEN ? AND ?"]
            query_params = [start_date, end_date]
            
            scope_type = employee_scope.get('type', 'all')
            if scope_type == 'department':
                department_ids = employee_scope.get('department_ids', [])
                if department_ids:
                    query_conditions.append(f"employee_id IN (SELECT id FROM employees WHERE department_id IN ({','.join(['?' for _ in department_ids])}))")
                    query_params.extend(department_ids)
            elif scope_type == 'specific':
                employee_ids = employee_scope.get('employee_ids', [])
                if employee_ids:
                    query_conditions.append(f"employee_id IN ({','.join(['?' for _ in employee_ids])})")
                    query_params.extend(employee_ids)
            
            if not overwrite_existing:
                query_conditions.append("(status IS NULL OR status = 'pending')")
            
            count_query = f"""
                SELECT COUNT(*) as total_records
                FROM attendance 
                WHERE {' AND '.join(query_conditions)}
            """
            
            cursor.execute(count_query, query_params)
            total_records = cursor.fetchone()[0] or 0
            
            # ===== 預估完成時間 =====
            days_diff = (end_dt - start_dt).days + 1
            estimated_minutes = max(1, total_records // 100)
            estimated_completion = (datetime.now() + timedelta(minutes=estimated_minutes)).strftime('%Y-%m-%d %H:%M:%S')
            
            # ===== 模擬處理邏輯 =====
            # 在實際應用中，這裡應該啟動背景任務或使用任務隊列
            # 目前簡化為同步處理
            
            processed_count = 0
            
            # 獲取要處理的考勤記錄
            query = f"""
                SELECT id, employee_id, check_in, check_out, status
                FROM attendance 
                WHERE {' AND '.join(query_conditions)}
                ORDER BY employee_id, check_in
            """
            
            cursor.execute(query, query_params)
            records = cursor.fetchall()
            
            logger.info(f"找到 {len(records)} 筆考勤記錄需要處理")
            
            # ===== 處理每筆記錄 =====
            for record_id, emp_id, check_in, check_out, current_status in records:
                try:
                    # ===== 基本狀態計算 =====
                    new_status = 'normal'  # 預設狀態
                    
                    # 這裡可以實現更複雜的計算邏輯
                    # 例如：比較實際打卡時間與排班時間，計算遲到早退等
                    
                    if check_in and check_out:
                        # 有完整打卡記錄
                        if calculate_late_early:
                            # 檢查是否遲到或早退（簡化邏輯）
                            pass
                        
                        if calculate_overtime:
                            # 計算加班時間（簡化邏輯）
                            pass
                        
                        if integrate_leaves:
                            # 整合請假資料（簡化邏輯）
                            pass
                    
                    # ===== 更新記錄狀態 =====
                    cursor.execute("""
                        UPDATE attendance 
                        SET status = ?, updated_at = ?
                        WHERE id = ?
                    """, (new_status, datetime.now(), record_id))
                    
                    processed_count += 1
                    
                except Exception as record_error:
                    logger.error(f"處理考勤記錄 {record_id} 時發生錯誤: {record_error}")
                    continue
            
            # ===== 提交事務 =====
            conn.commit()
            logger.info(f"考勤整理處理完成，處理了 {processed_count} 筆記錄")
            
        except Exception as process_error:
            logger.error(f"考勤整理處理過程中發生錯誤: {process_error}")
            conn.rollback()
            conn.close()
            return jsonify({"success": False, "error": f"處理失敗: {str(process_error)}"}), 500
        
        finally:
            conn.close()
        
        # ===== 構建響應 =====
        return jsonify({
            "success": True,
            "message": "考勤整理處理已完成",
            "processing_id": processing_id,
            "total_records": total_records,
            "estimated_completion": estimated_completion,
            "processed_count": processed_count
        })
        
    except Exception as e:
        logger.error(f"考勤整理處理API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500


@app.route("/api/attendance/import-text", methods=["POST"])
def import_attendance_text():
    """
    匯入打卡機文字檔案 API
    
    功能說明：
    - 接收打卡機匯出的 txt 檔案
    - 解析檔案格式並驗證資料
    - 自動配對上下班時間
    - 支援自動創建員工
    - 返回詳細的匯入結果
    
    檔案格式：機器編號,員工編號,日期,時間,狀態
    例如：011,00000701,20241220,0945,0
    
    參數：
    - file: 上傳的文字檔案
    - overwrite: 是否覆蓋現有記錄 (boolean)
    - auto_create_employee: 是否自動創建員工 (boolean)
    
    返回格式：
    {
        "success": true,
        "imported": 150,
        "skipped": 5,
        "errors": 2,
        "total": 157,
        "created_employees": 3,
        "message": "匯入完成"
    }
    """
    try:
        logger.info("開始處理文字檔匯入請求")
        
        # ===== 檢查檔案 =====
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "未找到檔案"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "未選擇檔案"}), 400
        
        if not file.filename.lower().endswith('.txt'):
            return jsonify({"success": False, "error": "僅支援 .txt 格式檔案"}), 400
        
        # ===== 獲取選項 =====
        overwrite = request.form.get('overwrite', 'false').lower() == 'true'
        auto_create_employee = request.form.get('auto_create_employee', 'true').lower() == 'true'
        
        logger.info(f"匯入選項 - 覆蓋現有記錄: {overwrite}, 自動創建員工: {auto_create_employee}")
        
        # ===== 讀取檔案內容 =====
        try:
            file_content = file.read().decode('utf-8')
        except UnicodeDecodeError:
            try:
                file.seek(0)
                file_content = file.read().decode('big5')
            except UnicodeDecodeError:
                return jsonify({"success": False, "error": "檔案編碼不支援，請使用 UTF-8 或 Big5 編碼"}), 400
        
        # ===== 解析檔案內容 =====
        raw_records = []
        lines = file_content.strip().split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 解析每行資料：機器編號,員工編號,日期,時間,狀態
                parts = line.split(',')
                if len(parts) != 5:
                    logger.warning(f"第 {line_num} 行格式錯誤，跳過: {line}")
                    continue
                
                machine_code = parts[0].strip()
                employee_code = parts[1].strip()
                date_str = parts[2].strip()
                time_str = parts[3].strip()
                status_code = parts[4].strip()
                
                # 轉換日期時間格式
                if len(date_str) != 8 or len(time_str) != 4:
                    logger.warning(f"第 {line_num} 行日期時間格式錯誤，跳過: {line}")
                    continue
                
                formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                formatted_time = f"{time_str[:2]}:{time_str[2:4]}:00"
                timestamp = f"{formatted_date} {formatted_time}"
                
                raw_records.append({
                    'machine_code': machine_code,
                    'employee_code': employee_code,
                    'timestamp': timestamp,
                    'status_code': status_code,
                    'line_num': line_num
                })
                
            except Exception as e:
                logger.error(f"第 {line_num} 行解析錯誤: {e}, 內容: {line}")
                continue
        
        if not raw_records:
            return jsonify({"success": False, "error": "檔案中沒有有效的打卡記錄"}), 400
        
        logger.info(f"成功解析 {len(raw_records)} 筆打卡記錄")
        
        # ===== 獲取員工對應表 =====
        conn = create_connection()
        if not conn:
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT id, employee_id, card_number FROM employees WHERE status = 'active'")
            employees = cursor.fetchall()
            
            # 建立對應表
            employee_mapping = {}
            for emp_id, emp_code, card_number in employees:
                if card_number:
                    employee_mapping[card_number] = emp_id
                else:
                    # 如果沒有 card_number，嘗試使用員工編號轉換
                    if emp_code.startswith('E'):
                        numeric_part = emp_code[1:]
                    else:
                        numeric_part = emp_code
                    
                    try:
                        padded_code = f"{int(numeric_part):08d}"
                        employee_mapping[padded_code] = emp_id
                    except ValueError:
                        continue
            
            logger.info(f"建立員工對應表，共 {len(employee_mapping)} 筆")
            
            # ===== 配對上下班記錄（考慮跨日情況） =====
            from collections import defaultdict
            from datetime import datetime, timedelta
            
            # 重新組織記錄，考慮6點前為前一天的下班
            adjusted_records = []
            for record in raw_records:
                timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
                hour = timestamp.hour
                
                # 如果是6點前，視為前一天的下班記錄
                if hour < 6:
                    # 調整為前一天的日期
                    adjusted_date = (timestamp - timedelta(days=1)).strftime('%Y-%m-%d')
                    adjusted_timestamp = f"{adjusted_date} {timestamp.strftime('%H:%M:%S')}"
                    
                    logger.info(f"跨日調整: {record['timestamp']} -> {adjusted_timestamp} (員工: {record['employee_code']})")
                    
                    adjusted_records.append({
                        'machine_code': record['machine_code'],
                        'employee_code': record['employee_code'],
                        'timestamp': adjusted_timestamp,
                        'original_timestamp': record['timestamp'],
                        'status_code': record['status_code'],
                        'line_num': record['line_num'],
                        'is_previous_day_checkout': True
                    })
                else:
                    logger.info(f"正常記錄: {record['timestamp']} (員工: {record['employee_code']})")
                    adjusted_records.append({
                        'machine_code': record['machine_code'],
                        'employee_code': record['employee_code'],
                        'timestamp': record['timestamp'],
                        'original_timestamp': record['timestamp'],
                        'status_code': record['status_code'],
                        'line_num': record['line_num'],
                        'is_previous_day_checkout': False
                    })
            
            # 按調整後的日期分組
            grouped = defaultdict(list)
            for record in adjusted_records:
                date = record['timestamp'].split(' ')[0]
                key = (record['employee_code'], date)
                grouped[key].append(record)
            
            attendance_records = []
            for (employee_code, date), day_records in grouped.items():
                # 按時間排序
                day_records.sort(key=lambda x: x['timestamp'])
                
                # 分離上班和下班記錄
                checkin_records = []
                checkout_records = []
                
                for record in day_records:
                    if record.get('is_previous_day_checkout', False):
                        # 6點前的記錄作為下班
                        checkout_records.append(record)
                    else:
                        # 6點後的記錄需要判斷
                        timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
                        hour = timestamp.hour
                        
                        if hour >= 6 and hour < 14:  # 6-14點視為上班時間
                            checkin_records.append(record)
                        else:  # 14點後視為下班時間
                            checkout_records.append(record)
                
                # 產生考勤記錄
                check_in_time = None
                check_out_time = None
                device_id = None
                total_records = len(day_records)
                note_parts = []
                
                if checkin_records:
                    # 取最早的上班記錄
                    earliest_checkin = min(checkin_records, key=lambda x: x['timestamp'])
                    check_in_time = earliest_checkin['timestamp']
                    device_id = earliest_checkin['machine_code']
                    note_parts.append(f"上班: {len(checkin_records)}筆")
                
                if checkout_records:
                    # 取最晚的下班記錄
                    latest_checkout = max(checkout_records, key=lambda x: x['timestamp'])
                    check_out_time = latest_checkout['timestamp']
                    if not device_id:
                        device_id = latest_checkout['machine_code']
                    note_parts.append(f"下班: {len(checkout_records)}筆")
                    
                    # 檢查是否有跨日記錄
                    cross_day_records = [r for r in checkout_records if r.get('is_previous_day_checkout', False)]
                    if cross_day_records:
                        note_parts.append(f"跨日: {len(cross_day_records)}筆")
                
                if check_in_time or check_out_time:
                    attendance_records.append({
                        'employee_code': employee_code,
                        'date': date,
                        'check_in': check_in_time,
                        'check_out': check_out_time,
                        'device_id': device_id or day_records[0]['machine_code'],
                        'records_count': total_records,
                        'note_detail': ', '.join(note_parts)
                    })
            
            logger.info(f"配對完成，產生 {len(attendance_records)} 筆考勤記錄")
            
            # ===== 匯入資料庫 =====
            imported_count = 0
            skipped_count = 0
            error_count = 0
            created_employees = 0
            
            for record in attendance_records:
                try:
                    # 查找對應的員工ID
                    employee_id = employee_mapping.get(record['employee_code'])
                    
                    if not employee_id and auto_create_employee:
                        # 自動創建新員工
                        logger.info(f"創建新員工: 卡號={record['employee_code']}")
                        
                        # 生成員工編號
                        cursor.execute("SELECT MAX(CAST(SUBSTR(employee_id, 2) AS INTEGER)) FROM employees WHERE employee_id LIKE 'E%'")
                        result = cursor.fetchone()
                        max_num = result[0] if result and result[0] else 0
                        new_employee_id = f"E{max_num + 1:03d}"
                        
                        # 獲取預設部門ID（如果沒有部門，創建一個預設部門）
                        cursor.execute("SELECT id FROM departments LIMIT 1")
                        dept_result = cursor.fetchone()
                        if not dept_result:
                            # 創建預設部門
                            cursor.execute("""
                                INSERT INTO departments (name, description, created_at)
                                VALUES ('預設部門', '自動匯入時創建的預設部門', ?)
                            """, (datetime.now(),))
                            default_dept_id = cursor.lastrowid
                        else:
                            default_dept_id = dept_result[0]
                        
                        # 創建員工記錄
                        cursor.execute("""
                            INSERT INTO employees (employee_id, name, card_number, department_id, position, status, created_at)
                            VALUES (?, ?, ?, ?, '一般員工', 'active', ?)
                        """, (new_employee_id, f"員工_{record['employee_code']}", record['employee_code'], default_dept_id, datetime.now()))
                        
                        employee_id = cursor.lastrowid
                        employee_mapping[record['employee_code']] = employee_id
                        created_employees += 1
                        logger.info(f"成功創建新員工: ID={employee_id}, 編號={new_employee_id}")
                    
                    if not employee_id:
                        logger.warning(f"找不到員工編號對應且未啟用自動創建: {record['employee_code']}")
                        skipped_count += 1
                        continue
                    
                    # 檢查是否已存在記錄
                    check_query = """
                        SELECT id FROM attendance 
                        WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out)) = ?
                    """
                    cursor.execute(check_query, (employee_id, record['date']))
                    existing = cursor.fetchone()
                    
                    if existing and not overwrite:
                        logger.debug(f"記錄已存在，跳過: 員工 {record['employee_code']} 日期 {record['date']}")
                        skipped_count += 1
                        continue
                    
                    # 決定考勤狀態
                    status = 'normal'
                    if record['check_in'] and record['check_out']:
                        status = 'normal'
                    elif record['check_in'] and not record['check_out']:
                        status = 'incomplete'
                    elif not record['check_in'] and record['check_out']:
                        status = 'incomplete'
                    
                    base_note = f"匯入自打卡機 {record['device_id']}，原始記錄數: {record['records_count']}"
                    detail_note = record.get('note_detail', '')
                    note = f"{base_note}. {detail_note}" if detail_note else base_note
                    
                    attendance_id = None
                    if existing and overwrite:
                        # 更新現有記錄
                        cursor.execute("""
                            UPDATE attendance 
                            SET check_in = ?, check_out = ?, status = ?, device_id = ?, note = ?
                            WHERE id = ?
                        """, (record['check_in'], record['check_out'], status, record['device_id'], note, existing[0]))
                        attendance_id = existing[0]
                        
                        # 刪除舊的原始記錄
                        cursor.execute("DELETE FROM clock_raw_records WHERE attendance_id = ?", (attendance_id,))
                    else:
                        # 插入新記錄
                        cursor.execute("""
                            INSERT INTO attendance (employee_id, check_in, check_out, status, device_id, note)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (employee_id, record['check_in'], record['check_out'], status, record['device_id'], note))
                        attendance_id = cursor.lastrowid
                    
                    # 插入所有相關的原始打卡記錄
                    for raw_record in adjusted_records:
                        if (raw_record['employee_code'] == record['employee_code'] and 
                            raw_record['timestamp'].split(' ')[0] == record['date']):
                            cursor.execute("""
                                INSERT INTO clock_raw_records (employee_code, machine_code, timestamp, status_code, attendance_id)
                                VALUES (?, ?, ?, ?, ?)
                            """, (raw_record['employee_code'], raw_record['machine_code'], 
                                  raw_record['original_timestamp'], raw_record['status_code'], attendance_id))
                    
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"匯入記錄失敗: {e}, 記錄: {record}")
                    error_count += 1
                    continue
            
            # ===== 提交事務 =====
            conn.commit()
            
            result = {
                'success': True,
                'imported': imported_count,
                'skipped': skipped_count,
                'errors': error_count,
                'total': len(attendance_records),
                'created_employees': created_employees,
                'message': f"匯入完成！成功 {imported_count} 筆，跳過 {skipped_count} 筆，錯誤 {error_count} 筆"
            }
            
            logger.info(f"文字檔匯入完成: {result}")
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"匯入處理錯誤: {e}")
            conn.rollback()
            return jsonify({"success": False, "error": f"匯入處理失敗: {str(e)}"}), 500
        
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"文字檔匯入API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500


@app.route("/api/attendance/raw-records", methods=["GET"])
def get_raw_clock_records():
    """
    查詢原始打卡記錄 API
    
    功能說明：
    - 查詢打卡機的原始記錄
    - 支援按員工、日期範圍篩選
    - 顯示所有原始打卡狀態
    
    查詢參數：
    - employee_code: 員工卡號 (可選)
    - start_date: 開始日期 (可選, 格式: YYYY-MM-DD)
    - end_date: 結束日期 (可選, 格式: YYYY-MM-DD)
    - page: 頁碼 (預設: 1)
    - limit: 每頁筆數 (預設: 50)
    
    返回格式：
    {
        "success": true,
        "records": [...],
        "total": 100,
        "page": 1,
        "limit": 50
    }
    """
    try:
        logger.info("開始處理原始打卡記錄查詢請求")
        
        # ===== 參數獲取與驗證 =====
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 50, type=int)
        employee_code = request.args.get('employee_code')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 參數驗證
        if page < 1:
            return jsonify({"error": "頁碼必須大於 0"}), 400
        
        if limit < 1 or limit > 100:
            return jsonify({"error": "每頁筆數必須在 1-100 之間"}), 400
        
        # 日期格式驗證
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({"error": "開始日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({"error": "結束日期格式錯誤，請使用 YYYY-MM-DD 格式"}), 400
        
        # ===== 計算偏移量 =====
        offset = (page - 1) * limit
        
        # ===== 資料庫連接 =====
        conn = create_connection()
        if not conn:
            return jsonify({"error": "資料庫連接失敗"}), 500
        
        cursor = conn.cursor()
        
        # ===== 構建查詢條件 =====
        where_conditions = []
        query_params = []
        
        if employee_code:
            where_conditions.append("crr.employee_code = ?")
            query_params.append(employee_code)
        
        if start_date:
            where_conditions.append("DATE(crr.timestamp) >= ?")
            query_params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(crr.timestamp) <= ?")
            query_params.append(end_date)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # ===== 查詢總記錄數 =====
        count_query = f"""
            SELECT COUNT(*) as total
            FROM clock_raw_records crr
            LEFT JOIN employees e ON crr.employee_code = e.card_number
            {where_clause}
        """
        
        cursor.execute(count_query, query_params)
        total_records = cursor.fetchone()[0]
        
        # ===== 查詢詳細記錄 =====
        records_query = f"""
            SELECT 
                crr.id,
                crr.employee_code,
                crr.machine_code,
                crr.timestamp,
                crr.status_code,
                crr.attendance_id,
                crr.created_at,
                e.name as employee_name,
                e.employee_id as employee_id
            FROM clock_raw_records crr
            LEFT JOIN employees e ON crr.employee_code = e.card_number
            {where_clause}
            ORDER BY crr.timestamp DESC
            LIMIT ? OFFSET ?
        """
        
        cursor.execute(records_query, query_params + [limit, offset])
        
        # 獲取欄位名稱
        columns = [description[0] for description in cursor.description]
        
        # 轉換為字典格式
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            records.append(record)
        
        conn.close()
        
        # ===== 計算總頁數 =====
        total_pages = (total_records + limit - 1) // limit
        
        # ===== 構建響應資料 =====
        response_data = {
            "success": True,
            "records": records,
            "pagination": {
                "total": total_records,
                "page": page,
                "limit": limit,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
        
        logger.info(f"原始打卡記錄查詢成功完成 - 返回 {len(records)} 筆記錄，總共 {total_records} 筆")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"查詢原始打卡記錄時發生錯誤: {e}")
        return jsonify({
            "success": False,
            "error": f"系統錯誤: {str(e)}"
        }), 500


@app.route("/api/attendance/processing/status/<string:processing_id>", methods=["GET"])
def get_processing_status(processing_id):
    """
    獲取特定處理任務的狀態 API
    
    功能說明：
    - 查詢特定處理任務的即時狀態
    - 提供處理進度和結果資訊
    
    返回格式：
    {
        "success": true,
        "status": "completed",
        "progress": 100,
        "message": "處理完成",
        "start_time": "2025-06-02 14:30:00",
        "end_time": "2025-06-02 14:33:00",
        "processed_records": 150,
        "errors": 0
    }
    """
    try:
        logger.info(f"查詢處理任務狀態: {processing_id}")
        
        # ===== 模擬處理狀態查詢 =====
        # 在實際應用中，這裡應該查詢任務狀態資料庫或快取
        
        # 簡化的狀態模擬
        status_data = {
            "status": "completed",  # idle, running, completed, failed
            "progress": 100,
            "message": "處理完成",
            "start_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "processed_records": 150,
            "errors": 0
        }
        
        return jsonify({
            "success": True,
            **status_data
        })
        
    except Exception as e:
        logger.error(f"查詢處理狀態API發生錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500


# ====== 應用程式啟動 ======
if __name__ == "__main__":
    try:
        # 初始化資料庫
        init_db()
        logger.info("資料庫初始化成功")
        
        # 啟動健康監控
        
        # 啟動考勤處理器
        
        logger.info(f"應用程式啟動於 http://{Config.HOST}:{Config.PORT}")
        
    except Exception as e:
        logger.error(f"應用程式啟動失敗: {e}")

    # 啟動應用程式
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG,
        threaded=True,
    )


