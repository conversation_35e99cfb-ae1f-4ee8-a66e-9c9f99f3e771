<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Han AttendanceOS - 遠漢科技考勤系統</title>

    <!-- PWA 支援 -->
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#0ea5e9">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Han考勤">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace'],
                    },
                    colors: {
                        // 現代化調色板 - 受 Apple 和 Linear 啟發
                        brand: {
                            50: '#f0f4ff',
                            100: '#e0e9ff',
                            200: '#c7d6fe',
                            300: '#a5b8fc',
                            400: '#8b93f8',
                            500: '#7c6df2',
                            600: '#6d4de6',
                            700: '#5d3dcb',
                            800: '#4c32a3',
                            900: '#402b82',
                            950: '#261a4f',
                        },
                        gray: {
                            50: '#fafafa',
                            100: '#f5f5f5',
                            200: '#e5e5e5',
                            300: '#d4d4d4',
                            400: '#a3a3a3',
                            500: '#737373',
                            600: '#525252',
                            700: '#404040',
                            800: '#262626',
                            900: '#171717',
                            950: '#0a0a0a',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.4s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'pulse-soft': 'pulseSoft 2s ease-in-out infinite',
                    },
                    backdropBlur: {
                        xs: '2px',
                    },
                    boxShadow: {
                        'soft': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                        'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'large': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                        'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
                        'glow': '0 0 20px rgba(124, 109, 242, 0.3)',
                    }
                }
            }
        }
    </script>

    <style>
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes pulseSoft {
            0%,
            100% {
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
        }
        /* 自定義滾動條 */
        
         ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
         ::-webkit-scrollbar-track {
            background: transparent;
        }
        
         ::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }
        
         ::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.7);
        }
        /* 玻璃效果 */
        
        .glass {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-dark {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        /* 漸層背景 */
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .gradient-mesh {
            background: radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 1) 0px, transparent 50%), radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 1) 0px, transparent 50%), radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 1) 0px, transparent 50%), radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 1) 0px, transparent 50%), radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 1) 0px, transparent 50%), radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 1) 0px, transparent 50%), radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 1) 0px, transparent 50%);
        }
        /* 動畫效果 */
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        /* 數據可視化樣式 */
        
        .metric-card {
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #7c6df2, #8b93f8);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .metric-card:hover::before {
            opacity: 1;
        }
        /* 側邊欄樣式 */
        
        .sidebar-nav {
            position: relative;
        }
        
        .sidebar-nav::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, #7c6df2, #8b93f8);
            border-radius: 0 2px 2px 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .sidebar-nav.active::before {
            opacity: 1;
        }
        /* 響應式設計 */
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.open {
                transform: translateX(0);
            }
        }
    </style>
</head>

<body class="bg-gray-50 font-sans antialiased">
    <!-- 主容器 -->
    <div class="flex h-screen overflow-hidden">
        <!-- 側邊欄 -->
        <aside class="sidebar w-72 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo 區域 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-brand-500 to-brand-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i data-lucide="zap" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">AttendanceOS</h1>
                        <p class="text-xs text-gray-500 font-medium">Enterprise Edition</p>
                    </div>
                </div>
                <button class="md:hidden p-2 rounded-lg hover:bg-gray-100">
                    <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                </button>
            </div>

            <!-- 快速操作 -->
            <div class="p-6 border-b border-gray-100">
                <a href="/elite/attendance" class="w-full bg-gradient-to-r from-brand-500 to-brand-600 text-white rounded-xl py-3 px-4 font-medium hover:from-brand-600 hover:to-brand-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2">
                    <i data-lucide="clock" class="w-4 h-4"></i>
                    <span>快速打卡</span>
                </a>
            </div>

            <!-- 導航選單 - 添加捲動 -->
            <nav class="flex-1 p-6 space-y-2 overflow-y-auto">
                <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-4">主要功能</div>

                <a href="/elite" class="sidebar-nav active flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-brand-600 bg-brand-50 hover:bg-brand-100 transition-all duration-200">
                    <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                    <span>儀表板</span>
                    <div class="ml-auto w-2 h-2 bg-brand-500 rounded-full"></div>
                </a>

                <a href="/elite/online-clock" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="clock" class="w-5 h-5"></i>
                    <span>線上打卡</span>
                </a>

                <a href="/elite/punch-records" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="file-search" class="w-5 h-5"></i>
                    <span>打卡紀錄</span>
                </a>

                <a href="/elite/attendance-management" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="user-check" class="w-5 h-5"></i>
                    <span>考勤作業</span>
                </a>

                <a href="/elite/schedule" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="calendar" class="w-5 h-5"></i>
                    <span>排班管理</span>
                </a>

                <a href="/elite/leaves" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="file-text" class="w-5 h-5"></i>
                    <span>請假申請</span>
                    <span class="ml-auto bg-error-100 text-error-600 text-xs px-2 py-1 rounded-full">3</span>
                </a>

                <a href="/elite/approval" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="file-check" class="w-5 h-5"></i>
                    <span>請假審核管理</span>
                    <span class="ml-auto bg-warning-100 text-warning-600 text-xs px-2 py-1 rounded-full" id="pendingApprovalCount">-</span>
                </a>

                <a href="/elite/overtime" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="clock-4" class="w-5 h-5"></i>
                    <span>加班審核管理</span>
                    <span class="ml-auto bg-warning-100 text-warning-600 text-xs px-2 py-1 rounded-full" id="pendingOvertimeCount">-</span>
                </a>

                <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-4 mt-8">管理工具</div>

                <a href="/elite/attendance-processing" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="calculator" class="w-5 h-5"></i>
                    <span>考勤整理</span>
                    <span class="ml-auto bg-success-100 text-success-600 text-xs px-2 py-1 rounded-full">自動</span>
                </a>

                <a href="/elite/import-attendance" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="upload" class="w-5 h-5"></i>
                    <span>匯入文字檔</span>
                </a>

                <a href="/elite/masterdata" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="database" class="w-5 h-5"></i>
                    <span>基本資料</span>
                </a>

                <a href="/elite/shifts" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="clock" class="w-5 h-5"></i>
                    <span>班別管理</span>
                </a>

                <a href="/elite/employees" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="users" class="w-5 h-5"></i>
                    <span>員工管理</span>
                </a>

                <a href="/elite/analytics" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                    <span>數據分析</span>
                </a>

                <a href="/elite/settings" class="sidebar-nav flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                    <i data-lucide="settings" class="w-5 h-5"></i>
                    <span>系統設定</span>
                </a>
            </nav>

            <!-- 用戶資訊 -->
            <div class="p-6 border-t border-gray-100">
                <div class="flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200 cursor-pointer">
                    <div class="w-10 h-10 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-5 h-5 text-gray-600"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">系統管理員</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <i data-lucide="more-horizontal" class="w-4 h-4 text-gray-400"></i>
                </div>
            </div>
        </aside>

        <!-- 主內容區 -->
        <main class="flex-1 overflow-auto">
            <!-- 頂部導航 -->
            <header class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="md:hidden p-2 rounded-lg hover:bg-gray-100">
                            <i data-lucide="menu" class="w-5 h-5 text-gray-500"></i>
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">儀表板總覽</h1>
                            <p class="text-sm text-gray-500">歡迎回來，今天是 2024年5月28日 星期二</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- 搜尋框 -->
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                            <input type="text" placeholder="搜尋員工、部門..." class="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent outline-none transition-all duration-200 w-64">
                        </div>

                        <!-- 功能總覽 -->
                        <a href="/elite/features" class="p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 flex items-center text-gray-500 hover:text-gray-700">
                            <i data-lucide="info" class="w-5 h-5"></i>
                        </a>

                        <!-- 通知 -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100 transition-all duration-200">
                            <i data-lucide="bell" class="w-5 h-5 text-gray-500"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full"></span>
                        </button>

                        <!-- 設定 -->
                        <button class="p-2 rounded-lg hover:bg-gray-100 transition-all duration-200">
                            <i data-lucide="settings" class="w-5 h-5 text-gray-500"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- 內容區域 -->
            <div class="p-6 space-y-6">
                <!-- 關鍵指標卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 今日出勤 -->
                    <div class="metric-card bg-white rounded-2xl p-6 shadow-soft hover-lift">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                                <i data-lucide="users-check" class="w-6 h-6 text-success-600"></i>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-900">156</p>
                                <p class="text-sm text-gray-500">今日出勤</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-success-500 rounded-full animate-pulse-soft"></div>
                                <span class="text-success-600 text-sm font-medium">+12%</span>
                                <span class="text-gray-500 text-sm">較昨日</span>
                            </div>
                            <div class="text-xs text-gray-400">94.2%</div>
                        </div>
                    </div>

                    <!-- 遲到統計 -->
                    <div class="metric-card bg-white rounded-2xl p-6 shadow-soft hover-lift">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                                <i data-lucide="clock-alert" class="w-6 h-6 text-warning-600"></i>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-900">8</p>
                                <p class="text-sm text-gray-500">遲到人數</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-warning-500 rounded-full"></div>
                                <span class="text-warning-600 text-sm font-medium">+2</span>
                                <span class="text-gray-500 text-sm">較昨日</span>
                            </div>
                            <div class="text-xs text-gray-400">4.8%</div>
                        </div>
                    </div>

                    <!-- 請假申請 -->
                    <div class="metric-card bg-white rounded-2xl p-6 shadow-soft hover-lift">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                                <i data-lucide="file-text" class="w-6 h-6 text-brand-600"></i>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-900">12</p>
                                <p class="text-sm text-gray-500">請假申請</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-error-500 rounded-full"></div>
                                <span class="text-error-600 text-sm font-medium">3 待審批</span>
                            </div>
                            <div class="text-xs text-gray-400">緊急</div>
                        </div>
                    </div>

                    <!-- 部門效率 -->
                    <div class="metric-card bg-white rounded-2xl p-6 shadow-soft hover-lift">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gray-50 rounded-xl flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-6 h-6 text-gray-600"></i>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-900">97.3%</p>
                                <p class="text-sm text-gray-500">整體效率</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-success-500 rounded-full"></div>
                                <span class="text-success-600 text-sm font-medium">+1.2%</span>
                                <span class="text-gray-500 text-sm">較上月</span>
                            </div>
                            <div class="text-xs text-gray-400">優秀</div>
                        </div>
                    </div>
                </div>

                <!-- 圖表區域 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 考勤趨勢圖 -->
                    <div class="lg:col-span-2 bg-white rounded-2xl p-6 shadow-soft">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">考勤趨勢分析</h3>
                                <p class="text-sm text-gray-500">過去30天的出勤統計</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <select class="text-sm border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brand-500 focus:border-transparent outline-none">
                                    <option>最近7天</option>
                                    <option>最近30天</option>
                                    <option>最近3個月</option>
                                </select>
                            </div>
                        </div>
                        <div class="h-80">
                            <canvas id="attendanceChart"></canvas>
                        </div>
                    </div>

                    <!-- 部門出勤率 -->
                    <div class="bg-white rounded-2xl p-6 shadow-soft">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">部門表現</h3>
                                <p class="text-sm text-gray-500">各部門出勤率</p>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-brand-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">技術部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-20 h-2 bg-gray-100 rounded-full overflow-hidden">
                                        <div class="w-4/5 h-full bg-gradient-to-r from-brand-500 to-brand-600 rounded-full"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900 w-10">96%</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-success-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">行銷部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-20 h-2 bg-gray-100 rounded-full overflow-hidden">
                                        <div class="w-full h-full bg-gradient-to-r from-success-500 to-success-600 rounded-full"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900 w-10">98%</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-warning-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">財務部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-20 h-2 bg-gray-100 rounded-full overflow-hidden">
                                        <div class="w-3/4 h-full bg-gradient-to-r from-warning-500 to-warning-600 rounded-full"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900 w-10">92%</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-error-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">人事部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-20 h-2 bg-gray-100 rounded-full overflow-hidden">
                                        <div class="w-5/6 h-full bg-gradient-to-r from-error-500 to-error-600 rounded-full"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900 w-10">89%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 圓形進度圖 -->
                        <div class="mt-6 pt-6 border-t border-gray-100">
                            <div class="flex items-center justify-center">
                                <div class="relative w-24 h-24">
                                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                                        <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="transparent" class="text-gray-200"/>
                                        <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="transparent" stroke-dasharray="251.2" stroke-dashoffset="25.12" class="text-brand-500" stroke-linecap="round"/>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xl font-bold text-gray-900">94%</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-center text-sm text-gray-500 mt-2">整體出勤率</p>
                        </div>
                    </div>
                </div>

                <!-- 活動和快速操作 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 最近活動 -->
                    <div class="bg-white rounded-2xl p-6 shadow-soft">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">最近活動</h3>
                                <p class="text-sm text-gray-500">實時考勤動態</p>
                            </div>
                            <button class="text-sm text-brand-600 hover:text-brand-700 font-medium">查看全部</button>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200">
                                <div class="w-10 h-10 bg-success-50 rounded-full flex items-center justify-center">
                                    <i data-lucide="check" class="w-5 h-5 text-success-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">張小明 正常上班</p>
                                    <p class="text-xs text-gray-500">09:00 · 技術部</p>
                                </div>
                                <span class="text-xs text-gray-400">剛剛</span>
                            </div>

                            <div class="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200">
                                <div class="w-10 h-10 bg-warning-50 rounded-full flex items-center justify-center">
                                    <i data-lucide="clock" class="w-5 h-5 text-warning-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">李小華 遲到15分鐘</p>
                                    <p class="text-xs text-gray-500">09:15 · 行銷部</p>
                                </div>
                                <span class="text-xs text-gray-400">5分鐘前</span>
                            </div>

                            <div class="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200">
                                <div class="w-10 h-10 bg-brand-50 rounded-full flex items-center justify-center">
                                    <i data-lucide="file-text" class="w-5 h-5 text-brand-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">王大明 申請病假</p>
                                    <p class="text-xs text-gray-500">待審批 · 財務部</p>
                                </div>
                                <span class="text-xs text-gray-400">10分鐘前</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="bg-white rounded-2xl p-6 shadow-soft">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">快速操作</h3>
                                <p class="text-sm text-gray-500">常用功能快捷入口</p>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <button class="flex flex-col items-center justify-center p-6 bg-gradient-to-br from-brand-500 to-brand-600 text-white rounded-xl hover:from-brand-600 hover:to-brand-700 transition-all duration-200 shadow-lg hover:shadow-xl group">
                                <i data-lucide="clock-in" class="w-8 h-8 mb-3 group-hover:scale-110 transition-transform duration-200"></i>
                                <span class="text-sm font-medium">上班打卡</span>
                            </button>

                            <button class="flex flex-col items-center justify-center p-6 bg-gradient-to-br from-success-500 to-success-600 text-white rounded-xl hover:from-success-600 hover:to-success-700 transition-all duration-200 shadow-lg hover:shadow-xl group">
                                <i data-lucide="clock-out" class="w-8 h-8 mb-3 group-hover:scale-110 transition-transform duration-200"></i>
                                <span class="text-sm font-medium">下班打卡</span>
                            </button>

                            <button class="flex flex-col items-center justify-center p-6 bg-gradient-to-br from-warning-500 to-warning-600 text-white rounded-xl hover:from-warning-600 hover:to-warning-700 transition-all duration-200 shadow-lg hover:shadow-xl group">
                                <i data-lucide="calendar-plus" class="w-8 h-8 mb-3 group-hover:scale-110 transition-transform duration-200"></i>
                                <span class="text-sm font-medium">申請請假</span>
                            </button>

                            <button class="flex flex-col items-center justify-center p-6 bg-gradient-to-br from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-lg hover:shadow-xl group">
                                <i data-lucide="download" class="w-8 h-8 mb-3 group-hover:scale-110 transition-transform duration-200"></i>
                                <span class="text-sm font-medium">匯出報表</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // 初始化 Lucide 圖標
        lucide.createIcons();

        // 全域變數
        let attendanceChart = null;

        // API 基礎 URL
        const API_BASE = '/api';

        // 初始化考勤趨勢圖表
        function initAttendanceChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');

            // 模擬數據 - 實際應用中應從API獲取
            const chartData = {
                labels: ['週一', '週二', '週三', '週四', '週五', '週六', '週日'],
                datasets: [{
                    label: '出勤人數',
                    data: [156, 152, 148, 160, 155, 45, 32],
                    borderColor: 'rgb(124, 109, 242)',
                    backgroundColor: 'rgba(124, 109, 242, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(124, 109, 242)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }, {
                    label: '遲到人數',
                    data: [8, 12, 6, 15, 9, 3, 2],
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(239, 68, 68)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            };

            const config = {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    family: 'Inter'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: 'rgba(124, 109, 242, 0.3)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            intersect: false,
                            mode: 'index'
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12,
                                    family: 'Inter'
                                },
                                color: '#6b7280'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12,
                                    family: 'Inter'
                                },
                                color: '#6b7280',
                                padding: 10
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: '#fff'
                        }
                    }
                }
            };

            attendanceChart = new Chart(ctx, config);
        }

        // 載入儀表板數據
        async function loadDashboardData() {
            try {
                // 載入最近考勤記錄
                const recentResponse = await fetch(`${API_BASE}/attendance/recent?limit=5`);
                if (recentResponse.ok) {
                    const recentData = await recentResponse.json();
                    updateRecentAttendance(recentData.recent_attendance || []);
                }

                // 載入部門統計
                const deptResponse = await fetch(`${API_BASE}/departments/stats`);
                if (deptResponse.ok) {
                    const deptData = await deptResponse.json();
                    updateDepartmentStats(deptData.departments || []);
                }

                // 載入儀表板報表
                const dashboardResponse = await fetch(`${API_BASE}/reports/dashboard`);
                if (dashboardResponse.ok) {
                    const dashboardData = await dashboardResponse.json();
                    updateDashboardMetrics(dashboardData);
                }

                // 載入考勤趨勢
                const trendsResponse = await fetch(`${API_BASE}/attendance/trends?days=7`);
                if (trendsResponse.ok) {
                    const trendsData = await trendsResponse.json();
                    updateAttendanceChart(trendsData);
                }

                // 載入待審核數量
                const approvalResponse = await fetch(`${API_BASE}/approval/stats`);
                if (approvalResponse.ok) {
                    const approvalData = await approvalResponse.json();
                    console.log('審核統計數據:', approvalData);
                    updatePendingApprovalCount(approvalData.pending_count || 0);
                }

                // 載入加班申請統計
                const overtimeResponse = await fetch(`${API_BASE}/overtime/statistics`);
                if (overtimeResponse.ok) {
                    const overtimeData = await overtimeResponse.json();
                    updatePendingOvertimeCount(overtimeData.pending_count || 0);
                }

            } catch (error) {
                console.error('載入儀表板數據失敗:', error);
            }
        }

        // 更新統計卡片
        function updateStatsCards(data) {
            // 更新總員工數
            const totalEmployeesElement = document.querySelector('.metric-card:nth-child(1) .text-3xl');
            if (totalEmployeesElement) {
                totalEmployeesElement.textContent = data.total_employees || 0;
            }

            // 更新今日出勤
            const checkedInElement = document.querySelector('.metric-card:nth-child(2) .text-3xl');
            if (checkedInElement) {
                checkedInElement.textContent = data.checked_in_today || 0;
            }

            // 更新出勤率
            const attendanceRateElement = document.querySelector('.metric-card:nth-child(3) .text-3xl');
            if (attendanceRateElement) {
                attendanceRateElement.textContent = `${data.attendance_rate || 0}%`;
            }

            // 更新部門數量
            const departmentsElement = document.querySelector('.metric-card:nth-child(4) .text-3xl');
            if (departmentsElement) {
                departmentsElement.textContent = data.total_departments || 0;
            }
        }

        // 載入考勤趨勢數據並更新圖表
        async function updateAttendanceChart(data) {
            try {
                if (attendanceChart) {
                    attendanceChart.destroy();
                }

                const ctx = document.getElementById('attendanceChart').getContext('2d');
                attendanceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || ['週一', '週二', '週三', '週四', '週五', '週六', '週日'],
                        datasets: [{
                            label: '正常出勤',
                            data: data.normal || [145, 152, 148, 156, 142, 89, 45],
                            borderColor: '#7c6df2',
                            backgroundColor: 'rgba(124, 109, 242, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#7c6df2',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                        }, {
                            label: '遲到',
                            data: data.late || [8, 12, 6, 8, 15, 3, 2],
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        family: 'Inter',
                                        size: 12,
                                        weight: '500'
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: 'rgba(255, 255, 255, 0.1)',
                                borderWidth: 1,
                                cornerRadius: 8,
                                displayColors: true,
                                titleFont: {
                                    family: 'Inter',
                                    size: 14,
                                    weight: '600'
                                },
                                bodyFont: {
                                    family: 'Inter',
                                    size: 12,
                                    weight: '400'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false,
                                },
                                ticks: {
                                    font: {
                                        family: 'Inter',
                                        size: 11,
                                        weight: '400'
                                    },
                                    color: '#6b7280'
                                }
                            },
                            x: {
                                grid: {
                                    display: false,
                                },
                                ticks: {
                                    font: {
                                        family: 'Inter',
                                        size: 11,
                                        weight: '400'
                                    },
                                    color: '#6b7280'
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            } catch (error) {
                console.error('載入考勤趨勢失敗:', error);
                // 使用預設數據
                initDefaultChart();
            }
        }

        // 初始化預設圖表（當API失敗時）
        function initDefaultChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            attendanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['週一', '週二', '週三', '週四', '週五', '週六', '週日'],
                    datasets: [{
                        label: '正常出勤',
                        data: [145, 152, 148, 156, 142, 89, 45],
                        borderColor: '#7c6df2',
                        backgroundColor: 'rgba(124, 109, 242, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#7c6df2',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                    }, {
                        label: '遲到',
                        data: [8, 12, 6, 8, 15, 3, 2],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#f59e0b',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    family: 'Inter',
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            titleFont: {
                                family: 'Inter',
                                size: 14,
                                weight: '600'
                            },
                            bodyFont: {
                                family: 'Inter',
                                size: 12,
                                weight: '400'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false,
                            },
                            ticks: {
                                font: {
                                    family: 'Inter',
                                    size: 11,
                                    weight: '400'
                                },
                                color: '#6b7280'
                            }
                        },
                        x: {
                            grid: {
                                display: false,
                            },
                            ticks: {
                                font: {
                                    family: 'Inter',
                                    size: 11,
                                    weight: '400'
                                },
                                color: '#6b7280'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 更新待審核數量
        function updatePendingApprovalCount(count) {
            const countElement = document.getElementById('pendingApprovalCount');
            if (countElement) {
                countElement.textContent = count > 0 ? count : '0';
                if (count > 0) {
                    countElement.classList.remove('bg-gray-100', 'text-gray-600');
                    countElement.classList.add('bg-warning-100', 'text-warning-600');
                } else {
                    countElement.classList.remove('bg-warning-100', 'text-warning-600');
                    countElement.classList.add('bg-gray-100', 'text-gray-600');
                }
            }
        }

        // 更新待審核加班申請數量
        function updatePendingOvertimeCount(count) {
            const countElement = document.getElementById('pendingOvertimeCount');
            if (countElement) {
                countElement.textContent = count > 0 ? count : '0';
                if (count > 0) {
                    countElement.classList.remove('bg-gray-100', 'text-gray-600');
                    countElement.classList.add('bg-warning-100', 'text-warning-600');
                } else {
                    countElement.classList.remove('bg-warning-100', 'text-warning-600');
                    countElement.classList.add('bg-gray-100', 'text-gray-600');
                }
            }
        }

        // 更新最近考勤記錄
        function updateRecentAttendance(records) {
            // 這裡可以更新最近考勤記錄的顯示
            console.log('最近考勤記錄:', records);
        }

        // 更新部門統計
        function updateDepartmentStats(departments) {
            // 這裡可以更新部門統計的顯示
            console.log('部門統計:', departments);
        }

        // 更新儀表板指標
        function updateDashboardMetrics(data) {
            // 這裡可以更新儀表板指標的顯示
            console.log('儀表板指標:', data);
        }

        // 顯示載入狀態
        function showLoading() {
            const loadingElement = document.createElement('div');
            loadingElement.id = 'loading-overlay';
            loadingElement.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            loadingElement.innerHTML = `
                <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-600"></div>
                    <span class="text-gray-700">載入中...</span>
                </div>
            `;
            document.body.appendChild(loadingElement);
        }

        // 隱藏載入狀態
        function hideLoading() {
            const loadingElement = document.getElementById('loading-overlay');
            if (loadingElement) {
                loadingElement.remove();
            }
        }

        // 顯示錯誤訊息
        function showError(message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            errorElement.textContent = message;
            document.body.appendChild(errorElement);

            setTimeout(() => {
                errorElement.remove();
            }, 5000);
        }

        // 快速操作按鈕事件
        function initQuickActions() {
            const quickActionButtons = document.querySelectorAll('.grid.grid-cols-2 button');

            quickActionButtons.forEach((button, index) => {
                button.addEventListener('click', async() => {
                    const action = button.querySelector('span').textContent;

                    switch (action) {
                        case '上班打卡':
                            await handleClockIn();
                            break;
                        case '下班打卡':
                            await handleClockOut();
                            break;
                        case '申請請假':
                            window.location.href = '/elite/leaves';
                            break;
                        case '匯出報表':
                            await handleExportReport();
                            break;
                    }
                });
            });
        }

        // 處理上班打卡
        async function handleClockIn() {
            try {
                showLoading();
                const response = await fetch(`${API_BASE}/attendance/clock-in`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        employee_id: 1 // 這裡應該從登入狀態獲取
                    })
                });

                const result = await response.json();
                hideLoading();

                if (response.ok) {
                    showSuccess('上班打卡成功！');
                    // 重新載入數據
                    loadDashboardData();
                } else {
                    showError(result.error || '打卡失敗');
                }
            } catch (error) {
                hideLoading();
                showError('打卡失敗，請稍後再試');
            }
        }

        // 處理下班打卡
        async function handleClockOut() {
            try {
                showLoading();
                const response = await fetch(`${API_BASE}/attendance/clock-out`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        employee_id: 1 // 這裡應該從登入狀態獲取
                    })
                });

                const result = await response.json();
                hideLoading();

                if (response.ok) {
                    showSuccess('下班打卡成功！');
                    // 重新載入數據
                    loadDashboardData();
                } else {
                    showError(result.error || '打卡失敗');
                }
            } catch (error) {
                hideLoading();
                showError('打卡失敗，請稍後再試');
            }
        }

        // 處理匯出報表
        async function handleExportReport() {
            try {
                const response = await fetch(`${API_BASE}/reports/export?format=csv`);
                const blob = await response.blob();

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `attendance_report_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showSuccess('報表匯出成功！');
            } catch (error) {
                showError('匯出失敗，請稍後再試');
            }
        }

        // 顯示成功訊息
        function showSuccess(message) {
            const successElement = document.createElement('div');
            successElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            successElement.textContent = message;
            document.body.appendChild(successElement);

            setTimeout(() => {
                successElement.remove();
            }, 3000);
        }

        // 響應式側邊欄
        const sidebarToggle = document.querySelector('[data-lucide="menu"]');
        const sidebar = document.querySelector('.sidebar');
        const sidebarClose = document.querySelector('[data-lucide="x"]');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.add('open');
            });
        }

        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => {
                sidebar.classList.remove('open');
            });
        }

        // 頁面載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.metric-card, .bg-white');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate-fade-in');
                }, index * 100);
            });

            // 初始化圖表
            initAttendanceChart();

            // 初始化快速操作
            initQuickActions();

            // 載入儀表板數據
            loadDashboardData();
        });

        // 實時時間更新
        function updateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const timeString = now.toLocaleDateString('zh-TW', options);
            const timeElement = document.querySelector('header p');
            if (timeElement) {
                timeElement.textContent = `歡迎回來，今天是 ${timeString}`;
            }
        }

        updateTime();
        setInterval(updateTime, 60000); // 每分鐘更新一次

        // 定期重新載入數據（每5分鐘）
        setInterval(loadDashboardData, 5 * 60 * 1000);
    </script>
</body>

</html>