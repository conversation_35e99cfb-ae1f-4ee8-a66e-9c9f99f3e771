'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
    ArrowLeft,
    Search,
    Filter,
    Eye,
    MessageSquare,
    Calendar,
    Clock,
    User,
    FileText,
    CheckCircle,
    XCircle,
    AlertCircle,
    Camera,
    Reply,
    BarChart3,
    RefreshCw,
    ZoomIn,
    X
} from 'lucide-react'

interface WorkReport {
    id: number
    employee_id: string
    employee_name: string
    report_date: string
    report_time: string
    category: string
    content: string
    photos: string[]
    is_read: number
    supervisor_feedback: string | null
    feedback_date: string | null
    feedback_by: string | null
    created_at: string
}

interface WorkReportStats {
    total_reports: number
    unread_reports: number
    replied_reports: number
    today_reports: number
    category_stats: Array<{ category: string, count: number }>
}

export default function WorkReportsPage() {
    const [reports, setReports] = useState<WorkReport[]>([])
    const [stats, setStats] = useState<WorkReportStats | null>(null)
    const [loading, setLoading] = useState(true)
    const [selectedReport, setSelectedReport] = useState<WorkReport | null>(null)
    const [showDetailModal, setShowDetailModal] = useState(false)
    const [showFeedbackModal, setShowFeedbackModal] = useState(false)
    const [feedbackText, setFeedbackText] = useState('')

    // 🖼️ 照片查看相關狀態
    const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null)
    const [showPhotoModal, setShowPhotoModal] = useState(false)

    // 篩選條件
    const [filters, setFilters] = useState({
        category: '',
        employee_id: '',
        is_read: '',
        start_date: '',
        end_date: ''
    })

    // 分頁
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 20,
        total: 0,
        pages: 0
    })

    // 🖼️ 獲取照片完整URL
    const getPhotoUrl = (photoPath: string) => {
        // 如果是 base64 格式，直接返回
        if (photoPath.startsWith('data:image/')) {
            return photoPath
        }
        // 如果是完整URL，直接返回
        if (photoPath.startsWith('http')) {
            return photoPath
        }
        // 如果是相對路徑，添加動態API基礎URL
        const apiBaseUrl = getApiBaseUrl()
        return `${apiBaseUrl}${photoPath}`
    }

    // 🖼️ 打開照片查看模態框
    const openPhotoModal = (photoPath: string) => {
        setSelectedPhoto(photoPath)
        setShowPhotoModal(true)
    }

    // 🖼️ 關閉照片查看模態框
    const closePhotoModal = () => {
        setSelectedPhoto(null)
        setShowPhotoModal(false)
    }

    // 🌐 獲取API基礎URL
    const getApiBaseUrl = () => {
        if (typeof window !== 'undefined') {
            const hostname = window.location.hostname
            console.log('🌐 工作回報頁面檢測到的主機:', hostname)

            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:7072'
            } else {
                const apiUrl = `http://${hostname}:7072`
                console.log('📱 工作回報頁面使用API URL:', apiUrl)
                return apiUrl
            }
        }
        return 'http://localhost:7072'
    }

    // 載入工作回報列表
    const loadWorkReports = async () => {
        try {
            setLoading(true)
            const params = new URLSearchParams({
                page: pagination.page.toString(),
                limit: pagination.limit.toString(),
                ...filters
            })

            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports/admin?${params}`
            console.log('📋 載入工作回報 URL:', url)

            const response = await fetch(url)
            const result = await response.json()

            console.log('📋 工作回報API響應:', { success: result.success, dataCount: result.data?.length })

            if (result.success) {
                setReports(result.data)
                setPagination(prev => ({
                    ...prev,
                    total: result.pagination.total,
                    pages: result.pagination.pages
                }))
            }
        } catch (error) {
            console.error('載入工作回報失敗:', error)
        } finally {
            setLoading(false)
        }
    }

    // 載入統計數據
    const loadStats = async () => {
        try {
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports/stats`
            console.log('📊 載入統計數據 URL:', url)

            const response = await fetch(url)
            const result = await response.json()

            console.log('📊 統計數據API響應:', { success: result.success, stats: result.stats })

            if (result.success) {
                setStats(result.stats)
            }
        } catch (error) {
            console.error('載入統計失敗:', error)
        }
    }

    // 標記為已讀
    const markAsRead = async (reportId: number) => {
        try {
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports/${reportId}/read`
            console.log('✅ 標記已讀 URL:', url)

            const response = await fetch(url, {
                method: 'POST'
            })
            const result = await response.json()

            if (result.success) {
                loadWorkReports()
                loadStats()
            }
        } catch (error) {
            console.error('標記已讀失敗:', error)
        }
    }

    // 提交回覆
    const submitFeedback = async () => {
        if (!selectedReport || !feedbackText.trim()) return

        try {
            const apiBaseUrl = getApiBaseUrl()
            const url = `${apiBaseUrl}/api/work-reports/${selectedReport.id}/feedback`
            console.log('💬 提交回覆 URL:', url)

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    feedback: feedbackText,
                    feedback_by: 'admin' // 這裡應該從登入狀態獲取
                })
            })
            const result = await response.json()

            if (result.success) {
                alert('回覆成功！')
                setShowFeedbackModal(false)
                setFeedbackText('')
                loadWorkReports()
                loadStats()
            } else {
                alert('回覆失敗：' + result.message)
            }
        } catch (error) {
            console.error('提交回覆失敗:', error)
            alert('回覆失敗，請檢查網路連線')
        }
    }

    // 獲取類別顯示名稱
    const getCategoryName = (category: string) => {
        const categoryMap: Record<string, string> = {
            'work_report': '工作回報',
            'feedback': '反饋',
            'suggestion': '建議',
            'issue': '問題回報',
            'progress': '進度更新'
        }
        return categoryMap[category] || category
    }

    // 獲取類別顏色
    const getCategoryColor = (category: string) => {
        const colorMap: Record<string, string> = {
            'work_report': 'bg-blue-100 text-blue-800 border-blue-200',
            'feedback': 'bg-green-100 text-green-800 border-green-200',
            'suggestion': 'bg-purple-100 text-purple-800 border-purple-200',
            'issue': 'bg-red-100 text-red-800 border-red-200',
            'progress': 'bg-orange-100 text-orange-800 border-orange-200'
        }
        return colorMap[category] || 'bg-gray-100 text-gray-800 border-gray-200'
    }

    useEffect(() => {
        loadWorkReports()
        loadStats()
    }, [pagination.page, filters])

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
            <div className="pt-6">
                {/* 🎨 頁面標題 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">工作回報管理</h1>
                            <div className="flex items-center space-x-2">
                                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                    <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                    <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                                </Link>
                                <p className="text-indigo-100 text-base font-medium">管理員工工作回報、查看內容並提供回覆</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">工作回報管理</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <FileText className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* 📊 統計卡片 */}
                {stats && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <FileText className="w-6 h-6 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold text-gray-900">{stats.total_reports}</p>
                                    <p className="text-sm text-gray-600">總回報數</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                                    <AlertCircle className="w-6 h-6 text-amber-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold text-gray-900">{stats.unread_reports}</p>
                                    <p className="text-sm text-gray-600">未讀回報</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                                    <Reply className="w-6 h-6 text-green-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold text-gray-900">{stats.replied_reports}</p>
                                    <p className="text-sm text-gray-600">已回覆</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                                    <Calendar className="w-6 h-6 text-purple-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold text-gray-900">{stats.today_reports}</p>
                                    <p className="text-sm text-gray-600">今日回報</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* 🔍 篩選工具欄 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-6">
                    <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 mb-4">
                        <div className="space-y-2">
                            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                <Filter className="w-4 h-4 text-blue-600" />
                                <span>類別</span>
                            </label>
                            <select
                                value={filters.category}
                                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            >
                                <option value="">全部類別</option>
                                <option value="work_report">工作回報</option>
                                <option value="feedback">反饋</option>
                                <option value="suggestion">建議</option>
                                <option value="issue">問題回報</option>
                                <option value="progress">進度更新</option>
                            </select>
                        </div>

                        <div className="space-y-2">
                            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                <User className="w-4 h-4 text-blue-600" />
                                <span>員工ID</span>
                            </label>
                            <input
                                type="text"
                                value={filters.employee_id}
                                onChange={(e) => setFilters(prev => ({ ...prev, employee_id: e.target.value }))}
                                placeholder="輸入員工ID"
                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            />
                        </div>

                        <div className="space-y-2">
                            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                <CheckCircle className="w-4 h-4 text-blue-600" />
                                <span>讀取狀態</span>
                            </label>
                            <select
                                value={filters.is_read}
                                onChange={(e) => setFilters(prev => ({ ...prev, is_read: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            >
                                <option value="">全部狀態</option>
                                <option value="0">未讀</option>
                                <option value="1">已讀</option>
                            </select>
                        </div>

                        <div className="space-y-2">
                            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                <Calendar className="w-4 h-4 text-blue-600" />
                                <span>開始日期</span>
                            </label>
                            <input
                                type="date"
                                value={filters.start_date}
                                onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            />
                        </div>

                        <div className="space-y-2">
                            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                                <Calendar className="w-4 h-4 text-blue-600" />
                                <span>結束日期</span>
                            </label>
                            <input
                                type="date"
                                value={filters.end_date}
                                onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
                                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            />
                        </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div className="flex items-center space-x-3">
                            <button
                                onClick={() => {
                                    setPagination(prev => ({ ...prev, page: 1 }))
                                    loadWorkReports()
                                }}
                                className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                                <Search className="w-4 h-4" />
                                <span>搜尋</span>
                            </button>

                            <button
                                onClick={() => {
                                    setFilters({
                                        category: '',
                                        employee_id: '',
                                        is_read: '',
                                        start_date: '',
                                        end_date: ''
                                    })
                                    setPagination(prev => ({ ...prev, page: 1 }))
                                }}
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
                            >
                                <XCircle className="w-4 h-4" />
                                <span>重置</span>
                            </button>
                        </div>

                        <button
                            onClick={() => {
                                loadWorkReports()
                                loadStats()
                            }}
                            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <RefreshCw className="w-4 h-4" />
                            <span>重新整理</span>
                        </button>
                    </div>
                </div>

                {/* 📋 工作回報列表 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                    <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                        <h2 className="text-xl font-bold text-gray-900">工作回報列表</h2>
                        <p className="text-sm text-gray-600">共 {pagination.total} 筆記錄</p>
                    </div>

                    {loading ? (
                        <div className="flex items-center justify-center py-16">
                            <div className="text-center">
                                <div className="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                                <p className="text-gray-600">載入中...</p>
                            </div>
                        </div>
                    ) : reports.length === 0 ? (
                        <div className="text-center py-16">
                            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">暫無工作回報</h3>
                            <p className="text-gray-500">目前沒有符合條件的工作回報記錄</p>
                        </div>
                    ) : (
                        <div className="divide-y divide-gray-100">
                            {reports.map((report) => (
                                <div key={report.id} className="p-6 hover:bg-gray-50 transition-colors">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-3 mb-3">
                                                <div className="flex items-center space-x-2">
                                                    <User className="w-4 h-4 text-gray-500" />
                                                    <span className="font-semibold text-gray-900">{report.employee_name}</span>
                                                    <span className="text-sm text-gray-500">({report.employee_id})</span>
                                                </div>

                                                <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getCategoryColor(report.category)}`}>
                                                    {getCategoryName(report.category)}
                                                </span>

                                                {report.is_read === 0 && (
                                                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-bold rounded-full animate-pulse">
                                                        未讀
                                                    </span>
                                                )}
                                            </div>

                                            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                                <div className="flex items-center space-x-1">
                                                    <Calendar className="w-4 h-4" />
                                                    <span>{report.report_date}</span>
                                                </div>
                                                <div className="flex items-center space-x-1">
                                                    <Clock className="w-4 h-4" />
                                                    <span>{report.report_time}</span>
                                                </div>
                                                {report.photos && report.photos.length > 0 && (
                                                    <div className="flex items-center space-x-1">
                                                        <Camera className="w-4 h-4" />
                                                        <span>{report.photos.length} 張照片</span>
                                                    </div>
                                                )}
                                            </div>

                                            <p className="text-gray-700 mb-3 line-clamp-2">{report.content}</p>

                                            {/* 🖼️ 照片預覽 */}
                                            {report.photos && report.photos.length > 0 && (
                                                <div className="mb-3">
                                                    <div className="flex items-center space-x-2 mb-2">
                                                        <Camera className="w-4 h-4 text-blue-600" />
                                                        <span className="text-sm font-medium text-blue-600">附件照片</span>
                                                    </div>
                                                    <div className="flex space-x-2 overflow-x-auto">
                                                        {report.photos.slice(0, 3).map((photo, index) => (
                                                            <div
                                                                key={index}
                                                                className="relative flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all"
                                                                onClick={() => openPhotoModal(photo)}
                                                            >
                                                                <img
                                                                    src={getPhotoUrl(photo)}
                                                                    alt={`照片 ${index + 1}`}
                                                                    className="w-full h-full object-cover"
                                                                    onError={(e) => {
                                                                        // 如果圖片載入失敗，顯示預設圖標
                                                                        const target = e.target as HTMLImageElement;
                                                                        target.style.display = 'none';
                                                                        const parent = target.parentElement;
                                                                        if (parent) {
                                                                            parent.innerHTML = `
                                                                                <div class="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center">
                                                                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                    </svg>
                                                                                </div>
                                                                            `;
                                                                        }
                                                                    }}
                                                                />
                                                                <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                                                                    <ZoomIn className="w-4 h-4 text-white" />
                                                                </div>
                                                            </div>
                                                        ))}
                                                        {report.photos.length > 3 && (
                                                            <div className="flex-shrink-0 w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center text-xs text-gray-600 font-medium">
                                                                +{report.photos.length - 3}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                            {report.supervisor_feedback && (
                                                <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r-lg">
                                                    <div className="flex items-center space-x-2 mb-1">
                                                        <Reply className="w-4 h-4 text-blue-600" />
                                                        <span className="text-sm font-semibold text-blue-800">長官回覆</span>
                                                        <span className="text-xs text-blue-600">
                                                            {new Date(report.feedback_date!).toLocaleString()}
                                                        </span>
                                                    </div>
                                                    <p className="text-blue-700 text-sm">{report.supervisor_feedback}</p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex items-center space-x-2 ml-4">
                                            <button
                                                onClick={() => {
                                                    setSelectedReport(report)
                                                    setShowDetailModal(true)
                                                    if (report.is_read === 0) {
                                                        markAsRead(report.id)
                                                    }
                                                }}
                                                className="p-2 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-lg transition-colors"
                                                title="查看詳情"
                                            >
                                                <Eye className="w-4 h-4" />
                                            </button>

                                            <button
                                                onClick={() => {
                                                    setSelectedReport(report)
                                                    setShowFeedbackModal(true)
                                                }}
                                                className="p-2 bg-green-100 hover:bg-green-200 text-green-600 rounded-lg transition-colors"
                                                title="回覆"
                                            >
                                                <MessageSquare className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* 分頁 */}
                    {pagination.pages > 1 && (
                        <div className="p-4 bg-gray-50 border-t border-gray-200">
                            <div className="flex items-center justify-between">
                                <p className="text-sm text-gray-600">
                                    第 {pagination.page} 頁，共 {pagination.pages} 頁
                                </p>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                                        disabled={pagination.page === 1}
                                        className="px-3 py-1 bg-white border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                    >
                                        上一頁
                                    </button>
                                    <button
                                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                                        disabled={pagination.page === pagination.pages}
                                        className="px-3 py-1 bg-white border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                    >
                                        下一頁
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 📝 詳情模態框 */}
            {showDetailModal && selectedReport && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-3xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl">
                        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-xl font-bold">工作回報詳情</h2>
                                    <p className="text-blue-100 text-sm">#{selectedReport.id}</p>
                                </div>
                                <button
                                    onClick={() => setShowDetailModal(false)}
                                    className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors"
                                >
                                    <XCircle className="w-5 h-5 text-white" />
                                </button>
                            </div>
                        </div>

                        <div className="p-6 max-h-[60vh] overflow-y-auto">
                            <div className="space-y-6">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700">員工姓名</label>
                                        <p className="text-gray-900">{selectedReport.employee_name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700">員工ID</label>
                                        <p className="text-gray-900">{selectedReport.employee_id}</p>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700">回報日期</label>
                                        <p className="text-gray-900">{selectedReport.report_date}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700">回報時間</label>
                                        <p className="text-gray-900">{selectedReport.report_time}</p>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-semibold text-gray-700">類別</label>
                                    <p className="text-gray-900">{getCategoryName(selectedReport.category)}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-semibold text-gray-700">工作內容</label>
                                    <div className="bg-gray-50 rounded-lg p-4 mt-2">
                                        <p className="text-gray-900 whitespace-pre-wrap">{selectedReport.content}</p>
                                    </div>
                                </div>

                                {/* 🖼️ 照片展示區域 */}
                                {selectedReport.photos && selectedReport.photos.length > 0 && (
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700 mb-3 block">
                                            附件照片 ({selectedReport.photos.length} 張)
                                        </label>
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                            {selectedReport.photos.map((photo, index) => (
                                                <div
                                                    key={index}
                                                    className="relative aspect-square bg-gray-100 rounded-xl overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all group"
                                                    onClick={() => openPhotoModal(photo)}
                                                >
                                                    <img
                                                        src={getPhotoUrl(photo)}
                                                        alt={`照片 ${index + 1}`}
                                                        className="w-full h-full object-cover"
                                                        onError={(e) => {
                                                            // 如果圖片載入失敗，顯示預設圖標
                                                            const target = e.target as HTMLImageElement;
                                                            target.style.display = 'none';
                                                            const parent = target.parentElement;
                                                            if (parent) {
                                                                parent.innerHTML = `
                                                                    <div class="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center">
                                                                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                        </svg>
                                                                    </div>
                                                                    <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                                                        </svg>
                                                                    </div>
                                                                    <div class="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                                                                        照片 ${index + 1}
                                                                    </div>
                                                                `;
                                                            }
                                                        }}
                                                    />
                                                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                        <ZoomIn className="w-6 h-6 text-white" />
                                                    </div>
                                                    <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                                                        照片 {index + 1}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {selectedReport.supervisor_feedback && (
                                    <div>
                                        <label className="text-sm font-semibold text-gray-700">長官回覆</label>
                                        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mt-2 rounded-r-lg">
                                            <p className="text-blue-900 whitespace-pre-wrap">{selectedReport.supervisor_feedback}</p>
                                            <p className="text-xs text-blue-600 mt-2">
                                                回覆時間：{new Date(selectedReport.feedback_date!).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 🖼️ 照片查看模態框 */}
            {showPhotoModal && selectedPhoto && (
                <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
                    <div className="relative max-w-4xl max-h-[90vh] w-full">
                        <button
                            onClick={closePhotoModal}
                            className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors"
                        >
                            <X className="w-6 h-6" />
                        </button>

                        <div className="bg-white rounded-2xl overflow-hidden shadow-2xl">
                            <div className="bg-gradient-to-r from-gray-800 to-gray-900 p-4 text-white">
                                <h3 className="text-lg font-semibold">照片查看</h3>
                                <p className="text-gray-300 text-sm">點擊右上角關閉</p>
                            </div>

                            <div className="p-6 bg-gray-50 flex items-center justify-center min-h-[400px]">
                                <div className="w-full max-w-3xl">
                                    <img
                                        src={getPhotoUrl(selectedPhoto)}
                                        alt="工作回報照片"
                                        className="w-full h-auto max-h-[70vh] object-contain rounded-lg shadow-lg"
                                        onError={(e) => {
                                            // 如果圖片載入失敗，顯示錯誤訊息
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                            const parent = target.parentElement;
                                            if (parent) {
                                                parent.innerHTML = `
                                                    <div class="text-center py-16">
                                                        <div class="w-32 h-32 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                                            <svg class="w-16 h-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                        </div>
                                                        <h4 class="text-lg font-semibold text-gray-900 mb-2">照片載入失敗</h4>
                                                        <p class="text-gray-600 mb-4">照片路徑：${selectedPhoto}</p>
                                                        <p class="text-sm text-gray-500">請檢查照片文件是否存在或網路連線是否正常</p>
                                                    </div>
                                                `;
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 💬 回覆模態框 */}
            {showFeedbackModal && selectedReport && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-3xl w-full max-w-lg overflow-hidden shadow-2xl">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6 text-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-xl font-bold">回覆工作回報</h2>
                                    <p className="text-green-100 text-sm">{selectedReport.employee_name} 的回報</p>
                                </div>
                                <button
                                    onClick={() => {
                                        setShowFeedbackModal(false)
                                        setFeedbackText('')
                                    }}
                                    className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors"
                                >
                                    <XCircle className="w-5 h-5 text-white" />
                                </button>
                            </div>
                        </div>

                        <div className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-semibold text-gray-700 mb-2 block">原始內容</label>
                                    <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700 max-h-32 overflow-y-auto">
                                        {selectedReport.content}
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-semibold text-gray-700 mb-2 block">
                                        您的回覆 <span className="text-red-500">*</span>
                                    </label>
                                    <textarea
                                        value={feedbackText}
                                        onChange={(e) => setFeedbackText(e.target.value)}
                                        placeholder="請輸入您的回覆..."
                                        rows={4}
                                        className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none"
                                    />
                                </div>
                            </div>

                            <div className="flex space-x-3 mt-6">
                                <button
                                    onClick={() => {
                                        setShowFeedbackModal(false)
                                        setFeedbackText('')
                                    }}
                                    className="flex-1 px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold rounded-xl transition-colors"
                                >
                                    取消
                                </button>
                                <button
                                    onClick={submitFeedback}
                                    disabled={!feedbackText.trim()}
                                    className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-semibold rounded-xl transition-all transform hover:scale-105 active:scale-95 disabled:transform-none disabled:cursor-not-allowed"
                                >
                                    提交回覆
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
} 