# 📋 Han AttendanceOS 考勤整理流程規則

## 🔄 核心概念

考勤系統分為兩個層次：
1. **打卡記錄** (`clock_raw_records`) - 原始打卡數據
2. **考勤記錄** (`attendance`) - 經過處理的考勤數據

## 📊 考勤整理流程

### 第一階段：數據收集與清理
```
打卡機原始數據 → clock_raw_records → 數據清理 → attendance 記錄
```

**處理規則：**
- 去除重複打卡記錄
- 配對上下班時間（第一筆為上班，最後一筆為下班）
- 處理異常數據（如只有上班或下班）

### 第二階段：班表匹配與計算
```
attendance + schedules + shifts → 考勤指標計算
```

**計算項目：**
1. **工作時數** = (下班時間 - 上班時間) - 請假時數
2. **遲到時間** = max(0, 實際上班時間 - 班表上班時間 - 容許時間)
3. **早退時間** = max(0, 班表下班時間 - 實際下班時間 - 容許時間)
4. **加班時間** = max(0, 實際下班時間 - 班表下班時間)

## ⏰ 跨日考勤規則

### 當天起算時間機制 (day_start_time)

每個班表都有一個「當天起算時間」欄位，用於定義該班別「當天」考勤的時間範圍：

**基本原則：**
- 從當天起算時間開始，到隔天起算時間前，都算是「當天」的考勤時間
- 例如：起算時間 06:30，則當天考勤時間為 06:30 ~ 隔天 06:29

**考勤抓取邏輯：**
```
當天考勤時間範圍 = [當天 day_start_time, 隔天 day_start_time - 1分鐘]
```

### 實際班表設定

| 班別 | 上班時間 | 下班時間 | 起算時間 | 考勤時間範圍 |
|------|---------|---------|---------|-------------|
| 標準日班 | 08:30 | 17:30 | 06:30 | 06:30 ~ 隔天 06:29 |
| 早班 | 06:00 | 14:00 | 04:00 | 04:00 ~ 隔天 03:59 |
| 晚班 | 14:00 | 22:00 | 12:00 | 12:00 ~ 隔天 11:59 |
| 夜班 | 22:00 | 06:00 | 20:00 | 20:00 ~ 隔天 19:59 |
| 彈性班 | 09:00 | 18:00 | 07:00 | 07:00 ~ 隔天 06:59 |
| 半日班 | 09:00 | 13:00 | 07:00 | 07:00 ~ 隔天 06:59 |

### 預設值計算規則

**自動計算公式：**
```
day_start_time = start_time - 2小時
```

**範例：**
- 上班時間 08:30 → 起算時間 06:30
- 上班時間 22:00 → 起算時間 20:00
- 上班時間 06:00 → 起算時間 04:00

### 跨日情境範例

**情境：夜班員工 (22:00-06:00)，起算時間設定為 22:00**

工作日：2025-06-07

```
打卡時間歸屬：
- 2025-06-07 21:50 → 歸屬於 2025-06-06 (前一天下班)
- 2025-06-07 22:00 → 歸屬於 2025-06-07 (當天上班)
- 2025-06-08 02:00 → 歸屬於 2025-06-07 (當天工作中)
- 2025-06-08 06:00 → 歸屬於 2025-06-07 (當天下班)
- 2025-06-08 06:30 → 歸屬於 2025-06-07 (當天加班)
```

## 🔧 班表換班功能

### 換班邏輯
當員工的實際上班時間與預設排班不同時，可以更換班別：

1. **選擇新班表**：從可用班表中選擇適合的班別
2. **重新計算**：根據新班表的時間和容許設定重新計算考勤指標
3. **跨日重新判斷**：使用新班表的day_start_time重新判斷打卡歸屬

### 重新計算項目
- 遲到時間（基於新班表的上班時間和容許時間）
- 早退時間（基於新班表的下班時間和容許時間）
- 加班時間（基於新班表的下班時間）
- 工作時數（重新計算總工時）
- 考勤狀態（normal, late, early_leave, absent, leave）

## 📋 考勤狀態判定

### 狀態優先級（由高到低）
1. **absent** - 無打卡記錄且無請假
2. **leave** - 有請假記錄（全天或部分）
3. **late** - 有遲到記錄
4. **early_leave** - 有早退記錄
5. **normal** - 正常出勤

### 判定邏輯
```python
if not check_in and not check_out:
    if leave_hours >= 8:
        status = 'leave'
    else:
        status = 'absent'
elif late_minutes > 0:
    status = 'late'
elif early_leave_minutes > 0:
    status = 'early_leave'
elif leave_hours > 0:
    status = 'leave'
else:
    status = 'normal'
```

## 🎯 容許時間機制

### 容許時間設定
每個班表可設定：
- **遲到容許時間** (late_tolerance_minutes)：預設5分鐘
- **早退容許時間** (early_leave_tolerance_minutes)：預設5分鐘

### 計算邏輯
```python
# 遲到計算
actual_late_minutes = (實際上班時間 - 班表上班時間).minutes
late_minutes = max(0, actual_late_minutes - late_tolerance_minutes)

# 早退計算
actual_early_minutes = (班表下班時間 - 實際下班時間).minutes
early_leave_minutes = max(0, actual_early_minutes - early_leave_tolerance_minutes)
```

## 🔄 系統一致性保證

### 重新計算函數統一化
所有涉及考勤計算的功能都使用相同的邏輯：

1. **班表更新功能** (`update_attendance_shift`)
2. **考勤編輯功能** (`update_attendance_record`)
3. **批次考勤生成** (`generate_attendance_records`)

### 統一計算邏輯
- 統一使用 `attendance.shift_id` 直接關聯 `shifts` 表
- 統一處理容許時間邏輯
- 統一支援多種時間格式 (%H:%M 和 %H:%M:%S)
- 統一使用day_start_time進行跨日判斷

## 📝 資料庫結構

### 核心表格關係
```
employees (員工) 
    ↓
schedules (排班) → shifts (班表)
    ↓
attendance (考勤) → shifts (班表)
    ↓
leaves (請假)
```

### 關鍵欄位
- `shifts.day_start_time` - 當天起算時間
- `shifts.late_tolerance_minutes` - 遲到容許時間
- `shifts.early_leave_tolerance_minutes` - 早退容許時間
- `attendance.shift_id` - 直接關聯班表ID

## 🚀 未來擴展

### 待實現功能
1. **完整跨日邏輯實現**
   - 根據day_start_time自動調整打卡歸屬
   - 支援複雜的跨日班表計算

2. **智能班表推薦**
   - 根據打卡時間自動推薦合適班表
   - 機器學習優化班表匹配

3. **異常檢測**
   - 自動檢測異常打卡模式
   - 提供修正建議

## 📊 版本記錄

- **v2005.6.12** - 新增day_start_time欄位和跨日邏輯框架
- **v2005.6.11** - 修復考勤計算一致性問題
- **v2005.6.10** - 實現班表換班功能

---

*本文檔將隨系統更新持續維護，確保考勤整理規則的準確性和一致性。* 