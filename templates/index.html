<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="智慧考勤系統 - 現代化企業考勤管理解決方案">
    <meta name="keywords" content="考勤系統,打卡,排班,人事管理,智慧考勤">
    <meta name="author" content="智慧考勤系統團隊">
    
    <title>智慧考勤系統 | Smart Attendance System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='icons/favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='icons/apple-touch-icon.png') }}">
    
    <!-- 預載字體 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- CSS 樣式表 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/design-system.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main-pinterest.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/employee-management.css') }}">
    
    <!-- PWA 支援 -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <meta name="theme-color" content="#0ea5e9">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="智慧考勤系統">
    <meta property="og:description" content="現代化企業考勤管理解決方案">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{{ url_for('static', filename='images/og-image.png', _external=True) }}">
    
    <!-- 結構化數據 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "智慧考勤系統",
        "description": "現代化企業考勤管理解決方案",
        "url": "{{ request.url }}",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript. Requires HTML5."
    }
    </script>
</head>
<body>
    <!-- 載入畫面 -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-icon">🏢</div>
                <h1 class="logo-text gradient-text">智慧考勤系統</h1>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <p class="loading-text">系統載入中，請稍候...</p>
        </div>
    </div>
    
    <!-- 主應用程式容器 -->
    <div id="app" class="app-container">
        <!-- 應用程式內容將由 JavaScript 動態載入 -->
    </div>
    
    <!-- 離線提示 -->
    <div id="offlineNotice" class="offline-notice">
        <div class="offline-content">
            <span class="offline-icon">📡</span>
            <span class="offline-text">網路連線中斷，部分功能可能無法使用</span>
        </div>
    </div>
    
    <!-- 更新提示 -->
    <div id="updateNotice" class="update-notice">
        <div class="update-content">
            <span class="update-icon">🔄</span>
            <span class="update-text">發現新版本，點擊更新</span>
            <button class="update-btn" onclick="location.reload()">更新</button>
        </div>
    </div>
    
    <!-- 全局模態框容器 -->
    <div id="modalContainer"></div>
    
    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>
    
    <!-- 載入 JavaScript -->
    <script>
        // 全局配置
        window.APP_CONFIG = {
            apiBaseUrl: '{{ url_for("static", filename="") }}',
            version: '1.0.0',
            debug: {{ 'true' if config.DEBUG else 'false' }},
            features: {
                pwa: true,
                notifications: true,
                darkMode: true,
                realTimeSync: true
            }
        };
        
        // 載入完成後隱藏載入畫面
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loadingScreen');
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        });
        
        // 錯誤處理
        window.addEventListener('error', function(e) {
            console.error('全局錯誤:', e.error);
            // 可以在這裡添加錯誤報告邏輯
        });
        
        // Service Worker 註冊
        if ('serviceWorker' in navigator && window.APP_CONFIG.features.pwa) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(function(registration) {
                        console.log('SW 註冊成功:', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('SW 註冊失敗:', error);
                    });
            });
        }
        
        // 網路狀態監控
        function updateOnlineStatus() {
            const offlineNotice = document.getElementById('offlineNotice');
            if (navigator.onLine) {
                offlineNotice.classList.remove('show');
            } else {
                offlineNotice.classList.add('show');
            }
        }
        
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        
        // 頁面可見性 API
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible' && window.app) {
                // 頁面重新可見時同步數據
                window.app.syncData();
            }
        });
    </script>
    
    <!-- 主應用程式 JavaScript -->
    <script src="{{ url_for('static', filename='js/employee-management.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <!-- 樣式：載入畫面和離線通知 -->
    <style>
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-logo {
            margin-bottom: 2rem;
        }
        
        .loading-logo .logo-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .loading-logo .logo-text {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }
        
        .loading-spinner {
            margin: 2rem 0;
        }
        
        .loading-spinner .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        .loading-text {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .offline-notice,
        .update-notice {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            transition: transform 0.3s ease;
        }
        
        .offline-notice.show,
        .update-notice.show {
            transform: translateX(-50%) translateY(0);
        }
        
        .offline-content,
        .update-content {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #374151;
        }
        
        .offline-notice {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
        }
        
        .update-notice {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.2);
        }
        
        .update-btn {
            background: #22c55e;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .update-btn:hover {
            background: #16a34a;
        }
        
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
        }
        
        .app-container {
            min-height: 100vh;
        }
        
        /* 響應式調整 */
        @media (max-width: 768px) {
            .loading-logo .logo-icon {
                font-size: 3rem;
            }
            
            .loading-logo .logo-text {
                font-size: 1.5rem;
            }
            
            .offline-notice,
            .update-notice {
                left: 10px;
                right: 10px;
                transform: translateY(-100px);
            }
            
            .offline-notice.show,
            .update-notice.show {
                transform: translateY(0);
            }
        }
    </style>
</body>
</html>