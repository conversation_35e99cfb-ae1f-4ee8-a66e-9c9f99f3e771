/**
 * 表單組件系統 - Han AttendanceOS v2005.6.12
 * 遠漢科技考勤系統統一表單規範
 * 
 * 設計原則：
 * - 一致性：統一的視覺風格和交互行為
 * - 可用性：清晰的標籤和錯誤提示
 * - 可訪問性：符合無障礙設計標準
 * - 響應性：適配各種設備和狀態
 */


/* ========== 基礎表單樣式 ========== */

.form {
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-col {
    flex: 1;
}

.form-col-auto {
    flex: 0 0 auto;
}


/* ========== 標籤樣式 ========== */

.form-label,
.label {
    display: block;
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: var(--leading-normal);
}

.form-label.required::after,
.label.required::after {
    content: ' *';
    color: var(--color-error-500);
    font-weight: var(--font-bold);
}

.form-label-inline,
.label-inline {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
    margin-right: var(--spacing-md);
}


/* ========== 基礎輸入框樣式 ========== */

.form-input,
.input,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"] {
    /* 基礎屬性 */
    display: block;
    width: 100%;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    /* 背景和邊框 */
    background-color: var(--bg-primary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    /* 間距 */
    padding: var(--spacing-sm) var(--spacing-md);
    /* 過渡效果 */
    transition: all var(--transition-fast);
    /* 外觀 */
    appearance: none;
    outline: none;
}

.form-input:focus,
.input:focus,
input:focus {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: var(--bg-primary);
}

.form-input:hover,
.input:hover,
input:hover {
    border-color: var(--border-dark);
}

.form-input:disabled,
.input:disabled,
input:disabled {
    background-color: var(--bg-tertiary);
    color: var(--text-quaternary);
    cursor: not-allowed;
    opacity: var(--opacity-60);
}

.form-input::placeholder,
.input::placeholder,
input::placeholder {
    color: var(--text-quaternary);
    opacity: 1;
}


/* ========== 輸入框尺寸 ========== */

.form-input-sm,
.input-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
}

.form-input-lg,
.input-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}


/* ========== 文字區域 ========== */

.form-textarea,
.textarea,
textarea {
    display: block;
    width: 100%;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-fast);
    appearance: none;
    outline: none;
    resize: vertical;
    min-height: 6rem;
}

.form-textarea:focus,
.textarea:focus,
textarea:focus {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea:hover,
.textarea:hover,
textarea:hover {
    border-color: var(--border-dark);
}

.form-textarea:disabled,
.textarea:disabled,
textarea:disabled {
    background-color: var(--bg-tertiary);
    color: var(--text-quaternary);
    cursor: not-allowed;
    opacity: var(--opacity-60);
    resize: none;
}


/* ========== 選擇器 ========== */

.form-select,
.select,
select {
    display: block;
    width: 100%;
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-fast);
    appearance: none;
    outline: none;
}

.form-select:focus,
.select:focus,
select:focus {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:hover,
.select:hover,
select:hover {
    border-color: var(--border-dark);
}

.form-select:disabled,
.select:disabled,
select:disabled {
    background-color: var(--bg-tertiary);
    color: var(--text-quaternary);
    cursor: not-allowed;
    opacity: var(--opacity-60);
}


/* ========== 複選框和單選框 ========== */

.form-checkbox,
.form-radio,
.checkbox,
.radio {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    color: var(--text-primary);
    cursor: pointer;
    user-select: none;
}

.form-checkbox input[type="checkbox"],
.form-radio input[type="radio"],
.checkbox input[type="checkbox"],
.radio input[type="radio"] {
    width: 1.125rem;
    height: 1.125rem;
    margin: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    cursor: pointer;
    appearance: none;
    outline: none;
    position: relative;
    flex-shrink: 0;
}

.form-radio input[type="radio"],
.radio input[type="radio"] {
    border-radius: var(--radius-full);
}

.form-checkbox input[type="checkbox"]:checked,
.form-radio input[type="radio"]:checked,
.checkbox input[type="checkbox"]:checked,
.radio input[type="radio"]:checked {
    background-color: var(--color-primary-500);
    border-color: var(--color-primary-500);
}

.form-checkbox input[type="checkbox"]:checked::after,
.checkbox input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0.375rem;
    height: 0.625rem;
    border: 2px solid white;
    border-top: 0;
    border-left: 0;
    transform: translate(-50%, -60%) rotate(45deg);
}

.form-radio input[type="radio"]:checked::after,
.radio input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0.375rem;
    height: 0.375rem;
    background-color: white;
    border-radius: var(--radius-full);
    transform: translate(-50%, -50%);
}

.form-checkbox input[type="checkbox"]:focus,
.form-radio input[type="radio"]:focus,
.checkbox input[type="checkbox"]:focus,
.radio input[type="radio"]:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-checkbox input[type="checkbox"]:disabled,
.form-radio input[type="radio"]:disabled,
.checkbox input[type="checkbox"]:disabled,
.radio input[type="radio"]:disabled {
    background-color: var(--bg-tertiary);
    border-color: var(--border-light);
    cursor: not-allowed;
    opacity: var(--opacity-60);
}

.form-checkbox:has(input:disabled),
.form-radio:has(input:disabled),
.checkbox:has(input:disabled),
.radio:has(input:disabled) {
    color: var(--text-quaternary);
    cursor: not-allowed;
}


/* ========== 開關按鈕 ========== */

.form-switch,
.switch {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    color: var(--text-primary);
    cursor: pointer;
    user-select: none;
}

.form-switch input[type="checkbox"],
.switch input[type="checkbox"] {
    width: 2.75rem;
    height: 1.5rem;
    margin: 0;
    background-color: var(--bg-quaternary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    cursor: pointer;
    appearance: none;
    outline: none;
    position: relative;
    flex-shrink: 0;
}

.form-switch input[type="checkbox"]:checked,
.switch input[type="checkbox"]:checked {
    background-color: var(--color-primary-500);
    border-color: var(--color-primary-500);
}

.form-switch input[type="checkbox"]::after,
.switch input[type="checkbox"]::after {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.125rem;
    height: 1.125rem;
    background-color: white;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-switch input[type="checkbox"]:checked::after,
.switch input[type="checkbox"]:checked::after {
    transform: translateX(1.25rem);
}

.form-switch input[type="checkbox"]:focus,
.switch input[type="checkbox"]:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-switch input[type="checkbox"]:disabled,
.switch input[type="checkbox"]:disabled {
    background-color: var(--bg-tertiary);
    border-color: var(--border-light);
    cursor: not-allowed;
    opacity: var(--opacity-60);
}

.form-switch:has(input:disabled),
.switch:has(input:disabled) {
    color: var(--text-quaternary);
    cursor: not-allowed;
}


/* ========== 文件上傳 ========== */

.form-file,
.file-input {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.form-file input[type="file"],
.file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label,
.file-input-label {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.form-file:hover .form-file-label,
.file-input:hover .file-input-label {
    background-color: var(--bg-tertiary);
    border-color: var(--border-dark);
}

.form-file:focus-within .form-file-label,
.file-input:focus-within .file-input-label {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}


/* ========== 輸入組合 ========== */

.input-group {
    display: flex;
    width: 100%;
}

.input-group .form-input,
.input-group .input {
    border-radius: 0;
    border-right-width: 0;
}

.input-group .form-input:first-child,
.input-group .input:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.input-group .form-input:last-child,
.input-group .input:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-right-width: 1px;
}

.input-group-prepend,
.input-group-append {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-medium);
    white-space: nowrap;
}

.input-group-prepend {
    border-right: 0;
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.input-group-append {
    border-left: 0;
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
}


/* ========== 表單狀態 ========== */


/* 成功狀態 */

.form-input.is-valid,
.input.is-valid,
.form-textarea.is-valid,
.textarea.is-valid,
.form-select.is-valid,
.select.is-valid {
    border-color: var(--color-success-500);
}

.form-input.is-valid:focus,
.input.is-valid:focus,
.form-textarea.is-valid:focus,
.textarea.is-valid:focus,
.form-select.is-valid:focus,
.select.is-valid:focus {
    border-color: var(--color-success-500);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}


/* 錯誤狀態 */

.form-input.is-invalid,
.input.is-invalid,
.form-textarea.is-invalid,
.textarea.is-invalid,
.form-select.is-invalid,
.select.is-invalid {
    border-color: var(--color-error-500);
}

.form-input.is-invalid:focus,
.input.is-invalid:focus,
.form-textarea.is-invalid:focus,
.textarea.is-invalid:focus,
.form-select.is-invalid:focus,
.select.is-invalid:focus {
    border-color: var(--color-error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}


/* ========== 幫助文字和錯誤訊息 ========== */

.form-help,
.help-text {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    line-height: var(--leading-normal);
}

.form-error,
.error-text {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--text-sm);
    color: var(--color-error-600);
    line-height: var(--leading-normal);
}

.form-success,
.success-text {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--text-sm);
    color: var(--color-success-600);
    line-height: var(--leading-normal);
}


/* ========== 表單佈局 ========== */

.form-horizontal .form-group {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.form-horizontal .form-label {
    flex: 0 0 auto;
    width: 8rem;
    margin-bottom: 0;
    padding-top: var(--spacing-sm);
}

.form-horizontal .form-control {
    flex: 1;
}

.form-inline {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.form-inline .form-group {
    margin-bottom: 0;
    flex: 0 0 auto;
}


/* ========== 響應式設計 ========== */

@media (max-width: 640px) {
    .form-row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    .form-horizontal .form-group {
        flex-direction: column;
        align-items: stretch;
    }
    .form-horizontal .form-label {
        width: auto;
        padding-top: 0;
        margin-bottom: var(--spacing-xs);
    }
    .form-inline {
        flex-direction: column;
        align-items: stretch;
    }
    .input-group {
        flex-direction: column;
    }
    .input-group .form-input,
    .input-group .input {
        border-radius: var(--radius-lg);
        border-right-width: 1px;
        border-bottom-width: 0;
    }
    .input-group .form-input:last-child,
    .input-group .input:last-child {
        border-bottom-width: 1px;
    }
    .input-group-prepend,
    .input-group-append {
        border-radius: var(--radius-lg);
    }
}


/* ========== 無障礙支援 ========== */

@media (prefers-reduced-motion: reduce) {
    .form-input,
    .input,
    .form-textarea,
    .textarea,
    .form-select,
    .select,
    input,
    textarea,
    select {
        transition: none;
    }
}


/* 高對比度模式 */

@media (prefers-contrast: high) {
    .form-input,
    .input,
    .form-textarea,
    .textarea,
    .form-select,
    .select,
    input,
    textarea,
    select {
        border-width: 2px;
    }
    .form-input:focus,
    .input:focus,
    .form-textarea:focus,
    .textarea:focus,
    .form-select:focus,
    .select:focus,
    input:focus,
    textarea:focus,
    select:focus {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    }
}


/* ========== 列印樣式 ========== */

@media print {
    .form-input,
    .input,
    .form-textarea,
    .textarea,
    .form-select,
    .select,
    input,
    textarea,
    select {
        background: white !important;
        border: 1px solid black !important;
        box-shadow: none !important;
    }
    .form-file,
    .file-input {
        display: none !important;
    }
}