<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>換班功能測試</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-button {
            background: linear-gradient(135deg, #7c6df2, #6d4de6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #6d4de6, #5b3dd9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 109, 242, 0.3);
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        /* 班表選擇模態框樣式 */
        
        .shift-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }
        
        .shift-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .shift-modal-content {
            background: white;
            border-radius: 16px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }
        
        .shift-modal-header {
            padding: 24px 24px 0 24px;
            flex-shrink: 0;
        }
        
        .shift-modal-body {
            padding: 0 24px;
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        
        .shift-modal-footer {
            padding: 0 24px 24px 24px;
            flex-shrink: 0;
            border-top: 1px solid #e5e7eb;
            margin-top: 16px;
            padding-top: 16px;
        }
        
        .shift-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 8px;
        }
        
        .shift-option:hover {
            border-color: #7c6df2;
            background: #f8f7ff;
        }
        
        .shift-option.selected {
            border-color: #7c6df2;
            background: linear-gradient(135deg, #f8f7ff, #f3f1ff);
            box-shadow: 0 2px 8px rgba(124, 109, 242, 0.2);
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔄 換班功能測試</h1>
        <p>這個頁面用來測試考勤管理頁面的換班功能是否正常工作。</p>

        <div>
            <h3>測試步驟：</h3>
            <button class="test-button" onclick="testAPI()">
                1. 測試API連接
            </button>
            <button class="test-button" onclick="testLoadShifts()">
                2. 測試載入班表
            </button>
            <button class="test-button" onclick="testShowModal()">
                3. 測試顯示模態框
            </button>
            <button class="test-button" onclick="testUpdateShift()">
                4. 測試換班API
            </button>
        </div>

        <div class="log-area" id="logArea">
            <div>等待測試...</div>
        </div>
    </div>

    <!-- 班表選擇模態框 -->
    <div id="shiftModal" class="shift-modal">
        <div class="shift-modal-content">
            <!-- 模態框頭部 -->
            <div class="shift-modal-header">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                    <h3 style="margin: 0; font-size: 20px; font-weight: bold;">選擇班表</h3>
                    <button type="button" id="closeShiftModalBtn" style="background: none; border: none; cursor: pointer; color: #6b7280;">
                        <i data-lucide="x" style="width: 24px; height: 24px;"></i>
                    </button>
                </div>

                <div style="margin-bottom: 16px;">
                    <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
                        員工：<span id="modalEmployeeName" style="font-weight: 500; color: #111827;">劉志偉</span>
                    </p>
                    <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
                        日期：<span id="modalWorkDate" style="font-weight: 500; color: #111827;">2025-06-30</span>
                    </p>
                </div>
            </div>

            <!-- 模態框主體（可滾動區域） -->
            <div class="shift-modal-body">
                <div id="shiftOptions" style="margin-bottom: 16px;">
                    <!-- 動態載入班表選項 -->
                </div>
            </div>

            <!-- 模態框底部（固定按鈕區域） -->
            <div class="shift-modal-footer">
                <div style="display: flex; justify-content: flex-end; gap: 12px;">
                    <button type="button" id="cancelShiftBtn" class="test-button" style="background: #6b7280;">
                        取消
                    </button>
                    <button type="button" id="confirmShiftBtn" class="test-button">
                        確認修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentAttendanceId = 1934; // 劉志偉6月30日的記錄
        let selectedShiftId = null;
        let shifts = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            bindEvents();
            log('頁面初始化完成');
        });

        // 綁定事件
        function bindEvents() {
            document.getElementById('closeShiftModalBtn').addEventListener('click', hideShiftModal);
            document.getElementById('cancelShiftBtn').addEventListener('click', hideShiftModal);
            document.getElementById('confirmShiftBtn').addEventListener('click', confirmShiftChange);
            log('事件綁定完成');
        }

        // 日誌函數
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 測試API連接
        async function testAPI() {
            log('開始測試API連接...');
            try {
                const response = await fetch('/api/employees');
                const data = await response.json();
                log(`✅ API連接正常，載入了 ${data.length} 個員工`);
            } catch (error) {
                log(`❌ API連接失敗: ${error.message}`);
            }
        }

        // 測試載入班表
        async function testLoadShifts() {
            log('開始測試載入班表...');
            try {
                const response = await fetch('/api/shifts');
                const data = await response.json();
                shifts = data.shifts || data;
                log(`✅ 班表載入成功，共 ${shifts.length} 個班表:`);
                shifts.forEach(shift => {
                    log(`   - ${shift.name} (ID: ${shift.id}) ${shift.start_time}-${shift.end_time}`);
                });
            } catch (error) {
                log(`❌ 班表載入失敗: ${error.message}`);
            }
        }

        // 測試顯示模態框
        async function testShowModal() {
            log('開始測試顯示模態框...');
            if (shifts.length === 0) {
                await testLoadShifts();
            }
            await showShiftModal(1934, '劉志偉', '2025-06-30', 1);
        }

        // 測試換班API
        async function testUpdateShift() {
            log('開始測試換班API...');
            try {
                const response = await fetch('/api/attendance/management/update-shift', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        record_id: 1934,
                        shift_id: 2 // 換成早班
                    })
                });

                const data = await response.json();
                if (response.ok && data.success) {
                    log(`✅ 換班API測試成功: ${JSON.stringify(data)}`);
                } else {
                    log(`❌ 換班API測試失敗: ${data.error || '未知錯誤'}`);
                }
            } catch (error) {
                log(`❌ 換班API測試失敗: ${error.message}`);
            }
        }

        // 顯示班表選擇模態框
        async function showShiftModal(attendanceId, employeeName, workDate, currentShiftId) {
            currentAttendanceId = attendanceId;
            selectedShiftId = currentShiftId;

            // 更新模態框資訊
            document.getElementById('modalEmployeeName').textContent = employeeName;
            document.getElementById('modalWorkDate').textContent = workDate;

            // 載入班表選項
            await loadShiftOptions(currentShiftId);

            // 顯示模態框
            document.getElementById('shiftModal').classList.add('active');
            log('✅ 模態框顯示成功');
        }

        // 隱藏班表選擇模態框
        function hideShiftModal() {
            document.getElementById('shiftModal').classList.remove('active');
            currentAttendanceId = null;
            selectedShiftId = null;
            log('模態框已隱藏');
        }

        // 載入班表選項
        async function loadShiftOptions(currentShiftId) {
            try {
                log('開始載入班表選項...');

                if (shifts.length === 0) {
                    const response = await fetch('/api/shifts');
                    const data = await response.json();
                    shifts = data.shifts || data;
                }

                const container = document.getElementById('shiftOptions');
                container.innerHTML = '';

                shifts.forEach(shift => {
                    const isSelected = shift.id == currentShiftId;
                    const option = document.createElement('div');
                    option.className = `shift-option ${isSelected ? 'selected' : ''}`;
                    option.dataset.shiftId = shift.id;

                    option.innerHTML = `
                        <div>
                            <div style="font-weight: 500; color: #111827;">${shift.name}</div>
                            <div style="font-size: 14px; color: #6b7280;">${shift.start_time} - ${shift.end_time}</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-size: 14px; font-weight: 500; color: #3b82f6;">${calculateWorkDuration(shift.start_time, shift.end_time)}</div>
                            <div style="font-size: 12px; color: #9ca3af;">${shift.code || ''}</div>
                        </div>
                    `;

                    option.addEventListener('click', function() {
                        // 移除其他選項的選中狀態
                        container.querySelectorAll('.shift-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });

                        // 選中當前選項
                        this.classList.add('selected');
                        selectedShiftId = shift.id;
                        log(`選擇班表: ${shift.name} (ID: ${shift.id})`);
                    });

                    container.appendChild(option);
                });

                log(`✅ 載入了 ${shifts.length} 個班表選項`);

            } catch (error) {
                log(`❌ 載入班表選項失敗: ${error.message}`);
            }
        }

        // 確認班表修改
        async function confirmShiftChange() {
            if (!currentAttendanceId || !selectedShiftId) {
                log('❌ 確認修改失敗: 未選擇班表');
                return;
            }

            try {
                log(`開始確認修改: 考勤ID=${currentAttendanceId}, 班表ID=${selectedShiftId}`);

                const response = await fetch('/api/attendance/management/update-shift', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        record_id: currentAttendanceId,
                        shift_id: selectedShiftId
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    log(`✅ 班表修改成功: ${JSON.stringify(data)}`);
                    hideShiftModal();
                } else {
                    log(`❌ 班表修改失敗: ${data.error || '未知錯誤'}`);
                }

            } catch (error) {
                log(`❌ 班表修改失敗: ${error.message}`);
            }
        }

        // 計算工作時長
        function calculateWorkDuration(startTime, endTime) {
            if (!startTime || !endTime) return '';

            const start = new Date(`2000-01-01 ${startTime}`);
            const end = new Date(`2000-01-01 ${endTime}`);

            // 處理跨日情況
            if (end < start) {
                end.setDate(end.getDate() + 1);
            }

            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);

            return `${diffHours}小時`;
        }
    </script>
</body>

</html>