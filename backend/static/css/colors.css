/**
 * 色彩系統 - Han AttendanceOS v2005.6.12
 * 遠漢科技考勤系統統一色彩規範
 * 
 * 設計原則：
 * - 品牌一致性：統一的主色調體系
 * - 語義清晰：明確的狀態色彩表達
 * - 無障礙友好：符合WCAG 2.1 AA標準
 * - 層次分明：豐富的灰階系統
 */

:root {
    /* ========== 主色調系統 ========== */
    /* 遠漢科技品牌藍 - 專業、可信賴 */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    /* 主色調 */
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;
    --color-primary-950: #172554;
    /* ========== 輔助色系統 ========== */
    /* 科技紫 - 創新、現代 */
    --color-secondary-50: #faf5ff;
    --color-secondary-100: #f3e8ff;
    --color-secondary-200: #e9d5ff;
    --color-secondary-300: #d8b4fe;
    --color-secondary-400: #c084fc;
    --color-secondary-500: #a855f7;
    --color-secondary-600: #9333ea;
    --color-secondary-700: #7c3aed;
    --color-secondary-800: #6b21a8;
    --color-secondary-900: #581c87;
    --color-secondary-950: #3b0764;
    /* 強調橙 - 活力、警示 */
    --color-accent-50: #fff7ed;
    --color-accent-100: #ffedd5;
    --color-accent-200: #fed7aa;
    --color-accent-300: #fdba74;
    --color-accent-400: #fb923c;
    --color-accent-500: #f97316;
    --color-accent-600: #ea580c;
    --color-accent-700: #c2410c;
    --color-accent-800: #9a3412;
    --color-accent-900: #7c2d12;
    --color-accent-950: #431407;
    /* ========== 中性色系統 ========== */
    /* 現代灰階 - 平衡、專業 */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;
    /* 暖灰色 - 溫和、親和 */
    --color-warm-gray-50: #fafaf9;
    --color-warm-gray-100: #f5f5f4;
    --color-warm-gray-200: #e7e5e4;
    --color-warm-gray-300: #d6d3d1;
    --color-warm-gray-400: #a8a29e;
    --color-warm-gray-500: #78716c;
    --color-warm-gray-600: #57534e;
    --color-warm-gray-700: #44403c;
    --color-warm-gray-800: #292524;
    --color-warm-gray-900: #1c1917;
    /* ========== 狀態色系統 ========== */
    /* 成功綠 - 完成、正確 */
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;
    --color-success-950: #052e16;
    /* 警告黃 - 注意、提醒 */
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    --color-warning-200: #fde68a;
    --color-warning-300: #fcd34d;
    --color-warning-400: #fbbf24;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;
    --color-warning-800: #92400e;
    --color-warning-900: #78350f;
    --color-warning-950: #451a03;
    /* 錯誤紅 - 危險、錯誤 */
    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;
    --color-error-200: #fecaca;
    --color-error-300: #fca5a5;
    --color-error-400: #f87171;
    --color-error-500: #ef4444;
    --color-error-600: #dc2626;
    --color-error-700: #b91c1c;
    --color-error-800: #991b1b;
    --color-error-900: #7f1d1d;
    --color-error-950: #450a0a;
    /* 資訊藍 - 提示、說明 */
    --color-info-50: #eff6ff;
    --color-info-100: #dbeafe;
    --color-info-200: #bfdbfe;
    --color-info-300: #93c5fd;
    --color-info-400: #60a5fa;
    --color-info-500: #3b82f6;
    --color-info-600: #2563eb;
    --color-info-700: #1d4ed8;
    --color-info-800: #1e40af;
    --color-info-900: #1e3a8a;
    --color-info-950: #172554;
    /* ========== 功能色系統 ========== */
    /* 考勤專用色彩 */
    --color-attendance-present: var(--color-success-500);
    /* 出勤 */
    --color-attendance-absent: var(--color-error-500);
    /* 缺勤 */
    --color-attendance-late: var(--color-warning-500);
    /* 遲到 */
    --color-attendance-early: var(--color-info-500);
    /* 早退 */
    --color-attendance-overtime: var(--color-secondary-500);
    /* 加班 */
    --color-attendance-leave: var(--color-accent-500);
    /* 請假 */
    /* 審核狀態色彩 */
    --color-status-pending: var(--color-warning-500);
    /* 待審核 */
    --color-status-approved: var(--color-success-500);
    /* 已核准 */
    --color-status-rejected: var(--color-error-500);
    /* 已拒絕 */
    --color-status-draft: var(--color-gray-500);
    /* 草稿 */
    /* 優先級色彩 */
    --color-priority-low: var(--color-gray-400);
    /* 低優先級 */
    --color-priority-medium: var(--color-warning-500);
    /* 中優先級 */
    --color-priority-high: var(--color-error-500);
    /* 高優先級 */
    --color-priority-urgent: var(--color-error-700);
    /* 緊急 */
    /* ========== 漸變色系統 ========== */
    --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary-500) 0%, var(--color-secondary-600) 100%);
    --gradient-success: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
    --gradient-warning: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
    --gradient-error: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
    /* 品牌漸變 */
    --gradient-brand: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-500) 100%);
    --gradient-warm: linear-gradient(135deg, var(--color-accent-400) 0%, var(--color-warning-400) 100%);
    --gradient-cool: linear-gradient(135deg, var(--color-primary-400) 0%, var(--color-info-400) 100%);
    /* 背景漸變 */
    --gradient-bg-light: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
    --gradient-bg-dark: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-primary-900) 100%);
    /* 毛玻璃漸變 */
    --gradient-glass-light: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    --gradient-glass-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
    /* ========== 透明度系統 ========== */
    --opacity-0: 0;
    --opacity-5: 0.05;
    --opacity-10: 0.1;
    --opacity-20: 0.2;
    --opacity-25: 0.25;
    --opacity-30: 0.3;
    --opacity-40: 0.4;
    --opacity-50: 0.5;
    --opacity-60: 0.6;
    --opacity-70: 0.7;
    --opacity-75: 0.75;
    --opacity-80: 0.8;
    --opacity-90: 0.9;
    --opacity-95: 0.95;
    --opacity-100: 1;
    /* ========== 邊框色彩 ========== */
    --border-light: var(--color-gray-200);
    --border-medium: var(--color-gray-300);
    --border-dark: var(--color-gray-400);
    --border-primary: var(--color-primary-300);
    --border-success: var(--color-success-300);
    --border-warning: var(--color-warning-300);
    --border-error: var(--color-error-300);
    --border-info: var(--color-info-300);
    /* ========== 文字色彩 ========== */
    --text-primary: var(--color-gray-900);
    --text-secondary: var(--color-gray-700);
    --text-tertiary: var(--color-gray-500);
    --text-quaternary: var(--color-gray-400);
    --text-inverse: var(--color-white);
    --text-brand: var(--color-primary-600);
    --text-success: var(--color-success-700);
    --text-warning: var(--color-warning-700);
    --text-error: var(--color-error-700);
    --text-info: var(--color-info-700);
    /* ========== 背景色彩 ========== */
    --bg-primary: var(--color-white);
    --bg-secondary: var(--color-gray-50);
    --bg-tertiary: var(--color-gray-100);
    --bg-quaternary: var(--color-gray-200);
    --bg-brand: var(--color-primary-500);
    --bg-success: var(--color-success-50);
    --bg-warning: var(--color-warning-50);
    --bg-error: var(--color-error-50);
    --bg-info: var(--color-info-50);
    /* ========== 白色和黑色 ========== */
    --color-white: #ffffff;
    --color-black: #000000;
}


/* ========== 暗色模式色彩覆蓋 ========== */

@media (prefers-color-scheme: dark) {
     :root {
        /* 反轉主要背景和文字色彩 */
        --bg-primary: var(--color-gray-900);
        --bg-secondary: var(--color-gray-800);
        --bg-tertiary: var(--color-gray-700);
        --bg-quaternary: var(--color-gray-600);
        --text-primary: var(--color-gray-100);
        --text-secondary: var(--color-gray-300);
        --text-tertiary: var(--color-gray-400);
        --text-quaternary: var(--color-gray-500);
        /* 調整邊框色彩 */
        --border-light: var(--color-gray-700);
        --border-medium: var(--color-gray-600);
        --border-dark: var(--color-gray-500);
        /* 調整背景漸變 */
        --gradient-bg-light: var(--gradient-bg-dark);
        --gradient-glass-light: var(--gradient-glass-dark);
    }
}


/* ========== 高對比度模式支援 ========== */

@media (prefers-contrast: high) {
     :root {
        /* 增強對比度 */
        --color-primary-500: #0066cc;
        --color-success-500: #008800;
        --color-warning-500: #cc6600;
        --color-error-500: #cc0000;
        --color-info-500: #0066cc;
        /* 強化邊框 */
        --border-light: var(--color-gray-400);
        --border-medium: var(--color-gray-600);
        --border-dark: var(--color-gray-800);
    }
}


/* ========== 色彩工具類別 ========== */

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-tertiary {
    color: var(--text-tertiary);
}

.text-brand {
    color: var(--text-brand);
}

.text-success {
    color: var(--text-success);
}

.text-warning {
    color: var(--text-warning);
}

.text-error {
    color: var(--text-error);
}

.text-info {
    color: var(--text-info);
}

.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.bg-tertiary {
    background-color: var(--bg-tertiary);
}

.bg-brand {
    background-color: var(--bg-brand);
}

.bg-success {
    background-color: var(--bg-success);
}

.bg-warning {
    background-color: var(--bg-warning);
}

.bg-error {
    background-color: var(--bg-error);
}

.bg-info {
    background-color: var(--bg-info);
}

.border-light {
    border-color: var(--border-light);
}

.border-medium {
    border-color: var(--border-medium);
}

.border-dark {
    border-color: var(--border-dark);
}

.border-primary {
    border-color: var(--border-primary);
}

.border-success {
    border-color: var(--border-success);
}

.border-warning {
    border-color: var(--border-warning);
}

.border-error {
    border-color: var(--border-error);
}

.border-info {
    border-color: var(--border-info);
}

.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.gradient-brand {
    background: var(--gradient-brand);
}

.gradient-success {
    background: var(--gradient-success);
}

.gradient-warning {
    background: var(--gradient-warning);
}

.gradient-error {
    background: var(--gradient-error);
}