#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
請假資料與考勤整理功能整合測試腳本

功能：
1. 生成六月份測試請假資料
2. 執行考勤整理功能
3. 驗證請假記錄是否正確整合到考勤記錄中
4. 測試各種請假情況的處理
"""

import requests
import json
from datetime import datetime, timedelta
import subprocess
import sys

# API基礎URL
BASE_URL = "http://127.0.0.1:7072"

def run_leave_data_generation():
    """
    執行請假資料生成腳本
    
    返回：
    - 執行結果
    """
    print("🚀 步驟1：生成六月份測試請假資料")
    print("-" * 50)
    
    try:
        # 執行請假資料生成腳本
        result = subprocess.run([sys.executable, "create_test_leave_data.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 請假資料生成成功")
            print(result.stdout)
            return True
        else:
            print("❌ 請假資料生成失敗")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 請假資料生成超時")
        return False
    except Exception as e:
        print(f"❌ 執行請假資料生成時發生錯誤: {str(e)}")
        return False

def test_attendance_completion_check(target_date):
    """
    測試考勤完整性檢查
    
    參數：
    - target_date: 目標日期
    
    返回：
    - 檢查結果
    """
    print(f"\n🔍 步驟2：檢查 {target_date} 的考勤完整性")
    print("-" * 50)
    
    try:
        url = f"{BASE_URL}/api/attendance/management/daily-completion-check"
        params = {"target_date": target_date}
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ 考勤完整性檢查成功")
            print(f"   總在職員工數：{data['completion_summary']['total_active_employees']}")
            print(f"   有考勤記錄員工數：{data['completion_summary']['employees_with_attendance']}")
            print(f"   有打卡記錄員工數：{data['completion_summary']['employees_with_punches']}")
            print(f"   有請假記錄員工數：{data['completion_summary']['employees_with_leaves']}")
            print(f"   缺少考勤記錄員工數：{data['completion_summary']['missing_attendance_count']}")
            print(f"   考勤記錄覆蓋率：{data['coverage_rates']['attendance_coverage']}%")
            print(f"   請假記錄覆蓋率：{data['coverage_rates']['leave_coverage']}%")
            
            return data
        else:
            print(f"❌ 考勤完整性檢查失敗: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ 考勤完整性檢查時發生錯誤: {str(e)}")
        return None

def test_complete_attendance_generation(target_date, force_regenerate=False):
    """
    測試完整考勤記錄生成
    
    參數：
    - target_date: 目標日期
    - force_regenerate: 是否強制重新生成
    
    返回：
    - 生成結果
    """
    print(f"\n📝 步驟3：生成 {target_date} 的完整考勤記錄")
    print("-" * 50)
    
    try:
        url = f"{BASE_URL}/api/attendance/management/generate-complete"
        data = {
            "target_date": target_date,
            "department_ids": [],
            "force_regenerate": force_regenerate
        }
        
        response = requests.post(url, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 完整考勤記錄生成成功")
            print(f"   處理員工數：{result['processing_summary']['total_active_employees']}")
            print(f"   有打卡記錄員工數：{result['processing_summary']['employees_with_punches']}")
            print(f"   有請假記錄員工數：{result['processing_summary']['employees_with_leaves']}")
            print(f"   新增考勤記錄：{result['generation_results']['generated_count']}")
            print(f"   更新考勤記錄：{result['generation_results']['updated_count']}")
            print(f"   總處理記錄：{result['generation_results']['total_processed']}")
            
            return result
        else:
            print(f"❌ 完整考勤記錄生成失敗: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ 完整考勤記錄生成時發生錯誤: {str(e)}")
        return None

def verify_leave_integration(target_date):
    """
    驗證請假記錄整合結果
    
    參數：
    - target_date: 目標日期
    
    返回：
    - 驗證結果
    """
    print(f"\n🔬 步驟4：驗證 {target_date} 的請假記錄整合")
    print("-" * 50)
    
    try:
        # 查詢考勤管理記錄
        url = f"{BASE_URL}/api/attendance/management"
        params = {
            "start_date": target_date,
            "end_date": target_date,
            "limit": 100
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            records = data['records']
            
            print(f"✅ 查詢到 {len(records)} 筆考勤記錄")
            
            # 分析請假記錄整合情況
            leave_integrated_count = 0
            leave_full_day_count = 0
            leave_partial_count = 0
            leave_partial_absent_count = 0
            normal_count = 0
            absent_count = 0
            
            leave_examples = []
            
            for record in records:
                status = record.get('status', '')
                note = record.get('note', '')
                employee_name = record.get('employee_name', '')
                
                if 'leave' in status:
                    leave_integrated_count += 1
                    
                    if status == 'leave_full_day':
                        leave_full_day_count += 1
                    elif status == 'leave_partial':
                        leave_partial_count += 1
                    elif status == 'leave_partial_absent':
                        leave_partial_absent_count += 1
                    
                    # 收集請假範例
                    if len(leave_examples) < 5:
                        leave_examples.append({
                            'employee_name': employee_name,
                            'status': status,
                            'note': note,
                            'check_in': record.get('check_in'),
                            'check_out': record.get('check_out')
                        })
                
                elif status == 'normal':
                    normal_count += 1
                elif status == 'absent':
                    absent_count += 1
            
            print(f"\n📊 請假記錄整合統計：")
            print(f"   整合請假記錄數：{leave_integrated_count}")
            print(f"   全天請假：{leave_full_day_count}")
            print(f"   部分請假：{leave_partial_count}")
            print(f"   請假未打卡：{leave_partial_absent_count}")
            print(f"   正常出勤：{normal_count}")
            print(f"   缺勤：{absent_count}")
            
            print(f"\n📝 請假記錄範例：")
            for i, example in enumerate(leave_examples, 1):
                print(f"   {i}. {example['employee_name']} - {example['status']}")
                print(f"      上班時間：{example['check_in'] or '無'}")
                print(f"      下班時間：{example['check_out'] or '無'}")
                print(f"      備註：{example['note'] or '無'}")
                print()
            
            return {
                'total_records': len(records),
                'leave_integrated_count': leave_integrated_count,
                'leave_full_day_count': leave_full_day_count,
                'leave_partial_count': leave_partial_count,
                'leave_partial_absent_count': leave_partial_absent_count,
                'normal_count': normal_count,
                'absent_count': absent_count,
                'leave_examples': leave_examples
            }
        else:
            print(f"❌ 查詢考勤記錄失敗: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ 驗證請假記錄整合時發生錯誤: {str(e)}")
        return None

def test_incremental_processing():
    """
    測試增量考勤整理功能
    
    返回：
    - 處理結果
    """
    print(f"\n⚡ 步驟5：測試增量考勤整理功能")
    print("-" * 50)
    
    try:
        # 先獲取上次整理日期
        url = f"{BASE_URL}/api/attendance/management/last-process-date"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            last_date_data = response.json()
            print(f"✅ 上次整理日期：{last_date_data.get('last_process_date', '無')}")
        
        # 執行增量整理
        url = f"{BASE_URL}/api/attendance/management/incremental-process"
        data = {
            "force_start_date": "2025-06-01",  # 強制從6月1日開始
            "department_ids": []
        }
        
        response = requests.post(url, json=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 增量考勤整理成功")
            print(f"   處理日期範圍：{result['processing_summary']['start_date']} 到 {result['processing_summary']['end_date']}")
            print(f"   總處理天數：{result['processing_summary']['total_days']}")
            print(f"   成功天數：{result['processing_summary']['successful_days']}")
            print(f"   失敗天數：{result['processing_summary']['failed_days']}")
            print(f"   總新增記錄：{result['processing_summary']['total_generated']}")
            print(f"   總更新記錄：{result['processing_summary']['total_updated']}")
            print(f"   總處理記錄：{result['processing_summary']['total_processed']}")
            
            # 顯示每日處理結果（前5天）
            print(f"\n📅 每日處理結果（前5天）：")
            for i, daily in enumerate(result['daily_results'][:5]):
                status_icon = "✅" if daily['success'] else "❌"
                print(f"   {status_icon} {daily['date']} - 新增:{daily['generated_count']}, 更新:{daily['updated_count']}")
            
            return result
        else:
            print(f"❌ 增量考勤整理失敗: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ 增量考勤整理時發生錯誤: {str(e)}")
        return None

def generate_test_report(results):
    """
    生成測試報告
    
    參數：
    - results: 測試結果字典
    
    返回：
    - 報告內容
    """
    print(f"\n📋 步驟6：生成測試報告")
    print("=" * 60)
    
    report = []
    report.append("# 請假資料與考勤整理功能整合測試報告")
    report.append(f"## 測試時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 測試概要
    report.append("## 測試概要")
    report.append("本次測試驗證了請假資料與考勤整理功能的整合，包括：")
    report.append("1. 六月份測試請假資料生成")
    report.append("2. 考勤完整性檢查")
    report.append("3. 完整考勤記錄生成")
    report.append("4. 請假記錄整合驗證")
    report.append("5. 增量考勤整理功能")
    report.append("")
    
    # 測試結果
    report.append("## 測試結果")
    
    # 請假資料生成結果
    if results.get('leave_generation'):
        report.append("### ✅ 請假資料生成：成功")
    else:
        report.append("### ❌ 請假資料生成：失敗")
    report.append("")
    
    # 考勤完整性檢查結果
    completion_check = results.get('completion_check')
    if completion_check:
        report.append("### ✅ 考勤完整性檢查：成功")
        report.append(f"- 總在職員工數：{completion_check['completion_summary']['total_active_employees']}")
        report.append(f"- 有請假記錄員工數：{completion_check['completion_summary']['employees_with_leaves']}")
        report.append(f"- 請假記錄覆蓋率：{completion_check['coverage_rates']['leave_coverage']}%")
    else:
        report.append("### ❌ 考勤完整性檢查：失敗")
    report.append("")
    
    # 完整考勤記錄生成結果
    generation = results.get('generation')
    if generation:
        report.append("### ✅ 完整考勤記錄生成：成功")
        report.append(f"- 有請假記錄員工數：{generation['processing_summary']['employees_with_leaves']}")
        report.append(f"- 新增考勤記錄：{generation['generation_results']['generated_count']}")
        report.append(f"- 更新考勤記錄：{generation['generation_results']['updated_count']}")
    else:
        report.append("### ❌ 完整考勤記錄生成：失敗")
    report.append("")
    
    # 請假記錄整合驗證結果
    verification = results.get('verification')
    if verification:
        report.append("### ✅ 請假記錄整合驗證：成功")
        report.append(f"- 總考勤記錄數：{verification['total_records']}")
        report.append(f"- 整合請假記錄數：{verification['leave_integrated_count']}")
        report.append(f"- 全天請假：{verification['leave_full_day_count']}")
        report.append(f"- 部分請假：{verification['leave_partial_count']}")
        report.append(f"- 請假未打卡：{verification['leave_partial_absent_count']}")
        
        if verification['leave_integrated_count'] > 0:
            integration_rate = (verification['leave_integrated_count'] / verification['total_records']) * 100
            report.append(f"- 請假整合率：{integration_rate:.1f}%")
    else:
        report.append("### ❌ 請假記錄整合驗證：失敗")
    report.append("")
    
    # 增量處理結果
    incremental = results.get('incremental')
    if incremental:
        report.append("### ✅ 增量考勤整理：成功")
        report.append(f"- 處理天數：{incremental['processing_summary']['total_days']}")
        report.append(f"- 成功天數：{incremental['processing_summary']['successful_days']}")
        report.append(f"- 總處理記錄：{incremental['processing_summary']['total_processed']}")
    else:
        report.append("### ❌ 增量考勤整理：失敗")
    report.append("")
    
    # 結論
    report.append("## 測試結論")
    
    success_count = sum(1 for key in ['leave_generation', 'completion_check', 'generation', 'verification', 'incremental'] 
                       if results.get(key))
    total_tests = 5
    
    if success_count == total_tests:
        report.append("🎉 **所有測試項目均通過！請假資料與考勤整理功能整合正常。**")
    else:
        report.append(f"⚠️ **{success_count}/{total_tests} 項測試通過，需要檢查失敗的功能。**")
    
    report.append("")
    report.append("## 建議")
    
    if verification and verification['leave_integrated_count'] > 0:
        report.append("- ✅ 請假記錄成功整合到考勤記錄中")
        report.append("- ✅ 系統能正確處理不同類型的請假狀況")
        report.append("- ✅ 考勤整理功能運作正常")
    
    if completion_check and completion_check['coverage_rates']['leave_coverage'] > 0:
        report.append("- ✅ 請假記錄覆蓋率良好")
    
    report.append("- 💡 建議定期執行增量考勤整理以保持資料同步")
    report.append("- 💡 建議監控請假記錄的整合品質")
    
    report_content = "\n".join(report)
    
    # 儲存報告
    with open("leave_integration_test_report.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print("✅ 測試報告已生成：leave_integration_test_report.md")
    print("\n" + "=" * 60)
    print(report_content)
    
    return report_content

def main():
    """
    主函數：執行完整的請假資料整合測試
    """
    print("🚀 請假資料與考勤整理功能整合測試")
    print("=" * 60)
    print("本測試將驗證請假記錄是否能正確整合到考勤記錄中")
    print()
    
    results = {}
    
    try:
        # 1. 生成請假資料
        results['leave_generation'] = run_leave_data_generation()
        
        if not results['leave_generation']:
            print("❌ 請假資料生成失敗，無法繼續測試")
            return
        
        # 測試日期：六月份的幾個工作日
        test_dates = ["2025-06-02", "2025-06-03", "2025-06-04"]
        
        for test_date in test_dates:
            print(f"\n🎯 測試日期：{test_date}")
            print("=" * 40)
            
            # 2. 考勤完整性檢查
            results['completion_check'] = test_attendance_completion_check(test_date)
            
            # 3. 完整考勤記錄生成
            results['generation'] = test_complete_attendance_generation(test_date, force_regenerate=True)
            
            # 4. 驗證請假記錄整合
            results['verification'] = verify_leave_integration(test_date)
            
            # 只測試第一個日期的詳細結果
            if test_date == test_dates[0]:
                break
        
        # 5. 增量考勤整理測試
        results['incremental'] = test_incremental_processing()
        
        # 6. 生成測試報告
        generate_test_report(results)
        
        print("\n🎉 請假資料與考勤整理功能整合測試完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 