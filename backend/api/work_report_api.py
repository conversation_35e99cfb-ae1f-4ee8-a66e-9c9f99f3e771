"""
工作回報API模組
提供工作回報的提交、查詢、管理等功能
"""

from flask import Blueprint, request, jsonify, session
import sqlite3
import json
import os
from datetime import datetime
import logging
import random

# 創建藍圖
work_report_bp = Blueprint('work_report', __name__)

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_connection():
    """獲取資料庫連接"""
    conn = sqlite3.connect('../attendance.db')
    conn.row_factory = sqlite3.Row
    return conn

@work_report_bp.route('/api/work-reports', methods=['POST'])
def submit_work_report():
    """提交工作回報"""
    try:
        data = request.get_json()
        
        # 驗證必填欄位
        required_fields = ['employee_id', 'employee_name', 'report_date', 'report_time', 'category', 'content']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填欄位: {field}'}), 400
        
        # 處理照片上傳（這裡先用空陣列，實際上傳功能需要另外實現）
        photos = data.get('photos', [])
        photos_json = json.dumps(photos)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入工作回報記錄
        cursor.execute('''
            INSERT INTO work_reports 
            (employee_id, employee_name, report_date, report_time, category, sub_category, content, photos)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['employee_id'],
            data['employee_name'],
            data['report_date'],
            data['report_time'],
            data['category'],
            data.get('sub_category'),
            data['content'],
            photos_json
        ))
        
        report_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"工作回報提交成功: {data['employee_name']} ({data['employee_id']}) - {data['category']}")
        
        return jsonify({
            'success': True,
            'message': '工作回報提交成功',
            'report_id': report_id
        })
        
    except Exception as e:
        logger.error(f"提交工作回報失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'提交失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports', methods=['GET'])
def get_work_reports():
    """獲取工作回報列表（員工查看自己的回報）"""
    try:
        employee_id = request.args.get('employee_id')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        category = request.args.get('category', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        if not employee_id:
            return jsonify({'success': False, 'message': '缺少員工ID'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = ['employee_id = ?']
        params = [employee_id]
        
        if category:
            where_conditions.append('category = ?')
            params.append(category)
        
        if start_date:
            where_conditions.append('report_date >= ?')
            params.append(start_date)
        
        if end_date:
            where_conditions.append('report_date <= ?')
            params.append(end_date)
        
        where_clause = ' AND '.join(where_conditions)
        
        # 獲取總數
        count_query = f'SELECT COUNT(*) as total FROM work_reports WHERE {where_clause}'
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 獲取分頁數據
        offset = (page - 1) * limit
        query = f'''
            SELECT id, employee_id, employee_name, report_date, report_time, 
                   category, sub_category, content, photos, is_read, supervisor_feedback, 
                   feedback_date, feedback_by, created_at
            FROM work_reports 
            WHERE {where_clause}
            ORDER BY report_date DESC, report_time DESC
            LIMIT ? OFFSET ?
        '''
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        reports = []
        
        for row in cursor.fetchall():
            report = dict(row)
            # 解析照片JSON
            try:
                report['photos'] = json.loads(report['photos']) if report['photos'] else []
            except:
                report['photos'] = []
            reports.append(report)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': reports,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        })
        
    except Exception as e:
        logger.error(f"獲取工作回報失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'獲取失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/admin', methods=['GET'])
def get_all_work_reports():
    """管理員獲取所有工作回報"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        category = request.args.get('category', '')
        employee_id = request.args.get('employee_id', '')
        is_read = request.args.get('is_read', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_conditions = []
        params = []
        
        if category:
            where_conditions.append('category = ?')
            params.append(category)
        
        if employee_id:
            where_conditions.append('employee_id = ?')
            params.append(employee_id)
        
        if is_read != '':
            where_conditions.append('is_read = ?')
            params.append(int(is_read))
        
        if start_date:
            where_conditions.append('report_date >= ?')
            params.append(start_date)
        
        if end_date:
            where_conditions.append('report_date <= ?')
            params.append(end_date)
        
        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'
        
        # 獲取總數
        count_query = f'SELECT COUNT(*) as total FROM work_reports WHERE {where_clause}'
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 獲取分頁數據
        offset = (page - 1) * limit
        query = f'''
            SELECT id, employee_id, employee_name, report_date, report_time, 
                   category, sub_category, content, photos, is_read, supervisor_feedback, 
                   feedback_date, feedback_by, created_at
            FROM work_reports 
            WHERE {where_clause}
            ORDER BY is_read ASC, report_date DESC, report_time DESC
            LIMIT ? OFFSET ?
        '''
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        reports = []
        
        for row in cursor.fetchall():
            report = dict(row)
            # 解析照片JSON
            try:
                report['photos'] = json.loads(report['photos']) if report['photos'] else []
            except:
                report['photos'] = []
            reports.append(report)
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': reports,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        })
        
    except Exception as e:
        logger.error(f"管理員獲取工作回報失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'獲取失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/<int:report_id>/read', methods=['POST'])
def mark_as_read(report_id):
    """標記為已讀"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE work_reports 
            SET is_read = 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (report_id,))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '找不到該回報記錄'}), 404
        
        conn.commit()
        conn.close()
        
        logger.info(f"工作回報已標記為已讀: ID {report_id}")
        
        return jsonify({
            'success': True,
            'message': '已標記為已讀'
        })
        
    except Exception as e:
        logger.error(f"標記已讀失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'操作失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/<int:report_id>/feedback', methods=['POST'])
def add_feedback(report_id):
    """添加長官回覆"""
    try:
        data = request.get_json()
        feedback = data.get('feedback', '').strip()
        feedback_by = data.get('feedback_by', '')
        
        if not feedback:
            return jsonify({'success': False, 'message': '回覆內容不能為空'}), 400
        
        if not feedback_by:
            return jsonify({'success': False, 'message': '缺少回覆人資訊'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE work_reports 
            SET supervisor_feedback = ?, feedback_date = CURRENT_TIMESTAMP, 
                feedback_by = ?, is_read = 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (feedback, feedback_by, report_id))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '找不到該回報記錄'}), 404
        
        conn.commit()
        conn.close()
        
        logger.info(f"工作回報回覆成功: ID {report_id} by {feedback_by}")
        
        return jsonify({
            'success': True,
            'message': '回覆成功'
        })
        
    except Exception as e:
        logger.error(f"添加回覆失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'回覆失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/upload-photos', methods=['POST'])
def upload_photos():
    """上傳工作回報照片"""
    try:
        if 'photos' not in request.files:
            return jsonify({'success': False, 'message': '沒有上傳的照片'}), 400
        
        files = request.files.getlist('photos')
        if not files or files[0].filename == '':
            return jsonify({'success': False, 'message': '沒有選擇照片'}), 400
        
        # 確保上傳目錄存在
        upload_dir = 'static/uploads/work-reports'
        os.makedirs(upload_dir, exist_ok=True)
        
        uploaded_files = []
        
        for file in files:
            if file and file.filename:
                # 生成唯一檔名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                random_suffix = random.randint(100, 999)
                file_ext = os.path.splitext(file.filename)[1]
                filename = f"work_report_{timestamp}_{random_suffix}{file_ext}"
                
                # 儲存檔案
                file_path = os.path.join(upload_dir, filename)
                file.save(file_path)
                
                # 返回相對於static的URL路徑
                uploaded_files.append(f"/static/uploads/work-reports/{filename}")
        
        return jsonify({
            'success': True,
            'files': uploaded_files
        })
        
    except Exception as e:
        logger.error(f"照片上傳失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'上傳失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/stats', methods=['GET'])
def get_work_report_stats():
    """獲取工作回報統計"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 總回報數
        cursor.execute('SELECT COUNT(*) as total FROM work_reports')
        total_reports = cursor.fetchone()['total']
        
        # 未讀回報數
        cursor.execute('SELECT COUNT(*) as unread FROM work_reports WHERE is_read = 0')
        unread_reports = cursor.fetchone()['unread']
        
        # 已回覆數
        cursor.execute('SELECT COUNT(*) as replied FROM work_reports WHERE supervisor_feedback IS NOT NULL')
        replied_reports = cursor.fetchone()['replied']
        
        # 今日回報數
        cursor.execute('SELECT COUNT(*) as today FROM work_reports WHERE report_date = DATE("now")')
        today_reports = cursor.fetchone()['today']
        
        # 各類別統計
        cursor.execute('''
            SELECT category, COUNT(*) as count 
            FROM work_reports 
            GROUP BY category 
            ORDER BY count DESC
        ''')
        category_stats = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_reports': total_reports,
                'unread_reports': unread_reports,
                'replied_reports': replied_reports,
                'today_reports': today_reports,
                'category_stats': category_stats
            }
        })
        
    except Exception as e:
        logger.error(f"獲取統計失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'獲取統計失敗: {str(e)}'}), 500

@work_report_bp.route('/api/work-reports/<int:report_id>', methods=['DELETE'])
def delete_work_report(report_id):
    """刪除工作回報"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 先查詢回報是否存在，並獲取照片信息
        cursor.execute('SELECT photos FROM work_reports WHERE id = ?', (report_id,))
        report = cursor.fetchone()
        
        if not report:
            conn.close()
            return jsonify({'success': False, 'message': '找不到該回報記錄'}), 404
        
        # 解析照片路徑並刪除照片檔案
        try:
            photos = json.loads(report['photos']) if report['photos'] else []
            for photo_path in photos:
                # 移除開頭的 '/' 並構建完整路徑
                if photo_path.startswith('/'):
                    photo_path = photo_path[1:]
                full_path = os.path.join(os.getcwd(), photo_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
                    logger.info(f"已刪除照片檔案: {full_path}")
        except Exception as e:
            logger.warning(f"刪除照片檔案時發生錯誤: {str(e)}")
        
        # 刪除資料庫記錄
        cursor.execute('DELETE FROM work_reports WHERE id = ?', (report_id,))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '刪除失敗，記錄不存在'}), 404
        
        conn.commit()
        conn.close()
        
        logger.info(f"工作回報刪除成功: ID {report_id}")
        
        return jsonify({
            'success': True,
            'message': '工作回報已成功刪除'
        })
        
    except Exception as e:
        logger.error(f"刪除工作回報失敗: {str(e)}")
        return jsonify({'success': False, 'message': f'刪除失敗: {str(e)}'}), 500 