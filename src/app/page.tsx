import { Button } from "@/components/ui/button"

/**
 * Han AttendanceOS v2025.6.8 - 首頁
 * 遠漢科技考勤系統 Next.js 版本
 */
export default function HomePage() {
    return (
        <div className="min-h-screen bg-gradient-bg-light">
            {/* 頁面標題區域 */}
            <div className="container mx-auto px-4 py-12">
                <div className="text-center mb-12">
                    <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
                        Han AttendanceOS
                    </h1>
                    <p className="text-xl text-gray-600 mb-2">
                        遠漢科技考勤系統
                    </p>
                    <p className="text-lg text-gray-500">
                        Next.js 版本 v2025.6.8
                    </p>
                </div>

                {/* 設計系統展示區域 */}
                <div className="max-w-4xl mx-auto">
                    {/* 按鈕組件展示 */}
                    <div className="glass rounded-3xl p-8 mb-8">
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                            按鈕組件系統
                        </h2>

                        {/* 主要按鈕變體 */}
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-700 mb-4">按鈕變體</h3>
                            <div className="flex flex-wrap gap-4">
                                <Button variant="primary">主要按鈕</Button>
                                <Button variant="secondary">次要按鈕</Button>
                                <Button variant="success">成功按鈕</Button>
                                <Button variant="warning">警告按鈕</Button>
                                <Button variant="error">錯誤按鈕</Button>
                                <Button variant="info">資訊按鈕</Button>
                                <Button variant="outline">外框按鈕</Button>
                                <Button variant="ghost">幽靈按鈕</Button>
                                <Button variant="glass">毛玻璃按鈕</Button>
                            </div>
                        </div>

                        {/* 按鈕尺寸 */}
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-700 mb-4">按鈕尺寸</h3>
                            <div className="flex flex-wrap items-center gap-4">
                                <Button size="xs">超小</Button>
                                <Button size="sm">小</Button>
                                <Button size="md">中</Button>
                                <Button size="lg">大</Button>
                                <Button size="xl">超大</Button>
                            </div>
                        </div>

                        {/* 按鈕狀態 */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-700 mb-4">按鈕狀態</h3>
                            <div className="flex flex-wrap gap-4">
                                <Button>正常狀態</Button>
                                <Button loading>載入中</Button>
                                <Button disabled>禁用狀態</Button>
                            </div>
                        </div>
                    </div>

                    {/* 色彩系統展示 */}
                    <div className="glass rounded-3xl p-8 mb-8">
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                            色彩系統
                        </h2>

                        {/* 主色調 */}
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-700 mb-4">主色調系統</h3>
                            <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                                    <div key={shade} className="text-center">
                                        <div
                                            className={`w-12 h-12 rounded-lg mb-2 bg-primary-${shade}`}
                                            title={`primary-${shade}`}
                                        />
                                        <span className="text-xs text-gray-600">{shade}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* 狀態色彩 */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-700 mb-4">狀態色彩</h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="text-center">
                                    <div className="w-16 h-16 rounded-lg bg-success-500 mb-2 mx-auto" />
                                    <span className="text-sm text-gray-600">成功</span>
                                </div>
                                <div className="text-center">
                                    <div className="w-16 h-16 rounded-lg bg-warning-500 mb-2 mx-auto" />
                                    <span className="text-sm text-gray-600">警告</span>
                                </div>
                                <div className="text-center">
                                    <div className="w-16 h-16 rounded-lg bg-error-500 mb-2 mx-auto" />
                                    <span className="text-sm text-gray-600">錯誤</span>
                                </div>
                                <div className="text-center">
                                    <div className="w-16 h-16 rounded-lg bg-info-500 mb-2 mx-auto" />
                                    <span className="text-sm text-gray-600">資訊</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 系統狀態 */}
                    <div className="glass rounded-3xl p-8">
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                            系統狀態
                        </h2>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">✅ 已完成</h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>• Next.js 14 專案初始化</li>
                                    <li>• TypeScript 配置</li>
                                    <li>• Tailwind CSS 設計系統</li>
                                    <li>• 色彩系統遷移</li>
                                    <li>• 按鈕組件系統</li>
                                    <li>• 工具函數庫</li>
                                    <li>• 類型定義系統</li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">🚧 進行中</h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>• 認證系統開發</li>
                                    <li>• 主儀表板遷移</li>
                                    <li>• API 路由建立</li>
                                    <li>• 資料庫整合</li>
                                </ul>
                            </div>
                        </div>

                        <div className="mt-8 p-4 bg-primary-50 border border-primary-200 rounded-xl">
                            <p className="text-primary-800 font-medium">
                                🎉 第一階段作業1完成！
                            </p>
                            <p className="text-primary-700 mt-1">
                                Next.js 基礎架構和設計系統已成功建立，準備進入作業2：認證系統開發
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 