#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試update-shift API的獨立腳本
"""

import sqlite3
from datetime import datetime

def test_schedules_table():
    """測試schedules表結構"""
    conn = sqlite3.connect('attendance.db')
    cursor = conn.cursor()
    
    # 檢查schedules表結構
    cursor.execute("PRAGMA table_info(schedules)")
    columns = cursor.fetchall()
    print("schedules表欄位:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 測試INSERT語句
    try:
        cursor.execute("""
            INSERT OR REPLACE INTO schedules (employee_id, shift_date, shift_id, created_at)
            VALUES (?, ?, ?, datetime('now'))
        """, (1, '2025-06-04', 1))
        print("✅ schedules INSERT測試成功")
        conn.rollback()  # 回滾測試數據
    except Exception as e:
        print(f"❌ schedules INSERT測試失敗: {e}")
    
    conn.close()

def test_attendance_table():
    """測試attendance表結構"""
    conn = sqlite3.connect('attendance.db')
    cursor = conn.cursor()
    
    # 檢查attendance表結構
    cursor.execute("PRAGMA table_info(attendance)")
    columns = cursor.fetchall()
    print("\nattendance表欄位:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 測試UPDATE語句
    try:
        cursor.execute("""
            UPDATE attendance 
            SET work_date = ?,
                late_minutes = ?, 
                early_leave_minutes = ?, 
                overtime_minutes = ?,
                work_hours = ?,
                shift_id = ?,
                status = ?,
                updated_at = datetime('now')
            WHERE id = ?
        """, ('2025-06-04', 0, 0, 0, 8.0, 1, 'normal', 999))
        print("✅ attendance UPDATE測試成功")
        conn.rollback()  # 回滾測試數據
    except Exception as e:
        print(f"❌ attendance UPDATE測試失敗: {e}")
    
    conn.close()

if __name__ == "__main__":
    print("=== 測試資料庫表結構 ===")
    test_schedules_table()
    test_attendance_table() 