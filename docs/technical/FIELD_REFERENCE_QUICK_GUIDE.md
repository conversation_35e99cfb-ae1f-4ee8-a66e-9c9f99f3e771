# 開發者快速欄位參考指南

## 🚀 快速查詢

### 考勤記錄 (attendance) 常用欄位

| 前端顯示 | 資料庫欄位 | 資料來源 | 說明 |
|---------|-----------|---------|------|
| 上班時間 | `check_in` | attendance | TIMESTAMP 格式 |
| 下班時間 | `check_out` | attendance | TIMESTAMP 格式 |
| 考勤狀態 | `status` | attendance | normal/late/early_leave/overtime/absent |
| 員工姓名 | `employee_name` | JOIN employees.name | 透過 JOIN 獲得 |
| 員工編號 | `employee_code` | JOIN employees.employee_id | 透過 JOIN 獲得 |
| 部門名稱 | `department_name` | JOIN departments.name | 透過 JOIN 獲得 |

### 員工資料 (employees) 常用欄位

| 前端顯示 | 資料庫欄位 | 說明 |
|---------|-----------|------|
| 員工ID | `id` | 主鍵 |
| 員工姓名 | `name` | 員工姓名 |
| 員工編號 | `employee_id` | 唯一員工編號（如 E001） |
| 部門ID | `department_id` | 外鍵關聯 departments.id |
| 員工狀態 | `status` | active/inactive/trial |

### 排班資料 (schedules) 常用欄位

| 前端顯示 | 資料庫欄位 | 說明 |
|---------|-----------|------|
| 排班日期 | `shift_date` | DATE 格式 |
| 班別ID | `shift_id` | 外鍵關聯 shifts.id |
| 排班狀態 | `status` | scheduled/completed/cancelled |
| 加班時數 | `overtime_hours` | DECIMAL(4,2) |

## ❌ 常見錯誤對照表

| 錯誤用法 | 正確用法 | 說明 |
|---------|---------|------|
| `timestamp` | `check_in` / `check_out` | 打卡時間欄位 |
| `clock_type` | `status` | 考勤狀態欄位 |
| `employee_code` | `employee_id` | 員工編號欄位（在 employees 表中） |
| `date` | `shift_date` | 排班日期欄位 |
| `shift_type` | `shift_id` | 班別關聯欄位 |

## 🔧 API 返回欄位對照

### /api/attendance/records 返回欄位

```json
{
  "id": 123,                    // attendance.id
  "employee_id": 1,             // attendance.employee_id
  "employee_name": "張三",       // employees.name (JOIN)
  "employee_code": "E001",      // employees.employee_id (JOIN)
  "department_name": "技術部",   // departments.name (JOIN)
  "check_in": "2025-06-02 09:00:00",
  "check_out": "2025-06-02 18:00:00",
  "status": "normal",
  "device_id": null,
  "note": "",
  "created_at": "2025-06-02 09:00:00"
}
```

## 🛠️ 開發工具

### 檢查資料庫結構
```bash
python check_actual_schema.py
```

### 測試 API 格式
```bash
python test_api_format.py
```

### 對比文檔與實際結構
```bash
python compare_schema_differences.py
```

## 📝 JavaScript 日期處理

### 正確的日期格式化函數
```javascript
function formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-TW', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
```

### 正確的狀態處理函數
```javascript
function getStatusText(status) {
    const statuses = {
        'normal': '正常',
        'late': '遲到',
        'early_leave': '早退',
        'overtime': '加班',
        'absent': '缺勤',
        'manual': '手動補登'
    };
    return statuses[status] || status;
}
``` 