import * as React from "react"
import { cn } from "@/lib/utils"

export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?:
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "error"
    | "info"
    | "outline"
    | "ghost"
    | "link"
    | "glass"
    size?: "xs" | "sm" | "md" | "lg" | "xl"
    loading?: boolean
    icon?: React.ReactNode
    children?: React.ReactNode
}

/**
 * 按鈕組件 - 遷移自 Han AttendanceOS 設計系統
 * 
 * 功能特色：
 * - 完整的變體支援（主要、次要、成功、警告、錯誤等）
 * - 多種尺寸選項（xs, sm, md, lg, xl）
 * - 載入狀態支援
 * - 圖標支援
 * - 無障礙設計
 * - Apple 設計語言風格
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({
        className,
        variant = "primary",
        size = "md",
        loading = false,
        icon,
        children,
        disabled,
        ...props
    }, ref) => {

        // 基礎樣式
        const baseStyles = [
            // 基礎屬性
            "inline-flex items-center justify-center gap-2",
            "font-medium text-center whitespace-nowrap",
            "border border-transparent rounded-lg",
            "transition-all duration-150 ease-in-out",
            "cursor-pointer select-none",
            "outline-none focus:outline-none",
            // 焦點樣式
            "focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
            // 禁用樣式
            "disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none",
            // 防止雙擊選中
            "tap-highlight-transparent"
        ]

        // 尺寸樣式
        const sizeStyles = {
            xs: "px-2 py-1 text-xs leading-tight rounded-sm min-h-[1.5rem]",
            sm: "px-3 py-1.5 text-sm leading-tight rounded-md min-h-[2rem]",
            md: "px-4 py-2 text-base leading-normal rounded-lg min-h-[2.5rem]",
            lg: "px-6 py-3 text-lg leading-normal rounded-xl min-h-[3rem]",
            xl: "px-8 py-4 text-xl leading-normal rounded-xl min-h-[3.5rem]"
        }

        // 變體樣式
        const variantStyles = {
            primary: [
                "bg-gradient-primary text-white border-primary-600 shadow-soft",
                "hover:bg-primary-700 hover:border-primary-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-primary-800 active:border-primary-800 active:shadow-soft active:translate-y-0"
            ],
            secondary: [
                "bg-gradient-secondary text-white border-secondary-600 shadow-soft",
                "hover:bg-secondary-700 hover:border-secondary-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-secondary-800 active:border-secondary-800 active:shadow-soft active:translate-y-0"
            ],
            success: [
                "bg-gradient-success text-white border-success-600 shadow-soft",
                "hover:bg-success-700 hover:border-success-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-success-800 active:border-success-800 active:shadow-soft active:translate-y-0"
            ],
            warning: [
                "bg-gradient-warning text-white border-warning-600 shadow-soft",
                "hover:bg-warning-700 hover:border-warning-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-warning-800 active:border-warning-800 active:shadow-soft active:translate-y-0"
            ],
            error: [
                "bg-gradient-error text-white border-error-600 shadow-soft",
                "hover:bg-error-700 hover:border-error-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-error-800 active:border-error-800 active:shadow-soft active:translate-y-0"
            ],
            info: [
                "bg-gradient-to-r from-info-500 to-info-600 text-white border-info-600 shadow-soft",
                "hover:from-info-600 hover:to-info-700 hover:border-info-700 hover:shadow-medium hover:-translate-y-0.5",
                "active:from-info-700 active:to-info-800 active:border-info-800 active:shadow-soft active:translate-y-0"
            ],
            outline: [
                "bg-transparent text-neutral-700 border-neutral-300",
                "hover:bg-neutral-50 hover:border-neutral-400",
                "active:bg-neutral-100"
            ],
            ghost: [
                "bg-transparent text-neutral-700 border-transparent",
                "hover:bg-neutral-100 hover:text-neutral-900",
                "active:bg-neutral-200"
            ],
            link: [
                "bg-transparent text-primary-600 border-transparent p-0 h-auto min-h-0",
                "hover:text-primary-700 hover:underline",
                "active:text-primary-800"
            ],
            glass: [
                "backdrop-blur-sm bg-white/20 text-neutral-900 border-white/20 shadow-soft",
                "hover:bg-white/30 hover:shadow-medium hover:-translate-y-0.5",
                "active:bg-white/40 active:shadow-soft active:translate-y-0"
            ]
        }

        // 載入動畫樣式
        const loadingSpinner = (
            <svg
                className="animate-spin h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                />
                <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
            </svg>
        )

        return (
            <button
                className={cn(
                    baseStyles,
                    sizeStyles[size],
                    variantStyles[variant],
                    className
                )}
                ref={ref}
                disabled={disabled || loading}
                {...props}
            >
                {loading && loadingSpinner}
                {!loading && icon && icon}
                {children}
            </button>
        )
    }
)

Button.displayName = "Button"

export { Button } 