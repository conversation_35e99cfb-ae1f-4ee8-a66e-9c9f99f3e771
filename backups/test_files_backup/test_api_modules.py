#!/usr/bin/env python3
"""
API模組測試腳本

測試所有已遷移的API模組是否能正常載入和運作。
"""

import sys
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_modules():
    """測試API模組載入"""
    
    print("🔄 開始測試API模組...")
    print("=" * 50)
    
    # 測試考勤管理API
    try:
        from api.attendance_api import attendance_bp
        print("✅ 考勤管理API模組載入成功")
        print(f"   - Blueprint名稱: {attendance_bp.name}")
        print(f"   - URL前綴: {attendance_bp.url_prefix or '/'}")
    except ImportError as e:
        print(f"❌ 考勤管理API模組載入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 考勤管理API模組錯誤: {e}")
        return False
    
    # 測試員工管理API
    try:
        from api.employee_api import employee_bp
        print("✅ 員工管理API模組載入成功")
        print(f"   - Blueprint名稱: {employee_bp.name}")
        print(f"   - URL前綴: {employee_bp.url_prefix or '/'}")
    except ImportError as e:
        print(f"❌ 員工管理API模組載入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 員工管理API模組錯誤: {e}")
        return False
    
    # 測試班表管理API
    try:
        from api.shift_api import shift_bp
        print("✅ 班表管理API模組載入成功")
        print(f"   - Blueprint名稱: {shift_bp.name}")
        print(f"   - URL前綴: {shift_bp.url_prefix or '/'}")
    except ImportError as e:
        print(f"❌ 班表管理API模組載入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 班表管理API模組錯誤: {e}")
        return False
    
    # 測試請假管理API
    try:
        from api.leave_api import leave_bp
        print("✅ 請假管理API模組載入成功")
        print(f"   - Blueprint名稱: {leave_bp.name}")
        print(f"   - URL前綴: {leave_bp.url_prefix or '/'}")
    except ImportError as e:
        print(f"❌ 請假管理API模組載入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 請假管理API模組錯誤: {e}")
        return False
    
    print("=" * 50)
    print("🎉 所有API模組測試通過！")
    return True

def test_database_connection():
    """測試資料庫連接"""
    
    print("\n🔄 測試資料庫連接...")
    print("=" * 50)
    
    try:
        from database import create_connection
        conn = create_connection()
        cursor = conn.cursor()
        
        # 測試基本查詢
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        print(f"✅ 資料庫連接成功")
        print(f"   - 員工總數: {employee_count}")
        
        cursor.execute("SELECT COUNT(*) FROM departments")
        dept_count = cursor.fetchone()[0]
        print(f"   - 部門總數: {dept_count}")
        
        cursor.execute("SELECT COUNT(*) FROM shifts")
        shift_count = cursor.fetchone()[0]
        print(f"   - 班別總數: {shift_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return False

def test_api_integration():
    """測試API整合"""
    
    print("\n🔄 測試API整合...")
    print("=" * 50)
    
    try:
        from flask import Flask
        from api.attendance_api import attendance_bp
        from api.employee_api import employee_bp
        from api.shift_api import shift_bp
        from api.leave_api import leave_bp
        
        # 創建測試應用
        test_app = Flask(__name__)
        
        # 註冊Blueprint
        test_app.register_blueprint(attendance_bp)
        test_app.register_blueprint(employee_bp)
        test_app.register_blueprint(shift_bp)
        test_app.register_blueprint(leave_bp)
        
        print("✅ API模組整合成功")
        
        # 顯示註冊的路由
        with test_app.app_context():
            routes = []
            for rule in test_app.url_map.iter_rules():
                if rule.endpoint.startswith(('attendance.', 'employee.', 'shift.', 'leave.')):
                    routes.append(f"   - {rule.methods} {rule.rule}")
            
            print(f"   - 已註冊路由數量: {len(routes)}")
            if len(routes) <= 10:  # 只顯示前10個路由
                for route in routes[:10]:
                    print(route)
            else:
                for route in routes[:5]:
                    print(route)
                print(f"   - ... 還有 {len(routes) - 5} 個路由")
        
        return True
        
    except Exception as e:
        print(f"❌ API整合失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🚀 AttendanceOS Elite - API模組測試")
    print("=" * 60)
    
    # 執行測試
    tests = [
        ("API模組載入", test_api_modules),
        ("資料庫連接", test_database_connection),
        ("API整合", test_api_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"\n❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！API模組遷移成功！")
        return 0
    else:
        print("⚠️  部分測試失敗，請檢查相關問題")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 