// 智慧考勤系統 - Service Worker
// 版本: 1.0.0

const CACHE_NAME = 'attendance-system-v1.0.0';
const urlsToCache = [
  '/',
  '/static/css/design-system.css',
  '/static/css/components.css',
  '/static/css/main.css',
  '/static/js/app.js',
  '/static/manifest.json'
];

// 安裝事件
self.addEventListener('install', function(event) {
  console.log('Service Worker: 安裝中...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Service Worker: 快取檔案');
        return cache.addAll(urlsToCache);
      })
      .then(function() {
        console.log('Service Worker: 安裝完成');
        return self.skipWaiting();
      })
  );
});

// 啟動事件
self.addEventListener('activate', function(event) {
  console.log('Service Worker: 啟動中...');
  
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: 清除舊快取', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(function() {
      console.log('Service Worker: 啟動完成');
      return self.clients.claim();
    })
  );
});

// 擷取事件
self.addEventListener('fetch', function(event) {
  // 只處理 GET 請求
  if (event.request.method !== 'GET') {
    return;
  }

  // 對於 API 請求，使用網路優先策略
  if (event.request.url.includes('/api/')) {
    event.respondWith(
      fetch(event.request)
        .then(function(response) {
          // 如果是成功的回應，可以選擇快取
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseClone);
              });
          }
          return response;
        })
        .catch(function() {
          // 網路失敗時嘗試從快取讀取
          return caches.match(event.request);
        })
    );
    return;
  }

  // 對於靜態資源，使用快取優先策略
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // 如果在快取中找到，直接返回
        if (response) {
          return response;
        }

        // 否則從網路獲取
        return fetch(event.request)
          .then(function(response) {
            // 檢查是否為有效回應
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // 複製回應並加入快取
            const responseToCache = response.clone();
            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseToCache);
              });

            return response;
          });
      })
  );
});

// 背景同步事件
self.addEventListener('sync', function(event) {
  if (event.tag === 'background-sync') {
    console.log('Service Worker: 背景同步');
    event.waitUntil(doBackgroundSync());
  }
});

// 推送通知事件
self.addEventListener('push', function(event) {
  console.log('Service Worker: 收到推送通知');
  
  const options = {
    body: event.data ? event.data.text() : '智慧考勤系統通知',
    icon: '/static/icons/icon-192x192.png',
    badge: '/static/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看詳情',
        icon: '/static/icons/checkmark.png'
      },
      {
        action: 'close',
        title: '關閉',
        icon: '/static/icons/xmark.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('智慧考勤系統', options)
  );
});

// 通知點擊事件
self.addEventListener('notificationclick', function(event) {
  console.log('Service Worker: 通知被點擊');
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // 關閉通知，不執行其他動作
  } else {
    // 預設動作：開啟應用程式
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// 背景同步函數
function doBackgroundSync() {
  // 這裡可以實現離線時的數據同步邏輯
  return new Promise(function(resolve) {
    console.log('Service Worker: 執行背景同步');
    // 實際的同步邏輯
    setTimeout(resolve, 1000);
  });
}

// 錯誤處理
self.addEventListener('error', function(event) {
  console.error('Service Worker 錯誤:', event.error);
});

self.addEventListener('unhandledrejection', function(event) {
  console.error('Service Worker 未處理的 Promise 拒絕:', event.reason);
});

// 定期清理快取
self.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CLEAN_CACHE') {
    event.waitUntil(
      caches.keys().then(function(cacheNames) {
        return Promise.all(
          cacheNames.map(function(cacheName) {
            if (cacheName !== CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      })
    );
  }
});

console.log('Service Worker: 已載入');