# 公司特定配置系統
COMPANY_CONFIGS = {
    'default': {
        'database_schema': 'public',
        'api_version': 'v1',
        'custom_fields': [],
        'ui_theme': 'default',
        'features': ['basic_attendance', 'leave_management']
    },
    
    'company_abc': {
        'database_schema': 'company_abc',
        'api_version': 'v2',  # 使用不同API版本
        'custom_fields': [
            {'name': 'project_code', 'type': 'string', 'required': True},
            {'name': 'cost_center', 'type': 'string', 'required': False}
        ],
        'ui_theme': 'abc_custom',
        'features': ['basic_attendance', 'leave_management', 'project_tracking', 'cost_center']
    },
    
    'company_xyz': {
        'database_schema': 'company_xyz', 
        'api_version': 'v2',
        'custom_fields': [
            {'name': 'department_level', 'type': 'integer', 'required': True},
            {'name': 'security_clearance', 'type': 'string', 'required': True}
        ],
        'ui_theme': 'xyz_secure',
        'features': ['basic_attendance', 'leave_management', 'security_tracking']
    }
}

def get_company_config(company_id):
    """
    獲取公司特定配置
    
    參數：
    company_id (str): 公司ID
    
    返回：
    dict: 公司配置字典
    """
    return COMPANY_CONFIGS.get(company_id, COMPANY_CONFIGS['default'])

def get_api_version(company_id):
    """
    獲取公司使用的API版本
    
    參數：
    company_id (str): 公司ID
    
    返回：
    str: API版本號
    """
    config = get_company_config(company_id)
    return config.get('api_version', 'v1')

def get_custom_fields(company_id):
    """
    獲取公司自定義欄位
    
    參數：
    company_id (str): 公司ID
    
    返回：
    list: 自定義欄位列表
    """
    config = get_company_config(company_id)
    return config.get('custom_fields', []) 