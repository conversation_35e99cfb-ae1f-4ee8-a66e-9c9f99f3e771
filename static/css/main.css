/* 
智慧考勤系統 - 主樣式表
現代化 UI 實現
*/

/* ============ 登入頁面樣式 ============ */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-3xl);
  padding: var(--space-10);
  box-shadow: var(--shadow-2xl);
  max-width: 450px;
  width: 90%;
  position: relative;
  z-index: 2;
  animation: slideInUp var(--duration-500) var(--ease-bounce);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.logo-icon {
  font-size: var(--text-4xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 2s infinite;
}

.logo-text {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0;
}

.login-subtitle {
  color: var(--neutral-600);
  font-size: var(--text-base);
  margin: 0;
}

.login-form {
  margin-bottom: var(--space-6);
}

.login-form .form-group {
  margin-bottom: var(--space-6);
  position: relative;
}

.login-form .form-input {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  font-size: var(--text-base);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  background: rgba(255, 255, 255, 0.8);
  transition: all var(--duration-300) var(--ease-out);
}

.login-form .form-input:focus {
  border-color: var(--primary-400);
  box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
}

.login-form .form-group.focused .form-label {
  color: var(--primary-600);
  transform: translateY(-2px);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--neutral-600);
  cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  background: white;
  position: relative;
  transition: all var(--duration-200) var(--ease-out);
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-mark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.login-btn {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-top: var(--space-4);
  position: relative;
  overflow: hidden;
}

.login-footer {
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid var(--neutral-200);
}

.demo-info {
  background: var(--primary-50);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin: 0;
}

/* ============ 儀表板樣式 ============ */
.dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-50) 100%);
}

/* 頂部導航 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 700;
  font-size: var(--text-lg);
  color: var(--neutral-800);
}

.navbar-content {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.time-display {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--neutral-600);
  padding: var(--space-2) var(--space-4);
  background: var(--neutral-100);
  border-radius: var(--radius-lg);
}

.user-menu {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-4);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.user-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-sm);
}

.user-name {
  font-weight: 600;
  color: var(--neutral-800);
}

.user-role {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  padding: var(--space-2);
  margin-top: var(--space-2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--duration-300) var(--ease-out);
  z-index: 200;
}

.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown a {
  display: block;
  padding: var(--space-3) var(--space-4);
  color: var(--neutral-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--duration-200) var(--ease-out);
}

.user-dropdown a:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

.dropdown-divider {
  height: 1px;
  background: var(--neutral-200);
  margin: var(--space-2) 0;
}

/* 側邊欄 */
.sidebar {
  position: fixed;
  left: 0;
  top: 80px;
  bottom: 0;
  width: 280px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-6);
  z-index: 50;
  overflow-y: auto;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--duration-300) var(--ease-out);
  color: var(--neutral-600);
  font-weight: 500;
}

.menu-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateX(5px);
}

.menu-item.active {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.menu-icon {
  font-size: var(--text-xl);
  width: 24px;
  text-align: center;
}

.menu-text {
  font-size: var(--text-base);
}

/* 主要內容 */
.main-content {
  margin-left: 280px;
  padding: var(--space-6);
  min-height: calc(100vh - 80px);
}

/* 統計卡片網格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* 儀表板網格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-6);
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.dashboard-card .card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-card .card-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--neutral-800);
}

.dashboard-card .card-body {
  padding: var(--space-6);
}

/* 考勤頁面樣式 */
.attendance-view {
  max-width: 1200px;
  margin: 0 auto;
}

.view-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-4);
  border-bottom: 2px solid var(--neutral-100);
}

.view-header h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--neutral-800);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-3);
}

.clock-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.clock-card {
  background: var(--gradient-primary);
  color: white;
  border-radius: var(--radius-3xl);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.clock-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.clock-display {
  position: relative;
  z-index: 2;
  margin-bottom: var(--space-8);
}

.current-time-large {
  font-size: var(--text-4xl);
  font-weight: 700;
  font-family: var(--font-family-mono);
  margin-bottom: var(--space-2);
}

.clock-date {
  font-size: var(--text-lg);
  opacity: 0.9;
}

.clock-actions {
  position: relative;
  z-index: 2;
  display: flex;
  gap: var(--space-4);
  justify-content: center;
}

.clock-actions .btn {
  flex-direction: column;
  gap: var(--space-1);
  min-width: 120px;
  height: 80px;
}

.clock-actions .btn span {
  font-size: var(--text-lg);
  font-weight: 600;
}

.clock-actions .btn small {
  font-size: var(--text-xs);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.attendance-status {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--neutral-100);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: var(--neutral-600);
}

.attendance-history {
  margin-top: var(--space-8);
}

/* 部門統計樣式 */
.dept-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--neutral-100);
}

.dept-stat-item:last-child {
  border-bottom: none;
}

.dept-name {
  font-weight: 500;
  color: var(--neutral-700);
}

.dept-count {
  font-weight: 600;
  color: var(--primary-600);
}

/* 通知樣式 */
.notification {
  min-width: 300px;
  margin-bottom: var(--space-3);
  animation: slideInFromRight var(--duration-300) var(--ease-out);
}

.notification-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-3);
}

.notification-message {
  flex: 1;
  font-size: var(--text-sm);
  line-height: 1.5;
}

.notification-close {
  background: none;
  border: none;
  font-size: var(--text-lg);
  color: var(--neutral-400);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--duration-200) var(--ease-out);
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--neutral-600);
}

/* 響應式設計 */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--duration-300) var(--ease-out);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .clock-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: var(--space-3) var(--space-4);
  }
  
  .navbar-content {
    gap: var(--space-3);
  }
  
  .time-display {
    display: none;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .view-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .clock-actions {
    flex-direction: column;
  }
  
  .login-card {
    padding: var(--space-6);
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--space-4);
  }
  
  .stats-grid {
    gap: var(--space-4);
  }
  
  .dashboard-card .card-header,
  .dashboard-card .card-body {
    padding: var(--space-4);
  }
  
  .current-time-large {
    font-size: var(--text-2xl);
  }
  
  .clock-card {
    padding: var(--space-6);
  }
}