#!/usr/bin/env python3
"""
測試換班API功能
"""

import requests
import json

def test_shift_change_api():
    """測試換班API"""
    base_url = "http://127.0.0.1:7072"
    
    print("=== 測試換班API功能 ===")
    
    # 1. 測試獲取班表列表
    print("\n1. 測試獲取班表列表...")
    try:
        response = requests.get(f"{base_url}/api/shifts")
        if response.status_code == 200:
            shifts = response.json()
            print(f"✅ 成功獲取班表列表，共 {len(shifts.get('shifts', []))} 個班表")
            for shift in shifts.get('shifts', []):
                print(f"   - {shift['name']} (ID: {shift['id']}) {shift['start_time']}-{shift['end_time']}")
        else:
            print(f"❌ 獲取班表列表失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 獲取班表列表異常: {e}")
    
    # 2. 測試獲取考勤記錄
    print("\n2. 測試獲取考勤記錄...")
    try:
        response = requests.get(f"{base_url}/api/attendance/records?employee_id=3&start_date=2025-06-30&end_date=2025-06-30")
        if response.status_code == 200:
            data = response.json()
            records = data.get('records', [])
            if records:
                record = records[0]
                print(f"✅ 找到測試記錄: ID={record['id']}, 員工={record['employee_name']}, 班表={record['shift_name']}")
                
                # 3. 測試換班API
                print("\n3. 測試換班API...")
                test_data = {
                    "record_id": record['id'],
                    "shift_id": 2  # 換到ID為2的班表
                }
                
                response = requests.post(
                    f"{base_url}/api/attendance/management/update-shift",
                    headers={"Content-Type": "application/json"},
                    data=json.dumps(test_data)
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("✅ 換班API測試成功!")
                        print(f"   - 更新的班表ID: {result.get('updated_shift_id')}")
                        if 'recalculated_data' in result:
                            calc_data = result['recalculated_data']
                            print(f"   - 重新計算結果:")
                            print(f"     * 班表: {calc_data.get('shift_name')} ({calc_data.get('shift_time')})")
                            print(f"     * 工作時數: {calc_data.get('work_hours', 0):.1f}小時")
                            print(f"     * 遲到: {calc_data.get('late_minutes', 0)}分鐘")
                            print(f"     * 早退: {calc_data.get('early_leave_minutes', 0)}分鐘")
                            print(f"     * 加班: {calc_data.get('overtime_minutes', 0)}分鐘")
                            print(f"     * 狀態: {calc_data.get('status', 'unknown')}")
                    else:
                        print(f"❌ 換班API返回失敗: {result.get('error', '未知錯誤')}")
                else:
                    print(f"❌ 換班API請求失敗: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   錯誤詳情: {error_data}")
                    except:
                        print(f"   響應內容: {response.text}")
            else:
                print("❌ 沒有找到測試記錄")
        else:
            print(f"❌ 獲取考勤記錄失敗: {response.status_code}")
    except Exception as e:
        print(f"❌ 測試異常: {e}")

if __name__ == "__main__":
    test_shift_change_api() 