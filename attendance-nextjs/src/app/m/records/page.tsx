"use client"

import { withAuth, useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { getAttendanceRecords, getAttendanceStatusOptions } from '@/lib/api-client'
import type { AttendanceRecord } from '@/types'
import {
    Clock,
    Search,
    Download,
    Filter,
    ArrowLeft,
    CheckCircle,
    XCircle,
    AlertCircle,
    Calendar,
    ChevronLeft,
    ChevronRight,
    LogOut,
    CalendarX,
    Edit
} from 'lucide-react'
import Link from 'next/link'

function AttendanceRecordsPage() {
    const { user } = useAuth()
    const [records, setRecords] = useState<AttendanceRecord[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [statusOptions, setStatusOptions] = useState<{ code: string, name: string, color: string, bgColor: string, icon: string }[]>([])
    const [searchParams, setSearchParams] = useState({
        start_date: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0],
        employee_id: '',
        status: ''
    })
    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const [showFilters, setShowFilters] = useState(false)

    // 載入考勤記錄
    const fetchRecords = async (page = 1) => {
        setLoading(true)
        try {
            const params = {
                page,
                limit: 20,
                employee_id: user?.employee_id || searchParams.employee_id,
                start_date: searchParams.start_date,
                end_date: searchParams.end_date,
                status: searchParams.status || undefined
            }

            const response = await getAttendanceRecords(params)
            if (response.success && response.data) {
                setRecords(response.data)
                // 計算總頁數
                setTotalPages(Math.ceil(response.data.length / 20))
            }
        } catch (error) {
            console.error('載入考勤記錄失敗:', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        // 載入狀態選項
        const loadStatusOptions = () => {
            const options = getAttendanceStatusOptions()
            setStatusOptions(options)
        }

        loadStatusOptions()
        fetchRecords(currentPage)
    }, [user, currentPage, searchParams])

    const handleSearch = () => {
        setCurrentPage(1)
        fetchRecords(1)
        setShowFilters(false)
    }

    const getStatusIcon = (status: string) => {
        const statusConfig = statusOptions.find(option => option.code === status)
        if (!statusConfig) return <Clock className="w-4 h-4" />

        // 根據圖標名稱返回對應的圖標組件
        switch (statusConfig.icon) {
            case 'check-circle': return <CheckCircle className="w-4 h-4" />
            case 'clock': return <Clock className="w-4 h-4" />
            case 'log-out': return <LogOut className="w-4 h-4" />
            case 'x-circle': return <XCircle className="w-4 h-4" />
            case 'calendar-x': return <CalendarX className="w-4 h-4" />
            case 'edit': return <Edit className="w-4 h-4" />
            default: return <Clock className="w-4 h-4" />
        }
    }

    const getStatusText = (status: string) => {
        const statusConfig = statusOptions.find(option => option.code === status)
        return statusConfig ? statusConfig.name : status
    }

    const getStatusColor = (status: string) => {
        const statusConfig = statusOptions.find(option => option.code === status)
        return statusConfig ? `${statusConfig.color} ${statusConfig.bgColor}` : 'text-gray-600 bg-gray-100'
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
            {/* Header */}
            <header className="bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-10">
                <div className="px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <Link href="/m">
                                <Button variant="ghost" size="sm" className="mr-3">
                                    <ArrowLeft className="w-4 h-4" />
                                </Button>
                            </Link>
                            <h1 className="text-lg font-bold text-neutral-900">考勤記錄</h1>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowFilters(!showFilters)}
                                className={showFilters ? "bg-blue-50 text-blue-600" : ""}
                            >
                                <Filter className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                                <Download className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="px-4 py-4">
                {/* 搜尋篩選區域 */}
                {showFilters && (
                    <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg mb-4">
                        <div className="flex items-center space-x-2 mb-3">
                            <Filter className="w-4 h-4 text-neutral-600" />
                            <h2 className="font-semibold text-neutral-900">篩選條件</h2>
                        </div>

                        <div className="space-y-3">
                            <div>
                                <label className="block text-sm font-medium text-neutral-700 mb-1">開始日期</label>
                                <input
                                    type="date"
                                    value={searchParams.start_date}
                                    onChange={(e) => setSearchParams({ ...searchParams, start_date: e.target.value })}
                                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-neutral-700 mb-1">結束日期</label>
                                <input
                                    type="date"
                                    value={searchParams.end_date}
                                    onChange={(e) => setSearchParams({ ...searchParams, end_date: e.target.value })}
                                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-neutral-700 mb-1">考勤狀態</label>
                                <select
                                    value={searchParams.status}
                                    onChange={(e) => setSearchParams({ ...searchParams, status: e.target.value })}
                                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                                >
                                    <option value="">全部狀態</option>
                                    <option value="normal">正常</option>
                                    <option value="late">遲到</option>
                                    <option value="early_leave">早退</option>
                                    <option value="absent">缺勤</option>
                                    <option value="manual">手動</option>
                                </select>
                            </div>

                            <div className="flex space-x-2 pt-2">
                                <Button
                                    variant="primary"
                                    size="sm"
                                    onClick={handleSearch}
                                    className="flex-1"
                                >
                                    <Search className="w-4 h-4 mr-2" />
                                    搜尋
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setShowFilters(false)}
                                    className="px-4"
                                >
                                    取消
                                </Button>
                            </div>
                        </div>
                    </div>
                )}

                {/* 考勤記錄列表 */}
                <div className="space-y-3">
                    {loading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        </div>
                    ) : records.length === 0 ? (
                        <div className="text-center py-12">
                            <Calendar className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-neutral-900 mb-2">暫無考勤記錄</h3>
                            <p className="text-neutral-500">請調整篩選條件或聯繫管理員</p>
                        </div>
                    ) : (
                        records.map((record) => (
                            <div key={record.id} className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <Calendar className="w-5 h-5 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-neutral-900">
                                                {record.work_date ? new Date(record.work_date).toLocaleDateString('zh-TW', {
                                                    month: 'short',
                                                    day: 'numeric',
                                                    weekday: 'short'
                                                }) : '未知日期'}
                                            </h3>
                                            <p className="text-sm text-neutral-600">{record.work_date || '未知日期'}</p>
                                        </div>
                                    </div>
                                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                                        <div className="flex items-center space-x-1">
                                            {getStatusIcon(record.status)}
                                            <span>{getStatusText(record.status)}</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-neutral-600">上班時間</span>
                                        <p className="font-medium">
                                            {record.check_in ?
                                                new Date(record.check_in).toLocaleTimeString('zh-TW', {
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                }) :
                                                '未打卡'
                                            }
                                        </p>
                                    </div>
                                    <div>
                                        <span className="text-neutral-600">下班時間</span>
                                        <p className="font-medium">
                                            {record.check_out ?
                                                new Date(record.check_out).toLocaleTimeString('zh-TW', {
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                }) :
                                                '未打卡'
                                            }
                                        </p>
                                    </div>
                                </div>

                                {/* 工作時數計算 */}
                                {record.check_in && record.check_out && (
                                    <div className="mt-3 pt-3 border-t border-neutral-200">
                                        <div className="flex justify-between items-center text-sm">
                                            <span className="text-neutral-600">工作時數</span>
                                            <span className="font-medium text-blue-600">
                                                {(() => {
                                                    const checkIn = new Date(record.check_in)
                                                    const checkOut = new Date(record.check_out)
                                                    const diffMs = checkOut.getTime() - checkIn.getTime()
                                                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
                                                    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
                                                    return `${diffHours}小時${diffMinutes}分鐘`
                                                })()}
                                            </span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))
                    )}
                </div>

                {/* 分頁控制 */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-center space-x-2 mt-6">
                        <Button
                            variant="outline"
                            size="sm"
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(currentPage - 1)}
                        >
                            <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <span className="text-sm text-neutral-600">
                            第 {currentPage} 頁，共 {totalPages} 頁
                        </span>
                        <Button
                            variant="outline"
                            size="sm"
                            disabled={currentPage === totalPages}
                            onClick={() => setCurrentPage(currentPage + 1)}
                        >
                            <ChevronRight className="w-4 h-4" />
                        </Button>
                    </div>
                )}
            </main>
        </div>
    )
}

export default withAuth(AttendanceRecordsPage) 