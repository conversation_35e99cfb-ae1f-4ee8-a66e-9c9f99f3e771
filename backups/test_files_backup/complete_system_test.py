#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系統測試腳本
Han AttendanceOS v2005.6.12 - 遠漢科技考勤系統

功能：
1. 清理6月份所有數據
2. 生成測試數據（打卡、加班、請假）
3. 執行審核流程
4. 計算考勤數據
5. 生成測試報告
"""

import sqlite3
import random
from datetime import datetime, timedelta, time
import json
import os

class CompleteSystemTest:
    def __init__(self, db_path='attendance.db'):
        """
        初始化完整系統測試類別
        
        參數：
        db_path (str): 數據庫文件路徑
        """
        self.db_path = db_path
        self.test_employees = ['E001', 'E002', 'E003']
        self.test_report = {
            'test_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'employees': {},
            'summary': {}
        }
        
    def connect_db(self):
        """連接數據庫"""
        return sqlite3.connect(self.db_path)
    
    def step1_cleanup_june_data(self):
        """第一階段：清理6月份所有數據"""
        print("🧹 第一階段：清理6月份數據...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        try:
            # 清理打卡記錄 (punch_records表)
            cursor.execute("DELETE FROM punch_records WHERE strftime('%Y-%m', punch_date) = '2025-06'")
            punches_deleted = cursor.rowcount
            
            # 清理請假記錄 (leaves表)
            cursor.execute("DELETE FROM leaves WHERE strftime('%Y-%m', start_date) = '2025-06'")
            leaves_deleted = cursor.rowcount
            
            # 清理加班記錄 (overtime_requests表)
            cursor.execute("DELETE FROM overtime_requests WHERE strftime('%Y-%m', overtime_date) = '2025-06'")
            overtime_deleted = cursor.rowcount
            
            # 清理考勤記錄 (attendance表)
            cursor.execute("DELETE FROM attendance WHERE strftime('%Y-%m', work_date) = '2025-06'")
            attendance_deleted = cursor.rowcount
            
            conn.commit()
            
            print(f"   ✅ 已刪除打卡記錄：{punches_deleted} 筆")
            print(f"   ✅ 已刪除請假記錄：{leaves_deleted} 筆")
            print(f"   ✅ 已刪除加班記錄：{overtime_deleted} 筆")
            print(f"   ✅ 已刪除考勤記錄：{attendance_deleted} 筆")
            
            self.test_report['cleanup'] = {
                'punches_deleted': punches_deleted,
                'leaves_deleted': leaves_deleted,
                'overtime_deleted': overtime_deleted,
                'attendance_deleted': attendance_deleted
            }
            
        except Exception as e:
            print(f"   ❌ 清理數據時發生錯誤：{e}")
            conn.rollback()
        finally:
            conn.close()
    
    def step2_generate_punch_records(self):
        """第二階段：生成6月份打卡記錄"""
        print("⏰ 第二階段：生成打卡記錄...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # 6月份工作日（排除週末）
        june_workdays = []
        start_date = datetime(2025, 6, 1)
        end_date = datetime(2025, 6, 30)
        
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 週一到週五
                june_workdays.append(current_date.date())
            current_date += timedelta(days=1)
        
        print(f"   📅 6月份工作日共：{len(june_workdays)} 天")
        
        for employee_id in self.test_employees:
            self.test_report['employees'][employee_id] = {
                'punch_records': [],
                'late_days': [],
                'early_leave_days': [],
                'overtime_requests': [],
                'leave_requests': []
            }
            
            punch_count = 0
            late_count = 0
            early_count = 0
            
            for work_date in june_workdays:
                # 正常上班時間 09:00，下班時間 18:00
                base_checkin = datetime.combine(work_date, time(9, 0))
                base_checkout = datetime.combine(work_date, time(18, 0))
                
                # 隨機生成遲到和早退情況
                is_late = random.random() < 0.15  # 15%機率遲到
                is_early = random.random() < 0.10  # 10%機率早退
                
                # 計算實際打卡時間
                if is_late:
                    # 遲到5-30分鐘
                    late_minutes = random.randint(5, 30)
                    actual_checkin = base_checkin + timedelta(minutes=late_minutes)
                    late_count += 1
                    self.test_report['employees'][employee_id]['late_days'].append({
                        'date': work_date.strftime('%Y-%m-%d'),
                        'minutes': late_minutes
                    })
                else:
                    # 正常或早到
                    early_minutes = random.randint(-10, 5)
                    actual_checkin = base_checkin + timedelta(minutes=early_minutes)
                
                if is_early:
                    # 早退10-60分鐘
                    early_minutes = random.randint(10, 60)
                    actual_checkout = base_checkout - timedelta(minutes=early_minutes)
                    early_count += 1
                    self.test_report['employees'][employee_id]['early_leave_days'].append({
                        'date': work_date.strftime('%Y-%m-%d'),
                        'minutes': early_minutes
                    })
                else:
                    # 正常或晚退
                    late_minutes = random.randint(-5, 30)
                    actual_checkout = base_checkout + timedelta(minutes=late_minutes)
                
                # 插入上班打卡記錄
                cursor.execute("""
                    INSERT INTO punch_records 
                    (device_id, employee_id, punch_date, punch_time, punch_datetime, status_code, raw_data, processed)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 0)
                """, (
                    'WEB001',
                    employee_id,
                    work_date.strftime('%Y-%m-%d'),
                    actual_checkin.strftime('%H:%M:%S'),
                    actual_checkin.strftime('%Y-%m-%d %H:%M:%S'),
                    '0',  # 0=上班
                    f'Web打卡-{employee_id}-上班'
                ))
                
                # 插入下班打卡記錄
                cursor.execute("""
                    INSERT INTO punch_records 
                    (device_id, employee_id, punch_date, punch_time, punch_datetime, status_code, raw_data, processed)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 0)
                """, (
                    'WEB001',
                    employee_id,
                    work_date.strftime('%Y-%m-%d'),
                    actual_checkout.strftime('%H:%M:%S'),
                    actual_checkout.strftime('%Y-%m-%d %H:%M:%S'),
                    '1',  # 1=下班
                    f'Web打卡-{employee_id}-下班'
                ))
                
                punch_count += 2
                
                self.test_report['employees'][employee_id]['punch_records'].append({
                    'date': work_date.strftime('%Y-%m-%d'),
                    'checkin': actual_checkin.strftime('%H:%M'),
                    'checkout': actual_checkout.strftime('%H:%M'),
                    'is_late': is_late,
                    'is_early': is_early
                })
            
            print(f"   ✅ {employee_id}：生成 {punch_count} 筆打卡記錄（遲到{late_count}天，早退{early_count}天）")
        
        conn.commit()
        conn.close()
    
    def step3_generate_overtime_requests(self):
        """第三階段：生成加班申請"""
        print("💼 第三階段：生成加班申請...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # 獲取員工ID對應
        cursor.execute("SELECT id, employee_id FROM employees WHERE employee_id IN ('E001', 'E002', 'E003')")
        employee_mapping = {row[1]: row[0] for row in cursor.fetchall()}
        
        # 加班類型選項
        overtime_types = ['weekday', 'weekend', 'holiday']
        overtime_type_names = {
            'weekday': '平日加班',
            'weekend': '假日加班', 
            'holiday': '國定假日加班'
        }
        
        for employee_code in self.test_employees:
            if employee_code not in employee_mapping:
                print(f"   ⚠️  員工 {employee_code} 不存在於employees表中")
                continue
                
            emp_id = employee_mapping[employee_code]
            
            for i in range(5):  # 每人5次加班申請
                # 隨機選擇6月份的工作日
                overtime_date = datetime(2025, 6, random.randint(1, 28))
                while overtime_date.weekday() >= 5:  # 確保是工作日
                    overtime_date = datetime(2025, 6, random.randint(1, 28))
                
                # 隨機加班時間（1-4小時）
                start_time = time(18, 0)  # 下班後開始
                hours = random.randint(1, 4)
                end_time = time(18 + hours, 0) if 18 + hours < 24 else time(23, 59)
                
                overtime_type = random.choice(overtime_types)
                reason = f"專案趕工需要 - {overtime_type_names[overtime_type]}"
                work_content = f"系統開發與測試工作 - {overtime_type_names[overtime_type]}"
                
                cursor.execute("""
                    INSERT INTO overtime_requests 
                    (employee_id, overtime_date, start_time, end_time, overtime_hours, overtime_type, reason, work_content, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?)
                """, (
                    emp_id,
                    overtime_date.strftime('%Y-%m-%d'),
                    start_time.strftime('%H:%M'),
                    end_time.strftime('%H:%M'),
                    hours,
                    overtime_type,
                    reason,
                    work_content,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                
                self.test_report['employees'][employee_code]['overtime_requests'].append({
                    'date': overtime_date.strftime('%Y-%m-%d'),
                    'start_time': start_time.strftime('%H:%M'),
                    'end_time': end_time.strftime('%H:%M'),
                    'hours': hours,
                    'type': overtime_type_names[overtime_type],
                    'status': 'pending'
                })
            
            print(f"   ✅ {employee_code}：生成 5 筆加班申請")
        
        conn.commit()
        conn.close()
    
    def step4_generate_leave_requests(self):
        """第四階段：生成請假申請"""
        print("🏖️ 第四階段：生成請假申請...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # 獲取請假類型
        cursor.execute("SELECT id, name FROM leave_types LIMIT 5")
        leave_types = cursor.fetchall()
        
        if not leave_types:
            print("   ⚠️  未找到請假類型，創建預設類型...")
            cursor.execute("INSERT INTO leave_types (name, description, max_days) VALUES ('事假', '因私事請假', 14)")
            cursor.execute("INSERT INTO leave_types (name, description, max_days) VALUES ('病假', '因病請假', 30)")
            cursor.execute("INSERT INTO leave_types (name, description, max_days) VALUES ('特休', '特別休假', 7)")
            conn.commit()
            
            cursor.execute("SELECT id, name FROM leave_types")
            leave_types = cursor.fetchall()
        
        # 獲取員工ID對應
        cursor.execute("SELECT id, employee_id FROM employees WHERE employee_id IN ('E001', 'E002', 'E003')")
        employee_mapping = {row[1]: row[0] for row in cursor.fetchall()}
        
        for employee_id in self.test_employees:
            if employee_id not in employee_mapping:
                print(f"   ⚠️  員工 {employee_id} 不存在於employees表中")
                continue
                
            emp_id = employee_mapping[employee_id]
            
            for i in range(5):  # 每人5次請假申請
                # 隨機選擇6月份的日期
                start_date = datetime(2025, 6, random.randint(1, 25))
                
                # 隨機請假天數（1-3天）
                days = random.randint(1, 3)
                end_date = start_date + timedelta(days=days-1)
                
                leave_type = random.choice(leave_types)
                reason = f"{leave_type[1]} - 個人事務處理"
                
                # 隨機選擇代理人（從其他員工中選擇）
                substitute_options = [emp_id for emp_code, emp_id in employee_mapping.items() if emp_code != employee_id]
                substitute_id = random.choice(substitute_options) if substitute_options else None
                
                cursor.execute("""
                    INSERT INTO leaves 
                    (employee_id, leave_type, start_date, end_date, leave_hours, status, reason, substitute_id, created_at, time_type)
                    VALUES (?, ?, ?, ?, ?, 'pending', ?, ?, ?, 'full_day')
                """, (
                    emp_id,
                    leave_type[1],
                    start_date.strftime('%Y-%m-%d'),
                    end_date.strftime('%Y-%m-%d'),
                    days * 8.0,  # 假設每天8小時
                    reason,
                    substitute_id,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                
                self.test_report['employees'][employee_id]['leave_requests'].append({
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days,
                    'type': leave_type[1],
                    'substitute': substitute_id,
                    'status': 'pending'
                })
            
            print(f"   ✅ {employee_id}：生成 5 筆請假申請")
        
        conn.commit()
        conn.close()
    
    def step5_approve_requests(self):
        """第五階段：審核申請"""
        print("✅ 第五階段：審核申請...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # 審核加班申請（80%通過，20%拒絕）
        cursor.execute("SELECT id FROM overtime_requests WHERE status = 'pending'")
        overtime_ids = cursor.fetchall()
        
        approved_overtime = 0
        rejected_overtime = 0
        
        for (request_id,) in overtime_ids:
            status = 'approved' if random.random() < 0.8 else 'rejected'
            reject_reason = None if status == 'approved' else '工作量不足，不需加班'
            
            cursor.execute("""
                UPDATE overtime_requests 
                SET status = ?, reject_reason = ?, approver_id = 1, approved_at = ?
                WHERE id = ?
            """, (status, reject_reason, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), request_id))
            
            if status == 'approved':
                approved_overtime += 1
            else:
                rejected_overtime += 1
        
        # 審核請假申請（85%通過，15%拒絕）
        cursor.execute("SELECT id FROM leaves WHERE status = 'pending'")
        leave_ids = cursor.fetchall()
        
        approved_leaves = 0
        rejected_leaves = 0
        
        for (request_id,) in leave_ids:
            status = 'approved' if random.random() < 0.85 else 'rejected'
            comment = '核准' if status == 'approved' else '工作繁忙，建議延後'
            
            cursor.execute("""
                UPDATE leaves 
                SET status = ?, comment = ?, approver_id = 1, approved_at = ?
                WHERE id = ?
            """, (status, comment, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), request_id))
            
            if status == 'approved':
                approved_leaves += 1
            else:
                rejected_leaves += 1
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 加班申請：核准 {approved_overtime} 筆，拒絕 {rejected_overtime} 筆")
        print(f"   ✅ 請假申請：核准 {approved_leaves} 筆，拒絕 {rejected_leaves} 筆")
        
        self.test_report['approval'] = {
            'overtime_approved': approved_overtime,
            'overtime_rejected': rejected_overtime,
            'leave_approved': approved_leaves,
            'leave_rejected': rejected_leaves
        }
    
    def step6_generate_attendance_records(self):
        """第六階段：生成考勤記錄"""
        print("📊 第六階段：生成考勤記錄...")
        
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # 獲取員工ID對應
        cursor.execute("SELECT id, employee_id FROM employees WHERE employee_id IN ('E001', 'E002', 'E003')")
        employee_mapping = {row[1]: row[0] for row in cursor.fetchall()}
        
        # 獲取6月份工作日
        june_workdays = []
        start_date = datetime(2025, 6, 1)
        end_date = datetime(2025, 6, 30)
        
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 週一到週五
                june_workdays.append(current_date.date())
            current_date += timedelta(days=1)
        
        for employee_code in self.test_employees:
            if employee_code not in employee_mapping:
                continue
                
            emp_id = employee_mapping[employee_code]
            
            for work_date in june_workdays:
                # 獲取當天的打卡記錄
                cursor.execute("""
                    SELECT punch_datetime, status_code 
                    FROM punch_records 
                    WHERE employee_id = ? AND punch_date = ?
                    ORDER BY punch_datetime
                """, (employee_code, work_date.strftime('%Y-%m-%d')))
                
                punches = cursor.fetchall()
                
                if len(punches) >= 2:
                    checkin_time = punches[0][0]
                    checkout_time = punches[-1][0]
                    
                    # 計算工作時數（簡化計算）
                    checkin_dt = datetime.strptime(checkin_time, '%Y-%m-%d %H:%M:%S')
                    checkout_dt = datetime.strptime(checkout_time, '%Y-%m-%d %H:%M:%S')
                    work_hours = (checkout_dt - checkin_dt).total_seconds() / 3600 - 1  # 扣除1小時午休
                    
                    # 計算遲到早退（簡化計算）
                    standard_checkin = datetime.combine(work_date, time(9, 0))
                    standard_checkout = datetime.combine(work_date, time(18, 0))
                    
                    late_minutes = max(0, (checkin_dt - standard_checkin).total_seconds() / 60)
                    early_minutes = max(0, (standard_checkout - checkout_dt).total_seconds() / 60)
                    
                    # 判斷狀態
                    status = 'normal'
                    if late_minutes > 0:
                        status = 'late'
                    if early_minutes > 0:
                        status = 'early_leave'
                    
                    # 插入考勤記錄
                    cursor.execute("""
                        INSERT INTO attendance 
                        (employee_id, work_date, check_in, check_out, work_hours, 
                         late_minutes, early_leave_minutes, status, shift_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                    """, (
                        emp_id,
                        work_date.strftime('%Y-%m-%d'),
                        checkin_time,
                        checkout_time,
                        round(work_hours, 2),
                        round(late_minutes),
                        round(early_minutes),
                        status
                    ))
        
        conn.commit()
        conn.close()
        
        print("   ✅ 考勤記錄生成完成")
    
    def step7_generate_report(self):
        """第七階段：生成測試報告"""
        print("📋 第七階段：生成測試報告...")
        
        # 統計數據
        total_late_days = sum(len(emp_data['late_days']) for emp_data in self.test_report['employees'].values())
        total_early_days = sum(len(emp_data['early_leave_days']) for emp_data in self.test_report['employees'].values())
        total_overtime = sum(len(emp_data['overtime_requests']) for emp_data in self.test_report['employees'].values())
        total_leaves = sum(len(emp_data['leave_requests']) for emp_data in self.test_report['employees'].values())
        
        self.test_report['summary'] = {
            'total_employees': len(self.test_employees),
            'total_late_days': total_late_days,
            'total_early_days': total_early_days,
            'total_overtime_requests': total_overtime,
            'total_leave_requests': total_leaves
        }
        
        # 保存報告到文件
        report_filename = f"COMPLETE_SYSTEM_TEST_REPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_report, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 測試報告已保存：{report_filename}")
        
        # 顯示摘要
        print("\n" + "="*60)
        print("🎉 完整系統測試完成！")
        print("="*60)
        print(f"📊 測試摘要：")
        print(f"   👥 測試員工：{len(self.test_employees)} 人")
        print(f"   ⏰ 遲到天數：{total_late_days} 天")
        print(f"   🏃 早退天數：{total_early_days} 天")
        print(f"   💼 加班申請：{total_overtime} 筆")
        print(f"   🏖️ 請假申請：{total_leaves} 筆")
        
        if 'approval' in self.test_report:
            print(f"   ✅ 加班核准：{self.test_report['approval']['overtime_approved']} 筆")
            print(f"   ❌ 加班拒絕：{self.test_report['approval']['overtime_rejected']} 筆")
            print(f"   ✅ 請假核准：{self.test_report['approval']['leave_approved']} 筆")
            print(f"   ❌ 請假拒絕：{self.test_report['approval']['leave_rejected']} 筆")
        
        return report_filename
    
    def run_complete_test(self):
        """執行完整測試流程"""
        print("🚀 開始執行完整系統測試...")
        print("="*60)
        
        try:
            self.step1_cleanup_june_data()
            self.step2_generate_punch_records()
            self.step3_generate_overtime_requests()
            self.step4_generate_leave_requests()
            self.step5_approve_requests()
            self.step6_generate_attendance_records()
            report_file = self.step7_generate_report()
            
            return report_file
            
        except Exception as e:
            print(f"❌ 測試過程中發生錯誤：{e}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    # 執行完整系統測試
    tester = CompleteSystemTest()
    report_file = tester.run_complete_test()
    
    if report_file:
        print(f"\n📄 詳細報告請查看：{report_file}")
    else:
        print("\n❌ 測試失敗，請檢查錯誤信息") 