"""
資料庫初始化和管理模組。

此模組負責：
- SQLite 資料庫的初始化
- 資料表的創建
- 基本的資料庫操作函數

⚠️ 重要提醒：
開發前請先查閱 DATABASE_SCHEMA.md 文檔，確認所有表格欄位名稱和資料類型。
該文檔包含完整的資料庫結構說明、關聯關係和查詢範例。

📋 資料庫結構文檔: DATABASE_SCHEMA.md
"""

import os

import sqlite3
from datetime import datetime
from sqlite3 import Error


def create_connection():
    """
    創建與 SQLite 資料庫的連接。

    返回：
    sqlite3.Connection: 資料庫連接物件
    """
    conn = None
    try:
        conn = sqlite3.connect("../attendance.db")
        conn.execute("PRAGMA foreign_keys = ON")  # 啟用外鍵約束
        return conn
    except Error as e:
        print(f"資料庫連接錯誤: {e}")
    return conn


def create_table(conn, create_table_sql):
    """
    在資料庫中創建表格。

    參數：
    conn (sqlite3.Connection): 資料庫連接物件
    create_table_sql (str): 創建表格的 SQL 語句

    返回：
    bool: 是否成功創建表格
    """
    try:
        c = conn.cursor()
        c.execute(create_table_sql)
        return True
    except Error as e:
        print(f"創建表格錯誤: {e}")
        return False


def create_index(conn, create_index_sql):
    """
    在資料庫中創建索引。

    參數：
    conn (sqlite3.Connection): 資料庫連接物件
    create_index_sql (str): 創建索引的 SQL 語句

    返回：
    bool: 是否成功創建索引
    """
    try:
        c = conn.cursor()
        c.execute(create_index_sql)
        return True
    except Error as e:
        print(f"創建索引錯誤: {e}")
        return False


def init_db():
    """
    初始化資料庫，創建所需的所有表格。

    返回：
    bool: 是否成功初始化資料庫
    """
    # 部門表
    sql_create_departments = """
    CREATE TABLE IF NOT EXISTS departments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        manager_id INTEGER,
        description TEXT,
        permission_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES employees (id)
    );"""

    # 員工資料表
    sql_create_employees = """
    CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        employee_id TEXT UNIQUE NOT NULL,
        department_id INTEGER NOT NULL,
        position TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        password TEXT,  -- 登錄密碼
        hire_date DATE,  -- 入職日期
        status TEXT DEFAULT 'active',  -- 員工狀態: active, inactive, trial
        salary_level TEXT,  -- 薪資等級
        id_number TEXT,  -- 身分證號
        address TEXT,  -- 聯絡地址
        emergency_contact TEXT,  -- 緊急聯絡人
        emergency_phone TEXT,  -- 緊急聯絡電話
        role_id INTEGER,
        manager_id INTEGER,  -- 直屬主管ID
        photo_url TEXT,  -- 員工照片連結
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments (id),
        FOREIGN KEY (role_id) REFERENCES permissions (id),
        FOREIGN KEY (manager_id) REFERENCES employees (id)
    );"""

    # 打卡原始資料表（從打卡機匯入的原始資料）
    sql_create_punch_records = """
    CREATE TABLE IF NOT EXISTS punch_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id TEXT NOT NULL,           -- 打卡機編號/設備ID
        employee_id TEXT NOT NULL,         -- 員工編號（來自打卡機的原始員工編號）
        punch_date DATE NOT NULL,          -- 打卡日期
        punch_time TIME NOT NULL,          -- 打卡時間
        punch_datetime TIMESTAMP NOT NULL, -- 完整的打卡日期時間
        status_code TEXT NOT NULL,         -- 打卡狀態碼（0=上班, 1=下班, 2=外出等）
        raw_data TEXT,                     -- 原始匯入資料（保留完整的原始記錄）
        processed BOOLEAN DEFAULT 0,      -- 是否已處理成考勤記錄（0=未處理, 1=已處理）
        attendance_id INTEGER,             -- 關聯的考勤記錄ID（處理後填入）
        note TEXT,                         -- 備註
        imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 匯入時間
        processed_at TIMESTAMP,            -- 處理時間
        FOREIGN KEY (attendance_id) REFERENCES attendance (id)
    );"""

    # 考勤記錄表
    sql_create_attendance = """
    CREATE TABLE IF NOT EXISTS attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        check_in TIMESTAMP,
        check_out TIMESTAMP,
        status TEXT NOT NULL,  -- normal, late, early_leave, absent, manual
        device_id TEXT,
        note TEXT,
        work_date DATE,  -- 工作日期，記錄考勤記錄所屬的工作日
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    );"""

    # 排班表
    sql_create_schedules = """
    CREATE TABLE IF NOT EXISTS schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        shift_date DATE NOT NULL,
        shift_id INTEGER NOT NULL,  -- 關聯到班別表
        status TEXT DEFAULT 'scheduled',  -- scheduled, completed, cancelled
        actual_start_time TIME,  -- 實際上班時間
        actual_end_time TIME,    -- 實際下班時間
        overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 加班時數
        pre_overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 上班前加班時數
        post_overtime_hours DECIMAL(4,2) DEFAULT 0,  -- 下班後加班時數
        break_time_minutes INTEGER DEFAULT 0,  -- 實際休息時間（分鐘）
        note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (shift_id) REFERENCES shifts (id)
    );"""

    # 班別定義表
    sql_create_shifts = """
    CREATE TABLE IF NOT EXISTS shifts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 班別名稱：早班、晚班、夜班、彈性班等
        code TEXT NOT NULL UNIQUE,  -- 班別代碼：MORNING, EVENING, NIGHT, FLEX等
        start_time TIME NOT NULL,   -- 上班時間
        end_time TIME NOT NULL,     -- 下班時間
        break_start_time TIME,      -- 休息開始時間
        break_duration_minutes INTEGER DEFAULT 60,  -- 休息時間長度（分鐘）
        pre_overtime_threshold_minutes INTEGER DEFAULT 0,  -- 上班前加班門檻（分鐘）
        post_overtime_threshold_minutes INTEGER DEFAULT 0,  -- 下班後加班門檻（分鐘）
        enable_pre_overtime BOOLEAN DEFAULT 0,   -- 是否啟用上班前加班
        enable_post_overtime BOOLEAN DEFAULT 0,  -- 是否啟用下班後加班
        auto_calculate_overtime BOOLEAN DEFAULT 1,  -- 是否自動計算加班
        color_code TEXT DEFAULT '#3B82F6',  -- 班別顏色代碼
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 排班模板表
    sql_create_schedule_templates = """
    CREATE TABLE IF NOT EXISTS schedule_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,  -- 模板名稱：標準週班表、輪班模式A等
        description TEXT,
        template_data TEXT NOT NULL,  -- JSON格式的模板資料
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 排班規則表（擴展）
    sql_create_schedule_rules = """
    CREATE TABLE IF NOT EXISTS schedule_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        rule_type TEXT NOT NULL,  -- shift_interval, max_continuous_days, overtime_limit等
        rule_value TEXT NOT NULL,
        department_id INTEGER,  -- 部門特定規則（NULL表示全公司適用）
        shift_id INTEGER,       -- 班別特定規則（NULL表示所有班別適用）
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments (id),
        FOREIGN KEY (shift_id) REFERENCES shifts (id)
    );"""

    # 請假記錄表
    sql_create_leaves = """
    CREATE TABLE IF NOT EXISTS leaves (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        leave_hours FLOAT,  -- 新增：請假時數（小時制，允許NULL）
        status TEXT NOT NULL,  -- pending, approved, rejected
        reason TEXT,
        comment TEXT,
        substitute_id INTEGER,  -- 代理人ID
        approver_id INTEGER,    -- 審核主管ID
        emergency_contact TEXT, -- 緊急聯絡方式
        approved_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (substitute_id) REFERENCES employees (id),
        FOREIGN KEY (approver_id) REFERENCES employees (id)
    );"""

    # 權限表
    sql_create_permissions = """
    CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        role_name TEXT NOT NULL,
        permission_level INTEGER NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 通知規則表
    sql_create_notification_rules = """
    CREATE TABLE IF NOT EXISTS notification_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event_type TEXT NOT NULL,  -- late, absent, leave_request, etc.
        notification_type TEXT NOT NULL,  -- email, sms, app
        recipients TEXT NOT NULL,  -- JSON array of recipient IDs or roles
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 通知記錄表
    sql_create_notifications = """
    CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        recipients TEXT NOT NULL,  -- JSON array
        message TEXT NOT NULL,
        status TEXT NOT NULL,  -- pending, sent, failed
        sent_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 打卡狀態類型表
    sql_create_clock_status_types = """
    CREATE TABLE IF NOT EXISTS clock_status_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        status_code TEXT NOT NULL UNIQUE,  -- 狀態代碼：0, 1, 2, 3, 4等
        status_name TEXT NOT NULL,         -- 狀態名稱：上班, 下班, 外出等
        description TEXT,                  -- 狀態描述
        sort_order INTEGER DEFAULT 0,     -- 排序順序
        is_active BOOLEAN DEFAULT 1,      -- 是否啟用
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 系統設定表
    sql_create_system_settings = """
    CREATE TABLE IF NOT EXISTS system_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,  -- basic, attendance_rules, notifications, etc.
        setting_key TEXT NOT NULL,
        setting_value TEXT NOT NULL,
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(category, setting_key)
    );"""

    # 薪資等級基本資料表
    sql_create_salary_grades = """
    CREATE TABLE IF NOT EXISTS salary_grades (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 薪資等級名稱：初級、中級、高級、資深等
        grade_code TEXT NOT NULL UNIQUE,  -- 等級代碼：junior, intermediate, senior, expert等
        level_order INTEGER NOT NULL,  -- 等級順序，數字越大等級越高
        min_salary DECIMAL(10,2) NOT NULL,  -- 最低薪資
        max_salary DECIMAL(10,2) NOT NULL,  -- 最高薪資
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 學歷基本資料表
    sql_create_education_levels = """
    CREATE TABLE IF NOT EXISTS education_levels (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 學歷名稱：高中、專科、大學、碩士、博士等
        level_order INTEGER NOT NULL,  -- 學歷等級順序，數字越大等級越高
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 職位基本資料表
    sql_create_positions = """
    CREATE TABLE IF NOT EXISTS positions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 職位名稱：經理、主管、專員、助理等
        department_id INTEGER,  -- 所屬部門（可選）
        level_order INTEGER NOT NULL,  -- 職位等級順序
        salary_range_min DECIMAL(10,2),  -- 薪資範圍最低
        salary_range_max DECIMAL(10,2),  -- 薪資範圍最高
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments (id)
    );"""

    # 假別基本資料表
    sql_create_leave_types = """
    CREATE TABLE IF NOT EXISTS leave_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 假別名稱：年假、病假、事假、產假等
        code TEXT NOT NULL UNIQUE,  -- 假別代碼：annual, sick, personal, maternity等
        max_days_per_year INTEGER,  -- 每年最大天數（NULL表示無限制）
        is_paid BOOLEAN DEFAULT 1,  -- 是否為有薪假
        requires_approval BOOLEAN DEFAULT 1,  -- 是否需要審核
        advance_notice_days INTEGER DEFAULT 1,  -- 需要提前申請天數
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 工作地點基本資料表
    sql_create_work_locations = """
    CREATE TABLE IF NOT EXISTS work_locations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 地點名稱：總公司、分公司A、遠端工作等
        address TEXT,  -- 詳細地址
        city TEXT,  -- 城市
        country TEXT DEFAULT '台灣',  -- 國家
        timezone TEXT DEFAULT 'Asia/Taipei',  -- 時區
        is_remote BOOLEAN DEFAULT 0,  -- 是否為遠端工作地點
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 技能基本資料表
    sql_create_skills = """
    CREATE TABLE IF NOT EXISTS skills (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,  -- 技能名稱：程式設計、專案管理、語言能力等
        category TEXT,  -- 技能分類：技術、管理、語言、證照等
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );"""

    # 建立資料庫連接
    conn = create_connection()

    if conn is not None:
        # 創建所有表格
        tables = [
            sql_create_departments,  # 1. 先創建部門表
            sql_create_permissions,  # 2. 再創建權限表
            sql_create_shifts,  # 3. 班別定義表（需要在排班表之前）
            sql_create_employees,  # 4. 然後是員工表（依賴部門表和權限表）
            sql_create_punch_records,  # 5. 打卡原始資料表
            sql_create_attendance,  # 6. 考勤記錄表
            sql_create_schedules,  # 7. 排班表（依賴員工表和班別表）
            sql_create_leaves,  # 8. 請假記錄表
            sql_create_schedule_rules,  # 9. 排班規則表
            sql_create_schedule_templates,  # 10. 排班模板表
            sql_create_notification_rules,  # 11. 通知規則表
            sql_create_notifications,  # 12. 通知記錄表
            sql_create_clock_status_types,  # 13. 打卡狀態類型表
            sql_create_salary_grades,  # 14. 薪資等級基本資料表
            sql_create_education_levels,  # 15. 學歷基本資料表
            sql_create_positions,  # 16. 職位基本資料表
            sql_create_leave_types,  # 17. 假別基本資料表
            sql_create_work_locations,  # 18. 工作地點基本資料表
            sql_create_skills,  # 19. 技能基本資料表
            sql_create_system_settings,  # 20. 系統設定表
        ]

        for table in tables:
            if not create_table(conn, table):
                return False

        # 創建索引以優化查詢效能
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department_id);",
            "CREATE INDEX IF NOT EXISTS idx_employees_role ON employees(role_id);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_employee ON punch_records(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_date ON punch_records(punch_date);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_datetime ON punch_records(punch_datetime);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_device ON punch_records(device_id);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_processed ON punch_records(processed);",
            "CREATE INDEX IF NOT EXISTS idx_punch_records_status ON punch_records(status_code);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_employee ON attendance(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(check_in);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status);",
            "CREATE INDEX IF NOT EXISTS idx_schedules_employee_date ON schedules(employee_id, shift_date);",
            "CREATE INDEX IF NOT EXISTS idx_schedules_date ON schedules(shift_date);",
            "CREATE INDEX IF NOT EXISTS idx_shifts_active ON shifts(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_shifts_code ON shifts(code);",
            "CREATE INDEX IF NOT EXISTS idx_leaves_employee ON leaves(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_leaves_status ON leaves(status);",
            "CREATE INDEX IF NOT EXISTS idx_leaves_date ON leaves(start_date);",
            "CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);",
        ]

        for index in indexes:
            if not create_index(conn, index):
                print(f"警告: 索引創建失敗 - {index}")

        # 插入預設資料
        try:
            # 插入預設部門
            conn.execute(
                """
                INSERT OR IGNORE INTO departments (name, description)
                VALUES 
                    ('管理部', '負責公司整體管理'),
                    ('人資部', '負責人力資源管理'),
                    ('業務部', '負責業務開發'),
                    ('技術部', '負責技術研發')
            """
            )

            # 插入預設權限角色
            conn.execute(
                """
                INSERT OR IGNORE INTO permissions (role_name, permission_level, description)
                VALUES 
                    ('系統管理員', 99, '最高權限'),
                    ('部門主管', 50, '部門管理權限'),
                    ('一般員工', 10, '基本權限')
            """
            )

            # 插入預設排班規則
            conn.execute(
                """
                INSERT OR IGNORE INTO schedule_rules (rule_type, rule_value, description)
                VALUES 
                    ('min_rest_hours', '8', '兩個班次之間最少休息時間（小時）'),
                    ('max_weekly_hours', '40', '每週最大工作時數'),
                    ('max_monthly_overtime', '46', '每月最大加班時數'),
                    ('overtime_rate_normal', '1.34', '平日加班費率'),
                    ('overtime_rate_holiday', '2.0', '假日加班費率'),
                    ('late_tolerance_minutes', '10', '遲到容忍時間（分鐘）'),
                    ('early_leave_tolerance_minutes', '10', '早退容忍時間（分鐘）'),
                    ('auto_clock_out_hours', '12', '自動下班打卡時間（小時後）'),
                    ('break_time_deduction', '1', '是否扣除休息時間（1=是，0=否）'),
                    ('weekend_overtime_auto', '1', '週末是否自動計算加班（1=是，0=否）'),
                    ('shift_interval', '12', '兩班次間最少間隔小時數'),
                    ('max_continuous_days', '6', '最大連續工作天數'),
                    ('day_change_time', '06:00', '每日換日時間（此時間前的打卡歸屬前一日）'),
                    ('cross_day_attendance', '1', '是否啟用跨日考勤處理（1=是，0=否）'),
                    ('first_punch_as_checkin', '1', '當日第一筆打卡作為上班（1=是，0=否）'),
                    ('last_punch_as_checkout', '1', '當日最後一筆打卡作為下班（1=是，0=否）')
            """
            )

            # 插入預設班別資料
            conn.execute(
                """
                INSERT OR IGNORE INTO shifts (name, code, start_time, end_time, break_start_time, break_duration_minutes, 
                                            pre_overtime_threshold_minutes, post_overtime_threshold_minutes, 
                                            enable_pre_overtime, enable_post_overtime, color_code, description)
                VALUES 
                    ('標準日班', 'STANDARD_DAY', '08:30', '17:30', '12:00', 60, 60, 30, 1, 1, '#3B82F6', '標準朝九晚五工作時間'),
                    ('早班', 'MORNING', '06:00', '14:00', '10:00', 30, 60, 30, 1, 1, '#F59E0B', '早班工作時間'),
                    ('晚班', 'EVENING', '14:00', '22:00', '18:00', 60, 30, 30, 1, 1, '#8B5CF6', '晚班工作時間'),
                    ('夜班', 'NIGHT', '22:00', '06:00', '02:00', 30, 30, 30, 1, 1, '#1F2937', '夜班工作時間'),
                    ('彈性班', 'FLEXIBLE', '09:00', '18:00', '12:30', 60, 0, 0, 0, 0, '#10B981', '彈性工作時間，不計算加班'),
                    ('半日班', 'HALF_DAY', '09:00', '13:00', NULL, 0, 0, 0, 0, 0, '#EF4444', '半日工作時間')
            """
            )

            # 插入預設通知規則
            conn.execute(
                """
                INSERT OR IGNORE INTO notification_rules (event_type, notification_type, recipients)
                VALUES 
                    ('late', 'email', '["department_manager","employee"]'),
                    ('absent', 'email', '["department_manager","hr_manager","employee"]'),
                    ('leave_request', 'app', '["department_manager"]')
            """
            )

            # 插入預設學歷資料
            conn.execute(
                """
                INSERT OR IGNORE INTO education_levels (name, level_order, description)
                VALUES 
                    ('國中', 1, '國民中學'),
                    ('高中職', 2, '高級中學或職業學校'),
                    ('專科', 3, '專科學校'),
                    ('大學', 4, '大學學士學位'),
                    ('碩士', 5, '碩士學位'),
                    ('博士', 6, '博士學位')
            """
            )

            # 插入預設職位資料
            conn.execute(
                """
                INSERT OR IGNORE INTO positions (name, level_order, description)
                VALUES 
                    ('實習生', 1, '實習職位'),
                    ('助理', 2, '助理職位'),
                    ('專員', 3, '專員職位'),
                    ('資深專員', 4, '資深專員職位'),
                    ('主任', 5, '主任職位'),
                    ('副理', 6, '副理職位'),
                    ('經理', 7, '經理職位'),
                    ('資深經理', 8, '資深經理職位'),
                    ('總監', 9, '總監職位'),
                    ('副總', 10, '副總經理'),
                    ('總經理', 11, '總經理')
            """
            )

            # 插入預設假別資料
            conn.execute(
                """
                INSERT OR IGNORE INTO leave_types (name, code, max_days_per_year, is_paid, requires_approval, advance_notice_days, description)
                VALUES 
                    ('年假', 'annual', 14, 1, 1, 3, '年度特別休假'),
                    ('病假', 'sick', 30, 1, 0, 0, '因病請假'),
                    ('事假', 'personal', NULL, 0, 1, 1, '因私事請假'),
                    ('產假', 'maternity', 56, 1, 1, 30, '產前產後假'),
                    ('陪產假', 'paternity', 5, 1, 1, 7, '陪產檢及陪產假'),
                    ('喪假', 'bereavement', 8, 1, 0, 0, '家屬喪葬假'),
                    ('婚假', 'marriage', 8, 1, 1, 14, '結婚假'),
                    ('公假', 'official', NULL, 1, 1, 1, '因公請假'),
                    ('補休', 'compensatory', NULL, 1, 1, 1, '加班補休')
            """
            )

            # 插入預設打卡狀態類型資料
            conn.execute(
                """
                INSERT OR IGNORE INTO clock_status_types (status_code, status_name, description)
                VALUES 
                    ('0', '上班', '員工已經打卡上班'),
                    ('1', '下班', '員工已經打卡下班'),
                    ('2', '外出', '員工正在外出'),
                    ('3', '請假', '員工正在請假'),
                    ('4', '休假', '員工正在休假')
            """
            )

            # 插入預設薪資等級資料
            conn.execute(
                """
                INSERT OR IGNORE INTO salary_grades (name, grade_code, level_order, min_salary, max_salary, description)
                VALUES 
                    ('初級', 'junior', 1, 28000, 35000, '初級職位薪資等級'),
                    ('中級', 'intermediate', 2, 35000, 50000, '中級職位薪資等級'),
                    ('高級', 'senior', 3, 50000, 70000, '高級職位薪資等級'),
                    ('資深', 'expert', 4, 70000, 100000, '資深職位薪資等級'),
                    ('主管', 'manager', 5, 80000, 120000, '主管職位薪資等級'),
                    ('高階主管', 'executive', 6, 120000, 200000, '高階主管薪資等級')
            """
            )

            # 插入預設工作地點資料
            conn.execute(
                """
                INSERT OR IGNORE INTO work_locations (name, address, city, country, is_remote, description)
                VALUES 
                    ('總公司', '台北市信義區信義路五段7號', '台北市', '台灣', 0, '公司總部'),
                    ('台中分公司', '台中市西屯區台灣大道三段99號', '台中市', '台灣', 0, '台中營運據點'),
                    ('高雄分公司', '高雄市前鎮區成功二路88號', '高雄市', '台灣', 0, '高雄營運據點'),
                    ('遠端工作', NULL, NULL, '台灣', 1, '居家辦公或遠端工作')
            """
            )

            # 插入預設技能資料
            conn.execute(
                """
                INSERT OR IGNORE INTO skills (name, category, description)
                VALUES 
                    ('程式設計', '技術', '軟體開發技能'),
                    ('專案管理', '管理', '專案規劃與執行能力'),
                    ('英語', '語言', '英語溝通能力'),
                    ('日語', '語言', '日語溝通能力'),
                    ('資料分析', '技術', '數據分析與統計能力'),
                    ('團隊領導', '管理', '團隊管理與領導能力'),
                    ('客戶服務', '業務', '客戶關係維護能力'),
                    ('財務分析', '財務', '財務報表分析能力'),
                    ('行銷企劃', '行銷', '市場行銷策略規劃'),
                    ('人力資源', '人資', '人力資源管理專業')
            """
            )

            # 插入預設員工狀態設定
            conn.execute(
                """
                INSERT OR IGNORE INTO system_settings (category, setting_key, setting_value, description)
                VALUES 
                    ('employee_status', 'active', '在職', '正常在職狀態'),
                    ('employee_status', 'trial', '試用期', '試用期員工'),
                    ('employee_status', 'inactive', '停職', '暫時停職狀態'),
                    ('employee_status', 'leave', '離職', '已離職員工')
            """
            )

            conn.commit()

        except Error as e:
            print(f"插入預設資料錯誤: {e}")
            return False

        conn.close()
        return True
    return False


def import_punch_data(punch_data_list):
    """
    匯入打卡原始資料到資料庫
    
    參數：
    punch_data_list (list): 打卡資料列表，每個元素包含：
        - device_id: 打卡機編號
        - employee_id: 員工編號
        - punch_date: 打卡日期 (YYYY-MM-DD)
        - punch_time: 打卡時間 (HH:MM:SS)
        - status_code: 打卡狀態碼
        - raw_data: 原始資料（可選）
    
    返回：
    tuple: (成功匯入筆數, 錯誤訊息列表)
    """
    conn = create_connection()
    if not conn:
        return 0, ["無法連接到資料庫"]
    
    success_count = 0
    errors = []
    
    try:
        cursor = conn.cursor()
        
        for i, punch_data in enumerate(punch_data_list):
            try:
                # 組合完整的打卡日期時間
                punch_datetime = f"{punch_data['punch_date']} {punch_data['punch_time']}"
                
                # 插入打卡記錄
                cursor.execute("""
                    INSERT INTO punch_records 
                    (device_id, employee_id, punch_date, punch_time, punch_datetime, 
                     status_code, raw_data, note)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    punch_data['device_id'],
                    punch_data['employee_id'],
                    punch_data['punch_date'],
                    punch_data['punch_time'],
                    punch_datetime,
                    punch_data['status_code'],
                    punch_data.get('raw_data', ''),
                    punch_data.get('note', '')
                ))
                
                success_count += 1
                
            except Exception as e:
                errors.append(f"第 {i+1} 筆資料匯入失敗: {str(e)}")
        
        conn.commit()
        
    except Exception as e:
        errors.append(f"資料庫操作失敗: {str(e)}")
    finally:
        conn.close()
    
    return success_count, errors


def insert_sample_punch_data():
    """
    插入示例打卡資料
    """
    sample_data = [
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP001',
            'punch_date': '2024-06-05',
            'punch_time': '08:25:30',
            'status_code': '0',  # 上班
            'raw_data': 'DEVICE001,EMP001,2024-06-05,08:25:30,0',
            'note': '正常上班打卡'
        },
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP001',
            'punch_date': '2024-06-05',
            'punch_time': '17:35:15',
            'status_code': '1',  # 下班
            'raw_data': 'DEVICE001,EMP001,2024-06-05,17:35:15,1',
            'note': '正常下班打卡'
        },
        {
            'device_id': 'DEVICE002',
            'employee_id': 'EMP002',
            'punch_date': '2024-06-05',
            'punch_time': '08:45:20',
            'status_code': '0',  # 上班（遲到）
            'raw_data': 'DEVICE002,EMP002,2024-06-05,08:45:20,0',
            'note': '遲到上班打卡'
        },
        {
            'device_id': 'DEVICE002',
            'employee_id': 'EMP002',
            'punch_date': '2024-06-05',
            'punch_time': '17:30:45',
            'status_code': '1',  # 下班
            'raw_data': 'DEVICE002,EMP002,2024-06-05,17:30:45,1',
            'note': '正常下班打卡'
        },
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP003',
            'punch_date': '2024-06-05',
            'punch_time': '08:30:10',
            'status_code': '0',  # 上班
            'raw_data': 'DEVICE001,EMP003,2024-06-05,08:30:10,0',
            'note': '正常上班打卡'
        },
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP003',
            'punch_date': '2024-06-05',
            'punch_time': '12:00:00',
            'status_code': '2',  # 外出
            'raw_data': 'DEVICE001,EMP003,2024-06-05,12:00:00,2',
            'note': '午餐外出打卡'
        },
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP003',
            'punch_date': '2024-06-05',
            'punch_time': '13:00:30',
            'status_code': '0',  # 回來上班
            'raw_data': 'DEVICE001,EMP003,2024-06-05,13:00:30,0',
            'note': '午餐回來打卡'
        },
        {
            'device_id': 'DEVICE001',
            'employee_id': 'EMP003',
            'punch_date': '2024-06-05',
            'punch_time': '17:28:45',
            'status_code': '1',  # 下班
            'raw_data': 'DEVICE001,EMP003,2024-06-05,17:28:45,1',
            'note': '正常下班打卡'
        }
    ]
    
    success_count, errors = import_punch_data(sample_data)
    
    if errors:
        print(f"示例打卡資料匯入完成，成功: {success_count} 筆")
        for error in errors:
            print(f"錯誤: {error}")
    else:
        print(f"示例打卡資料匯入成功，共 {success_count} 筆")
    
    return success_count, errors


if __name__ == "__main__":
    if init_db():
        print("資料庫初始化成功！")
    else:
        print("資料庫初始化失敗！")
