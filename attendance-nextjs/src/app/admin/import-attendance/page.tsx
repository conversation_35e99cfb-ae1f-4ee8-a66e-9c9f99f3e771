'use client'

import React, { useState, useRef, useCallback } from 'react'
import Link from 'next/link'
import {
    ArrowLeft,
    Upload,
    FileText,
    X,
    CheckCircle,
    XCircle,
    Info,
    Clock,
    Users,
    AlertTriangle,
    Download
} from 'lucide-react'

// 🔧 介面定義
interface ImportResult {
    success: boolean
    imported?: number
    skipped?: number
    errors?: number
    total?: number
    created_employees?: number
    message?: string
    error?: string
}

interface ImportOptions {
    overwrite: boolean
    auto_create_employee: boolean
}

interface FileInfo {
    name: string
    size: number
}

interface ImportStatistics {
    successCount: number
    skippedCount: number
    errorCount: number
    newEmployeeCount: number
    totalCount: number
}

export default function ImportAttendancePage() {
    // 🎯 狀態管理
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [fileInfo, setFileInfo] = useState<FileInfo | null>(null)
    const [importOptions, setImportOptions] = useState<ImportOptions>({
        overwrite: false,
        auto_create_employee: true
    })
    const [isImporting, setIsImporting] = useState(false)
    const [importResult, setImportResult] = useState<ImportResult | null>(null)
    const [statistics, setStatistics] = useState<ImportStatistics>({
        successCount: 0,
        skippedCount: 0,
        errorCount: 0,
        newEmployeeCount: 0,
        totalCount: 0
    })
    const [isDragOver, setIsDragOver] = useState(false)

    const fileInputRef = useRef<HTMLInputElement>(null)

    // 🔧 檔案處理函數
    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const handleFileSelect = (file: File) => {
        // 檢查檔案類型
        if (!file.name.toLowerCase().endsWith('.txt')) {
            alert('請選擇 .txt 格式的檔案')
            return
        }

        // 檢查檔案大小（限制 10MB）
        if (file.size > 10 * 1024 * 1024) {
            alert('檔案大小不能超過 10MB')
            return
        }

        setSelectedFile(file)
        setFileInfo({
            name: file.name,
            size: file.size
        })
        setImportResult(null) // 清除之前的結果
    }

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files
        if (files && files.length > 0) {
            handleFileSelect(files[0])
        }
    }

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(true)
    }, [])

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)
    }, [])

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)

        const files = e.dataTransfer.files
        if (files.length > 0) {
            handleFileSelect(files[0])
        }
    }, [])

    const removeFile = () => {
        setSelectedFile(null)
        setFileInfo(null)
        setImportResult(null)
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
        }
    }

    // 🔧 匯入處理函數
    const handleImport = async () => {
        if (!selectedFile) {
            alert('請先選擇檔案')
            return
        }

        setIsImporting(true)
        setImportResult(null)

        try {
            const formData = new FormData()
            formData.append('file', selectedFile)
            formData.append('overwrite', importOptions.overwrite.toString())
            formData.append('auto_create_employee', importOptions.auto_create_employee.toString())

            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/attendance/import-text`, {
                method: 'POST',
                body: formData
            })

            const result: ImportResult = await response.json()

            if (response.ok && result.success) {
                setImportResult(result)
                setStatistics({
                    successCount: result.imported || 0,
                    skippedCount: result.skipped || 0,
                    errorCount: result.errors || 0,
                    newEmployeeCount: result.created_employees || 0,
                    totalCount: result.total || 0
                })
            } else {
                throw new Error(result.error || '匯入失敗')
            }

        } catch (error) {
            console.error('匯入錯誤:', error)
            setImportResult({
                success: false,
                error: error instanceof Error ? error.message : '匯入失敗'
            })
        } finally {
            setIsImporting(false)
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
            {/* 🔙 返回按鈕 */}
            <div className="fixed top-4 left-4 z-50">
                <Link
                    href="/admin"
                    className="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
                >
                    <ArrowLeft className="w-4 h-4" />
                    <span className="text-sm font-medium">返回管理後台</span>
                </Link>
            </div>

            {/* 📦 主容器 */}
            <div className="pt-16">
                {/* 🎨 頁面標題 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">匯入考勤資料</h1>
                            <p className="text-indigo-100 text-base font-medium">從打卡機匯出的文字檔案匯入考勤記錄</p>
                        </div>
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">資料匯入管理</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <Upload className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* 📝 主要內容區 */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* 🔧 檔案上傳區 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                                <Upload className="w-5 h-5 text-indigo-600" />
                                <span>檔案上傳</span>
                            </h3>

                            {/* 🎯 拖拽上傳區 */}
                            <div
                                className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer ${isDragOver
                                    ? 'border-indigo-500 bg-indigo-50'
                                    : 'border-gray-300 hover:border-indigo-500'
                                    }`}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                                onClick={() => fileInputRef.current?.click()}
                            >
                                <div className="flex flex-col items-center space-y-4">
                                    <div className="w-16 h-16 bg-indigo-50 rounded-full flex items-center justify-center">
                                        <Upload className="w-8 h-8 text-indigo-500" />
                                    </div>
                                    <div>
                                        <p className="text-lg font-medium text-gray-900">拖拽檔案到此處或點擊選擇</p>
                                        <p className="text-sm text-gray-500 mt-1">支援 .txt 格式的打卡資料檔案</p>
                                    </div>
                                    <button
                                        type="button"
                                        className="bg-indigo-500 text-white px-6 py-2 rounded-lg hover:bg-indigo-600 transition-colors"
                                    >
                                        選擇檔案
                                    </button>
                                </div>
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept=".txt"
                                    className="hidden"
                                    onChange={handleFileInputChange}
                                />
                            </div>

                            {/* 🔧 檔案資訊 */}
                            {fileInfo && (
                                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <FileText className="w-5 h-5 text-gray-500" />
                                            <div>
                                                <p className="font-medium text-gray-900">{fileInfo.name}</p>
                                                <p className="text-sm text-gray-500">{formatFileSize(fileInfo.size)}</p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={removeFile}
                                            className="text-red-500 hover:text-red-600 transition-colors"
                                        >
                                            <X className="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* 🔧 匯入選項 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                                <Clock className="w-5 h-5 text-indigo-600" />
                                <span>匯入選項</span>
                            </h3>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <p className="font-medium text-gray-900">覆蓋現有記錄</p>
                                        <p className="text-sm text-gray-500">如果該員工該日期已有記錄，是否覆蓋</p>
                                    </div>
                                    <label className="relative inline-flex items-center cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={importOptions.overwrite}
                                            onChange={(e) => setImportOptions(prev => ({
                                                ...prev,
                                                overwrite: e.target.checked
                                            }))}
                                            className="sr-only peer"
                                        />
                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>

                                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <p className="font-medium text-gray-900">自動創建員工</p>
                                        <p className="text-sm text-gray-500">當找不到對應員工時，自動創建新員工記錄</p>
                                    </div>
                                    <label className="relative inline-flex items-center cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={importOptions.auto_create_employee}
                                            onChange={(e) => setImportOptions(prev => ({
                                                ...prev,
                                                auto_create_employee: e.target.checked
                                            }))}
                                            className="sr-only peer"
                                        />
                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                    </label>
                                </div>
                            </div>

                            {/* 🔧 匯入按鈕 */}
                            <div className="mt-6">
                                <button
                                    onClick={handleImport}
                                    disabled={!selectedFile || isImporting}
                                    className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${!selectedFile || isImporting
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : isImporting
                                            ? 'bg-yellow-500 text-white cursor-not-allowed'
                                            : 'bg-indigo-500 text-white hover:bg-indigo-600 hover:shadow-lg'
                                        }`}
                                >
                                    {isImporting ? '匯入中...' : selectedFile ? '開始匯入' : '請先選擇檔案'}
                                </button>
                            </div>
                        </div>

                        {/* 🔧 匯入結果 */}
                        {importResult && (
                            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
                                    {importResult.success ? (
                                        <CheckCircle className="w-5 h-5 text-green-600" />
                                    ) : (
                                        <XCircle className="w-5 h-5 text-red-600" />
                                    )}
                                    <span>匯入結果</span>
                                </h3>

                                {importResult.success ? (
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2 text-green-600">
                                            <CheckCircle className="w-5 h-5" />
                                            <span className="font-medium">匯入完成！</span>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div className="bg-green-50 p-3 rounded-lg">
                                                <p className="text-green-800 font-medium">成功匯入</p>
                                                <p className="text-2xl font-bold text-green-600">{importResult.imported || 0}</p>
                                            </div>
                                            <div className="bg-yellow-50 p-3 rounded-lg">
                                                <p className="text-yellow-800 font-medium">跳過記錄</p>
                                                <p className="text-2xl font-bold text-yellow-600">{importResult.skipped || 0}</p>
                                            </div>
                                            <div className="bg-red-50 p-3 rounded-lg">
                                                <p className="text-red-800 font-medium">錯誤記錄</p>
                                                <p className="text-2xl font-bold text-red-600">{importResult.errors || 0}</p>
                                            </div>
                                            <div className="bg-blue-50 p-3 rounded-lg">
                                                <p className="text-blue-800 font-medium">新增員工</p>
                                                <p className="text-2xl font-bold text-blue-600">{importResult.created_employees || 0}</p>
                                            </div>
                                        </div>

                                        {(importResult.created_employees || 0) > 0 && (
                                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <div className="flex items-start space-x-2">
                                                    <Info className="w-5 h-5 text-blue-500 mt-0.5" />
                                                    <div>
                                                        <p className="font-medium text-blue-900">提醒</p>
                                                        <p className="text-sm text-blue-800">
                                                            已自動創建 {importResult.created_employees} 筆新員工記錄，請到員工管理頁面手動修改這些員工的姓名和其他資訊。
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2 text-red-600">
                                            <XCircle className="w-5 h-5" />
                                            <span className="font-medium">匯入失敗</span>
                                        </div>

                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                            <p className="text-red-800">{importResult.error}</p>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <p className="font-medium mb-2">可能的解決方案：</p>
                                            <ul className="list-disc list-inside space-y-1">
                                                <li>檢查檔案格式是否正確</li>
                                                <li>確認檔案編碼為 UTF-8</li>
                                                <li>檢查網路連接狀態</li>
                                                <li>聯繫系統管理員</li>
                                            </ul>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* 📝 側邊欄 */}
                    <div className="space-y-6">
                        {/* 🔧 檔案格式說明 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                <FileText className="w-5 h-5 text-indigo-600" />
                                <span>檔案格式說明</span>
                            </h3>

                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">支援格式</h4>
                                    <p className="text-sm text-gray-600">打卡機匯出的 txt 檔案，每行包含 5 個欄位，以逗號分隔</p>
                                </div>

                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">欄位說明</h4>
                                    <div className="text-sm text-gray-600 space-y-1">
                                        <p><span className="font-medium">機器編號</span>：打卡機識別碼</p>
                                        <p><span className="font-medium">員工編號</span>：員工卡號</p>
                                        <p><span className="font-medium">日期</span>：YYYYMMDD 格式</p>
                                        <p><span className="font-medium">時間</span>：HHMM 格式</p>
                                        <p><span className="font-medium">狀態</span>：打卡狀態碼</p>
                                    </div>
                                </div>

                                <div>
                                    <h4 className="font-medium text-gray-900 mb-2">範例資料</h4>
                                    <div className="bg-gray-50 rounded-lg p-3 text-xs font-mono">
                                        <div className="text-gray-600">011,00000701,20241220,0945,0</div>
                                        <div className="text-gray-600">011,00000701,20241220,1800,0</div>
                                        <div className="text-gray-600">011,00000702,20241220,0830,0</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 🔧 匯入統計 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                <Users className="w-5 h-5 text-indigo-600" />
                                <span>匯入統計</span>
                            </h3>

                            <div className="space-y-3">
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">成功匯入</span>
                                    <span className="font-medium text-green-600">{statistics.successCount || '-'}</span>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">跳過記錄</span>
                                    <span className="font-medium text-yellow-600">{statistics.skippedCount || '-'}</span>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">錯誤記錄</span>
                                    <span className="font-medium text-red-600">{statistics.errorCount || '-'}</span>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">新增員工</span>
                                    <span className="font-medium text-blue-600">{statistics.newEmployeeCount || '-'}</span>
                                </div>
                                <div className="flex items-center justify-between text-sm border-t pt-3">
                                    <span className="text-gray-600 font-medium">總計處理</span>
                                    <span className="font-medium text-gray-900">{statistics.totalCount || '-'}</span>
                                </div>
                            </div>
                        </div>

                        {/* 🔧 注意事項 */}
                        <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
                            <h3 className="text-lg font-semibold text-yellow-900 mb-4 flex items-center space-x-2">
                                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                                <span>注意事項</span>
                            </h3>

                            <div className="space-y-2 text-sm text-yellow-800">
                                <p>• 匯入前請確保檔案格式正確</p>
                                <p>• 建議先備份現有資料</p>
                                <p>• 大檔案匯入可能需要較長時間</p>
                                <p>• 新增的員工需要手動完善資訊</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 