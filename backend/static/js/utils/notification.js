/**
 * 統一通知系統
 * 整合所有通知顯示邏輯，提供一致的用戶體驗
 * 
 * <AUTHOR> AttendanceOS
 * @version v2005.6.12
 */

class NotificationSystem {

    /**
     * 通知配置
     */
    static CONFIG = {
        DURATION: {
            SUCCESS: 2000, // 成功通知顯示時間
            ERROR: 5000, // 錯誤通知顯示時間
            WARNING: 3000, // 警告通知顯示時間
            INFO: 3000 // 資訊通知顯示時間
        },
        POSITION: {
            TOP_RIGHT: 'top-4 right-4',
            TOP_LEFT: 'top-4 left-4',
            BOTTOM_RIGHT: 'bottom-4 right-4',
            BOTTOM_LEFT: 'bottom-4 left-4',
            TOP_CENTER: 'top-4 left-1/2 transform -translate-x-1/2',
            BOTTOM_CENTER: 'bottom-4 left-1/2 transform -translate-x-1/2'
        },
        MAX_NOTIFICATIONS: 5 // 最大同時顯示通知數量
    };

    /**
     * 通知類型配置
     */
    static TYPES = {
        success: {
            bgColor: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
            textColor: 'text-white',
            icon: 'check-circle',
            borderColor: 'border-emerald-400'
        },
        error: {
            bgColor: 'bg-gradient-to-r from-red-500 to-red-600',
            textColor: 'text-white',
            icon: 'x-circle',
            borderColor: 'border-red-400'
        },
        warning: {
            bgColor: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
            textColor: 'text-white',
            icon: 'alert-triangle',
            borderColor: 'border-yellow-400'
        },
        info: {
            bgColor: 'bg-gradient-to-r from-blue-500 to-blue-600',
            textColor: 'text-white',
            icon: 'info',
            borderColor: 'border-blue-400'
        }
    };

    /**
     * 當前活動的通知列表
     */
    static activeNotifications = [];

    /**
     * 顯示滑動通知（主要方法）
     * @param {string} message - 通知訊息
     * @param {string} type - 通知類型 ('success', 'error', 'warning', 'info')
     * @param {Object} options - 額外選項
     */
    static showSlideNotification(message, type = 'info', options = {}) {
        // 合併預設選項
        const config = {
            duration: this.CONFIG.DURATION[type.toUpperCase()] || this.CONFIG.DURATION.INFO,
            position: this.CONFIG.POSITION.TOP_RIGHT,
            closable: true,
            autoHide: true,
            ...options
        };

        // 檢查通知數量限制
        this.cleanupOldNotifications();

        // 創建通知元素
        const notification = this.createNotificationElement(message, type, config);

        // 添加到頁面
        document.body.appendChild(notification);

        // 記錄活動通知
        this.activeNotifications.push({
            element: notification,
            timestamp: Date.now(),
            type: type
        });

        // 重新創建圖標
        if (window.lucide) {
            lucide.createIcons();
        }

        // 滑入動畫
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
            notification.classList.add('translate-x-0');
        }, 100);

        // 自動隱藏
        if (config.autoHide) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, config.duration);
        }

        return notification;
    }

    /**
     * 創建通知元素
     * @param {string} message - 訊息內容
     * @param {string} type - 通知類型
     * @param {Object} config - 配置選項
     * @returns {HTMLElement} 通知元素
     */
    static createNotificationElement(message, type, config) {
        const typeConfig = this.TYPES[type] || this.TYPES.info;

        const notification = document.createElement('div');
        notification.className = `
            fixed ${config.position} z-[9999] 
            ${typeConfig.bgColor} ${typeConfig.textColor} 
            rounded-lg shadow-xl border ${typeConfig.borderColor}
            transform translate-x-full transition-transform duration-300 ease-in-out
            flex items-center space-x-3 
            min-w-64 max-w-80 p-4
        `.replace(/\s+/g, ' ').trim();

        // 創建圖標
        const iconHtml = `<i data-lucide="${typeConfig.icon}" class="w-5 h-5 flex-shrink-0"></i>`;

        // 創建訊息內容
        const messageHtml = `<span class="font-medium text-sm flex-1">${this.escapeHtml(message)}</span>`;

        // 創建關閉按鈕（如果可關閉）
        const closeButtonHtml = config.closable ?
            `<button class="ml-2 text-white hover:text-gray-200 transition-colors" onclick="NotificationSystem.hideNotification(this.parentElement)">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>` : '';

        notification.innerHTML = iconHtml + messageHtml + closeButtonHtml;

        return notification;
    }

    /**
     * 隱藏通知
     * @param {HTMLElement} notification - 通知元素
     */
    static hideNotification(notification) {
        if (!notification || !notification.parentNode) return;

        // 滑出動畫
        notification.classList.remove('translate-x-0');
        notification.classList.add('translate-x-full');

        // 移除元素
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }

            // 從活動列表中移除
            this.activeNotifications = this.activeNotifications.filter(
                item => item.element !== notification
            );
        }, 300);
    }

    /**
     * 清理舊通知
     */
    static cleanupOldNotifications() {
        if (this.activeNotifications.length >= this.CONFIG.MAX_NOTIFICATIONS) {
            // 移除最舊的通知
            const oldestNotification = this.activeNotifications.shift();
            if (oldestNotification) {
                this.hideNotification(oldestNotification.element);
            }
        }
    }

    /**
     * 顯示成功通知
     * @param {string} message - 訊息內容
     * @param {Object} options - 額外選項
     */
    static success(message, options = {}) {
        return this.showSlideNotification(message, 'success', options);
    }

    /**
     * 顯示錯誤通知
     * @param {string} message - 訊息內容
     * @param {Object} options - 額外選項
     */
    static error(message, options = {}) {
        return this.showSlideNotification(message, 'error', options);
    }

    /**
     * 顯示警告通知
     * @param {string} message - 訊息內容
     * @param {Object} options - 額外選項
     */
    static warning(message, options = {}) {
        return this.showSlideNotification(message, 'warning', options);
    }

    /**
     * 顯示資訊通知
     * @param {string} message - 訊息內容
     * @param {Object} options - 額外選項
     */
    static info(message, options = {}) {
        return this.showSlideNotification(message, 'info', options);
    }

    /**
     * 顯示載入狀態
     * @param {string} message - 載入訊息
     * @returns {HTMLElement} 載入元素
     */
    static showLoading(message = '載入中...') {
        const loadingElement = document.createElement('div');
        loadingElement.id = 'loading-overlay';
        loadingElement.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]';
        loadingElement.innerHTML = `
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3 shadow-xl">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="text-gray-700 font-medium">${this.escapeHtml(message)}</span>
            </div>
        `;

        document.body.appendChild(loadingElement);
        return loadingElement;
    }

    /**
     * 隱藏載入狀態
     */
    static hideLoading() {
        const loadingElement = document.getElementById('loading-overlay');
        if (loadingElement) {
            loadingElement.remove();
        }
    }

    /**
     * 顯示確認對話框
     * @param {string} message - 確認訊息
     * @param {Object} options - 選項
     * @returns {Promise<boolean>} 用戶選擇結果
     */
    static showConfirm(message, options = {}) {
        return new Promise((resolve) => {
            const config = {
                title: '確認操作',
                confirmText: '確認',
                cancelText: '取消',
                type: 'warning',
                ...options
            };

            const modal = this.createConfirmModal(message, config, resolve);
            document.body.appendChild(modal);

            // 重新創建圖標
            if (window.lucide) {
                lucide.createIcons();
            }

            // 顯示動畫
            setTimeout(() => {
                modal.classList.remove('opacity-0');
                modal.querySelector('.transform').classList.remove('scale-95');
                modal.querySelector('.transform').classList.add('scale-100');
            }, 10);
        });
    }

    /**
     * 創建確認對話框
     * @param {string} message - 訊息
     * @param {Object} config - 配置
     * @param {Function} resolve - Promise resolve函數
     * @returns {HTMLElement} 對話框元素
     */
    static createConfirmModal(message, config, resolve) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] opacity-0 transition-opacity duration-300';

        const typeConfig = this.TYPES[config.type] || this.TYPES.warning;

        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl transform scale-95 transition-transform duration-300 max-w-md w-full mx-4">
                <div class="p-6">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 ${typeConfig.bgColor} rounded-full flex items-center justify-center">
                            <i data-lucide="${typeConfig.icon}" class="w-5 h-5 text-white"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">${this.escapeHtml(config.title)}</h3>
                    </div>
                    <p class="text-gray-600 mb-6">${this.escapeHtml(message)}</p>
                    <div class="flex space-x-3 justify-end">
                        <button class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors" onclick="NotificationSystem.closeConfirmModal(this, false)">
                            ${this.escapeHtml(config.cancelText)}
                        </button>
                        <button class="px-4 py-2 text-white ${typeConfig.bgColor} rounded-md hover:opacity-90 transition-opacity" onclick="NotificationSystem.closeConfirmModal(this, true)">
                            ${this.escapeHtml(config.confirmText)}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 儲存resolve函數
        modal._resolve = resolve;

        return modal;
    }

    /**
     * 關閉確認對話框
     * @param {HTMLElement} button - 按鈕元素
     * @param {boolean} result - 選擇結果
     */
    static closeConfirmModal(button, result) {
        const modal = button.closest('.fixed');
        if (modal && modal._resolve) {
            modal._resolve(result);

            // 隱藏動畫
            modal.classList.add('opacity-0');
            modal.querySelector('.transform').classList.remove('scale-100');
            modal.querySelector('.transform').classList.add('scale-95');

            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * 清除所有通知
     */
    static clearAll() {
        this.activeNotifications.forEach(item => {
            this.hideNotification(item.element);
        });
        this.activeNotifications = [];
    }

    /**
     * HTML轉義
     * @param {string} text - 原始文字
     * @returns {string} 轉義後的文字
     */
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 設置全域配置
     * @param {Object} config - 配置選項
     */
    static configure(config) {
        Object.assign(this.CONFIG, config);
    }
}

// 導出為全域變數
window.NotificationSystem = NotificationSystem;

// 提供簡化的全域函數（向後兼容）
window.showSlideNotification = function (message, type, options) {
    return NotificationSystem.showSlideNotification(message, type, options);
};

window.showNotification = function (message, type) {
    return NotificationSystem.showSlideNotification(message, type);
};

window.showSuccess = function (message) {
    return NotificationSystem.success(message);
};

window.showError = function (message) {
    return NotificationSystem.error(message);
};

window.showWarning = function (message) {
    return NotificationSystem.warning(message);
};

window.showInfo = function (message) {
    return NotificationSystem.info(message);
};

window.showLoading = function (message) {
    return NotificationSystem.showLoading(message);
};

window.hideLoading = function () {
    return NotificationSystem.hideLoading();
};

window.showConfirm = function (message, options) {
    return NotificationSystem.showConfirm(message, options);
};