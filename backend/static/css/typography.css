/**
 * 字體系統 - Han AttendanceOS v2005.6.12
 * 遠漢科技考勤系統統一字體規範
 * 
 * 設計原則：
 * - 可讀性優先：確保在各種設備上的清晰度
 * - 層次分明：建立清晰的信息層級
 * - 品牌一致：統一的字體風格
 * - 多語言支援：中英文混排優化
 */

:root {
    /* ========== 字體族系統 ========== */
    /* 主要字體 - 系統字體優先，確保最佳性能 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'Liberation Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    /* 中文字體優化 */
    --font-sans-zh: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Source Han Sans SC', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;
    /* 等寬字體 - 代碼和數據顯示 */
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', 'DejaVu Sans Mono', monospace;
    /* 襯線字體 - 正式文檔 */
    --font-serif: 'Georgia', 'Times New Roman', 'Times', serif;
    /* 品牌字體 - 標題和重要內容 */
    --font-brand: var(--font-sans);
    /* ========== 字體大小系統 ========== */
    /* 基於 1.25 比例的字體大小階層 */
    --text-xs: 0.75rem;
    /* 12px */
    --text-sm: 0.875rem;
    /* 14px */
    --text-base: 1rem;
    /* 16px - 基準大小 */
    --text-lg: 1.125rem;
    /* 18px */
    --text-xl: 1.25rem;
    /* 20px */
    --text-2xl: 1.5rem;
    /* 24px */
    --text-3xl: 1.875rem;
    /* 30px */
    --text-4xl: 2.25rem;
    /* 36px */
    --text-5xl: 3rem;
    /* 48px */
    --text-6xl: 3.75rem;
    /* 60px */
    --text-7xl: 4.5rem;
    /* 72px */
    --text-8xl: 6rem;
    /* 96px */
    --text-9xl: 8rem;
    /* 128px */
    /* ========== 行高系統 ========== */
    /* 基於字體大小的行高比例 */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    /* ========== 字重系統 ========== */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
    /* ========== 字母間距系統 ========== */
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    /* ========== 文字裝飾 ========== */
    --text-decoration-none: none;
    --text-decoration-underline: underline;
    --text-decoration-line-through: line-through;
    /* ========== 文字對齊 ========== */
    --text-align-left: left;
    --text-align-center: center;
    --text-align-right: right;
    --text-align-justify: justify;
    /* ========== 文字轉換 ========== */
    --text-transform-none: none;
    --text-transform-uppercase: uppercase;
    --text-transform-lowercase: lowercase;
    --text-transform-capitalize: capitalize;
}


/* ========== 標題系統 ========== */

.heading-1,
.h1 {
    font-family: var(--font-brand);
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.heading-2,
.h2 {
    font-family: var(--font-brand);
    font-size: var(--text-4xl);
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.heading-3,
.h3 {
    font-family: var(--font-brand);
    font-size: var(--text-3xl);
    font-weight: var(--font-semibold);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.heading-4,
.h4 {
    font-family: var(--font-brand);
    font-size: var(--text-2xl);
    font-weight: var(--font-medium);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.heading-5,
.h5 {
    font-family: var(--font-brand);
    font-size: var(--text-xl);
    font-weight: var(--font-medium);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.heading-6,
.h6 {
    font-family: var(--font-brand);
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}


/* ========== 段落系統 ========== */

.paragraph,
.p {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-relaxed);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.paragraph-large,
.p-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
}

.paragraph-small,
.p-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    color: var(--text-tertiary);
}

.paragraph-xs,
.p-xs {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    color: var(--text-quaternary);
}


/* ========== 特殊文字樣式 ========== */

.lead {
    font-size: var(--text-xl);
    font-weight: var(--font-light);
    line-height: var(--leading-relaxed);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.subtitle {
    font-size: var(--text-lg);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-md);
}

.caption {
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    color: var(--text-quaternary);
    margin-bottom: var(--spacing-sm);
}

.overline {
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wider);
    text-transform: uppercase;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-xs);
}


/* ========== 代碼和數據顯示 ========== */

.code,
code {
    font-family: var(--font-mono);
    font-size: 0.875em;
    font-weight: var(--font-normal);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
}

.code-block,
pre {
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    line-height: var(--leading-relaxed);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    overflow-x: auto;
    margin-bottom: var(--spacing-md);
}

.number,
.numeric {
    font-family: var(--font-mono);
    font-variant-numeric: tabular-nums;
    font-weight: var(--font-medium);
}


/* ========== 鏈接樣式 ========== */

.link,
a {
    color: var(--text-brand);
    text-decoration: var(--text-decoration-none);
    font-weight: var(--font-medium);
    transition: color var(--transition-fast);
}

.link:hover,
a:hover {
    color: var(--color-primary-700);
    text-decoration: var(--text-decoration-underline);
}

.link:focus,
a:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

.link-subtle {
    color: var(--text-secondary);
    font-weight: var(--font-normal);
}

.link-subtle:hover {
    color: var(--text-brand);
}


/* ========== 列表樣式 ========== */

.list,
ul,
ol {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.list-item,
li {
    margin-bottom: var(--spacing-xs);
}

.list-unstyled {
    list-style: none;
    padding-left: 0;
}


/* ========== 強調和重點 ========== */

.emphasis,
em {
    font-style: italic;
    color: var(--text-primary);
}

.strong,
strong {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.mark,
mark {
    background-color: var(--color-warning-100);
    color: var(--color-warning-900);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
}

.highlight {
    background: var(--gradient-brand);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-weight: var(--font-semibold);
}


/* ========== 工具類別 ========== */


/* 字體大小 */

.text-xs {
    font-size: var(--text-xs);
}

.text-sm {
    font-size: var(--text-sm);
}

.text-base {
    font-size: var(--text-base);
}

.text-lg {
    font-size: var(--text-lg);
}

.text-xl {
    font-size: var(--text-xl);
}

.text-2xl {
    font-size: var(--text-2xl);
}

.text-3xl {
    font-size: var(--text-3xl);
}

.text-4xl {
    font-size: var(--text-4xl);
}

.text-5xl {
    font-size: var(--text-5xl);
}


/* 字重 */

.font-thin {
    font-weight: var(--font-thin);
}

.font-light {
    font-weight: var(--font-light);
}

.font-normal {
    font-weight: var(--font-normal);
}

.font-medium {
    font-weight: var(--font-medium);
}

.font-semibold {
    font-weight: var(--font-semibold);
}

.font-bold {
    font-weight: var(--font-bold);
}

.font-extrabold {
    font-weight: var(--font-extrabold);
}


/* 行高 */

.leading-none {
    line-height: var(--leading-none);
}

.leading-tight {
    line-height: var(--leading-tight);
}

.leading-snug {
    line-height: var(--leading-snug);
}

.leading-normal {
    line-height: var(--leading-normal);
}

.leading-relaxed {
    line-height: var(--leading-relaxed);
}

.leading-loose {
    line-height: var(--leading-loose);
}


/* 字母間距 */

.tracking-tight {
    letter-spacing: var(--tracking-tight);
}

.tracking-normal {
    letter-spacing: var(--tracking-normal);
}

.tracking-wide {
    letter-spacing: var(--tracking-wide);
}

.tracking-wider {
    letter-spacing: var(--tracking-wider);
}


/* 文字對齊 */

.text-left {
    text-align: var(--text-align-left);
}

.text-center {
    text-align: var(--text-align-center);
}

.text-right {
    text-align: var(--text-align-right);
}

.text-justify {
    text-align: var(--text-align-justify);
}


/* 文字轉換 */

.uppercase {
    text-transform: var(--text-transform-uppercase);
}

.lowercase {
    text-transform: var(--text-transform-lowercase);
}

.capitalize {
    text-transform: var(--text-transform-capitalize);
}

.normal-case {
    text-transform: var(--text-transform-none);
}


/* 文字裝飾 */

.underline {
    text-decoration: var(--text-decoration-underline);
}

.line-through {
    text-decoration: var(--text-decoration-line-through);
}

.no-underline {
    text-decoration: var(--text-decoration-none);
}


/* 文字省略 */

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-ellipsis {
    text-overflow: ellipsis;
}

.text-clip {
    text-overflow: clip;
}


/* 換行控制 */

.whitespace-normal {
    white-space: normal;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.whitespace-pre {
    white-space: pre;
}

.whitespace-pre-line {
    white-space: pre-line;
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
}

.break-normal {
    overflow-wrap: normal;
    word-break: normal;
}

.break-words {
    overflow-wrap: break-word;
}

.break-all {
    word-break: break-all;
}


/* ========== 響應式字體 ========== */

@media (max-width: 640px) {
    .heading-1,
    .h1 {
        font-size: var(--text-4xl);
    }
    .heading-2,
    .h2 {
        font-size: var(--text-3xl);
    }
    .heading-3,
    .h3 {
        font-size: var(--text-2xl);
    }
    .heading-4,
    .h4 {
        font-size: var(--text-xl);
    }
    .heading-5,
    .h5 {
        font-size: var(--text-lg);
    }
    .heading-6,
    .h6 {
        font-size: var(--text-base);
    }
    .lead {
        font-size: var(--text-lg);
    }
    .paragraph-large,
    .p-large {
        font-size: var(--text-base);
    }
}


/* ========== 列印優化 ========== */

@media print {
    .heading-1,
    .h1,
    .heading-2,
    .h2,
    .heading-3,
    .h3,
    .heading-4,
    .h4,
    .heading-5,
    .h5,
    .heading-6,
    .h6 {
        color: black !important;
        page-break-after: avoid;
    }
    .paragraph,
    .p {
        color: black !important;
        orphans: 3;
        widows: 3;
    }
    .link,
    a {
        color: black !important;
        text-decoration: underline !important;
    }
}