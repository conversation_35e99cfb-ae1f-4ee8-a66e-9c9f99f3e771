"""
系統功能API模組

包含所有系統管理相關的API端點：
- 系統設定管理
- 健康檢查
- 系統監控
- 基本資料管理
- 權限管理
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import sqlite3
import logging
import json
import os
import psutil

# 創建藍圖
system_bp = Blueprint('system', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數
from database import create_connection

@system_bp.route("/api/settings", methods=["GET", "POST"])
def manage_settings():
    """
    管理系統設定
    
    GET: 獲取所有系統設定
    POST: 更新系統設定
    
    返回：
    - 系統設定資料
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT setting_key, setting_value, description, category, data_type
                FROM system_settings 
                ORDER BY category, setting_key
            """)
            
            settings = {}
            for row in cursor.fetchall():
                category = row[3] or 'general'
                if category not in settings:
                    settings[category] = {}
                
                # 根據資料類型轉換值
                value = row[1]
                if row[4] == 'boolean':
                    value = value.lower() in ('true', '1', 'yes')
                elif row[4] == 'integer':
                    value = int(value) if value.isdigit() else 0
                elif row[4] == 'float':
                    try:
                        value = float(value)
                    except ValueError:
                        value = 0.0
                
                settings[category][row[0]] = {
                    "value": value,
                    "description": row[2],
                    "data_type": row[4]
                }
            
            return jsonify({"settings": settings})
            
        else:  # POST
            data = request.json
            if not data:
                return jsonify({"error": "缺少設定資料"}), 400
            
            updated_count = 0
            for setting_key, setting_value in data.items():
                # 將值轉換為字串存儲
                if isinstance(setting_value, bool):
                    value_str = 'true' if setting_value else 'false'
                else:
                    value_str = str(setting_value)
                
                cursor.execute("""
                    UPDATE system_settings 
                    SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE setting_key = ?
                """, (value_str, setting_key))
                
                if cursor.rowcount > 0:
                    updated_count += 1
            
            conn.commit()
            logger.info(f"系統設定更新成功，共更新 {updated_count} 項設定")
            return jsonify({"message": f"設定更新成功，共更新 {updated_count} 項"})
            
    except Exception as e:
        logger.error(f"管理系統設定失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/settings/attendance-rules", methods=["GET", "POST"])
def manage_attendance_rules():
    """
    管理考勤規則設定
    
    GET: 獲取考勤規則
    POST: 更新考勤規則
    
    返回：
    - 考勤規則設定
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT rule_name, rule_value, description, is_active
                FROM attendance_rules 
                ORDER BY rule_name
            """)
            
            rules = {}
            for row in cursor.fetchall():
                rules[row[0]] = {
                    "value": row[1],
                    "description": row[2],
                    "is_active": bool(row[3])
                }
            
            return jsonify({"attendance_rules": rules})
            
        else:  # POST
            data = request.json
            if not data:
                return jsonify({"error": "缺少規則資料"}), 400
            
            updated_count = 0
            for rule_name, rule_data in data.items():
                cursor.execute("""
                    UPDATE attendance_rules 
                    SET rule_value = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE rule_name = ?
                """, (rule_data.get('value'), rule_data.get('is_active', True), rule_name))
                
                if cursor.rowcount > 0:
                    updated_count += 1
                else:
                    # 如果規則不存在，則新增
                    cursor.execute("""
                        INSERT INTO attendance_rules (rule_name, rule_value, description, is_active)
                        VALUES (?, ?, ?, ?)
                    """, (
                        rule_name, 
                        rule_data.get('value'), 
                        rule_data.get('description', ''), 
                        rule_data.get('is_active', True)
                    ))
                    updated_count += 1
            
            conn.commit()
            logger.info(f"考勤規則更新成功，共更新 {updated_count} 項規則")
            return jsonify({"message": f"規則更新成功，共更新 {updated_count} 項"})
            
    except Exception as e:
        logger.error(f"管理考勤規則失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/settings/notifications", methods=["GET", "POST"])
def manage_notifications():
    """
    管理通知設定
    
    GET: 獲取通知設定
    POST: 更新通知設定
    
    返回：
    - 通知設定資料
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT notification_type, is_enabled, settings, description
                FROM notification_settings 
                ORDER BY notification_type
            """)
            
            notifications = {}
            for row in cursor.fetchall():
                settings = {}
                if row[2]:
                    try:
                        settings = json.loads(row[2])
                    except json.JSONDecodeError:
                        settings = {}
                
                notifications[row[0]] = {
                    "is_enabled": bool(row[1]),
                    "settings": settings,
                    "description": row[3]
                }
            
            return jsonify({"notifications": notifications})
            
        else:  # POST
            data = request.json
            if not data:
                return jsonify({"error": "缺少通知設定資料"}), 400
            
            updated_count = 0
            for notification_type, notification_data in data.items():
                settings_json = json.dumps(notification_data.get('settings', {}))
                
                cursor.execute("""
                    UPDATE notification_settings 
                    SET is_enabled = ?, settings = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE notification_type = ?
                """, (
                    notification_data.get('is_enabled', True), 
                    settings_json, 
                    notification_type
                ))
                
                if cursor.rowcount > 0:
                    updated_count += 1
            
            conn.commit()
            logger.info(f"通知設定更新成功，共更新 {updated_count} 項設定")
            return jsonify({"message": f"通知設定更新成功，共更新 {updated_count} 項"})
            
    except Exception as e:
        logger.error(f"管理通知設定失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/permissions/roles", methods=["GET", "POST"])
def manage_roles():
    """
    管理角色權限
    
    GET: 獲取所有角色
    POST: 新增角色
    
    返回：
    - 角色列表或新增結果
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            cursor.execute("""
                SELECT id, role_name, description, permissions, is_active, created_at
                FROM user_roles 
                ORDER BY role_name
            """)
            
            roles = []
            for row in cursor.fetchall():
                permissions = []
                if row[3]:
                    try:
                        permissions = json.loads(row[3])
                    except json.JSONDecodeError:
                        permissions = []
                
                role = {
                    "id": row[0],
                    "role_name": row[1],
                    "description": row[2],
                    "permissions": permissions,
                    "is_active": bool(row[4]),
                    "created_at": row[5]
                }
                roles.append(role)
            
            return jsonify({"roles": roles})
            
        else:  # POST
            data = request.json
            required_fields = ["role_name", "description"]
            for field in required_fields:
                if field not in data:
                    return jsonify({"error": f"缺少必要欄位: {field}"}), 400
            
            permissions_json = json.dumps(data.get('permissions', []))
            
            cursor.execute("""
                INSERT INTO user_roles (role_name, description, permissions)
                VALUES (?, ?, ?)
            """, (data["role_name"], data["description"], permissions_json))
            
            role_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"角色新增成功: {data['role_name']} (ID: {role_id})")
            return jsonify({"message": "角色新增成功", "id": role_id}), 201
            
    except Exception as e:
        logger.error(f"管理角色失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/masterdata/<string:table_name>", methods=["GET", "POST"])
def manage_masterdata(table_name):
    """
    管理基本資料
    
    功能說明：
    - 支援多種基本資料表的CRUD操作
    - 動態處理不同表格的欄位結構
    
    參數：
    table_name (str): 資料表名稱
    
    GET: 獲取基本資料列表
    POST: 新增基本資料
    
    返回：
    - 基本資料列表或新增結果
    """
    # 定義允許的資料表和其欄位結構
    allowed_tables = {
        'clock_status_types': {
            'fields': ['status_code', 'status_name', 'description', 'color_code'],
            'required': ['status_code', 'status_name']
        },
        'leave_types': {
            'fields': ['name', 'code', 'max_days_per_year', 'requires_approval', 'is_paid', 'description'],
            'required': ['name', 'code']
        },
        'departments': {
            'fields': ['name', 'code', 'manager_id', 'description'],
            'required': ['name', 'code']
        },
        'shifts': {
            'fields': ['name', 'code', 'start_time', 'end_time', 'break_duration_minutes', 'description'],
            'required': ['name', 'code', 'start_time', 'end_time']
        },
        'education_levels': {
            'fields': ['name', 'level_order', 'description'],
            'required': ['name', 'level_order']
        },
        'positions': {
            'fields': ['name', 'department_id', 'level_order', 'salary_range_min', 'salary_range_max', 'description'],
            'required': ['name', 'level_order']
        },
        'salary_grades': {
            'fields': ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary', 'description'],
            'required': ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary']
        },
        'work_locations': {
            'fields': ['name', 'address', 'city', 'country', 'timezone', 'is_remote', 'description'],
            'required': ['name']
        },
        'skills': {
            'fields': ['name', 'category', 'description'],
            'required': ['name']
        }
    }
    
    if table_name not in allowed_tables:
        return jsonify({"error": "不支援的資料表"}), 400
    
    table_config = allowed_tables[table_name]
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 構建查詢語句
            base_fields = ['id'] + table_config['fields'] + ['created_at']
            
            # 特殊處理departments表（沒有is_active和updated_at欄位）
            if table_name == 'departments':
                fields = ', '.join(base_fields)
                query = f"SELECT {fields} FROM {table_name} ORDER BY id"
            else:
                fields = ', '.join(base_fields + ['updated_at'])
                query = f"SELECT {fields} FROM {table_name} WHERE is_active = 1 ORDER BY id"
            
            cursor.execute(query)
            
            # 獲取欄位名稱
            columns = [description[0] for description in cursor.description]
            
            # 轉換為字典格式
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                records.append(record)
            
            return jsonify({
                "table_name": table_name,
                "records": records,
                "total": len(records)
            })
            
        else:  # POST
            data = request.json
            if not data:
                return jsonify({"error": "缺少資料"}), 400
            
            # 驗證必要欄位
            for field in table_config['required']:
                if field not in data or not data[field]:
                    return jsonify({"error": f"缺少必要欄位: {field}"}), 400
            
            # 構建插入語句
            fields = []
            values = []
            placeholders = []
            
            for field in table_config['fields']:
                if field in data:
                    fields.append(field)
                    values.append(data[field])
                    placeholders.append('?')
            
            if not fields:
                return jsonify({"error": "沒有有效的欄位資料"}), 400
            
            fields_str = ', '.join(fields)
            placeholders_str = ', '.join(placeholders)
            query = f"INSERT INTO {table_name} ({fields_str}) VALUES ({placeholders_str})"
            
            cursor.execute(query, values)
            record_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"{table_name} 資料新增成功 (ID: {record_id})")
            return jsonify({"message": "資料新增成功", "id": record_id}), 201
            
    except sqlite3.Error as e:
        logger.error(f"管理 {table_name} 資料失敗: {e}")
        return jsonify({"error": f"資料庫錯誤: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"管理 {table_name} 資料失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/masterdata/<string:table_name>/<int:item_id>", methods=["GET", "PUT", "DELETE"])
def manage_masterdata_item(table_name, item_id):
    """
    管理單一基本資料項目
    
    功能說明：
    - 支援單一資料項目的查詢、更新、刪除操作
    
    參數：
    table_name (str): 資料表名稱
    item_id (int): 資料項目ID
    
    GET: 獲取單一資料項目
    PUT: 更新資料項目
    DELETE: 刪除資料項目（軟刪除）
    
    返回：
    - 資料項目詳情或操作結果
    """
    # 使用與上面相同的允許表格配置
    allowed_tables = {
        'clock_status_types': {
            'fields': ['status_code', 'status_name', 'description', 'color_code'],
            'required': ['status_code', 'status_name']
        },
        'leave_types': {
            'fields': ['name', 'code', 'max_days_per_year', 'requires_approval', 'is_paid', 'description'],
            'required': ['name', 'code']
        },
        'departments': {
            'fields': ['name', 'code', 'manager_id', 'description'],
            'required': ['name', 'code']
        },
        'shifts': {
            'fields': ['name', 'code', 'start_time', 'end_time', 'break_duration_minutes', 'description'],
            'required': ['name', 'code', 'start_time', 'end_time']
        },
        'education_levels': {
            'fields': ['name', 'level_order', 'description'],
            'required': ['name', 'level_order']
        },
        'positions': {
            'fields': ['name', 'department_id', 'level_order', 'salary_range_min', 'salary_range_max', 'description'],
            'required': ['name', 'level_order']
        },
        'salary_grades': {
            'fields': ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary', 'description'],
            'required': ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary']
        },
        'work_locations': {
            'fields': ['name', 'address', 'city', 'country', 'timezone', 'is_remote', 'description'],
            'required': ['name']
        },
        'skills': {
            'fields': ['name', 'category', 'description'],
            'required': ['name']
        }
    }
    
    if table_name not in allowed_tables:
        return jsonify({"error": "不支援的資料表"}), 400
    
    table_config = allowed_tables[table_name]
    
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        if request.method == "GET":
            # 獲取單一記錄
            fields = ', '.join(['id'] + table_config['fields'] + ['is_active', 'created_at', 'updated_at'])
            query = f"SELECT {fields} FROM {table_name} WHERE id = ?"
            
            cursor.execute(query, (item_id,))
            row = cursor.fetchone()
            
            if not row:
                return jsonify({"error": "資料不存在"}), 404
            
            # 獲取欄位名稱並轉換為字典
            columns = [description[0] for description in cursor.description]
            record = dict(zip(columns, row))
            
            return jsonify(record)
            
        elif request.method == "PUT":
            # 更新記錄
            data = request.json
            if not data:
                return jsonify({"error": "缺少更新資料"}), 400
            
            # 檢查記錄是否存在
            cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
            if not cursor.fetchone():
                return jsonify({"error": "資料不存在"}), 404
            
            # 構建更新語句
            update_fields = []
            values = []
            
            for field in table_config['fields']:
                if field in data:
                    update_fields.append(f"{field} = ?")
                    values.append(data[field])
            
            if not update_fields:
                return jsonify({"error": "沒有有效的更新欄位"}), 400
            
            # 添加更新時間
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            values.append(item_id)
            
            update_str = ', '.join(update_fields)
            query = f"UPDATE {table_name} SET {update_str} WHERE id = ?"
            
            cursor.execute(query, values)
            conn.commit()
            
            logger.info(f"{table_name} 資料更新成功 (ID: {item_id})")
            return jsonify({"message": "資料更新成功"})
            
        else:  # DELETE
            # 軟刪除記錄
            cursor.execute(f"SELECT id FROM {table_name} WHERE id = ?", (item_id,))
            if not cursor.fetchone():
                return jsonify({"error": "資料不存在"}), 404
            
            cursor.execute(f"""
                UPDATE {table_name} 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (item_id,))
            
            conn.commit()
            
            logger.info(f"{table_name} 資料刪除成功 (ID: {item_id})")
            return jsonify({"message": "資料刪除成功"})
            
    except sqlite3.Error as e:
        logger.error(f"管理 {table_name} 單一資料失敗: {e}")
        return jsonify({"error": f"資料庫錯誤: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"管理 {table_name} 單一資料失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@system_bp.route("/api/clock-status-types", methods=["GET"])
def get_clock_status_types():
    """
    獲取打卡狀態類型
    
    返回：
    - 所有可用的打卡狀態類型
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT status_code, status_name, description, color_code
            FROM clock_status_types 
            WHERE is_active = 1 
            ORDER BY status_code
        """)
        
        status_types = {}
        for row in cursor.fetchall():
            status_types[row[0]] = {
                "name": row[1],
                "description": row[2],
                "color_code": row[3]
            }
        
        # 如果沒有資料，返回預設狀態類型
        if not status_types:
            status_types = {
                "I": {"name": "上班", "description": "正常上班打卡", "color_code": "#28a745"},
                "O": {"name": "下班", "description": "正常下班打卡", "color_code": "#dc3545"},
                "L": {"name": "遲到", "description": "遲到打卡", "color_code": "#ffc107"},
                "E": {"name": "早退", "description": "早退打卡", "color_code": "#fd7e14"},
                "A": {"name": "缺勤", "description": "未打卡缺勤", "color_code": "#6c757d"}
            }
        
        return jsonify({"status_types": status_types})
        
    except Exception as e:
        logger.error(f"獲取打卡狀態類型失敗: {e}")
        # 返回預設狀態類型
        default_status_types = {
            "I": {"name": "上班", "description": "正常上班打卡", "color_code": "#28a745"},
            "O": {"name": "下班", "description": "正常下班打卡", "color_code": "#dc3545"},
            "L": {"name": "遲到", "description": "遲到打卡", "color_code": "#ffc107"},
            "E": {"name": "早退", "description": "早退打卡", "color_code": "#fd7e14"},
            "A": {"name": "缺勤", "description": "未打卡缺勤", "color_code": "#6c757d"}
        }
        return jsonify({"status_types": default_status_types})
    finally:
        conn.close()


@system_bp.route("/api/health", methods=["GET"])
def health_check():
    """
    系統健康檢查
    
    返回：
    - 系統整體健康狀態
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.1.0",
            "uptime": "運行中",
            "checks": {}
        }
        
        # 檢查資料庫連接
        try:
            conn = create_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            conn.close()
            health_status["checks"]["database"] = {"status": "healthy", "message": "資料庫連接正常"}
        except Exception as e:
            health_status["checks"]["database"] = {"status": "unhealthy", "message": f"資料庫連接失敗: {str(e)}"}
            health_status["status"] = "unhealthy"
        
        # 檢查磁碟空間
        try:
            disk_usage = psutil.disk_usage('/')
            free_space_gb = disk_usage.free / (1024**3)
            if free_space_gb > 1:  # 至少1GB可用空間
                health_status["checks"]["disk"] = {"status": "healthy", "free_space_gb": round(free_space_gb, 2)}
            else:
                health_status["checks"]["disk"] = {"status": "warning", "free_space_gb": round(free_space_gb, 2)}
        except Exception as e:
            health_status["checks"]["disk"] = {"status": "unknown", "message": str(e)}
        
        # 檢查記憶體使用
        try:
            memory = psutil.virtual_memory()
            memory_usage_percent = memory.percent
            if memory_usage_percent < 80:
                health_status["checks"]["memory"] = {"status": "healthy", "usage_percent": memory_usage_percent}
            else:
                health_status["checks"]["memory"] = {"status": "warning", "usage_percent": memory_usage_percent}
        except Exception as e:
            health_status["checks"]["memory"] = {"status": "unknown", "message": str(e)}
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500


@system_bp.route("/api/health/database", methods=["GET"])
def database_health():
    """
    資料庫健康檢查
    
    返回：
    - 資料庫詳細健康狀態
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查主要資料表
        tables_to_check = ['employees', 'departments', 'attendance', 'shifts', 'leaves']
        table_stats = {}
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_stats[table] = {"status": "healthy", "record_count": count}
            except Exception as e:
                table_stats[table] = {"status": "error", "message": str(e)}
        
        # 檢查資料庫檔案大小
        try:
            db_file = "attendance.db"
            if os.path.exists(db_file):
                file_size_mb = os.path.getsize(db_file) / (1024 * 1024)
                db_info = {"file_size_mb": round(file_size_mb, 2), "file_exists": True}
            else:
                db_info = {"file_exists": False}
        except Exception as e:
            db_info = {"error": str(e)}
        
        conn.close()
        
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_info": db_info,
            "table_stats": table_stats
        })
        
    except Exception as e:
        logger.error(f"資料庫健康檢查失敗: {e}")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500


@system_bp.route("/api/health/system", methods=["GET"])
def system_health():
    """
    系統資源健康檢查
    
    返回：
    - 系統資源使用狀況
    """
    try:
        # CPU 使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 記憶體使用率
        memory = psutil.virtual_memory()
        
        # 磁碟使用率
        disk = psutil.disk_usage('/')
        
        # 系統負載（如果可用）
        try:
            load_avg = os.getloadavg()
        except (OSError, AttributeError):
            load_avg = None
        
        system_info = {
            "cpu": {
                "usage_percent": cpu_percent,
                "status": "healthy" if cpu_percent < 80 else "warning"
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "usage_percent": memory.percent,
                "status": "healthy" if memory.percent < 80 else "warning"
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "used_gb": round(disk.used / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "usage_percent": round((disk.used / disk.total) * 100, 2),
                "status": "healthy" if (disk.used / disk.total) < 0.9 else "warning"
            }
        }
        
        if load_avg:
            system_info["load_average"] = {
                "1min": load_avg[0],
                "5min": load_avg[1],
                "15min": load_avg[2]
            }
        
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "system_info": system_info
        })
        
    except Exception as e:
        logger.error(f"系統健康檢查失敗: {e}")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500


@system_bp.route("/api/metrics", methods=["GET"])
def get_system_metrics():
    """
    獲取系統指標
    
    返回：
    - 系統性能指標和統計資料
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取基本統計
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "database_metrics": {},
            "system_metrics": {},
            "application_metrics": {}
        }
        
        # 資料庫指標
        cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
        metrics["database_metrics"]["active_employees"] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM attendance WHERE DATE(check_in) = DATE('now')")
        metrics["database_metrics"]["today_attendance_records"] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
        metrics["database_metrics"]["pending_leaves"] = cursor.fetchone()[0]
        
        # 系統指標
        metrics["system_metrics"]["cpu_usage"] = psutil.cpu_percent()
        metrics["system_metrics"]["memory_usage"] = psutil.virtual_memory().percent
        metrics["system_metrics"]["disk_usage"] = psutil.disk_usage('/').percent
        
        # 應用程式指標
        metrics["application_metrics"]["api_version"] = "2.1.0"
        metrics["application_metrics"]["database_file_size_mb"] = round(
            os.path.getsize("attendance.db") / (1024 * 1024), 2
        ) if os.path.exists("attendance.db") else 0
        
        conn.close()
        
        return jsonify(metrics)
        
    except Exception as e:
        logger.error(f"獲取系統指標失敗: {e}")
        return jsonify({"error": str(e)}), 500


# 這裡之後會添加其他系統功能相關的API
# 例如：備份管理、日誌管理、系統監控等 