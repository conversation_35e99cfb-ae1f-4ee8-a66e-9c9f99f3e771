<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本資料管理 - Han AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>

    <!-- 🔧 [工具函數庫] - 統一的工具函數庫 -->
    <!-- 📝 說明：載入統一的工具函數庫，提供通知系統等功能 -->
    <script src="/static/js/utils/index.js"></script>

    <!-- 🎨 [設計系統] - 統一的設計系統CSS -->
    <!-- 📝 說明：載入統一的設計系統，確保視覺一致性 -->
    <link rel="stylesheet" href="/static/css/design-system.css">
</head>

<body class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-sans">
    <!-- 🔙 返回導航 - 固定位置返回按鈕 -->
    <!-- 📝 說明：左上角固定返回按鈕，統一樣式 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 📦 主容器 - 內容區域容器 -->
    <!-- 📝 說明：包含所有主要內容，設定頂部間距避免與固定按鈕重疊 -->
    <div class="pt-16">
        <div class="min-h-screen p-6">
            <!-- 🎨 頁面標題 - 統一的標題設計 -->
            <!-- 📝 說明：使用漸層背景，左側標題右側資訊的佈局 -->
            <!-- 🎯 用途：提供頁面識別和管理員資訊顯示 -->
            <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                <div class="flex items-center justify-between">
                    <!-- 📍 左側標題區 -->
                    <div class="relative z-10">
                        <h1 class="text-3xl font-bold mb-2 text-white">基本資料管理</h1>
                        <p class="text-indigo-100 text-base font-medium">管理系統的基礎設定資料，包括學歷、職位、假別、薪資等級等</p>
                    </div>

                    <!-- 📍 右側資訊區 - 避免佔用兩行 -->
                    <!-- ⚠️ 注意：使用 flex items-center 確保垂直居中對齊 -->
                    <div class="flex items-center space-x-3 text-right">
                        <div>
                            <p class="text-sm font-medium text-white">管理員模式</p>
                            <p class="text-xs text-indigo-100">基本資料設定</p>
                        </div>
                        <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                            <i data-lucide="database" class="w-6 h-6 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 🔍 資料類別選擇 - 篩選和選擇功能 -->
            <!-- 📝 說明：統一的類別選擇設計，支援多種基本資料類型 -->
            <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 mb-6">
                <div class="flex flex-wrap gap-3">
                    <button class="category-btn active" data-table="education_levels" data-name="學歷">
                        <i data-lucide="graduation-cap" class="w-5 h-5"></i>
                        <span>學歷</span>
                    </button>
                    <button class="category-btn" data-table="positions" data-name="職位">
                        <i data-lucide="briefcase" class="w-5 h-5"></i>
                        <span>職位</span>
                    </button>
                    <button class="category-btn" data-table="leave_types" data-name="假別">
                        <i data-lucide="calendar-x" class="w-5 h-5"></i>
                        <span>假別</span>
                    </button>
                    <button class="category-btn" data-table="salary_grades" data-name="薪資等級">
                        <i data-lucide="dollar-sign" class="w-5 h-5"></i>
                        <span>薪資等級</span>
                    </button>
                    <button class="category-btn" data-table="work_locations" data-name="工作地點">
                        <i data-lucide="map-pin" class="w-5 h-5"></i>
                        <span>工作地點</span>
                    </button>
                    <button class="category-btn" data-table="skills" data-name="專業技能">
                        <i data-lucide="award" class="w-5 h-5"></i>
                        <span>專業技能</span>
                    </button>
                    <button class="category-btn" data-table="clock_status_types" data-name="打卡狀態設定">
                        <i data-lucide="fingerprint" class="w-5 h-5"></i>
                        <span>打卡狀態設定</span>
                    </button>
                    <button class="category-btn" data-table="departments" data-name="部門">
                        <i data-lucide="building" class="w-5 h-5"></i>
                        <span>部門</span>
                    </button>
                </div>
            </div>

            <!-- 📊 工具欄 - 列表標題和統計 -->
            <!-- 📝 說明：顯示列表標題和數據統計信息 -->
            <div class="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
                <div class="flex items-center justify-between">
                    <!-- 📍 左側標題和統計 -->
                    <div class="flex items-center space-x-4">
                        <h2 class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent" id="currentCategoryTitle">學歷管理</h2>
                        <span class="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm" id="itemCount">載入中...</span>
                    </div>

                    <!-- 📍 右側操作按鈕 -->
                    <div class="flex items-center space-x-3">
                        <!-- 🔄 重新整理按鈕 -->
                        <button id="refreshBtn" class="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            <span>重新整理</span>
                        </button>
                        <!-- 🆕 新增按鈕 -->
                        <button id="addBtn" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span>新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 📋 數據列表容器 -->
            <!-- 📝 說明：包含桌面版表格和手機版卡片的響應式設計 -->
            <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                <!-- 🔄 載入狀態 - 載入指示器 -->
                <!-- 📝 說明：統一的載入動畫設計 -->
                <div id="loadingIndicator" class="flex items-center justify-center py-16">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                            <div class="relative">
                                <div class="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                            </div>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                        <p class="text-gray-500">正在載入基本資料</p>
                    </div>
                </div>

                <!-- 💻 桌面版表格 - 大螢幕顯示 -->
                <!-- ⚠️ 注意：使用 hidden 控制顯示 -->
                <div id="dataTable" class="hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <!-- 🏷️ 表格標題行 -->
                            <thead class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                                <tr id="tableHeader">
                                    <!-- 動態生成表頭 -->
                                </tr>
                            </thead>
                            <!-- 📊 表格內容區 -->
                            <tbody id="tableBody" class="divide-y divide-gray-100">
                                <!-- 動態生成表格內容 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 🚫 空狀態 - 無數據時顯示 -->
                <!-- 📝 說明：友好的空狀態提示 -->
                <div id="noDataMessage" class="hidden text-center py-16">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
                        <i data-lucide="inbox" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暫無資料</h3>
                    <p class="text-gray-500 mb-4">目前沒有任何基本資料</p>
                    <button onclick="document.getElementById('addBtn').click()" class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 mx-auto shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新增第一筆資料</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔧 [新增/編輯模態框] - 統一的模態框設計 -->
    <!-- 📝 說明：使用統一的模態框設計標準 -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden border border-gray-100">
                <!-- 🎨 模態框標題區域 - 統一的標題設計 -->
                <!-- 📝 說明：使用漸層背景，包含圖標、標題描述和關閉按鈕 -->
                <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 px-8 py-6 text-white relative overflow-hidden">
                    <div class="absolute inset-0 bg-white opacity-10">
                        <div class="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white opacity-20"></div>
                        <div class="absolute top-8 -left-8 w-16 h-16 rounded-full bg-white opacity-15"></div>
                    </div>

                    <div class="relative flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                <i data-lucide="database" class="w-6 h-6"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold" id="modalTitle">新增資料</h3>
                                <p class="text-indigo-100 text-sm" id="modalSubtitle">填寫基本資料資訊</p>
                            </div>
                        </div>

                        <button type="button" id="closeModalBtn" class="bg-white bg-opacity-10 hover:bg-opacity-20 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm">
                            <i data-lucide="x" class="w-4 h-4"></i>
                            <span>關閉</span>
                        </button>
                    </div>
                </div>

                <!-- 📝 模態框內容區域 -->
                <div class="p-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <form id="editForm">
                        <div id="formFields" class="space-y-6">
                            <!-- 動態生成表單欄位 -->
                        </div>

                        <!-- 🎛️ 操作按鈕區 - 取消和提交功能 -->
                        <div class="flex justify-end space-x-4 pt-6 mt-6 border-t border-gray-200">
                            <button type="button" id="cancelBtn" class="px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                                取消
                            </button>
                            <button type="submit" id="saveBtn" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                                <i data-lucide="save" class="w-4 h-4"></i>
                                <span>儲存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 🎨 類別按鈕樣式 */
        
        .category-btn {
            @apply flex items-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium text-gray-600 bg-gray-50 hover: bg-gray-100 transition-all duration-200 cursor-pointer shadow-sm hover: shadow-md transform hover: scale-105;
        }
        
        .category-btn.active {
            @apply bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover: from-indigo-600 hover: to-purple-700 shadow-lg;
        }
        /* 🎨 表格樣式增強 */
        
        #dataTable table th {
            @apply px-6 py-4 text-left text-sm font-semibold text-gray-700 bg-gradient-to-r from-gray-50 to-gray-100;
        }
        
        #dataTable table td {
            @apply px-6 py-4 text-sm text-gray-900;
        }
        
        #dataTable table tr:hover {
            @apply bg-gradient-to-r from-blue-50 to-indigo-50;
        }
        /* 🎨 表單欄位樣式 */
        
        #formFields input,
        #formFields textarea,
        #formFields select {
            @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus: ring-2 focus: ring-indigo-500 focus: border-transparent transition-all duration-200 shadow-sm hover: shadow-md;
        }
        
        #formFields label {
            @apply block text-sm font-semibold text-gray-700 mb-2;
        }
    </style>

    <script>
        // 🔧 [全域變數] - 頁面狀態管理
        let currentTable = 'education_levels';
        let currentData = [];
        let editingItem = null;

        // 🔧 [資料表配置] - 各種基本資料的欄位配置
        const tableConfigs = {
            education_levels: {
                name: '學歷',
                columns: [{
                    key: 'name',
                    label: '學歷名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'level_order',
                    label: '等級順序',
                    type: 'number',
                    required: true
                }, {
                    key: 'description',
                    label: '說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'level_order', 'description']
            },
            positions: {
                name: '職位',
                columns: [{
                    key: 'name',
                    label: '職位名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'level_order',
                    label: '職位等級',
                    type: 'number',
                    required: true
                }, {
                    key: 'salary_range_min',
                    label: '最低薪資',
                    type: 'number'
                }, {
                    key: 'salary_range_max',
                    label: '最高薪資',
                    type: 'number'
                }, {
                    key: 'description',
                    label: '職位說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'level_order', 'salary_range_min', 'salary_range_max', 'description']
            },
            leave_types: {
                name: '假別',
                columns: [{
                    key: 'name',
                    label: '假別名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'code',
                    label: '假別代碼',
                    type: 'text',
                    required: true
                }, {
                    key: 'max_days_per_year',
                    label: '年度上限天數',
                    type: 'number'
                }, {
                    key: 'is_paid',
                    label: '是否有薪',
                    type: 'checkbox'
                }, {
                    key: 'requires_approval',
                    label: '需要審核',
                    type: 'checkbox'
                }, {
                    key: 'advance_notice_days',
                    label: '提前申請天數',
                    type: 'number'
                }, {
                    key: 'description',
                    label: '假別說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'code', 'max_days_per_year', 'is_paid', 'requires_approval', 'description']
            },
            salary_grades: {
                name: '薪資等級',
                columns: [{
                    key: 'name',
                    label: '等級名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'grade_code',
                    label: '等級代碼',
                    type: 'text',
                    required: true
                }, {
                    key: 'level_order',
                    label: '等級順序',
                    type: 'number',
                    required: true
                }, {
                    key: 'min_salary',
                    label: '最低薪資',
                    type: 'number',
                    required: true
                }, {
                    key: 'max_salary',
                    label: '最高薪資',
                    type: 'number',
                    required: true
                }, {
                    key: 'description',
                    label: '等級說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'grade_code', 'level_order', 'min_salary', 'max_salary', 'description']
            },
            work_locations: {
                name: '工作地點',
                columns: [{
                    key: 'name',
                    label: '地點名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'address',
                    label: '詳細地址',
                    type: 'text'
                }, {
                    key: 'city',
                    label: '城市',
                    type: 'text'
                }, {
                    key: 'country',
                    label: '國家',
                    type: 'text'
                }, {
                    key: 'is_remote',
                    label: '遠端工作',
                    type: 'checkbox'
                }, {
                    key: 'description',
                    label: '地點說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'address', 'city', 'is_remote', 'description']
            },
            skills: {
                name: '技能',
                columns: [{
                    key: 'name',
                    label: '技能名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'category',
                    label: '技能分類',
                    type: 'text'
                }, {
                    key: 'description',
                    label: '技能說明',
                    type: 'textarea'
                }],
                displayColumns: ['name', 'category', 'description']
            },
            clock_status_types: {
                name: '打卡狀態設定',
                columns: [{
                    key: 'status_code',
                    label: '狀態代碼',
                    type: 'text',
                    required: true
                }, {
                    key: 'status_name',
                    label: '狀態名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'description',
                    label: '說明',
                    type: 'textarea'
                }, {
                    key: 'sort_order',
                    label: '排序順序',
                    type: 'number'
                }, {
                    key: 'is_active',
                    label: '啟用狀態',
                    type: 'checkbox'
                }],
                displayColumns: ['status_code', 'status_name', 'description', 'sort_order', 'is_active']
            },
            departments: {
                name: '部門',
                columns: [{
                    key: 'name',
                    label: '部門名稱',
                    type: 'text',
                    required: true
                }, {
                    key: 'manager_id',
                    label: '主管ID',
                    type: 'number'
                }, {
                    key: 'description',
                    label: '部門說明',
                    type: 'textarea'
                }, {
                    key: 'permission_id',
                    label: '權限ID',
                    type: 'number'
                }],
                displayColumns: ['name', 'manager_id', 'description', 'permission_id']
            }
        };

        // 🔧 [頁面初始化] - DOMContentLoaded事件處理
        document.addEventListener('DOMContentLoaded', function() {
            // 🎨 初始化圖標
            lucide.createIcons();

            // 🔧 初始化工具函數庫
            try {
                if (typeof UtilsLoader !== 'undefined') {
                    UtilsLoader.initPageUtils('elite-masterdata');
                    console.log('✅ 工具函數庫初始化成功');
                } else {
                    console.warn('⚠️ 工具函數庫未載入，使用降級處理');
                }
            } catch (error) {
                console.error('❌ 工具函數庫初始化失敗:', error);
            }

            // 🔧 綁定事件和載入資料
            bindEvents();
            loadData();
        });

        // 🔧 [事件綁定] - 綁定所有頁面事件
        function bindEvents() {
            // 分類按鈕
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const table = this.dataset.table;
                    const name = this.dataset.name;
                    switchCategory(table, name);
                });
            });

            // 工具按鈕
            document.getElementById('refreshBtn').addEventListener('click', loadData);
            document.getElementById('addBtn').addEventListener('click', () => showEditModal());

            // 模態框
            document.getElementById('closeModalBtn').addEventListener('click', hideEditModal);
            document.getElementById('cancelBtn').addEventListener('click', hideEditModal);
            document.getElementById('editForm').addEventListener('submit', saveItem);

            // 點擊模態框背景關閉
            document.getElementById('editModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideEditModal();
                }
            });
        }

        // 🔧 [類別切換] - 切換不同的基本資料類別
        function switchCategory(table, name) {
            // 更新按鈕狀態
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-table="${table}"]`).classList.add('active');

            // 更新當前表格
            currentTable = table;
            document.getElementById('currentCategoryTitle').textContent = name + '管理';

            // 載入資料
            loadData();
        }

        // 🔧 [資料載入] - 從API載入資料
        async function loadData() {
            try {
                showLoading();

                const response = await fetch(`/api/masterdata/${currentTable}`);
                const data = await response.json();

                if (response.ok) {
                    currentData = data.records || data.items || [];
                    updateItemCount(data.total || 0);
                    renderTable();
                } else {
                    showNotification(data.error || '載入資料失敗', 'error');
                }

            } catch (error) {
                console.error('載入資料失敗:', error);
                showNotification('載入資料失敗', 'error');
            } finally {
                hideLoading();
            }
        }

        // 🔧 [表格渲染] - 渲染資料表格
        function renderTable() {
            const config = tableConfigs[currentTable];
            const tableHeader = document.getElementById('tableHeader');
            const tableBody = document.getElementById('tableBody');
            const dataTable = document.getElementById('dataTable');
            const noDataMessage = document.getElementById('noDataMessage');

            if (currentData.length === 0) {
                dataTable.classList.add('hidden');
                noDataMessage.classList.remove('hidden');
                return;
            }

            noDataMessage.classList.add('hidden');
            dataTable.classList.remove('hidden');

            // 生成表頭
            tableHeader.innerHTML = config.displayColumns.map(col => {
                const column = config.columns.find(c => c.key === col);
                return `<th class="px-6 py-4 text-left text-sm font-semibold text-gray-700">${column ? column.label : col}</th>`;
            }).join('') + '<th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">操作</th>';

            // 生成表格內容
            tableBody.innerHTML = currentData.map(item => {
                const cells = config.displayColumns.map(col => {
                    let value = item[col];

                    // 格式化不同類型的值
                    if (col === 'is_paid' || col === 'requires_approval' || col === 'is_remote' || col === 'is_active') {
                        value = value ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">是</span>' : '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">否</span>';
                    } else if (col === 'min_salary' || col === 'max_salary' || col === 'salary_range_min' || col === 'salary_range_max') {
                        value = value ? `<span class="font-medium text-green-600">$${Number(value).toLocaleString()}</span>` : '<span class="text-gray-400">-</span>';
                    } else if (value === null || value === undefined || value === '') {
                        value = '<span class="text-gray-400">-</span>';
                    }

                    return `<td class="px-6 py-4 text-sm text-gray-900">${value}</td>`;
                }).join('');

                return `
                    <tr class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200">
                        ${cells}
                        <td class="px-6 py-4 text-right">
                            <button onclick="editItem(${item.id})" class="text-blue-600 hover:text-blue-700 text-sm font-medium mr-3 px-3 py-1 rounded-lg hover:bg-blue-50 transition-all duration-200">
                                <i data-lucide="edit" class="w-4 h-4 inline mr-1"></i>編輯
                            </button>
                            <button onclick="deleteItem(${item.id}, '${item.name || item.status_name || item.id}')" class="text-red-600 hover:text-red-700 text-sm font-medium px-3 py-1 rounded-lg hover:bg-red-50 transition-all duration-200">
                                <i data-lucide="trash-2" class="w-4 h-4 inline mr-1"></i>刪除
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            // 重新初始化圖標
            lucide.createIcons();
        }

        // 🔧 [模態框顯示] - 顯示新增/編輯模態框
        function showEditModal(item = null) {
            editingItem = item;
            const config = tableConfigs[currentTable];

            // 更新標題
            document.getElementById('modalTitle').textContent = item ? `編輯${config.name}` : `新增${config.name}`;
            document.getElementById('modalSubtitle').textContent = `填寫${config.name}資訊`;

            // 生成表單欄位
            const formFields = document.getElementById('formFields');
            formFields.innerHTML = config.columns.map(column => {
                const value = item ? (item[column.key] || '') : '';

                if (column.type === 'textarea') {
                    return `
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i data-lucide="file-text" class="w-4 h-4 inline mr-2 text-gray-500"></i>
                                ${column.label}${column.required ? ' <span class="text-red-500">*</span>' : ''}
                            </label>
                            <textarea name="${column.key}" rows="3" 
                                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                                placeholder="請輸入${column.label}"
                                ${column.required ? 'required' : ''}>${value}</textarea>
                        </div>
                    `;
                } else if (column.type === 'checkbox') {
                    return `
                        <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                            <input type="checkbox" name="${column.key}" id="${column.key}"
                                class="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                ${value ? 'checked' : ''}>
                            <label for="${column.key}" class="text-sm font-semibold text-gray-700 flex items-center">
                                <i data-lucide="check-square" class="w-4 h-4 mr-2 text-gray-500"></i>
                                ${column.label}
                            </label>
                        </div>
                    `;
                } else {
                    const iconMap = {
                        'text': 'type',
                        'number': 'hash',
                        'email': 'mail',
                        'tel': 'phone'
                    };
                    const icon = iconMap[column.type] || 'edit';

                    return `
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i data-lucide="${icon}" class="w-4 h-4 inline mr-2 text-gray-500"></i>
                                ${column.label}${column.required ? ' <span class="text-red-500">*</span>' : ''}
                            </label>
                            <input type="${column.type}" name="${column.key}" value="${value}"
                                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md"
                                placeholder="請輸入${column.label}"
                                ${column.required ? 'required' : ''}>
                        </div>
                    `;
                }
            }).join('');

            document.getElementById('editModal').classList.remove('hidden');

            // 重新初始化圖標
            lucide.createIcons();
        }

        // 🔧 [模態框隱藏] - 隱藏編輯模態框
        function hideEditModal() {
            document.getElementById('editModal').classList.add('hidden');
            editingItem = null;
            document.getElementById('editForm').reset();
        }

        // 🔧 [編輯項目] - 編輯指定項目
        function editItem(id) {
            const item = currentData.find(i => i.id === id);
            if (item) {
                showEditModal(item);
            }
        }

        // 🔧 [刪除項目] - 刪除指定項目
        async function deleteItem(id, name) {
            if (!confirm(`確定要刪除「${name}」嗎？此操作無法復原。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/masterdata/${currentTable}/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification(result.message, 'success');
                    loadData();
                } else {
                    showNotification(result.error || '刪除失敗', 'error');
                }

            } catch (error) {
                console.error('刪除失敗:', error);
                showNotification('刪除失敗', 'error');
            }
        }

        // 🔧 [儲存項目] - 儲存新增或編輯的項目
        async function saveItem(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {};

            // 收集表單資料
            for (let [key, value] of formData.entries()) {
                if (value === 'on') { // checkbox
                    data[key] = true;
                } else if (value === '') {
                    data[key] = null;
                } else {
                    data[key] = value;
                }
            }

            // 處理未勾選的checkbox
            const config = tableConfigs[currentTable];
            config.columns.forEach(column => {
                if (column.type === 'checkbox' && !formData.has(column.key)) {
                    data[column.key] = false;
                }
            });

            try {
                const url = editingItem ?
                    `/api/masterdata/${currentTable}/${editingItem.id}` :
                    `/api/masterdata/${currentTable}`;

                const method = editingItem ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification(result.message, 'success');
                    hideEditModal();
                    loadData();
                } else {
                    showNotification(result.error || '儲存失敗', 'error');
                }

            } catch (error) {
                console.error('儲存失敗:', error);
                showNotification('儲存失敗', 'error');
            }
        }

        // 🔧 [更新項目數量] - 更新顯示的項目數量
        function updateItemCount(count) {
            document.getElementById('itemCount').textContent = `${count} 項目`;
        }

        // 🔧 [顯示載入] - 顯示載入指示器
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('dataTable').classList.add('hidden');
            document.getElementById('noDataMessage').classList.add('hidden');
        }

        // 🔧 [隱藏載入] - 隱藏載入指示器
        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
        }

        // 🔧 [通知系統] - 統一的通知顯示函數
        // 📝 說明：向後兼容的通知函數，優先使用新的工具函數庫
        function showNotification(message, type = 'info') {
            if (window.NotificationSystem) {
                switch (type) {
                    case 'success':
                        NotificationSystem.success(message);
                        break;
                    case 'error':
                        NotificationSystem.error(message);
                        break;
                    case 'warning':
                        NotificationSystem.warning(message);
                        break;
                    default:
                        NotificationSystem.info(message);
                }
            } else {
                // 降級處理：使用瀏覽器原生alert
                console.warn('通知系統未載入，使用降級處理:', message);
                alert(`${type.toUpperCase()}: ${message}`);
            }
        }
    </script>
</body>

</html>