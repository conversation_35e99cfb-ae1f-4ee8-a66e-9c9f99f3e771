# Han AttendanceOS 管理頁面 UI 設計標準

## 🎯 設計目標
建立統一、專業、現代化的管理頁面設計標準，確保所有管理功能具有一致的用戶體驗。

## 🎨 設計原則

### Apple 設計語言
- **簡潔至上**：去除不必要的視覺元素，專注於功能本身
- **人性化交互**：直觀的操作流程，符合用戶習慣
- **視覺層次**：清晰的信息架構和視覺引導
- **一致性**：統一的設計語言和交互模式

### 色彩系統
```css
/* 主背景漸層 */
background: linear-gradient(to bottom right, #f8fafc, #e0f2fe, #e0e7ff)

/* 標題區漸層 */
background: linear-gradient(to right, #6366f1, #8b5cf6, #ec4899)

/* 卡片背景 */
background: rgba(255, 255, 255, 0.9)
backdrop-filter: blur(8px)

/* 文字漸層 */
background: linear-gradient(to right, #4f46e5, #7c3aed)
```

## 🏗️ 頁面結構標準

### 1. 整體佈局
```tsx
<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
  {/* 📦 主容器 - 內容區域容器 */}
  <div className="p-6">
    {/* 🎨 頁面標題區 */}
    {/* 🔍 查詢工具欄 */}
    {/* 📊 工具欄 */}
    {/* 📋 數據列表 */}
  </div>
</div>
```

### 2. 頁面標題區設計
```tsx
{/* 🎨 頁面標題 - 統一的標題設計 */}
<div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
  <div className="flex items-center justify-between">
    {/* 📍 左側標題區 */}
    <div className="relative z-10">
      <h1 className="text-3xl font-bold mb-2 text-white">頁面標題</h1>
      <div className="flex items-center space-x-2">
        {/* 🔙 返回按鈕 - 圖標+文字設計 */}
        <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
          <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
          <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
        </Link>
        <p className="text-indigo-100 text-base font-medium">頁面描述文字</p>
      </div>
    </div>

    {/* 📍 右側資訊區 */}
    <div className="flex items-center space-x-3 text-right">
      <div>
        <p className="text-sm font-medium text-white">管理員模式</p>
        <p className="text-xs text-indigo-100">功能描述</p>
      </div>
      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
        <Icon className="w-6 h-6 text-white" />
      </div>
    </div>
  </div>
</div>
```

## 🔙 返回按鈕設計標準

### 設計特點
1. **位置**：位於標題區塊內，h1標題下方，描述文字前面
2. **樣式**：小型圖標+文字組合，半透明白色背景
3. **尺寸**：圖標 4x4，文字 text-sm
4. **互動**：懸停時背景變亮，文字顏色微調

### 技術實現
```tsx
{/* 🔙 返回按鈕 - 圖標+文字設計 */}
<Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
  <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
  <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
</Link>
```

### CSS 類別說明
- `inline-flex items-center space-x-1`：水平排列，圖標和文字間距
- `px-3 py-1.5`：內邊距，確保點擊區域足夠大
- `bg-white/20 backdrop-blur-sm`：半透明白色背景，毛玻璃效果
- `rounded-lg`：圓角設計
- `hover:bg-white/30`：懸停時背景變亮
- `transition-all duration-200`：平滑過渡動畫
- `group`：群組懸停效果
- `border border-white/30`：半透明邊框

## 📋 組件設計標準

### 查詢工具欄
```tsx
{/* 🔍 查詢工具欄 - 篩選和搜尋功能 */}
<div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
  {/* 查詢表單區域 */}
  <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-4">
    {/* 查詢欄位 */}
  </div>
  
  {/* 操作按鈕區域 */}
  <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-100">
    {/* 左側查詢按鈕 */}
    {/* 右側功能按鈕 */}
  </div>
</div>
```

### 工具欄設計
```tsx
{/* 📊 工具欄 - 列表標題和統計 */}
<div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-4">
      <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">列表標題</h2>
      <span className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
        統計信息
      </span>
    </div>
    
    <div className="flex items-center space-x-3">
      {/* 操作按鈕 */}
    </div>
  </div>
</div>
```

### 數據列表設計
```tsx
{/* 📋 數據列表容器 */}
<div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
  {/* 桌面版表格 */}
  <div className="hidden lg:block">
    {/* 表格標題行 */}
    <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
      {/* 標題內容 */}
    </div>
    
    {/* 表格內容區 */}
    <div className="divide-y divide-gray-100">
      {/* 數據行 */}
    </div>
  </div>
  
  {/* 手機版卡片 */}
  <div className="lg:hidden space-y-4 p-4">
    {/* 卡片內容 */}
  </div>
</div>
```

## 🎯 狀態處理標準

### 載入狀態
```tsx
{loading ? (
  <div className="flex items-center justify-center py-16">
    <div className="text-center">
      <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
        <div className="relative">
          <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
        </div>
      </div>
      <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
    </div>
  </div>
) : (
  {/* 正常內容 */}
)}
```

### 空狀態
```tsx
<div className="text-center py-16">
  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
    <Icon className="w-8 h-8 text-gray-400" />
  </div>
  <h3 className="text-lg font-medium text-gray-900 mb-2">空狀態標題</h3>
  <p className="text-gray-500">空狀態描述</p>
</div>
```

## 🎨 按鈕設計標準

### 主要按鈕（查詢、提交）
```tsx
<button className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
  <Icon className="w-4 h-4" />
  <span>按鈕文字</span>
</button>
```

### 次要按鈕（重置、取消）
```tsx
<button className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-sm hover:shadow-md transform hover:scale-105">
  <Icon className="w-4 h-4" />
  <span>按鈕文字</span>
</button>
```

### 功能按鈕顏色配置
- **新增功能**：`from-emerald-500 to-green-600`
- **匯入功能**：`from-blue-500 to-indigo-600`
- **匯出功能**：`from-purple-500 to-pink-600`
- **刪除功能**：`from-red-500 to-red-600`

## 📱 響應式設計標準

### 斷點設置
- **手機**：`< 1024px` 使用卡片佈局
- **桌面**：`>= 1024px` 使用表格佈局

### 網格系統
- **查詢表單**：`grid-cols-1 lg:grid-cols-4`
- **統計卡片**：`grid-cols-1 md:grid-cols-2 lg:grid-cols-4`
- **功能卡片**：`grid-cols-1 md:grid-cols-2 lg:grid-cols-3`

## 🔧 開發規範

### 必要導入
```tsx
import Link from 'next/link'
import { ArrowLeft, [其他圖標] } from 'lucide-react'
```

### 代碼註釋標準
```tsx
{/* 🎨 頁面標題 - 統一的標題設計 */}
{/* 📦 主容器 - 內容區域容器 */}
{/* 🔙 返回按鈕 - 圖標+文字設計 */}
{/* 📍 左側標題區 */}
{/* 📍 右側資訊區 */}
{/* 🔍 查詢工具欄 - 篩選和搜尋功能 */}
{/* 📊 工具欄 - 列表標題和統計 */}
{/* 📋 數據列表容器 */}
```

## ✅ 檢查清單

### 新建管理頁面時
- [ ] 使用統一的頁面結構
- [ ] 實現標準的返回按鈕設計
- [ ] 添加必要的圖標導入
- [ ] 使用統一的色彩配置
- [ ] 實現響應式設計
- [ ] 添加載入和空狀態處理
- [ ] 使用標準的代碼註釋

### 修改現有頁面時
- [ ] 檢查返回按鈕是否符合標準
- [ ] 確認色彩配置一致性
- [ ] 驗證響應式設計
- [ ] 更新代碼註釋

## 📝 維護指南

### 設計更新流程
1. 在此文檔中更新設計標準
2. 更新所有相關頁面
3. 測試設計一致性
4. 更新開發文檔

### 品質保證
- 定期檢查設計一致性
- 收集用戶反饋
- 持續優化設計標準
- 保持與最新設計趨勢同步

---

**最後更新**：2025年6月11日  
**版本**：v1.0.0  
**適用範圍**：所有管理頁面（/admin/*） 