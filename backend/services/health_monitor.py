"""
系統健康檢查和性能監控模組。

此模組負責：
- 監控系統運行狀態
- 檢查資料庫連接
- 監控API響應時間
- 生成系統健康報告
"""

import sqlite3
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from database import create_connection


class HealthMonitor:
    """系統健康監控器"""
    
    def __init__(self):
        """
        初始化健康監控器。
        """
        self.logger = logging.getLogger(__name__)
        self.start_time = datetime.now()
    
    def check_database_health(self) -> Dict[str, Any]:
        """
        檢查資料庫健康狀態。
        
        返回：
        dict: 包含資料庫健康狀態的字典
        """
        try:
            start_time = time.time()
            conn = create_connection()
            cursor = conn.cursor()
            
            # 測試基本查詢
            cursor.execute("SELECT COUNT(*) FROM employees")
            employee_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM attendance")
            attendance_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM departments")
            department_count = cursor.fetchone()[0]
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            conn.close()
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "employee_count": employee_count,
                "attendance_count": attendance_count,
                "department_count": department_count,
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"資料庫健康檢查失敗: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    def check_system_resources(self) -> Dict[str, Any]:
        """
        檢查系統資源使用情況。
        
        返回：
        dict: 包含系統資源狀態的字典
        """
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 記憶體使用情況
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = round(memory.available / (1024**3), 2)
            
            # 磁碟使用情況
            disk = psutil.disk_usage('/')
            disk_percent = round((disk.used / disk.total) * 100, 2)
            disk_free_gb = round(disk.free / (1024**3), 2)
            
            # 系統運行時間
            uptime = datetime.now() - self.start_time
            uptime_hours = round(uptime.total_seconds() / 3600, 2)
            
            return {
                "status": "healthy" if cpu_percent < 80 and memory_percent < 80 else "warning",
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "memory_available_gb": memory_available_gb,
                "disk_percent": disk_percent,
                "disk_free_gb": disk_free_gb,
                "uptime_hours": uptime_hours,
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"系統資源檢查失敗: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    def check_api_endpoints(self) -> Dict[str, Any]:
        """
        檢查關鍵API端點的健康狀態。
        
        返回：
        dict: 包含API端點狀態的字典
        """
        endpoints = [
            "/api/dashboard/stats",
            "/api/employees",
            "/api/departments",
            "/api/attendance/analysis"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                # 這裡可以添加實際的HTTP請求測試
                # 目前只是模擬檢查
                results[endpoint] = {
                    "status": "healthy",
                    "response_time_ms": 50,  # 模擬響應時間
                    "last_check": datetime.now().isoformat()
                }
            except Exception as e:
                results[endpoint] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "last_check": datetime.now().isoformat()
                }
        
        return results
    
    def get_attendance_statistics(self) -> Dict[str, Any]:
        """
        獲取考勤系統統計數據。
        
        返回：
        dict: 包含考勤統計的字典
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 今日考勤統計
            today = datetime.now().strftime("%Y-%m-%d")
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent
                FROM attendance 
                WHERE DATE(check_in) = ?
            """, (today,))
            
            today_stats = cursor.fetchone()
            
            # 本週考勤統計
            week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).strftime("%Y-%m-%d")
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as normal,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late
                FROM attendance 
                WHERE DATE(check_in) >= ?
            """, (week_start,))
            
            week_stats = cursor.fetchone()
            
            # 待審批請假數量
            cursor.execute("SELECT COUNT(*) FROM leaves WHERE status = 'pending'")
            pending_leaves = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                "today": {
                    "total": today_stats[0] or 0,
                    "normal": today_stats[1] or 0,
                    "late": today_stats[2] or 0,
                    "absent": today_stats[3] or 0
                },
                "this_week": {
                    "total": week_stats[0] or 0,
                    "normal": week_stats[1] or 0,
                    "late": week_stats[2] or 0
                },
                "pending_leaves": pending_leaves,
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"考勤統計獲取失敗: {str(e)}")
            return {
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    def generate_health_report(self) -> Dict[str, Any]:
        """
        生成完整的系統健康報告。
        
        返回：
        dict: 完整的系統健康報告
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "database": self.check_database_health(),
            "system_resources": self.check_system_resources(),
            "api_endpoints": self.check_api_endpoints(),
            "attendance_stats": self.get_attendance_statistics()
        }
        
        # 判斷整體狀態
        if (report["database"]["status"] != "healthy" or 
            report["system_resources"]["status"] == "error"):
            report["overall_status"] = "unhealthy"
        elif report["system_resources"]["status"] == "warning":
            report["overall_status"] = "warning"
        
        return report
    
    def log_health_metrics(self):
        """
        記錄健康指標到日誌。
        """
        report = self.generate_health_report()
        
        self.logger.info(f"系統健康檢查 - 狀態: {report['overall_status']}")
        self.logger.info(f"資料庫響應時間: {report['database'].get('response_time_ms', 'N/A')}ms")
        self.logger.info(f"CPU使用率: {report['system_resources'].get('cpu_percent', 'N/A')}%")
        self.logger.info(f"記憶體使用率: {report['system_resources'].get('memory_percent', 'N/A')}%")
        
        if report["overall_status"] != "healthy":
            self.logger.warning(f"系統健康狀態異常: {report}")


# 全域健康監控器實例
health_monitor = HealthMonitor() 