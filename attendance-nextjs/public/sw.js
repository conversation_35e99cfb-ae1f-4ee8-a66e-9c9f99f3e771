// Han AttendanceOS Service Worker
// 版本號，更新時需要修改
const CACHE_NAME = 'han-attendance-v1.0.0'
const STATIC_CACHE = 'han-attendance-static-v1.0.0'
const DYNAMIC_CACHE = 'han-attendance-dynamic-v1.0.0'

// 需要緩存的靜態資源
const STATIC_ASSETS = [
    '/',
    '/m',
    '/admin',
    '/manifest.json',
    '/icons/icon-192x192.png',
    '/icons/icon-512x512.png',
    '/favicon.ico'
]

// 需要緩存的API端點（用於離線時顯示）
const API_CACHE_PATTERNS = [
    /\/api\/auth\/verify/,
    /\/api\/employees/,
    /\/api\/attendance\/records/
]

// 安裝事件 - 緩存靜態資源
self.addEventListener('install', (event) => {
    console.log('Service Worker: 安裝中...')

    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: 緩存靜態資源')
                return cache.addAll(STATIC_ASSETS)
            })
            .then(() => {
                console.log('Service Worker: 安裝完成')
                return self.skipWaiting()
            })
            .catch((error) => {
                console.error('Service Worker: 安裝失敗', error)
            })
    )
})

// 激活事件 - 清理舊緩存
self.addEventListener('activate', (event) => {
    console.log('Service Worker: 激活中...')

    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: 刪除舊緩存', cacheName)
                            return caches.delete(cacheName)
                        }
                    })
                )
            })
            .then(() => {
                console.log('Service Worker: 激活完成')
                return self.clients.claim()
            })
    )
})

// 攔截網路請求
self.addEventListener('fetch', (event) => {
    const { request } = event
    const url = new URL(request.url)

    // 跳過非GET請求
    if (request.method !== 'GET') {
        return
    }

    // 跳過Chrome擴展請求
    if (url.protocol === 'chrome-extension:') {
        return
    }

    // 處理API請求
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request))
        return
    }

    // 處理靜態資源請求
    event.respondWith(handleStaticRequest(request))
})

// 處理API請求 - 網路優先策略
async function handleApiRequest(request) {
    const url = new URL(request.url)

    try {
        // 嘗試從網路獲取
        const networkResponse = await fetch(request)

        // 如果是可緩存的API，存入緩存
        if (networkResponse.ok && shouldCacheApi(url.pathname)) {
            const cache = await caches.open(DYNAMIC_CACHE)
            cache.put(request, networkResponse.clone())
        }

        return networkResponse
    } catch (error) {
        console.log('Service Worker: API請求失敗，嘗試從緩存獲取', url.pathname)

        // 網路失敗時從緩存獲取
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }

        // 如果緩存也沒有，返回離線頁面或錯誤響應
        return new Response(
            JSON.stringify({
                success: false,
                message: '網路連線中斷，請檢查網路設定',
                offline: true
            }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        }
        )
    }
}

// 處理靜態資源請求 - 緩存優先策略
async function handleStaticRequest(request) {
    try {
        // 先從緩存查找
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }

        // 緩存沒有則從網路獲取
        const networkResponse = await fetch(request)

        // 將新資源加入動態緩存
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE)
            cache.put(request, networkResponse.clone())
        }

        return networkResponse
    } catch (error) {
        console.log('Service Worker: 靜態資源請求失敗', request.url)

        // 如果是頁面請求且失敗，返回離線頁面
        if (request.destination === 'document') {
            const offlineResponse = await caches.match('/')
            if (offlineResponse) {
                return offlineResponse
            }
        }

        // 其他情況返回錯誤
        return new Response('離線模式：資源不可用', {
            status: 503,
            headers: { 'Content-Type': 'text/plain; charset=utf-8' }
        })
    }
}

// 判斷API是否應該被緩存
function shouldCacheApi(pathname) {
    return API_CACHE_PATTERNS.some(pattern => pattern.test(pathname))
}

// 監聽消息事件（用於與主線程通信）
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting()
    }

    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME })
    }
})

// 後台同步事件（用於離線時的數據同步）
self.addEventListener('sync', (event) => {
    console.log('Service Worker: 後台同步事件', event.tag)

    if (event.tag === 'attendance-sync') {
        event.waitUntil(syncAttendanceData())
    }
})

// 同步考勤數據
async function syncAttendanceData() {
    try {
        // 這裡可以實現離線時的數據同步邏輯
        console.log('Service Worker: 同步考勤數據')

        // 獲取離線時存儲的數據
        const offlineData = await getOfflineData()

        if (offlineData.length > 0) {
            // 將離線數據發送到服務器
            for (const data of offlineData) {
                await fetch('/api/attendance/sync', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                })
            }

            // 清除已同步的離線數據
            await clearOfflineData()
            console.log('Service Worker: 數據同步完成')
        }
    } catch (error) {
        console.error('Service Worker: 數據同步失敗', error)
    }
}

// 獲取離線數據（從IndexedDB或localStorage）
async function getOfflineData() {
    // 這裡應該實現從本地存儲獲取離線數據的邏輯
    return []
}

// 清除離線數據
async function clearOfflineData() {
    // 這裡應該實現清除本地離線數據的邏輯
}

console.log('Service Worker: 已載入 Han AttendanceOS Service Worker')