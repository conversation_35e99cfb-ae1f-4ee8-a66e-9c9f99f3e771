#!/bin/bash

# AttendanceOS Git 備份腳本
# 用於快速備份和管理Git倉庫

echo "🚀 AttendanceOS Git 備份腳本"
echo "================================"

# 顯示當前狀態
echo "📊 當前Git狀態:"
git status --short

echo ""
echo "📋 最近的提交記錄:"
git log --oneline -5

echo ""
echo "🏷️  版本標籤:"
git tag -l

echo ""
echo "🌿 分支信息:"
git branch -a

# 檢查是否有未提交的更改
if [[ -n $(git status --porcelain) ]]; then
    echo ""
    echo "⚠️  發現未提交的更改"
    echo "是否要提交這些更改? (y/n)"
    read -r response
    
    if [[ "$response" == "y" || "$response" == "Y" ]]; then
        echo "📝 請輸入提交信息:"
        read -r commit_message
        
        echo "📦 添加所有文件到暫存區..."
        git add .
        
        echo "💾 提交更改..."
        git commit --no-verify -m "$commit_message"
        
        echo "✅ 提交完成!"
    fi
else
    echo ""
    echo "✅ 工作目錄乾淨，沒有未提交的更改"
fi

echo ""
echo "🎯 備份選項:"
echo "1. 創建新的版本標籤"
echo "2. 推送到遠程倉庫 (如果已配置)"
echo "3. 創建本地備份"
echo "4. 顯示詳細信息"
echo "5. 退出"

echo ""
echo "請選擇操作 (1-5):"
read -r choice

case $choice in
    1)
        echo "📝 請輸入版本號 (例如: v1.0.1):"
        read -r version
        echo "📝 請輸入標籤描述:"
        read -r tag_message
        
        git tag -a "$version" -m "$tag_message"
        echo "✅ 版本標籤 $version 創建成功!"
        ;;
    2)
        echo "🌐 檢查遠程倉庫配置..."
        if git remote -v | grep -q origin; then
            echo "📤 推送到遠程倉庫..."
            git push origin main
            git push origin --tags
            echo "✅ 推送完成!"
        else
            echo "❌ 未配置遠程倉庫"
            echo "💡 要配置遠程倉庫，請使用:"
            echo "   git remote add origin <repository-url>"
        fi
        ;;
    3)
        echo "📦 創建本地備份..."
        backup_dir="../attend_backup_$(date +%Y%m%d_%H%M%S)"
        cp -r . "$backup_dir"
        echo "✅ 本地備份創建完成: $backup_dir"
        ;;
    4)
        echo ""
        echo "📊 詳細Git信息:"
        echo "================================"
        echo "🏷️  所有標籤:"
        git tag -l -n
        echo ""
        echo "📝 最近10次提交:"
        git log --oneline -10
        echo ""
        echo "📁 文件統計:"
        git ls-files | wc -l | xargs echo "總文件數:"
        echo ""
        echo "💾 倉庫大小:"
        du -sh .git | cut -f1 | xargs echo "Git倉庫大小:"
        ;;
    5)
        echo "👋 退出備份腳本"
        exit 0
        ;;
    *)
        echo "❌ 無效選擇"
        ;;
esac

echo ""
echo "🎉 Git備份操作完成!"
echo "📚 更多Git操作請參考:"
echo "   - git log --graph --oneline --all  # 查看分支圖"
echo "   - git show <commit-hash>           # 查看特定提交"
echo "   - git diff                         # 查看更改"
echo "   - git stash                        # 暫存更改" 