"""
增強版考勤處理器。

此模組實現完整的考勤整理邏輯：
1. 按照6點換日規則抓取打卡記錄
2. 自動為未打卡員工創建記錄
3. 整合請假資料並計算實際工作時間
4. 計算遲到、早退、加班時間
5. 設置正確的工作日期
6. 自動檢測上次整理日期並批量整理
"""

import logging
from datetime import datetime, timedelta, time, date
from typing import Dict, List, Optional, Tuple, Any, Union
from database import create_connection


class EnhancedAttendanceProcessor:
    """
    增強版考勤處理器類別。
    
    實現完整的考勤整理邏輯，包括：
    - 跨日考勤處理（6點換日）
    - 未打卡員工處理
    - 請假整合
    - 工作時間計算
    - 遲到早退加班計算
    - 自動檢測上次整理日期
    """
    
    def __init__(self):
        """
        初始化考勤處理器。
        """
        self.logger = logging.getLogger(__name__)
        self.day_change_time = time(6, 0)  # 早上6點換日
        self.standard_work_hours = 8.0  # 標準工作時間8小時
    
    def get_last_processed_date(self) -> Optional[str]:
        """
        獲取上次整理的最後日期。
        
        通過查詢 attendance 表中最新的 work_date 來確定上次整理到哪一天。
        
        返回：
        str: 上次整理的最後日期 (YYYY-MM-DD)，如果沒有記錄則返回 None
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 查詢最新的工作日期
            cursor.execute("""
                SELECT MAX(work_date) 
                FROM attendance 
                WHERE work_date IS NOT NULL
            """)
            
            result = cursor.fetchone()
            last_date = result[0] if result and result[0] else None
            
            conn.close()
            
            if last_date:
                self.logger.info(f"📅 檢測到上次整理日期: {last_date}")
            else:
                self.logger.info("📅 未找到之前的整理記錄")
            
            return last_date
            
        except Exception as e:
            self.logger.error(f"獲取上次整理日期時發生錯誤: {e}")
            return None

    def get_dates_to_process(self, end_date: Optional[Union[str, date]] = None) -> List[str]:
        """
        獲取需要處理的日期列表。
        
        從上次整理的日期開始，到指定的結束日期（預設為昨天）。
        
        參數：
        end_date (str 或 date, 可選): 結束日期，可以是字串 (YYYY-MM-DD) 或 date 物件，預設為昨天
        
        返回：
        List[str]: 需要處理的日期列表
        """
        try:
            # 如果沒有指定結束日期，使用昨天
            if not end_date:
                end_date_obj = (datetime.now() - timedelta(days=1)).date()
            elif isinstance(end_date, str):
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            elif isinstance(end_date, date):
                end_date_obj = end_date
            else:
                raise ValueError(f"end_date 必須是字串或 date 物件，收到: {type(end_date)}")
            
            # 獲取上次整理日期
            last_processed_date = self.get_last_processed_date()
            
            if last_processed_date:
                # 從上次整理日期的下一天開始
                start_date_obj = datetime.strptime(last_processed_date, '%Y-%m-%d').date() + timedelta(days=1)
            else:
                # 如果沒有記錄，從30天前開始（可以根據需要調整）
                start_date_obj = datetime.now().date() - timedelta(days=30)
                self.logger.info(f"📅 沒有找到之前的整理記錄，從 {start_date_obj} 開始處理")
            
            # 如果開始日期晚於結束日期，表示已經是最新的
            if start_date_obj > end_date_obj:
                self.logger.info(f"📅 考勤記錄已是最新，無需處理")
                return []
            
            # 生成按時間順序排列的日期列表
            # 確保從最早的日期開始，逐天遞增到最新的日期
            dates_to_process = []
            current_date = start_date_obj
            
            while current_date <= end_date_obj:
                dates_to_process.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
            
            self.logger.info(f"📅 需要處理的日期範圍: {start_date_obj} 到 {end_date_obj} (共 {len(dates_to_process)} 天)")
            self.logger.info(f"📋 處理順序: {' -> '.join(dates_to_process[:3])}{'...' if len(dates_to_process) > 3 else ''}")
            
            return dates_to_process
            
        except Exception as e:
            self.logger.error(f"獲取處理日期列表時發生錯誤: {e}")
            return []

    def process_attendance_batch(self, end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        批量處理考勤整理。
        
        自動檢測上次整理日期，從該日期開始處理到指定結束日期（預設為昨天）。
        
        參數：
        end_date (str, 可選): 結束日期 (YYYY-MM-DD)，預設為昨天
        
        返回：
        Dict: 批量處理結果
        """
        try:
            self.logger.info("🚀 開始批量考勤整理")
            
            # 獲取需要處理的日期列表
            dates_to_process = self.get_dates_to_process(end_date)
            
            if not dates_to_process:
                return {
                    'success': True,
                    'message': '考勤記錄已是最新，無需處理',
                    'total_days': 0,
                    'processed_days': 0,
                    'failed_days': 0,
                    'total_processed': 0,
                    'total_created': 0,
                    'total_updated': 0,
                    'date_range': {
                        'start_date': None,
                        'end_date': None
                    },
                    'daily_results': []
                }
            
            # 按照日期順序逐天處理考勤記錄
            # 例如：從6月1號到6月10號，會依序處理每一天
            # 這確保了資料的時間順序性和完整性
            daily_results = []
            successful_days = 0
            failed_days = 0
            total_processed = 0
            total_created = 0
            total_updated = 0
            
            self.logger.info(f"📋 開始按順序處理 {len(dates_to_process)} 天的考勤記錄")
            
            for i, date_str in enumerate(dates_to_process, 1):
                try:
                    self.logger.info(f"📅 第 {i}/{len(dates_to_process)} 天: 處理 {date_str}")
                    
                    # 處理當天所有員工的考勤記錄
                    result = self.process_daily_attendance(date_str)
                    
                    daily_results.append({
                        'date': date_str,
                        'success': result['success'],
                        'processed_count': result.get('processed_count', 0),
                        'created_count': result.get('created_count', 0),
                        'updated_count': result.get('updated_count', 0),
                        'error': result.get('error') if not result['success'] else None
                    })
                    
                    if result['success']:
                        successful_days += 1
                        total_processed += result.get('processed_count', 0)
                        total_created += result.get('created_count', 0)
                        total_updated += result.get('updated_count', 0)
                    else:
                        failed_days += 1
                        self.logger.error(f"❌ 處理日期 {date_str} 失敗: {result.get('error')}")
                    
                except Exception as e:
                    failed_days += 1
                    error_msg = f"處理日期 {date_str} 時發生錯誤: {e}"
                    self.logger.error(error_msg)
                    
                    daily_results.append({
                        'date': date_str,
                        'success': False,
                        'processed_count': 0,
                        'created_count': 0,
                        'updated_count': 0,
                        'error': error_msg
                    })
            
            # 統計結果
            result_summary = {
                'success': True,
                'message': f'批量考勤整理完成 - 成功: {successful_days} 天, 失敗: {failed_days} 天',
                'total_days': len(dates_to_process),
                'processed_days': successful_days,
                'failed_days': failed_days,
                'total_processed': total_processed,
                'total_created': total_created,
                'total_updated': total_updated,
                'date_range': {
                    'start_date': dates_to_process[0] if dates_to_process else None,
                    'end_date': dates_to_process[-1] if dates_to_process else None
                },
                'daily_results': daily_results
            }
            
            self.logger.info(f"🎉 批量考勤整理完成 - 處理 {len(dates_to_process)} 天，成功 {successful_days} 天，失敗 {failed_days} 天")
            
            return result_summary
            
        except Exception as e:
            error_msg = f"批量處理考勤整理時發生錯誤: {e}"
            self.logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'error': error_msg,
                'total_days': 0,
                'processed_days': 0,
                'failed_days': 0,
                'daily_results': []
            }
    
    def process_daily_attendance(self, target_date: str) -> Dict[str, Any]:
        """
        處理指定日期的考勤整理。
        
        參數：
        target_date (str): 目標日期 (YYYY-MM-DD)
        
        返回：
        Dict: 處理結果
        """
        try:
            self.logger.info(f"🚀 開始處理 {target_date} 的考勤整理")
            
            # 獲取所有在職員工
            employees = self._get_active_employees()
            
            if not employees:
                return {
                    'success': False,
                    'error': '沒有找到在職員工',
                    'total_employees': 0,
                    'processed_count': 0,
                    'created_count': 0,
                    'updated_count': 0
                }
            
            self.logger.info(f"📊 找到 {len(employees)} 位在職員工")
            
            processed_count = 0
            created_count = 0
            updated_count = 0
            
            # 處理每位員工
            for employee in employees:
                employee_id = employee['id']
                employee_name = employee['name']
                
                try:
                    result = self._process_employee_attendance(employee_id, target_date)
                    
                    if result['success']:
                        processed_count += 1
                        if result['action'] == 'created':
                            created_count += 1
                        elif result['action'] == 'updated':
                            updated_count += 1
                    else:
                        self.logger.error(f"❌ 處理員工 {employee_name} ({employee_id}) 時發生錯誤: {result.get('error')}")
                
                except Exception as e:
                    self.logger.error(f"❌ 處理員工 {employee_name} ({employee_id}) 時發生錯誤: {e}")
            
            self.logger.info(f"🎉 考勤整理完成 - 處理: {processed_count}, 新增: {created_count}, 更新: {updated_count}")
            
            return {
                'success': True,
                'total_employees': len(employees),
                'processed_count': processed_count,
                'created_count': created_count,
                'updated_count': updated_count
            }
            
        except Exception as e:
            error_msg = f"處理考勤整理時發生錯誤: {e}"
            self.logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'error': error_msg,
                'total_employees': 0,
                'processed_count': 0,
                'created_count': 0,
                'updated_count': 0
            }

    def _get_active_employees(self) -> List[Dict[str, Any]]:
        """
        獲取所有在職員工列表。
        
        返回：
        List[Dict]: 員工資訊列表
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, employee_id
                FROM employees 
                WHERE status IS NULL OR status IN ('active', 'trial')
                ORDER BY id
            """)
            
            employees = []
            for row in cursor.fetchall():
                employees.append({
                    'id': row[0],
                    'name': row[1],
                    'employee_id': row[2]
                })
            
            conn.close()
            return employees
            
        except Exception as e:
            self.logger.error(f"獲取在職員工列表時發生錯誤: {e}")
            return []

    def _process_employee_attendance(self, employee_id: int, target_date: str) -> Dict[str, Any]:
        """
        處理單個員工的考勤記錄。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        Dict: 處理結果
        """
        try:
            # 檢查是否已有考勤記錄
            existing_record = self._get_existing_attendance(employee_id, target_date)
            
            # 獲取打卡記錄
            punch_records = self._get_punch_records(employee_id, target_date)
            
            # 分析打卡記錄
            check_in, check_out = self._analyze_punch_records(punch_records, target_date)
            
            # 計算工作時間
            work_hours = self._calculate_work_hours(check_in, check_out)
            
            # 獲取請假時間
            leave_hours = self._get_leave_hours(employee_id, target_date)
            
            # 計算實際工作時間（扣除請假）
            actual_work_hours = max(0, work_hours - leave_hours)
            
            # 計算考勤指標（遲到、早退、加班）
            metrics = self._calculate_attendance_metrics(employee_id, check_in, check_out, target_date)
            
            # 確定狀態
            status = self._determine_status(check_in, check_out, metrics['late_minutes'])
            
            # 生成備註
            note = self._generate_note(work_hours, leave_hours, len(punch_records))
            
            if existing_record:
                # 更新現有記錄
                success = self._update_attendance_record(
                    existing_record['id'], check_in, check_out, status,
                    actual_work_hours, leave_hours, metrics, note
                )
                return {
                    'success': success,
                    'action': 'updated' if success else 'failed'
                }
            else:
                # 創建新記錄
                success = self._create_attendance_record(
                    employee_id, target_date, check_in, check_out, status,
                    actual_work_hours, leave_hours, metrics, note
                )
                return {
                    'success': success,
                    'action': 'created' if success else 'failed'
                }
                
        except Exception as e:
            return {
                'success': False,
                'action': 'failed',
                'error': str(e)
            }

    def _get_existing_attendance(self, employee_id: int, target_date: str) -> Optional[Dict[str, Any]]:
        """
        檢查是否已有考勤記錄。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        Dict: 現有記錄資訊，如果沒有則返回 None
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, check_in, check_out, status
                FROM attendance
                WHERE employee_id = ? AND work_date = ?
                LIMIT 1
            """, (employee_id, target_date))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'id': result[0],
                    'check_in': result[1],
                    'check_out': result[2],
                    'status': result[3]
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"檢查現有考勤記錄時發生錯誤: {e}")
            return None

    def _get_punch_records(self, employee_id: int, target_date: str) -> List[Dict[str, Any]]:
        """
        獲取員工指定日期的打卡記錄。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        List[Dict]: 打卡記錄列表
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 計算查詢範圍（前一天18:00到當天18:00）
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
            start_datetime = datetime.combine(target_date_obj - timedelta(days=1), time(18, 0))
            end_datetime = datetime.combine(target_date_obj + timedelta(days=1), time(18, 0))
            
            cursor.execute("""
                SELECT p.punch_datetime, p.status_code, p.device_id
                FROM punch_records p
                JOIN employees e ON p.employee_id = e.employee_id
                WHERE e.id = ? 
                AND p.punch_datetime BETWEEN ? AND ?
                ORDER BY p.punch_datetime
            """, (employee_id, start_datetime, end_datetime))
            
            punch_records = []
            for row in cursor.fetchall():
                punch_records.append({
                    'punch_datetime': datetime.fromisoformat(row[0]) if isinstance(row[0], str) else row[0],
                    'status_code': row[1],
                    'device_id': row[2]
                })
            
            conn.close()
            return punch_records
            
        except Exception as e:
            self.logger.error(f"獲取打卡記錄時發生錯誤: {e}")
            return []

    def _analyze_punch_records(self, punch_records: List[Dict], target_date: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        分析打卡記錄，按照6點換日規則確定上下班時間。
        
        參數：
        punch_records (List[Dict]): 打卡記錄列表
        target_date (str): 目標日期
        
        返回：
        Tuple: (上班時間, 下班時間)
        """
        if not punch_records:
            return None, None
        
        try:
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
            
            # 分類打卡記錄
            today_punches = []
            next_day_punches = []
            
            for record in punch_records:
                punch_time = record['punch_datetime'].time()
                punch_date = record['punch_datetime'].date()
                
                if punch_date == target_date_obj:
                    if punch_time >= self.day_change_time:
                        # 6點後的打卡歸屬今天
                        today_punches.append(record)
                elif punch_date == target_date_obj + timedelta(days=1):
                    if punch_time < self.day_change_time:
                        # 隔天6點前的打卡歸屬今天
                        next_day_punches.append(record)
            
            # 合併並排序
            all_punches = today_punches + next_day_punches
            all_punches.sort(key=lambda x: x['punch_datetime'])
            
            if not all_punches:
                return None, None
            
            # 找出上班時間（第一筆打卡）
            check_in = all_punches[0]['punch_datetime']
            
            # 找出下班時間（最後一筆打卡）
            check_out = all_punches[-1]['punch_datetime'] if len(all_punches) > 1 else None
            
            # 如果只有一筆打卡，根據時間判斷是上班還是下班
            if len(all_punches) == 1:
                punch_time = all_punches[0]['punch_datetime'].time()
                if punch_time < time(12, 0):  # 中午12點前視為上班
                    check_out = None
                else:  # 中午12點後視為下班
                    check_out = check_in
                    check_in = None
            
            return check_in, check_out
            
        except Exception as e:
            self.logger.error(f"分析打卡記錄時發生錯誤: {e}")
            return None, None

    def _calculate_work_hours(self, check_in: Optional[datetime], check_out: Optional[datetime]) -> float:
        """
        計算工作時間。
        
        參數：
        check_in (datetime): 上班時間
        check_out (datetime): 下班時間
        
        返回：
        float: 工作時間（小時）
        """
        if not check_in or not check_out:
            return 0.0
        
        # 計算總工作時間
        work_duration = check_out - check_in
        work_hours = work_duration.total_seconds() / 3600
        
        # 扣除午休時間（1小時）
        if work_hours > 4:  # 工作超過4小時才扣除午休
            work_hours -= 1
        
        return max(0, round(work_hours, 1))

    def _get_leave_hours(self, employee_id: int, target_date: str) -> float:
        """
        獲取員工指定日期的請假時間。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        float: 請假時間（小時）
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT SUM(leave_hours) 
                FROM leaves 
                WHERE employee_id = ? AND status = 'approved'
                AND ? BETWEEN start_date AND end_date
            """, (employee_id, target_date))
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result and result[0] else 0.0
            
        except Exception as e:
            self.logger.error(f"查詢請假時數失敗: {e}")
            return 0.0

    def _calculate_attendance_metrics(self, employee_id: int, check_in: Optional[datetime], 
                                    check_out: Optional[datetime], target_date: str) -> Dict[str, int]:
        """
        計算考勤指標（遲到、早退、加班）。
        
        參數：
        employee_id (int): 員工ID
        check_in (datetime): 上班時間
        check_out (datetime): 下班時間
        target_date (str): 目標日期
        
        返回：
        Dict: 考勤指標
        """
        # 獲取班表資訊（包含容許時間）
        shift_info = self._get_employee_shift_with_tolerance(employee_id, target_date)
        
        if not shift_info:
            return {'late_minutes': 0, 'early_leave_minutes': 0, 'overtime_minutes': 0}
        
        # 計算遲到時間（考慮容許時間）
        late_minutes = 0
        if check_in and shift_info['start_time']:
            expected_start = datetime.combine(check_in.date(), shift_info['start_time'])
            if check_in > expected_start:
                actual_late_minutes = int((check_in - expected_start).total_seconds() / 60)
                # 扣除容許時間
                late_tolerance = shift_info.get('late_tolerance_minutes', 0)
                late_minutes = max(0, actual_late_minutes - late_tolerance)
        
        # 計算早退時間（考慮容許時間）
        early_leave_minutes = 0
        if check_out and shift_info['end_time']:
            expected_end = datetime.combine(check_out.date(), shift_info['end_time'])
            if check_out < expected_end:
                actual_early_minutes = int((expected_end - check_out).total_seconds() / 60)
                # 扣除容許時間
                early_tolerance = shift_info.get('early_leave_tolerance_minutes', 0)
                early_leave_minutes = max(0, actual_early_minutes - early_tolerance)
        
        # 計算加班時間
        overtime_minutes = 0
        if check_out and shift_info['end_time']:
            expected_end = datetime.combine(check_out.date(), shift_info['end_time'])
            if check_out > expected_end:
                overtime_minutes = int((check_out - expected_end).total_seconds() / 60)
        
        return {
            'late_minutes': late_minutes,
            'early_leave_minutes': early_leave_minutes,
            'overtime_minutes': overtime_minutes
        }

    def _get_employee_shift(self, employee_id: int, target_date: str) -> Optional[Dict[str, Any]]:
        """
        獲取員工班表資訊。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        Dict: 班表資訊
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 首先嘗試從 schedules 表查詢
            cursor.execute("""
                SELECT s.start_time, s.end_time, s.name
                FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                WHERE sc.employee_id = ? AND sc.shift_date = ?
            """, (employee_id, target_date))
            
            result = cursor.fetchone()
            
            # 如果 schedules 表中沒有記錄，嘗試從員工的預設班表查詢
            if not result:
                cursor.execute("""
                    SELECT s.start_time, s.end_time, s.name
                    FROM employees e
                    JOIN shifts s ON e.shift_type = s.id
                    WHERE e.id = ?
                """, (employee_id,))
                result = cursor.fetchone()
            
            conn.close()
            
            if result:
                return {
                    'start_time': datetime.strptime(result[0], '%H:%M').time() if result[0] else None,
                    'end_time': datetime.strptime(result[1], '%H:%M').time() if result[1] else None,
                    'name': result[2]
                }
            
            # 如果沒有找到班表，使用標準日班（8:30-17:30）
            return {
                'start_time': time(8, 30),
                'end_time': time(17, 30),
                'name': '標準日班'
            }
            
        except Exception as e:
            self.logger.error(f"獲取員工班表時發生錯誤: {e}")
            return {
                'start_time': time(8, 30),
                'end_time': time(17, 30),
                'name': '標準日班'
            }

    def _get_employee_shift_with_tolerance(self, employee_id: int, target_date: str) -> Optional[Dict[str, Any]]:
        """
        獲取員工班表資訊（包含容許時間）。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 目標日期
        
        返回：
        Dict: 班表資訊（包含容許時間）
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            # 首先嘗試從 schedules 表查詢
            cursor.execute("""
                SELECT s.start_time, s.end_time, s.name, s.late_tolerance_minutes, s.early_leave_tolerance_minutes
                FROM schedules sc
                JOIN shifts s ON sc.shift_id = s.id
                WHERE sc.employee_id = ? AND sc.shift_date = ?
            """, (employee_id, target_date))
            
            result = cursor.fetchone()
            
            # 如果 schedules 表中沒有記錄，嘗試從員工的預設班表查詢
            if not result:
                cursor.execute("""
                    SELECT s.start_time, s.end_time, s.name, s.late_tolerance_minutes, s.early_leave_tolerance_minutes
                    FROM employees e
                    JOIN shifts s ON e.shift_type = s.id
                    WHERE e.id = ?
                """, (employee_id,))
                result = cursor.fetchone()
            
            conn.close()
            
            if result:
                return {
                    'start_time': datetime.strptime(result[0], '%H:%M').time() if result[0] else None,
                    'end_time': datetime.strptime(result[1], '%H:%M').time() if result[1] else None,
                    'name': result[2],
                    'late_tolerance_minutes': result[3] if result[3] is not None else 5,
                    'early_leave_tolerance_minutes': result[4] if result[4] is not None else 5
                }
            
            # 如果沒有找到班表，使用標準日班（8:30-17:30，容許5分鐘）
            return {
                'start_time': time(8, 30),
                'end_time': time(17, 30),
                'name': '標準日班',
                'late_tolerance_minutes': 5,
                'early_leave_tolerance_minutes': 5
            }
            
        except Exception as e:
            self.logger.error(f"獲取員工班表（含容許時間）時發生錯誤: {e}")
            return {
                'start_time': time(8, 30),
                'end_time': time(17, 30),
                'name': '標準日班',
                'late_tolerance_minutes': 5,
                'early_leave_tolerance_minutes': 5
            }

    def _determine_status(self, check_in: Optional[datetime], check_out: Optional[datetime], late_minutes: int) -> str:
        """
        確定考勤狀態。
        
        參數：
        check_in (datetime): 上班時間
        check_out (datetime): 下班時間
        late_minutes (int): 遲到分鐘數
        
        返回：
        str: 考勤狀態
        """
        if not check_in and not check_out:
            return 'absent'
        elif late_minutes > 0:
            return 'late'
        else:
            return 'normal'

    def _generate_note(self, work_hours: float, leave_hours: float, punch_count: int) -> str:
        """
        生成考勤備註。
        
        參數：
        work_hours (float): 工作時間
        leave_hours (float): 請假時間
        punch_count (int): 打卡次數
        
        返回：
        str: 備註內容
        """
        notes = []
        
        if work_hours > 0:
            notes.append(f"工時: {work_hours}小時")
        
        if leave_hours > 0:
            notes.append(f"請假: {leave_hours}小時")
        
        if punch_count > 0:
            notes.append(f"從打卡記錄生成 - 共{punch_count}筆打卡")
        else:
            notes.append("未打卡")
        
        return ", ".join(notes) if notes else ""

    def _create_attendance_record(self, employee_id: int, target_date: str, check_in: Optional[datetime],
                                check_out: Optional[datetime], status: str, work_hours: float,
                                leave_hours: float, metrics: Dict[str, int], note: str) -> bool:
        """
        創建考勤記錄。
        
        參數：
        employee_id (int): 員工ID
        target_date (str): 工作日期
        check_in (datetime): 上班時間
        check_out (datetime): 下班時間
        status (str): 考勤狀態
        work_hours (float): 工作時間
        leave_hours (float): 請假時間
        metrics (Dict): 考勤指標
        note (str): 備註
        
        返回：
        bool: 是否成功創建
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO attendance (
                    employee_id, work_date, check_in, check_out, status, 
                    work_hours, leave_hours, late_minutes, early_leave_minutes, 
                    overtime_minutes, note, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                employee_id, target_date, check_in, check_out, status,
                work_hours, leave_hours, metrics['late_minutes'],
                metrics['early_leave_minutes'], metrics['overtime_minutes'],
                note, datetime.now()
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"創建考勤記錄失敗: {e}")
            return False

    def _update_attendance_record(self, record_id: int, check_in: Optional[datetime],
                                check_out: Optional[datetime], status: str, work_hours: float,
                                leave_hours: float, metrics: Dict[str, int], note: str) -> bool:
        """
        更新考勤記錄。
        
        參數：
        record_id (int): 記錄ID
        check_in (datetime): 上班時間
        check_out (datetime): 下班時間
        status (str): 考勤狀態
        work_hours (float): 工作時間
        leave_hours (float): 請假時間
        metrics (Dict): 考勤指標
        note (str): 備註
        
        返回：
        bool: 是否成功更新
        """
        try:
            conn = create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE attendance SET
                    check_in = ?, check_out = ?, status = ?,
                    work_hours = ?, leave_hours = ?, late_minutes = ?,
                    early_leave_minutes = ?, overtime_minutes = ?, note = ?
                WHERE id = ?
            """, (
                check_in, check_out, status, work_hours, leave_hours,
                metrics['late_minutes'], metrics['early_leave_minutes'],
                metrics['overtime_minutes'], note, record_id
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"更新考勤記錄失敗: {e}")
            return False


# 創建全局實例
enhanced_attendance_processor = EnhancedAttendanceProcessor() 