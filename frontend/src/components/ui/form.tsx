import * as React from "react"
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"
import type { FormField } from "@/types"

export interface FormProps<T = any> {
  initialData?: T
  onSubmit: (data: T) => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  fields: FormField[]
  submitText?: string
  cancelText?: string
  className?: string
}

/**
 * 表單組件 - 遷移自 Han AttendanceOS 設計系統
 * 
 * 功能特色：
 * - 動態欄位生成
 * - 表單驗證
 * - 載入狀態
 * - 錯誤處理
 * - 響應式布局
 */
export function Form<T extends Record<string, any>>({
  initialData = {} as T,
  onSubmit,
  onCancel,
  loading = false,
  fields,
  submitText = "提交",
  cancelText = "取消",
  className
}: FormProps<T>) {
  const [formData, setFormData] = React.useState<T>(initialData)
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  // 更新表單資料
  const updateField = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    // 清除該欄位的錯誤
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // 驗證表單
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    fields.forEach(field => {
      if (field.required && (!formData[field.name] || formData[field.name] === '')) {
        newErrors[field.name] = `${field.label}為必填項目`
      }

      // 自定義驗證
      if (field.validation && formData[field.name]) {
        const validationResult = field.validation(formData[field.name])
        if (validationResult !== true) {
          newErrors[field.name] = validationResult
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交處理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('表單提交失敗:', error)
    }
  }

  // 渲染欄位
  const renderField = (field: FormField) => {
    const hasError = !!errors[field.name]
    const value = formData[field.name] || ''

    const inputClassName = cn(
      "w-full px-3 py-3 border rounded-xl transition-colors",
      "focus:outline-none focus:ring-2 focus:ring-primary-500",
      hasError 
        ? "border-error-300 focus:border-error-500" 
        : "border-neutral-200 focus:border-transparent",
      field.disabled && "bg-neutral-50 cursor-not-allowed opacity-60"
    )

    let inputElement: React.ReactNode

    switch (field.type) {
      case 'textarea':
        inputElement = (
          <textarea
            id={field.name}
            value={value}
            onChange={(e) => updateField(field.name, e.target.value)}
            className={cn(inputClassName, "resize-none")}
            placeholder={field.placeholder}
            required={field.required}
            disabled={field.disabled || loading}
            rows={4}
          />
        )
        break

      case 'select':
        inputElement = (
          <select
            id={field.name}
            value={value}
            onChange={(e) => updateField(field.name, e.target.value)}
            className={inputClassName}
            required={field.required}
            disabled={field.disabled || loading}
          >
            <option value="">{field.placeholder || `請選擇${field.label}`}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )
        break

      case 'date':
      case 'datetime':
      case 'time':
        inputElement = (
          <input
            type={field.type === 'datetime' ? 'datetime-local' : field.type}
            id={field.name}
            value={value}
            onChange={(e) => updateField(field.name, e.target.value)}
            className={inputClassName}
            placeholder={field.placeholder}
            required={field.required}
            disabled={field.disabled || loading}
          />
        )
        break

      default:
        inputElement = (
          <input
            type={field.type}
            id={field.name}
            value={value}
            onChange={(e) => {
              const newValue = field.type === 'number' ? 
                (e.target.value === '' ? '' : Number(e.target.value)) : 
                e.target.value
              updateField(field.name, newValue)
            }}
            className={inputClassName}
            placeholder={field.placeholder}
            required={field.required}
            disabled={field.disabled || loading}
          />
        )
    }

    return (
      <div key={field.name} className="space-y-2">
        <label 
          htmlFor={field.name}
          className="block text-sm font-medium text-neutral-700"
        >
          {field.label}
          {field.required && <span className="text-error-500 ml-1">*</span>}
        </label>
        
        {inputElement}
        
        {field.description && (
          <p className="text-xs text-neutral-500">{field.description}</p>
        )}
        
        {hasError && (
          <div className="flex items-center space-x-1 text-error-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{errors[field.name]}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
      {/* 表單欄位 */}
      {fields.map(renderField)}

      {/* 操作按鈕 */}
      <div className="flex items-center justify-end space-x-4 pt-6">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className="px-6 py-3 border border-neutral-200 rounded-xl text-neutral-700 hover:bg-neutral-50 transition-colors disabled:opacity-50"
          >
            {cancelText}
          </button>
        )}
        <button
          type="submit"
          disabled={loading}
          className={cn(
            "px-6 py-3 bg-gradient-primary text-white rounded-xl font-medium",
            "hover:bg-primary-700 transition-colors disabled:opacity-50",
            "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          )}
        >
          {loading ? '處理中...' : submitText}
        </button>
      </div>
    </form>
  )
}

/**
 * 表單欄位組件 - 用於單獨使用
 */
export interface FormFieldProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
  className?: string
}

export function FormFieldComponent({
  field,
  value,
  onChange,
  error,
  disabled = false,
  className
}: FormFieldProps) {
  const hasError = !!error

  const inputClassName = cn(
    "w-full px-3 py-3 border rounded-xl transition-colors",
    "focus:outline-none focus:ring-2 focus:ring-primary-500",
    hasError 
      ? "border-error-300 focus:border-error-500" 
      : "border-neutral-200 focus:border-transparent",
    (field.disabled || disabled) && "bg-neutral-50 cursor-not-allowed opacity-60"
  )

  let inputElement: React.ReactNode

  switch (field.type) {
    case 'textarea':
      inputElement = (
        <textarea
          id={field.name}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className={cn(inputClassName, "resize-none")}
          placeholder={field.placeholder}
          required={field.required}
          disabled={field.disabled || disabled}
          rows={4}
        />
      )
      break

    case 'select':
      inputElement = (
        <select
          id={field.name}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className={inputClassName}
          required={field.required}
          disabled={field.disabled || disabled}
        >
          <option value="">{field.placeholder || `請選擇${field.label}`}</option>
          {field.options?.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )
      break

    case 'date':
    case 'datetime':
    case 'time':
      inputElement = (
        <input
          type={field.type === 'datetime' ? 'datetime-local' : field.type}
          id={field.name}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className={inputClassName}
          placeholder={field.placeholder}
          required={field.required}
          disabled={field.disabled || disabled}
        />
      )
      break

    default:
      inputElement = (
        <input
          type={field.type}
          id={field.name}
          value={value || ''}
          onChange={(e) => {
            const newValue = field.type === 'number' ? 
              (e.target.value === '' ? '' : Number(e.target.value)) : 
              e.target.value
            onChange(newValue)
          }}
          className={inputClassName}
          placeholder={field.placeholder}
          required={field.required}
          disabled={field.disabled || disabled}
        />
      )
  }

  return (
    <div className={cn("space-y-2", className)}>
      <label 
        htmlFor={field.name}
        className="block text-sm font-medium text-neutral-700"
      >
        {field.label}
        {field.required && <span className="text-error-500 ml-1">*</span>}
      </label>
      
      {inputElement}
      
      {field.description && (
        <p className="text-xs text-neutral-500">{field.description}</p>
      )}
      
      {hasError && (
        <div className="flex items-center space-x-1 text-error-600">
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}