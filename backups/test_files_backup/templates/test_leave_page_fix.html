<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>請假資料載入測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <h1>請假資料載入測試</h1>

    <div id="status" class="status loading">正在測試...</div>

    <h2>測試結果</h2>
    <div id="results"></div>

    <script>
        const API_BASE = '/api';
        let currentEmployee = {
            id: 15,
            name: '鄭政宏',
            employee_id: 'E015'
        };

        async function testLeaveDataLoading() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');

            try {
                statusDiv.textContent = '正在載入請假資料...';
                statusDiv.className = 'status loading';

                // 測試請假記錄API
                console.log('測試請假記錄API...');
                const leavesResponse = await fetch(`${API_BASE}/leaves?employee_id=${currentEmployee.id}`);
                const leavesData = await leavesResponse.json();

                console.log('請假記錄API響應:', leavesData);

                if (!leavesResponse.ok) {
                    throw new Error(`請假記錄API錯誤: ${leavesResponse.status}`);
                }

                const leaves = leavesData.records || [];

                // 測試主管列表API
                console.log('測試主管列表API...');
                const managersResponse = await fetch(`${API_BASE}/employees/managers`);
                const managersData = await managersResponse.json();

                if (!managersResponse.ok) {
                    throw new Error(`主管列表API錯誤: ${managersResponse.status}`);
                }

                const managers = managersData.managers || [];

                // 測試代理人列表API
                console.log('測試代理人列表API...');
                const substitutesResponse = await fetch(`${API_BASE}/employees/substitutes/${currentEmployee.id}`);
                const substitutesData = await substitutesResponse.json();

                if (!substitutesResponse.ok) {
                    throw new Error(`代理人列表API錯誤: ${substitutesResponse.status}`);
                }

                const substitutes = substitutesData.substitutes || [];

                // 測試請假類型API
                console.log('測試請假類型API...');
                const leaveTypesResponse = await fetch(`${API_BASE}/masterdata/leave_types`);
                const leaveTypesData = await leaveTypesResponse.json();

                if (!leaveTypesResponse.ok) {
                    throw new Error(`請假類型API錯誤: ${leaveTypesResponse.status}`);
                }

                const leaveTypes = leaveTypesData.records || [];

                // 顯示成功結果
                statusDiv.textContent = '所有API測試成功！';
                statusDiv.className = 'status success';

                resultsDiv.innerHTML = `
                    <h3>✅ 測試成功</h3>
                    <p><strong>請假記錄:</strong> ${leaves.length} 筆</p>
                    <p><strong>主管列表:</strong> ${managers.length} 位</p>
                    <p><strong>代理人列表:</strong> ${substitutes.length} 位</p>
                    <p><strong>請假類型:</strong> ${leaveTypes.length} 種</p>
                    
                    <h4>請假記錄詳情:</h4>
                    <pre>${JSON.stringify(leaves, null, 2)}</pre>
                    
                    <h4>統計測試:</h4>
                    <p>待審批: ${leaves.filter(leave => leave.status === 'pending').length} 筆</p>
                    <p>已核准: ${leaves.filter(leave => leave.status === 'approved').length} 筆</p>
                    <p>已拒絕: ${leaves.filter(leave => leave.status === 'rejected').length} 筆</p>
                `;

            } catch (error) {
                console.error('測試失敗:', error);
                statusDiv.textContent = `測試失敗: ${error.message}`;
                statusDiv.className = 'status error';

                resultsDiv.innerHTML = `
                    <h3>❌ 測試失敗</h3>
                    <p><strong>錯誤訊息:</strong> ${error.message}</p>
                    <p><strong>詳細錯誤:</strong></p>
                    <pre>${error.stack}</pre>
                `;
            }
        }

        // 頁面載入後自動執行測試
        document.addEventListener('DOMContentLoaded', testLeaveDataLoading);
    </script>
</body>

</html>