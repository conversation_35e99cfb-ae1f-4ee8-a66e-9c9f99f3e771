# 打卡資料匯入功能使用指南

## 📋 功能概述

AttendanceOS Elite 提供了強大的打卡資料匯入功能，可以從打卡機匯出的 txt 檔案中自動匯入考勤記錄。系統支援自動創建新員工，讓您無需手動維護員工清單。

## 🎯 主要特色

### ✅ 自動員工創建
- **智能識別**：當打卡記錄中的員工編號在系統中不存在時，自動創建新員工記錄
- **暫用編號**：新員工的姓名和員工編號暫時使用打卡機編號
- **後續修改**：管理員可以在員工管理頁面手動修改為正確的姓名和資訊

### ✅ 靈活對應機制
- **卡號對應**：優先使用員工檔案中的 `card_number` 欄位進行對應
- **編號轉換**：支援系統員工編號（如 E001）與打卡機編號（如 00000001）的自動轉換
- **重複檢查**：避免重複創建相同的員工記錄

### ✅ 智能記錄配對
- **上下班配對**：自動將同一員工同一天的多筆打卡記錄配對為上下班時間
- **時間判斷**：單筆記錄根據時間（上午/下午）智能判斷為上班或下班
- **設備記錄**：保留打卡機編號，便於追蹤和管理

## 📁 檔案格式說明

### 支援的檔案格式
打卡機匯出的 txt 檔案，每行包含 5 個欄位，以逗號分隔：

```
機器編號,員工編號,日期,時間,狀態
```

### 範例資料
```
011,00000701,20241220,0945,0
011,00000701,20241220,1800,0
011,00000702,20241220,0830,0
011,00000702,20241220,1730,0
```

### 欄位說明
| 欄位 | 說明 | 範例 |
|------|------|------|
| 機器編號 | 打卡機的識別編號 | 011 |
| 員工編號 | 員工的打卡卡號 | 00000701 |
| 日期 | 打卡日期 (YYYYMMDD) | 20241220 |
| 時間 | 打卡時間 (HHMM) | 0945 |
| 狀態 | 打卡狀態碼 | 0 |

## 🚀 使用方法

### 命令列匯入
```bash
# 基本匯入
python import_attendance_data.py download/20241220.txt

# 覆蓋現有記錄
python import_attendance_data.py download/20241220.txt --overwrite

# 不自動創建員工
python import_attendance_data.py download/20241220.txt --no-auto-create

# 詳細輸出
python import_attendance_data.py download/20241220.txt --verbose
```

### 參數說明
| 參數 | 說明 |
|------|------|
| `file_path` | 打卡資料檔案路徑（必填） |
| `--overwrite` | 覆蓋現有的考勤記錄 |
| `--no-auto-create` | 停用自動創建員工功能 |
| `--verbose` | 顯示詳細的處理過程 |

## 📊 匯入流程

### 1. 檔案解析
- 讀取 txt 檔案
- 驗證格式正確性
- 轉換日期時間格式

### 2. 員工對應
- 查詢現有員工的 `card_number`
- 建立打卡機編號與系統員工ID的對應表
- 如找不到對應員工且啟用自動創建，則創建新員工

### 3. 記錄配對
- 按員工和日期分組打卡記錄
- 配對上下班時間
- 處理單次打卡的情況

### 4. 資料匯入
- 檢查重複記錄
- 插入或更新考勤記錄
- 記錄打卡機編號和處理資訊

## 👥 員工管理

### 自動創建的員工特徵
- **姓名**：使用打卡機員工編號（如 "00000701"）
- **員工編號**：使用打卡機員工編號
- **卡號**：記錄在 `card_number` 欄位
- **部門**：分配到第一個部門
- **職位**：設為 "待確認"
- **狀態**：設為 "active"

### 後續處理步驟
1. **登入管理系統**：訪問 `http://localhost:7072/elite/employees`
2. **查找新員工**：搜尋姓名為數字編號的員工
3. **編輯資訊**：
   - 修改姓名為真實姓名
   - 調整部門分配
   - 更新職位資訊
   - 補充聯絡資訊
4. **保存變更**：確認修改並保存

## 📈 匯入結果

### 成功訊息範例
```
✅ 匯入成功！
   成功匯入: 15 筆
   跳過記錄: 2 筆
   錯誤記錄: 0 筆
   新增員工: 3 筆
   總計處理: 17 筆

💡 提醒：已自動創建 3 筆新員工記錄
   請到員工管理頁面手動修改這些員工的姓名和其他資訊
```

### 結果說明
- **成功匯入**：成功處理並匯入的考勤記錄數
- **跳過記錄**：已存在或無法處理的記錄數
- **錯誤記錄**：處理失敗的記錄數
- **新增員工**：自動創建的新員工數
- **總計處理**：總共處理的考勤記錄數

## ⚠️ 注意事項

### 資料庫鎖定
- 如果 Flask 應用程式正在運行，可能會遇到資料庫鎖定
- 建議在匯入大量資料前暫停應用程式

### 重複匯入
- 預設不會覆蓋現有記錄
- 使用 `--overwrite` 參數可以覆蓋現有記錄
- 建議先備份資料庫

### 員工編號格式
- 系統支援多種員工編號格式
- 建議在員工檔案中設定 `card_number` 欄位以確保正確對應

## 🔧 故障排除

### 常見問題

#### 1. 檔案格式錯誤
**問題**：`第 X 行格式錯誤，跳過`
**解決**：檢查檔案格式，確保每行有 5 個欄位，以逗號分隔

#### 2. 找不到員工對應
**問題**：`找不到員工編號對應: XXXXXXXX`
**解決**：
- 檢查員工檔案中的 `card_number` 欄位
- 或啟用自動創建員工功能

#### 3. 資料庫鎖定
**問題**：`database is locked`
**解決**：
- 停止 Flask 應用程式
- 等待一段時間後重試
- 檢查是否有其他程式正在使用資料庫

#### 4. 日期時間格式錯誤
**問題**：`日期時間格式錯誤`
**解決**：確保日期為 8 位數字（YYYYMMDD），時間為 4 位數字（HHMM）

## 📞 技術支援

如果遇到問題，請：
1. 檢查日誌輸出中的錯誤訊息
2. 使用 `--verbose` 參數獲取詳細資訊
3. 確認檔案格式符合規範
4. 檢查資料庫連接狀態

---

**版本**：AttendanceOS Elite v1.0  
**更新日期**：2025-06-03  
**文檔狀態**：✅ 完整 