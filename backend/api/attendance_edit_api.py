#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
考勤編輯API模組

功能說明：
- 考勤記錄編輯功能
- 班表修改功能
- 請假記錄編輯功能
- 時間修改功能
"""

from flask import Blueprint, request, jsonify
from database import create_connection
import sqlite3
import logging
from datetime import datetime, time
from services.attendance_service import AttendanceService
from services.employee_service import EmployeeService

# 設定日誌
logger = logging.getLogger(__name__)

# 創建藍圖
attendance_edit_bp = Blueprint('attendance_edit_api', __name__)


@attendance_edit_bp.route("/api/attendance/edit/<int:record_id>", methods=["GET"])
def get_attendance_edit_detail(record_id):
    """
    獲取考勤記錄編輯詳情
    
    參數：
    record_id (int): 考勤記錄ID
    
    返回：
    - 考勤記錄詳情（包含班表資訊、請假資訊等）
    """
    try:
        logger.info(f"開始查詢考勤記錄編輯詳情 - record_id: {record_id}")
        
        # 參數驗證
        if record_id <= 0:
            logger.warning(f"無效的記錄ID: {record_id}")
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢考勤記錄詳情
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.work_date,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.note,
                a.created_at,
                a.shift_id,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN shifts s ON a.shift_id = s.id
            WHERE a.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            logger.warning(f"找不到記錄ID為 {record_id} 的考勤記錄")
            return jsonify({
                "success": False,
                "error": "找不到指定的考勤記錄"
            }), 404
        
        # 轉換為字典格式
        record_dict = dict(record)
        
        # 計算加班時數（從分鐘轉換為小時）
        if record_dict['overtime_minutes']:
            record_dict['overtime_hours'] = round(record_dict['overtime_minutes'] / 60, 2)
        else:
            record_dict['overtime_hours'] = 0
        
        # 查詢可用班表列表
        cursor.execute("""
            SELECT id, name, start_time, end_time, description
            FROM shifts
            WHERE is_active = 1
            ORDER BY name
        """)
        available_shifts = [dict(row) for row in cursor.fetchall()]
        
        # 查詢請假類型列表（從 leave_types 表獲取中文名稱）
        cursor.execute("""
            SELECT name, code
            FROM leave_types
            WHERE is_active = 1
            ORDER BY name
        """)
        leave_types_data = cursor.fetchall()
        
        # 構建請假類型列表（包含中文名稱和代碼的對應關係）
        leave_types = []
        leave_type_mapping = {}
        
        for row in leave_types_data:
            name, code = row
            leave_types.append({"name": name, "code": code})
            leave_type_mapping[code] = name
        
        # 如果沒有請假類型，提供預設選項
        if not leave_types:
            default_types = [
                {"name": "病假", "code": "sick"},
                {"name": "事假", "code": "personal"},
                {"name": "年假", "code": "annual"},
                {"name": "公假", "code": "official"},
                {"name": "喪假", "code": "bereavement"},
                {"name": "婚假", "code": "marriage"},
                {"name": "產假", "code": "maternity"},
                {"name": "陪產假", "code": "paternity"},
                {"name": "補休", "code": "compensatory"}
            ]
            leave_types = default_types
        
        # 查詢該員工當天的請假記錄
        cursor.execute("""
            SELECT l.id, l.leave_type, l.start_date, l.end_date, l.leave_hours, l.reason, l.status,
                   COALESCE(lt.name, l.leave_type) as leave_type_name
            FROM leaves l
            LEFT JOIN leave_types lt ON l.leave_type = lt.code
            WHERE l.employee_id = ? AND ? BETWEEN l.start_date AND l.end_date
            ORDER BY l.created_at DESC
        """, (record_dict['employee_id'], record_dict['work_date']))
        
        leave_records = [dict(row) for row in cursor.fetchall()]
        
        logger.info(f"成功查詢考勤記錄編輯詳情 - record_id: {record_id}, employee: {record_dict['employee_name']}")
        
        return jsonify({
            "success": True,
            "record": record_dict,
            "available_shifts": available_shifts,
            "leave_types": leave_types,
            "leave_records": leave_records
        })
        
    except Exception as e:
        logger.error(f"查詢考勤記錄編輯詳情時發生錯誤: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"查詢考勤記錄編輯詳情失敗: {str(e)}"
        }), 500
    
    finally:
        if conn:
            conn.close()


@attendance_edit_bp.route("/api/attendance/edit/<int:record_id>", methods=["PUT"])
def update_attendance_record(record_id):
    """
    更新考勤記錄
    
    參數：
    record_id (int): 考勤記錄ID
    
    請求體：
    {
        "check_in": "2025-06-05 08:30:00",  // 上班時間
        "check_out": "2025-06-05 17:30:00", // 下班時間
        "shift_id": 1,                       // 班表ID
        "leave_type": "病假",                // 請假類型
        "leave_hours": 4.0,                  // 請假時數
        "leave_reason": "身體不適",          // 請假原因
        "note": "備註"                       // 備註
    }
    
    返回：
    - 更新結果
    """
    try:
        logger.info(f"開始更新考勤記錄 - record_id: {record_id}")
        
        # 參數驗證
        if record_id <= 0:
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # 獲取請求資料
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "請提供更新資料"
            }), 400
        
        # 資料庫連接
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查記錄是否存在
        cursor.execute("SELECT * FROM attendance WHERE id = ?", (record_id,))
        existing_record = cursor.fetchone()
        
        if not existing_record:
            return jsonify({
                "success": False,
                "error": "找不到指定的考勤記錄"
            }), 404
        
        # 準備更新資料
        update_fields = []
        update_values = []
        
        # 處理上班時間
        if 'check_in' in data:
            check_in = data['check_in']
            if check_in:
                try:
                    # 驗證時間格式
                    datetime.strptime(check_in, '%Y-%m-%d %H:%M:%S')
                    update_fields.append("check_in = ?")
                    update_values.append(check_in)
                except ValueError:
                    return jsonify({
                        "success": False,
                        "error": "上班時間格式錯誤，請使用 YYYY-MM-DD HH:MM:SS 格式"
                    }), 400
            else:
                update_fields.append("check_in = ?")
                update_values.append(None)
        
        # 處理下班時間
        if 'check_out' in data:
            check_out = data['check_out']
            if check_out:
                try:
                    # 驗證時間格式
                    datetime.strptime(check_out, '%Y-%m-%d %H:%M:%S')
                    update_fields.append("check_out = ?")
                    update_values.append(check_out)
                except ValueError:
                    return jsonify({
                        "success": False,
                        "error": "下班時間格式錯誤，請使用 YYYY-MM-DD HH:MM:SS 格式"
                    }), 400
            else:
                update_fields.append("check_out = ?")
                update_values.append(None)
        
        # 處理請假時數
        if 'leave_hours' in data:
            leave_hours = data['leave_hours']
            if leave_hours is not None:
                try:
                    leave_hours = float(leave_hours)
                    if leave_hours < 0:
                        return jsonify({
                            "success": False,
                            "error": "請假時數不能為負數"
                        }), 400
                    update_fields.append("leave_hours = ?")
                    update_values.append(leave_hours)
                except (ValueError, TypeError):
                    return jsonify({
                        "success": False,
                        "error": "請假時數格式錯誤"
                    }), 400
            else:
                update_fields.append("leave_hours = ?")
                update_values.append(0)
        
        # 處理備註
        if 'note' in data:
            update_fields.append("note = ?")
            update_values.append(data['note'] or '')
        
        # 處理班表更新（如果提供了shift_id）
        if 'shift_id' in data:
            shift_id = data['shift_id']
            if shift_id:
                # 驗證班表是否存在
                cursor.execute("SELECT id FROM shifts WHERE id = ?", (shift_id,))
                if not cursor.fetchone():
                    return jsonify({
                        "success": False,
                        "error": "指定的班表不存在"
                    }), 400
                
                # 直接更新考勤記錄的班表ID
                update_fields.append("shift_id = ?")
                update_values.append(shift_id)
            else:
                # 如果 shift_id 為空，設為 NULL
                update_fields.append("shift_id = ?")
                update_values.append(None)
        
        # 處理請假記錄（如果提供了請假資訊）
        if any(key in data for key in ['leave_type', 'leave_reason']):
            leave_type = data.get('leave_type')
            leave_reason = data.get('leave_reason', '')
            leave_hours = data.get('leave_hours', 0)
            
            if leave_type and leave_hours > 0:
                work_date = existing_record[6]  # work_date 欄位
                employee_id = existing_record[1]  # employee_id 欄位
                
                # 檢查該記錄是否有實際出勤記錄（有打卡時間）
                current_check_in = data.get('check_in') or existing_record[2]  # check_in 欄位
                current_check_out = data.get('check_out') or existing_record[3]  # check_out 欄位
                
                if current_check_in and current_check_out:
                    return jsonify({
                        "success": False,
                        "error": "該員工當天已有完整出勤記錄（上下班打卡），無法添加請假記錄。如需請假，請先清除打卡時間。"
                    }), 400
                
                # 檢查是否已有當天的請假記錄
                cursor.execute("""
                    SELECT id FROM leaves 
                    WHERE employee_id = ? AND start_date <= ? AND end_date >= ?
                    ORDER BY created_at DESC LIMIT 1
                """, (employee_id, work_date, work_date))
                
                existing_leave = cursor.fetchone()
                
                if existing_leave:
                    # 更新現有請假記錄
                    cursor.execute("""
                        UPDATE leaves 
                        SET leave_type = ?, reason = ?, leave_hours = ?, 
                            status = 'approved', updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (leave_type, leave_reason, leave_hours, existing_leave[0]))
                else:
                    # 創建新請假記錄
                    cursor.execute("""
                        INSERT INTO leaves 
                        (employee_id, leave_type, start_date, end_date, 
                         leave_hours, reason, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, 'approved', CURRENT_TIMESTAMP)
                    """, (employee_id, leave_type, work_date, work_date, 
                          leave_hours, leave_reason))
        
        # 執行考勤記錄更新
        if update_fields:
            update_query = f"UPDATE attendance SET {', '.join(update_fields)} WHERE id = ?"
            update_values.append(record_id)
            
            cursor.execute(update_query, update_values)
        
        # 重新計算考勤指標
        recalculate_attendance_metrics(cursor, record_id)
        
        conn.commit()
        
        logger.info(f"成功更新考勤記錄 - record_id: {record_id}")
        
        return jsonify({
            "success": True,
            "message": "考勤記錄更新成功"
        })
        
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"更新考勤記錄時發生錯誤: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"更新考勤記錄失敗: {str(e)}"
        }), 500
    
    finally:
        if conn:
            conn.close()


def recalculate_attendance_metrics(cursor, record_id):
    """
    重新計算考勤指標（遲到、早退、加班、工時）
    
    參數：
    cursor: 資料庫游標
    record_id: 考勤記錄ID
    """
    try:
        # 獲取考勤記錄和班表資訊（包含容許時間和當天起算時間）
        cursor.execute("""
            SELECT 
                a.id, a.employee_id, a.check_in, a.check_out, a.work_date, a.leave_hours, a.shift_id,
                s.start_time, s.end_time, s.late_tolerance_minutes, s.early_leave_tolerance_minutes, s.day_start_time
            FROM attendance a
            LEFT JOIN shifts s ON a.shift_id = s.id
            WHERE a.id = ?
        """, (record_id,))
        
        record = cursor.fetchone()
        if not record:
            return
        
        (att_id, employee_id, check_in, check_out, work_date, leave_hours, shift_id,
         shift_start_time, shift_end_time, late_tolerance_minutes, early_leave_tolerance_minutes, day_start_time) = record
        
        # 初始化指標
        late_minutes = 0
        early_leave_minutes = 0
        overtime_minutes = 0
        work_hours = 0
        status = 'normal'
        
        # 重新計算請假小時（只有在沒有完整出勤記錄時才計算）
        if employee_id and work_date:
            # 如果有完整的上下班打卡記錄，則忽略請假記錄
            if check_in and check_out:
                leave_hours = 0.0
            else:
                cursor.execute("""
                    SELECT SUM(
                        CASE 
                            WHEN time_type = 'full_day' THEN 8.0
                            WHEN time_type = 'partial_day' AND start_time IS NOT NULL AND end_time IS NOT NULL THEN
                                (CAST(SUBSTR(end_time, 1, 2) AS INTEGER) * 60 + CAST(SUBSTR(end_time, 4, 2) AS INTEGER) -
                                 CAST(SUBSTR(start_time, 1, 2) AS INTEGER) * 60 - CAST(SUBSTR(start_time, 4, 2) AS INTEGER)) / 60.0
                            ELSE 0
                        END
                    ) as total_leave_hours
                    FROM leaves 
                    WHERE employee_id = ? 
                        AND status = 'approved' 
                        AND ? BETWEEN start_date AND end_date
                """, (employee_id, work_date))
                
                result = cursor.fetchone()
                leave_hours = float(result[0]) if result and result[0] else 0.0
        
        # 如果有上下班時間，計算工時
        if check_in and check_out:
            check_in_dt = datetime.strptime(check_in, '%Y-%m-%d %H:%M:%S')
            check_out_dt = datetime.strptime(check_out, '%Y-%m-%d %H:%M:%S')
            
            # TODO: 實現跨日邏輯
            # 如果有day_start_time，需要檢查打卡時間是否在當天起算時間之前
            # 如果是，則該打卡記錄應歸屬前一天
            
            # 計算總工時（扣除請假時數）
            total_seconds = (check_out_dt - check_in_dt).total_seconds()
            work_hours = max(0, total_seconds / 3600 - (leave_hours or 0))
            
            # 如果有班表資訊，計算遲到早退加班
            if shift_start_time and shift_end_time:
                # 解析班表時間（統一使用 %H:%M 格式）
                try:
                    shift_start = datetime.strptime(shift_start_time, '%H:%M').time()
                    shift_end = datetime.strptime(shift_end_time, '%H:%M').time()
                except ValueError:
                    # 如果是 %H:%M:%S 格式，則使用該格式
                    shift_start = datetime.strptime(shift_start_time, '%H:%M:%S').time()
                    shift_end = datetime.strptime(shift_end_time, '%H:%M:%S').time()
                
                # 構建當天的班表時間
                work_date_obj = datetime.strptime(work_date, '%Y-%m-%d').date()
                expected_start = datetime.combine(work_date_obj, shift_start)
                expected_end = datetime.combine(work_date_obj, shift_end)
                
                # 計算遲到（考慮容許時間）
                if check_in_dt > expected_start:
                    actual_late_minutes = (check_in_dt - expected_start).total_seconds() / 60
                    # 扣除容許時間
                    late_tolerance = late_tolerance_minutes or 5
                    late_minutes = max(0, int(actual_late_minutes - late_tolerance))
                
                # 計算早退（考慮容許時間）
                if check_out_dt < expected_end:
                    actual_early_minutes = (expected_end - check_out_dt).total_seconds() / 60
                    # 扣除容許時間
                    early_tolerance = early_leave_tolerance_minutes or 5
                    early_leave_minutes = max(0, int(actual_early_minutes - early_tolerance))
                
                # 計算加班
                if check_out_dt > expected_end:
                    overtime_minutes = int((check_out_dt - expected_end).total_seconds() / 60)
        
        # 確定狀態
        if not check_in and not check_out:
            if leave_hours and leave_hours >= 8:
                status = 'leave'
            else:
                status = 'absent'
        elif late_minutes > 0:
            status = 'late'
        elif early_leave_minutes > 0:
            status = 'early_leave'
        elif leave_hours and leave_hours > 0:
            status = 'leave'
        else:
            status = 'normal'
        
        # 更新考勤記錄
        cursor.execute("""
            UPDATE attendance 
            SET work_hours = ?, leave_hours = ?, late_minutes = ?, early_leave_minutes = ?, 
                overtime_minutes = ?, status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (work_hours, leave_hours, late_minutes, early_leave_minutes, overtime_minutes, status, record_id))
        
        logger.debug(f"重新計算考勤指標完成 - record_id: {record_id}, work_hours: {work_hours}, "
                    f"late: {late_minutes}, early_leave: {early_leave_minutes}, overtime: {overtime_minutes}")
        
    except Exception as e:
        logger.error(f"重新計算考勤指標時發生錯誤: {e}", exc_info=True) 


@attendance_edit_bp.route('/api/attendance/shift-details/<int:record_id>', methods=['GET'])
def get_shift_details(record_id):
    """
    獲取指定考勤記錄的換班所需詳細資訊。

    Args:
        record_id (int): 考勤記錄的 ID。

    Returns:
        JSON: 包含考勤記錄詳情和所有可用班別的 JSON 物件。
    """
    try:
        logger.info(f"開始查詢換班詳細資訊 - record_id: {record_id}")
        
        # 查詢考勤記錄
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        record_query = """
            SELECT 
                a.id, a.work_date, a.shift_id,
                e.name as employee_name, e.employee_id as employee_code
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            WHERE a.id = ?
        """
        record = conn.execute(record_query, (record_id,)).fetchone()
        
        if not record:
            logger.warning(f"找不到指定的考勤記錄 - record_id: {record_id}")
            return jsonify({"error": "找不到指定的考勤記錄"}), 404

        # 查詢所有班別
        shifts = conn.execute('SELECT * FROM shifts ORDER BY name').fetchall()
        conn.close()

        logger.info(f"成功查詢換班詳細資訊 - record_id: {record_id}, employee: {record['employee_name']}")
        
        return jsonify({
            "success": True,
            "record": dict(record),
            "available_shifts": [dict(s) for s in shifts]
        })

    except Exception as e:
        logger.error(f"查詢換班詳細資訊時發生錯誤 - record_id: {record_id}, error: {e}", exc_info=True)
        return jsonify({"error": "伺服器內部錯誤"}), 500


@attendance_edit_bp.route('/api/attendance/edit/<int:record_id>', methods=['GET'])
def get_attendance_edit_details(record_id):
    """
    獲取考勤記錄編輯詳情
    
    參數：
    record_id (int): 考勤記錄ID
    
    返回：
    - 考勤記錄詳情（包含班表資訊、請假資訊等）
    """
    try:
        logger.info(f"開始查詢考勤記錄編輯詳情 - record_id: {record_id}")
        
        # 參數驗證
        if record_id <= 0:
            logger.warning(f"無效的記錄ID: {record_id}")
            return jsonify({
                "success": False,
                "error": "記錄ID必須是正整數"
            }), 400
        
        # 資料庫連接
        conn = create_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查詢考勤記錄詳情
        query = """
            SELECT 
                a.id,
                a.employee_id,
                a.check_in,
                a.check_out,
                a.status,
                a.work_date,
                a.work_hours,
                a.leave_hours,
                a.late_minutes,
                a.early_leave_minutes,
                a.overtime_minutes,
                a.note,
                a.created_at,
                e.name as employee_name,
                e.employee_id as employee_code,
                e.phone as employee_phone,
                e.email as employee_email,
                d.name as department_name,
                d.id as department_id,
                s.name as shift_name,
                s.start_time as shift_start_time,
                s.end_time as shift_end_time,
                s.id as shift_id
            FROM attendance a
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN schedules sc ON sc.employee_id = e.id AND sc.shift_date = a.work_date
            LEFT JOIN shifts s ON sc.shift_id = s.id
            WHERE a.id = ?
        """
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            logger.warning(f"找不到記錄ID為 {record_id} 的考勤記錄")
            return jsonify({
                "success": False,
                "error": "找不到指定的考勤記錄"
            }), 404
        
        # 轉換為字典格式
        record_dict = dict(record)
        
        # 計算加班時數（從分鐘轉換為小時）
        if record_dict['overtime_minutes']:
            record_dict['overtime_hours'] = round(record_dict['overtime_minutes'] / 60, 2)
        else:
            record_dict['overtime_hours'] = 0
        
        # 查詢可用班表列表
        cursor.execute("""
            SELECT id, name, start_time, end_time, description
            FROM shifts
            WHERE is_active = 1
            ORDER BY name
        """)
        available_shifts = [dict(row) for row in cursor.fetchall()]
        
        # 查詢請假類型列表（從 leave_types 表獲取中文名稱）
        cursor.execute("""
            SELECT name, code
            FROM leave_types
            WHERE is_active = 1
            ORDER BY name
        """)
        leave_types_data = cursor.fetchall()
        
        # 構建請假類型列表（包含中文名稱和代碼的對應關係）
        leave_types = []
        leave_type_mapping = {}
        
        for row in leave_types_data:
            name, code = row
            leave_types.append({"name": name, "code": code})
            leave_type_mapping[code] = name
        
        # 如果沒有請假類型，提供預設選項
        if not leave_types:
            default_types = [
                {"name": "病假", "code": "sick"},
                {"name": "事假", "code": "personal"},
                {"name": "年假", "code": "annual"},
                {"name": "公假", "code": "official"},
                {"name": "喪假", "code": "bereavement"},
                {"name": "婚假", "code": "marriage"},
                {"name": "產假", "code": "maternity"},
                {"name": "陪產假", "code": "paternity"},
                {"name": "補休", "code": "compensatory"}
            ]
            leave_types = default_types
        
        # 查詢該員工當天的請假記錄
        cursor.execute("""
            SELECT l.id, l.leave_type, l.start_date, l.end_date, l.leave_hours, l.reason, l.status,
                   COALESCE(lt.name, l.leave_type) as leave_type_name
            FROM leaves l
            LEFT JOIN leave_types lt ON l.leave_type = lt.code
            WHERE l.employee_id = ? AND ? BETWEEN l.start_date AND l.end_date
            ORDER BY l.created_at DESC
        """, (record_dict['employee_id'], record_dict['work_date']))
        
        leave_records = [dict(row) for row in cursor.fetchall()]
        
        logger.info(f"成功查詢考勤記錄編輯詳情 - record_id: {record_id}, employee: {record_dict['employee_name']}")
        
        return jsonify({
            "success": True,
            "record": record_dict,
            "available_shifts": available_shifts,
            "leave_types": leave_types,
            "leave_records": leave_records
        })
        
    except Exception as e:
        logger.error(f"查詢考勤記錄編輯詳情時發生錯誤: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"查詢考勤記錄編輯詳情失敗: {str(e)}"
        }), 500
    
    finally:
        if conn:
            conn.close()


@attendance_edit_bp.route('/api/attendance/update-shift/<int:record_id>', methods=['POST'])
def update_shift(record_id):
    """
    更新指定考勤記錄的班表。

    Args:
        record_id (int): 考勤記錄的 ID。

    Returns:
        JSON: 更新結果。
    """
    try:
        logger.info(f"開始更新班表 - record_id: {record_id}")
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "請提供有效的JSON資料"}), 400
            
        shift_id = data.get('shift_id')
        if not shift_id:
            return jsonify({"error": "請提供班表ID"}), 400

        # 查詢考勤記錄是否存在
        conn = create_connection()
        record_query = "SELECT * FROM attendance WHERE id = ?"
        record = conn.execute(record_query, (record_id,)).fetchone()
        
        if not record:
            logger.warning(f"找不到指定的考勤記錄 - record_id: {record_id}")
            return jsonify({"error": "找不到指定的考勤記錄"}), 404

        # 查詢班表是否存在
        shift_query = "SELECT * FROM shifts WHERE id = ?"
        shift = conn.execute(shift_query, (shift_id,)).fetchone()
        
        if not shift:
            logger.warning(f"找不到指定的班表 - shift_id: {shift_id}")
            return jsonify({"error": "找不到指定的班表"}), 404

        # 更新考勤記錄的班表
        update_query = "UPDATE attendance SET shift_id = ? WHERE id = ?"
        conn.execute(update_query, (shift_id, record_id))
        conn.commit()
        
        # 重新計算考勤指標
        recalculate_attendance_metrics(record_id, conn)
        
        conn.close()

        logger.info(f"成功更新班表 - record_id: {record_id}, new_shift_id: {shift_id}")
        
        return jsonify({
            "success": True,
            "message": "班表更新成功",
            "record_id": record_id,
            "shift_id": shift_id
        })

    except Exception as e:
        logger.error(f"更新班表時發生錯誤 - record_id: {record_id}, error: {e}", exc_info=True)
        return jsonify({"error": "伺服器內部錯誤"}), 500


def recalculate_attendance_metrics(record_id, conn):
    """
    重新計算考勤記錄的指標（遲到、早退、加班等）。

    Args:
        record_id (int): 考勤記錄的 ID。
        conn: 資料庫連接。
    """
    try:
        # 獲取考勤記錄和班表資訊
        query = """
            SELECT a.*, s.start_time, s.end_time, s.late_tolerance_minutes, s.early_leave_tolerance_minutes
            FROM attendance a
            LEFT JOIN shifts s ON a.shift_id = s.id
            WHERE a.id = ?
        """
        result = conn.execute(query, (record_id,)).fetchone()
        
        if not result:
            logger.warning(f"重新計算時找不到考勤記錄 - record_id: {record_id}")
            return
            
        # 如果result是tuple，轉換為dict
        if isinstance(result, tuple):
            columns = [desc[0] for desc in conn.execute(query, (record_id,)).description]
            record = dict(zip(columns, result))
        else:
            record = dict(result)

        # 如果沒有班表資訊或沒有打卡時間，跳過計算
        if not record.get('start_time') or not record.get('check_in'):
            logger.info(f"跳過計算 - 缺少班表或打卡時間 - record_id: {record_id}")
            return

        from datetime import datetime, time
        
        # 解析時間
        check_in_time = datetime.strptime(record['check_in'], '%Y-%m-%d %H:%M:%S').time()
        
        # 支援多種時間格式
        start_time_str = record['start_time']
        if len(start_time_str.split(':')) == 2:
            start_time_str += ':00'
        shift_start = datetime.strptime(start_time_str, '%H:%M:%S').time()
        
        # 計算遲到分鐘
        late_tolerance_minutes = record.get('late_tolerance_minutes', 0) or 0
        
        check_in_minutes = check_in_time.hour * 60 + check_in_time.minute
        shift_start_minutes = shift_start.hour * 60 + shift_start.minute
        late_minutes = max(0, check_in_minutes - shift_start_minutes - late_tolerance_minutes)

        # 計算早退和加班（如果有下班時間）
        early_leave_minutes = 0
        overtime_minutes = 0
        
        if record.get('check_out'):
            check_out_time = datetime.strptime(record['check_out'], '%Y-%m-%d %H:%M:%S').time()
            
            end_time_str = record['end_time']
            if len(end_time_str.split(':')) == 2:
                end_time_str += ':00'
            shift_end = datetime.strptime(end_time_str, '%H:%M:%S').time()
            
            check_out_minutes = check_out_time.hour * 60 + check_out_time.minute
            shift_end_minutes = shift_end.hour * 60 + shift_end.minute
            early_leave_tolerance_minutes = record.get('early_leave_tolerance_minutes', 0) or 0
            
            if check_out_minutes < shift_end_minutes:
                early_leave_minutes = max(0, shift_end_minutes - check_out_minutes - early_leave_tolerance_minutes)
            elif check_out_minutes > shift_end_minutes:
                overtime_minutes = check_out_minutes - shift_end_minutes

        # 更新考勤記錄
        update_query = """
            UPDATE attendance 
            SET late_minutes = ?, early_leave_minutes = ?, overtime_minutes = ?
            WHERE id = ?
        """
        conn.execute(update_query, (late_minutes, early_leave_minutes, overtime_minutes, record_id))
        conn.commit()

        logger.info(f"重新計算完成 - record_id: {record_id}, late: {late_minutes}, early_leave: {early_leave_minutes}, overtime: {overtime_minutes}")

    except Exception as e:
        logger.error(f"重新計算考勤指標時發生錯誤 - record_id: {record_id}, error: {e}", exc_info=True) 