# 🚀 Han AttendanceOS 快速參考

## 🔥 最重要的配置（避免重複錯誤）

### 資料庫欄位名稱
```
✅ 正確：photo_url
❌ 錯誤：avatar_url

✅ 正確：employees.position  
❌ 錯誤：roles.name (roles表不存在)
```

### 服務器端口
```
Flask:   http://localhost:7072
Next.js: http://localhost:7075
```

### API 測試命令
```bash
# 測試 Profile API
curl -X GET "http://localhost:7072/api/profile/E001"
```

### 啟動命令
```bash
# 1. 啟動 Flask
python app.py

# 2. 啟動 Next.js  
cd attendance-nextjs && npm run dev
```

### 端口清理
```bash
# 清理被佔用的端口
lsof -ti:7075 | xargs kill -9
lsof -ti:7072 | xargs kill -9
```

## 📱 手機版頁面路徑
- 主頁：http://localhost:7075/m
- 設定：http://localhost:7075/m/settings
- 登錄：http://localhost:7075/m/login

---
**記住：先檢查文檔，再動手修改！** 

## 🚀 系統啟動

### 標準啟動順序
```bash
# 1. 啟動Flask API (端口7072)
python app.py

# 2. 啟動Next.js前端 (端口7075)
cd attendance-nextjs && npm run dev
```

### 端口衝突解決
```bash
# 終止佔用端口的程序
lsof -ti:7072 | xargs kill -9  # Flask API
lsof -ti:7075 | xargs kill -9  # Next.js
```

## 📱 手機版登入問題診斷

### "Load failed" 錯誤診斷步驟

1. **檢查Flask API狀態**
```bash
curl -X POST "http://localhost:7072/api/login" -H "Content-Type: application/json" -d '{"username":"E001","password":"password123"}'
```

2. **檢查網路IP訪問**
```bash
curl -X POST "http://**************:7072/api/login" -H "Content-Type: application/json" -d '{"username":"E001","password":"password123"}'
```

3. **檢查Next.js服務器**
```bash
ps aux | grep "next dev" | grep -v grep
```

4. **檢查頁面訪問**
```bash
curl -I "http://localhost:7075/m/login"
```

### 常見解決方案

- **API連接失敗**: 確保Flask API在0.0.0.0:7072監聽
- **網路訪問問題**: 檢查防火牆設置
- **URL配置錯誤**: 使用動態API URL檢測
- **前端錯誤**: 查看瀏覽器控制台調試信息

### 測試功能

手機版登入頁面現已包含"🧪 測試API連接"按鈕，可快速診斷API連接問題。

## 🔧 常見問題

### 資料庫欄位對應
- 頭像欄位：`photo_url` (不是 `avatar_url`)
- 職位資訊：直接存在 `employees.position` (沒有獨立的 `roles` 表)

### API端點
- 登入：`POST /api/login`
- 健康檢查：`GET /api/health`
- 個人資料：`GET /api/profile/{employee_id}`

### 測試帳號
- 帳號：E001
- 密碼：password123

## 📊 系統狀態檢查

### 快速狀態檢查
```bash
# 檢查所有服務狀態
curl -s http://localhost:7072/api/health | jq .
curl -I http://localhost:7075/m
```

### 日誌查看
- Flask API：直接在終端查看
- Next.js：瀏覽器開發者工具控制台

## 🎯 開發提醒

1. **避免重複錯誤**：每次修改前先查看相關文檔
2. **API測試**：使用curl測試API端點
3. **錯誤處理**：添加詳細的調試信息
4. **文檔更新**：及時更新技術文檔 