/**
 * 考勤輔助函數庫
 * 整合考勤相關的重複邏輯和工具函數
 * 
 * <AUTHOR> AttendanceOS
 * @version v2005.6.12
 */

class AttendanceHelper {

    /**
     * 考勤狀態配置
     */
    static STATUS_CONFIG = {
        normal: {
            text: '正常',
            color: 'text-green-600',
            bgColor: 'bg-green-100',
            icon: 'check-circle'
        },
        late: {
            text: '遲到',
            color: 'text-yellow-600',
            bgColor: 'bg-yellow-100',
            icon: 'clock'
        },
        early_leave: {
            text: '早退',
            color: 'text-orange-600',
            bgColor: 'bg-orange-100',
            icon: 'log-out'
        },
        absent: {
            text: '缺勤',
            color: 'text-red-600',
            bgColor: 'bg-red-100',
            icon: 'x-circle'
        },
        leave: {
            text: '請假',
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
            icon: 'calendar-x'
        },
        overtime: {
            text: '加班',
            color: 'text-purple-600',
            bgColor: 'bg-purple-100',
            icon: 'clock'
        },
        manual: {
            text: '手動',
            color: 'text-gray-600',
            bgColor: 'bg-gray-100',
            icon: 'edit'
        }
    };

    /**
     * 時間格式配置
     */
    static TIME_FORMAT = {
        DATE: 'YYYY-MM-DD',
        TIME: 'HH:mm',
        DATETIME: 'YYYY-MM-DD HH:mm',
        DISPLAY_DATE: 'MM/DD',
        DISPLAY_TIME: 'HH:mm',
        DISPLAY_DATETIME: 'MM/DD HH:mm'
    };

    /**
     * 格式化日期時間
     * @param {string|Date} dateTime - 日期時間
     * @param {string} format - 格式類型
     * @returns {string} 格式化後的字符串
     */
    static formatDateTime(dateTime, format = 'DISPLAY_DATETIME') {
        if (!dateTime) return '-';

        const date = new Date(dateTime);
        if (isNaN(date.getTime())) return '-';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        switch (format) {
            case 'DATE':
            case this.TIME_FORMAT.DATE:
                return `${year}-${month}-${day}`;

            case 'TIME':
            case this.TIME_FORMAT.TIME:
                return `${hours}:${minutes}`;

            case 'DATETIME':
            case this.TIME_FORMAT.DATETIME:
                return `${year}-${month}-${day} ${hours}:${minutes}`;

            case 'DISPLAY_DATE':
            case this.TIME_FORMAT.DISPLAY_DATE:
                return `${month}/${day}`;

            case 'DISPLAY_TIME':
            case this.TIME_FORMAT.DISPLAY_TIME:
                return `${hours}:${minutes}`;

            case 'DISPLAY_DATETIME':
            case this.TIME_FORMAT.DISPLAY_DATETIME:
            default:
                return `${month}/${day} ${hours}:${minutes}`;
        }
    }

    /**
     * 格式化考勤狀態
     * @param {string} status - 狀態代碼
     * @param {Object} options - 選項
     * @returns {Object} 格式化後的狀態信息
     */
    static formatStatus(status, options = {}) {
        const config = this.STATUS_CONFIG[status] || this.STATUS_CONFIG.normal;
        const showIcon = options.showIcon !== false;
        const showBadge = options.showBadge === true;

        const result = {
            text: config.text,
            color: config.color,
            bgColor: config.bgColor,
            icon: config.icon,
            status: status
        };

        if (showBadge) {
            result.html = `
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.color}">
                    ${showIcon ? `<i data-lucide="${config.icon}" class="w-3 h-3 mr-1"></i>` : ''}
                    ${config.text}
                </span>
            `;
        } else {
            result.html = `
                <span class="inline-flex items-center ${config.color}">
                    ${showIcon ? `<i data-lucide="${config.icon}" class="w-4 h-4 mr-1"></i>` : ''}
                    ${config.text}
                </span>
            `;
        }

        return result;
    }

    /**
     * 計算工作時數
     * @param {string} checkIn - 上班時間
     * @param {string} checkOut - 下班時間
     * @param {number} breakMinutes - 休息時間（分鐘）
     * @returns {number} 工作時數
     */
    static calculateWorkHours(checkIn, checkOut, breakMinutes = 60) {
        if (!checkIn || !checkOut) return 0;

        const startTime = new Date(checkIn);
        const endTime = new Date(checkOut);

        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) return 0;
        if (startTime >= endTime) return 0;

        // 計算總分鐘數
        const totalMinutes = (endTime - startTime) / (1000 * 60);

        // 扣除休息時間
        const workMinutes = totalMinutes - breakMinutes;

        // 轉換為小時並保留一位小數
        return Math.max(0, Math.round(workMinutes / 60 * 10) / 10);
    }

    /**
     * 計算遲到分鐘數
     * @param {string} checkIn - 實際上班時間
     * @param {string} scheduledStart - 預定上班時間
     * @returns {number} 遲到分鐘數
     */
    static calculateLateMinutes(checkIn, scheduledStart = '09:00') {
        if (!checkIn) return 0;

        const actualTime = new Date(checkIn);
        const workDate = this.formatDateTime(actualTime, 'DATE');
        const scheduledTime = new Date(`${workDate}T${scheduledStart}`);

        if (isNaN(actualTime.getTime()) || isNaN(scheduledTime.getTime())) return 0;

        const diffMinutes = (actualTime - scheduledTime) / (1000 * 60);
        return Math.max(0, Math.round(diffMinutes));
    }

    /**
     * 計算早退分鐘數
     * @param {string} checkOut - 實際下班時間
     * @param {string} scheduledEnd - 預定下班時間
     * @returns {number} 早退分鐘數
     */
    static calculateEarlyLeaveMinutes(checkOut, scheduledEnd = '18:00') {
        if (!checkOut) return 0;

        const actualTime = new Date(checkOut);
        const workDate = this.formatDateTime(actualTime, 'DATE');
        const scheduledTime = new Date(`${workDate}T${scheduledEnd}`);

        if (isNaN(actualTime.getTime()) || isNaN(scheduledTime.getTime())) return 0;

        const diffMinutes = (scheduledTime - actualTime) / (1000 * 60);
        return Math.max(0, Math.round(diffMinutes));
    }

    /**
     * 判斷考勤狀態
     * @param {Object} record - 考勤記錄
     * @returns {string} 狀態代碼
     */
    static determineStatus(record) {
        const { check_in, check_out, leave_hours, scheduled_start = '09:00', scheduled_end = '18:00' } = record;

        // 如果有請假時數，優先顯示請假
        if (leave_hours && leave_hours > 0) {
            return 'leave';
        }

        // 如果沒有上下班記錄，判定為缺勤
        if (!check_in && !check_out) {
            return 'absent';
        }

        // 如果只有上班沒有下班，檢查是否遲到
        if (check_in && !check_out) {
            const lateMinutes = this.calculateLateMinutes(check_in, scheduled_start);
            return lateMinutes > 5 ? 'late' : 'normal';
        }

        // 如果有完整的上下班記錄
        if (check_in && check_out) {
            const lateMinutes = this.calculateLateMinutes(check_in, scheduled_start);
            const earlyMinutes = this.calculateEarlyLeaveMinutes(check_out, scheduled_end);

            if (lateMinutes > 5) return 'late';
            if (earlyMinutes > 5) return 'early_leave';
            return 'normal';
        }

        return 'normal';
    }

    /**
     * 格式化工作時數顯示
     * @param {number} hours - 時數
     * @returns {string} 格式化後的時數字符串
     */
    static formatWorkHours(hours) {
        if (!hours || hours === 0) return '0小時';

        const wholeHours = Math.floor(hours);
        const minutes = Math.round((hours - wholeHours) * 60);

        if (minutes === 0) {
            return `${wholeHours}小時`;
        } else {
            return `${wholeHours}小時${minutes}分`;
        }
    }

    /**
     * 生成考勤記錄摘要
     * @param {Object} record - 考勤記錄
     * @returns {Object} 摘要信息
     */
    static generateSummary(record) {
        const status = this.determineStatus(record);
        const statusInfo = this.formatStatus(status, { showBadge: true });
        const workHours = this.calculateWorkHours(record.check_in, record.check_out);
        const lateMinutes = this.calculateLateMinutes(record.check_in);
        const earlyMinutes = this.calculateEarlyLeaveMinutes(record.check_out);

        return {
            status: status,
            statusInfo: statusInfo,
            workHours: workHours,
            workHoursText: this.formatWorkHours(workHours),
            lateMinutes: lateMinutes,
            earlyMinutes: earlyMinutes,
            checkInTime: this.formatDateTime(record.check_in, 'DISPLAY_TIME'),
            checkOutTime: this.formatDateTime(record.check_out, 'DISPLAY_TIME'),
            workDate: this.formatDateTime(record.work_date || record.check_in, 'DISPLAY_DATE'),
            leaveHours: record.leave_hours || 0,
            leaveHoursText: this.formatWorkHours(record.leave_hours || 0)
        };
    }

    /**
     * 創建考勤記錄卡片HTML
     * @param {Object} record - 考勤記錄
     * @param {Object} options - 選項
     * @returns {string} HTML字符串
     */
    static createRecordCard(record, options = {}) {
        const summary = this.generateSummary(record);
        const showActions = options.showActions !== false;
        const compact = options.compact === true;

        const cardClass = compact ? 'p-3' : 'p-4';
        const titleClass = compact ? 'text-sm' : 'text-base';

        return `
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 ${cardClass} hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center space-x-2">
                        <span class="${titleClass} font-medium text-gray-900">${summary.workDate}</span>
                        ${summary.statusInfo.html}
                    </div>
                    ${showActions ? `
                        <div class="flex items-center space-x-1">
                            <button class="text-gray-400 hover:text-gray-600 transition-colors" onclick="AttendanceHelper.editRecord(${record.id})">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
                
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-500">上班時間</span>
                        <div class="font-medium">${summary.checkInTime}</div>
                    </div>
                    <div>
                        <span class="text-gray-500">下班時間</span>
                        <div class="font-medium">${summary.checkOutTime}</div>
                    </div>
                    <div>
                        <span class="text-gray-500">工作時數</span>
                        <div class="font-medium text-blue-600">${summary.workHoursText}</div>
                    </div>
                    ${summary.leaveHours > 0 ? `
                        <div>
                            <span class="text-gray-500">請假時數</span>
                            <div class="font-medium text-orange-600">${summary.leaveHoursText}</div>
                        </div>
                    ` : `
                        <div>
                            <span class="text-gray-500">狀態</span>
                            <div class="font-medium">${summary.statusInfo.text}</div>
                        </div>
                    `}
                </div>
                
                ${summary.lateMinutes > 0 || summary.earlyMinutes > 0 ? `
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            ${summary.lateMinutes > 0 ? `<span>遲到 ${summary.lateMinutes} 分鐘</span>` : ''}
                            ${summary.earlyMinutes > 0 ? `<span>早退 ${summary.earlyMinutes} 分鐘</span>` : ''}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 驗證考勤時間
     * @param {string} checkIn - 上班時間
     * @param {string} checkOut - 下班時間
     * @returns {Object} 驗證結果
     */
    static validateAttendanceTime(checkIn, checkOut) {
        const errors = [];

        if (checkIn) {
            const checkInTime = new Date(checkIn);
            if (isNaN(checkInTime.getTime())) {
                errors.push('上班時間格式無效');
            }
        }

        if (checkOut) {
            const checkOutTime = new Date(checkOut);
            if (isNaN(checkOutTime.getTime())) {
                errors.push('下班時間格式無效');
            }
        }

        if (checkIn && checkOut) {
            const checkInTime = new Date(checkIn);
            const checkOutTime = new Date(checkOut);

            if (checkInTime >= checkOutTime) {
                errors.push('下班時間必須晚於上班時間');
            }

            // 檢查工作時數是否合理（不超過24小時）
            const workHours = this.calculateWorkHours(checkIn, checkOut);
            if (workHours > 24) {
                errors.push('工作時數不能超過24小時');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 獲取考勤統計信息
     * @param {Array} records - 考勤記錄列表
     * @returns {Object} 統計信息
     */
    static getStatistics(records) {
        const stats = {
            total: records.length,
            normal: 0,
            late: 0,
            early_leave: 0,
            absent: 0,
            leave: 0,
            totalWorkHours: 0,
            totalLeaveHours: 0,
            averageWorkHours: 0
        };

        records.forEach(record => {
            const status = this.determineStatus(record);
            stats[status] = (stats[status] || 0) + 1;

            const workHours = this.calculateWorkHours(record.check_in, record.check_out);
            stats.totalWorkHours += workHours;

            if (record.leave_hours) {
                stats.totalLeaveHours += record.leave_hours;
            }
        });

        stats.averageWorkHours = stats.total > 0 ?
            Math.round(stats.totalWorkHours / stats.total * 10) / 10 : 0;

        return stats;
    }

    /**
     * 編輯考勤記錄（佔位符函數）
     * @param {number} recordId - 記錄ID
     */
    static editRecord(recordId) {
        // 這個函數應該由具體頁面實現
        console.log('編輯考勤記錄:', recordId);
        if (window.editAttendanceRecord) {
            window.editAttendanceRecord(recordId);
        }
    }

    /**
     * 獲取當前日期範圍
     * @param {string} period - 期間類型 ('today', 'week', 'month')
     * @returns {Object} 日期範圍
     */
    static getDateRange(period = 'today') {
        const today = new Date();
        const startDate = new Date(today);
        const endDate = new Date(today);

        switch (period) {
            case 'week':
                const dayOfWeek = today.getDay();
                const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                startDate.setDate(today.getDate() + mondayOffset);
                endDate.setDate(startDate.getDate() + 6);
                break;

            case 'month':
                startDate.setDate(1);
                endDate.setMonth(endDate.getMonth() + 1, 0);
                break;

            case 'today':
            default:
                // 保持今天的日期
                break;
        }

        return {
            startDate: this.formatDateTime(startDate, 'DATE'),
            endDate: this.formatDateTime(endDate, 'DATE'),
            startDateObj: startDate,
            endDateObj: endDate
        };
    }
}

// 導出為全域變數
window.AttendanceHelper = AttendanceHelper;

// 提供簡化的全域函數（向後兼容）
window.formatDateTime = function (dateTime, format) {
    return AttendanceHelper.formatDateTime(dateTime, format);
};

window.formatAttendanceStatus = function (status, options) {
    return AttendanceHelper.formatStatus(status, options);
};

window.calculateWorkHours = function (checkIn, checkOut, breakMinutes) {
    return AttendanceHelper.calculateWorkHours(checkIn, checkOut, breakMinutes);
};