#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考勤整理執行器
執行考勤整理功能，處理今天的打卡記錄並生成考勤記錄
"""

import requests
import json
import time
from datetime import datetime
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:7072"

def get_today_date():
    """獲取今天的日期"""
    return datetime.now().strftime('%Y-%m-%d')

def execute_attendance_processing():
    """執行考勤整理"""
    today = get_today_date()
    
    logger.info("🚀 開始執行考勤整理")
    
    # 準備請求資料
    processing_data = {
        "date_range": {
            "start_date": today,
            "end_date": today
        },
        "employee_scope": {
            "type": "all"
        },
        "processing_options": {
            "calculate_late_early": True,
            "calculate_overtime": True,
            "integrate_leaves": True,
            "overwrite_existing": True
        }
    }
    
    try:
        # 發送考勤整理請求
        logger.info(f"📤 發送考勤整理請求: {today}")
        response = requests.post(
            f"{BASE_URL}/api/attendance/processing/execute",
            json=processing_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                processing_id = result.get('processing_id')
                logger.info(f"✅ 考勤整理已啟動，處理ID: {processing_id}")
                
                # 監控處理進度
                monitor_processing_status(processing_id)
                
            else:
                logger.error(f"❌ 考勤整理啟動失敗: {result.get('error')}")
        else:
            logger.error(f"❌ API 請求失敗: {response.status_code}")
            logger.error(f"   錯誤內容: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ 執行考勤整理時發生錯誤: {str(e)}")

def monitor_processing_status(processing_id):
    """監控處理狀態"""
    logger.info(f"👀 開始監控處理狀態: {processing_id}")
    
    max_attempts = 30  # 最多等待30次
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{BASE_URL}/api/attendance/processing/status/{processing_id}")
            
            if response.status_code == 200:
                status_data = response.json()
                
                if status_data.get('success'):
                    status = status_data.get('status', {})
                    progress = status.get('progress', 0)
                    current_step = status.get('current_step', '')
                    is_completed = status.get('completed', False)
                    
                    logger.info(f"📊 處理進度: {progress}% - {current_step}")
                    
                    if is_completed:
                        logger.info("🎉 考勤整理完成！")
                        
                        # 顯示處理結果
                        results = status.get('results', {})
                        logger.info("📈 處理結果:")
                        logger.info(f"   處理員工數: {results.get('processed_employees', 0)}")
                        logger.info(f"   處理天數: {results.get('processed_days', 0)}")
                        logger.info(f"   生成記錄數: {results.get('generated_records', 0)}")
                        logger.info(f"   更新記錄數: {results.get('updated_records', 0)}")
                        logger.info(f"   錯誤數: {results.get('errors', 0)}")
                        
                        break
                    
                else:
                    logger.warning(f"⚠️ 獲取狀態失敗: {status_data.get('error')}")
            else:
                logger.warning(f"⚠️ 狀態查詢失敗: {response.status_code}")
            
            attempt += 1
            time.sleep(2)  # 等待2秒後再次查詢
            
        except Exception as e:
            logger.error(f"❌ 監控狀態時發生錯誤: {str(e)}")
            break
    
    if attempt >= max_attempts:
        logger.warning("⏰ 監控超時，請手動檢查處理狀態")

def check_attendance_records():
    """檢查生成的考勤記錄"""
    today = get_today_date()
    
    logger.info("🔍 檢查今天的考勤記錄")
    
    try:
        # 查詢今天的考勤記錄
        response = requests.get(
            f"{BASE_URL}/api/attendance/management",
            params={
                'start_date': today,
                'end_date': today,
                'limit': 50
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                records = data.get('records', [])
                statistics = data.get('statistics', {})
                
                logger.info(f"📊 今天的考勤記錄統計:")
                logger.info(f"   總記錄數: {len(records)}")
                logger.info(f"   正常: {statistics.get('status_breakdown', {}).get('normal', 0)}")
                logger.info(f"   遲到: {statistics.get('status_breakdown', {}).get('late', 0)}")
                logger.info(f"   早退: {statistics.get('status_breakdown', {}).get('early_leave', 0)}")
                logger.info(f"   加班: {statistics.get('status_breakdown', {}).get('overtime', 0)}")
                logger.info(f"   請假: {statistics.get('status_breakdown', {}).get('leave', 0)}")
                logger.info(f"   曠職: {statistics.get('status_breakdown', {}).get('absent', 0)}")
                
                # 顯示加班記錄詳情
                overtime_records = [r for r in records if r.get('overtime_hours', 0) > 0]
                if overtime_records:
                    logger.info(f"\n⏰ 加班記錄詳情 ({len(overtime_records)} 筆):")
                    for record in overtime_records:
                        logger.info(f"   {record.get('employee_name')} - {record.get('shift_name')}")
                        logger.info(f"      上班: {record.get('check_in', '').split(' ')[1] if record.get('check_in') else '未打卡'}")
                        logger.info(f"      下班: {record.get('check_out', '').split(' ')[1] if record.get('check_out') else '未打卡'}")
                        logger.info(f"      加班: {record.get('overtime_hours', 0)} 小時")
                        if record.get('late_minutes', 0) > 0:
                            logger.info(f"      遲到: {record.get('late_minutes')} 分鐘")
                        if record.get('early_leave_minutes', 0) > 0:
                            logger.info(f"      早退: {record.get('early_leave_minutes')} 分鐘")
                
            else:
                logger.error(f"❌ 查詢考勤記錄失敗: {data.get('error')}")
        else:
            logger.error(f"❌ API 請求失敗: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ 檢查考勤記錄時發生錯誤: {str(e)}")

def main():
    """主函數"""
    logger.info("🎯 考勤整理執行器啟動")
    
    # 1. 執行考勤整理
    execute_attendance_processing()
    
    # 2. 等待一下讓處理完成
    time.sleep(3)
    
    # 3. 檢查生成的考勤記錄
    check_attendance_records()
    
    logger.info("✨ 考勤整理執行完成")

if __name__ == "__main__":
    main() 