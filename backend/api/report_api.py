"""
報表分析API模組

包含所有報表分析相關的API端點：
- 儀表板統計
- 考勤報表
- 分析報表
- 資料匯出
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import sqlite3
import logging
import json

# 創建藍圖
report_bp = Blueprint('report', __name__)

# 設置日誌
logger = logging.getLogger(__name__)

# 導入資料庫連接函數
from database import create_connection

@report_bp.route("/api/dashboard/stats", methods=["GET"])
def get_dashboard_stats():
    """
    獲取儀表板統計資料
    
    返回：
    - 員工總數、部門總數、今日出勤率等統計資料
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取員工總數
        cursor.execute("SELECT COUNT(*) FROM employees WHERE is_active = 1")
        total_employees = cursor.fetchone()[0]
        
        # 獲取部門總數
        cursor.execute("SELECT COUNT(*) FROM departments WHERE is_active = 1")
        total_departments = cursor.fetchone()[0]
        
        # 獲取今日出勤人數
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) 
            FROM attendance 
            WHERE DATE(check_in) = ?
        """, (today,))
        today_attendance = cursor.fetchone()[0]
        
        # 計算出勤率
        attendance_rate = (today_attendance / total_employees * 100) if total_employees > 0 else 0
        
        # 獲取今日請假人數
        cursor.execute("""
            SELECT COUNT(*) 
            FROM leaves 
            WHERE status = 'approved' 
            AND start_date <= ? 
            AND end_date >= ?
        """, (today, today))
        today_leaves = cursor.fetchone()[0]
        
        # 獲取待審核請假申請
        cursor.execute("""
            SELECT COUNT(*) 
            FROM leaves 
            WHERE status = 'pending'
        """, )
        pending_leaves = cursor.fetchone()[0]
        
        # 獲取本月加班時數
        current_month = datetime.now().strftime('%Y-%m')
        cursor.execute("""
            SELECT COALESCE(0, 0) 
            FROM attendance 
            WHERE strftime('%Y-%m', check_in) = ?
        """, (current_month,))
        monthly_overtime = cursor.fetchone()[0]
        
        stats = {
            "total_employees": total_employees,
            "total_departments": total_departments,
            "today_attendance": today_attendance,
            "attendance_rate": round(attendance_rate, 1),
            "today_leaves": today_leaves,
            "pending_leaves": pending_leaves,
            "monthly_overtime": round(monthly_overtime, 1),
            "last_updated": datetime.now().isoformat()
        }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"獲取儀表板統計失敗: {e}")
        return jsonify({"error": "獲取統計資料失敗"}), 500
    finally:
        conn.close()


@report_bp.route("/api/dashboard/reports", methods=["GET"])
def get_dashboard_reports():
    """
    獲取儀表板報表資料
    
    返回：
    - 最近的考勤記錄、請假申請、異常記錄等
    """
    conn = create_connection()
    try:
        cursor = conn.cursor()
        
        # 獲取最近的考勤記錄
        cursor.execute("""
            SELECT ar.id, ar.employee_id, e.name as employee_name, 
                   ar.check_in, ar.check_out, ar.status
            FROM attendance ar
            JOIN employees e ON ar.employee_id = e.id
            ORDER BY ar.check_in DESC
            LIMIT 10
        """)
        
        recent_attendance = []
        for row in cursor.fetchall():
            record = {
                "id": row[0],
                "employee_id": row[1],
                "employee_name": row[2],
                "check_in": row[3],
                "check_out": row[4],
                "status": row[5]
            }
            recent_attendance.append(record)
        
        # 獲取最近的請假申請
        cursor.execute("""
            SELECT lr.id, lr.employee_id, e.name as employee_name,
                   lr.start_date, lr.end_date, lr.status, lt.name as leave_type
            FROM leaves lr
            JOIN employees e ON lr.employee_id = e.id
            JOIN leave_types lt ON lr.leave_type = lt.code
            ORDER BY lr.created_at DESC
            LIMIT 10
        """)
        
        recent_leaves = []
        for row in cursor.fetchall():
            record = {
                "id": row[0],
                "employee_id": row[1],
                "employee_name": row[2],
                "start_date": row[3],
                "end_date": row[4],
                "status": row[5],
                "leave_type": row[6]
            }
            recent_leaves.append(record)
        
        # 獲取異常考勤記錄
        cursor.execute("""
            SELECT ar.id, ar.employee_id, e.name as employee_name,
                   ar.check_in, ar.status
            FROM attendance ar
            JOIN employees e ON ar.employee_id = e.id
            WHERE ar.status IN ('late', 'early_leave', 'absent')
            ORDER BY ar.check_in DESC
            LIMIT 10
        """)
        
        abnormal_records = []
        for row in cursor.fetchall():
            record = {
                "id": row[0],
                "employee_id": row[1],
                "employee_name": row[2],
                "check_in": row[3],
                "status": row[4]
            }
            abnormal_records.append(record)
        
        reports = {
            "recent_attendance": recent_attendance,
            "recent_leaves": recent_leaves,
            "abnormal_records": abnormal_records,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(reports)
        
    except Exception as e:
        logger.error(f"獲取儀表板報表失敗: {e}")
        return jsonify({"error": "獲取報表資料失敗"}), 500
    finally:
        conn.close()


@report_bp.route("/api/reports/export", methods=["GET"])
def export_reports():
    """
    匯出報表資料
    
    查詢參數：
    - type: 報表類型（attendance/leave/overtime）
    - start_date: 開始日期
    - end_date: 結束日期
    - format: 匯出格式（json/csv）
    
    返回：
    - 匯出的報表資料
    """
    try:
        report_type = request.args.get('type', 'attendance')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        export_format = request.args.get('format', 'json')
        
        if not start_date or not end_date:
            return jsonify({"error": "請提供開始日期和結束日期"}), 400
        
        conn = create_connection()
        cursor = conn.cursor()
        
        if report_type == 'attendance':
            # 考勤報表
            cursor.execute("""
                SELECT ar.id, ar.employee_id, e.name as employee_name,
                       ar.check_in, ar.check_out, CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END,
                       0, ar.status
                FROM attendance ar
                JOIN employees e ON ar.employee_id = e.id
                WHERE DATE(ar.check_in) BETWEEN ? AND ?
                ORDER BY ar.check_in
            """, (start_date, end_date))
            
        elif report_type == 'leave':
            # 請假報表
            cursor.execute("""
                SELECT lr.id, lr.employee_id, e.name as employee_name,
                       lr.start_date, lr.end_date, lr.leave_hours,
                       lt.name as leave_type, lr.status
                FROM leaves lr
                JOIN employees e ON lr.employee_id = e.id
                JOIN leave_types lt ON lr.leave_type = lt.code
                WHERE lr.start_date BETWEEN ? AND ?
                ORDER BY lr.start_date
            """, (start_date, end_date))
            
        elif report_type == 'overtime':
            # 加班報表
            cursor.execute("""
                SELECT ar.id, ar.employee_id, e.name as employee_name,
                       DATE(ar.check_in) as work_date, 0
                FROM attendance ar
                JOIN employees e ON ar.employee_id = e.id
                WHERE DATE(ar.check_in) BETWEEN ? AND ?
                AND 0 > 0
                ORDER BY ar.check_in
            """, (start_date, end_date))
        
        else:
            return jsonify({"error": "不支援的報表類型"}), 400
        
        # 轉換為字典格式
        columns = [description[0] for description in cursor.description]
        records = []
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            records.append(record)
        
        result = {
            "report_type": report_type,
            "start_date": start_date,
            "end_date": end_date,
            "total_records": len(records),
            "records": records,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"匯出報表失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/attendance-trends", methods=["GET"])
def get_attendance_trends():
    """
    獲取考勤趨勢分析
    
    查詢參數：
    - period: 分析週期（week/month/quarter）
    - employee_id: 員工ID（可選）
    
    返回：
    - 考勤趨勢資料
    """
    try:
        period = request.args.get('period', 'month')
        employee_id_param = request.args.get('employee_id')  # 可能是字符串編號或數字ID
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 根據週期設定日期格式
        if period == 'week':
            date_format = '%Y-W%W'
            date_label = '週'
        elif period == 'month':
            date_format = '%Y-%m'
            date_label = '月'
        elif period == 'quarter':
            date_format = '%Y-Q%q'
            date_label = '季'
        else:
            return jsonify({"error": "不支援的分析週期"}), 400
        
        # 構建查詢條件
        where_clause = ""
        params = []
        employee_id = None
        
        if employee_id_param:
            # 如果是字符串編號（如E015），需要轉換為數字ID
            if isinstance(employee_id_param, str) and employee_id_param.startswith('E'):
                cursor.execute("SELECT id FROM employees WHERE employee_id = ?", (employee_id_param,))
                result = cursor.fetchone()
                if result:
                    employee_id = result[0]
            else:
                # 如果是數字ID，直接使用
                try:
                    employee_id = int(employee_id_param)
                except ValueError:
                    pass
            
            if employee_id:
                where_clause = "WHERE ar.employee_id = ?"
                params.append(employee_id)
        
        # 查詢趨勢資料
        cursor.execute(f"""
            SELECT 
                strftime('{date_format}', ar.check_in) as period,
                COUNT(*) as attendance_count,
                AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours,
                SUM(0) as total_overtime,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN ar.status = 'early_leave' THEN 1 END) as early_leave_count
            FROM attendance ar
            {where_clause}
            GROUP BY strftime('{date_format}', ar.check_in)
            ORDER BY period DESC
            LIMIT 12
        """, params)
        
        trends = []
        for row in cursor.fetchall():
            trend = {
                "period": row[0],
                "period_label": f"{row[0]}{date_label}",
                "attendance_count": row[1],
                "avg_work_hours": round(row[2] or 0, 2),
                "total_overtime": round(row[3] or 0, 2),
                "late_count": row[4],
                "early_leave_count": row[5]
            }
            trends.append(trend)
        
        return jsonify({
            "period": period,
            "employee_id": employee_id,
            "trends": trends,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取考勤趨勢失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/department-stats", methods=["GET"])
def get_department_stats():
    """
    獲取部門統計分析
    
    查詢參數：
    - month: 分析月份（YYYY-MM，預設當月）
    
    返回：
    - 各部門的考勤統計資料
    """
    try:
        month = request.args.get('month', datetime.now().strftime('%Y-%m'))
        
        conn = create_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                d.id, d.name as department_name,
                COUNT(DISTINCT e.id) as employee_count,
                COUNT(ar.id) as attendance_count,
                AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours,
                SUM(0) as total_overtime,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN ar.status = 'early_leave' THEN 1 END) as early_leave_count
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
            LEFT JOIN attendance ar ON e.id = ar.employee_id 
                AND strftime('%Y-%m', ar.check_in) = ?
            WHERE 1=1
            GROUP BY d.id, d.name
            ORDER BY d.name
        """, (month,))
        
        stats = []
        for row in cursor.fetchall():
            stat = {
                "department_id": row[0],
                "department_name": row[1],
                "employee_count": row[2],
                "attendance_count": row[3],
                "avg_work_hours": round(row[4] or 0, 2),
                "total_overtime": round(row[5] or 0, 2),
                "late_count": row[6],
                "early_leave_count": row[7],
                "attendance_rate": round((row[3] / (row[2] * 22) * 100) if row[2] > 0 else 0, 1)  # 假設每月22個工作日
            }
            stats.append(stat)
        
        return jsonify({
            "month": month,
            "department_stats": stats,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取部門統計失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/time-distribution", methods=["GET"])
def get_time_distribution():
    """
    獲取時間分佈分析
    
    查詢參數：
    - date: 分析日期（YYYY-MM-DD，預設今天）
    
    返回：
    - 打卡時間分佈資料
    """
    try:
        date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取打卡時間分佈
        cursor.execute("""
            SELECT 
                strftime('%H', check_in) as hour,
                COUNT(*) as count
            FROM attendance
            WHERE DATE(check_in) = ?
            GROUP BY strftime('%H', check_in)
            ORDER BY hour
        """, (date,))
        
        clock_in_distribution = []
        for row in cursor.fetchall():
            clock_in_distribution.append({
                "hour": int(row[0]),
                "count": row[1]
            })
        
        # 獲取下班時間分佈
        cursor.execute("""
            SELECT 
                strftime('%H', check_out) as hour,
                COUNT(*) as count
            FROM attendance
            WHERE DATE(check_out) = ? AND check_out IS NOT NULL
            GROUP BY strftime('%H', check_out)
            ORDER BY hour
        """, (date,))
        
        clock_out_distribution = []
        for row in cursor.fetchall():
            clock_out_distribution.append({
                "hour": int(row[0]),
                "count": row[1]
            })
        
        return jsonify({
            "date": date,
            "clock_in_distribution": clock_in_distribution,
            "clock_out_distribution": clock_out_distribution,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取時間分佈失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/leave-stats", methods=["GET"])
def get_leave_stats():
    """
    獲取請假統計分析
    
    查詢參數：
    - year: 分析年份（預設當年）
    
    返回：
    - 請假統計資料
    """
    try:
        year = request.args.get('year', datetime.now().year, type=int)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 按請假類型統計
        cursor.execute("""
            SELECT 
                lt.name as leave_type,
                COUNT(lr.id) as request_count,
                SUM(lr.leave_hours) as total_hours,
                COUNT(CASE WHEN lr.status = 'approved' THEN 1 END) as approved_count,
                COUNT(CASE WHEN lr.status = 'rejected' THEN 1 END) as rejected_count,
                COUNT(CASE WHEN lr.status = 'pending' THEN 1 END) as pending_count
            FROM leave_types lt
            LEFT JOIN leaves lr ON lt.code = lr.leave_type 
                AND strftime('%Y', lr.start_date) = ?
            WHERE 1=1
            GROUP BY lt.id, lt.name
            ORDER BY total_hours DESC
        """, (str(year),))
        
        leave_type_stats = []
        for row in cursor.fetchall():
            stat = {
                "leave_type": row[0],
                "request_count": row[1],
                "total_hours": row[2] or 0,
                "approved_count": row[3],
                "rejected_count": row[4],
                "pending_count": row[5],
                "approval_rate": round((row[3] / row[1] * 100) if row[1] > 0 else 0, 1)
            }
            leave_type_stats.append(stat)
        
        # 按月份統計
        cursor.execute("""
            SELECT 
                strftime('%m', start_date) as month,
                COUNT(*) as request_count,
                SUM(leave_hours) as total_hours
            FROM leaves
            WHERE strftime('%Y', start_date) = ? AND status = 'approved'
            GROUP BY strftime('%m', start_date)
            ORDER BY month
        """, (str(year),))
        
        monthly_stats = []
        for row in cursor.fetchall():
            monthly_stats.append({
                "month": int(row[0]),
                "request_count": row[1],
                "total_hours": row[2]
            })
        
        return jsonify({
            "year": year,
            "leave_type_stats": leave_type_stats,
            "monthly_stats": monthly_stats,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取請假統計失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/efficiency", methods=["GET"])
def get_efficiency_analysis():
    """
    獲取工作效率分析
    
    查詢參數：
    - period: 分析週期（week/month）
    - department_id: 部門ID（可選）
    
    返回：
    - 工作效率分析資料
    """
    try:
        period = request.args.get('period', 'month')
        department_id = request.args.get('department_id', type=int)
        
        conn = create_connection()
        cursor = conn.cursor()
        
        # 構建查詢條件
        where_clause = ""
        params = []
        if department_id:
            where_clause = "AND e.department_id = ?"
            params.append(department_id)
        
        # 根據週期設定日期範圍
        if period == 'week':
            date_condition = "DATE(ar.check_in) >= DATE('now', '-7 days')"
        else:  # month
            date_condition = "strftime('%Y-%m', ar.check_in) = strftime('%Y-%m', 'now')"
        
        # 查詢效率指標
        cursor.execute(f"""
            SELECT 
                e.id, e.name as employee_name, d.name as department_name,
                COUNT(ar.id) as attendance_days,
                AVG(CASE WHEN ar.check_in IS NOT NULL AND ar.check_out IS NOT NULL THEN ROUND((strftime('%s', ar.check_out) - strftime('%s', ar.check_in)) / 3600.0, 2) ELSE 0 END) as avg_work_hours,
                SUM(0) as total_overtime,
                COUNT(CASE WHEN ar.status = 'on_time' THEN 1 END) as on_time_count,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count
            FROM employees e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN attendance ar ON e.id = ar.employee_id AND {date_condition}
            WHERE e.status = 'active' {where_clause}
            GROUP BY e.id, e.name, d.name
            HAVING attendance_days > 0
            ORDER BY avg_work_hours DESC
        """, params)
        
        efficiency_data = []
        for row in cursor.fetchall():
            efficiency_score = 0
            if row[3] > 0:  # attendance_days > 0
                # 計算效率分數（準時率 * 0.4 + 工作時數比率 * 0.6）
                on_time_rate = row[6] / row[3]  # on_time_count / attendance_days
                work_hour_ratio = min(row[4] / 8, 1.2) if row[4] else 0  # avg_work_hours / 8, 最高1.2
                efficiency_score = (on_time_rate * 0.4 + work_hour_ratio * 0.6) * 100
            
            data = {
                "employee_id": row[0],
                "employee_name": row[1],
                "department_name": row[2],
                "attendance_days": row[3],
                "avg_work_hours": round(row[4] or 0, 2),
                "total_overtime": round(row[5] or 0, 2),
                "on_time_count": row[6],
                "late_count": row[7],
                "on_time_rate": round((row[6] / row[3] * 100) if row[3] > 0 else 0, 1),
                "efficiency_score": round(efficiency_score, 1)
            }
            efficiency_data.append(data)
        
        return jsonify({
            "period": period,
            "department_id": department_id,
            "efficiency_analysis": efficiency_data,
            "generated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取效率分析失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@report_bp.route("/api/analytics/export", methods=["GET"])
def export_analytics():
    """
    匯出分析報表
    
    查詢參數：
    - type: 分析類型（trends/department/efficiency）
    - format: 匯出格式（json/csv）
    - 其他參數根據分析類型而定
    
    返回：
    - 匯出的分析資料
    """
    try:
        analysis_type = request.args.get('type', 'trends')
        export_format = request.args.get('format', 'json')
        
        # 根據分析類型調用對應的函數
        if analysis_type == 'trends':
            return get_attendance_trends()
        elif analysis_type == 'department':
            return get_department_stats()
        elif analysis_type == 'efficiency':
            return get_efficiency_analysis()
        else:
            return jsonify({"error": "不支援的分析類型"}), 400
        
    except Exception as e:
        logger.error(f"匯出分析報表失敗: {e}")
        return jsonify({"error": str(e)}), 500


@report_bp.route("/api/reports/dashboard", methods=["GET"])
def get_reports_dashboard():
    """
    獲取報表儀表板資料
    
    返回：
    - 報表儀表板的綜合資料
    """
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取本月統計
        current_month = datetime.now().strftime('%Y-%m')
        
        # 本月考勤統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT employee_id) as active_employees,
                AVG(CASE WHEN check_in IS NOT NULL AND check_out IS NOT NULL THEN (strftime("%s", check_out) - strftime("%s", check_in)) / 3600.0 ELSE 0 END) as avg_work_hours,
                0 as total_overtime
            FROM attendance
            WHERE strftime('%Y-%m', check_in) = ?
        """, (current_month,))
        
        attendance_stats = cursor.fetchone()
        
        # 本月請假統計
        cursor.execute("""
            SELECT 
                COUNT(*) as total_requests,
                SUM(leave_hours) as total_days,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
            FROM leaves
            WHERE strftime('%Y-%m', start_date) = ?
        """, (current_month,))
        
        leave_stats = cursor.fetchone()
        
        # 部門排名（按出勤率）
        cursor.execute("""
            SELECT 
                d.name as department_name,
                COUNT(DISTINCT e.id) as employee_count,
                COUNT(ar.id) as attendance_count,
                ROUND(COUNT(ar.id) * 100.0 / (COUNT(DISTINCT e.id) * 22), 1) as attendance_rate
            FROM departments d
            LEFT JOIN employees e ON d.id = e.department_id AND e.status = 'active'
            LEFT JOIN attendance ar ON e.id = ar.employee_id 
                AND strftime('%Y-%m', ar.check_in) = ?
            WHERE 1=1
            GROUP BY d.id, d.name
            HAVING employee_count > 0
            ORDER BY attendance_rate DESC
            LIMIT 5
        """, (current_month,))
        
        department_ranking = []
        for row in cursor.fetchall():
            department_ranking.append({
                "department_name": row[0],
                "employee_count": row[1],
                "attendance_count": row[2],
                "attendance_rate": row[3]
            })
        
        dashboard_data = {
            "month": current_month,
            "attendance_stats": {
                "total_records": attendance_stats[0],
                "active_employees": attendance_stats[1],
                "avg_work_hours": round(attendance_stats[2] or 0, 2),
                "total_overtime": round(attendance_stats[3] or 0, 2)
            },
            "leave_stats": {
                "total_requests": leave_stats[0],
                "total_days": leave_stats[1] or 0,
                "approved_count": leave_stats[2],
                "pending_count": leave_stats[3],
                "approval_rate": round((leave_stats[2] / leave_stats[0] * 100) if leave_stats[0] > 0 else 0, 1)
            },
            "department_ranking": department_ranking,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(dashboard_data)
        
    except Exception as e:
        logger.error(f"獲取報表儀表板失敗: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


# 這裡之後會添加其他報表分析相關的API
# 例如：自定義報表、報表排程等 