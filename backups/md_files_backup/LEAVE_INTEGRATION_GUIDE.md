# 📋 請假整合功能使用指南

## 🎯 功能概述

本系統的請假整合功能已經過全面優化，能夠正確處理各種請假情況，包括：

- **跨多天請假**: 員工請假兩天或多天時，系統會自動為每一天創建對應的考勤記錄
- **智能時數分配**: 總請假時數會平均分配到每一天
- **全天請假識別**: 一整天請假自動設定為8小時
- **部分請假處理**: 支援半天或幾小時的請假

## 🔧 核心改進

### 1. 跨多天請假處理

**改進前的問題**：
- 員工請假兩天，只會在其中一天顯示請假記錄
- 請假時數計算不準確
- 考勤記錄不完整

**改進後的效果**：
```
員工請假：2024-12-20 至 2024-12-21（共16小時）

考勤記錄：
- 2024-12-20: 請假8小時（年假）
- 2024-12-21: 請假8小時（年假）
```

### 2. 智能時數分配算法

```python
# 計算邏輯
總請假天數 = (結束日期 - 開始日期).days + 1
每日請假時數 = 總請假時數 / 總請假天數

# 範例
請假期間：2024-12-20 至 2024-12-22（3天）
總請假時數：24小時
每日請假時數：24 ÷ 3 = 8小時/天
```

### 3. 考勤狀態智能判斷

| 請假時數 | 打卡情況 | 考勤狀態 | 說明 |
|---------|---------|---------|------|
| 8小時 | 無打卡 | `leave_full_day` | 全天請假 |
| 4小時 | 無打卡 | `leave_partial_absent` | 部分請假未打卡 |
| 4小時 | 有打卡 | `leave_partial` | 部分請假有打卡 |
| 0小時 | 無打卡 | `absent` | 曠職 |
| 0小時 | 有打卡 | `normal` | 正常出勤 |

## 📝 使用方法

### 1. 創建請假申請

```python
# API 請求範例
POST /api/leave-requests
{
    "employee_id": 1,
    "leave_type_id": 1,
    "start_date": "2024-12-20",
    "end_date": "2024-12-21",
    "leave_hours": 16.0,  # 可選，不填則預設每天8小時
    "reason": "家庭事務"
}
```

### 2. 執行考勤整理

```python
# API 請求範例
POST /api/attendance/management/generate-complete
{
    "target_date": "2024-12-20",
    "force_regenerate": true
}
```

### 3. 查看考勤記錄

```python
# API 請求範例
GET /api/attendance/records?date=2024-12-20&employee_id=1
```

## 🧪 測試驗證

### 運行測試腳本

```bash
# 執行請假整合測試
python test_leave_integration_enhanced.py
```

### 測試場景

1. **跨兩天請假**：
   - 員工1請假2024-12-20至2024-12-21（16小時）
   - 預期：每天8小時請假記錄

2. **全天請假**：
   - 員工2請假2024-12-20（8小時）
   - 預期：狀態為`leave_full_day`

3. **部分請假+打卡**：
   - 員工3請假2024-12-20上午（4小時）+ 下午打卡
   - 預期：狀態為`leave_partial`，有打卡記錄

## 📊 資料庫結構

### 請假記錄表 (leave_requests)

| 欄位 | 類型 | 說明 |
|------|------|------|
| employee_id | INTEGER | 員工ID |
| start_date | DATE | 請假開始日期 |
| end_date | DATE | 請假結束日期 |
| leave_hours | FLOAT | 總請假時數 |
| status | TEXT | 審核狀態 |

### 考勤記錄表 (attendance_records)

| 欄位 | 類型 | 說明 |
|------|------|------|
| employee_id | INTEGER | 員工ID |
| work_date | DATE | 工作日期 |
| leave_hours | FLOAT | 當日請假時數 |
| status | TEXT | 考勤狀態 |
| note | TEXT | 備註（包含請假資訊）|

## 🔍 故障排除

### 常見問題

1. **請假時數不正確**
   - 檢查 `leave_hours` 欄位是否正確設定
   - 確認請假日期範圍計算正確

2. **考勤記錄缺失**
   - 確認請假狀態為 `approved`
   - 檢查考勤生成API是否正確調用

3. **跨多天請假只顯示一天**
   - 確認使用的是改進後的考勤生成邏輯
   - 檢查日期範圍查詢是否正確

### 調試方法

```python
# 查看請假記錄
SELECT * FROM leave_requests 
WHERE employee_id = 1 AND status = 'approved';

# 查看考勤記錄
SELECT * FROM attendance_records 
WHERE employee_id = 1 AND work_date BETWEEN '2024-12-20' AND '2024-12-21';
```

## 📈 性能優化

### 批量處理

系統支援批量處理多個員工的考勤記錄：

```python
# 批量生成考勤記錄
POST /api/attendance/management/generate-complete
{
    "target_date": "2024-12-20",
    "department_ids": [1, 2, 3],  # 指定部門
    "force_regenerate": true
}
```

### 增量處理

對於大量資料，建議使用增量處理：

```python
# 增量處理考勤記錄
POST /api/attendance/management/incremental-process
{
    "start_date": "2024-12-20",
    "end_date": "2024-12-21"
}
```

## 🎉 總結

改進後的請假整合功能提供了：

- ✅ **完整的跨多天請假支援**
- ✅ **智能的時數分配算法**
- ✅ **準確的考勤狀態判斷**
- ✅ **詳細的請假資訊記錄**
- ✅ **全面的測試驗證**

這些改進確保了考勤系統能夠正確處理各種請假情況，為企業提供準確可靠的考勤管理功能。 