<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>請假管理 - AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            100: '#e0e8ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            500: '#f59e0b',
                            600: '#d97706',
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            500: '#ef4444',
                            600: '#dc2626',
                        },
                        purple: {
                            50: '#faf5ff',
                            100: '#f3e8ff',
                            500: '#a855f7',
                            600: '#9333ea',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': {
                                opacity: '0'
                            },
                            '100%': {
                                opacity: '1'
                            },
                        },
                        slideUp: {
                            '0%': {
                                transform: 'translateY(10px)',
                                opacity: '0'
                            },
                            '100%': {
                                transform: 'translateY(0)',
                                opacity: '1'
                            },
                        },
                        bounceGentle: {
                            '0%, 100%': {
                                transform: 'translateY(0)'
                            },
                            '50%': {
                                transform: 'translateY(-5px)'
                            },
                        }
                    }
                }
            }
        }
    </script>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>

    <style>
        /* Glassmorphism 效果 */
        
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        /* 自訂捲軸 */
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表單動畫 */
        
        .form-group {
            transition: all 0.3s ease;
        }
        
        .form-group:focus-within {
            transform: translateY(-2px);
        }
        /* 時間選項互動效果 */
        
        .time-option input:checked+.time-option-card {
            background: linear-gradient(135deg, #6366f1, #a855f7);
            border-color: #6366f1;
            color: white;
            transform: scale(1.02);
            box-shadow: 0 8px 25px -5px rgba(99, 102, 241, 0.4);
        }
        
        .time-option input:checked+.time-option-card i,
        .time-option input:checked+.time-option-card span {
            color: white !important;
        }
        
        .partial-option input:checked+.partial-option-card {
            transform: scale(1.02);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .partial-option[data-value="morning"] input:checked+.partial-option-card {
            background: linear-gradient(135deg, #f97316, #eab308);
            border-color: #f97316;
        }
        
        .partial-option[data-value="afternoon"] input:checked+.partial-option-card {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-color: #3b82f6;
        }
        
        .partial-option input:checked+.partial-option-card i,
        .partial-option input:checked+.partial-option-card span,
        .partial-option input:checked+.partial-option-card div {
            color: white !important;
        }
        /* 動畫效果 */
        
        .slide-down-enter {
            animation: slideDownEnter 0.3s ease-out;
        }
        
        .slide-up-exit {
            animation: slideUpExit 0.3s ease-in;
        }
        
        @keyframes slideDownEnter {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideUpExit {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <!-- 頂部導航 -->
    <nav class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5 text-gray-600"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">請假管理</h1>
                        <p class="text-sm text-gray-500">人資專用 - 代理員工請假申請</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="showLeaveModal()" class="bg-gradient-to-r from-brand-500 to-purple-600 text-white px-6 py-2.5 rounded-xl font-medium hover:from-brand-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center space-x-2">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新增請假申請</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 員工選擇區域 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-100/50 border border-gray-100/60 p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 tracking-tight">員工選擇</h2>
                        <p class="text-sm text-gray-500 mt-1">選擇要代理申請請假的員工</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">
                        <i data-lucide="building" class="w-4 h-4 inline mr-2"></i>
                        部門
                    </label>
                    <select id="departmentSelect" class="w-full bg-white/50 backdrop-blur-sm border border-gray-200 rounded-xl px-4 py-4 pr-10 focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none">
                        <option value="">請選擇部門</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">
                        <i data-lucide="user" class="w-4 h-4 inline mr-2"></i>
                        員工
                    </label>
                    <select id="employeeSelect" class="w-full bg-white/50 backdrop-blur-sm border border-gray-200 rounded-xl px-4 py-4 pr-10 focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none">
                        <option value="">請先選擇部門</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button id="loadEmployeeData" onclick="loadSelectedEmployeeData()" class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-4 rounded-xl font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center space-x-2"
                        disabled>
                        <i data-lucide="search" class="w-4 h-4"></i>
                        <span>載入員工資料</span>
                    </button>
                </div>
            </div>

            <!-- 選中員工信息顯示 -->
            <div id="selectedEmployeeInfo" class="mt-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl hidden">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i data-lucide="user-check" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h3 id="selectedEmployeeName" class="text-xl font-bold text-gray-900">-</h3>
                        <p id="selectedEmployeeDetails" class="text-sm text-gray-600">-</p>
                        <p class="text-xs text-blue-600 mt-1">已選擇此員工進行請假申請代理</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 請假記錄列表 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-100/50 border border-gray-100/60 overflow-hidden">
            <!-- 標題區域 -->
            <div class="bg-gradient-to-r from-indigo-500/5 to-purple-500/5 px-8 py-6 border-b border-gray-100/60">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i data-lucide="calendar-days" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 tracking-tight">請假記錄</h3>
                            <p class="text-sm text-gray-500 mt-1">管理員工的請假申請記錄</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div id="recordsCount" class="text-sm text-gray-500 font-medium">請先選擇員工</div>
                        <div class="text-xs text-gray-400 mt-1">實時更新</div>
                    </div>
                </div>
            </div>

            <!-- 篩選區域 -->
            <div class="px-8 py-6 border-b border-gray-100/60 bg-gray-50/50">
                <div class="flex flex-wrap items-center gap-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">狀態篩選：</label>
                        <select id="statusFilter" class="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option value="">全部狀態</option>
                            <option value="pending">待審批</option>
                            <option value="approved">已核准</option>
                            <option value="rejected">已拒絕</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">假別篩選：</label>
                        <select id="typeFilter" class="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                            <option value="">全部假別</option>
                        </select>
                    </div>
                    <button onclick="applyFilters()" class="bg-brand-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-brand-600 transition-colors">
                        套用篩選
                    </button>
                    <button onclick="clearFilters()" class="bg-gray-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-600 transition-colors">
                        清除篩選
                    </button>
                </div>
            </div>

            <!-- 記錄列表 -->
            <div class="divide-y divide-gray-100">
                <!-- 載入中提示 -->
                <div id="loadingRecords" class="p-12 text-center">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-3xl mb-6 shadow-lg">
                        <div class="relative">
                            <i data-lucide="calendar-days" class="w-10 h-10 text-indigo-500"></i>
                        </div>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-700 mb-2">請選擇員工</h4>
                    <p class="text-sm text-gray-500">選擇員工後即可查看其請假記錄</p>
                </div>

                <!-- 空狀態 -->
                <div id="emptyRecords" class="p-12 text-center hidden">
                    <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl mb-8 shadow-lg">
                        <i data-lucide="calendar-x" class="w-12 h-12 text-gray-400"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-700 mb-3">沒有請假記錄</h4>
                    <p class="text-gray-500 mb-8 max-w-md mx-auto leading-relaxed">此員工還沒有任何請假申請記錄，您可以代理其提交新的請假申請。</p>
                    <button onclick="showLeaveModal()" class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-0.5">
                        <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                        新增請假申請
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 請假申請模態框 -->
    <div id="leaveModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50 overflow-y-auto">
        <div class="min-h-screen px-4 text-center">
            <div class="inline-block align-bottom bg-white rounded-3xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle w-full max-w-4xl">
                <!-- 模態框標題 -->
                <div class="bg-gradient-to-r from-brand-500/10 to-purple-500/10 px-8 py-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-brand-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i data-lucide="calendar-plus" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900">代理請假申請</h3>
                                <p class="text-sm text-gray-500 mt-1">為員工代理提交請假申請</p>
                            </div>
                        </div>
                        <button onclick="hideLeaveModal()" class="p-2 hover:bg-gray-100 rounded-xl transition-colors">
                            <i data-lucide="x" class="w-6 h-6 text-gray-500"></i>
                        </button>
                    </div>
                </div>

                <!-- 表單內容 -->
                <div class="px-8 py-6 max-h-[70vh] overflow-y-auto scrollbar-thin">
                    <!-- 申請員工信息 -->
                    <div id="modalEmployeeInfo" class="mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl hidden">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i data-lucide="user" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-gray-900">申請員工</h4>
                                <p id="modalEmployeeDetails" class="text-sm text-gray-600">請先選擇員工</p>
                            </div>
                        </div>
                    </div>

                    <form id="leaveForm" class="space-y-6">
                        <!-- 請假類型 -->
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">
                                <i data-lucide="tag" class="w-4 h-4 inline mr-2"></i>
                                請假類型 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="leave_type" required class="w-full bg-white/50 backdrop-blur-sm border border-gray-200 rounded-xl px-4 py-4 pr-10 focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none">
                                    <option value="">請選擇請假類型</option>
                                    <option value="annual">🏖️ 年假</option>
                                    <option value="sick">🏥 病假</option>
                                    <option value="personal">📋 事假</option>
                                    <option value="special">⭐ 特休</option>
                                    <option value="marriage">💒 婚假</option>
                                    <option value="funeral">🕊️ 喪假</option>
                                    <option value="maternity">👶 產假</option>
                                    <option value="paternity">👨‍👶 陪產假</option>
                                    <option value="family_care">👨‍👩‍👧‍👦 家庭照顧假</option>
                                    <option value="official">🏛️ 公假</option>
                                    <option value="compensatory">⏰ 補休</option>
                                    <option value="other">📝 其他</option>
                                </select>
                                <i data-lucide="chevron-down" class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none"></i>
                            </div>
                            <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                        </div>

                        <!-- 請假時間類型 -->
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">
                                <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                                請假時間 <span class="text-red-500">*</span>
                            </label>

                            <!-- 時間類型選擇 -->
                            <div class="grid grid-cols-3 gap-3 mb-4">
                                <label class="relative cursor-pointer group time-option" data-value="full_day">
                                    <input type="radio" name="time_type" value="full_day" checked class="sr-only">
                                    <div class="time-option-card bg-white/50 backdrop-blur-sm border-2 border-gray-200 rounded-xl p-4 text-center transition-all duration-200 hover:border-brand-300 hover:shadow-md">
                                        <i data-lucide="sun" class="w-6 h-6 mx-auto mb-2 text-gray-500"></i>
                                        <span class="text-sm font-medium text-gray-700">全天</span>
                                    </div>
                                </label>
                                <label class="relative cursor-pointer group time-option" data-value="partial_day">
                                    <input type="radio" name="time_type" value="partial_day" class="sr-only">
                                    <div class="time-option-card bg-white/50 backdrop-blur-sm border-2 border-gray-200 rounded-xl p-4 text-center transition-all duration-200 hover:border-brand-300 hover:shadow-md">
                                        <i data-lucide="clock" class="w-6 h-6 mx-auto mb-2 text-gray-500"></i>
                                        <span class="text-sm font-medium text-gray-700">部分時間</span>
                                    </div>
                                </label>
                                <label class="relative cursor-pointer group time-option" data-value="hourly">
                                    <input type="radio" name="time_type" value="hourly" class="sr-only">
                                    <div class="time-option-card bg-white/50 backdrop-blur-sm border-2 border-gray-200 rounded-xl p-4 text-center transition-all duration-200 hover:border-brand-300 hover:shadow-md">
                                        <i data-lucide="timer" class="w-6 h-6 mx-auto mb-2 text-gray-500"></i>
                                        <span class="text-sm font-medium text-gray-700">指定時段</span>
                                    </div>
                                </label>
                            </div>

                            <!-- 部分時間選項 (上午/下午) -->
                            <div id="partialTimeOptions" class="grid grid-cols-2 gap-3 mb-4 hidden">
                                <label class="relative cursor-pointer group partial-option" data-value="morning">
                                    <input type="radio" name="partial_time" value="morning" class="sr-only">
                                    <div class="partial-option-card bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-orange-200 rounded-xl p-4 text-center transition-all duration-200 hover:border-orange-300 hover:shadow-md">
                                        <i data-lucide="sunrise" class="w-5 h-5 mx-auto mb-2 text-orange-500"></i>
                                        <span class="text-sm font-medium text-orange-700">上午</span>
                                        <div class="text-xs text-orange-600 mt-1">08:30 - 12:00</div>
                                    </div>
                                </label>
                                <label class="relative cursor-pointer group partial-option" data-value="afternoon">
                                    <input type="radio" name="partial_time" value="afternoon" class="sr-only">
                                    <div class="partial-option-card bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-4 text-center transition-all duration-200 hover:border-blue-300 hover:shadow-md">
                                        <i data-lucide="sunset" class="w-5 h-5 mx-auto mb-2 text-blue-500"></i>
                                        <span class="text-sm font-medium text-blue-700">下午</span>
                                        <div class="text-xs text-blue-600 mt-1">13:30 - 17:30</div>
                                    </div>
                                </label>
                            </div>

                            <!-- 具體時間選擇 -->
                            <div id="specificTimeOptions" class="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 hidden">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-purple-700 mb-2">開始時間</label>
                                        <input type="time" name="start_time" class="w-full bg-white/70 border border-purple-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-purple-700 mb-2">結束時間</label>
                                        <input type="time" name="end_time" class="w-full bg-white/70 border border-purple-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    </div>
                                </div>
                                <div class="mt-3 flex items-center space-x-2 text-sm text-purple-600">
                                    <i data-lucide="info" class="w-4 h-4"></i>
                                    <span>請選擇具體的請假時間段</span>
                                </div>
                            </div>
                        </div>

                        <!-- 日期範圍 -->
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                                    開始日期 <span class="text-red-500">*</span>
                                </label>
                                <input type="date" name="start_date" required class="w-full bg-white/50 backdrop-blur-sm border border-gray-200 rounded-xl px-4 py-4 focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200">
                                <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i data-lucide="calendar-check" class="w-4 h-4 inline mr-2"></i>
                                    結束日期 <span class="text-red-500">*</span>
                                </label>
                                <input type="date" name="end_date" required class="w-full bg-white/50 backdrop-blur-sm border border-gray-200 rounded-xl px-4 py-4 focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200">
                                <div class="error-message text-red-500 text-sm mt-2 hidden"></div>
                            </div>
                        </div>

                        <!-- 請假天數顯示 -->
                        <div class="bg-gradient-to-r from-brand-50 to-purple-50 border border-brand-200 rounded-2xl p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-brand-500 to-purple-500 rounded-xl flex items-center justify-center">
                                        <i data-lucide="calculator" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">預計請假天數</p>
                                        <p class="text-xs text-gray-500">系統自動計算</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span id="leaveDays" class="text-3xl font-bold bg-gradient-to-r from-brand-600 to-purple-600 bg-clip-text text-transparent">0</span>
                                    <span class="text-lg font-semibold text-gray-600 ml-1">天</span>
                                </div>
                            </div>
                        </div>

                        <!-- 請假原因 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                請假原因 <span class="text-red-500">*</span>
                            </label>
                            <textarea name="reason" required rows="4" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent" placeholder="請詳細說明請假原因，如為病假請說明症狀，如為事假請說明事由..."></textarea>
                            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-500 mt-1">至少輸入10個字元</div>
                        </div>

                        <!-- 代理人 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                工作代理人 <span class="text-red-500">*</span>
                            </label>
                            <select name="substitute_id" required class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <option value="">請選擇代理人</option>
                            </select>
                            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-500 mt-1">請選擇能夠代理您工作的同事</div>
                        </div>

                        <!-- 審核主管 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                審核主管 <span class="text-red-500">*</span>
                            </label>
                            <select name="approver_id" required class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                                <option value="">請選擇審核主管</option>
                            </select>
                            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-500 mt-1">請選擇有權限審核您請假的主管</div>
                        </div>

                        <!-- 緊急聯絡方式 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                緊急聯絡方式
                            </label>
                            <input type="text" name="emergency_contact" class="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-brand-500 focus:border-transparent" placeholder="請假期間的緊急聯絡電話或其他聯絡方式">
                            <div class="text-xs text-gray-500 mt-1">選填：請假期間如有緊急事務的聯絡方式</div>
                        </div>

                        <!-- 附件上傳 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                相關證明文件
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-brand-400 transition-colors">
                                <input type="file" name="attachment" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" class="hidden" id="fileInput">
                                <label for="fileInput" class="cursor-pointer">
                                    <i data-lucide="upload" class="w-8 h-8 mx-auto text-gray-400 mb-2"></i>
                                    <p class="text-sm text-gray-600">點擊上傳證明文件</p>
                                    <p class="text-xs text-gray-500 mt-1">支援 PDF、圖片、Word 文件，最大 5MB</p>
                                </label>
                            </div>
                            <div id="filePreview" class="mt-2 hidden">
                                <div class="flex items-center space-x-2 text-sm text-gray-600">
                                    <i data-lucide="file" class="w-4 h-4"></i>
                                    <span id="fileName"></span>
                                    <button type="button" onclick="removeFile()" class="text-red-500 hover:text-red-700">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按鈕 -->
                        <div class="flex space-x-3">
                            <button type="submit" class="flex-1 bg-gradient-to-r from-brand-500 to-brand-600 text-white py-3 px-4 rounded-lg font-medium hover:from-brand-600 hover:to-brand-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                                <span class="submit-text">提交申請</span>
                                <span class="loading-text hidden">提交中...</span>
                            </button>
                            <button type="button" onclick="resetForm()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                重置
                            </button>
                        </div>

                        <!-- 表單說明 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start space-x-2">
                                <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                                <div class="text-sm text-blue-800">
                                    <p class="font-medium mb-1">請假申請須知：</p>
                                    <ul class="list-disc list-inside space-y-1 text-xs">
                                        <li>請假申請需提前至少1個工作日提出</li>
                                        <li>病假超過3天需提供醫生證明</li>
                                        <li>特殊假別請參考公司人事規章</li>
                                        <li>緊急請假請先電話聯絡主管</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        lucide.createIcons();

        // 全域變數
        const API_BASE = '/api';
        let currentEmployee = null;
        let leaves = [];
        let employees = [];
        let managers = [];
        let departments = [];
        let leaveStats = {};
        let leaveTypeMap = {}; // 動態假別類型對應表

        // 載入部門列表
        async function loadDepartments() {
            try {
                const response = await fetch(`${API_BASE}/departments`);
                const data = await response.json();

                if (response.ok) {
                    departments = data.departments || data || [];
                    updateDepartmentSelect();
                } else {
                    console.error('載入部門失敗:', data.error);
                    showError('載入部門失敗');
                }
            } catch (error) {
                console.error('載入部門錯誤:', error);
                showError('載入部門失敗');
            }
        }

        // 更新部門選擇器
        function updateDepartmentSelect() {
            const departmentSelect = document.getElementById('departmentSelect');
            departmentSelect.innerHTML = '<option value="">請選擇部門</option>';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.id;
                option.textContent = dept.name;
                departmentSelect.appendChild(option);
            });
        }

        // 載入指定部門的員工
        async function loadEmployeesByDepartment(departmentId) {
            try {
                const response = await fetch(`${API_BASE}/employees?department_id=${departmentId}`);
                const data = await response.json();

                if (response.ok) {
                    employees = data.employees || data || [];
                    updateEmployeeSelect();
                } else {
                    console.error('載入員工失敗:', data.error);
                    showError('載入員工失敗');
                }
            } catch (error) {
                console.error('載入員工錯誤:', error);
                showError('載入員工失敗');
            }
        }

        // 更新員工選擇器
        function updateEmployeeSelect() {
            const employeeSelect = document.getElementById('employeeSelect');
            const loadButton = document.getElementById('loadEmployeeData');

            employeeSelect.innerHTML = '<option value="">請選擇員工</option>';

            employees.forEach(emp => {
                const option = document.createElement('option');
                option.value = emp.id;
                option.textContent = `${emp.name} (${emp.employee_id})`;
                employeeSelect.appendChild(option);
            });

            // 啟用載入按鈕
            loadButton.disabled = false;
        }

        // 載入選中員工的資料
        async function loadSelectedEmployeeData() {
            const employeeSelect = document.getElementById('employeeSelect');
            const selectedEmployeeId = employeeSelect.value;

            if (!selectedEmployeeId) {
                showError('請先選擇員工');
                return;
            }

            try {
                showLoading();

                // 找到選中的員工
                const selectedEmployee = employees.find(emp => emp.id == selectedEmployeeId);
                if (!selectedEmployee) {
                    showError('找不到選中的員工');
                    return;
                }

                currentEmployee = selectedEmployee;

                // 顯示選中員工信息
                showSelectedEmployeeInfo();

                // 載入該員工的請假記錄
                await loadLeaves();

                hideLoading();

            } catch (error) {
                console.error('載入員工資料錯誤:', error);
                showError('載入員工資料失敗');
                hideLoading();
            }
        }

        // 顯示選中員工信息
        function showSelectedEmployeeInfo() {
            const infoDiv = document.getElementById('selectedEmployeeInfo');
            const nameSpan = document.getElementById('selectedEmployeeName');
            const detailsSpan = document.getElementById('selectedEmployeeDetails');
            const recordsCount = document.getElementById('recordsCount');

            nameSpan.textContent = currentEmployee.name;
            detailsSpan.textContent = `員工編號：${currentEmployee.employee_id} | 部門：${currentEmployee.department_name || '未知'}`;
            recordsCount.textContent = '載入中...';

            infoDiv.classList.remove('hidden');

            // 更新模態框中的員工信息
            const modalInfo = document.getElementById('modalEmployeeInfo');
            const modalDetails = document.getElementById('modalEmployeeDetails');
            modalDetails.textContent = `${currentEmployee.name} (${currentEmployee.employee_id}) - ${currentEmployee.department_name || '未知'}`;
            modalInfo.classList.remove('hidden');
        }

        // 顯示請假申請模態框
        function showLeaveModal() {
            if (!currentEmployee) {
                showError('請先選擇要代理申請的員工');
                return;
            }

            const modal = document.getElementById('leaveModal');
            modal.classList.remove('hidden');

            // 重置表單
            document.getElementById('leaveForm').reset();

            // 設置今天為預設日期
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('input[name="start_date"]').value = today;
            document.querySelector('input[name="end_date"]').value = today;

            // 重新計算天數
            calculateLeaveDays();
        }

        // 隱藏請假申請模態框
        function hideLeaveModal() {
            const modal = document.getElementById('leaveModal');
            modal.classList.add('hidden');
        }

        // 部門選擇變更事件
        document.addEventListener('DOMContentLoaded', function() {
            const departmentSelect = document.getElementById('departmentSelect');
            const employeeSelect = document.getElementById('employeeSelect');
            const loadButton = document.getElementById('loadEmployeeData');

            departmentSelect.addEventListener('change', function() {
                const departmentId = this.value;
                if (departmentId) {
                    loadEmployeesByDepartment(departmentId);
                } else {
                    employeeSelect.innerHTML = '<option value="">請先選擇部門</option>';
                    loadButton.disabled = true;
                }
            });

            employeeSelect.addEventListener('change', function() {
                loadButton.disabled = !this.value;
            });
        });

        // 載入請假資料
        async function loadLeaves() {
            if (!currentEmployee) {
                console.error('沒有選擇員工');
                return;
            }

            try {
                // 並行載入多個API
                const [leavesResponse, managersResponse] = await Promise.all([
                    fetch(`${API_BASE}/leaves?employee_id=${currentEmployee.id}`),
                    fetch(`${API_BASE}/employees/managers`)
                ]);

                const leavesData = await leavesResponse.json();
                leaves = leavesData.records || [];
                const managersData = await managersResponse.json();
                managers = managersData.managers || [];

                // 載入所有員工作為代理人選項
                const allEmployeesResponse = await fetch(`${API_BASE}/employees`);
                const allEmployeesData = await allEmployeesResponse.json();
                const allEmployees = allEmployeesData.employees || [];

                // 過濾掉自己，其他人都可以作為代理人
                const substitutes = allEmployees.filter(emp => emp.id !== currentEmployee.id);

                renderLeaveRecords();
                updateEmployeeSelects(substitutes, managers);

                // 更新記錄數量
                const recordsCount = document.getElementById('recordsCount');
                recordsCount.textContent = `共 ${leaves.length} 筆記錄`;

            } catch (error) {
                console.error('載入請假資料失敗:', error);
                showError('載入請假資料失敗');
            }
        }

        // 套用篩選
        function applyFilters() {
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            let filteredLeaves = leaves;

            if (statusFilter) {
                filteredLeaves = filteredLeaves.filter(leave => leave.status === statusFilter);
            }

            if (typeFilter) {
                filteredLeaves = filteredLeaves.filter(leave => leave.leave_type === typeFilter);
            }

            renderLeaveRecords(filteredLeaves);
        }

        // 清除篩選
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('typeFilter').value = '';
            renderLeaveRecords();
        }

        // 更新員工選擇器
        function updateEmployeeSelects(substitutes, managers) {
            // 更新代理人選擇器
            const substituteSelect = document.querySelector('select[name="substitute_id"]');
            substituteSelect.innerHTML = '<option value="">請選擇代理人</option>';

            substitutes.forEach(emp => {
                const option = document.createElement('option');
                option.value = emp.id;
                option.textContent = `${emp.name} (${emp.employee_id}) - ${emp.department_name}`;
                substituteSelect.appendChild(option);
            });

            // 更新審核主管選擇器
            const approverSelect = document.querySelector('select[name="approver_id"]');
            approverSelect.innerHTML = '<option value="">請選擇審核主管</option>';

            managers.forEach(manager => {
                const option = document.createElement('option');
                option.value = manager.id;
                option.textContent = `${manager.name} (${manager.position || manager.role_name || '主管'}) - ${manager.department_name}`;
                approverSelect.appendChild(option);
            });
        }

        // 渲染請假記錄
        function renderLeaveRecords(filteredLeaves = null) {
            const container = document.querySelector('.divide-y.divide-gray-100');
            const recordsToShow = filteredLeaves || leaves;

            container.innerHTML = '';

            if (recordsToShow.length === 0) {
                container.innerHTML = `
                    <div class="p-12 text-center">
                        <i data-lucide="calendar-x" class="w-12 h-12 mx-auto text-gray-400 mb-4"></i>
                        <p class="text-gray-500">沒有找到請假記錄</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            recordsToShow.forEach(leave => {
                const record = createLeaveRecord(leave);
                container.appendChild(record);
            });
        }

        // 創建請假記錄
        function createLeaveRecord(leave) {
            const record = document.createElement('div');
            record.className = 'p-6 hover:bg-gray-50 transition-colors';

            const statusClass = getLeaveStatusClass(leave.status);
            const statusText = getLeaveStatusText(leave.status);
            const leaveTypeText = getLeaveTypeText(leave.leave_type);
            const days = calculateLeaveDays(leave.start_date, leave.end_date);

            record.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="${statusClass}">${statusText}</span>
                            <span class="font-medium text-gray-900">${leaveTypeText}申請</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">${formatDateRange(leave.start_date, leave.end_date)} (${days}天)</p>
                        <p class="text-sm text-gray-500">原因：${leave.reason}</p>
                        ${leave.comment ? `<p class="text-sm text-gray-500 mt-1">備註：${leave.comment}</p>` : ''}
                    </div>
                    <div class="flex items-center space-x-2">
                        ${leave.status === 'pending' ? `
                            <button onclick="editLeave(${leave.id})" class="text-brand-600 hover:text-brand-700 text-sm font-medium">編輯</button>
                            <button onclick="cancelLeave(${leave.id})" class="text-error-600 hover:text-error-700 text-sm font-medium">撤回</button>
                        ` : `
                            <span class="text-sm text-gray-500">審批人：${leave.approver_name || '系統'}</span>
                        `}
                    </div>
                </div>
            `;
            
            return record;
        }
        
        // 獲取請假狀態樣式
        function getLeaveStatusClass(status) {
            const classes = {
                'pending': 'bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full',
                'approved': 'bg-success-100 text-success-800 text-xs px-2 py-1 rounded-full',
                'rejected': 'bg-error-100 text-error-800 text-xs px-2 py-1 rounded-full'
            };
            return classes[status] || classes['pending'];
        }
        
        // 獲取請假狀態文字
        function getLeaveStatusText(status) {
            const texts = {
                'pending': '待審批',
                'approved': '已核准',
                'rejected': '已拒絕'
            };
            return texts[status] || '未知';
        }
        
        // 獲取請假類型文字
        function getLeaveTypeText(type) {
            return leaveTypeMap[type] || type;
        }

        // 計算請假天數（使用新的工具函數庫）
        function calculateLeaveDays() {
            const startDate = document.querySelector('input[name="start_date"]').value;
            const endDate = document.querySelector('input[name="end_date"]').value;
            const timeType = document.querySelector('input[name="time_type"]:checked')?.value;

            if (!startDate || !endDate) {
                updateLeaveDaysDisplay(0, '天');
                return 0;
            }

            // 使用新的請假計算工具
            if (window.LeaveCalculator) {
                try {
                    let result;
                    
                    if (timeType === 'hourly') {
                        // 具體時間計算
                        const startTime = document.querySelector('input[name="start_time"]').value;
                        const endTime = document.querySelector('input[name="end_time"]').value;
                        
                        if (startTime && endTime) {
                            const hours = LeaveCalculator.calculateLeaveHours(startDate, endDate, startTime, endTime, 'hourly');
                            updateLeaveDaysDisplay(LeaveCalculator.formatHours(hours), hours === 1 ? '小時' : '小時');
                            return hours;
                        }
                    }
                    
                    // 天數計算
                    const days = LeaveCalculator.calculateLeaveDays(startDate, endDate);
                    
                    if (timeType === 'partial_day') {
                        // 部分時間（上午/下午）
                        const partialDays = days * 0.5;
                        updateLeaveDaysDisplay(LeaveCalculator.formatHours(partialDays), '天');
                        return partialDays;
                    } else {
                        // 全天請假
                        updateLeaveDaysDisplay(days, '天');
                        return days;
                    }
                } catch (error) {
                    console.error('請假計算錯誤:', error);
                    updateLeaveDaysDisplay('計算錯誤', '');
                    return 0;
                }
            } else {
                // 降級到舊的計算方法
                console.warn('請假計算工具未載入，使用舊方法');
                return calculateLeaveDaysLegacy();
            }
        }

        // 舊的請假計算方法（向後兼容）
        function calculateLeaveDaysLegacy() {
            const startDate = document.querySelector('input[name="start_date"]').value;
            const endDate = document.querySelector('input[name="end_date"]').value;
            const timeType = document.querySelector('input[name="time_type"]:checked')?.value;

            if (!startDate || !endDate) {
                updateLeaveDaysDisplay(0, '天');
                return 0;
            }

            const start = new Date(startDate);
            const end = new Date(endDate);

            if (start > end) {
                updateLeaveDaysDisplay('日期錯誤', '');
                return 0;
            }

            let days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
            let unit = '天';

            // 處理不同的時間類型
            if (timeType === 'partial_day') {
                days = days * 0.5;
            } else if (timeType === 'hourly') {
                const startTime = document.querySelector('input[name="start_time"]').value;
                const endTime = document.querySelector('input[name="end_time"]').value;
                
                if (startTime && endTime && days === 1) {
                    const hours = calculateHoursLegacy(startTime, endTime);
                    if (hours > 0) {
                        days = hours;
                        unit = '小時';
                    }
                }
            }

            updateLeaveDaysDisplay(days, unit);
            return days;
        }

        // 舊的小時計算方法（向後兼容）
        function calculateHoursLegacy(startTime, endTime) {
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = endTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            
            if (endMinutes <= startMinutes) {
                return 0;
            }
            
            const totalMinutes = endMinutes - startMinutes;
            const hours = Math.round((totalMinutes / 60) * 10) / 10;
            return hours;
        }

        // 更新請假天數顯示
        function updateLeaveDaysDisplay(value, unit) {
            const leaveDaysElement = document.getElementById('leaveDays');
            if (typeof value === 'number') {
                leaveDaysElement.textContent = value % 1 === 0 ? value.toString() : value.toFixed(1);
            } else {
                leaveDaysElement.textContent = value;
            }
            
            const unitElement = leaveDaysElement.nextElementSibling;
            if (unitElement) {
                unitElement.textContent = unit;
            }
        }

        // 處理時間類型變更
        function handleTimeTypeChange() {
            const timeType = document.querySelector('input[name="time_type"]:checked').value;
            const partialTimeOptions = document.getElementById('partialTimeOptions');
            const specificTimeOptions = document.getElementById('specificTimeOptions');

            // 隱藏所有子選項
            hideElement(partialTimeOptions);
            hideElement(specificTimeOptions);

            // 根據選擇顯示對應選項
            if (timeType === 'partial_day') {
                showElement(partialTimeOptions);
            } else if (timeType === 'hourly') {
                showElement(specificTimeOptions);
            }

            // 重新計算天數
            calculateLeaveDays();
        }

        // 顯示元素（帶動畫）
        function showElement(element) {
            element.classList.remove('hidden');
            element.classList.add('slide-down-enter');
            setTimeout(() => {
                element.classList.remove('slide-down-enter');
            }, 300);
        }

        // 隱藏元素（帶動畫）
        function hideElement(element) {
            element.classList.add('slide-up-exit');
            setTimeout(() => {
                element.classList.add('hidden');
                element.classList.remove('slide-up-exit');
            }, 300);
        }

        // 處理部分時間選擇
        function handlePartialTimeChange() {
            calculateLeaveDays();
        }

        // 處理具體時間變更
        function handleSpecificTimeChange() {
            calculateLeaveDays();
        }
        
        // 格式化日期範圍
        function formatDateRange(startDate, endDate) {
            const start = new Date(startDate).toLocaleDateString('zh-TW');
            const end = new Date(endDate).toLocaleDateString('zh-TW');
            return startDate === endDate ? start : `${start} - ${end}`;
        }

        // 申請請假
        async function submitLeaveApplication(formData) {
            try {
                const data = Object.fromEntries(formData);
                data.employee_id = currentEmployee.id;
                
                // 添加時間類型到請假原因中
                const timeType = data.time_type;
                if (timeType !== 'full_day') {
                    const timeText = timeType === 'morning' ? '上午' : '下午';
                    data.reason = `【${timeText}請假】${data.reason}`;
                }

                const response = await fetch(`${API_BASE}/leaves`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    showSuccess('請假申請提交成功！審核結果將以郵件通知。');
                    resetForm();
                    loadLeaves(); // 重新載入請假記錄
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '申請失敗');
                }
            } catch (error) {
                console.error('申請請假失敗:', error);
                showError(error.message || '申請請假失敗');
            }
        }
        
        // 編輯請假
        function editLeave(leaveId) {
            const leave = leaves.find(l => l.id === leaveId);
            if (leave) {
                // 填充表單
                const form = document.querySelector('form');
                form.querySelector('select[name="leave_type"]').value = leave.leave_type;
                form.querySelector('input[name="start_date"]').value = leave.start_date;
                form.querySelector('input[name="end_date"]').value = leave.end_date;
                form.querySelector('textarea[name="reason"]').value = leave.reason;
                
                // 更改提交按鈕
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.textContent = '更新申請';
                submitBtn.onclick = (e) => {
                    e.preventDefault();
                    updateLeaveApplication(leaveId, new FormData(form));
                };
            }
        }
        
        // 更新請假申請
        async function updateLeaveApplication(leaveId, formData) {
            try {
                const data = Object.fromEntries(formData);
                
                const response = await fetch(`${API_BASE}/leaves/${leaveId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    showSuccess('請假申請更新成功！');
                    resetForm();
                    loadLeaves();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '更新失敗');
                }
            } catch (error) {
                console.error('更新請假失敗:', error);
                showError(error.message || '更新請假失敗');
            }
        }
        
        // 撤回請假
        async function cancelLeave(leaveId) {
            if (!confirm('確定要撤回這個請假申請嗎？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/leaves/${leaveId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showSuccess('請假申請已撤回');
                    loadLeaves();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || '撤回失敗');
                }
            } catch (error) {
                console.error('撤回請假失敗:', error);
                showError(error.message || '撤回請假失敗');
            }
        }
        
        // 重置表單
        function resetForm() {
            const form = document.getElementById('leaveForm');
            form.reset();
            
            // 重置錯誤訊息
            document.querySelectorAll('.error-message').forEach(el => {
                el.classList.add('hidden');
                el.textContent = '';
            });

            // 重置樣式
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
                el.classList.add('border-gray-200');
            });

            // 重置天數顯示
            document.getElementById('leaveDays').textContent = '0 天';

            // 重置提交按鈕
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.querySelector('.submit-text').classList.remove('hidden');
            submitBtn.querySelector('.loading-text').classList.add('hidden');
            submitBtn.disabled = false;

            // 重置文件上傳
            removeFile();
        }
        
        // 文件上傳處理
        function handleFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const filePreview = document.getElementById('filePreview');
            const fileName = document.getElementById('fileName');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 檢查文件大小（5MB限制）
                    if (file.size > 5 * 1024 * 1024) {
                        showError('文件大小不能超過 5MB');
                        fileInput.value = '';
                        return;
                    }

                    fileName.textContent = file.name;
                    filePreview.classList.remove('hidden');
                }
            });
        }

        // 移除文件
        function removeFile() {
            const fileInput = document.getElementById('fileInput');
            const filePreview = document.getElementById('filePreview');
            
            fileInput.value = '';
            filePreview.classList.add('hidden');
        }

        // 表單驗證
        function validateForm(formData) {
            const errors = {};
            
            // 請假類型驗證
            if (!formData.get('leave_type')) {
                errors.leave_type = '請選擇請假類型';
            }

            // 日期驗證
            const startDate = formData.get('start_date');
            const endDate = formData.get('end_date');
            
            if (!startDate) {
                errors.start_date = '請選擇開始日期';
            }
            if (!endDate) {
                errors.end_date = '請選擇結束日期';
            }

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (start > end) {
                    errors.end_date = '結束日期不能早於開始日期';
                }

                // 檢查是否提前申請（除緊急情況外）
                if (start < today) {
                    errors.start_date = '請假日期不能早於今天';
                }
            }

            // 請假原因驗證
            const reason = formData.get('reason');
            if (!reason || reason.trim().length < 10) {
                errors.reason = '請假原因至少需要10個字元';
            }

            // 代理人驗證
            if (!formData.get('substitute_id')) {
                errors.substitute_id = '請選擇工作代理人';
            }

            // 審核主管驗證
            if (!formData.get('approver_id')) {
                errors.approver_id = '請選擇審核主管';
            }

            return errors;
        }

        // 顯示表單錯誤
        function showFormErrors(errors) {
            // 清除所有錯誤訊息
            document.querySelectorAll('.error-message').forEach(el => {
                el.classList.add('hidden');
                el.textContent = '';
            });

            // 移除錯誤樣式
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
                el.classList.add('border-gray-200');
            });

            // 顯示新的錯誤訊息
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                const errorDiv = input.parentElement.querySelector('.error-message');
                
                if (input && errorDiv) {
                    input.classList.remove('border-gray-200');
                    input.classList.add('border-red-500');
                    errorDiv.textContent = errors[field];
                    errorDiv.classList.remove('hidden');
                }
            });
        }

        // 狀態篩選
        function filterByStatus() {
            const statusFilter = document.querySelector('.p-6.border-b select').value;
            
            if (!statusFilter || statusFilter === '全部狀態') {
                renderLeaveRecords();
            } else {
                const statusMap = {
                    '待審批': 'pending',
                    '已核准': 'approved',
                    '已拒絕': 'rejected'
                };
                
                const filteredLeaves = leaves.filter(leave => 
                    leave.status === statusMap[statusFilter]
                );
                renderLeaveRecords(filteredLeaves);
            }
        }
        
        // 顯示載入狀態
        function showLoading() {
            const loadingHtml = `
                <div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-600"></div>
                        <span class="text-gray-700">載入中...</span>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHtml);
        }
        
        // 隱藏載入狀態
        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.remove();
            }
        }
            
        // 顯示成功訊息
        function showSuccess(message) {
            const successHtml = `
                <div class="fixed top-4 right-4 bg-success-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', successHtml);
            
            const element = document.querySelector('.fixed.top-4.right-4');
            setTimeout(() => element.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                element.classList.add('translate-x-full');
                setTimeout(() => element.remove(), 3000);
            }, 3000);
        }
        
        // 顯示錯誤訊息
        function showError(message) {
            const errorHtml = `
                <div class="fixed top-4 right-4 bg-error-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', errorHtml);
            
            const element = document.querySelector('.fixed.top-4.right-4');
            setTimeout(() => element.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                element.classList.add('translate-x-full');
                setTimeout(() => element.remove(), 5000);
            }, 5000);
        }
        
        // 表單提交處理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('leaveForm');
            
            // 日期變更時重新計算天數
            form.querySelectorAll('input[type="date"]').forEach(input => {
                input.addEventListener('change', calculateLeaveDays);
            });

            // 時間類型變更時處理顯示和計算
            form.querySelectorAll('input[name="time_type"]').forEach(input => {
                input.addEventListener('change', handleTimeTypeChange);
            });

            // 部分時間選擇變更
            form.querySelectorAll('input[name="partial_time"]').forEach(input => {
                input.addEventListener('change', handlePartialTimeChange);
            });

            // 具體時間輸入變更
            form.querySelectorAll('input[name="start_time"], input[name="end_time"]').forEach(input => {
                input.addEventListener('change', handleSpecificTimeChange);
            });

            // 表單提交
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const submitBtn = this.querySelector('button[type="submit"]');
                const submitText = submitBtn.querySelector('.submit-text');
                const loadingText = submitBtn.querySelector('.loading-text');
                
                // 表單驗證
                const formData = new FormData(this);
                const errors = validateForm(formData);
                
                if (Object.keys(errors).length > 0) {
                    showFormErrors(errors);
                    return;
                }

                // 顯示載入狀態
                submitText.classList.add('hidden');
                loadingText.classList.remove('hidden');
                submitBtn.disabled = true;
                
                try {
                    await submitLeaveApplication(formData);
                } finally {
                    // 恢復按鈕狀態
                    submitText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                    submitBtn.disabled = false;
                }
            });

            // 初始化文件上傳
            handleFileUpload();
            
                    // 初始化頁面
        loadDepartments(); // 載入部門列表
        loadLeaveTypes(); // 載入假別類型
        
        // 不自動載入員工資料，需要手動選擇

            // 初始化時間類型顯示
            handleTimeTypeChange();
        });

        // 新增：載入假別類型
        async function loadLeaveTypes() {
            try {
                const response = await fetch('/api/masterdata/leave_types');
                const data = await response.json();
                
                if (response.ok && data.items) {
                    // 建立假別類型對應表
                    leaveTypeMap = {};
                    data.items.forEach(item => {
                        leaveTypeMap[item.code] = item.name;
                    });
                    
                    // 更新請假類型選項
                    updateLeaveTypeOptions(data.items);
                    
                    console.log('假別類型載入成功:', leaveTypeMap);
                } else {
                    console.error('載入假別類型失敗:', data.error);
                    // 使用預設的假別類型
                    leaveTypeMap = {
                        'annual': '年假',
                        'sick': '病假',
                        'personal': '事假',
                        'special': '特休',
                        'marriage': '婚假',
                        'funeral': '喪假',
                        'maternity': '產假',
                        'paternity': '陪產假',
                        'family_care': '家庭照顧假',
                        'official': '公假',
                        'compensatory': '補休',
                        'other': '其他'
                    };
                }
            } catch (error) {
                console.error('載入假別類型錯誤:', error);
                // 使用預設的假別類型
                leaveTypeMap = {
                    'annual': '年假',
                    'sick': '病假',
                    'personal': '事假',
                    'special': '特休',
                    'marriage': '婚假',
                    'funeral': '喪假',
                    'maternity': '產假',
                    'paternity': '陪產假',
                    'family_care': '家庭照顧假',
                    'official': '公假',
                    'compensatory': '補休',
                    'other': '其他'
                };
            }
        }

        // 新增：更新請假類型選項
        function updateLeaveTypeOptions(leaveTypes) {
            const leaveTypeSelect = document.querySelector('select[name="leave_type"]');
            if (leaveTypeSelect) {
                // 清空現有選項
                leaveTypeSelect.innerHTML = '<option value="">請選擇假別</option>';
                
                // 添加動態載入的假別類型
                leaveTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.code;
                    option.textContent = type.name;
                    if (type.description) {
                        option.title = type.description;
                    }
                    leaveTypeSelect.appendChild(option);
                });
            }
        }
    </script>
</body>

</html>

</html>