<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打卡原始記錄查詢 - Han AttendanceOS Elite</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        brand: {
                            50: '#f0f4ff',
                            500: '#7c6df2',
                            600: '#6d4de6',
                        },
                        success: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                        },
                        warning: {
                            50: '#fffbeb',
                            500: '#f59e0b',
                        },
                        error: {
                            50: '#fef2f2',
                            500: '#ef4444',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        /* 固定表頭樣式 */
        
        .table-wrapper {
            position: relative;
            border-radius: 16px;
            border: none;
            overflow: hidden;
            background: white;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        }
        
        .table-container {
            position: relative;
            overflow: visible;
        }
        
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 20;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            backdrop-filter: blur(12px);
            border-bottom: none;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
        }
        
        .sticky-header th {
            background: transparent;
            font-weight: 700;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: 15px;
            padding: 20px 16px;
            border-bottom: none;
            white-space: nowrap;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        /* 頁面載入動畫 */
        
        .main-content {
            position: relative;
            z-index: 1;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* 卡片懸停效果增強 */
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        /* 表格行樣式優化 */
        
        .table-row {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            background: rgba(255, 255, 255, 0.8);
        }
        
        .table-row:hover {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9));
            transform: translateX(3px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.08);
        }
        
        .table-row:last-child {
            border-bottom: none;
        }
    </style>

    <!-- 載入工具函數庫 -->
    <script src="/static/js/utils/index.js"></script>
</head>

<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-sans min-h-screen">
    <!-- 返回按鈕 -->
    <div class="fixed top-4 left-4 z-50">
        <a href="/elite" class="flex items-center space-x-2 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200">
            <i data-lucide="arrow-left" class="w-4 h-4 text-white"></i>
            <span class="text-sm font-medium text-white">返回儀表板</span>
        </a>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen p-4 pt-16 main-content">
        <!-- 頁面標題 -->
        <div class="mb-4 relative">
            <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4 text-white shadow-xl">
                <div class="relative z-10">
                    <h1 class="text-2xl font-bold mb-1 text-white">打卡原始記錄查詢</h1>
                    <p class="text-indigo-100 text-sm font-medium">查詢員工打卡原始記錄，包含設備資訊、狀態代碼等詳細資料</p>
                </div>
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-xl"></div>
            </div>
        </div>

        <!-- 查詢條件與統計摘要 -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 mb-3">
            <!-- 查詢條件 -->
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-3 mb-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">員工</label>
                    <select id="employeeSelect" class="w-full border border-gray-200 rounded-lg px-3 py-1.5 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有員工</option>
                        <!-- 動態載入員工選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">部門</label>
                    <select id="departmentSelect" class="w-full border border-gray-200 rounded-lg px-3 py-1.5 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                        <option value="">所有部門</option>
                        <!-- 動態載入部門選項 -->
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">開始日期</label>
                    <input type="date" id="startDate" class="w-full border border-gray-200 rounded-lg px-3 py-1.5 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">結束日期</label>
                    <input type="date" id="endDate" class="w-full border border-gray-200 rounded-lg px-3 py-1.5 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent">
                </div>

                <div class="flex items-end">
                    <button id="searchBtn" class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1.5 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center space-x-1 shadow-lg hover:shadow-xl transform hover:scale-105 text-sm">
                        <i data-lucide="search" class="w-3 h-3"></i>
                        <span>查詢</span>
                    </button>
                </div>


            </div>

            <!-- 快速選擇與統計摘要 -->
            <div class="flex items-center justify-between">
                <!-- 快速選擇 -->
                <div class="flex flex-wrap gap-1">
                    <button class="quick-date-btn text-xs bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-2 py-1 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                        data-days="0">今天</button>
                    <button class="quick-date-btn text-xs bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-2 py-1 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                        data-days="1">昨天</button>
                    <button class="quick-date-btn text-xs bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-2 py-1 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                        data-days="7">最近7天</button>
                    <button class="quick-date-btn text-xs bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-2 py-1 rounded hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                        data-days="30">最近30天</button>
                </div>

                <!-- 統計摘要 -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-brand-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="fingerprint" class="w-3 h-3 text-brand-600"></i>
                        </div>
                        <div>
                            <span id="totalRecords" class="text-lg font-bold text-gray-900">0</span>
                            <span class="text-xs text-gray-500 ml-1">筆記錄</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-success-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="users" class="w-3 h-3 text-success-600"></i>
                        </div>
                        <div>
                            <span id="totalEmployees" class="text-lg font-bold text-gray-900">0</span>
                            <span class="text-xs text-gray-500 ml-1">員工</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-warning-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="calendar-days" class="w-3 h-3 text-warning-600"></i>
                        </div>
                        <div>
                            <span id="totalDays" class="text-lg font-bold text-gray-900">0</span>
                            <span class="text-xs text-gray-500 ml-1">天</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-error-50 rounded-lg flex items-center justify-center">
                            <i data-lucide="clock" class="w-3 h-3 text-error-600"></i>
                        </div>
                        <div>
                            <span id="avgRecordsPerDay" class="text-lg font-bold text-gray-900">0</span>
                            <span class="text-xs text-gray-500 ml-1">日均</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 紀錄列表 -->
        <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-xl border border-white/30 overflow-hidden">
            <!-- 表格標題與分頁資訊 -->
            <div class="bg-gradient-to-r from-indigo-100 to-purple-100 px-4 py-2 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <h3 class="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">打卡原始記錄</h3>
                    <span class="bg-white text-indigo-700 text-xs px-2 py-1 rounded-full font-medium shadow-sm" id="recordCount">0 筆記錄</span>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="refreshBtn" class="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-3 py-1 rounded text-xs hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-1 shadow-sm hover:shadow-md transform hover:scale-105">
                        <i data-lucide="refresh-cw" class="w-3 h-3"></i>
                        <span>重整</span>
                    </button>
                    <button id="exportBtn" class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1 rounded text-xs hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-1 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i data-lucide="download" class="w-3 h-3"></i>
                        <span>匯出</span>
                    </button>
                    <!-- 分頁資訊 -->
                    <div id="pagination" class="flex items-center space-x-2 hidden">
                        <div class="text-xs text-gray-600">
                            第 <span id="pageInfo">1-20</span> 項，共 <span id="totalItems">0</span> 項
                        </div>
                        <button id="prevPage" class="px-2 py-1 bg-white text-gray-700 rounded text-xs hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed" disabled>上一頁</button>
                        <button id="nextPage" class="px-2 py-1 bg-white text-gray-700 rounded text-xs hover:bg-gray-50 transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed" disabled>下一頁</button>
                    </div>
                </div>
            </div>

            <!-- 載入指示器 -->
            <div id="loadingIndicator" class="flex items-center justify-center py-8">
                <div class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-brand-500"></div>
                    <span class="text-gray-600 text-sm">載入中...</span>
                </div>
            </div>

            <!-- 資料表格 -->
            <div id="dataTable" class="hidden">
                <div class="table-wrapper">
                    <div class="table-container">
                        <table class="w-full">
                            <thead class="sticky-header">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium">打卡時間</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium">員工</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium">部門</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium">打卡狀態</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium">設備編號</th>
                                    <th class="px-3 py-2 text-right text-xs font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody id="tableBody" class="divide-y divide-gray-100">
                                <!-- 動態生成表格內容 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 無資料提示 -->
            <div id="noDataMessage" class="hidden text-center py-8">
                <div class="text-gray-400 mb-2">
                    <i data-lucide="file-search" class="w-8 h-8 mx-auto mb-2"></i>
                </div>
                <p class="text-gray-600 text-sm">暫無打卡原始記錄</p>
                <p class="text-xs text-gray-400 mt-1">調整查詢條件或選擇其他時間範圍</p>
            </div>
        </div>
    </div>

    <!-- 打卡詳情模態框 -->
    <div id="recordDetailModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden backdrop-blur-sm">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                <!-- 模態框頭部 -->
                <div class="bg-gradient-to-r from-brand-500 to-brand-600 px-4 py-3 text-white rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-bold">打卡詳情</h3>
                            <p class="text-brand-100 text-xs">詳細打卡資訊</p>
                        </div>
                        <button type="button" id="closeDetailBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-1.5 rounded-lg transition-all duration-200">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <!-- 模態框內容 -->
                <div class="p-4">
                    <div id="recordDetailContent">
                        <!-- 動態載入打卡詳情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentPage = 1;
        let pageSize = 20;
        let totalRecords = 0;
        let currentRecords = [];

        // 全域變數儲存打卡狀態對應表
        let clockStatusMap = {};

        // 載入打卡狀態類型
        async function loadClockStatusTypes() {
            try {
                const response = await fetch('/api/clock-status-types');
                const data = await response.json();

                if (data.status_types) {
                    // 建立狀態代碼到狀態名稱的對應表
                    clockStatusMap = {};
                    Object.keys(data.status_types).forEach(code => {
                        clockStatusMap[code] = data.status_types[code].name;
                    });
                    console.log('打卡狀態類型載入成功:', clockStatusMap);
                } else {
                    console.warn('打卡狀態類型載入失敗，使用預設對應表');
                    useDefaultClockStatusMap();
                }
            } catch (error) {
                console.error('載入打卡狀態類型失敗:', error);
                useDefaultClockStatusMap();
            }
        }

        // 使用預設的打卡狀態對應表（容錯處理）
        function useDefaultClockStatusMap() {
            clockStatusMap = {
                '0': '上班',
                '1': '下班',
                '2': '外出',
                '3': '返回',
                'I': '上班',
                'O': '下班',
                'B': '休息開始',
                'E': '休息結束',
                'OT': '加班'
            };
        }

        // 獲取打卡狀態文字（動態版本）
        function getClockStatusText(statusCode) {
            // 使用動態載入的對應表
            if (clockStatusMap[statusCode]) {
                return clockStatusMap[statusCode];
            }

            // 如果找不到對應，返回原始代碼
            console.warn('未知的打卡狀態代碼:', statusCode);
            return statusCode;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();

            // 初始化工具函數庫
            try {
                if (typeof UtilsLoader !== 'undefined') {
                    UtilsLoader.initPageUtils('elite-punch-records');
                    console.log('工具函數庫初始化成功');
                } else {
                    console.warn('工具函數庫未載入，使用降級模式');
                }
            } catch (error) {
                console.warn('工具函數庫初始化失敗，使用降級模式:', error);
            }

            initializeForm();
            loadEmployees();
            loadDepartments();
            loadClockStatusTypes();
            bindEvents();
            setDefaultDates();
            // 初始化時顯示無資料狀態
            showInitialState();
        });

        // 顯示初始狀態（無資料）
        function showInitialState() {
            const loading = document.getElementById('loadingIndicator');
            const table = document.getElementById('dataTable');
            const noData = document.getElementById('noDataMessage');
            const pagination = document.getElementById('pagination');

            loading.classList.add('hidden');
            table.classList.add('hidden');
            noData.classList.remove('hidden');
            pagination.classList.add('hidden');

            // 重置統計資訊
            updateStatistics({});
            document.getElementById('recordCount').textContent = '0 筆記錄';

            // 重置全域變數
            currentRecords = [];
            totalRecords = 0;
        }

        // 設定預設日期（今天）
        function setDefaultDates() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = today;
            document.getElementById('endDate').value = today;
        }

        // 綁定事件
        function bindEvents() {
            document.getElementById('searchBtn').addEventListener('click', searchRecords);
            document.getElementById('refreshBtn').addEventListener('click', searchRecords);
            document.getElementById('exportBtn').addEventListener('click', exportRecords);
            document.getElementById('closeDetailBtn').addEventListener('click', hideDetailModal);

            // 快速日期選擇
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const days = parseInt(this.dataset.days);
                    setQuickDate(days);
                });
            });

            // Enter鍵查詢
            document.getElementById('startDate').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') searchRecords();
            });
            document.getElementById('endDate').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') searchRecords();
            });
        }

        // 設定快速日期
        function setQuickDate(days) {
            const today = new Date();
            const endDate = new Date(today);
            const startDate = new Date(today);

            if (days === 0) {
                // 今天
                startDate.setDate(today.getDate());
            } else if (days === 1) {
                // 昨天
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
            } else {
                // 最近N天
                startDate.setDate(today.getDate() - days + 1);
            }

            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

            // 自動查詢
            searchRecords();
        }

        // 初始化表單
        function initializeForm() {
            // 可以在這裡設定表單的初始狀態
        }

        // 載入員工列表
        async function loadEmployees() {
            try {
                const response = await fetch('/api/employees');
                const data = await response.json();
                const employees = data.employees || data;

                const select = document.getElementById('employeeSelect');
                employees.forEach(emp => {
                    const option = document.createElement('option');
                    option.value = emp.employee_id; // 使用員工編號而不是數字ID
                    option.textContent = `${emp.name} (${emp.employee_id})`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入員工失敗:', error);
            }
        }

        // 載入部門列表
        async function loadDepartments() {
            try {
                const response = await fetch('/api/departments');
                const data = await response.json();

                const select = document.getElementById('departmentSelect');
                data.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('載入部門失敗:', error);
            }
        }

        // 查詢打卡原始記錄
        async function searchRecords() {
            showLoading(true);

            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                let url = `/api/punch/records?page=${currentPage}&limit=${pageSize}`;
                if (employeeId) url += `&employee_id=${employeeId}`;
                if (departmentId) url += `&department_id=${departmentId}`;
                if (startDate) url += `&start_date=${startDate}`;
                if (endDate) url += `&end_date=${endDate}`;

                console.log('查詢URL:', url);

                const response = await fetch(url);
                const data = await response.json();

                console.log('API回應:', data);

                if (response.ok && data.success) {
                    currentRecords = data.records || [];
                    totalRecords = data.pagination ? data.pagination.total : 0;
                    updateStatistics(data.statistics || {});
                    renderTable();
                    updatePagination(data.pagination);
                } else {
                    showNotification(data.error || '查詢失敗', 'error');
                    showInitialState();
                }

            } catch (error) {
                console.error('查詢失敗:', error);
                showNotification('查詢失敗', 'error');
                showInitialState();
            } finally {
                showLoading(false);
            }
        }

        // 更新統計資訊
        function updateStatistics(stats) {
            document.getElementById('totalRecords').textContent = stats.total_records || 0;
            document.getElementById('totalEmployees').textContent = stats.total_employees || 0;
            document.getElementById('totalDays').textContent = stats.total_days || 0;
            document.getElementById('avgRecordsPerDay').textContent = (stats.avg_records_per_day || 0).toFixed(1);
        }

        // 渲染表格
        function renderTable() {
            const tableBody = document.getElementById('tableBody');
            const dataTable = document.getElementById('dataTable');
            const noDataMessage = document.getElementById('noDataMessage');
            const recordCount = document.getElementById('recordCount');

            recordCount.textContent = `${totalRecords} 筆記錄`;

            if (currentRecords.length === 0) {
                dataTable.classList.add('hidden');
                noDataMessage.classList.remove('hidden');
                return;
            }

            noDataMessage.classList.add('hidden');
            dataTable.classList.remove('hidden');

            // 生成表格內容
            tableBody.innerHTML = currentRecords.map(record => {
                        // 處理日期顯示 - 使用punch_datetime
                        const displayDateTime = record.punch_datetime;

                        return `
                    <tr class="table-row cursor-pointer" onclick="showRecordDetail(${record.id})">
                        <td class="px-3 py-2 text-sm text-gray-900">
                            <div class="font-medium text-xs">${formatDateTime(displayDateTime)}</div>
                            <div class="text-xs text-gray-500">${formatTime(displayDateTime)}</div>
                        </td>
                        <td class="px-3 py-2 text-sm text-gray-900">
                            <div class="font-medium text-xs">${record.employee_name || record.employee_id || '未知員工'}</div>
                            <div class="text-xs text-gray-500">${record.employee_code || record.employee_id || '-'}</div>
                        </td>
                        <td class="px-3 py-2 text-xs text-gray-900">${record.department_name || '-'}</td>
                        <td class="px-3 py-2 text-sm">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusCodeClass(record.status_code)}">
                                ${getClockStatusText(record.status_code)}
                            </span>
                        </td>
                        <td class="px-3 py-2 text-sm text-gray-500">
                            ${record.device_id ? 
                                `<span class="text-xs bg-blue-100 text-blue-600 px-1.5 py-0.5 rounded">${record.device_id}</span>` : 
                                '<span class="text-gray-400 text-xs">-</span>'
                            }
                        </td>
                        <td class="px-3 py-2 text-right">
                            <button onclick="event.stopPropagation(); showRecordDetail(${record.id})" class="text-brand-600 hover:text-brand-700 text-xs font-medium bg-gradient-to-r from-brand-50 to-brand-100 px-2 py-1 rounded hover:from-brand-100 hover:to-brand-200 transition-all duration-200">
                                查看
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 顯示打卡詳情
        async function showRecordDetail(recordId) {
            try {
                const response = await fetch(`/api/punch/records/${recordId}`);
                const data = await response.json();

                if (response.ok) {
                    const record = data.record;
                    const content = document.getElementById('recordDetailContent');

                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">員工資訊</label>
                                    <p class="text-gray-900 text-sm">${record.employee_name || record.employee_id} (${record.employee_code || record.employee_id})</p>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">部門</label>
                                    <p class="text-gray-900 text-sm">${record.department_name || '-'}</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">打卡時間</label>
                                    <p class="text-gray-900 text-sm">${formatDateTime(record.punch_datetime)}</p>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">打卡狀態</label>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusCodeClass(record.status_code)}">
                                        ${getClockStatusText(record.status_code)}
                                    </span>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">設備編號</label>
                                    <p class="text-gray-900 text-sm">${record.device_id || '-'}</p>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-500 mb-1">處理狀態</label>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${record.processed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                        ${record.processed ? '已處理' : '未處理'}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">原始資料</label>
                                <p class="text-gray-900 text-xs bg-gray-50 p-2 rounded">${record.raw_data || '-'}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">匯入時間</label>
                                <p class="text-gray-900 text-sm">${formatDateTime(record.imported_at)}</p>
                            </div>
                        </div>
                    `;
                    
                    document.getElementById('recordDetailModal').classList.remove('hidden');
                } else {
                    showNotification(data.error || '載入詳情失敗', 'error');
                }
            } catch (error) {
                console.error('載入詳情失敗:', error);
                showNotification('載入詳情失敗', 'error');
            }
        }

        // 隱藏詳情模態框
        function hideDetailModal() {
            document.getElementById('recordDetailModal').classList.add('hidden');
        }

        // 匯出紀錄
        async function exportRecords() {
            try {
                const employeeId = document.getElementById('employeeSelect').value;
                const departmentId = document.getElementById('departmentSelect').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                let url = '/api/attendance/records/export?';
                const params = new URLSearchParams();
                if (employeeId) params.append('employee_id', employeeId);
                if (departmentId) params.append('department_id', departmentId);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);

                const response = await fetch(url + params.toString());
                const blob = await response.blob();

                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `punch_records_${startDate}_${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                showNotification('匯出成功', 'success');
            } catch (error) {
                console.error('匯出失敗:', error);
                showNotification('匯出失敗', 'error');
            }
        }

        // 更新分頁
        function updatePagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            const pageInfo = document.getElementById('pageInfo');
            const totalItems = document.getElementById('totalItems');
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');
            
            if (!pagination || pagination.total === 0) {
                paginationDiv.classList.add('hidden');
                return;
            }
            
            paginationDiv.classList.remove('hidden');
            
            const start = (pagination.page - 1) * pagination.limit + 1;
            const end = Math.min(pagination.page * pagination.limit, pagination.total);
            
            pageInfo.textContent = `${start}-${end}`;
            totalItems.textContent = pagination.total;
            
            prevPage.disabled = !pagination.has_prev;
            nextPage.disabled = !pagination.has_next;
            
            prevPage.onclick = () => {
                if (pagination.has_prev) {
                    currentPage = pagination.page - 1;
                    searchRecords();
                }
            };
            
            nextPage.onclick = () => {
                if (pagination.has_next) {
                    currentPage = pagination.page + 1;
                    searchRecords();
                }
            };
        }

        // 工具函數
        function formatDateTime(timestamp) {
            if (!timestamp) return '-';
            
            const date = new Date(timestamp);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的時間戳:', timestamp);
                return '-';
            }
            
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function formatTime(timestamp) {
            if (!timestamp) return '-';
            
            const date = new Date(timestamp);
            
            // 檢查日期是否有效
            if (isNaN(date.getTime())) {
                console.warn('無效的時間戳:', timestamp);
                return '-';
            }
            
            return date.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function showLoading(show) {
            const loading = document.getElementById('loadingIndicator');
            const table = document.getElementById('dataTable');
            const noData = document.getElementById('noDataMessage');
            
            if (show) {
                loading.classList.remove('hidden');
                table.classList.add('hidden');
                noData.classList.add('hidden');
            } else {
                loading.classList.add('hidden');
            }
        }

        // 向後兼容的通知函數（使用新的工具函數庫）
        function showNotification(message, type = 'info') {
            if (window.NotificationSystem) {
                switch (type) {
                    case 'success':
                        NotificationSystem.success(message);
                        break;
                    case 'error':
                        NotificationSystem.error(message);
                        break;
                    case 'warning':
                        NotificationSystem.warning(message);
                        break;
                    default:
                        NotificationSystem.info(message);
                }
            } else {
                console.warn('通知系統未載入，使用控制台輸出:', message);
            }
        }



        function getStatusCodeClass(statusCode) {
            const classMap = {
                '0': 'bg-green-100 text-green-800',
                '1': 'bg-red-100 text-red-800',
                '2': 'bg-yellow-100 text-yellow-800',
                '3': 'bg-blue-100 text-blue-800',
                'I': 'bg-green-100 text-green-800',
                'O': 'bg-red-100 text-red-800'
            };
            return classMap[statusCode] || 'bg-gray-100 text-gray-800';
        }
    </script>
</body>

</html>