/**
 * 員工管理模組 - 修復版
 * 完整的 CRUD 功能實現
 */

class EmployeeManagement {
    constructor(app) {
        this.app = app;
        this.employees = [];
        this.departments = [];
        this.currentEmployee = null;
        
        // 將方法綁定到全局以供 HTML onclick 使用
        window.employeeManager = this;
    }

    async init() {
        await this.loadDepartments();
        await this.loadEmployees();
    }

    // ============ 員工管理視圖 ============
    getEmployeeManagementHTML() {
        return `
            <div class="employee-management">
                <div class="view-header">
                    <h2>員工管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-outline" onclick="employeeManager.exportEmployees()">
                            📊 匯出員工資料
                        </button>
                        <button class="btn btn-primary" onclick="employeeManager.showAddEmployeeModal()">
                            ➕ 新增員工
                        </button>
                    </div>
                </div>

                <!-- 搜尋和篩選 -->
                <div class="search-section">
                    <div class="search-container">
                        <div class="search-input-group">
                            <input type="text" id="employeeSearch" class="form-input" 
                                   placeholder="搜尋員工姓名或編號..." 
                                   oninput="employeeManager.searchEmployees(this.value)">
                            <button class="search-btn" onclick="employeeManager.searchEmployees()">🔍</button>
                        </div>
                        
                        <div class="filter-group">
                            <select id="departmentFilter" class="form-select" 
                                    onchange="employeeManager.filterByDepartment(this.value)">
                                <option value="">所有部門</option>
                            </select>
                            
                            <select id="statusFilter" class="form-select" 
                                    onchange="employeeManager.filterByStatus(this.value)">
                                <option value="">所有狀態</option>
                                <option value="active">在職</option>
                                <option value="inactive">離職</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 員工列表 -->
                <div class="employee-list-section">
                    <div class="card">
                        <div class="card-header">
                            <h3>員工列表 (<span id="employeeCount">0</span> 人)</h3>
                            <div class="list-controls">
                                <button class="btn btn-ghost btn-sm" onclick="employeeManager.toggleViewMode()">
                                    <span id="viewModeIcon">📋</span> <span id="viewModeText">表格檢視</span>
                                </button>
                                <button class="btn btn-ghost btn-sm" onclick="employeeManager.refreshEmployees()">
                                    🔄 刷新
                                </button>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <!-- 表格檢視 -->
                            <div id="tableView" class="table-view">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="selectAll" 
                                                           onchange="employeeManager.toggleSelectAll(this.checked)">
                                                </th>
                                                <th onclick="employeeManager.sortEmployees('name')" class="sortable">
                                                    姓名 <span class="sort-icon">⇅</span>
                                                </th>
                                                <th onclick="employeeManager.sortEmployees('employee_id')" class="sortable">
                                                    員工編號 <span class="sort-icon">⇅</span>
                                                </th>
                                                <th>部門</th>
                                                <th>職位</th>
                                                <th>聯絡方式</th>
                                                <th>狀態</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="employeeTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center">載入中...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 卡片檢視 -->
                            <div id="cardView" class="card-view" style="display: none;">
                                <div id="employeeCards" class="employee-cards-grid">
                                    <!-- 員工卡片將在這裡動態生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批次操作工具列 -->
                <div id="batchActions" class="batch-actions" style="display: none;">
                    <div class="batch-actions-content">
                        <span id="selectedCount">已選擇 0 位員工</span>
                        <div class="batch-buttons">
                            <button class="btn btn-outline btn-sm" onclick="employeeManager.batchExport()">
                                📊 批次匯出
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="employeeManager.batchDeactivate()">
                                ⏸️ 批次停用
                            </button>
                            <button class="btn btn-error btn-sm" onclick="employeeManager.batchDelete()">
                                🗑️ 批次刪除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ============ 資料載入 ============
    async loadEmployees() {
        try {
            const response = await fetch(`${this.app.apiBase}/employees`);
            const data = await response.json();
            
            if (response.ok) {
                this.employees = data;
                this.renderEmployees();
                this.updateEmployeeCount();
            } else {
                throw new Error(data.error || '載入員工資料失敗');
            }
        } catch (error) {
            console.error('載入員工資料失敗:', error);
            this.app.showNotification('載入員工資料失敗', 'error');
        }
    }

    async loadDepartments() {
        try {
            const response = await fetch(`${this.app.apiBase}/departments`);
            const data = await response.json();
            
            if (response.ok) {
                this.departments = data;
                this.updateDepartmentFilter();
            } else {
                throw new Error(data.error || '載入部門資料失敗');
            }
        } catch (error) {
            console.error('載入部門資料失敗:', error);
        }
    }

    // ============ UI 渲染 ============
    renderEmployees(employees = this.employees) {
        const tableBody = document.getElementById('employeeTableBody');
        
        if (!tableBody) return;

        if (employees.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center">暫無員工資料</td></tr>';
            return;
        }

        // 渲染表格
        tableBody.innerHTML = employees.map(employee => `
            <tr data-employee-id="${employee.id}">
                <td>
                    <input type="checkbox" class="employee-checkbox" 
                           value="${employee.id}" 
                           onchange="employeeManager.updateSelection()">
                </td>
                <td>
                    <div class="employee-info">
                        <div class="employee-avatar">${employee.name.charAt(0)}</div>
                        <div class="employee-details">
                            <div class="employee-name">${employee.name}</div>
                            <div class="employee-email">${employee.email || '-'}</div>
                        </div>
                    </div>
                </td>
                <td><code class="employee-id">${employee.employee_id}</code></td>
                <td>${employee.department || '-'}</td>
                <td>${employee.position || '-'}</td>
                <td>
                    <div class="contact-info">
                        ${employee.email ? `<div>📧 ${employee.email}</div>` : ''}
                        ${employee.phone ? `<div>📱 ${employee.phone}</div>` : ''}
                    </div>
                </td>
                <td>
                    <span class="badge badge-success">在職</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-ghost btn-sm" 
                                onclick="employeeManager.viewEmployee(${employee.id})"
                                title="查看詳情">👁️</button>
                        <button class="btn btn-ghost btn-sm" 
                                onclick="employeeManager.editEmployee(${employee.id})"
                                title="編輯">✏️</button>
                        <button class="btn btn-ghost btn-sm" 
                                onclick="employeeManager.deleteEmployee(${employee.id})"
                                title="刪除">🗑️</button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateEmployeeCount() {
        const countElement = document.getElementById('employeeCount');
        if (countElement) {
            countElement.textContent = this.employees.length;
        }
    }

    updateDepartmentFilter() {
        const filter = document.getElementById('departmentFilter');
        if (!filter) return;

        const currentValue = filter.value;
        filter.innerHTML = '<option value="">所有部門</option>' +
            this.departments.map(dept => 
                `<option value="${dept.id}">${dept.name}</option>`
            ).join('');
        filter.value = currentValue;
    }

    // ============ 新增員工 ============
    showAddEmployeeModal() {
        const modalContent = `
            <form id="addEmployeeForm" class="employee-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="employeeName">姓名 *</label>
                        <input type="text" id="employeeName" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="employeeId">員工編號 *</label>
                        <input type="text" id="employeeId" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="employeeDepartment">部門 *</label>
                        <select id="employeeDepartment" class="form-select" required>
                            <option value="">請選擇部門</option>
                            ${this.departments.map(dept => 
                                `<option value="${dept.id}">${dept.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="employeePosition">職位 *</label>
                        <input type="text" id="employeePosition" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="employeeEmail">電子信箱</label>
                        <input type="email" id="employeeEmail" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="employeePhone">電話號碼</label>
                        <input type="tel" id="employeePhone" class="form-input">
                    </div>
                </div>
                
                <div class="form-note">
                    <p>* 為必填欄位</p>
                </div>
            </form>
        `;

        const actions = [
            {
                text: '取消',
                class: 'btn-outline',
                onclick: 'app.closeModal()'
            },
            {
                text: '新增員工',
                class: 'btn-primary',
                onclick: 'employeeManager.submitAddEmployee()'
            }
        ];

        this.app.showModal('新增員工', modalContent, actions);
    }

    async submitAddEmployee() {
        const employeeData = {
            name: document.getElementById('employeeName').value,
            employee_id: document.getElementById('employeeId').value,
            department_id: parseInt(document.getElementById('employeeDepartment').value),
            position: document.getElementById('employeePosition').value,
            email: document.getElementById('employeeEmail').value || null,
            phone: document.getElementById('employeePhone').value || null
        };

        // 驗證必填欄位
        if (!employeeData.name || !employeeData.employee_id || !employeeData.department_id || !employeeData.position) {
            this.app.showNotification('請填寫所有必填欄位', 'warning');
            return;
        }

        try {
            const response = await fetch(`${this.app.apiBase}/employees`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(employeeData)
            });

            const data = await response.json();

            if (response.ok) {
                this.app.showNotification('員工新增成功！', 'success');
                this.app.closeModal();
                await this.loadEmployees();
            } else {
                throw new Error(data.error || '新增員工失敗');
            }
        } catch (error) {
            this.app.showNotification(error.message, 'error');
        }
    }

    // ============ 編輯員工 ============
    async editEmployee(employeeId) {
        try {
            const response = await fetch(`${this.app.apiBase}/employees/${employeeId}`);
            const employee = await response.json();

            if (!response.ok) {
                throw new Error(employee.error || '載入員工資料失敗');
            }

            const modalContent = `
                <form id="editEmployeeForm" class="employee-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="editEmployeeName">姓名 *</label>
                            <input type="text" id="editEmployeeName" class="form-input" 
                                   value="${employee.name}" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="editEmployeeId">員工編號 *</label>
                            <input type="text" id="editEmployeeId" class="form-input" 
                                   value="${employee.employee_id}" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="editEmployeeDepartment">部門 *</label>
                            <select id="editEmployeeDepartment" class="form-select" required>
                                <option value="">請選擇部門</option>
                                ${this.departments.map(dept => 
                                    `<option value="${dept.id}" ${dept.id === employee.department_id ? 'selected' : ''}>${dept.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="editEmployeePosition">職位 *</label>
                            <input type="text" id="editEmployeePosition" class="form-input" 
                                   value="${employee.position}" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="editEmployeeEmail">電子信箱</label>
                            <input type="email" id="editEmployeeEmail" class="form-input" 
                                   value="${employee.email || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="editEmployeePhone">電話號碼</label>
                            <input type="tel" id="editEmployeePhone" class="form-input" 
                                   value="${employee.phone || ''}">
                        </div>
                    </div>
                </form>
            `;

            const actions = [
                {
                    text: '取消',
                    class: 'btn-outline',
                    onclick: 'app.closeModal()'
                },
                {
                    text: '儲存變更',
                    class: 'btn-primary',
                    onclick: `employeeManager.submitEditEmployee(${employeeId})`
                }
            ];

            this.app.showModal('編輯員工資料', modalContent, actions);
        } catch (error) {
            this.app.showNotification(error.message, 'error');
        }
    }

    async submitEditEmployee(employeeId) {
        const employeeData = {
            name: document.getElementById('editEmployeeName').value,
            employee_id: document.getElementById('editEmployeeId').value,
            department_id: parseInt(document.getElementById('editEmployeeDepartment').value),
            position: document.getElementById('editEmployeePosition').value,
            email: document.getElementById('editEmployeeEmail').value || null,
            phone: document.getElementById('editEmployeePhone').value || null
        };

        try {
            const response = await fetch(`${this.app.apiBase}/employees/${employeeId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(employeeData)
            });

            const data = await response.json();

            if (response.ok) {
                this.app.showNotification('員工資料更新成功！', 'success');
                this.app.closeModal();
                await this.loadEmployees();
            } else {
                throw new Error(data.error || '更新員工資料失敗');
            }
        } catch (error) {
            this.app.showNotification(error.message, 'error');
        }
    }

    // ============ 刪除員工 ============
    async deleteEmployee(employeeId) {
        const employee = this.employees.find(emp => emp.id === employeeId);
        if (!employee) return;

        const confirmContent = `
            <div class="delete-confirmation">
                <div class="warning-icon">⚠️</div>
                <p>確定要刪除員工 <strong>${employee.name}</strong> 嗎？</p>
                <p class="warning-text">此操作無法復原，將會同時刪除該員工的所有考勤記錄。</p>
            </div>
        `;

        const actions = [
            {
                text: '取消',
                class: 'btn-outline',
                onclick: 'app.closeModal()'
            },
            {
                text: '確定刪除',
                class: 'btn-error',
                onclick: `employeeManager.confirmDeleteEmployee(${employeeId})`
            }
        ];

        this.app.showModal('刪除員工', confirmContent, actions);
    }

    async confirmDeleteEmployee(employeeId) {
        try {
            const response = await fetch(`${this.app.apiBase}/employees/${employeeId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (response.ok) {
                this.app.showNotification('員工刪除成功！', 'success');
                this.app.closeModal();
                await this.loadEmployees();
            } else {
                throw new Error(data.error || '刪除員工失敗');
            }
        } catch (error) {
            this.app.showNotification(error.message, 'error');
        }
    }

    // ============ 搜尋和篩選 ============
    searchEmployees(query = '') {
        const searchInput = document.getElementById('employeeSearch');
        if (!query && searchInput) {
            query = searchInput.value.toLowerCase();
        }

        if (!query) {
            this.renderEmployees();
            return;
        }

        const filteredEmployees = this.employees.filter(employee => 
            employee.name.toLowerCase().includes(query) ||
            employee.employee_id.toLowerCase().includes(query) ||
            (employee.email && employee.email.toLowerCase().includes(query))
        );

        this.renderEmployees(filteredEmployees);
    }

    filterByDepartment(departmentId) {
        if (!departmentId) {
            this.renderEmployees();
            return;
        }

        const filteredEmployees = this.employees.filter(employee => 
            employee.department_id === parseInt(departmentId)
        );

        this.renderEmployees(filteredEmployees);
    }

    filterByStatus(status) {
        // 目前所有員工都是 active 狀態
        console.log('Filtering by status:', status);
        this.renderEmployees();
    }

    // ============ 排序功能 ============
    sortEmployees(field) {
        this.employees.sort((a, b) => {
            if (a[field] < b[field]) return -1;
            if (a[field] > b[field]) return 1;
            return 0;
        });
        
        this.renderEmployees();
    }

    // ============ 檢視模式切換 ============
    toggleViewMode() {
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');
        const viewModeIcon = document.getElementById('viewModeIcon');
        const viewModeText = document.getElementById('viewModeText');

        if (tableView.style.display === 'none') {
            tableView.style.display = 'block';
            cardView.style.display = 'none';
            viewModeIcon.textContent = '📋';
            viewModeText.textContent = '表格檢視';
        } else {
            tableView.style.display = 'none';
            cardView.style.display = 'block';
            viewModeIcon.textContent = '🃏';
            viewModeText.textContent = '卡片檢視';
        }
    }

    // ============ 選擇和批次操作 ============
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateSelection();
    }

    updateSelection() {
        const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');

        if (checkboxes.length > 0) {
            batchActions.style.display = 'block';
            selectedCount.textContent = `已選擇 ${checkboxes.length} 位員工`;
        } else {
            batchActions.style.display = 'none';
        }
    }

    // ============ 其他功能 ============
    async refreshEmployees() {
        await this.loadEmployees();
        this.app.showNotification('員工資料已刷新', 'success');
    }

    async viewEmployee(employeeId) {
        console.log('Viewing employee:', employeeId);
        
        const employee = this.employees.find(emp => emp.id === employeeId);
        if (!employee) {
            this.app.showNotification('找不到員工資料', 'error');
            return;
        }

        const content = `
            <div class="employee-detail">
                <div class="detail-row">
                    <label>姓名：</label>
                    <span>${employee.name}</span>
                </div>
                <div class="detail-row">
                    <label>部門：</label>
                    <span>${employee.department_name || '未分配'}</span>
                </div>
                <div class="detail-row">
                    <label>職位：</label>
                    <span>${employee.position || '未設定'}</span>
                </div>
                <div class="detail-row">
                    <label>電話：</label>
                    <span>${employee.phone || '未提供'}</span>
                </div>
                <div class="detail-row">
                    <label>信箱：</label>
                    <span>${employee.email || '未提供'}</span>
                </div>
                <div class="detail-row">
                    <label>入職日期：</label>
                    <span>${employee.hire_date || '未設定'}</span>
                </div>
            </div>
        `;

        this.app.showModal(`員工詳情 - ${employee.name}`, content, [
            { text: '編輯', class: 'btn-primary', onclick: `employeeManager.showEditEmployeeModal(${employeeId})` },
            { text: '關閉', class: 'btn-outline', onclick: 'app.closeModal()' }
        ]);
    }

    exportEmployees() {
        if (this.employees.length === 0) {
            this.app.showNotification('沒有員工資料可匯出', 'warning');
            return;
        }

        // 準備CSV數據
        const headers = ['姓名', '部門', '職位', '電話', '信箱', '入職日期'];
        const csvData = [
            headers.join(','),
            ...this.employees.map(emp => [
                emp.name || '',
                emp.department_name || '',
                emp.position || '',
                emp.phone || '',
                emp.email || '',
                emp.hire_date || ''
            ].join(','))
        ].join('\n');

        // 創建下載
        const blob = new Blob(['\uFEFF' + csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `員工資料_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.app.showNotification('員工資料匯出成功', 'success');
    }

    batchExport() {
        this.app.showNotification('批次匯出功能開發中...', 'info');
    }

    batchDeactivate() {
        this.app.showNotification('批次停用功能開發中...', 'info');
    }

    batchDelete() {
        this.app.showNotification('批次刪除功能開發中...', 'info');
    }
}

// 將員工管理功能整合到主應用程式
if (typeof window !== 'undefined') {
    window.EmployeeManagement = EmployeeManagement;
}