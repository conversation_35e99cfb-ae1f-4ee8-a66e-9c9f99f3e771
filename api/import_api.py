"""
匯入考勤資料 API 模組

功能說明：
- 處理打卡機文字檔案匯入
- 自動配對上下班時間
- 支援自動創建員工
- 跨日打卡記錄處理
- 詳細的匯入結果統計

作者：Han AttendanceOS
版本：v2025.6.11
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from collections import defaultdict
import logging
from database import create_connection

# 設定日誌
logger = logging.getLogger(__name__)

# 創建藍圖
import_bp = Blueprint('import_api', __name__, url_prefix='/api')

@import_bp.route("/attendance/import-text", methods=["POST"])
def import_attendance_text():
    """
    匯入打卡機文字檔案 API
    
    功能說明：
    - 接收打卡機匯出的 txt 檔案
    - 解析檔案格式並驗證資料
    - 自動配對上下班時間
    - 支援自動創建員工
    - 返回詳細的匯入結果
    
    檔案格式：機器編號,員工編號,日期,時間,狀態
    例如：011,00000701,20241220,0945,0
    
    參數：
    - file: 上傳的文字檔案
    - overwrite: 是否覆蓋現有記錄 (boolean)
    - auto_create_employee: 是否自動創建員工 (boolean)
    
    返回格式：
    {
        "success": true,
        "imported": 150,
        "skipped": 5,
        "errors": 2,
        "total": 157,
        "created_employees": 3,
        "message": "匯入完成"
    }
    """
    try:
        logger.info("開始處理文字檔匯入請求")
        
        # ===== 檢查檔案 =====
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "未找到檔案"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "未選擇檔案"}), 400
        
        if not file.filename.lower().endswith('.txt'):
            return jsonify({"success": False, "error": "僅支援 .txt 格式檔案"}), 400
        
        # ===== 獲取選項 =====
        overwrite = request.form.get('overwrite', 'false').lower() == 'true'
        auto_create_employee = request.form.get('auto_create_employee', 'true').lower() == 'true'
        
        logger.info(f"匯入選項 - 覆蓋現有記錄: {overwrite}, 自動創建員工: {auto_create_employee}")
        
        # ===== 讀取檔案內容 =====
        try:
            file_content = file.read().decode('utf-8')
        except UnicodeDecodeError:
            try:
                file.seek(0)
                file_content = file.read().decode('big5')
            except UnicodeDecodeError:
                return jsonify({"success": False, "error": "檔案編碼不支援，請使用 UTF-8 或 Big5 編碼"}), 400
        
        # ===== 解析檔案內容 =====
        raw_records = []
        lines = file_content.strip().split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 解析每行資料：機器編號,員工編號,日期,時間,狀態
                parts = line.split(',')
                if len(parts) != 5:
                    logger.warning(f"第 {line_num} 行格式錯誤，跳過: {line}")
                    continue
                
                machine_code = parts[0].strip()
                employee_code = parts[1].strip()
                date_str = parts[2].strip()
                time_str = parts[3].strip()
                status_code = parts[4].strip()
                
                # 轉換日期時間格式
                if len(date_str) != 8 or len(time_str) != 4:
                    logger.warning(f"第 {line_num} 行日期時間格式錯誤，跳過: {line}")
                    continue
                
                formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                formatted_time = f"{time_str[:2]}:{time_str[2:4]}:00"
                timestamp = f"{formatted_date} {formatted_time}"
                
                raw_records.append({
                    'machine_code': machine_code,
                    'employee_code': employee_code,
                    'timestamp': timestamp,
                    'status_code': status_code,
                    'line_num': line_num
                })
                
            except Exception as e:
                logger.error(f"第 {line_num} 行解析錯誤: {e}, 內容: {line}")
                continue
        
        if not raw_records:
            return jsonify({"success": False, "error": "檔案中沒有有效的打卡記錄"}), 400
        
        logger.info(f"成功解析 {len(raw_records)} 筆打卡記錄")
        
        # ===== 獲取員工對應表 =====
        conn = create_connection()
        if not conn:
            return jsonify({"success": False, "error": "資料庫連接失敗"}), 500
        
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT id, employee_id, card_number FROM employees WHERE status = 'active'")
            employees = cursor.fetchall()
            
            # 建立對應表
            employee_mapping = {}
            for emp_id, emp_code, card_number in employees:
                if card_number:
                    employee_mapping[card_number] = emp_id
                else:
                    # 如果沒有 card_number，嘗試使用員工編號轉換
                    if emp_code.startswith('E'):
                        numeric_part = emp_code[1:]
                    else:
                        numeric_part = emp_code
                    
                    try:
                        padded_code = f"{int(numeric_part):08d}"
                        employee_mapping[padded_code] = emp_id
                    except ValueError:
                        continue
            
            logger.info(f"建立員工對應表，共 {len(employee_mapping)} 筆")
            
            # ===== 配對上下班記錄（考慮跨日情況） =====
            # 重新組織記錄，考慮6點前為前一天的下班
            adjusted_records = []
            for record in raw_records:
                timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
                hour = timestamp.hour
                
                # 如果是6點前，視為前一天的下班記錄
                if hour < 6:
                    # 調整為前一天的日期
                    adjusted_date = (timestamp - timedelta(days=1)).strftime('%Y-%m-%d')
                    adjusted_timestamp = f"{adjusted_date} {timestamp.strftime('%H:%M:%S')}"
                    
                    logger.info(f"跨日調整: {record['timestamp']} -> {adjusted_timestamp} (員工: {record['employee_code']})")
                    
                    adjusted_records.append({
                        'machine_code': record['machine_code'],
                        'employee_code': record['employee_code'],
                        'timestamp': adjusted_timestamp,
                        'original_timestamp': record['timestamp'],
                        'status_code': record['status_code'],
                        'line_num': record['line_num'],
                        'is_previous_day_checkout': True
                    })
                else:
                    logger.info(f"正常記錄: {record['timestamp']} (員工: {record['employee_code']})")
                    adjusted_records.append({
                        'machine_code': record['machine_code'],
                        'employee_code': record['employee_code'],
                        'timestamp': record['timestamp'],
                        'original_timestamp': record['timestamp'],
                        'status_code': record['status_code'],
                        'line_num': record['line_num'],
                        'is_previous_day_checkout': False
                    })
            
            # 按調整後的日期分組
            grouped = defaultdict(list)
            for record in adjusted_records:
                date = record['timestamp'].split(' ')[0]
                key = (record['employee_code'], date)
                grouped[key].append(record)
            
            attendance_records = []
            for (employee_code, date), day_records in grouped.items():
                # 按時間排序
                day_records.sort(key=lambda x: x['timestamp'])
                
                # 分離上班和下班記錄
                checkin_records = []
                checkout_records = []
                
                for record in day_records:
                    if record.get('is_previous_day_checkout', False):
                        # 6點前的記錄作為下班
                        checkout_records.append(record)
                    else:
                        # 6點後的記錄需要判斷
                        timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
                        hour = timestamp.hour
                        
                        if hour >= 6 and hour < 14:  # 6-14點視為上班時間
                            checkin_records.append(record)
                        else:  # 14點後視為下班時間
                            checkout_records.append(record)
                
                # 產生考勤記錄
                check_in_time = None
                check_out_time = None
                device_id = None
                total_records = len(day_records)
                note_parts = []
                
                if checkin_records:
                    # 取最早的上班記錄
                    earliest_checkin = min(checkin_records, key=lambda x: x['timestamp'])
                    check_in_time = earliest_checkin['timestamp']
                    device_id = earliest_checkin['machine_code']
                    note_parts.append(f"上班: {len(checkin_records)}筆")
                
                if checkout_records:
                    # 取最晚的下班記錄
                    latest_checkout = max(checkout_records, key=lambda x: x['timestamp'])
                    check_out_time = latest_checkout['timestamp']
                    if not device_id:
                        device_id = latest_checkout['machine_code']
                    note_parts.append(f"下班: {len(checkout_records)}筆")
                    
                    # 檢查是否有跨日記錄
                    cross_day_records = [r for r in checkout_records if r.get('is_previous_day_checkout', False)]
                    if cross_day_records:
                        note_parts.append(f"跨日: {len(cross_day_records)}筆")
                
                if check_in_time or check_out_time:
                    attendance_records.append({
                        'employee_code': employee_code,
                        'date': date,
                        'check_in': check_in_time,
                        'check_out': check_out_time,
                        'device_id': device_id or day_records[0]['machine_code'],
                        'records_count': total_records,
                        'note_detail': ', '.join(note_parts)
                    })
            
            logger.info(f"配對完成，產生 {len(attendance_records)} 筆考勤記錄")
            
            # ===== 匯入資料庫 =====
            imported_count = 0
            skipped_count = 0
            error_count = 0
            created_employees = 0
            
            for record in attendance_records:
                try:
                    # 查找對應的員工ID
                    employee_id = employee_mapping.get(record['employee_code'])
                    
                    if not employee_id and auto_create_employee:
                        # 自動創建新員工
                        logger.info(f"創建新員工: 卡號={record['employee_code']}")
                        
                        # 生成員工編號
                        cursor.execute("SELECT MAX(CAST(SUBSTR(employee_id, 2) AS INTEGER)) FROM employees WHERE employee_id LIKE 'E%'")
                        result = cursor.fetchone()
                        max_num = result[0] if result and result[0] else 0
                        new_employee_id = f"E{max_num + 1:03d}"
                        
                        # 獲取預設部門ID（如果沒有部門，創建一個預設部門）
                        cursor.execute("SELECT id FROM departments LIMIT 1")
                        dept_result = cursor.fetchone()
                        if not dept_result:
                            # 創建預設部門
                            cursor.execute("""
                                INSERT INTO departments (name, description, created_at)
                                VALUES ('預設部門', '自動匯入時創建的預設部門', ?)
                            """, (datetime.now(),))
                            default_dept_id = cursor.lastrowid
                        else:
                            default_dept_id = dept_result[0]
                        
                        # 獲取預設班表ID
                        cursor.execute("SELECT id FROM shifts WHERE is_default = 1 AND is_active = 1 LIMIT 1")
                        default_shift_result = cursor.fetchone()
                        default_shift_id = default_shift_result[0] if default_shift_result else None
                        
                        # 創建員工記錄
                        cursor.execute("""
                            INSERT INTO employees (employee_id, name, card_number, department_id, position, status, shift_type, created_at)
                            VALUES (?, ?, ?, ?, '一般員工', 'active', ?, ?)
                        """, (new_employee_id, f"員工_{record['employee_code']}", record['employee_code'], default_dept_id, default_shift_id, datetime.now()))
                        
                        employee_id = cursor.lastrowid
                        employee_mapping[record['employee_code']] = employee_id
                        created_employees += 1
                        logger.info(f"成功創建新員工: ID={employee_id}, 編號={new_employee_id}")
                    
                    if not employee_id:
                        logger.warning(f"找不到員工編號對應且未啟用自動創建: {record['employee_code']}")
                        skipped_count += 1
                        continue
                    
                    # 檢查是否已存在記錄
                    check_query = """
                        SELECT id FROM attendance 
                        WHERE employee_id = ? AND DATE(COALESCE(check_in, check_out)) = ?
                    """
                    cursor.execute(check_query, (employee_id, record['date']))
                    existing = cursor.fetchone()
                    
                    if existing and not overwrite:
                        logger.debug(f"記錄已存在，跳過: 員工 {record['employee_code']} 日期 {record['date']}")
                        skipped_count += 1
                        continue
                    
                    # 決定考勤狀態
                    status = 'normal'
                    if record['check_in'] and record['check_out']:
                        status = 'normal'
                    elif record['check_in'] and not record['check_out']:
                        status = 'incomplete'
                    elif not record['check_in'] and record['check_out']:
                        status = 'incomplete'
                    
                    base_note = f"匯入自打卡機 {record['device_id']}，原始記錄數: {record['records_count']}"
                    detail_note = record.get('note_detail', '')
                    note = f"{base_note}. {detail_note}" if detail_note else base_note
                    
                    attendance_id = None
                    if existing and overwrite:
                        # 更新現有記錄
                        cursor.execute("""
                            UPDATE attendance 
                            SET check_in = ?, check_out = ?, status = ?, device_id = ?, note = ?
                            WHERE id = ?
                        """, (record['check_in'], record['check_out'], status, record['device_id'], note, existing[0]))
                        attendance_id = existing[0]
                        
                        # 刪除舊的原始記錄
                        cursor.execute("DELETE FROM punch_records WHERE attendance_id = ?", (attendance_id,))
                    else:
                        # 插入新記錄
                        cursor.execute("""
                            INSERT INTO attendance (employee_id, check_in, check_out, status, device_id, note)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (employee_id, record['check_in'], record['check_out'], status, record['device_id'], note))
                        attendance_id = cursor.lastrowid
                    
                    # 插入所有相關的原始打卡記錄到 punch_records 表（只填入基本必要欄位）
                    for raw_record in adjusted_records:
                        if (raw_record['employee_code'] == record['employee_code'] and 
                            raw_record['timestamp'].split(' ')[0] == record['date']):
                            # 解析時間戳
                            punch_datetime = raw_record['original_timestamp']
                            punch_date = punch_datetime.split(' ')[0]
                            punch_time = punch_datetime.split(' ')[1]
                            
                            cursor.execute("""
                                INSERT INTO punch_records (
                                    device_id, employee_id, punch_date, punch_time, 
                                    punch_datetime, status_code
                                ) VALUES (?, ?, ?, ?, ?, ?)
                            """, (
                                raw_record['machine_code'],  # device_id
                                raw_record['employee_code'],  # employee_id (原始員工編號)
                                punch_date,  # punch_date
                                punch_time,  # punch_time
                                punch_datetime,  # punch_datetime
                                raw_record['status_code']  # status_code
                            ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"匯入記錄失敗: {e}, 記錄: {record}")
                    error_count += 1
                    continue
            
            # ===== 提交事務 =====
            conn.commit()
            
            result = {
                'success': True,
                'imported': imported_count,
                'skipped': skipped_count,
                'errors': error_count,
                'total': len(attendance_records),
                'created_employees': created_employees,
                'message': f"匯入完成！成功 {imported_count} 筆，跳過 {skipped_count} 筆，錯誤 {error_count} 筆"
            }
            
            logger.info(f"文字檔匯入完成: {result}")
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"匯入處理錯誤: {e}")
            conn.rollback()
            return jsonify({"success": False, "error": f"匯入處理失敗: {str(e)}"}), 500
        
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"匯入API錯誤: {e}")
        return jsonify({"success": False, "error": f"系統錯誤: {str(e)}"}), 500 