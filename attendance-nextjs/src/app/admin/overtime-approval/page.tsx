"use client"

import { useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { login } from '@/lib/api-client'
import {
    ArrowLeft,
    Clock,
    PlusCircle,
    CheckCircle,
    XCircle,
    Calendar,
    User,
    FileText,
    Eye,
    Check,
    X,
    AlertCircle,
    Search,
    Filter,
    Download,
    RefreshCw,
    MessageSquare,
    Building,
    Timer
} from 'lucide-react'

interface OvertimeRequest {
    id: number
    employee_id: string
    employee_name: string
    department_name: string
    overtime_date: string
    start_time: string
    end_time: string
    hours: number
    overtime_type: string
    reason: string
    status: 'pending' | 'approved' | 'rejected'
    submitted_at: string
    created_at: string
    approved_by?: string
    approved_at?: string
    rejection_reason?: string
}

export default function OvertimeApprovalPage() {
    const { user, login: authLogin, loading } = useAuth()
    const router = useRouter()
    const [overtimeRequests, setOvertimeRequests] = useState<OvertimeRequest[]>([])
    const [dataLoading, setDataLoading] = useState(false)
    const [autoLoginLoading, setAutoLoginLoading] = useState(false)
    const [selectedRequest, setSelectedRequest] = useState<OvertimeRequest | null>(null)
    const [showApprovalModal, setShowApprovalModal] = useState(false)
    const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve')
    const [approvalComment, setApprovalComment] = useState('')
    const [searchParams, setSearchParams] = useState({
        employee_name: '',
        status: '',
        overtime_type: ''
    })
    const [filteredRequests, setFilteredRequests] = useState<OvertimeRequest[]>([])
    const [stats, setStats] = useState({
        pending: 0,
        todayNew: 0,
        approved: 0,
        rejected: 0
    })

    // 加班類型對應
    const overtimeTypeMap: { [key: string]: string } = {
        weekday: '平日加班',
        weekend: '假日加班',
        holiday: '國定假日加班',
        urgent: '緊急加班',
        project: '專案加班'
    }

    // 班表時間對應
    const shiftTimeMap: { [key: string]: { start: string, end: string } } = {
        weekday: { start: '08:30', end: '17:30' }, // 標準日班
        weekend: { start: '09:00', end: '18:00' }, // 假日班
        holiday: { start: '09:00', end: '18:00' }, // 國定假日班
        urgent: { start: '08:30', end: '17:30' }, // 緊急加班（依標準班）
        project: { start: '08:30', end: '17:30' }  // 專案加班（依標準班）
    }

    // 自動登入功能 - 使用管理員測試帳號
    const handleAutoLogin = async () => {
        setAutoLoginLoading(true)
        try {
            console.log('開始自動登入管理員帳號')
            const response = await login({
                employee_id: 'admin',
                password: 'admin123'
            })

            if (response && response.user) {
                const userData = response.user
                const user = {
                    id: userData.employee_id,
                    name: userData.employee_name,
                    employee_id: userData.employee_code,
                    department_id: userData.department_id,
                    position: userData.role_id === 999 ? '系統管理員' : '員工',
                    email: userData.email,
                    role_id: userData.role_id,
                    department_name: userData.department_name
                }

                authLogin(user)
                console.log('自動登入成功:', user)
            } else {
                console.error('自動登入失敗:', response)
            }
        } catch (error) {
            console.error('自動登入錯誤:', error)
        } finally {
            setAutoLoginLoading(false)
        }
    }

    // 頁面載入時自動登入（如果未登入）
    useEffect(() => {
        if (!loading && !user) {
            console.log('檢測到未登入，執行自動登入')
            handleAutoLogin()
        }
    }, [loading, user])

    // 載入真實的加班申請數據
    useEffect(() => {
        const fetchOvertimeRequests = async () => {
            try {
                setDataLoading(true)
                const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                    ? 'http://localhost:7072'
                    : `http://${window.location.hostname}:7072`
                const response = await fetch(`${apiBaseUrl}/api/overtime/requests`)

                if (!response.ok) {
                    throw new Error('Failed to fetch overtime requests')
                }

                const data = await response.json()
                const requests = data.records || []

                setOvertimeRequests(requests)
                setFilteredRequests(requests)

                // 計算統計數據
                const newStats = {
                    pending: requests.filter((r: OvertimeRequest) => r.status === 'pending').length,
                    todayNew: requests.filter((r: OvertimeRequest) =>
                        new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()
                    ).length,
                    approved: requests.filter((r: OvertimeRequest) => r.status === 'approved').length,
                    rejected: requests.filter((r: OvertimeRequest) => r.status === 'rejected').length
                }
                setStats(newStats)
            } catch (error) {
                console.error('Error fetching overtime requests:', error)
                // 如果API失敗，設置空數據
                setOvertimeRequests([])
                setFilteredRequests([])
                setStats({ pending: 0, todayNew: 0, approved: 0, rejected: 0 })
            } finally {
                setDataLoading(false)
            }
        }

        fetchOvertimeRequests()
    }, [])

    // 搜索和篩選
    const handleSearch = () => {
        let filtered = overtimeRequests

        if (searchParams.employee_name) {
            filtered = filtered.filter(request =>
                request.employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase())
            )
        }
        if (searchParams.status) {
            filtered = filtered.filter(request => request.status === searchParams.status)
        }
        if (searchParams.overtime_type) {
            filtered = filtered.filter(request => request.overtime_type === searchParams.overtime_type)
        }

        setFilteredRequests(filtered)
    }

    const handleReset = () => {
        setSearchParams({
            employee_name: '',
            status: '',
            overtime_type: ''
        })
        setFilteredRequests(overtimeRequests)
    }

    // 處理審核
    const handleApproval = async () => {
        if (!selectedRequest) return

        try {
            // 調用真實的API
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:7072'
                : `http://${window.location.hostname}:7072`
            const response = await fetch(`${apiBaseUrl}/api/overtime/requests/${selectedRequest.id}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: approvalAction,
                    comment: approvalAction === 'reject' ? approvalComment : undefined
                })
            })

            if (!response.ok) {
                throw new Error('Failed to update overtime request')
            }

            // 重新載入數據
            const fetchResponse = await fetch(`${apiBaseUrl}/api/overtime/requests`)
            if (fetchResponse.ok) {
                const data = await fetchResponse.json()
                const requests = data.records || []

                setOvertimeRequests(requests)
                setFilteredRequests(requests.filter((request: OvertimeRequest) => {
                    let match = true
                    if (searchParams.employee_name) {
                        match = match && request.employee_name.toLowerCase().includes(searchParams.employee_name.toLowerCase())
                    }
                    if (searchParams.status) {
                        match = match && request.status === searchParams.status
                    }
                    if (searchParams.overtime_type) {
                        match = match && request.overtime_type === searchParams.overtime_type
                    }
                    return match
                }))

                // 更新統計
                const newStats = {
                    pending: requests.filter((r: OvertimeRequest) => r.status === 'pending').length,
                    todayNew: requests.filter((r: OvertimeRequest) =>
                        new Date(r.created_at || r.submitted_at).toDateString() === new Date().toDateString()
                    ).length,
                    approved: requests.filter((r: OvertimeRequest) => r.status === 'approved').length,
                    rejected: requests.filter((r: OvertimeRequest) => r.status === 'rejected').length
                }
                setStats(newStats)
            }

            setShowApprovalModal(false)
            setSelectedRequest(null)
            setApprovalComment('')
        } catch (error) {
            console.error('Error updating overtime request:', error)
            alert('審核失敗，請稍後再試')
        }
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-warning-50 text-warning-600 border-warning-200'
            case 'approved': return 'bg-success-50 text-success-600 border-success-200'
            case 'rejected': return 'bg-error-50 text-error-600 border-error-200'
            default: return 'bg-gray-50 text-gray-600 border-gray-200'
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case 'pending': return '待審核'
            case 'approved': return '已核准'
            case 'rejected': return '已拒絕'
            default: return '未知'
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending': return <Clock className="w-3 h-3" />
            case 'approved': return <CheckCircle className="w-3 h-3" />
            case 'rejected': return <XCircle className="w-3 h-3" />
            default: return <AlertCircle className="w-3 h-3" />
        }
    }

    // 獲取班表時間資訊
    const getShiftInfo = (overtimeType: string) => {
        const shiftTime = shiftTimeMap[overtimeType]
        if (!shiftTime) return null

        return {
            shiftStart: shiftTime.start,
            shiftEnd: shiftTime.end,
            description: `班表時間: ${shiftTime.start} - ${shiftTime.end}`
        }
    }

    if (loading || autoLoginLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">
                        {autoLoginLoading ? '自動登入中...' : '載入中...'}
                    </p>
                </div>
            </div>
        )
    }

    if (!user || user.role_id !== 999) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">權限不足</h2>
                    <p className="text-gray-600 mb-4">您沒有權限訪問此頁面</p>
                    <Button onClick={() => router.push('/admin')}>
                        返回管理後台
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
            {/* 🎨 頁面標題 - 統一的標題設計 */}
            <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                <div className="flex items-center justify-between">
                    {/* 📍 左側標題區 */}
                    <div className="relative z-10">
                        <h1 className="text-3xl font-bold mb-2 text-white">加班審核管理</h1>
                        <div className="flex items-center space-x-2">
                            {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                            <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                            </Link>
                            <p className="text-indigo-100 text-base font-medium">管理員工加班申請與審核流程</p>
                        </div>
                    </div>

                    {/* 📍 右側資訊區 */}
                    <div className="flex items-center space-x-3 text-right">
                        <div>
                            <p className="text-sm font-medium text-white">管理員模式</p>
                            <p className="text-xs text-indigo-100">加班審核</p>
                        </div>
                        <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                            <Timer className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto">
                {/* 統計卡片 */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    {/* 待審核總數 */}
                    <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
                        <div className="flex items-center justify-between">
                            <div className="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center">
                                <Clock className="w-6 h-6 text-warning-600" />
                            </div>
                            <div className="text-right">
                                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                                <p className="text-sm text-gray-500">待審核</p>
                            </div>
                        </div>
                    </div>

                    {/* 今日新增 */}
                    <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
                        <div className="flex items-center justify-between">
                            <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                                <PlusCircle className="w-6 h-6 text-blue-600" />
                            </div>
                            <div className="text-right">
                                <p className="text-2xl font-bold text-gray-900">{stats.todayNew}</p>
                                <p className="text-sm text-gray-500">今日新增</p>
                            </div>
                        </div>
                    </div>

                    {/* 已核准 */}
                    <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
                        <div className="flex items-center justify-between">
                            <div className="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center">
                                <CheckCircle className="w-6 h-6 text-success-600" />
                            </div>
                            <div className="text-right">
                                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                                <p className="text-sm text-gray-500">已核准</p>
                            </div>
                        </div>
                    </div>

                    {/* 已拒絕 */}
                    <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-soft">
                        <div className="flex items-center justify-between">
                            <div className="w-12 h-12 bg-error-50 rounded-xl flex items-center justify-center">
                                <XCircle className="w-6 h-6 text-error-600" />
                            </div>
                            <div className="text-right">
                                <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                                <p className="text-sm text-gray-500">已拒絕</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 查詢工具欄 */}
                <div className="bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden mb-8">
                    {/* 標題區域 */}
                    <div className="bg-gradient-to-r from-orange-500/5 to-red-500/5 px-8 py-6 border-b border-gray-100/60">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    <Filter className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-bold text-gray-900">篩選條件</h3>
                                    <p className="text-sm text-gray-500 mt-1">快速找到特定的加班申請</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Button variant="outline" className="flex items-center space-x-2">
                                    <Download className="w-4 h-4" />
                                    <span>匯出報表</span>
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* 查詢表單 */}
                    <div className="p-8">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                            {/* 員工姓名搜尋 */}
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <User className="w-4 h-4 text-blue-600" />
                                    <label className="block text-sm font-semibold text-gray-700">員工姓名</label>
                                </div>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                    <input
                                        type="text"
                                        value={searchParams.employee_name}
                                        onChange={(e) => setSearchParams({ ...searchParams, employee_name: e.target.value })}
                                        placeholder="搜尋員工姓名..."
                                        className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                                    />
                                </div>
                            </div>

                            {/* 審核狀態篩選 */}
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                    <label className="block text-sm font-semibold text-gray-700">審核狀態</label>
                                </div>
                                <select
                                    value={searchParams.status}
                                    onChange={(e) => setSearchParams({ ...searchParams, status: e.target.value })}
                                    className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm"
                                >
                                    <option value="">所有狀態</option>
                                    <option value="pending">待審核</option>
                                    <option value="approved">已核准</option>
                                    <option value="rejected">已拒絕</option>
                                </select>
                            </div>

                            {/* 加班類型篩選 */}
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <Timer className="w-4 h-4 text-orange-600" />
                                    <label className="block text-sm font-semibold text-gray-700">加班類型</label>
                                </div>
                                <select
                                    value={searchParams.overtime_type}
                                    onChange={(e) => setSearchParams({ ...searchParams, overtime_type: e.target.value })}
                                    className="w-full bg-gray-50/80 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-sm"
                                >
                                    <option value="">所有類型</option>
                                    {Object.entries(overtimeTypeMap).map(([key, value]) => (
                                        <option key={key} value={key}>{value}</option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* 操作按鈕 */}
                        <div className="flex items-center justify-between pt-6 border-t border-gray-100">
                            <div className="flex items-center space-x-3">
                                <Button variant="primary" onClick={handleSearch} className="flex items-center space-x-2">
                                    <Search className="w-4 h-4" />
                                    <span>查詢</span>
                                </Button>

                                <Button variant="outline" onClick={handleReset} className="flex items-center space-x-2">
                                    <RefreshCw className="w-4 h-4" />
                                    <span>重置</span>
                                </Button>
                            </div>

                            <div className="text-sm text-gray-500">
                                共找到 {filteredRequests.length} 筆申請
                            </div>
                        </div>
                    </div>
                </div>

                {/* 加班申請列表 */}
                <div className="bg-white/60 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
                    {/* 標題區域 */}
                    <div className="bg-gradient-to-r from-orange-500/5 to-yellow-500/5 px-8 py-6 border-b border-gray-100/60">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    <Timer className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-2xl font-bold text-gray-900">加班申請列表</h3>
                                    <p className="text-sm text-gray-500 mt-1">管理員工的加班申請</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {dataLoading ? (
                        <div className="flex items-center justify-center py-16">
                            <div className="text-center">
                                <div className="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                                <p className="text-gray-500">載入加班申請中...</p>
                            </div>
                        </div>
                    ) : filteredRequests.length > 0 ? (
                        <>
                            {/* 桌面版表格 */}
                            <div className="hidden lg:block overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gradient-to-r from-orange-500 to-yellow-600 text-white">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">申請人</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">加班類型</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">加班日期</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">時間</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">時數</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">狀態</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">申請時間</th>
                                            <th className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {filteredRequests.map((request) => (
                                            <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                                                <td className="px-6 py-4">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                                                            <span className="text-white font-medium text-sm">
                                                                {request.employee_name.charAt(0)}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div className="font-medium text-gray-900">{request.employee_name}</div>
                                                            <div className="text-sm text-gray-500">{request.employee_id}</div>
                                                            <div className="text-xs text-gray-400">{request.department_name}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="space-y-1">
                                                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-50 text-orange-600 border border-orange-200">
                                                            {overtimeTypeMap[request.overtime_type]}
                                                        </span>
                                                        {getShiftInfo(request.overtime_type) && (
                                                            <div className="text-xs text-gray-500">
                                                                {getShiftInfo(request.overtime_type)?.description}
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                    {new Date(request.overtime_date).toLocaleDateString('zh-TW')}
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                    {request.start_time} - {request.end_time}
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                    {request.hours} 小時
                                                </td>
                                                <td className="px-6 py-4">
                                                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                                                        {getStatusIcon(request.status)}
                                                        <span className="ml-1">{getStatusText(request.status)}</span>
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                    {new Date(request.submitted_at).toLocaleDateString('zh-TW')}
                                                    <div className="text-xs text-gray-500">
                                                        {new Date(request.submitted_at).toLocaleTimeString('zh-TW', {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 text-sm font-medium">
                                                    <div className="flex items-center space-x-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => setSelectedRequest(request)}
                                                        >
                                                            <Eye className="w-4 h-4" />
                                                        </Button>
                                                        {request.status === 'pending' && (
                                                            <>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => {
                                                                        setSelectedRequest(request)
                                                                        setApprovalAction('approve')
                                                                        setShowApprovalModal(true)
                                                                    }}
                                                                >
                                                                    <Check className="w-4 h-4 text-green-600" />
                                                                </Button>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => {
                                                                        setSelectedRequest(request)
                                                                        setApprovalAction('reject')
                                                                        setShowApprovalModal(true)
                                                                    }}
                                                                >
                                                                    <X className="w-4 h-4 text-red-600" />
                                                                </Button>
                                                            </>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* 手機版卡片 */}
                            <div className="lg:hidden space-y-4 p-6">
                                {filteredRequests.map((request) => (
                                    <div key={request.id} className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="flex items-center space-x-3">
                                                <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                                                    <span className="text-white font-medium text-sm">
                                                        {request.employee_name.charAt(0)}
                                                    </span>
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">{request.employee_name}</h3>
                                                    <p className="text-sm text-gray-500">{request.department_name}</p>
                                                </div>
                                            </div>
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                                                {getStatusIcon(request.status)}
                                                <span className="ml-1">{getStatusText(request.status)}</span>
                                            </span>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                                            <div>
                                                <span className="text-gray-500">類型:</span>
                                                <div className="mt-1">
                                                    <span className="text-gray-900">{overtimeTypeMap[request.overtime_type]}</span>
                                                    {getShiftInfo(request.overtime_type) && (
                                                        <div className="text-xs text-gray-400 mt-1">
                                                            {getShiftInfo(request.overtime_type)?.description}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div>
                                                <span className="text-gray-500">時數:</span>
                                                <span className="ml-2 text-gray-900">{request.hours} 小時</span>
                                            </div>
                                            <div>
                                                <span className="text-gray-500">日期:</span>
                                                <span className="ml-2 text-gray-900">
                                                    {new Date(request.overtime_date).toLocaleDateString('zh-TW')}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-gray-500">時間:</span>
                                                <span className="ml-2 text-gray-900">
                                                    {request.start_time} - {request.end_time}
                                                </span>
                                            </div>
                                        </div>

                                        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                                            <span className="text-gray-500 text-sm">原因:</span>
                                            <p className="text-gray-900 text-sm mt-1">{request.reason}</p>
                                        </div>

                                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                                            <div className="text-xs text-gray-500">
                                                申請於 {new Date(request.submitted_at).toLocaleDateString('zh-TW')}
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => setSelectedRequest(request)}
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                                {request.status === 'pending' && (
                                                    <>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedRequest(request)
                                                                setApprovalAction('approve')
                                                                setShowApprovalModal(true)
                                                            }}
                                                        >
                                                            <Check className="w-4 h-4 text-green-600" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedRequest(request)
                                                                setApprovalAction('reject')
                                                                setShowApprovalModal(true)
                                                            }}
                                                        >
                                                            <X className="w-4 h-4 text-red-600" />
                                                        </Button>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    ) : (
                        <div className="text-center py-16">
                            <Timer className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">沒有找到加班申請</h3>
                            <p className="text-gray-500">目前沒有符合條件的加班申請記錄</p>
                        </div>
                    )}
                </div>
            </main>

            {/* 審核模態框 */}
            {showApprovalModal && selectedRequest && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-2xl max-w-md w-full p-6">
                        <h3 className="text-lg font-semibold mb-4">
                            {approvalAction === 'approve' ? '核准' : '拒絕'}加班申請
                        </h3>

                        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-2">申請詳情</h4>
                            <div className="space-y-1 text-sm">
                                <div><span className="text-gray-500">申請人:</span> {selectedRequest.employee_name}</div>
                                <div><span className="text-gray-500">加班日期:</span> {new Date(selectedRequest.overtime_date).toLocaleDateString('zh-TW')}</div>
                                <div><span className="text-gray-500">加班時間:</span> {selectedRequest.start_time} - {selectedRequest.end_time}</div>
                                <div><span className="text-gray-500">加班時數:</span> {selectedRequest.hours} 小時</div>
                                <div><span className="text-gray-500">加班類型:</span> {overtimeTypeMap[selectedRequest.overtime_type]}</div>
                                <div><span className="text-gray-500">申請原因:</span> {selectedRequest.reason}</div>
                            </div>
                        </div>

                        {approvalAction === 'reject' && (
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    拒絕原因 <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    value={approvalComment}
                                    onChange={(e) => setApprovalComment(e.target.value)}
                                    rows={3}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                    placeholder="請說明拒絕原因..."
                                />
                            </div>
                        )}

                        <div className="flex space-x-3">
                            <Button
                                variant={approvalAction === 'approve' ? 'primary' : 'error'}
                                onClick={handleApproval}
                                disabled={approvalAction === 'reject' && !approvalComment.trim()}
                                className="flex-1"
                            >
                                {approvalAction === 'approve' ? '確認核准' : '確認拒絕'}
                            </Button>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setShowApprovalModal(false)
                                    setApprovalComment('')
                                    setSelectedRequest(null)
                                }}
                                className="flex-1"
                            >
                                取消
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* 詳情模態框 */}
            {selectedRequest && !showApprovalModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-2xl max-w-lg w-full p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-lg font-semibold">加班申請詳情</h3>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedRequest(null)}
                            >
                                <X className="w-4 h-4" />
                            </Button>
                        </div>

                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm text-gray-500">申請人</label>
                                    <p className="font-medium">{selectedRequest.employee_name}</p>
                                </div>
                                <div>
                                    <label className="text-sm text-gray-500">部門</label>
                                    <p className="font-medium">{selectedRequest.department_name}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm text-gray-500">加班日期</label>
                                    <p className="font-medium">{new Date(selectedRequest.overtime_date).toLocaleDateString('zh-TW')}</p>
                                </div>
                                <div>
                                    <label className="text-sm text-gray-500">加班類型</label>
                                    <p className="font-medium">{overtimeTypeMap[selectedRequest.overtime_type]}</p>
                                    {getShiftInfo(selectedRequest.overtime_type) && (
                                        <p className="text-xs text-gray-400 mt-1">
                                            {getShiftInfo(selectedRequest.overtime_type)?.description}
                                        </p>
                                    )}
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm text-gray-500">加班時間</label>
                                    <p className="font-medium">{selectedRequest.start_time} - {selectedRequest.end_time}</p>
                                </div>
                                <div>
                                    <label className="text-sm text-gray-500">加班時數</label>
                                    <p className="font-medium">{selectedRequest.hours} 小時</p>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm text-gray-500">申請原因</label>
                                <p className="font-medium mt-1 p-3 bg-gray-50 rounded-lg">{selectedRequest.reason}</p>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm text-gray-500">申請時間</label>
                                    <p className="font-medium">{new Date(selectedRequest.submitted_at).toLocaleString('zh-TW')}</p>
                                </div>
                                <div>
                                    <label className="text-sm text-gray-500">狀態</label>
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedRequest.status)}`}>
                                        {getStatusIcon(selectedRequest.status)}
                                        <span className="ml-1">{getStatusText(selectedRequest.status)}</span>
                                    </span>
                                </div>
                            </div>

                            {selectedRequest.status !== 'pending' && (
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm text-gray-500">審核人</label>
                                        <p className="font-medium">{selectedRequest.approved_by}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm text-gray-500">審核時間</label>
                                        <p className="font-medium">
                                            {selectedRequest.approved_at && new Date(selectedRequest.approved_at).toLocaleString('zh-TW')}
                                        </p>
                                    </div>
                                </div>
                            )}

                            {selectedRequest.rejection_reason && (
                                <div>
                                    <label className="text-sm text-gray-500">拒絕原因</label>
                                    <p className="font-medium mt-1 p-3 bg-red-50 rounded-lg text-red-700">
                                        {selectedRequest.rejection_reason}
                                    </p>
                                </div>
                            )}
                        </div>

                        {selectedRequest.status === 'pending' && (
                            <div className="flex space-x-3 mt-6 pt-6 border-t">
                                <Button
                                    variant="primary"
                                    onClick={() => {
                                        setApprovalAction('approve')
                                        setShowApprovalModal(true)
                                    }}
                                    className="flex-1"
                                >
                                    <Check className="w-4 h-4 mr-2" />
                                    核准
                                </Button>
                                <Button
                                    variant="error"
                                    onClick={() => {
                                        setApprovalAction('reject')
                                        setShowApprovalModal(true)
                                    }}
                                    className="flex-1"
                                >
                                    <X className="w-4 h-4 mr-2" />
                                    拒絕
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    )
} 