#!/usr/bin/env python3
"""
增量考勤整理功能測試腳本

測試功能：
1. 獲取上次整理日期
2. 執行增量考勤整理
3. 驗證處理結果
4. 測試強制指定開始日期
"""

import requests
import json
from datetime import datetime, timedelta
import sqlite3
from database import create_connection

# API 基礎 URL
BASE_URL = "http://localhost:7072"

def test_get_last_process_date():
    """
    測試獲取上次整理日期功能
    """
    print("=" * 60)
    print("測試 1: 獲取上次整理日期")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/api/attendance/management/last-process-date")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 獲取上次整理日期成功")
            print(f"📅 上次整理日期: {data.get('last_process_date', '尚未執行過')}")
            print(f"📅 下次建議日期: {data.get('next_process_date', '無')}")
            print(f"📊 距離上次整理: {data.get('days_since_last', 0)} 天")
            print(f"🔄 是否有待處理: {'是' if data.get('has_pending_days') else '否'}")
            
            return data
        else:
            print(f"❌ 獲取上次整理日期失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 獲取上次整理日期異常: {str(e)}")
        return None


def test_incremental_process():
    """
    測試增量考勤整理功能
    """
    print("\n" + "=" * 60)
    print("測試 2: 增量考勤整理")
    print("=" * 60)
    
    try:
        # 準備請求資料
        request_data = {
            "department_ids": [],  # 空陣列表示處理所有部門
            "force_start_date": None  # 使用系統記錄的上次整理日期
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/incremental-process",
            json=request_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 增量考勤整理成功")
            print(f"📊 處理摘要:")
            
            summary = data.get('processing_summary', {})
            print(f"   - 開始日期: {summary.get('start_date')}")
            print(f"   - 結束日期: {summary.get('end_date')}")
            print(f"   - 總處理天數: {summary.get('total_days', 0)}")
            print(f"   - 成功天數: {summary.get('successful_days', 0)}")
            print(f"   - 失敗天數: {summary.get('failed_days', 0)}")
            print(f"   - 新增記錄: {summary.get('total_generated', 0)} 筆")
            print(f"   - 更新記錄: {summary.get('total_updated', 0)} 筆")
            print(f"   - 總處理記錄: {summary.get('total_processed', 0)} 筆")
            
            # 顯示每日處理結果
            daily_results = data.get('daily_results', [])
            if daily_results:
                print(f"\n📋 每日處理詳情:")
                for result in daily_results[:5]:  # 只顯示前5天
                    status = "✅" if result.get('success') else "❌"
                    print(f"   {status} {result.get('date')}: 新增 {result.get('generated_count', 0)}, 更新 {result.get('updated_count', 0)}")
                
                if len(daily_results) > 5:
                    print(f"   ... 還有 {len(daily_results) - 5} 天的記錄")
            
            return data
        else:
            print(f"❌ 增量考勤整理失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 增量考勤整理異常: {str(e)}")
        return None


def test_force_start_date():
    """
    測試強制指定開始日期功能
    """
    print("\n" + "=" * 60)
    print("測試 3: 強制指定開始日期")
    print("=" * 60)
    
    # 測試從3天前開始
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    try:
        request_data = {
            "department_ids": [],
            "force_start_date": start_date
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/incremental-process",
            json=request_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 強制指定開始日期成功")
            print(f"📅 指定開始日期: {start_date}")
            
            summary = data.get('processing_summary', {})
            print(f"📊 處理結果:")
            print(f"   - 實際開始日期: {summary.get('start_date')}")
            print(f"   - 結束日期: {summary.get('end_date')}")
            print(f"   - 處理天數: {summary.get('total_days', 0)}")
            print(f"   - 成功天數: {summary.get('successful_days', 0)}")
            
            return data
        else:
            print(f"❌ 強制指定開始日期失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 強制指定開始日期異常: {str(e)}")
        return None


def test_department_filter():
    """
    測試部門篩選功能
    """
    print("\n" + "=" * 60)
    print("測試 4: 部門篩選功能")
    print("=" * 60)
    
    try:
        # 先獲取部門列表
        conn = create_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM departments LIMIT 2")
        departments = cursor.fetchall()
        conn.close()
        
        if not departments:
            print("⚠️  沒有找到部門資料，跳過部門篩選測試")
            return None
        
        department_ids = [dept[0] for dept in departments]
        department_names = [dept[1] for dept in departments]
        
        print(f"🏢 測試部門: {', '.join(department_names)}")
        
        request_data = {
            "department_ids": department_ids,
            "force_start_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        }
        
        response = requests.post(
            f"{BASE_URL}/api/attendance/management/incremental-process",
            json=request_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 部門篩選處理成功")
            
            summary = data.get('processing_summary', {})
            print(f"📊 處理結果:")
            print(f"   - 處理天數: {summary.get('total_days', 0)}")
            print(f"   - 總處理記錄: {summary.get('total_processed', 0)} 筆")
            
            return data
        else:
            print(f"❌ 部門篩選處理失敗: {response.status_code}")
            print(f"錯誤訊息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 部門篩選處理異常: {str(e)}")
        return None


def verify_system_settings():
    """
    驗證系統設定表中的記錄
    """
    print("\n" + "=" * 60)
    print("測試 5: 驗證系統設定記錄")
    print("=" * 60)
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 查詢系統設定
        cursor.execute("""
            SELECT setting_key, setting_value, updated_at
            FROM system_settings
            WHERE setting_key = 'last_attendance_process_date'
        """)
        
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 找到系統設定記錄")
            print(f"📅 設定鍵: {result[0]}")
            print(f"📅 設定值: {result[1]}")
            print(f"📅 更新時間: {result[2]}")
        else:
            print(f"⚠️  沒有找到系統設定記錄")
        
        # 查詢所有系統設定
        cursor.execute("SELECT * FROM system_settings")
        all_settings = cursor.fetchall()
        
        print(f"\n📋 所有系統設定 ({len(all_settings)} 筆):")
        for setting in all_settings:
            print(f"   - {setting[1]}: {setting[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證系統設定異常: {str(e)}")


def create_test_punch_records():
    """
    創建測試打卡記錄
    """
    print("\n" + "=" * 60)
    print("測試 6: 創建測試打卡記錄")
    print("=" * 60)
    
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 獲取前3名員工
        cursor.execute("SELECT id, name FROM employees LIMIT 3")
        employees = cursor.fetchall()
        
        if not employees:
            print("⚠️  沒有找到員工資料")
            return
        
        # 為過去3天創建測試打卡記錄
        for i in range(3):
            test_date = datetime.now() - timedelta(days=i+1)
            date_str = test_date.strftime('%Y-%m-%d')
            
            for j, employee in enumerate(employees):
                employee_id = employee[0]
                employee_name = employee[1]
                
                # 創建上班打卡記錄
                clock_in_time = f"{date_str} 0{8+j}:{30+j*10}:00"
                cursor.execute("""
                    INSERT OR IGNORE INTO punch_records (employee_id, punch_time, note)
                    VALUES (?, ?, ?)
                """, (employee_id, clock_in_time, f"測試上班打卡 - {employee_name}"))
                
                # 創建下班打卡記錄（只為部分員工）
                if j < 2:  # 只為前2名員工創建下班記錄
                    clock_out_time = f"{date_str} 1{7+j}:{30+j*10}:00"
                    cursor.execute("""
                        INSERT OR IGNORE INTO punch_records (employee_id, punch_time, note)
                        VALUES (?, ?, ?)
                    """, (employee_id, clock_out_time, f"測試下班打卡 - {employee_name}"))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 測試打卡記錄創建完成")
        print(f"📊 為 {len(employees)} 名員工創建了過去 3 天的打卡記錄")
        
    except Exception as e:
        print(f"❌ 創建測試打卡記錄異常: {str(e)}")


def main():
    """
    主測試函數
    """
    print("🚀 開始增量考勤整理功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 創建測試打卡記錄
    create_test_punch_records()
    
    # 2. 測試獲取上次整理日期
    last_date_data = test_get_last_process_date()
    
    # 3. 測試增量考勤整理
    process_data = test_incremental_process()
    
    # 4. 再次檢查上次整理日期（應該已更新）
    if process_data:
        print("\n" + "=" * 60)
        print("驗證: 檢查整理日期是否已更新")
        print("=" * 60)
        updated_date_data = test_get_last_process_date()
        
        if updated_date_data and last_date_data:
            old_date = last_date_data.get('last_process_date')
            new_date = updated_date_data.get('last_process_date')
            
            if old_date != new_date:
                print(f"✅ 整理日期已更新: {old_date} → {new_date}")
            else:
                print(f"⚠️  整理日期未更新: {new_date}")
    
    # 5. 測試強制指定開始日期
    test_force_start_date()
    
    # 6. 測試部門篩選
    test_department_filter()
    
    # 7. 驗證系統設定
    verify_system_settings()
    
    print("\n" + "=" * 60)
    print("🎉 增量考勤整理功能測試完成")
    print("=" * 60)


if __name__ == "__main__":
    main() 