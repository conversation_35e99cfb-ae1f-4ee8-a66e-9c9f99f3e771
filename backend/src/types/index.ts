/**
 * Han AttendanceOS v2025.6.8 - TypeScript 類型定義
 * 遠漢科技考勤系統統一類型規範
 */

// ========== 基礎類型 ==========

export interface BaseEntity {
  id: number
  created_at: string
  updated_at: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: Record<string, string[]>
}

export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: PaginationMeta
}

// ========== 員工相關類型 ==========

export interface Employee extends BaseEntity {
  employee_id: string
  name: string
  email?: string
  phone?: string
  department_id: number
  department_name?: string
  position_id: number
  position_name?: string
  hire_date: string
  status: 'active' | 'inactive' | 'suspended'
  avatar?: string
  salary?: number
  manager_id?: number
  manager_name?: string
}

export interface Department extends BaseEntity {
  name: string
  code: string
  description?: string
  manager_id?: number
  manager_name?: string
  employee_count?: number
}

export interface Position extends BaseEntity {
  name: string
  code: string
  description?: string
  department_id: number
  department_name?: string
  level: number
  salary_min?: number
  salary_max?: number
}

// ========== 考勤相關類型 ==========

export interface AttendanceRecord extends BaseEntity {
  employee_id: number
  employee_name?: string
  date: string
  work_date: string
  shift_id: number
  shift_name?: string
  clock_in_time?: string
  clock_out_time?: string
  break_start_time?: string
  break_end_time?: string
  work_hours: number
  overtime_hours: number
  late_minutes: number
  early_leave_minutes: number
  status: 'present' | 'absent' | 'late' | 'early' | 'overtime' | 'leave'
  date_type: 'workday' | 'weekend' | 'holiday'
  notes?: string
  location?: string
  ip_address?: string
}

export interface PunchRecord extends BaseEntity {
  employee_id: number
  employee_name?: string
  punch_time: string
  punch_type: 'in' | 'out' | 'break_start' | 'break_end'
  location?: string
  ip_address?: string
  device_info?: string
  photo_url?: string
  notes?: string
}

export interface Shift extends BaseEntity {
  name: string
  code: string
  start_time: string
  end_time: string
  break_start_time?: string
  break_end_time?: string
  work_hours: number
  color: string
  is_active: boolean
  description?: string
  tolerance_minutes: number
}

// ========== 請假相關類型 ==========

export interface LeaveType extends BaseEntity {
  name: string
  code: string
  days_per_year: number
  is_paid: boolean
  requires_approval: boolean
  color: string
  description?: string
}

export interface LeaveRequest extends BaseEntity {
  employee_id: number
  employee_name?: string
  leave_type_id: number
  leave_type_name?: string
  start_date: string
  end_date: string
  start_time?: string
  end_time?: string
  days: number
  hours: number
  reason: string
  substitute_id?: number
  substitute_name?: string
  status: 'draft' | 'pending' | 'approved' | 'rejected'
  approver_id?: number
  approver_name?: string
  approved_at?: string
  comments?: string
  attachments?: string[]
}

// ========== 加班相關類型 ==========

export interface OvertimeType extends BaseEntity {
  name: string
  code: string
  rate_multiplier: number
  description?: string
}

export interface OvertimeRequest extends BaseEntity {
  employee_id: number
  employee_name?: string
  overtime_type_id: number
  overtime_type_name?: string
  date: string
  start_time: string
  end_time: string
  hours: number
  reason: string
  status: 'draft' | 'pending' | 'approved' | 'rejected'
  approver_id?: number
  approver_name?: string
  approved_at?: string
  comments?: string
}

// ========== 審核相關類型 ==========

export interface ApprovalRequest extends BaseEntity {
  type: 'leave' | 'overtime' | 'attendance_edit'
  reference_id: number
  employee_id: number
  employee_name?: string
  title: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  approver_id?: number
  approver_name?: string
  approved_at?: string
  comments?: string
  data?: Record<string, any>
}

// ========== 報表相關類型 ==========

export interface AttendanceStats {
  total_employees: number
  present_count: number
  absent_count: number
  late_count: number
  early_leave_count: number
  overtime_count: number
  leave_count: number
  attendance_rate: number
}

export interface EmployeeAttendanceStats extends BaseEntity {
  employee_id: number
  employee_name: string
  department_name: string
  total_days: number
  present_days: number
  absent_days: number
  late_days: number
  early_leave_days: number
  total_work_hours: number
  total_overtime_hours: number
  attendance_rate: number
}

export interface DepartmentStats extends BaseEntity {
  department_id: number
  department_name: string
  employee_count: number
  attendance_rate: number
  average_work_hours: number
  total_overtime_hours: number
}

// ========== 系統相關類型 ==========

export interface SystemConfig extends BaseEntity {
  key: string
  value: string
  type: 'string' | 'number' | 'boolean' | 'json'
  description?: string
  category: string
}

export interface AuditLog extends BaseEntity {
  user_id: number
  user_name?: string
  action: string
  resource_type: string
  resource_id?: number
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
}

// ========== 認證相關類型 ==========

export interface User extends BaseEntity {
  username: string
  email: string
  role: 'admin' | 'manager' | 'employee'
  employee_id?: number
  employee?: Employee
  last_login_at?: string
  is_active: boolean
}

export interface LoginRequest {
  username: string
  password: string
  remember_me?: boolean
}

export interface LoginResponse {
  success: boolean
  user?: User
  token?: string
  expires_at?: string
  message?: string
}

export interface Session {
  user: User
  token: string
  expires_at: string
}

// ========== 表單相關類型 ==========

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'time' | 'select' | 'textarea' | 'checkbox' | 'radio'
  required?: boolean
  placeholder?: string
  options?: { value: string | number; label: string }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

export interface FormData {
  [key: string]: any
}

export interface FormErrors {
  [key: string]: string[]
}

// ========== UI 相關類型 ==========

export interface MenuItem {
  id: string
  label: string
  icon?: string
  href?: string
  children?: MenuItem[]
  badge?: string | number
  active?: boolean
  disabled?: boolean
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  actions?: {
    label: string
    action: () => void
  }[]
}

export interface Modal {
  id: string
  title: string
  content: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  onClose?: () => void
}

export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: T, index: number) => React.ReactNode
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[]
  data: T[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  rowKey?: string | ((record: T) => string)
  onRow?: (record: T, index: number) => React.HTMLAttributes<HTMLTableRowElement>
}

// ========== 圖表相關類型 ==========

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}

export interface ChartOptions {
  responsive?: boolean
  maintainAspectRatio?: boolean
  plugins?: {
    legend?: {
      display?: boolean
      position?: 'top' | 'bottom' | 'left' | 'right'
    }
    title?: {
      display?: boolean
      text?: string
    }
  }
  scales?: {
    x?: {
      display?: boolean
      title?: {
        display?: boolean
        text?: string
      }
    }
    y?: {
      display?: boolean
      title?: {
        display?: boolean
        text?: string
      }
    }
  }
}

// ========== 導出所有類型 ==========

export type Status = 'active' | 'inactive' | 'pending' | 'approved' | 'rejected' | 'draft'
export type AttendanceStatus = 'present' | 'absent' | 'late' | 'early' | 'overtime' | 'leave'
export type DateType = 'workday' | 'weekend' | 'holiday'
export type PunchType = 'in' | 'out' | 'break_start' | 'break_end'
export type UserRole = 'admin' | 'manager' | 'employee'
export type NotificationType = 'success' | 'error' | 'warning' | 'info' 