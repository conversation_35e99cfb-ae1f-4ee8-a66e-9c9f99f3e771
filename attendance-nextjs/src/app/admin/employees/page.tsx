"use client"

import { withAuth, useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  getEmployees,
  getDepartments,
  getSkills,
  getEmployeePromotions,
  getEmployeeRewards,
  getEducationLevels,
  getPromotionTypes,
  getRewardTypes,
  getEmployeeFullDetails,
  updateEmployee,
  getPositions,
  getRoles
} from '@/lib/api-client'
import type { Employee } from '@/types'
import {
  Users,
  Search,
  Plus,
  Upload,
  Download,
  ArrowLeft,
  User,
  Hash,
  Briefcase,
  Building,
  Phone,
  Mail,
  Edit,
  Trash2,
  Eye,
  Shield,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Info,
  Calendar,
  MapPin,
  ChevronLeft,
  ChevronRight,
  GraduationCap,
  Award,
  Activity,
  Settings,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

export default function EmployeesPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<string[]>([])
  const [departmentOptions, setDepartmentOptions] = useState<{ id: number, name: string }[]>([])
  const [positionOptions, setPositionOptions] = useState<{ id: number, name: string }[]>([])
  const [roleOptions, setRoleOptions] = useState<{ id: number, role_name: string, permission_level: number }[]>([])
  const [loading, setLoading] = useState(true)
  const [searchParams, setSearchParams] = useState({
    name: '',
    employee_id: '',
    position: '',
    department: ''
  })
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([])
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [editFormData, setEditFormData] = useState<any>({})

  // 查看員工詳細資料的狀態
  const [showViewModal, setShowViewModal] = useState(false)
  const [viewEmployeeData, setViewEmployeeData] = useState<any>(null)
  const [loadingViewData, setLoadingViewData] = useState(false)

  // 額外的數據狀態
  const [skills, setSkills] = useState<any[]>([])
  const [educationLevels, setEducationLevels] = useState<any[]>([])
  const [promotionTypes, setPromotionTypes] = useState<any[]>([])
  const [rewardTypes, setRewardTypes] = useState<any[]>([])
  const [employeePromotions, setEmployeePromotions] = useState<any[]>([])
  const [employeeRewards, setEmployeeRewards] = useState<any[]>([])
  const [employeeSkills, setEmployeeSkills] = useState<any[]>([])
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [savingEmployee, setSavingEmployee] = useState(false)

  // 載入員工資料
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        console.log('開始載入員工資料...')
        const response = await getEmployees()
        console.log('員工資料響應:', response)

        if (response.success && response.data) {
          console.log('成功載入員工資料:', response.data)
          setEmployees(response.data)
          setFilteredEmployees(response.data)

          // 提取部門列表
          const uniqueDepartments = Array.from(new Set(response.data.map(emp => emp.department_name).filter(Boolean))) as string[]
          setDepartments(uniqueDepartments)

          // 提取部門選項（ID 和名稱）
          const departmentMap = new Map()
          response.data.forEach(emp => {
            if (emp.department_id && emp.department_name) {
              departmentMap.set(emp.department_id, emp.department_name)
            }
          })
          const deptOptions = Array.from(departmentMap.entries()).map(([id, name]) => ({
            id: Number(id),
            name: name as string
          }))
          setDepartmentOptions(deptOptions)
        } else {
          console.error('載入員工資料失敗:', response.error)
        }
      } catch (error) {
        console.error('載入員工資料錯誤:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchEmployees()
    loadMasterData()
  }, [])

  // 載入主數據
  const loadMasterData = async () => {
    try {
      // 載入技能列表
      const skillsResponse = await getSkills()
      if (skillsResponse.success && skillsResponse.data?.skills) {
        setSkills(skillsResponse.data.skills)
      }

      // 載入學歷等級
      const educationResponse = await getEducationLevels()
      if (educationResponse.success && educationResponse.data?.education_levels) {
        setEducationLevels(educationResponse.data.education_levels)
      }

      // 🆕 載入職位選項
      const positionsResponse = await getPositions()
      if (positionsResponse.success && positionsResponse.data?.records) {
        const positions = positionsResponse.data.records.map(pos => ({
          id: pos.id,
          name: pos.name
        }))
        setPositionOptions(positions)
        console.log('載入職位選項:', positions)
      }

      // 🆕 載入角色選項
      const rolesResponse = await getRoles()
      if (rolesResponse.success && rolesResponse.data?.roles) {
        setRoleOptions(rolesResponse.data.roles)
        console.log('載入角色選項:', rolesResponse.data.roles)
      }

      // 載入升遷類型
      const promotionTypesResponse = await getPromotionTypes()
      if (promotionTypesResponse.success && promotionTypesResponse.data?.promotion_types) {
        setPromotionTypes(promotionTypesResponse.data.promotion_types)
      }

      // 載入獎懲類型
      const rewardTypesResponse = await getRewardTypes()
      if (rewardTypesResponse.success && rewardTypesResponse.data?.reward_types) {
        setRewardTypes(rewardTypesResponse.data.reward_types)
      }
    } catch (error) {
      console.error('載入主數據失敗:', error)
    }
  }

  // 搜索和篩選
  const handleSearch = () => {
    let filtered = employees

    if (searchParams.name) {
      filtered = filtered.filter(emp =>
        emp.name.toLowerCase().includes(searchParams.name.toLowerCase())
      )
    }
    if (searchParams.employee_id) {
      filtered = filtered.filter(emp =>
        emp.employee_id.toLowerCase().includes(searchParams.employee_id.toLowerCase())
      )
    }
    if (searchParams.position) {
      filtered = filtered.filter(emp =>
        emp.position.toLowerCase().includes(searchParams.position.toLowerCase())
      )
    }
    if (searchParams.department) {
      filtered = filtered.filter(emp => emp.department_name === searchParams.department)
    }

    setFilteredEmployees(filtered)
  }

  const handleReset = () => {
    setSearchParams({
      name: '',
      employee_id: '',
      position: '',
      department: ''
    })
    setFilteredEmployees(employees)
  }

  const getRoleText = (roleId?: number) => {
    if (!roleId) return '未設定角色'
    const role = roleOptions.find(r => r.id === roleId)
    return role ? role.role_name : '未知角色'
  }

  const getRoleColor = (roleId?: number) => {
    if (!roleId) return 'text-gray-600 bg-gray-50'
    const role = roleOptions.find(r => r.id === roleId)
    if (!role) return 'text-gray-600 bg-gray-50'

    // 根據權限等級分配顏色
    if (role.permission_level >= 99) return 'text-red-600 bg-red-50'      // 系統管理員
    if (role.permission_level >= 50) return 'text-purple-600 bg-purple-50' // 部門主管
    if (role.permission_level >= 10) return 'text-blue-600 bg-blue-50'     // 一般員工
    return 'text-gray-600 bg-gray-50'
  }

  // 獲取狀態文字
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '在職'
      case 'inactive': return '離職'
      case 'suspended': return '停職'
      default: return '未知'
    }
  }

  // 獲取狀態顏色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50'
      case 'inactive': return 'text-red-600 bg-red-50'
      case 'suspended': return 'text-yellow-600 bg-yellow-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  // 根據姓名生成頭像背景顏色
  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
      'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-emerald-500'
    ]

    // 根據姓名的第一個字符的Unicode值選擇顏色
    const charCode = name.charCodeAt(0)
    return colors[charCode % colors.length]
  }

  // 生成技能顯示HTML
  const generateSkillsDisplay = (skills: any[]) => {
    if (!skills || skills.length === 0) {
      return <span className="text-gray-400 text-xs">暫無技能資料</span>
    }

    // 最多顯示3個技能，其餘用+N表示
    const displaySkills = skills.slice(0, 3)
    const remainingCount = skills.length - 3

    return (
      <div className="flex flex-wrap gap-1">
        {displaySkills.map((skill, index) => (
          <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {skill.name || skill}
          </span>
        ))}
        {remainingCount > 0 && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
            +{remainingCount}
          </span>
        )}
      </div>
    )
  }

  // 編輯員工
  const handleEditEmployee = async (employee: Employee) => {
    setSelectedEmployee(employee)
    setEditFormData({
      name: employee.name,
      employee_id: employee.employee_id,
      email: employee.email,
      phone: employee.phone,
      position: employee.position,
      department_id: employee.department_id,
      role_id: employee.role_id,
      status: employee.status,
      hire_date: employee.hire_date,
      photo_url: employee.photo_url,
      address: (employee as any).address,
      emergency_contact: (employee as any).emergency_contact,
      emergency_phone: (employee as any).emergency_phone,
      education_level_id: (employee as any).education_level_id
    })
    setShowEditModal(true)

    // 載入員工的詳細數據
    setLoadingDetails(true)
    try {
      // 載入員工升遷紀錄
      const promotionsResponse = await getEmployeePromotions(employee.id)
      if (promotionsResponse.success && promotionsResponse.data?.promotions) {
        setEmployeePromotions(promotionsResponse.data.promotions)
      }

      // 載入員工獎懲紀錄
      const rewardsResponse = await getEmployeeRewards(employee.id)
      if (rewardsResponse.success && rewardsResponse.data?.rewards) {
        setEmployeeRewards(rewardsResponse.data.rewards)
      }

      // 從員工數據中提取技能（如果有的話）
      if ((employee as any).skills) {
        setEmployeeSkills((employee as any).skills)
      }
    } catch (error) {
      console.error('載入員工詳細數據失敗:', error)
    } finally {
      setLoadingDetails(false)
    }
  }

  // 查看員工詳細資料
  const handleViewEmployee = async (employee: Employee) => {
    setLoadingViewData(true)
    setShowViewModal(true)

    try {
      console.log('載入員工詳細資料:', employee.id)

      // 並行載入升遷和獎懲記錄
      const [promotionsRes, rewardsRes] = await Promise.all([
        getEmployeePromotions(employee.id),
        getEmployeeRewards(employee.id)
      ])

      // 使用員工基本資料中已包含的技能資料
      const employeeSkills = (employee as any).skills || []

      setViewEmployeeData({
        employee: employee,
        skills: employeeSkills, // 使用員工實際擁有的技能
        promotions: promotionsRes.success ? promotionsRes.data?.promotions || [] : [],
        rewards: rewardsRes.success ? rewardsRes.data?.rewards || [] : [],
        education_levels: []
      })

      console.log('員工詳細資料載入完成:', {
        employee: employee.name,
        skillsCount: employeeSkills.length,
        promotionsCount: promotionsRes.success ? promotionsRes.data?.promotions?.length || 0 : 0,
        rewardsCount: rewardsRes.success ? rewardsRes.data?.rewards?.length || 0 : 0
      })

    } catch (error) {
      console.error('載入員工詳細資料錯誤:', error)
      // 如果發生錯誤，至少顯示基本資料
      setViewEmployeeData({
        employee: employee,
        skills: (employee as any).skills || [],
        promotions: [],
        rewards: [],
        education_levels: []
      })
    } finally {
      setLoadingViewData(false)
    }
  }

  // 關閉查看模態框
  const handleCloseViewModal = () => {
    setShowViewModal(false)
    setViewEmployeeData(null)
  }

  // 關閉編輯模態框
  const handleCloseEditModal = () => {
    setShowEditModal(false)
    setSelectedEmployee(null)
    setEditFormData({})
  }

  // 儲存員工資料
  const handleSaveEmployee = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedEmployee) {
      console.error('沒有選中的員工')
      return
    }

    try {
      setSavingEmployee(true)
      console.log('開始儲存員工資料:', editFormData)
      console.log('選中的員工:', selectedEmployee)
      console.log('員工ID:', selectedEmployee.id)

      // 清理和驗證資料
      const cleanedData = { ...editFormData }

      // 移除空字串，轉換為 null 或適當的值
      Object.keys(cleanedData).forEach(key => {
        if (cleanedData[key] === '') {
          if (['department_id', 'role_id', 'manager_id', 'education_level_id'].includes(key)) {
            delete cleanedData[key] // 不發送空的 ID 欄位
          } else {
            cleanedData[key] = null
          }
        }
      })

      console.log('清理後的資料:', cleanedData)

      // 調用更新 API
      const response = await updateEmployee(selectedEmployee.id, cleanedData)
      console.log('API 響應:', response)

      if (response.success) {
        console.log('員工資料更新成功')

        // 重新載入員工列表以顯示最新資料
        const employeesResponse = await getEmployees()
        if (employeesResponse.success && employeesResponse.data) {
          setEmployees(employeesResponse.data)
          setFilteredEmployees(employeesResponse.data)
        }

        // 關閉編輯模態框
        handleCloseEditModal()

        // 顯示成功訊息（可以添加通知組件）
        alert('員工資料更新成功！')
      } else {
        console.error('更新員工資料失敗:', response.error)
        alert(`更新失敗：${response.error || '未知錯誤'}`)
      }
    } catch (error) {
      console.error('儲存員工資料時發生錯誤:', error)
      alert('儲存時發生錯誤，請稍後再試')
    } finally {
      setSavingEmployee(false)
    }
  }

  if (!user || user.role_id !== 999) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">權限不足</h2>
          <p className="text-gray-600 mb-4">您需要管理員權限才能訪問此頁面</p>
          <Button onClick={() => router.push('/m')}>
            返回儀表板
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 📦 主容器 - 內容區域容器 */}
      <div className="p-6">
        {/* 🎨 頁面標題 - 統一的標題設計 */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
          <div className="flex items-center justify-between">
            {/* 📍 左側標題區 */}
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2 text-white">員工資訊管理</h1>
              <div className="flex items-center space-x-2">
                {/* 🔙 返回按鈕 - 圖標+文字設計 */}
                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                  <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                  <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                </Link>
                <p className="text-indigo-100 text-base font-medium">管理員工基本資料、部門配置與權限設定</p>
              </div>
            </div>

            {/* 📍 右側資訊區 */}
            <div className="flex items-center space-x-3 text-right">
              <div>
                <p className="text-sm font-medium text-white">管理員模式</p>
                <p className="text-xs text-indigo-100">員工資料管理</p>
              </div>
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* 查詢工具欄 */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
          {/* 查詢表單 */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-4">
            {/* 員工姓名搜尋 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-blue-600" />
                <label className="block text-sm font-semibold text-gray-700">員工姓名</label>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchParams.name}
                  onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                  placeholder="搜尋員工姓名..."
                  className="w-full border border-gray-200 rounded-lg px-4 py-2 pl-11 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                />
              </div>
            </div>

            {/* 員工編號搜尋 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Hash className="w-4 h-4 text-green-600" />
                <label className="block text-sm font-semibold text-gray-700">員工編號</label>
              </div>
              <div className="relative">
                <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchParams.employee_id}
                  onChange={(e) => setSearchParams({ ...searchParams, employee_id: e.target.value })}
                  placeholder="員工編號..."
                  className="w-full border border-gray-200 rounded-lg px-4 py-2 pl-11 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-sm"
                />
              </div>
            </div>

            {/* 職位搜尋 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Briefcase className="w-4 h-4 text-purple-600" />
                <label className="block text-sm font-semibold text-gray-700">職位</label>
              </div>
              <div className="relative">
                <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchParams.position}
                  onChange={(e) => setSearchParams({ ...searchParams, position: e.target.value })}
                  placeholder="職位..."
                  className="w-full border border-gray-200 rounded-lg px-4 py-2 pl-11 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-sm"
                />
              </div>
            </div>

            {/* 部門篩選 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Building className="w-4 h-4 text-orange-600" />
                <label className="block text-sm font-semibold text-gray-700">部門</label>
              </div>
              <select
                value={searchParams.department}
                onChange={(e) => setSearchParams({ ...searchParams, department: e.target.value })}
                className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-sm"
              >
                <option value="">所有部門</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
          </div>

          {/* 操作按鈕 */}
          <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-100">
            <div className="flex items-center space-x-3">
              <button
                onClick={handleSearch}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Search className="w-4 h-4" />
                <span>查詢</span>
              </button>

              <button
                onClick={handleReset}
                className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-sm hover:shadow-md transform hover:scale-105"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重置</span>
              </button>
            </div>

            <div className="flex items-center space-x-3">
              <button className="bg-gradient-to-r from-emerald-500 to-green-600 text-white px-4 py-2 rounded-lg hover:from-emerald-600 hover:to-green-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                <Plus className="w-4 h-4" />
                <span>新增員工</span>
              </button>

              <button className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                <Upload className="w-4 h-4" />
                <span>批量匯入</span>
              </button>

              <button className="bg-gradient-to-r from-purple-500 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105">
                <Download className="w-4 h-4" />
                <span>匯出</span>
              </button>
            </div>
          </div>
        </div>

        {/* 查詢結果統計 */}
        {filteredEmployees.length !== employees.length && (
          <div className="mb-4">
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Info className="w-5 h-5 text-white" />
                </div>
                <div>
                  <span className="text-blue-800 font-semibold text-lg">
                    找到 {filteredEmployees.length} 位員工（總共 {employees.length} 位）
                  </span>
                  <p className="text-blue-600 text-sm mt-1">實時更新的查詢結果</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 工具欄 */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">員工資料列表</h2>
              <span className="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm px-4 py-2 rounded-full font-medium shadow-sm">
                {filteredEmployees.length} 位員工
              </span>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={() => window.location.reload()}
                className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-300 flex items-center space-x-2 shadow-sm hover:shadow-md transform hover:scale-105"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重新整理</span>
              </button>
            </div>
          </div>
        </div>

        {/* 員工列表 */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">

          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                  <div className="relative">
                    <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-700 mb-2">載入員工資料中</h4>
                <p className="text-gray-500 text-sm">請稍候...</p>
              </div>
            </div>
          ) : filteredEmployees.length > 0 ? (
            <>
              {/* 桌面版表格 */}
              <div className="hidden lg:block">
                {/* 表格標題 */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                  <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                    <div className="col-span-2 flex items-center space-x-2">
                      <User className="w-4 h-4 text-blue-600" />
                      <span>員工資訊</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <Building className="w-4 h-4 text-green-600" />
                      <span>部門</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <Briefcase className="w-4 h-4 text-purple-600" />
                      <span>職位</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <GraduationCap className="w-4 h-4 text-blue-600" />
                      <span>學歷</span>
                    </div>
                    <div className="col-span-2 flex items-center space-x-2">
                      <Award className="w-4 h-4 text-purple-600" />
                      <span>專業技能</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <Shield className="w-4 h-4 text-red-600" />
                      <span>角色</span>
                    </div>
                    <div className="col-span-2 flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-indigo-600" />
                      <span>聯絡方式</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-emerald-600" />
                      <span>狀態</span>
                    </div>
                    <div className="col-span-1 flex items-center space-x-2">
                      <Settings className="w-4 h-4 text-gray-600" />
                      <span>操作</span>
                    </div>
                  </div>
                </div>

                {/* 表格內容 */}
                <div className="divide-y divide-gray-100">
                  {filteredEmployees.map((employee) => (
                    <div key={employee.id} className="px-6 py-4 hover:bg-gray-50/50 transition-colors">
                      <div className="grid grid-cols-12 gap-4 items-center">
                        {/* 員工資訊 */}
                        <div className="col-span-2 flex items-center space-x-3">
                          {(employee as any).photo_url ? (
                            <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                              <img
                                src={(employee as any).photo_url}
                                alt={employee.name}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.innerHTML = `<div class="w-full h-full ${getAvatarColor(employee.name)} rounded-full flex items-center justify-center text-white font-medium">${employee.name.charAt(0)}</div>`;
                                  }
                                }}
                              />
                            </div>
                          ) : (
                            <div className={`w-10 h-10 ${getAvatarColor(employee.name)} rounded-full flex items-center justify-center text-white font-medium`}>
                              {employee.name.charAt(0)}
                            </div>
                          )}
                          <div>
                            <p className="font-medium text-gray-900">{employee.name}</p>
                            <p className="text-sm text-gray-500">{employee.employee_id}</p>
                          </div>
                        </div>

                        {/* 部門 */}
                        <div className="col-span-1">
                          <span className="text-sm text-gray-900">{employee.department_name || '未設定部門'}</span>
                        </div>

                        {/* 職位 */}
                        <div className="col-span-1">
                          <span className="text-sm text-gray-900">{employee.position}</span>
                        </div>

                        {/* 學歷 */}
                        <div className="col-span-1">
                          <span className="text-sm text-gray-900">{(employee as any).education_level_name || '未設定學歷'}</span>
                        </div>

                        {/* 專業技能 */}
                        <div className="col-span-2">
                          <div className="text-sm">
                            {generateSkillsDisplay((employee as any).skills || [])}
                          </div>
                        </div>

                        {/* 角色 */}
                        <div className="col-span-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(employee.role_id)}`}>
                            {getRoleText(employee.role_id)}
                          </span>
                        </div>

                        {/* 聯絡方式 */}
                        <div className="col-span-2">
                          <div className="text-sm">
                            {employee.email && <p className="text-gray-900">{employee.email}</p>}
                            {employee.phone && <p className="text-gray-500">{employee.phone}</p>}
                          </div>
                        </div>

                        {/* 狀態 */}
                        <div className="col-span-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(employee.status)}`}>
                            {getStatusText(employee.status)}
                          </span>
                        </div>

                        {/* 操作 */}
                        <div className="col-span-1 flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleEditEmployee(employee)}
                            className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 text-sm font-medium"
                          >
                            編輯
                          </button>
                          <button
                            onClick={() => handleViewEmployee(employee)}
                            className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 text-sm font-medium"
                          >
                            查看
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 手機版卡片 */}
              <div className="lg:hidden space-y-4 p-4">
                {filteredEmployees.map((employee) => (
                  <div key={employee.id} className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="flex items-center space-x-4 mb-4">
                      {(employee as any).photo_url ? (
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                          <img
                            src={(employee as any).photo_url}
                            alt={employee.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = `<div class="w-full h-full ${getAvatarColor(employee.name)} rounded-full flex items-center justify-center text-white font-medium">${employee.name.charAt(0)}</div>`;
                              }
                            }}
                          />
                        </div>
                      ) : (
                        <div className={`w-12 h-12 ${getAvatarColor(employee.name)} rounded-full flex items-center justify-center text-white font-medium`}>
                          {employee.name.charAt(0)}
                        </div>
                      )}
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{employee.name}</h3>
                        <p className="text-sm text-gray-500">{employee.employee_id}</p>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(employee.status)}`}>
                        {getStatusText(employee.status)}
                      </span>
                    </div>

                    <div className="space-y-3 text-sm mb-4">
                      <div className="flex items-center">
                        <Building className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">部門:</span>
                        <span className="ml-2 text-gray-900">{employee.department_name || '未設定部門'}</span>
                      </div>
                      <div className="flex items-center">
                        <Briefcase className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">職位:</span>
                        <span className="ml-2 text-gray-900">{employee.position}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">電話:</span>
                        <span className="ml-2 text-gray-900">{employee.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">郵件:</span>
                        <span className="ml-2 text-gray-900 truncate">{employee.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Shield className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-gray-500">角色:</span>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(employee.role_id)}`}>
                          {getRoleText(employee.role_id)}
                        </span>
                      </div>
                      {(employee as any).education_level_name && (
                        <div className="flex items-center">
                          <GraduationCap className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-gray-500">學歷:</span>
                          <span className="ml-2 text-gray-900">{(employee as any).education_level_name}</span>
                        </div>
                      )}
                      {(employee as any).skills && (employee as any).skills.length > 0 && (
                        <div className="flex items-start">
                          <Award className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                          <span className="text-gray-500">技能:</span>
                          <div className="ml-2 flex-1">
                            {generateSkillsDisplay((employee as any).skills)}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="text-xs text-gray-400">
                        {(employee as any).hire_date && `入職: ${(employee as any).hire_date}`}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditEmployee(employee)}
                          className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1.5 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 text-xs font-medium flex items-center space-x-1"
                        >
                          <Edit className="w-3 h-3" />
                          <span>編輯</span>
                        </button>
                        <button
                          onClick={() => handleViewEmployee(employee)}
                          className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-3 py-1.5 rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 text-xs font-medium flex items-center space-x-1"
                        >
                          <Eye className="w-3 h-3" />
                          <span>查看</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4 shadow-sm">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">查無員工資料</h3>
              <p className="text-gray-500">請調整搜尋條件或新增員工</p>
            </div>
          )}
        </div>
      </div>

      {/* 員工編輯模態框 */}
      {showEditModal && selectedEmployee && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 backdrop-blur-sm">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden border border-gray-100">
              {/* 模態框頭部 */}
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-6 text-white relative overflow-hidden">
                {/* 背景裝飾 */}
                <div className="absolute inset-0 bg-white opacity-10">
                  <div className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white opacity-20"></div>
                  <div className="absolute top-8 -left-8 w-16 h-16 rounded-full bg-white opacity-15"></div>
                </div>

                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                      <User className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">編輯員工資料</h3>
                      <p className="text-blue-100 text-sm">管理員工基本資訊與權限設定</p>
                    </div>
                  </div>

                  {/* 頂部操作按鈕 */}
                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={handleSaveEmployee}
                      className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>儲存</span>
                    </button>
                    <button
                      type="button"
                      onClick={handleCloseEditModal}
                      className="bg-white bg-opacity-10 hover:bg-opacity-20 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm"
                    >
                      <XCircle className="w-4 h-4" />
                      <span>取消</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 模態框內容 */}
              <div className="p-8 overflow-y-auto max-h-[calc(90vh-120px)] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <form onSubmit={handleSaveEmployee} className="space-y-8">
                  {/* 基本資訊區塊 */}
                  <div className="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 border border-gray-100 shadow-sm">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">基本資訊</h4>
                    </div>

                    {/* 照片預覽區域 */}
                    <div className="flex items-start space-x-6 mb-6">
                      {/* 照片顯示區域 */}
                      <div className="flex-shrink-0">
                        <div className="relative">
                          <div className="w-24 h-24 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg border-4 border-white">
                            {editFormData.photo_url ? (
                              <img
                                src={editFormData.photo_url}
                                alt="員工照片"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <span className="text-white text-2xl font-bold">
                                {editFormData.name?.charAt(0) || 'U'}
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mt-2 text-center">員工照片</p>
                      </div>

                      {/* 基本資訊表單 */}
                      <div className="flex-1 grid grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            員工姓名 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={editFormData.name || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            員工編號 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={editFormData.employee_id || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, employee_id: e.target.value })}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            電子郵件 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="email"
                            value={editFormData.email || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">聯絡電話</label>
                          <input
                            type="tel"
                            value={editFormData.phone || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, phone: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                      </div>
                    </div>

                    {/* 照片連結輸入 */}
                    <div className="grid grid-cols-1 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">照片連結</label>
                        <input
                          type="url"
                          value={editFormData.photo_url || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, photo_url: e.target.value })}
                          placeholder="https://example.com/photo.jpg"
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* 職位資訊區塊 */}
                  <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl p-6 border border-blue-100 shadow-sm">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <Briefcase className="w-5 h-5 text-blue-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">職位資訊</h4>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          部門 <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={editFormData.department_id || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, department_id: parseInt(e.target.value) })}
                          required
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        >
                          <option value="">請選擇部門</option>
                          {departmentOptions.map((dept) => (
                            <option key={dept.id} value={dept.id}>{dept.name}</option>
                          ))}
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          職位 <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={editFormData.position || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, position: e.target.value })}
                          required
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        >
                          <option value="">請選擇職位</option>
                          {positionOptions.map((position) => (
                            <option key={position.id} value={position.name}>{position.name}</option>
                          ))}
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">角色權限</label>
                        <select
                          value={editFormData.role_id || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, role_id: parseInt(e.target.value) })}
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        >
                          <option value="">請選擇角色</option>
                          {roleOptions.map((role) => (
                            <option key={role.id} value={role.id}>
                              {role.role_name} (權限等級: {role.permission_level})
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">到職日期</label>
                        <input
                          type="date"
                          value={editFormData.hire_date || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, hire_date: e.target.value })}
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* 專業技能區塊 */}
                  <div className="bg-gradient-to-br from-purple-50 to-white rounded-2xl p-6 border border-purple-100 shadow-sm">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                          <Award className="w-5 h-5 text-purple-600" />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">專業技能</h4>
                      </div>
                      <button
                        type="button"
                        className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-sm"
                      >
                        <Plus className="w-4 h-4" />
                        <span>新增技能</span>
                      </button>
                    </div>

                    {/* 技能列表 */}
                    <div className="space-y-3">
                      {loadingDetails ? (
                        <div className="text-center py-4">
                          <RefreshCw className="w-6 h-6 animate-spin mx-auto text-purple-500" />
                          <p className="text-sm text-gray-500 mt-2">載入技能資料中...</p>
                        </div>
                      ) : employeeSkills.length > 0 ? (
                        employeeSkills.map((skill, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-white rounded-xl border border-purple-100">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <Activity className="w-4 h-4 text-purple-600" />
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">{skill.name || skill.skill_name}</span>
                                {skill.level && (
                                  <span className="text-sm text-gray-500 ml-2">{skill.level}</span>
                                )}
                              </div>
                            </div>
                            <button className="text-red-500 hover:text-red-700">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Award className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未添加任何專業技能</p>
                          <p className="text-sm">點擊上方「新增技能」按鈕開始添加</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 升遷紀錄區塊 */}
                  <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl p-6 border border-blue-100 shadow-sm">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                          <Activity className="w-5 h-5 text-blue-600" />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">升遷紀錄</h4>
                      </div>
                      <button
                        type="button"
                        className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-sm"
                      >
                        <Plus className="w-4 h-4" />
                        <span>新增升遷</span>
                      </button>
                    </div>

                    {/* 升遷列表 */}
                    <div className="space-y-3">
                      {loadingDetails ? (
                        <div className="text-center py-4">
                          <RefreshCw className="w-6 h-6 animate-spin mx-auto text-blue-500" />
                          <p className="text-sm text-gray-500 mt-2">載入升遷紀錄中...</p>
                        </div>
                      ) : employeePromotions.length > 0 ? (
                        employeePromotions.map((promotion, index) => (
                          <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl border border-blue-100">
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium text-gray-900">
                                  {promotion.from_position} → {promotion.to_position}
                                </span>
                                <span className="text-sm text-gray-500">
                                  {new Date(promotion.promotion_date).toLocaleDateString('zh-TW')}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600">{promotion.reason}</p>
                              {promotion.notes && (
                                <p className="text-xs text-gray-500 mt-1">{promotion.notes}</p>
                              )}
                            </div>
                            <button className="text-red-500 hover:text-red-700 ml-4">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Activity className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未添加任何升遷紀錄</p>
                          <p className="text-sm">點擊上方「新增升遷」按鈕開始添加</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 獎懲紀錄區塊 */}
                  <div className="bg-gradient-to-br from-orange-50 to-white rounded-2xl p-6 border border-orange-100 shadow-sm">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                          <Award className="w-5 h-5 text-orange-600" />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">獎懲紀錄</h4>
                      </div>
                      <button
                        type="button"
                        className="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center space-x-2 shadow-sm"
                      >
                        <Plus className="w-4 h-4" />
                        <span>新增獎懲</span>
                      </button>
                    </div>

                    {/* 獎懲列表 */}
                    <div className="space-y-3">
                      {loadingDetails ? (
                        <div className="text-center py-4">
                          <RefreshCw className="w-6 h-6 animate-spin mx-auto text-orange-500" />
                          <p className="text-sm text-gray-500 mt-2">載入獎懲紀錄中...</p>
                        </div>
                      ) : employeeRewards.length > 0 ? (
                        employeeRewards.map((reward, index) => (
                          <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl border border-orange-100">
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <span className={`px-2 py-1 text-xs rounded-full ${reward.type_name?.includes('獎') || reward.type_name?.includes('獎勵')
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                    }`}>
                                    {reward.type_name}
                                  </span>
                                  <span className="font-medium text-gray-900">{reward.reason}</span>
                                </div>
                                <span className="text-sm text-gray-500">
                                  {new Date(reward.reward_date).toLocaleDateString('zh-TW')}
                                </span>
                              </div>
                              {reward.amount && (
                                <p className="text-sm text-gray-600">金額：NT$ {reward.amount}</p>
                              )}
                              {reward.notes && (
                                <p className="text-xs text-gray-500 mt-1">{reward.notes}</p>
                              )}
                              {reward.approver && (
                                <p className="text-xs text-gray-500">核准人：{reward.approver}</p>
                              )}
                            </div>
                            <button className="text-red-500 hover:text-red-700 ml-4">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Award className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未添加任何獎懲紀錄</p>
                          <p className="text-sm">點擊上方「新增獎懲」按鈕開始添加</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 其他資訊區塊 */}
                  <div className="bg-gradient-to-br from-green-50 to-white rounded-2xl p-6 border border-green-100 shadow-sm">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <Settings className="w-5 h-5 text-green-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">其他資訊</h4>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">員工狀態</label>
                        <select
                          value={editFormData.status || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, status: e.target.value })}
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        >
                          <option value="active">在職</option>
                          <option value="trial">試用期</option>
                          <option value="leave">離職</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">學歷等級</label>
                        <select
                          value={editFormData.education_level_id || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, education_level_id: e.target.value })}
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        >
                          <option value="">請選擇學歷</option>
                          {educationLevels.map((level) => (
                            <option key={level.id} value={level.id}>{level.name}</option>
                          ))}
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">地址</label>
                        <textarea
                          value={editFormData.address || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, address: e.target.value })}
                          rows={3}
                          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm resize-none"
                        />
                      </div>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">緊急聯絡人</label>
                          <input
                            type="text"
                            value={editFormData.emergency_contact || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, emergency_contact: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">緊急聯絡電話</label>
                          <input
                            type="tel"
                            value={editFormData.emergency_phone || ''}
                            onChange={(e) => setEditFormData({ ...editFormData, emergency_phone: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 安全設定區塊 */}
                  <div className="bg-gradient-to-br from-red-50 to-white rounded-2xl p-6 border border-red-100 shadow-sm">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                        <Shield className="w-5 h-5 text-red-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">安全設定</h4>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">密碼</label>
                      <div className="relative">
                        <input
                          type="password"
                          value={editFormData.password || ''}
                          onChange={(e) => setEditFormData({ ...editFormData, password: e.target.value })}
                          className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-4 flex items-center"
                        >
                          <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors" />
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-2 bg-gray-50 p-3 rounded-lg">
                        <Info className="w-4 h-4 inline mr-1" /> 編輯時留空表示不修改密碼
                      </p>
                    </div>
                  </div>

                  {/* 底部操作按鈕 */}
                  <div className="flex justify-end space-x-4 pt-6 border-t border-gray-100">
                    <button
                      type="button"
                      onClick={handleCloseEditModal}
                      className="px-8 py-3 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2 shadow-sm"
                    >
                      <XCircle className="w-4 h-4" />
                      <span>取消</span>
                    </button>
                    <button
                      type="submit"
                      disabled={savingEmployee}
                      className={`px-8 py-3 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg ${savingEmployee
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700'
                        }`}
                    >
                      {savingEmployee ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>儲存中...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4" />
                          <span>儲存變更</span>
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 員工詳細資料查看模態框 */}
      {showViewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 backdrop-blur-sm">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-white rounded-3xl shadow-2xl max-w-7xl w-full max-h-[95vh] overflow-hidden border border-gray-100">
              {/* 模態框頭部 */}
              <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 px-8 py-6 text-white relative overflow-hidden">
                {/* 背景裝飾 */}
                <div className="absolute inset-0 bg-white opacity-10">
                  <div className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white opacity-20"></div>
                  <div className="absolute top-8 -left-8 w-16 h-16 rounded-full bg-white opacity-15"></div>
                </div>

                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                      <Eye className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">員工詳細資料</h3>
                      <p className="text-indigo-100 text-sm">完整的員工資訊檢視</p>
                    </div>
                  </div>

                  {/* 關閉按鈕 */}
                  <button
                    type="button"
                    onClick={handleCloseViewModal}
                    className="bg-white bg-opacity-10 hover:bg-opacity-20 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 backdrop-blur-sm"
                  >
                    <XCircle className="w-4 h-4" />
                    <span>關閉</span>
                  </button>
                </div>
              </div>

              {/* 模態框內容 */}
              <div className="p-8 overflow-y-auto max-h-[calc(95vh-120px)] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {loadingViewData ? (
                  <div className="flex items-center justify-center py-16">
                    <div className="text-center">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl mb-4 shadow-lg">
                        <div className="relative">
                          <div className="w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                        </div>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-700 mb-2">載入中...</h4>
                      <p className="text-gray-500">正在載入員工詳細資料</p>
                    </div>
                  </div>
                ) : viewEmployeeData ? (
                  <div className="space-y-8">
                    {/* 員工基本資料卡片 */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 shadow-sm">
                      <div className="flex items-start space-x-8">
                        {/* 員工照片 */}
                        <div className="flex-shrink-0">
                          <div className="w-32 h-32 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg border-4 border-white">
                            {viewEmployeeData.employee?.photo_url ? (
                              <img
                                src={viewEmployeeData.employee.photo_url}
                                alt="員工照片"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <span className="text-4xl font-bold text-white">
                                {viewEmployeeData.employee?.name?.charAt(0) || '?'}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* 基本資訊 */}
                        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                                {viewEmployeeData.employee?.name || '未知姓名'}
                              </h2>
                              <div className="flex items-center space-x-3">
                                <span className="text-lg text-gray-600">
                                  {viewEmployeeData.employee?.employee_id || '未知編號'}
                                </span>
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(viewEmployeeData.employee?.status || 'unknown')}`}>
                                  {getStatusText(viewEmployeeData.employee?.status || 'unknown')}
                                </span>
                              </div>
                            </div>

                            <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <Building className="w-5 h-5 text-blue-600" />
                                <span className="text-gray-600">部門：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.department_name || '未設定部門'}
                                </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Briefcase className="w-5 h-5 text-purple-600" />
                                <span className="text-gray-600">職位：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.position || '未設定職位'}
                                </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Shield className="w-5 h-5 text-red-600" />
                                <span className="text-gray-600">角色：</span>
                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(viewEmployeeData.employee?.role_id)}`}>
                                  {getRoleText(viewEmployeeData.employee?.role_id)}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <Mail className="w-5 h-5 text-green-600" />
                                <span className="text-gray-600">郵件：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.email || '未設定郵件'}
                                </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Phone className="w-5 h-5 text-orange-600" />
                                <span className="text-gray-600">電話：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.phone || '未設定電話'}
                                </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Calendar className="w-5 h-5 text-indigo-600" />
                                <span className="text-gray-600">入職日期：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.hire_date ?
                                    new Date(viewEmployeeData.employee.hire_date).toLocaleDateString('zh-TW') :
                                    '未設定日期'
                                  }
                                </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <GraduationCap className="w-5 h-5 text-teal-600" />
                                <span className="text-gray-600">學歷：</span>
                                <span className="font-medium text-gray-900">
                                  {viewEmployeeData.employee?.education_level_name || '未設定學歷'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 專業技能區塊 */}
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100 shadow-sm">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                          <Award className="w-5 h-5 text-green-600" />
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900">專業技能</h4>
                      </div>

                      {viewEmployeeData.skills && viewEmployeeData.skills.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {viewEmployeeData.skills.map((skill: any, index: number) => (
                            <div key={index} className="bg-white rounded-xl p-4 border border-green-100 shadow-sm">
                              <div className="flex items-center justify-between mb-2">
                                <h5 className="font-medium text-gray-900">{skill.name}</h5>
                                <span className="text-sm text-green-600 font-medium">
                                  {skill.proficiency_level || '初級'}
                                </span>
                              </div>
                              <div className="text-xs text-gray-500 mb-2">
                                分類：{skill.category || '未分類'}
                              </div>
                              {skill.years_experience && (
                                <p className="text-sm text-gray-600 mb-2">
                                  經驗：{skill.years_experience} 年
                                </p>
                              )}
                              {skill.certification && (
                                <p className="text-xs text-gray-500">
                                  認證：{skill.certification}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Award className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未添加專業技能</p>
                        </div>
                      )}
                    </div>

                    {/* 升遷記錄區塊 */}
                    <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100 shadow-sm">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                          <BarChart3 className="w-5 h-5 text-purple-600" />
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900">升遷記錄</h4>
                      </div>

                      {viewEmployeeData.promotions && viewEmployeeData.promotions.length > 0 ? (
                        <div className="space-y-4">
                          {viewEmployeeData.promotions.map((promotion: any, index: number) => (
                            <div key={index} className="bg-white rounded-xl p-4 border border-purple-100 shadow-sm">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                  <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium">
                                    {promotion.type_name || '升遷'}
                                  </span>
                                  <h5 className="font-medium text-gray-900">
                                    {promotion.from_position} → {promotion.to_position}
                                  </h5>
                                </div>
                                <span className="text-sm text-gray-500">
                                  {promotion.promotion_date ?
                                    new Date(promotion.promotion_date).toLocaleDateString('zh-TW') :
                                    '未知日期'
                                  }
                                </span>
                              </div>
                              {promotion.reason && (
                                <p className="text-sm text-gray-600 mb-2">原因：{promotion.reason}</p>
                              )}
                              {promotion.notes && (
                                <p className="text-sm text-green-600">備註：{promotion.notes}</p>
                              )}
                              {promotion.approver && (
                                <p className="text-xs text-gray-500 mt-2">核准人：{promotion.approver}</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未有升遷記錄</p>
                        </div>
                      )}
                    </div>

                    {/* 獎懲記錄區塊 */}
                    <div className="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-6 border border-orange-100 shadow-sm">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                          <Award className="w-5 h-5 text-orange-600" />
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900">獎懲記錄</h4>
                      </div>

                      {viewEmployeeData.rewards && viewEmployeeData.rewards.length > 0 ? (
                        <div className="space-y-4">
                          {viewEmployeeData.rewards.map((reward: any, index: number) => (
                            <div key={index} className="bg-white rounded-xl p-4 border border-orange-100 shadow-sm">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                  <span className={`px-3 py-1 text-sm rounded-full font-medium ${reward.type_name?.includes('獎') || reward.type_name?.includes('獎勵')
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                    }`}>
                                    {reward.type_name || '獎懲'}
                                  </span>
                                  <h5 className="font-medium text-gray-900">{reward.reason}</h5>
                                </div>
                                <span className="text-sm text-gray-500">
                                  {reward.reward_date ?
                                    new Date(reward.reward_date).toLocaleDateString('zh-TW') :
                                    '未知日期'
                                  }
                                </span>
                              </div>
                              {reward.amount && (
                                <p className="text-sm text-gray-600 mb-2">金額：NT$ {reward.amount}</p>
                              )}
                              {reward.notes && (
                                <p className="text-sm text-gray-600 mb-2">{reward.notes}</p>
                              )}
                              {reward.approver && (
                                <p className="text-xs text-gray-500">核准人：{reward.approver}</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Award className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>尚未有獎懲記錄</p>
                        </div>
                      )}
                    </div>

                    {/* 其他資訊區塊 */}
                    <div className="bg-gradient-to-br from-gray-50 to-slate-50 rounded-2xl p-6 border border-gray-100 shadow-sm">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                          <Info className="w-5 h-5 text-gray-600" />
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900">其他資訊</h4>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">地址</label>
                            <div className="bg-white rounded-xl p-3 border border-gray-200">
                              <p className="text-gray-900">
                                {viewEmployeeData.employee?.address || '未設定地址'}
                              </p>
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">緊急聯絡人</label>
                            <div className="bg-white rounded-xl p-3 border border-gray-200">
                              <p className="text-gray-900">
                                {viewEmployeeData.employee?.emergency_contact || '未設定緊急聯絡人'}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">緊急聯絡電話</label>
                            <div className="bg-white rounded-xl p-3 border border-gray-200">
                              <p className="text-gray-900">
                                {viewEmployeeData.employee?.emergency_phone || '未設定緊急聯絡電話'}
                              </p>
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">薪資等級</label>
                            <div className="bg-white rounded-xl p-3 border border-gray-200">
                              <p className="text-gray-900">
                                {viewEmployeeData.employee?.salary_level || '未設定薪資等級'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-16">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl mb-4 shadow-sm">
                      <XCircle className="w-8 h-8 text-red-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">載入失敗</h3>
                    <p className="text-gray-500">無法載入員工詳細資料</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}