@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
 * Han AttendanceOS v2025.6.8 - 全局樣式
 * 遠漢科技考勤系統統一設計規範
 */


/* 字體導入 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* 全局重置和基礎樣式 */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Inter', system-ui, sans-serif;
    color: theme('colors.gray.900');
    background-color: theme('colors.gray.50');
    min-height: 100vh;
}


/* 自定義滾動條樣式 */

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 150ms ease-in-out;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}


/* 選擇文字樣式 */

::selection {
    background-color: theme('colors.primary.100');
    color: theme('colors.primary.900');
}


/* 焦點樣式 */

:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}


/* 無障礙支援 */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}


/* 高對比度模式支援 */

@media (prefers-contrast: high) {
     :root {
        --tw-prose-body: theme('colors.gray.900');
        --tw-prose-headings: theme('colors.gray.900');
    }
}


/* 暗色模式支援 */

@media (prefers-color-scheme: dark) {
    body {
        background-color: theme('colors.gray.900');
        color: theme('colors.gray.100');
    }
}


/* 工具類別 */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}


/* 毛玻璃效果 */

.glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}


/* 容器樣式 */

.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
    }
}


/* 動畫效果 */

.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: theme('boxShadow.xl');
}


/* 隱藏類別 */

.hidden {
    display: none;
}

@media (max-width: 639px) {
    .sm\:hidden {
        display: none;
    }
}

@media (max-width: 767px) {
    .md\:hidden {
        display: none;
    }
}

@media (max-width: 1023px) {
    .lg\:hidden {
        display: none;
    }
}


/* 列印樣式 */

@media print {
    .no-print {
        display: none !important;
    }
    body {
        background: white !important;
        color: black !important;
    }
    .glass {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #e5e7eb !important;
    }
}