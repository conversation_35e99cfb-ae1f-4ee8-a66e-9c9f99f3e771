'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
    ArrowLeft,
    Settings,
    Building,
    Clock,
    Bell,
    Shield,
    Database,
    Save,
    RefreshCw,
    Globe,
    Calendar,
    Users,
    Mail,
    Smartphone,
    Key,
    HardDrive,
    Download,
    Upload,
    Trash2,
    CheckCircle,
    AlertTriangle
} from 'lucide-react'

interface SystemSettings {
    // 基本設定
    companyName: string
    timezone: string
    language: string
    workStartTime: string
    workEndTime: string
    workDays: string[]

    // 通知設定
    emailNotifications: {
        lateReminder: boolean
        leaveApproval: boolean
        dailyReport: boolean
        weeklyReport: boolean
    }
    smsNotifications: {
        urgentAlerts: boolean
        approvalResults: boolean
    }

    // 權限管理
    defaultPermissions: {
        onlineClock: boolean
        leaveApplication: boolean
        overtimeApplication: boolean
        viewReports: boolean
    }

    // 系統維護
    autoBackup: boolean
    backupFrequency: string
    dataRetentionDays: number
    maintenanceMode: boolean
}

export default function SystemSettingsPage() {
    const [activeTab, setActiveTab] = useState('basic')
    const [settings, setSettings] = useState<SystemSettings>({
        companyName: '遠漢科技股份有限公司',
        timezone: 'Asia/Taipei',
        language: 'zh-TW',
        workStartTime: '09:00',
        workEndTime: '18:00',
        workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],

        emailNotifications: {
            lateReminder: true,
            leaveApproval: true,
            dailyReport: false,
            weeklyReport: true
        },
        smsNotifications: {
            urgentAlerts: true,
            approvalResults: false
        },

        defaultPermissions: {
            onlineClock: true,
            leaveApplication: true,
            overtimeApplication: true,
            viewReports: false
        },

        autoBackup: true,
        backupFrequency: 'daily',
        dataRetentionDays: 365,
        maintenanceMode: false
    })

    const [saving, setSaving] = useState(false)
    const [saveMessage, setSaveMessage] = useState('')

    const tabs = [
        { id: 'basic', name: '基本設定', icon: Building },
        { id: 'notifications', name: '通知設定', icon: Bell },
        { id: 'permissions', name: '權限管理', icon: Shield },
        { id: 'maintenance', name: '系統維護', icon: Database }
    ]

    const handleSave = async () => {
        setSaving(true)
        try {
            // 這裡會調用 API 保存設定
            // TODO: 實作API調用來保存設定
            await new Promise(resolve => setTimeout(resolve, 1000))
            setSaveMessage('設定已成功保存！')
            setTimeout(() => setSaveMessage(''), 3000)
        } catch (error) {
            setSaveMessage('保存失敗，請重試')
            setTimeout(() => setSaveMessage(''), 3000)
        } finally {
            setSaving(false)
        }
    }

    const updateSettings = (path: string, value: any) => {
        setSettings(prev => {
            const keys = path.split('.')
            const newSettings = { ...prev }
            let current: any = newSettings

            for (let i = 0; i < keys.length - 1; i++) {
                current[keys[i]] = { ...current[keys[i]] }
                current = current[keys[i]]
            }

            current[keys[keys.length - 1]] = value
            return newSettings
        })
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
            <div className="pt-6">
                {/* 頁面標題 */}
                <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-6 text-white shadow-2xl mb-6">
                    <div className="flex items-center justify-between">
                        <div className="relative z-10">
                            <h1 className="text-3xl font-bold mb-2 text-white">系統設定</h1>
                            <div className="flex items-center space-x-2">
                                {/* 返回按鈕 - 圖標+文字設計 */}
                                <Link href="/admin" className="inline-flex items-center space-x-1 px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-all duration-200 group border border-white/30">
                                    <ArrowLeft className="w-4 h-4 text-white group-hover:text-indigo-100" />
                                    <span className="text-sm font-medium text-white group-hover:text-indigo-100">返回</span>
                                </Link>
                                <p className="text-indigo-100 text-base font-medium">配置系統參數、管理權限設定和維護選項</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3 text-right">
                            <div>
                                <p className="text-sm font-medium text-white">管理員模式</p>
                                <p className="text-xs text-indigo-100">系統設定管理</p>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <Settings className="w-6 h-6 text-white" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* 保存提示 */}
                {saveMessage && (
                    <div className={`mb-6 p-4 rounded-xl flex items-center space-x-3 ${saveMessage.includes('成功')
                        ? 'bg-green-50 border border-green-200 text-green-800'
                        : 'bg-red-50 border border-red-200 text-red-800'
                        }`}>
                        {saveMessage.includes('成功') ? (
                            <CheckCircle className="w-5 h-5" />
                        ) : (
                            <AlertTriangle className="w-5 h-5" />
                        )}
                        <span className="font-medium">{saveMessage}</span>
                    </div>
                )}

                {/* 設定導航 */}
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 mb-6">
                    <div className="flex overflow-x-auto">
                        {tabs.map((tab) => {
                            const Icon = tab.icon
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 transition-all whitespace-nowrap ${activeTab === tab.id
                                        ? 'border-indigo-500 text-indigo-600 bg-indigo-50'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                                        }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    <span>{tab.name}</span>
                                </button>
                            )
                        })}
                    </div>
                </div>

                {/* 設定內容 */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div className="lg:col-span-3">
                        {/* 基本設定 */}
                        {activeTab === 'basic' && (
                            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
                                    <Building className="w-5 h-5 text-indigo-600" />
                                    <span>基本設定</span>
                                </h3>

                                <div className="space-y-6">
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-2">公司名稱</label>
                                        <input
                                            type="text"
                                            value={settings.companyName}
                                            onChange={(e) => updateSettings('companyName', e.target.value)}
                                            className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center space-x-2">
                                                <Globe className="w-4 h-4 text-indigo-600" />
                                                <span>時區</span>
                                            </label>
                                            <select
                                                value={settings.timezone}
                                                onChange={(e) => updateSettings('timezone', e.target.value)}
                                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                            >
                                                <option value="Asia/Taipei">Asia/Taipei (UTC+8)</option>
                                                <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                                                <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
                                                <option value="Asia/Seoul">Asia/Seoul (UTC+9)</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2">語言</label>
                                            <select
                                                value={settings.language}
                                                onChange={(e) => updateSettings('language', e.target.value)}
                                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                            >
                                                <option value="zh-TW">繁體中文</option>
                                                <option value="zh-CN">簡體中文</option>
                                                <option value="en">English</option>
                                                <option value="ja">日本語</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center space-x-2">
                                                <Clock className="w-4 h-4 text-indigo-600" />
                                                <span>上班時間</span>
                                            </label>
                                            <input
                                                type="time"
                                                value={settings.workStartTime}
                                                onChange={(e) => updateSettings('workStartTime', e.target.value)}
                                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-semibold text-gray-700 mb-2">下班時間</label>
                                            <input
                                                type="time"
                                                value={settings.workEndTime}
                                                onChange={(e) => updateSettings('workEndTime', e.target.value)}
                                                className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                                            <Calendar className="w-4 h-4 text-indigo-600" />
                                            <span>工作日設定</span>
                                        </label>
                                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                                            {[
                                                { id: 'monday', name: '週一' },
                                                { id: 'tuesday', name: '週二' },
                                                { id: 'wednesday', name: '週三' },
                                                { id: 'thursday', name: '週四' },
                                                { id: 'friday', name: '週五' },
                                                { id: 'saturday', name: '週六' },
                                                { id: 'sunday', name: '週日' }
                                            ].map((day) => (
                                                <label key={day.id} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer">
                                                    <input
                                                        type="checkbox"
                                                        checked={settings.workDays.includes(day.id)}
                                                        onChange={(e) => {
                                                            const newWorkDays = e.target.checked
                                                                ? [...settings.workDays, day.id]
                                                                : settings.workDays.filter(d => d !== day.id)
                                                            updateSettings('workDays', newWorkDays)
                                                        }}
                                                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                    />
                                                    <span className="text-sm font-medium text-gray-700">{day.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 通知設定 */}
                        {activeTab === 'notifications' && (
                            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
                                    <Bell className="w-5 h-5 text-indigo-600" />
                                    <span>通知設定</span>
                                </h3>

                                <div className="space-y-8">
                                    {/* 電子郵件通知 */}
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                            <Mail className="w-4 h-4 text-blue-600" />
                                            <span>電子郵件通知</span>
                                        </h4>
                                        <div className="space-y-3">
                                            {[
                                                { key: 'lateReminder', name: '遲到提醒', desc: '員工遲到時發送郵件通知' },
                                                { key: 'leaveApproval', name: '請假審批', desc: '有新的請假申請時通知主管' },
                                                { key: 'dailyReport', name: '每日報表', desc: '每日考勤統計報表' },
                                                { key: 'weeklyReport', name: '週報表', desc: '每週考勤統計報表' }
                                            ].map((item) => (
                                                <div key={item.key} className="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
                                                    <div>
                                                        <p className="font-medium text-gray-900">{item.name}</p>
                                                        <p className="text-sm text-gray-600">{item.desc}</p>
                                                    </div>
                                                    <label className="relative inline-flex items-center cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            checked={settings.emailNotifications[item.key as keyof typeof settings.emailNotifications]}
                                                            onChange={(e) => updateSettings(`emailNotifications.${item.key}`, e.target.checked)}
                                                            className="sr-only peer"
                                                        />
                                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    {/* 簡訊通知 */}
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                            <Smartphone className="w-4 h-4 text-green-600" />
                                            <span>簡訊通知</span>
                                        </h4>
                                        <div className="space-y-3">
                                            {[
                                                { key: 'urgentAlerts', name: '緊急警報', desc: '重要事件的即時簡訊通知' },
                                                { key: 'approvalResults', name: '審批結果', desc: '請假或加班審批結果通知' }
                                            ].map((item) => (
                                                <div key={item.key} className="flex items-center justify-between p-4 bg-green-50 rounded-xl border border-green-200">
                                                    <div>
                                                        <p className="font-medium text-gray-900">{item.name}</p>
                                                        <p className="text-sm text-gray-600">{item.desc}</p>
                                                    </div>
                                                    <label className="relative inline-flex items-center cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            checked={settings.smsNotifications[item.key as keyof typeof settings.smsNotifications]}
                                                            onChange={(e) => updateSettings(`smsNotifications.${item.key}`, e.target.checked)}
                                                            className="sr-only peer"
                                                        />
                                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 權限管理 */}
                        {activeTab === 'permissions' && (
                            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
                                    <Shield className="w-5 h-5 text-indigo-600" />
                                    <span>權限管理</span>
                                </h3>

                                <div className="space-y-6">
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                            <Key className="w-4 h-4 text-orange-600" />
                                            <span>預設員工權限</span>
                                        </h4>
                                        <div className="space-y-3">
                                            {[
                                                { key: 'onlineClock', name: '線上打卡', desc: '允許員工使用線上打卡功能' },
                                                { key: 'leaveApplication', name: '請假申請', desc: '允許員工提交請假申請' },
                                                { key: 'overtimeApplication', name: '加班申請', desc: '允許員工提交加班申請' },
                                                { key: 'viewReports', name: '查看報表', desc: '允許員工查看個人考勤報表' }
                                            ].map((item) => (
                                                <div key={item.key} className="flex items-center justify-between p-4 bg-orange-50 rounded-xl border border-orange-200">
                                                    <div>
                                                        <p className="font-medium text-gray-900">{item.name}</p>
                                                        <p className="text-sm text-gray-600">{item.desc}</p>
                                                    </div>
                                                    <label className="relative inline-flex items-center cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            checked={settings.defaultPermissions[item.key as keyof typeof settings.defaultPermissions]}
                                                            onChange={(e) => updateSettings(`defaultPermissions.${item.key}`, e.target.checked)}
                                                            className="sr-only peer"
                                                        />
                                                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                                                    </label>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 系統維護 */}
                        {activeTab === 'maintenance' && (
                            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
                                    <Database className="w-5 h-5 text-indigo-600" />
                                    <span>系統維護</span>
                                </h3>

                                <div className="space-y-8">
                                    {/* 備份設定 */}
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                            <HardDrive className="w-4 h-4 text-blue-600" />
                                            <span>備份設定</span>
                                        </h4>
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
                                                <div>
                                                    <p className="font-medium text-gray-900">自動備份</p>
                                                    <p className="text-sm text-gray-600">定期自動備份系統資料</p>
                                                </div>
                                                <label className="relative inline-flex items-center cursor-pointer">
                                                    <input
                                                        type="checkbox"
                                                        checked={settings.autoBackup}
                                                        onChange={(e) => updateSettings('autoBackup', e.target.checked)}
                                                        className="sr-only peer"
                                                    />
                                                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                                </label>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label className="block text-sm font-semibold text-gray-700 mb-2">備份頻率</label>
                                                    <select
                                                        value={settings.backupFrequency}
                                                        onChange={(e) => updateSettings('backupFrequency', e.target.value)}
                                                        className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                    >
                                                        <option value="daily">每日</option>
                                                        <option value="weekly">每週</option>
                                                        <option value="monthly">每月</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-semibold text-gray-700 mb-2">資料保留天數</label>
                                                    <input
                                                        type="number"
                                                        value={settings.dataRetentionDays}
                                                        onChange={(e) => updateSettings('dataRetentionDays', parseInt(e.target.value))}
                                                        className="w-full border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                        min="30"
                                                        max="3650"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 系統操作 */}
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-4">系統操作</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <button className="flex items-center justify-center space-x-2 p-4 bg-green-50 hover:bg-green-100 border border-green-200 rounded-xl transition-colors">
                                                <Download className="w-5 h-5 text-green-600" />
                                                <span className="font-medium text-green-800">匯出資料</span>
                                            </button>
                                            <button className="flex items-center justify-center space-x-2 p-4 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-xl transition-colors">
                                                <Upload className="w-5 h-5 text-blue-600" />
                                                <span className="font-medium text-blue-800">匯入資料</span>
                                            </button>
                                            <button className="flex items-center justify-center space-x-2 p-4 bg-red-50 hover:bg-red-100 border border-red-200 rounded-xl transition-colors">
                                                <Trash2 className="w-5 h-5 text-red-600" />
                                                <span className="font-medium text-red-800">清理快取</span>
                                            </button>
                                        </div>
                                    </div>

                                    {/* 維護模式 */}
                                    <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                                        <div>
                                            <p className="font-medium text-gray-900 flex items-center space-x-2">
                                                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                                <span>維護模式</span>
                                            </p>
                                            <p className="text-sm text-gray-600">啟用後系統將暫停服務進行維護</p>
                                        </div>
                                        <label className="relative inline-flex items-center cursor-pointer">
                                            <input
                                                type="checkbox"
                                                checked={settings.maintenanceMode}
                                                onChange={(e) => updateSettings('maintenanceMode', e.target.checked)}
                                                className="sr-only peer"
                                            />
                                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* 側邊欄 - 快速操作 */}
                    <div className="space-y-6">
                        {/* 保存按鈕 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h4 className="font-semibold text-gray-900 mb-4">操作</h4>
                            <div className="space-y-3">
                                <button
                                    onClick={handleSave}
                                    disabled={saving}
                                    className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-semibold py-3 px-4 rounded-xl transition-all transform hover:scale-105 active:scale-95 disabled:transform-none disabled:cursor-not-allowed"
                                >
                                    {saving ? (
                                        <RefreshCw className="w-4 h-4 animate-spin" />
                                    ) : (
                                        <Save className="w-4 h-4" />
                                    )}
                                    <span>{saving ? '保存中...' : '保存設定'}</span>
                                </button>
                                <button className="w-full flex items-center justify-center space-x-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors">
                                    <RefreshCw className="w-4 h-4" />
                                    <span>重置設定</span>
                                </button>
                            </div>
                        </div>

                        {/* 系統資訊 */}
                        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20">
                            <h4 className="font-semibold text-gray-900 mb-4">系統資訊</h4>
                            <div className="space-y-3 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">版本</span>
                                    <span className="font-medium">v2.1.0</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">最後更新</span>
                                    <span className="font-medium">2025-06-11</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">資料庫</span>
                                    <span className="font-medium text-green-600">正常</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">儲存空間</span>
                                    <span className="font-medium">2.3GB / 10GB</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 