#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
創建測試申請記錄
用於展示員工端申請記錄功能
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_connection():
    """
    創建資料庫連接
    
    Returns:
        sqlite3.Connection: 資料庫連接對象
    """
    return sqlite3.connect('attendance.db')

def create_test_overtime_requests():
    """
    創建測試加班申請記錄
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    # 測試加班申請資料
    test_requests = [
        {
            'employee_id': 1,
            'overtime_date': '2025-06-05',
            'start_time': '18:00',
            'end_time': '20:00',
            'overtime_type': 'weekday',
            'reason': '專案趕工需要',
            'status': 'approved',
            'hours': 2.0,
            'created_at': '2025-06-05 15:30:00'
        },
        {
            'employee_id': 1,
            'overtime_date': '2025-06-04',
            'start_time': '19:00',
            'end_time': '22:00',
            'overtime_type': 'weekday',
            'reason': '客戶緊急需求',
            'status': 'pending',
            'hours': 3.0,
            'created_at': '2025-06-04 16:45:00'
        },
        {
            'employee_id': 1,
            'overtime_date': '2025-06-03',
            'start_time': '17:30',
            'end_time': '19:30',
            'overtime_type': 'weekday',
            'reason': '系統維護',
            'status': 'rejected',
            'hours': 2.0,
            'comment': '該日期已有其他安排',
            'created_at': '2025-06-03 14:20:00'
        }
    ]
    
    for request in test_requests:
        cursor.execute('''
            INSERT INTO overtime_requests 
            (employee_id, overtime_date, start_time, end_time, overtime_type, reason, status, overtime_hours, reject_reason, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            request['employee_id'],
            request['overtime_date'],
            request['start_time'],
            request['end_time'],
            request['overtime_type'],
            request['reason'],
            request['status'],
            request['hours'],
            request.get('comment', ''),
            request['created_at']
        ))
    
    conn.commit()
    conn.close()
    print("✅ 已創建測試加班申請記錄")

def create_test_leave_requests():
    """
    創建測試請假申請記錄
    """
    conn = create_connection()
    cursor = conn.cursor()
    
    # 測試請假申請資料
    test_requests = [
        {
            'employee_id': 1,
            'leave_type': 'annual',
            'start_date': '2025-06-10',
            'end_date': '2025-06-12',
            'reason': '家庭旅遊',
            'status': 'approved',
            'created_at': '2025-06-02 10:15:00'
        },
        {
            'employee_id': 1,
            'leave_type': 'sick',
            'start_date': '2025-06-06',
            'end_date': '2025-06-06',
            'reason': '身體不適需要休息',
            'status': 'pending',
            'created_at': '2025-06-06 08:30:00'
        },
        {
            'employee_id': 1,
            'leave_type': 'personal',
            'start_date': '2025-06-01',
            'end_date': '2025-06-01',
            'reason': '處理個人事務',
            'status': 'rejected',
            'comment': '該日期人力不足，無法批准',
            'created_at': '2025-05-30 14:45:00'
        }
    ]
    
    for request in test_requests:
        cursor.execute('''
            INSERT INTO leaves 
            (employee_id, leave_type, start_date, end_date, reason, status, comment, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            request['employee_id'],
            request['leave_type'],
            request['start_date'],
            request['end_date'],
            request['reason'],
            request['status'],
            request.get('comment', ''),
            request['created_at']
        ))
    
    conn.commit()
    conn.close()
    print("✅ 已創建測試請假申請記錄")

def main():
    """
    主函數
    """
    print("🚀 開始創建測試申請記錄...")
    
    try:
        create_test_overtime_requests()
        create_test_leave_requests()
        print("\n✅ 測試申請記錄創建完成！")
        print("📋 包含內容：")
        print("   - 3筆加班申請（已通過、待審核、已拒絕）")
        print("   - 3筆請假申請（已通過、待審核、已拒絕）")
        print("\n🌐 現在可以訪問 http://localhost:7072/user 查看申請記錄")
        
    except Exception as e:
        print(f"❌ 創建測試資料失敗: {e}")

if __name__ == "__main__":
    main() 