#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
請假記錄整合修正測試腳本

功能：
1. 測試修正後的請假記錄整合功能
2. 驗證leave_hours欄位是否正確更新
3. 檢查考勤狀態是否正確設定
"""

import requests
import sqlite3
from datetime import datetime, timedelta
from database import create_connection

# API基礎URL
BASE_URL = "http://127.0.0.1:7072"

def check_leave_integration():
    """
    檢查請假記錄整合情況
    
    返回：
    - 檢查結果
    """
    print("🔍 檢查請假記錄整合情況")
    print("-" * 50)
    
    conn = create_connection()
    cursor = conn.cursor()
    
    # 查詢有請假記錄的考勤資料
    cursor.execute("""
        SELECT 
            ar.id, ar.employee_id, ar.work_date, ar.status, 
            ar.leave_hours, ar.note,
            lr.leave_type, lr.hours as leave_request_hours, lr.reason,
            e.name as employee_name, e.employee_id as employee_code
        FROM attendance_records ar
        LEFT JOIN leave_requests lr ON ar.employee_id = lr.employee_id 
            AND ar.work_date = lr.start_date
        LEFT JOIN employees e ON ar.employee_id = e.id
        WHERE ar.work_date BETWEEN '2025-06-01' AND '2025-06-05'
            AND lr.id IS NOT NULL
        ORDER BY ar.work_date, ar.employee_id
        LIMIT 20
    """)
    
    records = cursor.fetchall()
    conn.close()
    
    print(f"✅ 找到 {len(records)} 筆有請假記錄的考勤資料")
    
    # 統計分析
    leave_hours_correct = 0
    leave_hours_incorrect = 0
    status_correct = 0
    status_incorrect = 0
    
    for record in records:
        ar_id, emp_id, work_date, status, leave_hours, note, leave_type, request_hours, reason, emp_name, emp_code = record
        
        print(f"\n📋 考勤記錄 {ar_id} - {emp_name} ({emp_code}) - {work_date}")
        print(f"   考勤狀態: {status}")
        print(f"   考勤請假時數: {leave_hours}")
        print(f"   請假申請時數: {request_hours}")
        print(f"   請假類型: {leave_type}")
        print(f"   備註: {note}")
        
        # 檢查leave_hours是否正確
        if leave_hours == request_hours:
            print(f"   ✅ 請假時數正確")
            leave_hours_correct += 1
        else:
            print(f"   ❌ 請假時數不正確 (應為 {request_hours})")
            leave_hours_incorrect += 1
        
        # 檢查狀態是否正確
        expected_status = None
        if request_hours >= 8:
            expected_status = 'leave_full_day'
        elif request_hours > 0:
            expected_status = 'leave_partial_absent'  # 假設沒有打卡
        
        if status == expected_status:
            print(f"   ✅ 考勤狀態正確")
            status_correct += 1
        else:
            print(f"   ❌ 考勤狀態不正確 (應為 {expected_status})")
            status_incorrect += 1
    
    print(f"\n📊 整合檢查結果：")
    print(f"   請假時數正確: {leave_hours_correct}")
    print(f"   請假時數錯誤: {leave_hours_incorrect}")
    print(f"   考勤狀態正確: {status_correct}")
    print(f"   考勤狀態錯誤: {status_incorrect}")
    
    return {
        'total_records': len(records),
        'leave_hours_correct': leave_hours_correct,
        'leave_hours_incorrect': leave_hours_incorrect,
        'status_correct': status_correct,
        'status_incorrect': status_incorrect
    }

def test_incremental_process():
    """
    測試增量考勤整理功能
    
    返回：
    - 測試結果
    """
    print("\n⚡ 測試增量考勤整理功能")
    print("-" * 50)
    
    try:
        # 執行增量考勤整理
        response = requests.post(f"{BASE_URL}/api/attendance/management/incremental-process", 
                               json={
                                   "force_start_date": "2025-06-01",
                                   "end_date": "2025-06-05"
                               })
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 增量考勤整理成功")
            print(f"   處理天數: {result['processing_summary']['total_days']}")
            print(f"   成功天數: {result['processing_summary']['successful_days']}")
            print(f"   總新增記錄: {result['processing_summary']['total_generated']}")
            print(f"   總更新記錄: {result['processing_summary']['total_updated']}")
            return True
        else:
            print(f"❌ 增量考勤整理失敗: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 增量考勤整理錯誤: {str(e)}")
        return False

def main():
    """
    主函數
    """
    print("🚀 請假記錄整合修正測試")
    print("=" * 60)
    
    # 1. 檢查修正前的狀態
    print("\n1️⃣ 檢查修正前的請假記錄整合狀態")
    before_result = check_leave_integration()
    
    # 2. 執行增量考勤整理
    print("\n2️⃣ 執行增量考勤整理（應用修正）")
    process_success = test_incremental_process()
    
    if not process_success:
        print("❌ 增量考勤整理失敗，無法繼續測試")
        return
    
    # 3. 檢查修正後的狀態
    print("\n3️⃣ 檢查修正後的請假記錄整合狀態")
    after_result = check_leave_integration()
    
    # 4. 比較結果
    print("\n4️⃣ 修正效果比較")
    print("-" * 50)
    print(f"修正前 - 請假時數正確: {before_result['leave_hours_correct']}/{before_result['total_records']}")
    print(f"修正後 - 請假時數正確: {after_result['leave_hours_correct']}/{after_result['total_records']}")
    print(f"修正前 - 考勤狀態正確: {before_result['status_correct']}/{before_result['total_records']}")
    print(f"修正後 - 考勤狀態正確: {after_result['status_correct']}/{after_result['total_records']}")
    
    # 5. 測試結論
    print("\n5️⃣ 測試結論")
    print("=" * 60)
    
    if (after_result['leave_hours_correct'] > before_result['leave_hours_correct'] or
        after_result['status_correct'] > before_result['status_correct']):
        print("🎉 修正成功！請假記錄整合功能已改善")
    elif (after_result['leave_hours_correct'] == after_result['total_records'] and
          after_result['status_correct'] == after_result['total_records']):
        print("✅ 完美！請假記錄整合功能運作正常")
    else:
        print("⚠️ 仍有問題需要進一步修正")
    
    print(f"\n📊 最終統計：")
    print(f"   總考勤記錄: {after_result['total_records']}")
    print(f"   請假時數正確率: {after_result['leave_hours_correct']}/{after_result['total_records']} ({after_result['leave_hours_correct']/after_result['total_records']*100:.1f}%)")
    print(f"   考勤狀態正確率: {after_result['status_correct']}/{after_result['total_records']} ({after_result['status_correct']/after_result['total_records']*100:.1f}%)")

if __name__ == "__main__":
    main() 