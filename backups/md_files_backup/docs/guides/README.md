# AttendanceOS Elite - 企業級智慧考勤管理系統

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.1.0-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![System Status](https://img.shields.io/badge/System-Production%20Ready-brightgreen.svg)](ATTENDANCE_SYSTEM_FIX_REPORT.md)

## 🚨 最新更新 (2025-06-02)

### 🎉 考勤系統全面升級完成
- **新增功能**: 
  - ✅ **打卡紀錄**: 完整的打卡記錄查詢、篩選、匯出系統
  - ✅ **考勤作業**: 日常考勤摘要，包含遲到、早退、加班計算
  - ✅ **考勤整理**: 自動化考勤資料處理和狀態更新
- **UI升級**: 
  - 側邊欄滾動功能
  - 「考勤管理」重命名為「線上打卡」
  - 新增三個專業級功能頁面
- **API擴展**: 新增8個API端點，支援完整考勤生態系統
- **測試結果**: 100% 通過 (10項完整功能測試)
- **系統狀態**: 🟢 生產就緒

詳細完成報告請參閱: [項目完成報告](PROJECT_COMPLETE_REPORT.md)

---

## 🚨 最新更新 (2025-05-29)

### ✅ 考勤系統修復完成
- **問題**: 考勤頁面"載入考勤資料錯誤"已完全解決
- **修復內容**: 
  - 修正前端JavaScript與API數據格式不匹配問題
  - 新增動態員工選擇功能
  - 改善錯誤處理和用戶體驗
- **測試結果**: 100% 通過 (完整打卡流程測試)
- **系統狀態**: 🟢 生產就緒

詳細修復報告請參閱: [考勤系統修復報告](ATTENDANCE_SYSTEM_FIX_REPORT.md)

---

## 🌟 系統概述

AttendanceOS Elite 是一個採用國際級設計標準的企業考勤管理系統，融合了 Apple、Google、Microsoft 等頂級公司的設計語言，提供流暢的用戶體驗和強大的管理功能。

## 📋 目錄
- [專案簡介](#專案簡介)
- [功能特點](#功能特點)
- [技術架構](#技術架構)
- [快速開始](#快速開始)
- [安裝說明](#安裝說明)
- [使用指南](#使用指南)
- [API 文檔](#api-文檔)
- [開發指南](#開發指南)
- [部署](#部署)
- [常見問題](#常見問題)
- [貢獻指南](#貢獻指南)
- [授權](#授權)

## 📖 專案簡介

智慧考勤系統是一個現代化的企業考勤管理解決方案，採用 Flask 框架開發，結合 SQLite 資料庫，提供直觀的使用者介面和強大的後端功能。系統設計注重使用者體驗、安全性和可擴展性。

### 🎯 專案目標
- 簡化企業考勤管理流程
- 提供準確的出勤統計與分析
- 支援多種打卡方式和排班模式
- 實現自動化的請假審批流程

## ✨ 核心特色

### 🎨 設計理念
- **國際級設計標準**: 參考 Apple、Google、Microsoft、Linear 等頂級公司設計語言
- **玻璃擬態效果**: 現代化的視覺效果和漸層背景
- **響應式設計**: 完美適配桌面、平板、手機
- **無障礙支援**: 符合 WCAG 2.1 AA 標準

### 🛠 技術棧
- **後端**: Flask + SQLite
- **前端**: Tailwind CSS 3.x + Lucide Icons + Chart.js 4.x
- **字體**: Inter 字體系統
- **色彩**: 基於品牌色 #7c6df2 的完整色彩體系

## 📱 版本體系

### 🏆 Elite版 (推薦) - `/elite`
國際級企業設計標準，提供最佳用戶體驗
- 玻璃擬態效果和流暢動畫
- 專業級數據視覺化
- 完整的功能模組

### 📱 移動端版 - `/mobile`
原生App體驗，PWA支援
- 觸摸優化界面
- 安全區域適配
- 手勢支援

### 🏥 專業版 - `/professional`
醫療級儀表板設計風格
- 高對比度設計
- 專業數據展示

### 🎯 現代版 - `/modern`
Tailwind現代化設計
- 簡潔明快
- 功能完整

## 🚀 功能模組

### 1. 📊 智慧儀表板
- **實時統計**: 出勤率、遲到率、部門統計
- **動態圖表**: Chart.js 驅動的專業圖表
- **快速操作**: 一鍵訪問常用功能
- **數據刷新**: 自動更新和手動刷新

### 2. ⏰ 考勤打卡系統
- **一鍵打卡**: 流暢的打卡體驗
- **實時時鐘**: 動態時間顯示
- **狀態管理**: 智慧判斷上班/下班狀態
- **成功動畫**: 視覺反饋和確認
- **今日記錄**: 即時顯示打卡記錄

### 2.1. 📋 打卡紀錄系統 (新增)
- **完整查詢**: 支援員工、部門、日期範圍多維度篩選
- **快速篩選**: 今天、昨天、最近7天、最近30天快速選擇
- **統計儀表板**: 總記錄數、員工數、天數、平均每日記錄數
- **詳細檢視**: 打卡記錄詳情模態框，包含照片和位置資料
- **Excel匯出**: 格式化的Excel檔案匯出，支援中文狀態對照
- **分頁顯示**: 高效的大量資料展示

### 2.2. 📊 考勤作業系統 (新增)
- **日常摘要**: 每人每日考勤摘要（上下班時間、加班、遲到早退）
- **班別整合**: 基於員工指派的班別排程進行考勤計算
- **狀態統計**: 正常、遲到、早退、加班、請假、缺勤數量統計
- **智能計算**: 自動計算遲到早退分鐘數、加班時數
- **請假整合**: 顯示請假類型和時數
- **記錄生成**: 批量生成/重新計算考勤記錄功能
- **詳細檢視**: 完整考勤資訊模態框，包含打卡記錄

### 2.3. ⚙️ 考勤整理系統 (新增)
- **狀態總覽**: 待處理天數、員工數、預估時間、異常記錄數
- **彈性設定**: 日期範圍、員工範圍（全部/部門/特定）選擇
- **處理選項**: 遲到/早退/加班計算、請假整合、覆蓋選項
- **即時進度**: 總進度條和四步驟指示器（資料載入→時間計算→資料整合→儲存結果）
- **處理記錄**: 即時日誌、分類標記、時間戳記
- **智能特性**: 自動模式、統計更新、錯誤預測、時間估算
- **預覽功能**: 處理範圍預覽、處理項目預覽、注意事項提醒

### 3. 📅 智慧排班系統
- **日曆視圖**: 直觀的排班管理
- **拖拽操作**: 便捷的班次調整
- **自動排班**: 智慧排班算法
- **衝突檢測**: 自動檢測排班衝突
- **統計分析**: 排班數據統計

### 4. 🏖 請假管理系統
- **線上申請**: 完整的請假申請流程
- **智慧代理人選擇**: 優先同部門員工，支援跨部門選擇
- **主管權限審核**: 僅限具備主管權限的員工可審核
- **審批流程**: 多級審批支援
- **狀態追蹤**: 實時追蹤審批狀態
- **歷史記錄**: 完整的請假歷史
- **統計報表**: 請假數據分析
- **日期驗證**: 防止申請過去日期的請假
- **多種請假類型**: 年假、病假、事假、婚假、產假等

#### 🔗 員工關係管理
- **主管關係**: 建立完整的組織架構和主管關係
- **代理人邏輯**: 智慧推薦同部門或有權限的員工作為代理人
- **審核權限**: 確保只有具備主管權限的員工才能審核請假
- **工作流程**: 申請 → 選擇代理人 → 主管審核 → 狀態更新

### 5. 👥 員工管理系統
- **員工檔案**: 完整的員工資訊管理
- **智慧搜尋**: 支援姓名、員工編號、職位的模糊搜尋
- **部門篩選**: 按部門快速篩選員工
- **組合查詢**: 支援多條件組合查詢
- **即時統計**: 查詢結果統計和部門統計
- **批量操作**: 支援批量匯入/匯出
- **權限管理**: 角色和權限配置
- **分頁顯示**: 高效的數據展示

#### 🎨 界面設計增強 (v2.2.0)
- **國際級設計**: 採用漸層背景、圓角設計、陰影效果，提升視覺質感
- **頂部操作按鈕**: 在模態框頂部添加儲存/取消按鈕，無需滾動到底部
- **分區塊設計**: 將表單分為基本資訊、職位資訊、其他資訊、安全設定四個區塊
- **背景模糊效果**: 模態框背景添加模糊效果，提升專業感
- **顏色主題分區**: 不同區塊使用不同的顏色主題（灰、藍、綠、紫）
- **輸入框增強**: 更大的輸入框、圓角設計、焦點效果
- **按鈕設計**: 漸層按鈕、圖標配合、懸停效果
- **響應式佈局**: 支援不同螢幕尺寸的最佳顯示

#### 🔍 查詢功能特色
- **姓名搜尋**: 支援中文姓名模糊搜尋
- **員工編號**: 精確或模糊搜尋員工編號
- **職位篩選**: 按職位關鍵字搜尋
- **部門篩選**: 快速篩選特定部門員工
- **組合查詢**: 多條件同時搜尋
- **即時反饋**: 查詢結果即時顯示統計信息

#### 🔐 角色權限管理
- **三級權限體系**:
  - **系統管理員** (權限等級 99): 最高權限，可管理所有功能
  - **部門主管** (權限等級 50): 部門管理權限，可審核請假、管理下屬
  - **一般員工** (權限等級 15): 基本權限，考勤打卡、請假申請
- **主管關係建立**: 支援設定員工的直屬主管，建立完整組織架構
- **權限繼承**: 主管可管理下屬員工的考勤和請假
- **角色動態配置**: 可隨時調整員工角色和權限等級
- **權限驗證**: 系統自動驗證操作權限，確保安全性

#### 🏢 組織架構管理
- **主管關係**: 建立完整的上下級關係
- **部門結構**: 清晰的部門組織架構
- **權限繼承**: 主管自動獲得下屬管理權限
- **跨部門管理**: 支援跨部門的主管關係設定

### 6. 📈 數據分析系統
- **出勤趨勢**: 多維度出勤分析
- **部門對比**: 部門間數據對比
- **時間分布**: 打卡時間分布分析
- **效率指標**: 工作效率評估
- **報表匯出**: 多格式報表匯出

### 7. ⚙️ 系統設定
- **基本設定**: 公司資訊、時區語言配置
- **考勤規則**: 遲到早退、GPS定位設定
- **通知設定**: 郵件通知、系統推送
- **權限管理**: 角色權限動態配置
- **系統維護**: 資料備份、系統監控

## 🗄 資料庫架構

### 📋 資料庫結構文檔
**⚠️ 重要：開發前必讀**
- 📖 **完整資料庫結構說明**: [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md)
- 🔍 **欄位名稱查詢**: 所有表格欄位定義、資料類型、約束條件
- 🔗 **關聯關係圖**: 表格間的外鍵關聯和依賴關係
- 📝 **常用查詢範例**: SQL 查詢語句參考
- ⚠️ **命名規範**: 統一的欄位命名和狀態值定義

### 核心資料表
- **employees**: 員工基本資訊
- **departments**: 部門管理
- **attendance**: 考勤記錄
- **schedules**: 排班資料
- **leaves**: 請假記錄
- **permissions**: 權限管理

### 資料完整性
- ✅ 10位測試員工
- ✅ 220筆考勤記錄
- ✅ 完整的排班資料
- ✅ 請假記錄和審批流程

## 🔌 API 端點

### 考勤管理
- `POST /api/attendance/clock-in` - 上班打卡
- `POST /api/attendance/clock-out` - 下班打卡
- `GET /api/attendance/today/{employee_id}` - 今日考勤狀態
- `GET /api/attendance/history/{employee_id}` - 考勤歷史

### 打卡記錄管理 (新增)
- `GET /api/attendance/records` - 打卡記錄查詢（支援分頁、篩選）
  - `page`: 頁碼
  - `limit`: 每頁記錄數
  - `employee_id`: 員工ID篩選
  - `department_id`: 部門ID篩選
  - `start_date`: 開始日期
  - `end_date`: 結束日期
  - `status`: 狀態篩選
- `GET /api/attendance/records/{id}` - 單個記錄詳情
- `GET /api/attendance/records/export` - Excel匯出

### 考勤作業管理 (新增)
- `GET /api/attendance/management` - 考勤作業查詢（支援篩選）
  - `employee_id`: 員工ID篩選
  - `department_id`: 部門ID篩選
  - `start_date`: 開始日期
  - `end_date`: 結束日期
  - `status`: 考勤狀態篩選
- `POST /api/attendance/management/generate` - 考勤記錄生成
  - `date_range`: 日期範圍
  - `employee_ids`: 員工ID列表
  - `force_regenerate`: 是否強制重新生成

### 考勤整理處理 (新增)
- `GET /api/attendance/processing` - 考勤整理狀態查詢
- `POST /api/attendance/processing/execute` - 執行考勤整理
  - `date_range`: 日期範圍
  - `employee_scope`: 員工範圍設定
  - `processing_options`: 處理選項
- `GET /api/attendance/processing/status/{processing_id}` - 處理狀態查詢

### 員工管理
- `GET /api/employees` - 員工列表（支援查詢參數）
  - `department_id`: 部門ID篩選
  - `employee_id`: 員工編號搜尋（支援模糊搜尋）
  - `name`: 員工姓名搜尋（支援模糊搜尋）
  - `position`: 職位搜尋（支援模糊搜尋）
- `POST /api/employees` - 新增員工
- `PUT /api/employees/{id}` - 更新員工資訊
- `DELETE /api/employees/{id}` - 刪除員工
- `GET /api/roles` - 獲取所有角色
- `GET /api/managers` - 獲取所有主管
- `GET /api/employees/managers` - 獲取有主管權限的員工列表
- `GET /api/employees/substitutes/{employee_id}` - 獲取可作為代理人的員工列表

### 請假管理
- `GET /api/leaves` - 請假記錄
- `POST /api/leaves` - 申請請假
- `PUT /api/leaves/{id}` - 更新/審核請假
- `DELETE /api/leaves/{id}` - 撤回請假

### 數據分析
- `GET /api/analytics/attendance-trends` - 出勤趨勢分析
- `GET /api/analytics/department-stats` - 部門統計分析
- `GET /api/analytics/time-distribution` - 時間分布分析
- `GET /api/analytics/leave-stats` - 請假統計分析
- `GET /api/analytics/efficiency` - 效率指標分析
- `GET /api/analytics/export` - 報告匯出

### 系統管理
- `GET /api/departments` - 部門管理
- `GET /api/reports/dashboard` - 儀表板統計

## 🚀 快速開始

### 環境要求
- Python 3.8+
- Flask
- SQLite

### 安裝步驟

1. **克隆專案**
```bash
git clone <repository-url>
cd attend
```

2. **建立虛擬環境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安裝依賴**
```bash
pip install -r requirements.txt
```

4. **初始化資料庫**
```bash
python database.py
python init_test_data.py
```

5. **啟動應用程式**
```bash
python app.py
```

6. **訪問系統**
- Elite版 (推薦): http://localhost:7072/elite
- 移動端版: http://localhost:7072/mobile
- 專業版: http://localhost:7072/professional
- 現代版: http://localhost:7072/modern

## 🎯 使用指南

### 基本操作
1. **考勤打卡**: 訪問 `/elite/attendance` 進行打卡
2. **查看儀表板**: 主頁顯示實時統計數據
3. **管理員工**: 在員工管理頁面進行CRUD操作
4. **申請請假**: 使用請假管理系統提交申請
5. **查看分析**: 數據分析頁面提供深度洞察

### 管理員功能
- 員工管理和權限配置
- 排班系統管理
- 請假審批
- 系統設定和維護
- 數據分析和報表匯出

## 🔧 開發指南

### 專案結構
```
attend/
├── app.py              # Flask 主應用程式
├── database.py         # 資料庫初始化
├── config.py          # 配置文件
├── templates/         # HTML 模板
├── static/           # 靜態資源
├── models/           # 資料模型
├── services/         # 業務邏輯
└── logs/            # 日誌文件
```

### 代碼規範
- 遵循 SOLID 原則
- 使用設計模式解決常見問題
- 完善的注釋和文檔
- 單元測試和集成測試

### 性能優化
- 懶加載和緩存策略
- 資源壓縮和優化
- 資料庫查詢優化
- 前端性能監控

## 🛡 安全特性

- **資料驗證**: 前後端雙重驗證
- **SQL注入防護**: 參數化查詢
- **XSS防護**: 輸入過濾和輸出編碼
- **CSRF防護**: Token驗證
- **權限控制**: 角色基礎的訪問控制

## 📊 系統監控

- **日誌記錄**: 完整的操作日誌
- **錯誤追蹤**: 異常監控和報告
- **性能監控**: 響應時間和資源使用
- **資料備份**: 自動備份機制

## 🔄 更新日誌

### v1.0.0 (2024-05-29)
- ✅ 完成Elite版設計系統
- ✅ 實現所有核心功能模組
- ✅ 完成前後端整合
- ✅ 添加完整的API端點
- ✅ 實現數據分析功能
- ✅ 完成系統設定模組
- ✅ 增強請假管理系統：智慧代理人選擇和主管權限審核
- ✅ 建立完整的員工主管關係架構
- ✅ 實現員工管理查詢功能增強

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支
3. 提交變更
4. 推送到分支
5. 創建 Pull Request

## 📄 授權條款

本專案採用 MIT 授權條款。

## 📞 技術支援

如有問題或建議，請聯繫開發團隊。

---

**AttendanceOS Elite** - 讓考勤管理變得簡單而優雅 ✨